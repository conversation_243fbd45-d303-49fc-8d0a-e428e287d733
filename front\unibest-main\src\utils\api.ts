/**
 * @description API请求工具类
 * 提供统一的HTTP请求方法和错误处理
 */

import { ref } from 'vue'

// 全局loading状态
export const globalLoading = ref(false)

/**
 * @description API配置
 */
export const API_CONFIG = {
  baseURL: 'http://localhost:9000/api', // 根据环境自动配置
  timeout: 10000,
  retryCount: 3,
  retryDelay: 1000,
}

/**
 * @description 请求响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
}

/**
 * @description 请求配置接口
 */
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: any
  header?: Record<string, string>
  showLoading?: boolean
  showError?: boolean
  timeout?: number
  retryCount?: number
}

/**
 * @description 统一请求方法
 * @param config 请求配置
 */
export const request = async <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
  const {
    url,
    method = 'GET',
    data,
    params,
    header = {},
    showLoading = false,
    showError = true,
    timeout = API_CONFIG.timeout,
    retryCount = API_CONFIG.retryCount,
  } = config

  // 显示loading
  if (showLoading) {
    globalLoading.value = true
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })
  }

  // 构建请求头
  const requestHeader = {
    'Content-Type': 'application/json',
    ...header,
  }

  // 添加token
  const token = uni.getStorageSync('token')
  if (token) {
    requestHeader.Authorization = `Bearer ${token}`
  }

  // 构建完整URL
  let fullUrl = url.startsWith('http') ? url : `${API_CONFIG.baseURL}${url}`

  // 添加查询参数
  if (params && Object.keys(params).length > 0) {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
      .join('&')
    fullUrl += `${fullUrl.includes('?') ? '&' : '?'}${queryString}`
  }

  let lastError: any = null

  // 重试机制
  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      const response = await uni.request({
        url: fullUrl,
        method,
        data: method === 'GET' ? undefined : data,
        header: requestHeader,
        timeout,
      })

      // 隐藏loading
      if (showLoading) {
        globalLoading.value = false
        uni.hideLoading()
      }

      // 处理HTTP状态码
      if (response.statusCode >= 200 && response.statusCode < 300) {
        const result = response.data as ApiResponse<T>

        // 检查业务状态码
        if (result.success) {
          return result
        } else {
          // 业务错误
          if (showError) {
            // uni.showToast({
            //   title: result.message || '请求失败',
            //   icon: 'none',
            //   duration: 2000,
            // })
          }
          throw new Error(result.message || '请求失败')
        }
      } else {
        // HTTP错误
        throw new Error(`HTTP ${response.statusCode}`)
      }
    } catch (error: any) {
      lastError = error

      // 如果不是最后一次尝试，等待后重试
      if (attempt < retryCount) {
        await new Promise((resolve) => setTimeout(resolve, API_CONFIG.retryDelay * (attempt + 1)))
        continue
      }
    }
  }

  // 隐藏loading
  if (showLoading) {
    globalLoading.value = false
    uni.hideLoading()
  }

  // 处理最终错误
  const errorMessage = getErrorMessage(lastError)

  if (showError) {
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    })
  }

  throw lastError
}

/**
 * @description 获取错误信息
 * @param error 错误对象
 */
const getErrorMessage = (error: any): string => {
  if (!error) return '未知错误'

  // 网络错误
  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      return '请求超时，请检查网络连接'
    }
    if (error.errMsg.includes('fail')) {
      return '网络连接失败，请检查网络设置'
    }
  }

  // 自定义错误
  if (error.message) {
    return error.message
  }

  return '请求失败，请稍后重试'
}

/**
 * @description GET请求
 */
export const get = <T = any>(url: string, params?: any, config: Partial<RequestConfig> = {}) => {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config,
  })
}

/**
 * @description POST请求
 */
export const post = <T = any>(url: string, data?: any, config: Partial<RequestConfig> = {}) => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config,
  })
}

/**
 * @description PUT请求
 */
export const put = <T = any>(url: string, data?: any, config: Partial<RequestConfig> = {}) => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config,
  })
}

/**
 * @description DELETE请求
 */
export const del = <T = any>(url: string, params?: any, config: Partial<RequestConfig> = {}) => {
  return request<T>({
    url,
    method: 'DELETE',
    params,
    ...config,
  })
}

/**
 * @description 文件上传
 * @param url 上传地址
 * @param filePath 文件路径
 * @param name 文件对应的key
 * @param formData 额外的表单数据
 */
export const upload = (
  url: string,
  filePath: string,
  name: string = 'file',
  formData: Record<string, any> = {},
): Promise<ApiResponse> => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token')
    const header: Record<string, string> = {}

    if (token) {
      header.Authorization = `Bearer ${token}`
    }

    uni.uploadFile({
      url: url.startsWith('http') ? url : `${API_CONFIG.baseURL}${url}`,
      filePath,
      name,
      formData,
      header,
      success(res) {
        try {
          const result = JSON.parse(res.data) as ApiResponse
          if (result.success) {
            resolve(result)
          } else {
            uni.showToast({
              title: result.message || '上传失败',
              icon: 'none',
            })
            reject(new Error(result.message || '上传失败'))
          }
        } catch (error) {
          uni.showToast({
            title: '上传响应解析失败',
            icon: 'none',
          })
          reject(error)
        }
      },
      fail(error) {
        uni.showToast({
          title: '文件上传失败',
          icon: 'none',
        })
        reject(error)
      },
    })
  })
}

/**
 * @description 取消所有请求
 */
export const cancelAllRequests = () => {
  // UniApp没有提供取消请求的API，这里可以实现请求队列管理
  globalLoading.value = false
  uni.hideLoading()
}

/**
 * @description 清除用户认证信息
 */
export const clearAuth = () => {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
  // 可以添加其他清理逻辑
}

/**
 * @description 处理认证错误
 */
export const handleAuthError = () => {
  clearAuth()
  uni.showModal({
    title: '登录过期',
    content: '您的登录已过期，请重新登录',
    showCancel: false,
    success() {
      uni.reLaunch({
        url: '/pages/auth/login',
      })
    },
  })
}

// 环境配置
// #ifdef H5
API_CONFIG.baseURL = 'http://localhost:8080/api'
// #endif

// #ifdef MP-WEIXIN
API_CONFIG.baseURL = 'https://api.example.com/api'
// #endif

// #ifdef APP-PLUS
API_CONFIG.baseURL = 'https://api.example.com/api'
// #endif
