<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// @ts-ignore
import type { UserGrowthProfile, UserGrowthStage } from '@/types/onboarding'

// Props
interface Props {
  profile: UserGrowthProfile | null
  showAnimation?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAnimation: true,
  compact: false,
})

// 响应式数据
const animationProgress = ref(0)
const isVisible = ref(false)

// 计算属性
const progressData = computed(() => {
  if (!props.profile) return null

  const stageOrder = ['new_user', 'beginner', 'intermediate', 'advanced', 'expert']
  const currentIndex = stageOrder.indexOf(props.profile.currentStage)
  const progressPercent = ((currentIndex + 1) / stageOrder.length) * 100

  return {
    currentStage: props.profile.currentStage,
    currentIndex,
    totalStages: stageOrder.length,
    progressPercent,
    stageNames: {
      new_user: '新手',
      beginner: '初学者',
      intermediate: '进阶者',
      advanced: '高级',
      expert: '专家',
    },
  }
})

const weeklyProgress = computed(() => {
  if (!props.profile) return []

  // 模拟最近4周的学习数据
  const weeks = []
  const now = new Date()

  for (let i = 3; i >= 0; i--) {
    const weekDate = new Date(now)
    weekDate.setDate(now.getDate() - i * 7)

    const weekData = {
      week: `第${4 - i}周`,
      date: `${weekDate.getMonth() + 1}/${weekDate.getDate()}`,
      interviews: Math.floor(Math.random() * 5) + 1,
      studyHours: Math.floor(Math.random() * 15) + 5,
      improvement: Math.floor(Math.random() * 20) + 5,
    }
    weeks.push(weekData)
  }

  return weeks
})

const milestones = computed(() => {
  if (!props.profile) return []

  const allMilestones = [
    { id: 1, title: '完成首次评估', completed: true, date: '2024-01-01' },
    {
      id: 2,
      title: '连续学习7天',
      completed: props.profile.continuousLearningDays >= 7,
      date: null,
    },
    { id: 3, title: '完成10次面试', completed: props.profile.totalInterviews >= 10, date: null },
    { id: 4, title: '能力提升20分', completed: false, date: null },
    {
      id: 5,
      title: '晋升至高级阶段',
      completed:
        props.profile.currentStage === 'advanced' || props.profile.currentStage === 'expert',
      date: null,
    },
  ]

  return allMilestones
})

// 方法
function getStageColor(stage: string): string {
  const colors = {
    new_user: '#3B82F6',
    beginner: '#10B981',
    intermediate: '#F59E0B',
    advanced: '#EF4444',
    expert: '#8B5CF6',
  }
  return colors[stage] || colors.new_user
}

function getStageIcon(stage: string): string {
  const icons = {
    new_user: 'i-mdi-seedling',
    beginner: 'i-mdi-sprout',
    intermediate: 'i-mdi-tree',
    advanced: 'i-mdi-trophy',
    expert: 'i-mdi-crown',
  }
  return icons[stage] || icons.new_user
}

function startAnimation() {
  if (!props.showAnimation) return

  const duration = 2000
  const startTime = Date.now()

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)

    animationProgress.value = progress

    if (progress < 1) {
      requestAnimationFrame(animate)
    }
  }

  requestAnimationFrame(animate)
}

function getWeeklyBarHeight(value: number, maxValue: number): string {
  const height = (value / maxValue) * 100
  return `${Math.max(height * animationProgress.value, 5)}%`
}

// 生命周期
onMounted(() => {
  isVisible.value = true
  setTimeout(() => {
    startAnimation()
  }, 300)
})
</script>

<template>
  <view class="progress-visualization" :class="{ compact: compact, visible: isVisible }">
    <template v-if="profile && progressData">
      <!-- 整体进度 -->
      <view class="section overall-progress">
        <text class="section-title">成长轨迹</text>

        <view class="progress-track">
          <view
            v-for="(stage, index) in progressData.totalStages"
            :key="index"
            class="stage-node"
            :class="{
              active: index <= progressData.currentIndex,
              current: index === progressData.currentIndex,
            }"
          >
            <view
              class="node-circle"
              :style="{
                backgroundColor:
                  index <= progressData.currentIndex
                    ? getStageColor(Object.keys(progressData.stageNames)[index])
                    : '#E5E7EB',
              }"
            >
              <text
                :class="getStageIcon(Object.keys(progressData.stageNames)[index])"
                class="node-icon"
              ></text>
            </view>
            <text class="node-label">{{ Object.values(progressData.stageNames)[index] }}</text>
          </view>

          <!-- 进度线 -->
          <view class="progress-line">
            <view
              class="progress-fill"
              :style="{
                width: `${progressData.progressPercent * animationProgress}%`,
                backgroundColor: getStageColor(progressData.currentStage),
              }"
            ></view>
          </view>
        </view>
      </view>

      <!-- 周学习统计 -->
      <view class="section weekly-stats" v-if="!compact">
        <text class="section-title">最近学习统计</text>

        <view class="stats-grid">
          <view class="stat-card interviews">
            <text class="stat-icon i-mdi-microphone"></text>
            <text class="stat-value">{{ profile.totalInterviews }}</text>
            <text class="stat-label">总面试次数</text>
          </view>

          <view class="stat-card learning">
            <text class="stat-icon i-mdi-calendar-today"></text>
            <text class="stat-value">{{ profile.continuousLearningDays }}</text>
            <text class="stat-label">连续学习天数</text>
          </view>

          <view class="stat-card improvement">
            <text class="stat-icon i-mdi-trending-up"></text>
            <text class="stat-value">
              {{
                Math.round(
                  (profile.currentAssessment.overallScore -
                    (profile.initialAssessment?.overallScore || 0)) *
                    10,
                ) / 10
              }}
            </text>
            <text class="stat-label">能力提升分数</text>
          </view>
        </view>

        <!-- 周进度图表 -->
        <view class="weekly-chart">
          <view class="chart-header">
            <text class="chart-title">学习活跃度</text>
            <text class="chart-subtitle">最近4周</text>
          </view>

          <view class="chart-container">
            <view v-for="week in weeklyProgress" :key="week.week" class="week-column">
              <view class="week-bars">
                <view
                  class="bar interviews-bar"
                  :style="{ height: getWeeklyBarHeight(week.interviews, 5) }"
                ></view>
                <view
                  class="bar study-bar"
                  :style="{ height: getWeeklyBarHeight(week.studyHours, 20) }"
                ></view>
              </view>
              <text class="week-label">{{ week.week }}</text>
            </view>
          </view>

          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color interviews-color"></view>
              <text class="legend-text">面试次数</text>
            </view>
            <view class="legend-item">
              <view class="legend-color study-color"></view>
              <text class="legend-text">学习时长</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 里程碑进度 -->
      <view class="section milestones" v-if="!compact">
        <text class="section-title">成长里程碑</text>

        <view class="milestone-list">
          <view
            v-for="milestone in milestones"
            :key="milestone.id"
            class="milestone-item"
            :class="{ completed: milestone.completed }"
          >
            <view class="milestone-icon">
              <text
                :class="{
                  'i-mdi-check-circle': milestone.completed,
                  'i-mdi-circle-outline': !milestone.completed,
                }"
              ></text>
            </view>
            <view class="milestone-content">
              <text class="milestone-title">{{ milestone.title }}</text>
              <text v-if="milestone.completed && milestone.date" class="milestone-date">
                完成于 {{ milestone.date }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 目标进度 -->
      <view class="section goals">
        <text class="section-title">目标进度</text>

        <view class="goal-item">
          <view class="goal-header">
            <text class="goal-title">目标岗位: {{ profile.targetPosition }}</text>
            <text class="goal-progress">{{ Math.round(progressData.progressPercent) }}%</text>
          </view>
          <view class="goal-bar">
            <view
              class="goal-fill"
              :style="{
                width: `${progressData.progressPercent * animationProgress}%`,
                backgroundColor: getStageColor(progressData.currentStage),
              }"
            ></view>
          </view>
          <text class="goal-description">
            继续努力，距离成为{{ profile.targetPosition }}又近了一步！
          </text>
        </view>
      </view>
    </template>

    <!-- 无数据状态 -->
    <view v-else class="no-data">
      <text class="no-data-icon i-mdi-chart-line"></text>
      <text class="no-data-title">暂无学习数据</text>
      <text class="no-data-desc">完成初始评估后即可查看个人成长轨迹</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.progress-visualization {
  padding: 32rpx;
  background: #f7f9fc;
  border-radius: 24rpx;
  opacity: 0;
  transform: translateY(40rpx);
  transition: all 0.6s ease-out;

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  &.compact {
    padding: 24rpx;

    .section {
      margin-bottom: 24rpx;
    }
  }
}

.section {
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: block;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

/* 整体进度样式 */
.overall-progress {
  .progress-track {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 0 60rpx;
  }

  .stage-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
  }

  .node-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    margin-bottom: 12rpx;
    transition: all 0.3s ease;

    &.current {
      transform: scale(1.2);
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    }
  }

  .node-icon {
    font-size: 28rpx;
    color: #fff;
  }

  .node-label {
    font-size: 24rpx;
    color: #6b7280;
    text-align: center;
  }

  .progress-line {
    position: absolute;
    top: 72rpx;
    left: 32rpx;
    right: 32rpx;
    height: 4rpx;
    background: #e5e7eb;
    border-radius: 2rpx;
    z-index: 1;
  }

  .progress-fill {
    height: 100%;
    border-radius: 2rpx;
    transition: width 2s ease-out;
  }
}

/* 周统计样式 */
.weekly-stats {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
    margin-bottom: 32rpx;
  }

  .stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }

  .stat-icon {
    font-size: 32rpx;
    margin-bottom: 8rpx;

    .interviews & {
      color: #3b82f6;
    }

    .learning & {
      color: #10b981;
    }

    .improvement & {
      color: #f59e0b;
    }
  }

  .stat-value {
    font-size: 32rpx;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 4rpx;
  }

  .stat-label {
    font-size: 20rpx;
    color: #6b7280;
    text-align: center;
  }
}

/* 周图表样式 */
.weekly-chart {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 24rpx;
  }

  .chart-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1f2937;
  }

  .chart-subtitle {
    font-size: 20rpx;
    color: #6b7280;
  }

  .chart-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 120rpx;
    margin-bottom: 16rpx;
  }

  .week-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }

  .week-bars {
    display: flex;
    align-items: flex-end;
    gap: 4rpx;
    height: 100rpx;
    margin-bottom: 8rpx;
  }

  .bar {
    width: 16rpx;
    border-radius: 8rpx 8rpx 0 0;
    transition: height 1s ease-out;

    &.interviews-bar {
      background: linear-gradient(to top, #3b82f6, #60a5fa);
    }

    &.study-bar {
      background: linear-gradient(to top, #10b981, #34d399);
    }
  }

  .week-label {
    font-size: 20rpx;
    color: #6b7280;
  }

  .chart-legend {
    display: flex;
    justify-content: center;
    gap: 24rpx;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  .legend-color {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;

    &.interviews-color {
      background: #3b82f6;
    }

    &.study-color {
      background: #10b981;
    }
  }

  .legend-text {
    font-size: 20rpx;
    color: #6b7280;
  }
}

/* 里程碑样式 */
.milestones {
  .milestone-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  .milestone-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx;
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &.completed {
      background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
      border-left: 4rpx solid #10b981;
    }
  }

  .milestone-icon {
    margin-right: 16rpx;
    margin-top: 4rpx;
    font-size: 24rpx;

    .completed & {
      color: #10b981;
    }

    .milestone-item:not(.completed) & {
      color: #d1d5db;
    }
  }

  .milestone-content {
    flex: 1;
  }

  .milestone-title {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4rpx;
  }

  .milestone-date {
    display: block;
    font-size: 20rpx;
    color: #10b981;
  }
}

/* 目标进度样式 */
.goals {
  .goal-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }

  .goal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .goal-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1f2937;
  }

  .goal-progress {
    font-size: 24rpx;
    font-weight: bold;
    color: #10b981;
  }

  .goal-bar {
    height: 8rpx;
    background: #e5e7eb;
    border-radius: 4rpx;
    margin-bottom: 12rpx;
    overflow: hidden;
  }

  .goal-fill {
    height: 100%;
    border-radius: 4rpx;
    transition: width 2s ease-out;
  }

  .goal-description {
    font-size: 24rpx;
    color: #6b7280;
    line-height: 1.5;
  }
}

/* 无数据状态 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;

  .no-data-icon {
    font-size: 80rpx;
    color: #d1d5db;
    margin-bottom: 16rpx;
  }

  .no-data-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 8rpx;
  }

  .no-data-desc {
    font-size: 24rpx;
    color: #9ca3af;
    line-height: 1.5;
  }
}
</style>
