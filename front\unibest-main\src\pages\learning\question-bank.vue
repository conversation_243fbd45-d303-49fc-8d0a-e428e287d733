<template>
  <view class="question-bank-container">
    <!-- 头部导航栏 -->
    <HeadBar fixed :title="questionBank.title" :show-back="true" />

    <!-- 主页面 -->
    <scroll-view scroll-y class="main-content" @scrolltolower="loadMoreQuestions">
      <view v-if="!isInitialLoading" class="content-wrapper fade-in">
        <!-- 题库头部信息 -->
        <view class="bank-header-card">
          <view class="bank-info-section">
            <view class="bank-icon-wrapper">
              <view class="bank-icon" :class="getColorStyle(questionBank.color)">
                <text class="icon-text" :class="questionBank.icon"></text>
              </view>
            </view>
            <!-- 题库详情 -->
            <view class="bank-details">
              <view class="bank-title-row">
                <text class="bank-title">{{ questionBank.title }}</text>
                <button
                  class="bookmark-btn"
                  :class="{
                    bookmarked: questionBank.isBookmarked,
                    loading: isLoading && !isInitialLoading,
                  }"
                  :disabled="isLoading"
                  @click="toggleBookmark"
                >
                  <text
                    v-if="!(isLoading && !isInitialLoading)"
                    class="bookmark-icon"
                    :class="questionBank.isBookmarked ? 'i-mdi-star' : 'i-mdi-star-outline'"
                  ></text>
                  <text v-else class="loading-icon i-mdi-loading"></text>
                </button>
              </view>
              <text class="bank-description">
                {{ questionBank.description }}
              </text>
              <view class="bank-meta-tags">
                <view class="difficulty-tag" :class="getDifficultyStyle(questionBank.difficulty)">
                  {{ questionBank.difficulty }}
                </view>
                <view class="meta-item">
                  <text class="meta-icon i-mdi-file-document-outline"></text>
                  <text class="meta-text">{{ questionBank.totalQuestions }} 题</text>
                </view>
                <view class="meta-item">
                  <text class="meta-icon i-mdi-account-group"></text>
                  <text class="meta-text">
                    {{ (questionBank.practiceCount / 1000).toFixed(1) }}k 人练习
                  </text>
                </view>
              </view>

              <view class="progress-section">
                <view class="progress-header">
                  <text class="progress-title">学习进度</text>
                  <text class="progress-value">{{ questionBank.progress }}%</text>
                </view>
                <view class="progress-bar">
                  <view
                    class="progress-fill"
                    :style="getProgressStyle(questionBank.progress)"
                  ></view>
                </view>
              </view>
            </view>
          </view>

          <view class="action-buttons">
            <button class="primary-btn" @click="startPractice">
              <text class="btn-icon i-mdi-play"></text>
              <text class="btn-text">开始练习</text>
            </button>
            <button class="secondary-btn" @click="showAllQuestions">
              <text class="btn-icon i-mdi-clipboard-list"></text>
              <text class="btn-text">全部题目</text>
            </button>
          </view>
        </view>
        <!-- 推荐题目 -->
        <view class="recommend-section">
          <text class="section-title">推荐题目</text>
          <view class="question-list slide-in-up" v-if="recommendedQuestions.length > 0">
            <view
              v-for="(question, index) in recommendedQuestions"
              :key="question.id"
              class="question-card"
              :class="`animate-delay-${index % 3}`"
              @click="openQuestionDetail(question.id)"
            >
              <view class="question-header">
                <text class="question-title">
                  {{ question.title }}
                </text>
                <view class="difficulty-badge" :class="getDifficultyStyle(question.difficulty)">
                  {{ question.difficulty }}
                </view>
              </view>
              <view class="question-footer">
                <view class="question-stats">
                  <view class="stat-item">
                    <text class="stat-icon i-mdi-account-group"></text>
                    <text class="stat-text">{{ question.practiceCount }} 人练习</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon i-mdi-chart-line"></text>
                    <text class="stat-text">正确率 {{ question.correctRate }}%</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon i-mdi-comment-outline"></text>
                    <text class="stat-text">{{ question.commentCount }} 讨论</text>
                  </view>
                </view>
                <text class="arrow-icon i-mdi-chevron-right"></text>
              </view>
            </view>
          </view>
          <view v-else class="no-data">
            <text class="no-data-text">暂无推荐题目</text>
          </view>
        </view>

        <!-- 题目分类 -->
        <view class="category-section">
          <!-- 题目分类筛选 -->
          <CategoryFilter
            title="题目分类"
            :category-options="categoryOptions"
            :selected-category="selectedCategory"
            @change="handleCategoryChange"
            @reset="handleCategoryReset"
          />

          <!-- 分类题目列表 -->
          <view
            class="question-list"
            :class="isQuestionListAnimating ? 'fade-out' : 'slide-in-up'"
            v-if="currentCategoryQuestions.length > 0"
          >
            <view
              v-for="(question, index) in currentCategoryQuestions"
              :key="question.id"
              class="question-card"
              :class="`animate-delay-${index % 5}`"
              @click="openQuestionDetail(question.id)"
            >
              <view class="question-header">
                <view class="question-title-row">
                  <view
                    class="difficulty-dot"
                    :class="{
                      'dot-easy': question.difficulty === '简单',
                      'dot-medium': question.difficulty === '中等',
                      'dot-hard': question.difficulty === '困难',
                    }"
                  ></view>
                  <text class="question-title">
                    {{ question.title }}
                  </text>
                </view>
                <view class="difficulty-badge" :class="getDifficultyStyle(question.difficulty)">
                  {{ question.difficulty }}
                </view>
              </view>
              <view class="question-footer">
                <view class="question-stats">
                  <view class="stat-item">
                    <text class="stat-icon i-mdi-account-group"></text>
                    <text class="stat-text">{{ question.practiceCount }} 人练习</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon i-mdi-chart-line"></text>
                    <text class="stat-text">正确率 {{ question.correctRate }}%</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon i-mdi-comment-outline"></text>
                    <text class="stat-text">{{ question.commentCount }} 讨论</text>
                  </view>
                </view>
                <text class="arrow-icon i-mdi-chevron-right"></text>
              </view>
            </view>
          </view>
          <view v-else class="no-data">
            <view class="no-data-content">
              <view class="no-data-icon-wrapper">
                <text class="no-data-icon i-mdi-file-document-outline"></text>
              </view>
              <text class="no-data-title">暂无题目</text>
              <text class="no-data-description">
                {{
                  selectedCategory === 'all'
                    ? '该题库暂时没有题目'
                    : `"${selectedCategory}" 分类下暂无题目`
                }}
              </text>
              <view class="no-data-actions">
                <button class="refresh-btn" @click="refreshCurrentCategory">
                  <text class="refresh-icon i-mdi-refresh"></text>
                  <text class="refresh-text">刷新试试</text>
                </button>
              </view>
            </view>
          </view>

          <!-- 加载更多状态指示器 -->
          <view v-if="currentCategoryStats.hasData" class="load-more-section">
            <view v-if="isLoadingMore" class="loading-more">
              <text class="loading-icon i-mdi-loading"></text>
              <text class="loading-text">正在加载更多...</text>
              <text class="loading-progress">{{ currentCategoryStats.loadingProgress }}%</text>
            </view>
            <view v-else-if="!hasMoreData" class="no-more-data">
              <text class="no-more-text">已显示全部题目</text>
              <text class="total-count">共 {{ currentCategoryStats.totalCount }} 题</text>
            </view>
            <view v-else class="pull-up-tip">
              <text class="tip-text">上拉加载更多题目</text>
              <text class="tip-icon i-mdi-arrow-up"></text>
              <text class="loaded-count">
                已加载 {{ currentCategoryStats.loadedCount }} /
                {{ currentCategoryStats.totalCount }} 题
              </text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载状态 -->
    <LoadingSpinner v-if="isInitialLoading || isLoading" />
  </view>
</template>


<script setup lang="ts">
import { ref, computed } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingSpinner from '@/components/LoadingSpinner.vue'
// @ts-ignore
import CategoryFilter from '@/components/CategoryFilter.vue'
import {
  getQuestionBankDetail,
  getQuestionsByCategory,
  getRecommendedQuestions,
  toggleQuestionBankBookmark,
  type Question,
  // @ts-ignore
} from '@/service/api/learning/question-bank'
// 引入类型定义
import type { Ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'

// 定义题库数据类型
interface QuestionBankData {
  id: string
  title: string
  description: string
  icon: string
  color: string
  difficulty: string
  totalQuestions: number
  practiceCount: number
  progress: number
  categories: string[]
  isBookmarked: boolean
}

// 定义页面选项类型
interface PageOptions {
  id?: string
  major?: string
}

// 定义当前页面类型
interface CurrentPage {
  options?: PageOptions
}

// 获取路由参数
const bankId: Ref<string> = ref('')
const majorId: Ref<string> = ref('')

// 题库数据
const questionBank: Ref<QuestionBankData> = ref({
  id: '',
  title: '',
  description: '',
  icon: '',
  color: '',
  difficulty: '',
  totalQuestions: 0,
  practiceCount: 0,
  progress: 0,
  categories: [],
  isBookmarked: false,
})

// 是否显示加载状态
const isLoading: Ref<boolean> = ref(true)
// 新增：区分初始化加载和操作加载
const isInitialLoading: Ref<boolean> = ref(true)
// 题目列表 - 按分类
const questionsByCategory: Ref<Record<string, Question[]>> = ref({})

// 当前选中的分类
const selectedCategory: Ref<string> = ref('all')

// 推荐题目
const recommendedQuestions: Ref<Question[]> = ref([])

// 新增：控制题目列表动画状态
const isQuestionListAnimating: Ref<boolean> = ref(false)

// 新增：分页相关变量
const currentPage: Ref<number> = ref(1)
const pageSize: Ref<number> = ref(10)
const isLoadingMore: Ref<boolean> = ref(false)
const hasMoreData: Ref<boolean> = ref(true)
const totalCount: Ref<number> = ref(0)

/**
 * @description 获取分类选项数据，用于CategoryFilter组件
 */
const categoryOptions = computed(() => {
  const options = [
    {
      key: 'all',
      name: '全部分类',
      icon: 'i-mdi-view-grid',
    },
  ]

  // 添加题库的分类选项
  questionBank.value.categories.forEach((category) => {
    options.push({
      key: category,
      name: category,
      icon: 'i-mdi-folder-outline',
    })
  })

  return options
})

/**
 * @description 获取难度等级对应的样式类名
 * @param difficulty 难度等级
 * @returns 返回对应的样式类名
 */
const getDifficultyStyle = (difficulty: string): string => {
  const styles: Record<string, string> = {
    简单: 'bg-green-100 text-green-700',
    中等: 'bg-yellow-100 text-yellow-700',
    困难: 'bg-red-100 text-red-700',
  }
  return styles[difficulty] || 'bg-gray-100 text-gray-700'
}

/**
 * @description 获取颜色主题对应的样式类名
 * @param color 颜色主题
 * @returns 返回对应的样式类名
 */
const getColorStyle = (color: string): string => {
  const styles: Record<string, string> = {
    blue: 'bg-blue-500 from-blue-500 to-blue-600',
    green: 'bg-green-500 from-green-500 to-green-600',
    purple: 'bg-purple-500 from-purple-500 to-purple-600',
    indigo: 'bg-indigo-500 from-indigo-500 to-indigo-600',
    red: 'bg-red-500 from-red-500 to-red-600',
    orange: 'bg-orange-500 from-orange-500 to-orange-600',
  }
  return styles[color] || 'bg-blue-500 from-blue-500 to-blue-600'
}

/**
 * @description 获取进度条样式对象
 * @param progress 进度百分比
 * @returns 返回包含样式属性的对象
 */
const getProgressStyle = (progress: number): Record<string, string> => {
  return {
    width: `${progress}%`,
    transition: 'width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  }
}

/**
 * @description 获取当前分类的题目列表
 */
const currentCategoryQuestions = computed(() => {
  if (selectedCategory.value === 'all') {
    // 手动实现flat函数效果，避免类型错误
    const allQuestions: Question[] = []
    Object.values(questionsByCategory.value).forEach((questions) => {
      allQuestions.push(...questions)
    })
    return allQuestions
  }
  return questionsByCategory.value[selectedCategory.value] || []
})

/**
 * @description 获取当前分类的题目数量统计
 */
const currentCategoryStats = computed(() => {
  const currentQuestions = currentCategoryQuestions.value
  const loadedCount = currentQuestions.length
  const total = totalCount.value

  return {
    loadedCount,
    totalCount: total,
    hasData: loadedCount > 0,
    isComplete: !hasMoreData.value && loadedCount > 0,
    loadingProgress: total > 0 ? Math.round((loadedCount / total) * 100) : 0,
  }
})

/**
 * @description 切换题库收藏状态
 */
const toggleBookmark = async (): Promise<void> => {
  // 防止重复点击
  if (isLoading.value) return


  isLoading.value = true
  try {
    const res = await toggleQuestionBankBookmark(bankId.value)
    if (res.code === 200) {
      questionBank.value.isBookmarked = res.data.isBookmarked


      uni.showToast({
        title: res.data.message || (questionBank.value.isBookmarked ? '已收藏题库' : '已取消收藏'),
        icon: 'success',
        duration: 1500,
      })
    }
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error',
      duration: 1500,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 切换选中的分类（直接切换，不带动画）
 * @param category 分类名称
 */
const switchCategory = async (category: string): Promise<void> => {
  if (category === selectedCategory.value) return

  selectedCategory.value = category

  // 重置分页状态并重新加载数据
  currentPage.value = 1
  hasMoreData.value = true
  totalCount.value = 0

  // 加载新分类的题目数据
  await loadQuestionsByCategory(bankId.value, false)
}

/**
 * @description 从弹窗中选择分类（带动画效果）
 * @param category 分类名称
 */
const selectCategoryFromModal = async (category: string): Promise<void> => {
  if (category === selectedCategory.value) return

  // 使用动画效果切换分类
  await handleCategoryChange(category)
}

/**
 * @description 处理分类筛选器的变更事件
 * @param category 选中的分类key
 */
const handleCategoryChange = async (category: string): Promise<void> => {
  if (category === selectedCategory.value) return

  // 开始动画过渡
  isQuestionListAnimating.value = true

  // 延迟切换分类，营造淡出淡入效果
  setTimeout(async () => {
    selectedCategory.value = category

    // 重置分页状态并重新加载数据
    currentPage.value = 1
    hasMoreData.value = true
    totalCount.value = 0

    // 加载新分类的题目数据
    await loadQuestionsByCategory(bankId.value, false)

    // 动画完成后恢复状态
    setTimeout(() => {
      isQuestionListAnimating.value = false
    }, 100)
  }, 150)
}

/**
 * @description 处理分类筛选器的重置事件
 */
const handleCategoryReset = async (): Promise<void> => {
  if (selectedCategory.value === 'all') return

  // 开始动画过渡
  isQuestionListAnimating.value = true

  setTimeout(async () => {
    selectedCategory.value = 'all'

    // 重置分页状态并重新加载数据
    currentPage.value = 1
    hasMoreData.value = true
    totalCount.value = 0

    // 加载全部分类的题目数据
    await loadQuestionsByCategory(bankId.value, false)

    setTimeout(() => {
      isQuestionListAnimating.value = false
    }, 100)
  }, 150)
}

/**
 * @description 开始练习题目
 */
const startPractice = (): void => {
  // const isPaidBank = checkIfPaidBank(bankId.value)

  // if (isPaidBank.isPaid) {
  //   uni.showModal({
  //     title: '付费题库',
  //     content: `《${questionBank.value.title}》是付费题库，价格：¥${isPaidBank.price}，是否购买？`,
  //     success: (res) => {
  //       if (res.confirm) {
  //         // 跳转到支付页面
  //         uni.navigateTo({
  //           url: `/pages/pay/pay?id=${bankId.value}&type=question-bank`,
  //         })
  //       }
  //     },
  //   })
  //   return
  // }

  uni.navigateTo({
    url: `/pages/learning/practice-question?id=${bankId.value}`,
  })
}

/**
 * @description 检查题库是否为付费题库（模拟逻辑）
 * @param bankId 题库ID
 * @returns 返回付费信息
 */
const checkIfPaidBank = (bankId: string): { isPaid: boolean; price: number } => {
  // 模拟：某些题库ID为付费题库
  const paidBanks: Record<string, number> = {
    '1': 68, // Java面试题库 - 68元
    '2': 89, // 算法与数据结构题库 - 89元
    '3': 58, // Python面试题库 - 58元
    '5': 128, // 高级系统设计题库 - 128元
  }

  const price = paidBanks[bankId]
  return {
    isPaid: !!price,
    price: price || 0,
  }
}

/**
 * @description 查看所有题目列表
 */
const showAllQuestions = (): void => {
  uni.navigateTo({
    url: `/pages/learning/all-questions?bankId=${bankId.value}&major=${majorId.value}`,
  })
}

/**
 * @description 打开题目详情页面
 * @param questionId 题目ID
 */
const openQuestionDetail = (questionId: string): void => {
  uni.navigateTo({
    url: `/pages/learning/question-detail?id=${questionId}&bankId=${bankId.value}`,
  })
}

/**
 * @description 加载题库详情数据
 */
const loadQuestionBank = async (): Promise<void> => {
  try {
    const res = await getQuestionBankDetail(bankId.value)
    if (res.code === 200 && res.data) {

      // 转换数据格式
      questionBank.value = {
        id: res.data.id,
        title: res.data.title,
        description: res.data.description,
        icon: res.data.icon,
        color: res.data.color,
        difficulty: res.data.difficulty,
        totalQuestions: res.data.totalQuestions,
        practiceCount: res.data.practiceCount,
        progress: res.data.progress || 0,
        categories: res.data.categories || [],
        isBookmarked: res.data.isBookmarked || false,
      }

      // 加载分类题目和推荐题目
      await Promise.all([
        loadQuestionsByCategory(bankId.value, false),
        loadRecommendedQuestions(bankId.value),
      ])

    } else {
      throw new Error('题库不存在')
    }
  } catch (error) {   
    uni.showToast({
      title: '题库不存在',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

/**
 * @description 加载分类题目数据
 * @param bankId 题库ID
 * @param isLoadMore 是否为加载更多（默认为false，表示重新加载）
 */
const loadQuestionsByCategory = async (
  bankId: string,
  isLoadMore: boolean = false,
): Promise<void> => {
  try {
    // 如果不是加载更多，重置分页状态
    if (!isLoadMore) {
      currentPage.value = 1
      hasMoreData.value = true
      totalCount.value = 0
    }
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      category: selectedCategory.value,
    }
    console.log(params)

    const res = await getQuestionsByCategory(
      bankId,
      currentPage.value,
      pageSize.value,
      selectedCategory.value,
    )
    console.log(res)
    if (res.code === 200) {
      const categoryKey = selectedCategory.value
      const newQuestions = res.data[categoryKey] || []

      // 如果是加载更多，追加数据；否则替换数据
      if (isLoadMore) {
        questionsByCategory.value[categoryKey] = [
          ...questionsByCategory.value[categoryKey],
          ...newQuestions,
        ]
      } else {
        questionsByCategory.value[categoryKey] = newQuestions
      }

      // 更新分页信息
      totalCount.value = res.data?.page || 0
      hasMoreData.value = questionsByCategory.value[categoryKey].length < totalCount.value
    }
  } catch (error) {
    if (!isLoadMore) {
      questionsByCategory.value = {}
    }
  }
}

/**
 * @description 加载更多题目数据
 */
const loadMoreQuestions = async (): Promise<void> => {
  // 防止重复加载
  if (isLoadingMore.value || !hasMoreData.value) {
    return
  }

  isLoadingMore.value = true

  try {
    // 增加页码
    currentPage.value += 1

    // 加载更多数据
    await loadQuestionsByCategory(bankId.value, true)
  } catch (error) {
    // 回滚页码
    currentPage.value = Math.max(1, currentPage.value - 1)

    uni.showToast({
      title: '加载失败',
      icon: 'none',
      duration: 1500,
    })
  } finally {
    isLoadingMore.value = false
  }
}

/**
 * @description 重置分页状态
 * @param clearData 是否清空当前数据（用于快速显示加载状态）
 */
const resetPagination = (clearData: boolean = false): void => {
  currentPage.value = 1
  hasMoreData.value = true
  totalCount.value = 0
  isLoadingMore.value = false

  if (clearData) {
    const categoryKey = selectedCategory.value
    if (questionsByCategory.value[categoryKey]) {
      questionsByCategory.value[categoryKey] = []
    }
  }
}

/**
 * @description 刷新当前分类的题目数据
 */
const refreshCurrentCategory = async (): Promise<void> => {

  // 重置分页状态
  resetPagination(true)

  // 显示加载状态
  isLoading.value = true

  try {
    // 重新加载当前分类的题目数据
    await loadQuestionsByCategory(bankId.value, false)

    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1500,
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
      duration: 1500,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 加载推荐题目数据
 * @param bankId 题库ID
 */
const loadRecommendedQuestions = async (bankId: string): Promise<void> => {
  try {
    const res = await getRecommendedQuestions(bankId, 5)
    if (res.code === 200) {
      recommendedQuestions.value = res.data || []
    }
  } catch (error) {
    recommendedQuestions.value = []
  }
}

/**
 * @description 页面数据初始化
 */
const initializePageData = async (): Promise<void> => {
  try {
    // 获取路由参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1] as CurrentPage
    const options = currentPage?.options || {}

    if (!options.id) {
      throw new Error('缺少题库ID参数')
    }

    bankId.value = options.id || ''
    majorId.value = options.major || ''

    // 加载题库数据
    await loadQuestionBank()
  } catch (error) {
    const errorMessage = (error as Error).message || '页面加载失败'
    uni.showToast({
      title: errorMessage,
      icon: 'error',
      duration: 2000,
    })

    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  } finally {
    // 关闭加载状态
    isInitialLoading.value = false
    isLoading.value = false
  }
}

/**
 * @description 页面加载完成时初始化
 */
onShow(async () => {
  // 启动页面数据初始化
  await initializePageData()
})
</script>
<style scoped lang="scss">
.question-bank-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.main-content {
  flex: 1;
  background: #f5f5f5;
  margin-top: 100rpx;
}

.content-wrapper {
  padding: 24rpx 20rpx;
  padding-bottom: 160rpx;

  &.fade-in {
    animation: fadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 题库头部卡片
.bank-header-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f5f5f5;
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bank-info-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.bank-icon-wrapper {
  margin-right: 24rpx;
  animation: bounceIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bank-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:active {
    transform: scale(0.95);
  }

  .icon-text {
    font-size: 32rpx;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.bank-details {
  flex: 1;
  animation: slideInUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bank-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.bank-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

.bookmark-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);

  &:active {
    transform: scale(0.9);
  }

  &.bookmarked {
    background: #fff3cd;
    animation: pulse 0.5s ease-in-out;
  }

  &.loading {
    background: #e9ecef;
    cursor: not-allowed;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .bookmark-icon {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .loading-icon {
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    animation: spin 1s linear infinite;
  }
}

.bank-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.bank-meta-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
  animation: slideInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.difficulty-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  transform: scale(1);

  &:active {
    transform: scale(0.95);
  }

  &.bg-green-100 {
    background: #d1fae5;
    color: #065f46;
  }

  &.bg-yellow-100 {
    background: #fef3c7;
    color: #92400e;
  }

  &.bg-red-100 {
    background: #fee2e2;
    color: #991b1b;
  }
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: all 0.2s ease;
  transform: scale(1);

  &:active {
    transform: scale(0.98);
  }

  .meta-icon {
    font-size: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .meta-text {
    font-size: 20rpx;
    color: #999;
  }
}

.progress-section {
  background: rgba(0, 201, 167, 0.08);
  border-radius: 12rpx;
  padding: 24rpx;
  animation: slideInUp 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-title {
  font-size: 24rpx;
  font-weight: 500;
  color: #00c9a7;
}

.progress-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #00c9a7;
  animation: bounceIn 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.progress-bar {
  height: 12rpx;
  background: rgba(0, 201, 167, 0.15);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #00c9a7, #00b39a);
  border-radius: 6rpx;
  transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: slideInUp 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 16rpx;
  animation: slideInUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.primary-btn {
  flex: 1;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.25);
  }

  .btn-icon {
    font-size: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
  }

  &:active .btn-icon {
    transform: scale(1.1);
  }
}

.secondary-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);

  &:active {
    transform: scale(0.95);
    background: #e9ecef;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .btn-icon {
    font-size: 20rpx;
    transition: transform 0.2s ease;
  }

  &:active .btn-icon {
    transform: scale(1.1);
  }
}

// 分类和推荐部分
.category-section,
.recommend-section {
  margin-bottom: 32rpx;
  animation: slideInUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// 分类头部
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;

  .more-categories-btn {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 20rpx;
    padding: 12rpx 20rpx;
    font-size: 28rpx;
    color: #6c757d;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: scale(1);

    &:active {
      background: #e9ecef;
      border-color: #dee2e6;
      transform: scale(0.96);
    }

    .more-text {
      margin-right: 8rpx;
      color: #6c757d;
      font-size: 28rpx;
    }

    .more-icon {
      font-size: 24rpx;
      font-weight: bold;
      transition: transform 0.3s ease;
    }
  }
}

// 简化分类列表
.filter-list-simple {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
  align-items: center;
  overflow-x: auto;
  white-space: nowrap;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 筛选器
.filter-scroll {
  margin-bottom: 24rpx;
  white-space: nowrap;
}

.filter-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 12rpx 22rpx;
  background: white;
  border: 1rpx solid #f0f0f0;
  border-radius: 24rpx;
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  white-space: nowrap;
  transform: scale(1);

  &:active {
    transform: scale(0.95);
  }

  &.filter-active {
    background: #00c9a7;
    color: white;
    border-color: #00c9a7;
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.25);
  }

  .filter-text {
    font-size: 22rpx;
  }
}

// 题目列表
.question-list {
  margin-top: 30rpx;

  // 添加滑入动画
  &.slide-in-up {
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  // 添加淡出动画
  &.fade-out {
    opacity: 0;
    transform: translateY(10rpx);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .question-card {
    background: white;
    border-radius: 12rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid #f5f5f5;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateY(0);
    opacity: 1 !important;
    display: block;

    &:active {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
    }
  }

  // 只在slide-in-up状态下应用延迟动画
  &.slide-in-up .question-card {
    animation: staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;

    &.animate-delay-0 {
      animation-delay: 0s;
    }

    &.animate-delay-1 {
      animation-delay: 0.1s;
    }

    &.animate-delay-2 {
      animation-delay: 0.2s;
    }

    &.animate-delay-3 {
      animation-delay: 0.3s;
    }

    &.animate-delay-4 {
      animation-delay: 0.4s;
    }

    // 确保所有题目卡片都有默认动画，即使没有延迟类
    &:not([class*='animate-delay']) {
      animation-delay: 0s;
    }
  }

  // 确保非动画状态下的题目卡片正常显示
  &:not(.slide-in-up):not(.fade-out) .question-card {
    opacity: 1;
    transform: translateY(0);
  }
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  padding-bottom: 16rpx;
}

.question-title-row {
  display: flex;
  align-items: flex-start;
  flex: 1;
  margin-right: 16rpx;
}

.difficulty-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);

  &.dot-easy {
    background: #52c41a;
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
  }

  &.dot-medium {
    background: #faad14;
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.4);
  }

  &.dot-hard {
    background: #ff4d4f;
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }
}

.question-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  transition: color 0.2s ease;
}

.difficulty-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  flex-shrink: 0;
  transition: all 0.2s ease;
  transform: scale(1);

  &.bg-green-100 {
    background: #d1fae5;
    color: #065f46;
  }

  &.bg-yellow-100 {
    background: #fef3c7;
    color: #92400e;
  }

  &.bg-red-100 {
    background: #fee2e2;
    color: #991b1b;
  }
}

.question-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #f5f5f5;
}

.question-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: all 0.2s ease;
  transform: scale(1);

  .stat-icon {
    font-size: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .stat-text {
    font-size: 20rpx;
    color: #999;
    transition: color 0.2s ease;
  }
}

.arrow-icon {
  font-size: 24rpx;
  color: #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateX(0);
}

// 为问题卡片添加悬停效果（适用于H5端）
.question-card {
  &:active {
    .difficulty-dot {
      transform: scale(1.5);
      animation: pulse 0.6s ease-in-out;
    }

    .question-title {
      color: #00c9a7;
    }

    .difficulty-badge {
      transform: scale(1.05);
    }

    .stat-item {
      transform: scale(1.02);

      .stat-icon {
        color: #00c9a7;
      }

      .stat-text {
        color: #666;
      }
    }

    .arrow-icon {
      color: #00c9a7;
      transform: translateX(4rpx);
    }
  }
}

// 分类选择弹窗
.category-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.category-modal {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f3f4;

  .modal-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #1a1a1a;
  }

  .modal-close {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: #f8f9fa;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background: #e9ecef;
    }

    .close-icon {
      font-size: 40rpx;
      color: #6c757d;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.modal-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 16rpx;
}

.category-option {
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    background: #e9ecef;
    border-color: #dee2e6;
  }

  &.option-active {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;

    .option-text {
      color: #ffffff;
    }
  }

  .option-text {
    font-size: 28rpx;
    color: #495057;
    font-weight: 500;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 新增：序列淡入动画
@keyframes fadeInSequence {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 新增：滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 新增：弹跳进入动画
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(40rpx);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10rpx);
  }
  70% {
    transform: scale(0.9) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 新增：脉冲动画用于加载状态
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 新增：渐进式显示动画
@keyframes staggerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 新增：加载微动画
@keyframes shimmer {
  0% {
    background-position: -200rpx 0;
  }
  100% {
    background-position: calc(200rpx + 100%) 0;
  }
}

// 新增：呼吸动画
@keyframes breathe {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

// 加载更多状态指示器样式
.load-more-section {
  padding: 32rpx 0;
  text-align: center;
  animation: slideInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 24rpx;
  background: rgba(0, 201, 167, 0.08);
  border-radius: 12rpx;
  margin: 0 20rpx;

  .loading-icon {
    font-size: 24rpx;
    color: #00c9a7;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 24rpx;
    color: #00c9a7;
    font-weight: 500;
  }

  .loading-progress {
    font-size: 20rpx;
    color: #00c9a7;
    font-weight: 600;
    background: rgba(0, 201, 167, 0.1);
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
  }
}

.no-more-data {
  padding: 24rpx;
  text-align: center;
  color: #999;

  .no-more-text {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    display: block;
  }

  .total-count {
    font-size: 20rpx;
    color: #ccc;
  }
}

.pull-up-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  color: #999;
  animation: breathe 2s ease-in-out infinite;

  .tip-text {
    font-size: 22rpx;
    color: #999;
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  .tip-icon {
    font-size: 20rpx;
    color: #999;
    animation: bounceVertical 2s ease-in-out infinite;
  }

  .loaded-count {
    font-size: 20rpx;
    color: #ccc;
    margin-top: 4rpx;
  }
}

// 垂直弹跳动画
@keyframes bounceVertical {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  60% {
    transform: translateY(-4rpx);
  }
}

// 空状态样式
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 60rpx 40rpx;
  animation: fadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  .no-data-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 500rpx;
    width: 100%;
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .no-data-icon-wrapper {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32rpx;
    animation: bounceIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -8rpx;
      left: -8rpx;
      right: -8rpx;
      bottom: -8rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.05) 0%, rgba(0, 179, 154, 0.05) 100%);
      z-index: -1;
    }

    .no-data-icon {
      font-size: 56rpx;
      color: rgba(0, 201, 167, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      animation: pulse 2s ease-in-out infinite;
    }
  }

  .no-data-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    animation: slideInUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .no-data-description {
    font-size: 24rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 40rpx;
    animation: slideInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .no-data-actions {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    width: 100%;
    animation: slideInUp 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .refresh-btn,
  .reset-filter-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    padding: 20rpx 32rpx;
    border-radius: 12rpx;
    font-size: 24rpx;
    font-weight: 500;
    border: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: scale(1);
    width: 100%;

    &:active {
      transform: scale(0.95);
    }
  }

  .refresh-btn {
    background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
    color: white;
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.25);

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.3);
    }

    .refresh-icon {
      font-size: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    &:active .refresh-icon {
      transform: rotate(180deg);
    }

    .refresh-text {
      font-size: 24rpx;
    }
  }

  .reset-filter-btn {
    background: #f8f9fa;
    color: #666;
    border: 1rpx solid #e9ecef;

    &:active {
      background: #e9ecef;
      border-color: #dee2e6;
    }

    .reset-icon {
      font-size: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    &:active .reset-icon {
      transform: scale(1.1);
    }

    .reset-text {
      font-size: 24rpx;
    }
  }
}

// 空状态呼吸动画
@keyframes breatheGently {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

// 为空状态图标添加轻微呼吸效果
.no-data-icon-wrapper .no-data-icon {
  animation: pulse 3s ease-in-out infinite;
}
</style>
