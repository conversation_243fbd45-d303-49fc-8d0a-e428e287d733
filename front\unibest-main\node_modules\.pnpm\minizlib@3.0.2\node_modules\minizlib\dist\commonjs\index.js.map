{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAA2B;AAC3B,mCAA+B;AAC/B,uCAAmC;AACnC,+CAAgC;AAChC,iDAA0C;AAC1C,+CAA0C;AAAjC,yGAAA,SAAS,OAAA;AAElB,MAAM,oBAAoB,GAAG,eAAM,CAAC,MAAM,CAAA;AAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,eAAM,EAAE,QAAQ,CAAC,CAAA;AAC9D,MAAM,IAAI,GAAG,CAAC,IAAc,EAAE,EAAE,CAAC,IAAyB,CAAA;AAC1D,MAAM,uBAAuB,GAC3B,IAAI,EAAE,QAAQ,KAAK,IAAI,IAAI,IAAI,EAAE,GAAG,KAAK,SAAS;IAChD,CAAC,CAAC,CAAC,QAAiB,EAAE,EAAE;QACpB,eAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAA;IACxD,CAAC;IACH,CAAC,CAAC,CAAC,CAAU,EAAE,EAAE,GAAE,CAAC,CAAA;AAExB,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AAEzC,MAAa,SAAU,SAAQ,KAAK;IAClC,IAAI,CAAS;IACb,KAAK,CAAS;IACd,YAAY,GAAkC;QAC5C,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,CAAA;QAC7B,IAAI,CAAC,IAAI,GAAI,GAA6B,CAAC,IAAI,CAAA;QAC/C,IAAI,CAAC,KAAK,GAAI,GAA6B,CAAC,KAAK,CAAA;QACjD,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,YAAY,CAAA;QAExC,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAA;QACrC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACjD,CAAC;IAED,IAAI,IAAI;QACN,OAAO,WAAW,CAAA;IACpB,CAAC;CACF;AAjBD,8BAiBC;AAED,uCAAuC;AACvC,wDAAwD;AACxD,uDAAuD;AACvD,gCAAgC;AAChC,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AA4BtC,MAAe,QAAS,SAAQ,mBAAoC;IAClE,SAAS,GAAY,KAAK,CAAA;IAC1B,MAAM,GAAY,KAAK,CAAA;IACvB,UAAU,CAAQ;IAClB,gBAAgB,CAAQ;IACxB,cAAc,CAAQ;IACtB,OAAO,CAAa;IACpB,QAAQ,CAAyB;IAEjC,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;IACD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IACD,qBAAqB;IACrB,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IACD,oBAAoB;IAEpB,YAAY,IAAqB,EAAE,IAA2B;QAC5D,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;YACnC,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAA;QAEjE,YAAY;QACZ,KAAK,CAAC,IAAI,CAAC,CAAA;QAEX,qBAAqB;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAA;QACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,CAAA;QAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA;QAC7C,oBAAoB;QAEpB,oEAAoE;QACpE,IAAI,CAAC;YACH,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAA;QACzC,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,mDAAmD;YACnD,MAAM,IAAI,SAAS,CAAC,EAA2B,CAAC,CAAA;QAClD,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,EAAE;YACpB,qEAAqE;YACrE,IAAI,IAAI,CAAC,SAAS;gBAAE,OAAM;YAE1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;YAErB,sCAAsC;YACtC,qCAAqC;YACrC,IAAI,CAAC,KAAK,EAAE,CAAA;YACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QACzB,CAAC,CAAA;QAED,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QACjE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACpC,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;YACpB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAA;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpB,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAA,gBAAM,EAAC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAA;YAC3C,YAAY;YACZ,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAA;QAC/B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAkB;QACtB,IAAI,IAAI,CAAC,KAAK;YAAE,OAAM;QAEtB,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,SAAS,GAAG,IAAI,CAAC,cAAc,CAAA;QAElE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;IACzE,CAAC;IASD,GAAG,CACD,KAAyC,EACzC,QAA2C,EAC3C,EAAe;QAEf,qBAAqB;QACrB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAChC,EAAE,GAAG,KAAK,CAAA;YACV,QAAQ,GAAG,SAAS,CAAA;YACpB,KAAK,GAAG,SAAS,CAAA;QACnB,CAAC;QACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,oBAAoB;QACpB,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,QAAQ;gBAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;;gBACpC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACxB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAClB,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IACtB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,uDAAuD;IACvD,CAAC,WAAW,CAAC,CAAC,IAAwC;QACpD,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAQD,KAAK,CACH,KAAyB,EACzB,QAA2C,EAC3C,EAAe;QAEf,2CAA2C;QAC3C,8CAA8C;QAC9C,IAAI,OAAO,QAAQ,KAAK,UAAU;YAChC,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAA;QAEtC,IAAI,OAAO,KAAK,KAAK,QAAQ;YAC3B,KAAK,GAAG,eAAM,CAAC,IAAI,CAAC,KAAe,EAAE,QAA0B,CAAC,CAAA;QAElE,IAAI,IAAI,CAAC,SAAS;YAAE,OAAM;QAC1B,IAAA,gBAAM,EAAC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAA;QAE3C,2EAA2E;QAC3E,mDAAmD;QACnD,iDAAiD;QACjD,MAAM,YAAY,GAAI,IAAI,CAAC,OAAuC;aAC/D,OAAO,CAAA;QACV,MAAM,mBAAmB,GAAG,YAAY,CAAC,KAAK,CAAA;QAC9C,YAAY,CAAC,KAAK,GAAG,GAAG,EAAE,GAAE,CAAC,CAAA;QAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA;QACxC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,EAAE,GAAE,CAAC,CAAA;QAC7B,sEAAsE;QACtE,oEAAoE;QACpE,uBAAuB,CAAC,IAAI,CAAC,CAAA;QAC7B,IAAI,MAAM,GAAkC,SAAS,CAAA;QACrD,IAAI,CAAC;YACH,MAAM,SAAS,GACb,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,QAAQ;gBACnC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;gBACnB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;YACrB,MAAM,GACJ,IAAI,CAAC,OAGN,CAAC,aAAa,CAAC,KAAe,EAAE,SAAS,CAAC,CAAA;YAC3C,8CAA8C;YAC9C,uBAAuB,CAAC,KAAK,CAAC,CAAA;QAChC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,6DAA6D;YAC7D,mEAAmE;YACnE,uBAAuB,CAAC,KAAK,CAAC,CAAA;YAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,GAA4B,CAAC,CAAC,CAAA;QAC5D,CAAC;gBAAS,CAAC;YACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,mEAAmE;gBACnE,oEAAoE;gBACpE,2CAA2C;gBAC3C,CAAC;gBAAC,IAAI,CAAC,OAAuC,CAAC,OAAO;oBACpD,YAAY,CAAA;gBACd,YAAY,CAAC,KAAK,GAAG,mBAAmB,CAAA;gBACxC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,aAAa,CAAA;gBAClC,oEAAoE;gBACpE,mDAAmD;gBACnD,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;gBACxC,qDAAqD;YACvD,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,OAAO;YACd,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAElE,IAAI,WAAW,CAAA;QACf,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/C,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;gBACnB,iEAAiE;gBACjE,sEAAsE;gBACtE,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,eAAM,CAAC,IAAI,CAAC,CAAW,CAAC,CAAC,CAAA;gBACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACvC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAW,CAAC,CAAA;gBACtD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2CAA2C;gBAC3C,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,eAAM,CAAC,IAAI,CAAC,MAAqB,CAAC,CAAC,CAAA;YACrE,CAAC;QACH,CAAC;QAED,IAAI,EAAE;YAAE,EAAE,EAAE,CAAA;QACZ,OAAO,WAAW,CAAA;IACpB,CAAC;CACF;AAOD,MAAa,IAAK,SAAQ,QAAQ;IAChC,MAAM,CAAS;IACf,SAAS,CAAS;IAElB,YAAY,IAAiB,EAAE,IAAc;QAC3C,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QAEjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,wBAAS,CAAC,UAAU,CAAA;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,wBAAS,CAAC,QAAQ,CAAA;QACzD,IAAI,CAAC,aAAa,GAAG,wBAAS,CAAC,YAAY,CAAA;QAC3C,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAEjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;IAChC,CAAC;IAED,MAAM,CAAC,KAAa,EAAE,QAAgB;QACpC,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAM;QAEzB,IAAI,CAAC,IAAI,CAAC,MAAM;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;QAEhE,gEAAgE;QAChE,qBAAqB;QACrB,IAAI,CAAE,IAAI,CAAC,MAA2B,CAAC,MAAM;YAC3C,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACzD,oBAAoB;QAEpB,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,wBAAS,CAAC,YAAY,CAAC,CAAA;YAClC,IAAA,gBAAM,EAAC,IAAI,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAA;YAC1C,kEAAkE;YAClE,oEAAoE;YACpE,uBAAuB;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;YACnC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAClB,SAAiC,EACjC,EAAe,EACf,EAAE;gBACF,qBAAqB;gBACrB,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE,CAAC;oBACpC,EAAE,GAAG,SAAS,CAAA;oBACd,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;gBAC5B,CAAC;gBACD,oBAAoB;gBACpB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBACrB,EAAE,EAAE,EAAE,CAAA;YACR,CAAC,CAAA;YACD,IAAI,CAAC;gBACH,CAAC;gBACC,IAAI,CAAC,MAGN,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,CAAA;YAC/B,CAAC;YACD,qBAAqB;YACrB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;gBACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;YAC3B,CAAC;YACD,oBAAoB;QACtB,CAAC;IACH,CAAC;CACF;AAjED,oBAiEC;AAED,wBAAwB;AACxB,MAAa,OAAQ,SAAQ,IAAI;IAC/B,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;IACxB,CAAC;CACF;AAJD,0BAIC;AAED,MAAa,OAAQ,SAAQ,IAAI;IAC/B,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;IACxB,CAAC;CACF;AAJD,0BAIC;AAID,MAAa,IAAK,SAAQ,IAAI;IAC5B,SAAS,CAAS;IAClB,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAA;IAC1C,CAAC;IAED,CAAC,WAAW,CAAC,CAAC,IAAwC;QACpD,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAA;QAEpD,yDAAyD;QACzD,4CAA4C;QAC5C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;QACb,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;CACF;AAhBD,oBAgBC;AAED,MAAa,MAAO,SAAQ,IAAI;IAC9B,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IACvB,CAAC;CACF;AAJD,wBAIC;AAED,kBAAkB;AAClB,MAAa,UAAW,SAAQ,IAAI;IAClC,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;IAC3B,CAAC;CACF;AAJD,gCAIC;AAED,MAAa,UAAW,SAAQ,IAAI;IAClC,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;IAC3B,CAAC;CACF;AAJD,gCAIC;AAED,sBAAsB;AACtB,MAAa,KAAM,SAAQ,IAAI;IAC7B,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACtB,CAAC;CACF;AAJD,sBAIC;AAED,MAAa,MAAO,SAAQ,QAAQ;IAClC,YAAY,IAAiB,EAAE,IAAgB;QAC7C,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;QAEjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,wBAAS,CAAC,wBAAwB,CAAA;QAC7D,IAAI,CAAC,WAAW;YACd,IAAI,CAAC,WAAW,IAAI,wBAAS,CAAC,uBAAuB,CAAA;QACvD,IAAI,CAAC,aAAa,GAAG,wBAAS,CAAC,sBAAsB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACnB,CAAC;CACF;AAVD,wBAUC;AAED,MAAa,cAAe,SAAQ,MAAM;IACxC,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAA;IAC/B,CAAC;CACF;AAJD,wCAIC;AAED,MAAa,gBAAiB,SAAQ,MAAM;IAC1C,YAAY,IAAiB;QAC3B,KAAK,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;IACjC,CAAC;CACF;AAJD,4CAIC", "sourcesContent": ["import assert from 'assert'\nimport { <PERSON><PERSON><PERSON> } from 'buffer'\nimport { Minipass } from 'minipass'\nimport * as realZlib from 'zlib'\nimport { constants } from './constants.js'\nexport { constants } from './constants.js'\n\nconst OriginalBufferConcat = Buffer.concat\nconst desc = Object.getOwnPropertyDescriptor(<PERSON><PERSON><PERSON>, 'concat')\nconst noop = (args: <PERSON><PERSON><PERSON>[]) => args as unknown as <PERSON><PERSON><PERSON>\nconst passthroughBufferConcat =\n  desc?.writable === true || desc?.set !== undefined\n    ? (makeNoOp: boolean) => {\n        Buffer.concat = makeNoOp ? noop : OriginalBufferConcat\n      }\n    : (_: boolean) => {}\n\nconst _superWrite = Symbol('_superWrite')\n\nexport class ZlibError extends Error {\n  code?: string\n  errno?: number\n  constructor(err: NodeJS.ErrnoException | Error) {\n    super('zlib: ' + err.message)\n    this.code = (err as NodeJS.ErrnoException).code\n    this.errno = (err as NodeJS.ErrnoException).errno\n    /* c8 ignore next */\n    if (!this.code) this.code = 'ZLIB_ERROR'\n\n    this.message = 'zlib: ' + err.message\n    Error.captureStackTrace(this, this.constructor)\n  }\n\n  get name() {\n    return 'ZlibError'\n  }\n}\n\n// the Zlib class they all inherit from\n// This thing manages the queue of requests, and returns\n// true or false if there is anything in the queue when\n// you call the .write() method.\nconst _flushFlag = Symbol('flushFlag')\n\nexport type ChunkWithFlushFlag = Minipass.ContiguousData & {\n  [_flushFlag]?: number\n}\n\nexport type ZlibBaseOptions = Minipass.Options<Minipass.ContiguousData> & {\n  flush?: number\n  finishFlush?: number\n  fullFlushFlag?: number\n}\nexport type ZlibMode =\n  | 'Gzip'\n  | 'Gunzip'\n  | 'Deflate'\n  | 'Inflate'\n  | 'DeflateRaw'\n  | 'InflateRaw'\n  | 'Unzip'\nexport type ZlibHandle =\n  | realZlib.Gzip\n  | realZlib.Gunzip\n  | realZlib.Deflate\n  | realZlib.Inflate\n  | realZlib.DeflateRaw\n  | realZlib.InflateRaw\nexport type BrotliMode = 'BrotliCompress' | 'BrotliDecompress'\n\nabstract class ZlibBase extends Minipass<Buffer, ChunkWithFlushFlag> {\n  #sawError: boolean = false\n  #ended: boolean = false\n  #flushFlag: number\n  #finishFlushFlag: number\n  #fullFlushFlag: number\n  #handle?: ZlibHandle\n  #onError: (err: ZlibError) => any\n\n  get sawError() {\n    return this.#sawError\n  }\n  get handle() {\n    return this.#handle\n  }\n  /* c8 ignore start */\n  get flushFlag() {\n    return this.#flushFlag\n  }\n  /* c8 ignore stop */\n\n  constructor(opts: ZlibBaseOptions, mode: ZlibMode | BrotliMode) {\n    if (!opts || typeof opts !== 'object')\n      throw new TypeError('invalid options for ZlibBase constructor')\n\n    //@ts-ignore\n    super(opts)\n\n    /* c8 ignore start */\n    this.#flushFlag = opts.flush ?? 0\n    this.#finishFlushFlag = opts.finishFlush ?? 0\n    this.#fullFlushFlag = opts.fullFlushFlag ?? 0\n    /* c8 ignore stop */\n\n    // this will throw if any options are invalid for the class selected\n    try {\n      // @types/node doesn't know that it exports the classes, but they're there\n      //@ts-ignore\n      this.#handle = new realZlib[mode](opts)\n    } catch (er) {\n      // make sure that all errors get decorated properly\n      throw new ZlibError(er as NodeJS.ErrnoException)\n    }\n\n    this.#onError = err => {\n      // no sense raising multiple errors, since we abort on the first one.\n      if (this.#sawError) return\n\n      this.#sawError = true\n\n      // there is no way to cleanly recover.\n      // continuing only obscures problems.\n      this.close()\n      this.emit('error', err)\n    }\n\n    this.#handle?.on('error', er => this.#onError(new ZlibError(er)))\n    this.once('end', () => this.close)\n  }\n\n  close() {\n    if (this.#handle) {\n      this.#handle.close()\n      this.#handle = undefined\n      this.emit('close')\n    }\n  }\n\n  reset() {\n    if (!this.#sawError) {\n      assert(this.#handle, 'zlib binding closed')\n      //@ts-ignore\n      return this.#handle.reset?.()\n    }\n  }\n\n  flush(flushFlag?: number) {\n    if (this.ended) return\n\n    if (typeof flushFlag !== 'number') flushFlag = this.#fullFlushFlag\n\n    this.write(Object.assign(Buffer.alloc(0), { [_flushFlag]: flushFlag }))\n  }\n\n  end(cb?: () => void): this\n  end(chunk: ChunkWithFlushFlag, cb?: () => void): this\n  end(\n    chunk: ChunkWithFlushFlag,\n    encoding?: Minipass.Encoding,\n    cb?: () => void,\n  ): this\n  end(\n    chunk?: ChunkWithFlushFlag | (() => void),\n    encoding?: Minipass.Encoding | (() => void),\n    cb?: () => void,\n  ) {\n    /* c8 ignore start */\n    if (typeof chunk === 'function') {\n      cb = chunk\n      encoding = undefined\n      chunk = undefined\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    /* c8 ignore stop */\n    if (chunk) {\n      if (encoding) this.write(chunk, encoding)\n      else this.write(chunk)\n    }\n    this.flush(this.#finishFlushFlag)\n    this.#ended = true\n    return super.end(cb)\n  }\n\n  get ended() {\n    return this.#ended\n  }\n\n  // overridden in the gzip classes to do portable writes\n  [_superWrite](data: Buffer & { [_flushFlag]?: number }) {\n    return super.write(data)\n  }\n\n  write(chunk: ChunkWithFlushFlag, cb?: () => void): boolean\n  write(\n    chunk: ChunkWithFlushFlag,\n    encoding?: Minipass.Encoding,\n    cb?: () => void,\n  ): boolean\n  write(\n    chunk: ChunkWithFlushFlag,\n    encoding?: Minipass.Encoding | (() => void),\n    cb?: () => void,\n  ) {\n    // process the chunk using the sync process\n    // then super.write() all the outputted chunks\n    if (typeof encoding === 'function')\n      (cb = encoding), (encoding = 'utf8')\n\n    if (typeof chunk === 'string')\n      chunk = Buffer.from(chunk as string, encoding as BufferEncoding)\n\n    if (this.#sawError) return\n    assert(this.#handle, 'zlib binding closed')\n\n    // _processChunk tries to .close() the native handle after it's done, so we\n    // intercept that by temporarily making it a no-op.\n    // diving into the node:zlib internals a bit here\n    const nativeHandle = (this.#handle as unknown as { _handle: any })\n      ._handle\n    const originalNativeClose = nativeHandle.close\n    nativeHandle.close = () => {}\n    const originalClose = this.#handle.close\n    this.#handle.close = () => {}\n    // It also calls `Buffer.concat()` at the end, which may be convenient\n    // for some, but which we are not interested in as it slows us down.\n    passthroughBufferConcat(true)\n    let result: undefined | Buffer | Buffer[] = undefined\n    try {\n      const flushFlag =\n        typeof chunk[_flushFlag] === 'number'\n          ? chunk[_flushFlag]\n          : this.#flushFlag\n      result = (\n        this.#handle as unknown as {\n          _processChunk: (chunk: Buffer, flushFlag: number) => Buffer[]\n        }\n      )._processChunk(chunk as Buffer, flushFlag)\n      // if we don't throw, reset it back how it was\n      passthroughBufferConcat(false)\n    } catch (err) {\n      // or if we do, put Buffer.concat() back before we emit error\n      // Error events call into user code, which may call Buffer.concat()\n      passthroughBufferConcat(false)\n      this.#onError(new ZlibError(err as NodeJS.ErrnoException))\n    } finally {\n      if (this.#handle) {\n        // Core zlib resets `_handle` to null after attempting to close the\n        // native handle. Our no-op handler prevented actual closure, but we\n        // need to restore the `._handle` property.\n        ;(this.#handle as unknown as { _handle: any })._handle =\n          nativeHandle\n        nativeHandle.close = originalNativeClose\n        this.#handle.close = originalClose\n        // `_processChunk()` adds an 'error' listener. If we don't remove it\n        // after each call, these handlers start piling up.\n        this.#handle.removeAllListeners('error')\n        // make sure OUR error listener is still attached tho\n      }\n    }\n\n    if (this.#handle)\n      this.#handle.on('error', er => this.#onError(new ZlibError(er)))\n\n    let writeReturn\n    if (result) {\n      if (Array.isArray(result) && result.length > 0) {\n        const r = result[0]\n        // The first buffer is always `handle._outBuffer`, which would be\n        // re-used for later invocations; so, we always have to copy that one.\n        writeReturn = this[_superWrite](Buffer.from(r as Buffer))\n        for (let i = 1; i < result.length; i++) {\n          writeReturn = this[_superWrite](result[i] as Buffer)\n        }\n      } else {\n        // either a single Buffer or an empty array\n        writeReturn = this[_superWrite](Buffer.from(result as Buffer | []))\n      }\n    }\n\n    if (cb) cb()\n    return writeReturn\n  }\n}\n\nexport type ZlibOptions = ZlibBaseOptions & {\n  level?: number\n  strategy?: number\n}\n\nexport class Zlib extends ZlibBase {\n  #level?: number\n  #strategy?: number\n\n  constructor(opts: ZlibOptions, mode: ZlibMode) {\n    opts = opts || {}\n\n    opts.flush = opts.flush || constants.Z_NO_FLUSH\n    opts.finishFlush = opts.finishFlush || constants.Z_FINISH\n    opts.fullFlushFlag = constants.Z_FULL_FLUSH\n    super(opts, mode)\n\n    this.#level = opts.level\n    this.#strategy = opts.strategy\n  }\n\n  params(level: number, strategy: number) {\n    if (this.sawError) return\n\n    if (!this.handle)\n      throw new Error('cannot switch params when binding is closed')\n\n    // no way to test this without also not supporting params at all\n    /* c8 ignore start */\n    if (!(this.handle as { params?: any }).params)\n      throw new Error('not supported in this implementation')\n    /* c8 ignore stop */\n\n    if (this.#level !== level || this.#strategy !== strategy) {\n      this.flush(constants.Z_SYNC_FLUSH)\n      assert(this.handle, 'zlib binding closed')\n      // .params() calls .flush(), but the latter is always async in the\n      // core zlib. We override .flush() temporarily to intercept that and\n      // flush synchronously.\n      const origFlush = this.handle.flush\n      this.handle.flush = (\n        flushFlag?: (() => void) | number,\n        cb?: () => void,\n      ) => {\n        /* c8 ignore start */\n        if (typeof flushFlag === 'function') {\n          cb = flushFlag\n          flushFlag = this.flushFlag\n        }\n        /* c8 ignore stop */\n        this.flush(flushFlag)\n        cb?.()\n      }\n      try {\n        ;(\n          this.handle as unknown as {\n            params: (level?: number, strategy?: number) => void\n          }\n        ).params(level, strategy)\n      } finally {\n        this.handle.flush = origFlush\n      }\n      /* c8 ignore start */\n      if (this.handle) {\n        this.#level = level\n        this.#strategy = strategy\n      }\n      /* c8 ignore stop */\n    }\n  }\n}\n\n// minimal 2-byte header\nexport class Deflate extends Zlib {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'Deflate')\n  }\n}\n\nexport class Inflate extends Zlib {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'Inflate')\n  }\n}\n\n// gzip - bigger header, same deflate compression\nexport type GzipOptions = ZlibOptions & { portable?: boolean }\nexport class Gzip extends Zlib {\n  #portable: boolean\n  constructor(opts: GzipOptions) {\n    super(opts, 'Gzip')\n    this.#portable = opts && !!opts.portable\n  }\n\n  [_superWrite](data: Buffer & { [_flushFlag]?: number }) {\n    if (!this.#portable) return super[_superWrite](data)\n\n    // we'll always get the header emitted in one first chunk\n    // overwrite the OS indicator byte with 0xFF\n    this.#portable = false\n    data[9] = 255\n    return super[_superWrite](data)\n  }\n}\n\nexport class Gunzip extends Zlib {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'Gunzip')\n  }\n}\n\n// raw - no header\nexport class DeflateRaw extends Zlib {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'DeflateRaw')\n  }\n}\n\nexport class InflateRaw extends Zlib {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'InflateRaw')\n  }\n}\n\n// auto-detect header.\nexport class Unzip extends Zlib {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'Unzip')\n  }\n}\n\nexport class Brotli extends ZlibBase {\n  constructor(opts: ZlibOptions, mode: BrotliMode) {\n    opts = opts || {}\n\n    opts.flush = opts.flush || constants.BROTLI_OPERATION_PROCESS\n    opts.finishFlush =\n      opts.finishFlush || constants.BROTLI_OPERATION_FINISH\n    opts.fullFlushFlag = constants.BROTLI_OPERATION_FLUSH\n    super(opts, mode)\n  }\n}\n\nexport class BrotliCompress extends Brotli {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'BrotliCompress')\n  }\n}\n\nexport class BrotliDecompress extends Brotli {\n  constructor(opts: ZlibOptions) {\n    super(opts, 'BrotliDecompress')\n  }\n}\n"]}