/**
 * @description 调试工具函数
 * <AUTHOR>
 */

// 定义颜色样式
const styles = {
  debug:
    'color: #2196F3; background: #E3F2FD; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
  error:
    'color: #F44336; background: #FFEBEE; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
  warn: 'color: #FF9800; background: #FFF3E0; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
  info: 'color: #4CAF50; background: #E8F5E8; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
  perf: 'color: #9C27B0; background: #F3E5F5; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
  group:
    'color: #607D8B; background: #ECEFF1; padding: 2px 6px; border-radius: 3px; font-weight: bold;',
}

// 定义文本颜色
const textStyles = {
  debug: 'color: #1976D2;',
  error: 'color: #D32F2F;',
  warn: 'color: #F57C00;',
  info: 'color: #388E3C;',
  perf: 'color: #7B1FA2;',
  group: 'color: #455A64;',
}

/**
 * 开发环境下的日志输出
 * @param message 日志消息
 * @param data 要输出的数据
 */
export const debugLog = (message: string, data?: any) => {
  if (data !== undefined) {
    console.log(`%c[DEBUG]%c ${message}:`, styles.debug, textStyles.debug, data)
  } else {
    console.log(`%c[DEBUG]%c ${message}`, styles.debug, textStyles.debug)
  }
}

/**
 * 开发环境下的错误日志输出
 * @param message 错误消息
 * @param error 错误对象
 */
export const debugError = (message: string, error?: any) => {
  if (error !== undefined) {
    console.error(`%c[ERROR]%c ${message}:`, styles.error, textStyles.error, error)
  } else {
    console.error(`%c[ERROR]%c ${message}`, styles.error, textStyles.error)
  }
}

/**
 * 开发环境下的警告日志输出
 * @param message 警告消息
 * @param data 要输出的数据
 */
export const debugWarn = (message: string, data?: any) => {
  if (data !== undefined) {
    console.warn(`%c[WARN]%c ${message}:`, styles.warn, textStyles.warn, data)
  } else {
    console.warn(`%c[WARN]%c ${message}`, styles.warn, textStyles.warn)
  }
}

/**
 * 开发环境下的信息日志输出
 * @param message 信息消息
 * @param data 要输出的数据
 */
export const debugInfo = (message: string, data?: any) => {
  if (data !== undefined) {
    console.info(`%c[INFO]%c ${message}:`, styles.info, textStyles.info, data)
  } else {
    console.info(`%c[INFO]%c ${message}`, styles.info, textStyles.info)
  }
}

/**
 * 开发环境下的性能计时开始
 * @param label 计时标签
 */
export const debugTimeStart = (label: string) => {
  console.time(`%c[PERF]%c ${label}`)
  console.log(`%c[PERF]%c ⏱️ 开始计时: ${label}`, styles.perf, textStyles.perf)
}

/**
 * 开发环境下的性能计时结束
 * @param label 计时标签
 */
export const debugTimeEnd = (label: string) => {
  console.timeEnd(`%c[PERF]%c ${label}`)
  console.log(`%c[PERF]%c ⏹️ 计时结束: ${label}`, styles.perf, textStyles.perf)
}

/**
 * 开发环境下的分组日志开始
 * @param groupName 分组名称
 */
export const debugGroupStart = (groupName: string) => {
  console.group(`%c[GROUP]%c 📁 ${groupName}`, styles.group, textStyles.group)
}

/**
 * 开发环境下的分组日志结束
 */
export const debugGroupEnd = () => {
  console.groupEnd()
}

/**
 * 开发环境下的成功日志输出
 * @param message 成功消息
 * @param data 要输出的数据
 */
export const debugSuccess = (message: string, data?: any) => {
  const successStyle =
    'color: #4CAF50; background: #E8F5E8; padding: 2px 6px; border-radius: 3px; font-weight: bold;'
  const successTextStyle = 'color: #2E7D32;'
  if (data !== undefined) {
    console.log(`%c[SUCCESS]%c ✅ ${message}:`, successStyle, successTextStyle, data)
  } else {
    console.log(`%c[SUCCESS]%c ✅ ${message}`, successStyle, successTextStyle)
  }
}

/**
 * 开发环境下的网络请求日志
 * @param method 请求方法
 * @param url 请求URL
 * @param data 请求数据
 */
export const debugRequest = (method: string, url: string, data?: any) => {
  const requestStyle =
    'color: #FF5722; background: #FFF3E0; padding: 2px 6px; border-radius: 3px; font-weight: bold;'
  const requestTextStyle = 'color: #E64A19;'
  if (data !== undefined) {
    console.log(
      `%c[REQUEST]%c 🌐 ${method.toUpperCase()} ${url}:`,
      requestStyle,
      requestTextStyle,
      data,
    )
  } else {
    console.log(`%c[REQUEST]%c 🌐 ${method.toUpperCase()} ${url}`, requestStyle, requestTextStyle)
  }
}

/**
 * 开发环境下的响应日志
 * @param status 响应状态
 * @param url 请求URL
 * @param data 响应数据
 */
export const debugResponse = (status: number, url: string, data?: any) => {
  const isSuccess = status >= 200 && status < 300
  const responseStyle = isSuccess
    ? 'color: #4CAF50; background: #E8F5E8; padding: 2px 6px; border-radius: 3px; font-weight: bold;'
    : 'color: #F44336; background: #FFEBEE; padding: 2px 6px; border-radius: 3px; font-weight: bold;'
  const responseTextStyle = isSuccess ? 'color: #2E7D32;' : 'color: #D32F2F;'
  const emoji = isSuccess ? '✅' : '❌'

  if (data !== undefined) {
    console.log(`%c[RESPONSE]%c ${emoji} ${status} ${url}:`, responseStyle, responseTextStyle, data)
  } else {
    console.log(`%c[RESPONSE]%c ${emoji} ${status} ${url}`, responseStyle, responseTextStyle)
  }
}

/**
 * 开发环境下的表格数据输出
 * @param data 表格数据
 * @param title 表格标题
 */
export const debugTable = (data: any[], title?: string) => {
  if (title) {
    console.log(`%c[TABLE]%c 📊 ${title}`, styles.info, textStyles.info)
  }
  console.table(data)
}
