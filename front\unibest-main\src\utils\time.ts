/**
 * @description 时间工具函数
 * <AUTHOR>
 */

/**
 * @description 格式化时间为相对时间
 * @param dateStr 时间字符串
 * @returns 格式化后的时间，如 "刚刚"、"2分钟前"、"3小时前"、"5天前" 或具体日期
 */
export const formatTime = (dateStr: string): string => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    return date.toLocaleDateString()
  }
}

/**
 * @description 格式化时间为指定格式
 * @param dateStr 时间字符串
 * @param format 格式字符串，如 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export const formatDateTime = (dateStr: string, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  const date = new Date(dateStr)

  if (isNaN(date.getTime())) {
    return dateStr
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace(/YYYY/g, String(year))
    .replace(/MM/g, month)
    .replace(/DD/g, day)
    .replace(/HH/g, hours)
    .replace(/mm/g, minutes)
    .replace(/ss/g, seconds)
}

/**
 * @description 获取今天的开始时间
 * @returns 今天00:00:00的Date对象
 */
export const getTodayStart = (): Date => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return today
}

/**
 * @description 获取今天的结束时间
 * @returns 今天23:59:59的Date对象
 */
export const getTodayEnd = (): Date => {
  const today = new Date()
  today.setHours(23, 59, 59, 999)
  return today
}

/**
 * @description 判断是否为今天
 * @param dateStr 时间字符串
 * @returns 是否为今天
 */
export const isToday = (dateStr: string): boolean => {
  const date = new Date(dateStr)
  const today = new Date()

  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  )
}

/**
 * @description 判断是否为昨天
 * @param dateStr 时间字符串
 * @returns 是否为昨天
 */
export const isYesterday = (dateStr: string): boolean => {
  const date = new Date(dateStr)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)

  return (
    date.getFullYear() === yesterday.getFullYear() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getDate() === yesterday.getDate()
  )
}
