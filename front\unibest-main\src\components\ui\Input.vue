<template>
  <view :class="wrapperClasses">
    <!-- 标签 -->
    <text v-if="label" class="ui-input__label">{{ label }}</text>

    <!-- 输入框容器 -->
    <view class="ui-input__container">
      <!-- 左侧图标 -->
      <text v-if="leftIcon" :class="['ui-input__icon', 'ui-input__icon--left', leftIcon]"></text>

      <!-- 输入框 -->
      <input
        v-if="type !== 'textarea'"
        :class="inputClasses"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />

      <!-- 文本域 -->
      <textarea
        v-else
        :class="inputClasses"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :auto-height="autoHeight"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      ></textarea>

      <!-- 右侧图标 -->
      <text v-if="rightIcon" :class="['ui-input__icon', 'ui-input__icon--right', rightIcon]"></text>

      <!-- 清除按钮 -->
      <text
        v-if="clearable && modelValue && !disabled"
        class="ui-input__clear i-mdi-close-circle"
        @click="handleClear"
      ></text>

      <!-- 密码显示切换 -->
      <text
        v-if="type === 'password'"
        :class="['ui-input__password-toggle', showPassword ? 'i-mdi-eye-off' : 'i-mdi-eye']"
        @click="togglePassword"
      ></text>
    </view>

    <!-- 错误信息 -->
    <text v-if="error" class="ui-input__error">{{ error }}</text>

    <!-- 帮助信息 -->
    <text v-if="help && !error" class="ui-input__help">{{ help }}</text>

    <!-- 字符计数 -->
    <text v-if="showCount && maxlength" class="ui-input__count">
      {{ (modelValue || '').length }}/{{ maxlength }}
    </text>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  // 输入框类型
  type?: 'text' | 'number' | 'password' | 'textarea' | 'search' | 'tel' | 'email'
  // 双向绑定值
  modelValue?: string | number
  // 占位符
  placeholder?: string
  // 标签
  label?: string
  // 是否禁用
  disabled?: boolean
  // 是否只读
  readonly?: boolean
  // 是否可清除
  clearable?: boolean
  // 最大长度
  maxlength?: number
  // 是否显示字符计数
  showCount?: boolean
  // 左侧图标
  leftIcon?: string
  // 右侧图标
  rightIcon?: string
  // 错误信息
  error?: string
  // 帮助信息
  help?: string
  // 输入框尺寸
  size?: 'small' | 'medium' | 'large'
  // 是否自动高度（仅textarea）
  autoHeight?: boolean
  // 自定义类名
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  modelValue: '',
  placeholder: '',
  label: '',
  disabled: false,
  readonly: false,
  clearable: false,
  maxlength: 0,
  showCount: false,
  leftIcon: '',
  rightIcon: '',
  error: '',
  help: '',
  size: 'medium',
  autoHeight: false,
  customClass: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  focus: [event: Event]
  blur: [event: Event]
  input: [event: Event]
  confirm: [event: Event]
  clear: []
}>()

// 密码显示状态
const showPassword = ref(false)
// 聚焦状态
const isFocused = ref(false)

// 计算输入框类型
const inputType = computed(() => {
  if (props.type === 'password') {
    return showPassword.value ? 'text' : 'password'
  }
  return props.type === 'textarea' ? 'text' : props.type
})

// 计算容器类名
const wrapperClasses = computed(() => {
  const classes = ['ui-input', `ui-input--${props.size}`, props.customClass]

  if (props.disabled) classes.push('ui-input--disabled')
  if (props.readonly) classes.push('ui-input--readonly')
  if (props.error) classes.push('ui-input--error')
  if (isFocused.value) classes.push('ui-input--focused')

  return classes.join(' ')
})

// 计算输入框类名
const inputClasses = computed(() => {
  const classes = ['ui-input__field']

  if (props.leftIcon) classes.push('ui-input__field--has-left-icon')
  if (props.rightIcon || props.clearable || props.type === 'password') {
    classes.push('ui-input__field--has-right-icon')
  }

  return classes.join(' ')
})

// 处理输入事件
const handleInput = (event: any) => {
  const value = event.detail?.value || event.target?.value || ''
  emit('update:modelValue', value)
  emit('input', event)
}

// 处理聚焦事件
const handleFocus = (event: Event) => {
  isFocused.value = true
  emit('focus', event)
}

// 处理失焦事件
const handleBlur = (event: Event) => {
  isFocused.value = false
  emit('blur', event)
}

// 处理确认事件
const handleConfirm = (event: Event) => {
  emit('confirm', event)
}

// 处理清除
const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables';
@import '@/styles/mixins';

.ui-input {
  position: relative;
  margin-bottom: $spacing-lg;

  // 输入框尺寸
  &--small {
    .ui-input__field {
      height: 64rpx;
      padding: 0 $spacing-base;
      font-size: $font-size-sm;
    }

    .ui-input__label {
      font-size: $font-size-sm;
    }
  }

  &--medium {
    .ui-input__field {
      height: 80rpx;
      padding: 0 $spacing-lg;
      font-size: $font-size-base;
    }
  }

  &--large {
    .ui-input__field {
      height: 96rpx;
      padding: 0 $spacing-xl;
      font-size: $font-size-lg;
    }

    .ui-input__label {
      font-size: $font-size-lg;
    }
  }

  // 聚焦状态
  &--focused {
    .ui-input__container {
      border-color: $primary-color;
      box-shadow: 0 0 0 6rpx rgba($primary-color, 0.1);
    }
  }

  // 错误状态
  &--error {
    .ui-input__container {
      border-color: $error-color;
    }

    .ui-input__label {
      color: $error-color;
    }
  }

  // 禁用状态
  &--disabled {
    .ui-input__container {
      background: $bg-secondary;
      border-color: $border-light;
    }

    .ui-input__field {
      color: $text-disabled;
      cursor: not-allowed;
    }

    .ui-input__label {
      color: $text-disabled;
    }
  }

  // 只读状态
  &--readonly {
    .ui-input__field {
      cursor: default;
      background: $bg-secondary;
    }
  }
}

// 标签
.ui-input__label {
  display: block;
  margin-bottom: $spacing-sm;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

// 输入框容器
.ui-input__container {
  position: relative;
  display: flex;
  align-items: center;
  background: $bg-primary;
  border: 2rpx solid $border-light;
  border-radius: $radius-md;
  transition: all $transition-base;
}

// 输入框
.ui-input__field {
  flex: 1;
  height: 80rpx;
  padding: 0 $spacing-lg;
  font-size: $font-size-base;
  color: $text-primary;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: $text-tertiary;
  }

  // 有左侧图标时的样式
  &--has-left-icon {
    padding-left: 80rpx;
  }

  // 有右侧图标时的样式
  &--has-right-icon {
    padding-right: 80rpx;
  }
}

// 文本域特殊样式
textarea.ui-input__field {
  height: auto;
  min-height: 120rpx;
  padding: $spacing-lg;
  line-height: $line-height-relaxed;
  resize: none;
}

// 图标
.ui-input__icon {
  position: absolute;
  font-size: 32rpx;
  color: $text-tertiary;

  &--left {
    left: $spacing-lg;
  }

  &--right {
    right: $spacing-lg;
  }
}

// 清除按钮
.ui-input__clear {
  position: absolute;
  right: $spacing-lg;
  font-size: 32rpx;
  color: $text-tertiary;
  cursor: pointer;
  transition: color $transition-fast;

  &:hover {
    color: $text-secondary;
  }
}

// 密码切换按钮
.ui-input__password-toggle {
  position: absolute;
  right: $spacing-lg;
  font-size: 32rpx;
  color: $text-tertiary;
  cursor: pointer;
  transition: color $transition-fast;

  &:hover {
    color: $text-secondary;
  }
}

// 错误信息
.ui-input__error {
  display: block;
  margin-top: $spacing-xs;
  font-size: $font-size-sm;
  color: $error-color;
}

// 帮助信息
.ui-input__help {
  display: block;
  margin-top: $spacing-xs;
  font-size: $font-size-sm;
  color: $text-tertiary;
}

// 字符计数
.ui-input__count {
  position: absolute;
  right: $spacing-lg;
  bottom: -$spacing-lg - $font-size-sm;
  font-size: $font-size-xs;
  color: $text-tertiary;
}

// 搜索框特殊样式
.ui-input--search {
  .ui-input__container {
    border-radius: $radius-full;
  }

  .ui-input__field {
    padding-left: 80rpx;
  }

  .ui-input__icon--left {
    left: $spacing-lg;
  }
}

// 响应式适配
@include mobile {
  .ui-input {
    margin-bottom: $spacing-base;

    &__field {
      height: 72rpx;
      padding: 0 $spacing-base;
      font-size: $font-size-sm;
    }

    &__icon {
      font-size: 28rpx;
    }

    &__clear,
    &__password-toggle {
      font-size: 28rpx;
    }
  }
}
</style>
