/**
 * API配置文件
 * 统一管理所有接口地址
 */

// 基础API地址
const BASE_URL = 'http://localhost:8080' // 开发环境地址，生产环境时需要修改

// API接口配置
export const API_CONFIG = {
  // 基础地址
  BASE_URL,
  // 支付相关接口
  PAYMENT: {
    // 创建支付订单
    CREATE_ORDER: `${BASE_URL}/app/pay/create-order`,
    // 支付宝支付
    ALIPAY_PAY: `${BASE_URL}/app/pay/alipay`,
    // 查询订单状态
    QUERY_STATUS: `${BASE_URL}/app/pay/query-status`,
    // 取消订单
    CANCEL_ORDER: `${BASE_URL}/app/pay/cancel`,
    // 支付宝异步通知
    ALIPAY_NOTIFY: `${BASE_URL}/app/pay/notify`,
    // 支付宝同步回调
    ALIPAY_RETURN: `${BASE_URL}/app/pay/return`,
    // SSE连接
    SSE_CONNECT: `${BASE_URL}/app/pay/sse/connect`,
    // SSE手动查询
    SSE_QUERY: `${BASE_URL}/app/pay/sse/query-status`,
  },

  // 其他业务接口可以在这里添加
  // USER: {
  //   LOGIN: `${BASE_URL}/app/user/login`,
  //   REGISTER: `${BASE_URL}/app/user/register`,
  // }
}

// 支付相关类型定义
export interface PaymentOrderRequest {
  productId: number
  productType: string
  productTitle: string
  amount: number
  paymentMethod: string
  userId?: number
  remark?: string
}

export interface PaymentOrderResponse {
  orderId: number
  orderNo: string
  productId: number
  productType: string
  productTitle: string
  amount: number
  paymentMethod: string
  status: 'pending' | 'paid' | 'cancelled' | 'expired'
  createTime: string
  expireTime: string
  payToken: string
}

export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

/**
 * 统一的API请求方法
 */
export const apiRequest = <T = any>(options: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
}): Promise<ApiResponse<T>> => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        ...options.header,
      },
      success: (response) => {
        if (response.statusCode === 200) {
          resolve(response.data as ApiResponse<T>)
        } else {
          reject(new Error(`请求失败，状态码：${response.statusCode}`))
        }
      },
      fail: (error) => {
        reject(new Error(`网络请求失败：${error.errMsg}`))
      },
    })
  })
}
