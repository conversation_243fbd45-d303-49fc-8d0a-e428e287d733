/**
 * @description 面试相关API服务
 */

import { get, post, put, del } from '@/utils/api'

/**
 * @description 用户统计数据接口
 */
export interface UserStats {
  totalInterviews: number
  totalTime: number
  averageScore: number
  rank: string
  weeklyProgress: number
}

/**
 * @description 用户能力数据接口
 */
export interface UserAbilityData {
  professionalKnowledge: number
  communicationSkill: number
  logicalThinking: number
  innovation: number
  stressResistance: number
  teamwork: number
}

/**
 * @description AI激励数据接口
 */
export interface AIMotivationData {
  greeting: string
  message: string
  personalizedTip: string
}

/**
 * @description 智能推荐任务接口
 */
export interface SmartTask {
  id: number
  title: string
  description: string
  action: string
  priority: 'high' | 'medium' | 'low'
  url: string
}

/**
 * @description 面试记录接口
 */
export interface InterviewRecord {
  id: number
  jobName: string
  company: string
  score: number
  date: string
  status: 'completed' | 'incomplete'
  duration?: number
  category?: string
}

/**
 * @description 用户仪表盘数据接口
 */
export interface DashboardData {
  userStats: UserStats
  radarData: UserAbilityData
  aiMotivation: AIMotivationData
  smartTasks: SmartTask[]
  recentInterviews: InterviewRecord[]
}

/**
 * @description 获取用户仪表盘数据
 */
export const getDashboardSummary = () => {
  return get<DashboardData>(
    '/dashboard/summary',
    {},
    {
      showLoading: true,
      showError: true,
    },
  )
}

/**
 * @description 获取用户统计数据
 */
export const getUserStats = () => {
  return get<UserStats>('/user/stats')
}

/**
 * @description 获取用户能力雷达数据
 */
export const getUserAbilityData = () => {
  return get<UserAbilityData>('/user/ability')
}

/**
 * @description 获取AI激励数据
 */
export const getAIMotivation = () => {
  return get<AIMotivationData>('/ai/motivation')
}

/**
 * @description 获取智能推荐任务
 */
export const getSmartTasks = (limit: number = 5) => {
  return get<SmartTask[]>('/recommendations/tasks', { limit })
}

/**
 * @description 获取最近面试记录
 */
export const getRecentInterviews = (limit: number = 5) => {
  return get<InterviewRecord[]>('/interviews/recent', { limit })
}

/**
 * @description 获取面试详情
 * @param interviewId 面试ID
 */
export const getInterviewDetail = (interviewId: number) => {
  return get(`/interviews/${interviewId}`)
}

/**
 * @description 开始新面试
 * @param jobType 岗位类型
 * @param difficulty 难度级别
 */
export const startNewInterview = (data: {
  jobType: string
  difficulty: string
  duration?: number
}) => {
  return post('/interviews/start', data)
}

/**
 * @description 提交面试答案
 * @param interviewId 面试ID
 * @param questionId 问题ID
 * @param answer 答案内容
 */
export const submitAnswer = (interviewId: number, questionId: number, answer: string) => {
  return post(`/interviews/${interviewId}/answers`, {
    questionId,
    answer,
  })
}

/**
 * @description 结束面试
 * @param interviewId 面试ID
 */
export const finishInterview = (interviewId: number) => {
  return post(`/interviews/${interviewId}/finish`)
}

/**
 * @description 获取面试结果
 * @param interviewId 面试ID
 */
export const getInterviewResult = (interviewId: number) => {
  return get(`/interviews/${interviewId}/result`)
}

/**
 * @description 获取学习建议
 */
export const getLearningSuggestions = () => {
  return get('/learning/suggestions')
}

/**
 * @description 获取岗位列表
 */
export const getJobCategories = () => {
  return get('/jobs/categories')
}

/**
 * @description 获取面试题库
 * @param category 岗位类别
 * @param difficulty 难度
 * @param limit 题目数量
 */
export const getInterviewQuestions = (params: {
  category?: string
  difficulty?: string
  limit?: number
}) => {
  return get('/questions', params)
}

/**
 * @description 收藏面试题
 * @param questionId 问题ID
 */
export const favoriteQuestion = (questionId: number) => {
  return post(`/questions/${questionId}/favorite`)
}

/**
 * @description 取消收藏面试题
 * @param questionId 问题ID
 */
export const unfavoriteQuestion = (questionId: number) => {
  return del(`/questions/${questionId}/favorite`)
}

/**
 * @description 获取用户收藏的题目
 */
export const getFavoriteQuestions = () => {
  return get('/questions/favorites')
}

/**
 * @description 提交面试反馈
 * @param interviewId 面试ID
 * @param feedback 反馈内容
 */
export const submitFeedback = (
  interviewId: number,
  feedback: {
    rating: number
    comment: string
    tags?: string[]
  },
) => {
  return post(`/interviews/${interviewId}/feedback`, feedback)
}

/**
 * @description 获取学习资源
 * @param category 资源类别
 */
export const getLearningResources = (category?: string) => {
  return get('/learning/resources', { category })
}

/**
 * @description 记录学习进度
 * @param resourceId 资源ID
 * @param progress 进度百分比
 */
export const updateLearningProgress = (resourceId: number, progress: number) => {
  return put(`/learning/resources/${resourceId}/progress`, { progress })
}

/**
 * @description 获取用户学习统计
 */
export const getLearningStats = () => {
  return get('/learning/stats')
}

/**
 * @description 获取岗位详情
 * @param params 请求参数
 */
export const getJobDetail = (params: { jobId: number; categoryId?: number }) => {
  return get(`/jobs/${params.jobId}/detail`, { categoryId: params.categoryId })
}

/**
 * @description 收藏/取消收藏岗位
 * @param params 收藏参数
 */
export const favoriteJob = (params: { jobId: number; isFavorited: boolean }) => {
  if (params.isFavorited) {
    return post(`/jobs/${params.jobId}/favorite`)
  } else {
    return del(`/jobs/${params.jobId}/favorite`)
  }
}

/**
 * @description 分享岗位
 * @param params 分享参数
 */
export const shareJob = (params: { jobId: number; platform: string }) => {
  return post(`/jobs/${params.jobId}/share`, { platform: params.platform })
}
