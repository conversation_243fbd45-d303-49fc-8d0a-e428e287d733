<script setup lang="ts">
import { ref, reactive, computed, shallowRef, markRaw } from 'vue'
import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

// 学习计划类型
interface Plan {
  id: number
  title: string
  desc: string
  done: boolean
  category: string
  deadline?: string
  createTime: number
}

// 状态管理
const plans = ref<Plan[]>([])
const isLoading = ref(true)
const showAddPlan = ref(false)
const newPlanTitle = ref('')
const newPlanDesc = ref('')
const newPlanCategory = ref('其他')
const newPlanDeadline = ref('')
const showValidation = ref(false)

// 计划分类
const planCategories = [
  { value: '算法', color: '#6366f1' },
  { value: '前端', color: '#00C9A7' },
  { value: '后端', color: '#ff4d4f' },
  { value: '面试', color: '#faad14' },
  { value: '其他', color: '#9ca3af' },
]

// 计算属性：按完成状态分组的计划
const groupedPlans = computed(() => {
  const pendingPlans = plans.value.filter((p) => !p.done)
  const completedPlans = plans.value.filter((p) => p.done)

  return {
    pending: pendingPlans,
    completed: completedPlans,
    pendingCount: pendingPlans.length,
    completedCount: completedPlans.length,
    totalCount: plans.value.length,
  }
})

// 过滤选项
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '待完成', value: 'pending' },
  { label: '已完成', value: 'completed' },
]
const currentFilter = ref('all')

// 排序选项
const sortOptions = [
  { label: '最新添加', value: 'newest' },
  { label: '最早添加', value: 'oldest' },
]
const currentSort = ref('newest')

// 计算属性：过滤和排序后的计划列表
const filteredPlans = computed(() => {
  // 先按过滤条件筛选
  let result = [...plans.value]

  if (currentFilter.value === 'pending') {
    result = result.filter((p) => !p.done)
  } else if (currentFilter.value === 'completed') {
    result = result.filter((p) => p.done)
  }

  // 然后按排序条件排序
  if (currentSort.value === 'newest') {
    result.sort((a, b) => b.createTime - a.createTime)
  } else {
    result.sort((a, b) => a.createTime - b.createTime)
  }

  return result
})

/**
 * @description 加载学习计划
 */
function loadPlans() {
  isLoading.value = true

  try {
    const storedPlans = uni.getStorageSync('studyPlans')
    if (storedPlans) {
      try {
        const parsedData = JSON.parse(storedPlans)
        // 兼容旧数据
        const updatedData = parsedData.map((item: any) => ({
          ...item,
          category: item.category || '其他',
          createTime: item.createTime || Date.now(),
        }))
        plans.value = updatedData
      } catch (e) {
        console.error('解析学习计划数据失败', e)
        plans.value = []
      }
    }
  } catch (error) {
    console.error('加载计划失败', error)
    uni.showToast({
      title: '加载数据失败',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 保存学习计划到本地
 */
function savePlans() {
  uni.setStorageSync('studyPlans', JSON.stringify(plans.value))
}

/**
 * @description 添加新学习计划
 */
function addPlan() {
  showValidation.value = true

  if (!newPlanTitle.value.trim()) {
    uni.showToast({
      title: '请输入计划标题',
      icon: 'none',
    })
    return
  }

  const newPlan: Plan = {
    id: Date.now(),
    title: newPlanTitle.value.trim(),
    desc: newPlanDesc.value.trim(),
    category: newPlanCategory.value,
    done: false,
    createTime: Date.now(),
  }

  // 如果设置了截止日期
  if (newPlanDeadline.value) {
    newPlan.deadline = newPlanDeadline.value
  }

  plans.value.unshift(newPlan)
  savePlans()

  // 重置表单
  resetForm()

  uni.showToast({
    title: '添加成功',
    icon: 'success',
  })
}

/**
 * @description 切换计划完成状态
 */
function togglePlanStatus(plan: Plan) {
  plan.done = !plan.done
  savePlans()

  // 触感反馈
  uni.vibrateShort()

  uni.showToast({
    title: plan.done ? '已完成' : '已取消完成',
    icon: 'none',
  })
}

/**
 * @description 删除计划
 */
function deletePlan(id: number) {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个学习计划吗？',
    success: (res) => {
      if (res.confirm) {
        plans.value = plans.value.filter((p) => p.id !== id)
        savePlans()
        uni.showToast({
          title: '删除成功',
          icon: 'success',
        })
      }
    },
  })
}

/**
 * @description 编辑计划
 */
const editingPlan = ref<Plan | null>(null)

function startEdit(plan: Plan) {
  // 深拷贝计划对象，避免直接修改原对象
  editingPlan.value = JSON.parse(JSON.stringify(plan))
  initEditForm()
  showAddPlan.value = true
}

function updatePlan() {
  showValidation.value = true

  if (!editingPlan.value || !newPlanTitle.value.trim()) {
    uni.showToast({
      title: '请输入计划标题',
      icon: 'none',
    })
    return
  }

  // 查找并更新计划
  const index = plans.value.findIndex((p) => p.id === editingPlan.value!.id)
  if (index !== -1) {
    plans.value[index] = {
      ...plans.value[index],
      title: newPlanTitle.value.trim(),
      desc: newPlanDesc.value.trim(),
      category: newPlanCategory.value,
      deadline: newPlanDeadline.value || undefined,
    }

    savePlans()

    // 重置表单
    resetForm()

    uni.showToast({
      title: '更新成功',
      icon: 'success',
    })
  }
}

/**
 * @description 获取分类对应的颜色
 */
function getCategoryColor(category: string) {
  const found = planCategories.find((c) => c.value === category)
  return found ? found.color : '#9ca3af'
}

/**
 * @description 获取计划项样式类
 */
function getPlanClass(plan: Plan) {
  return {
    'plan-done': plan.done,
    'plan-pending': !plan.done,
  }
}

/**
 * @description 获取复选框样式类
 */
function getCheckboxClass(plan: Plan) {
  return {
    checked: plan.done,
  }
}

/**
 * @description 清除所有已完成的计划
 */
function clearCompletedPlans() {
  uni.showModal({
    title: '清除确认',
    content: '确定要清除所有已完成的计划吗？',
    success: (res) => {
      if (res.confirm) {
        plans.value = plans.value.filter((p) => !p.done)
        savePlans()
        uni.showToast({
          title: '清除成功',
          icon: 'success',
        })
      }
    },
  })
}

/**
 * @description 格式化日期显示
 */
function formatDate(timestamp: string | undefined) {
  if (!timestamp) return ''

  try {
    const date = new Date(timestamp)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  } catch (e) {
    return ''
  }
}

// 页面生命周期
onLoad(() => {
  loadPlans()
})

onShow(() => {
  loadPlans()
})

// 重置表单
function resetForm() {
  newPlanTitle.value = ''
  newPlanDesc.value = ''
  newPlanCategory.value = '其他'
  newPlanDeadline.value = ''
  showValidation.value = false
  editingPlan.value = null
  showAddPlan.value = false
}

// 取消添加
function cancelAdd() {
  resetForm()
}

// 初始化编辑表单
function initEditForm() {
  if (editingPlan.value) {
    newPlanTitle.value = editingPlan.value.title
    newPlanDesc.value = editingPlan.value.desc || ''
    newPlanCategory.value = editingPlan.value.category
    newPlanDeadline.value = editingPlan.value.deadline || ''
  }
  showValidation.value = false
}
</script>

<template>
  <view class="page-container">
    <!-- 顶部导航栏 -->
    <view class="nav-wrapper">
      <HeadBar title="我的学习计划" :show-back="true" />
    </view>

    <!-- 主内容区域 -->
    <view class="main-wrapper">
      <scroll-view class="main-content" scroll-y>
        <!-- 加载状态 -->
        <view v-if="isLoading" class="skeleton-container">
          <view v-for="i in 3" :key="i" class="skeleton-plan"></view>
        </view>

        <view v-else class="content-wrapper">
          <!-- 计划统计卡片 -->
          <view class="stats-card">
            <view class="stat-item">
              <text class="stat-value">{{ groupedPlans.totalCount }}</text>
              <text class="stat-label">总计划</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ groupedPlans.pendingCount }}</text>
              <text class="stat-label">待完成</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ groupedPlans.completedCount }}</text>
              <text class="stat-label">已完成</text>
            </view>
            <view class="stat-item">
              <text class="stat-value-small">
                {{
                  Math.round((groupedPlans.completedCount / (groupedPlans.totalCount || 1)) * 100)
                }}
                <text class="stat-unit">%</text>
              </text>
              <text class="stat-label">完成率</text>
            </view>
          </view>

          <!-- 过滤和排序工具栏 -->
          <view class="toolbar">
            <view class="filter-group">
              <text
                v-for="option in filterOptions"
                :key="option.value"
                class="filter-option"
                :class="{ active: currentFilter === option.value }"
                @click="currentFilter = option.value"
              >
                {{ option.label }}
              </text>
            </view>
            <view class="sort-dropdown">
              <picker
                :range="sortOptions"
                range-key="label"
                :value="sortOptions.findIndex((o) => o.value === currentSort)"
                @change="(e) => (currentSort = sortOptions[e.detail.value].value)"
              >
                <view class="sort-text">
                  <text class="i-mdi-sort"></text>
                  <text>{{ sortOptions.find((o) => o.value === currentSort)?.label }}</text>
                </view>
              </picker>
            </view>
          </view>

          <!-- 计划列表 -->
          <view class="plan-card">
            <view class="plan-list">
              <view
                v-for="plan in filteredPlans"
                :key="plan.id"
                class="plan-item"
                :class="getPlanClass(plan)"
              >
                <view class="plan-checkbox" @click="togglePlanStatus(plan)">
                  <view class="checkbox" :class="getCheckboxClass(plan)">
                    <text v-if="plan.done" class="i-mdi-check check-icon"></text>
                  </view>
                </view>
                <view class="plan-info" @click="togglePlanStatus(plan)">
                  <text class="plan-name">{{ plan.title }}</text>
                  <view class="plan-meta">
                    <view
                      class="plan-category"
                      :style="{ backgroundColor: getCategoryColor(plan.category) }"
                    >
                      {{ plan.category }}
                    </view>
                    <text v-if="plan.deadline" class="plan-deadline">
                      <text class="i-mdi-calendar"></text>
                      {{ formatDate(plan.deadline) }}
                    </text>
                    <text v-if="plan.desc" class="plan-desc">{{ plan.desc }}</text>
                  </view>
                </view>
                <view class="plan-actions">
                  <text class="plan-edit-btn i-mdi-pencil" @click.stop="startEdit(plan)"></text>
                  <text
                    class="plan-delete-btn i-mdi-delete"
                    @click.stop="deletePlan(plan.id)"
                  ></text>
                </view>
              </view>

              <!-- 空状态 -->
              <view v-if="filteredPlans.length === 0" class="empty-plans">
                <text class="i-mdi-clipboard-outline empty-icon"></text>
                <text>
                  {{
                    currentFilter === 'completed'
                      ? '暂无已完成的计划'
                      : '暂无学习计划，快来添加吧！'
                  }}
                </text>
              </view>
            </view>
          </view>

          <!-- 操作区域 -->
          <view class="action-area">
            <button
              v-if="groupedPlans.completedCount > 0"
              class="clear-completed-btn"
              @click="clearCompletedPlans"
            >
              清除已完成计划
            </button>
          </view>

          <!-- 底部安全距离 -->
          <view class="bottom-safe-area"></view>
        </view>
      </scroll-view>
    </view>

    <!-- 悬浮添加按钮 -->
    <view class="add-btn" @click="showAddPlan = true">
      <text class="i-mdi-plus"></text>
    </view>

    <!-- 添加计划弹窗 -->
    <view v-if="showAddPlan" class="add-plan-popup">
      <view class="popup-mask" @click="cancelAdd"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">{{ editingPlan ? '编辑计划' : '添加计划' }}</text>
          <text class="popup-close" @click="cancelAdd">×</text>
        </view>

        <view class="popup-form">
          <view class="form-item">
            <text class="form-label">
              标题
              <text class="required">*</text>
            </text>
            <input
              class="form-textarea"
              style="height: 96rpx;"
              :class="{ 'input-error': !newPlanTitle.trim() && showValidation }"
              placeholder="请输入计划标题"
              v-model="newPlanTitle"
              maxlength="50"
            />
            <text v-if="!newPlanTitle.trim() && showValidation" class="error-text">
              请输入计划标题
            </text>
          </view>

          <view class="form-item">
            <text class="form-label">描述</text>
            <textarea
              class="form-textarea"
              placeholder="请输入计划描述（选填）"
              v-model="newPlanDesc"
              maxlength="200"
            ></textarea>
            <view class="char-count">{{ newPlanDesc.length }}/200</view>
          </view>

          <view class="form-item">
            <text class="form-label">分类</text>
            <picker
              :range="planCategories"
              range-key="value"
              :value="
                planCategories.findIndex(
                  (c) => c.value === (editingPlan ? editingPlan.category : newPlanCategory),
                )
              "
              @change="(e) => (newPlanCategory = planCategories[e.detail.value].value)"
            >
              <view class="category-picker">
                <view
                  class="selected-category"
                  :style="{ backgroundColor: getCategoryColor(newPlanCategory) }"
                >
                  {{ newPlanCategory }}
                </view>
                <text class="i-mdi-chevron-down"></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">截止日期</text>
            <picker
              mode="date"
              :value="newPlanDeadline || (editingPlan && editingPlan.deadline) || ''"
              @change="(e) => (newPlanDeadline = e.detail.value)"
            >
              <view class="date-picker">
                <text v-if="newPlanDeadline || (editingPlan && editingPlan.deadline)">
                  {{ newPlanDeadline || (editingPlan && editingPlan.deadline) }}
                </text>
                <text v-else class="placeholder">选择日期（选填）</text>
                <text class="i-mdi-calendar"></text>
              </view>
            </picker>
          </view>

          <view class="form-actions">
            <button class="cancel-btn" @click="cancelAdd">取消</button>
            <button class="submit-btn" @click="editingPlan ? updatePlan() : addPlan()">
              {{ editingPlan ? '更新' : '添加' }}
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
/* ==================== 基础布局 ==================== */
.page-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

.nav-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.2);

  // 添加安全距离支持
  // #ifdef MP-WEIXIN
  padding-top: var(--status-bar-height);
  // #endif
}

.main-wrapper {
  position: absolute;
  top: 120rpx;
  left: 0;
  right: 0;
  bottom: 0;

  // #ifdef MP-WEIXIN
  top: calc(120rpx + var(--status-bar-height));
  // #endif
}

.main-content {
  width: 100%;
  height: 100%;
  -webkit-overflow-scrolling: touch;
  contain: layout style paint;
  overscroll-behavior: contain;
}

.content-wrapper {
  padding: 32rpx;
  padding-bottom: 40rpx;
}

/* ==================== 计划统计卡片 ==================== */
.stats-card {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
  margin-bottom: 32rpx;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #00c9a7, #4caf50, #ffc107, #ff9800);
}

.stat-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 8rpx 0;
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-item:nth-child(1) {
  animation-delay: 0.1s;
}
.stat-item:nth-child(2) {
  animation-delay: 0.2s;
}
.stat-item:nth-child(3) {
  animation-delay: 0.3s;
}
.stat-item:nth-child(4) {
  animation-delay: 0.4s;
}

.stat-item:not(:last-child):after {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: #f0f0f0;
}

.stat-value {
  display: block;
  margin-bottom: 8rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: #00c9a7;
  line-height: 1.2;
}

.stat-value-small {
  display: block;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #00c9a7;
  line-height: 1.2;
}

.stat-unit {
  font-size: 24rpx;
  font-weight: normal;
  color: #00c9a7;
  opacity: 0.8;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1;
}

/* ==================== 工具栏 ==================== */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.filter-group {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.filter-option {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-option.active {
  color: #00c9a7;
  font-weight: 500;
}

.filter-option.active::after {
  content: '';
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 16rpx;
  height: 4rpx;
  background-color: #00c9a7;
  border-radius: 2rpx;
}

.sort-dropdown {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
  padding: 12rpx 20rpx;
}

.sort-text {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.sort-text text:first-child {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* ==================== 计划列表 ==================== */
.plan-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin-bottom: 32rpx;
}

.plan-list {
  padding: 8rpx 0;
}

.plan-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s ease;
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-item:active {
  background-color: rgba(0, 201, 167, 0.05);
}

.plan-checkbox {
  margin-right: 16rpx;
  padding: 4rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #00c9a7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: #00c9a7;
}

.check-icon {
  color: #fff;
  font-size: 24rpx;
}

.plan-info {
  flex: 1;
  padding-right: 16rpx;
}

.plan-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  font-weight: 500;
}

.plan-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.plan-category {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  color: #fff;
}

.plan-deadline {
  font-size: 22rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.plan-deadline text {
  margin-right: 4rpx;
}

.plan-desc {
  font-size: 24rpx;
  color: #666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.plan-actions {
  display: flex;
  align-items: center;
}

.plan-edit-btn,
.plan-delete-btn {
  padding: 10rpx;
  font-size: 32rpx;
}

.plan-edit-btn {
  color: #00c9a7;
  margin-right: 8rpx;
}

.plan-delete-btn {
  color: #ff4d4f;
}

.plan-done .plan-name {
  text-decoration: line-through;
  color: #999;
}

.empty-plans {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  display: inline-flex;
}

/* ==================== 操作区域 ==================== */
.action-area {
  margin-top: 24rpx;
  display: flex;
  justify-content: center;
}

.clear-completed-btn {
  font-size: 28rpx;
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.3);
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
}

/* ==================== 添加按钮 ==================== */
.add-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.4);
  z-index: 100;
  transition: transform 0.3s ease;

  /* 性能优化：强制GPU加速 */
  transform: translateZ(0);
  will-change: transform, box-shadow;
  backface-visibility: hidden;

  // #ifdef MP-WEIXIN
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  // #endif
}

.add-btn:active {
  transform: scale(0.95) translateZ(0);
}

/* ==================== 添加计划弹窗 ==================== */
.add-plan-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: flex;
  align-items: flex-end;
}

.popup-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  animation: fadeIn 0.3s ease-out;
}

.popup-content {
  position: relative;
  width: 100%;
  max-height: 80vh;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;

  // #ifdef MP-WEIXIN
  padding-bottom: env(safe-area-inset-bottom);
  // #endif
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.popup-header::before {
  content: '';
  position: absolute;
  top: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #e0e0e0;
  border-radius: 3rpx;
}

.popup-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.popup-close {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #666;
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.popup-close:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.popup-form {
  padding: 24rpx 32rpx 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 32rpx;
  position: relative;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #374151;
  margin-bottom: 16rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

.required {
  color: #ef4444;
  margin-left: 4rpx;
  font-size: 28rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  padding: 24rpx 20rpx;
  font-size: 30rpx;
  background: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.form-input:focus,
.form-textarea:focus {
  border-color: #00c9a7;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(0, 201, 167, 0.1);
}

.form-input.input-error,
.form-textarea.input-error {
  border-color: #ef4444;
  background: #fef2f2;
}

.form-textarea {
  height: 180rpx;
  resize: none;
  line-height: 1.6;
}

.char-count {
  position: absolute;
  right: 16rpx;
  bottom: -28rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.error-text {
  position: absolute;
  left: 0;
  bottom: -28rpx;
  font-size: 24rpx;
  color: #ef4444;
}

.category-picker,
.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  padding: 24rpx 20rpx;
  font-size: 30rpx;
  background: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.category-picker:active,
.date-picker:active {
  border-color: #00c9a7;
  box-shadow: 0 0 0 6rpx rgba(0, 201, 167, 0.1);
}

.selected-category {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.placeholder {
  color: #9ca3af;
  font-style: italic;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 48rpx;
  padding-top: 24rpx;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
}

.cancel-btn {
  color: #6b7280;
  background: #f8f9fa;
  border: 2rpx solid #e5e7eb;
}

.cancel-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.submit-btn {
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);
  border: none;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
}

/* ==================== 骨架屏 ==================== */
.skeleton-container {
  padding: 32rpx;
}

.skeleton-plan {
  height: 120rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  will-change: background-position;
}

/* ==================== 底部安全距离 ==================== */
.bottom-safe-area {
  height: 160rpx;

  // #ifdef MP-WEIXIN
  height: calc(160rpx + env(safe-area-inset-bottom));
  // #endif
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4rpx);
  }
  75% {
    transform: translateX(4rpx);
  }
}

.input-error {
  animation: shake 0.3s ease-in-out;
}

/* ==================== 响应式优化 ==================== */
@media screen and (max-width: 375px) {
  .popup-form {
    padding: 20rpx 24rpx 24rpx;
  }

  .form-item {
    margin-bottom: 28rpx;
  }

  .form-actions {
    gap: 16rpx;
  }

  .cancel-btn,
  .submit-btn {
    height: 88rpx;
    font-size: 30rpx;
  }
}

/* ==================== 深色模式支持 ==================== */
@media (prefers-color-scheme: dark) {
  .popup-content {
    background: #1f2937;
  }

  .popup-title {
    color: #f9fafb;
  }

  .form-label {
    color: #e5e7eb;
  }

  .form-input,
  .form-textarea,
  .category-picker,
  .date-picker {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .placeholder {
    color: #9ca3af;
  }

  .cancel-btn {
    background: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }
}
</style>
