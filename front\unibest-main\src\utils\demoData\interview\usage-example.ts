/**
 * @description 面试房间演示数据使用示例
 * 展示如何在面试房间页面中使用演示数据
 *
 * 注意：这个文件仅用于展示使用方法，不会被实际引入到项目中
 */

import { interviewRoomHelper } from './roomHelper'
import { initInterviewRoomDemoData, createMockWebSocket } from './roomInitializer'

/**
 * 面试房间页面中使用演示数据的示例
 */
export const interviewRoomExample = async () => {
  // 1. 初始化面试房间演示数据
  // 在页面的onLoad或onMounted生命周期中调用
  const isDemoMode = interviewRoomHelper.init()
  console.log('是否使用演示模式:', isDemoMode)

  // 2. 获取会话信息
  // 替代原来的getSessionInfo API调用
  const sessionInfo = await interviewRoomHelper.getSessionInfo({ jobId: 1001 })
  console.log('会话信息:', sessionInfo)

  // 3. 获取问题列表
  // 替代原来的getQuestions API调用
  const questions = await interviewRoomHelper.getQuestions({ count: 10 })
  console.log('问题列表:', questions)

  // 4. 获取当前问题
  // 替代原来的getQuestion API调用
  const question = await interviewRoomHelper.getQuestion(1)
  console.log('当前问题:', question)

  // 5. 提交答案
  // 替代原来的submitAnswer API调用
  const answerResult = await interviewRoomHelper.submitAnswer({
    id: 1,
    answer: '这是我的回答',
    duration: 120,
  })
  console.log('提交答案结果:', answerResult)

  // 6. 创建WebSocket连接
  // 替代原来的WebSocket创建
  const ws = interviewRoomHelper.createWebSocket('wss://example.com/ws')

  // 设置WebSocket事件处理
  ws.onopen = (event) => {
    console.log('WebSocket连接已打开:', event)

    // 发送消息
    ws.send(
      JSON.stringify({
        type: 'audio',
        data: 'base64-audio-data',
      }),
    )
  }

  ws.onmessage = (event) => {
    console.log('收到WebSocket消息:', event.data)

    // 处理消息
    try {
      const data = JSON.parse(event.data)

      if (data.type === 'audioAnalysis') {
        console.log('音频分析结果:', data)
      } else if (data.type === 'videoAnalysis') {
        console.log('视频分析结果:', data)
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  ws.onclose = (event) => {
    console.log('WebSocket连接已关闭:', event)
  }

  ws.onerror = (event) => {
    console.error('WebSocket连接错误:', event)
  }

  // 7. 分析音频
  // 替代原来的analyzeAudio API调用
  const audioAnalysis = await interviewRoomHelper.analyzeAudio({
    questionId: 1,
    audio: 'base64-audio-data',
  })
  console.log('音频分析结果:', audioAnalysis)

  // 8. 分析视频
  // 替代原来的analyzeVideo API调用
  const videoAnalysis = await interviewRoomHelper.analyzeVideo({
    questionId: 1,
    video: 'base64-video-data',
  })
  console.log('视频分析结果:', videoAnalysis)

  // 9. 分析文本
  // 替代原来的analyzeText API调用
  const textAnalysis = await interviewRoomHelper.analyzeText({
    questionId: 1,
    text: '这是我的回答文本',
  })
  console.log('文本分析结果:', textAnalysis)

  // 10. 综合分析
  // 替代原来的comprehensiveAnalysis API调用
  const comprehensiveAnalysis = await interviewRoomHelper.comprehensiveAnalysis({
    questionId: 1,
    audio: 'base64-audio-data',
    video: 'base64-video-data',
    text: '这是我的回答文本',
  })
  console.log('综合分析结果:', comprehensiveAnalysis)

  // 11. 结束面试
  // 替代原来的endInterview API调用
  const endResult = await interviewRoomHelper.endInterview({
    feedback: '面试体验很好',
  })
  console.log('结束面试结果:', endResult)

  // 12. 获取面试报告
  // 替代原来的getInterviewReport API调用
  const report = await interviewRoomHelper.getInterviewReport()
  console.log('面试报告:', report)

  // 13. 获取报告摘要
  // 替代原来的getReportSummary API调用
  const summary = await interviewRoomHelper.getReportSummary()
  console.log('报告摘要:', summary)

  // 14. 安全API调用示例
  // 使用safeApiCall方法包装原始API调用，确保在API调用失败时使用演示数据
  try {
    const result = await interviewRoomHelper.safeApiCall(
      // 原始API调用
      () => fetch('https://api.example.com/data').then((res) => res.json()),
      // 对应的API路径
      'interview-room/getData',
      // 请求参数
      { id: 123 },
    )
    console.log('安全API调用结果:', result)
  } catch (error) {
    console.error('安全API调用失败:', error)
  }
}

/**
 * 在面试房间页面的onLoad或onMounted生命周期中使用
 */
export const onPageLoad = () => {
  // 初始化面试房间演示数据
  interviewRoomHelper.init()

  // 其他初始化逻辑...
}

/**
 * 在面试房间页面的onUnload或onUnmounted生命周期中使用
 */
export const onPageUnload = () => {
  // 关闭所有WebSocket连接
  interviewRoomHelper.closeAllWebSockets()

  // 其他清理逻辑...
}

export default {
  interviewRoomExample,
  onPageLoad,
  onPageUnload,
}
