/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/404/404" |
       "/pages/about/about" |
       "/pages/about/agreement" |
       "/pages/about/index" |
       "/pages/about/privacy-policy" |
       "/pages/achievement/wall" |
       "/pages/aichat/index" |
       "/pages/assessment/initial" |
       "/pages/assessment/result" |
       "/pages/auth/forgetpassword" |
       "/pages/auth/login" |
       "/pages/auth/register" |
       "/pages/interview/detail" |
       "/pages/interview/index" |
       "/pages/interview/job-detail" |
       "/pages/interview/result" |
       "/pages/interview/room" |
       "/pages/interview/select" |
       "/pages/learning/all-question-banks" |
       "/pages/learning/all-questions" |
       "/pages/learning/book-reader" |
       "/pages/learning/book" |
       "/pages/learning/community" |
       "/pages/learning/data-center" |
       "/pages/learning/detail" |
       "/pages/learning/index" |
       "/pages/learning/learning" |
       "/pages/learning/plan" |
       "/pages/learning/practice-question" |
       "/pages/learning/practice-result" |
       "/pages/learning/practice" |
       "/pages/learning/question-bank-detail" |
       "/pages/learning/question-bank" |
       "/pages/learning/question-comments" |
       "/pages/learning/question-detail" |
       "/pages/learning/recommend" |
       "/pages/learning/resource-detail" |
       "/pages/learning/resources" |
       "/pages/learning/video-player" |
       "/pages/learning/video" |
       "/pages/message/index" |
       "/pages/pay/pay-confirm" |
       "/pages/pay/pay" |
       "/pages/size-test/index" |
       "/pages/user/ability-assessment" |
       "/pages/user/agreement" |
       "/pages/user/center" |
       "/pages/user/feedback-list" |
       "/pages/user/feedback" |
       "/pages/user/growth-detail" |
       "/pages/user/history" |
       "/pages/user/more-setting" |
       "/pages/user/preference-settings" |
       "/pages/user/privacy-policy" |
       "/pages/user/privacy" |
       "/pages/user/profile" |
       "/pages/user/resume-preview" |
       "/pages/user/resume";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/about/about" | "/pages/size-test/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
