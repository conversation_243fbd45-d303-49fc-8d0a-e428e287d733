<template>
  <div class="app-container">
    <!-- 应用内容 -->
  </div>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import 'tailwindcss'
// 在App.vue中全局获取系统信息，供HeadBar组件使用
onLaunch(() => {
  console.log('App Launch')

  // 获取系统信息并存储到全局数据中
  try {
    const systemInfo = uni.getSystemInfoSync()

    // 创建全局数据对象
    const globalData = {
      system: systemInfo,
      capsule: null as any,
      navBarHeight: 88,
      statusBarHeight: systemInfo.statusBarHeight || 0,
    }
    console.log('systemInfo', systemInfo)

    // #ifdef MP-WEIXIN
    // 微信小程序获取胶囊按钮信息
    try {
      const menuButton = wx.getMenuButtonBoundingClientRect()
      globalData.capsule = menuButton
      // 计算导航栏高度
      globalData.navBarHeight =
        globalData.statusBarHeight +
        menuButton.height +
        (menuButton.top - globalData.statusBarHeight) * 2
    } catch (error) {
      console.warn('获取胶囊信息失败:', error)
    }
    // #endif

    // #ifdef H5
    // H5端固定高度
    globalData.navBarHeight = globalData.statusBarHeight + 44
    globalData.capsule = {
      top: globalData.statusBarHeight + 4,
      height: 36,
      width: 72,
      right: 0,
    }
    // #endif

    // #ifdef APP-PLUS
    // APP端处理
    globalData.navBarHeight = globalData.statusBarHeight + 44
    globalData.capsule = {
      top: globalData.statusBarHeight + 4,
      height: 36,
      width: 72,
      right: 0,
    }
      // #endif

      // 将全局数据挂载到全局对象上
      ; (getApp() as any).globalData = globalData

    console.log('全局数据初始化完成:', globalData)
  } catch (error) {
    console.error('初始化全局数据失败:', error)
  }
})

onShow(() => {
  console.log('App Show')
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style scoped lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

// 应用容器样式
.app-container {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
}
</style>
