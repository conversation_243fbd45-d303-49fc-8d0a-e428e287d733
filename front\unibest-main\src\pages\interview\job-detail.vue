<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import HeadBar from '@/components/HeadBar.vue'
import {
  getJobDetail,
  getSampleQuestions,
  getRelatedJobs,
  toggleFavorite,
  shareJob as shareJobApi
} from '@/service/interview-job-detail'
import type { JobDetail, JobBasicInfo, InterviewQuestion, GetJobDetailResponse } from '@/types/interview'

/**
 * @description 面试岗位详情页面
 * 展示具体岗位的面试信息，包括难度、题目数量、面试流程等
 * 用户可以开始模拟面试或查看岗位要求
 */

// 演示数据常量
const DEMO_JOB_DETAIL: JobDetail = {
  id: 101,
  categoryId: 1,
  name: '前端开发工程师',
  company: '腾讯科技',
  logo: '/static/images/company-logo.png',
  difficulty: 3,
  duration: 30,
  questionCount: 10,
  interviewers: 156,
  passRate: '68%',
  tags: ['Vue.js', 'React', 'JavaScript', 'TypeScript'],
  description: '负责Web前端开发，参与产品功能设计与开发，优化用户体验',
  requirements: [
    '熟练掌握HTML、CSS、JavaScript基础知识',
    '熟悉Vue.js或React等前端框架',
    '了解TypeScript开发',
    '具备良好的代码规范和团队协作能力',
    '有移动端H5开发经验者优先',
  ],
  /* TODO: 下列字段需要后端提供 暂未实现 */
  interviewProcess: [
    {
      step: 1,
      name: '自我介绍',
      duration: 3,
      description: '简单介绍个人背景和项目经验',
    },
    {
      step: 2,
      name: '技术基础',
      duration: 10,
      description: '考察前端基础知识和编程能力',
    },
    {
      step: 3,
      name: '项目经验',
      duration: 12,
      description: '深入了解项目经验和技术栈',
    },
    {
      step: 4,
      name: '场景题目',
      duration: 5,
      description: '解决实际工作中的技术问题',
    },
  ],
  skillPoints: [
    { name: 'JavaScript', weight: 25, level: 'high' },
    { name: 'Vue.js/React', weight: 30, level: 'high' },
    { name: 'CSS/HTML', weight: 20, level: 'medium' },
    { name: '工程化工具', weight: 15, level: 'medium' },
    { name: '项目经验', weight: 10, level: 'low' },
  ],
  benefits: [
    '提升前端技术面试能力',
    '了解大厂面试流程',
    '获得详细的能力评估报告',
    '针对性的学习建议',
  ],
}

const DEMO_RELATED_JOBS: JobBasicInfo[] = [
  {
    id: 102,
    categoryId: 1,
    name: 'React开发工程师',
    company: '字节跳动',
    difficulty: 3,
    duration: 35,
    questionCount: 12,
    interviewers: 89,
    passRate: '72%',
    tags: ['React', 'TypeScript', 'Node.js'],
  },
  {
    id: 103,
    categoryId: 1,
    name: '全栈开发工程师',
    company: '阿里巴巴',
    difficulty: 4,
    duration: 45,
    questionCount: 15,
    interviewers: 203,
    passRate: '59%',
    tags: ['Vue.js', 'Node.js', 'MongoDB'],
  },
  {
    id: 104,
    categoryId: 1,
    name: 'UI开发工程师',
    company: '美团',
    difficulty: 2,
    duration: 25,
    questionCount: 8,
    interviewers: 67,
    passRate: '75%',
    tags: ['CSS', 'JavaScript', 'Vue.js'],
  },
]

const DEMO_SAMPLE_QUESTIONS: InterviewQuestion[] = [
  {
    id: 1001,
    type: 'technical',
    question: '请解释JavaScript中的闭包概念，并给出一个实际应用场景。',
    difficulty: 3,
    timeLimit: 5,
    tags: ['JavaScript', '闭包', '作用域', '内存管理'],
  },
  {
    id: 1002,
    type: 'project',
    question: '请介绍一个你参与的前端项目，重点说明你在其中承担的技术难点和解决方案。',
    difficulty: 2,
    timeLimit: 8,
    tags: ['项目经验', '技术选型', '问题解决', '团队协作'],
  },
  {
    id: 1003,
    type: 'technical',
    question: '如何优化Vue.js应用的性能？请从多个角度进行分析。',
    difficulty: 4,
    timeLimit: 6,
    tags: ['Vue.js', '性能优化', '组件设计', '构建优化'],
  },
  {
    id: 1004,
    type: 'problem',
    question: '假设用户反馈页面加载很慢，你会如何排查和解决这个问题？',
    difficulty: 3,
    timeLimit: 7,
    tags: ['性能分析', '问题排查', '网络优化', '用户体验'],
  },
]

// 页面参数
const pageParams = ref({
  jobId: '',
  categoryId: '',
})

// 加载状态
const loading = ref(true)

// 岗位详情数据（使用类型定义）
const jobDetail = ref<JobDetail>(DEMO_JOB_DETAIL)

// 是否收藏
const isFavorited = ref(false)

// 推荐相关岗位（使用类型定义）
const relatedJobs = ref<JobBasicInfo[]>(DEMO_RELATED_JOBS)

// 样题显示状态
const showSampleQuestions = ref(false)

// 样题数据（使用类型定义）
const sampleQuestions = ref<InterviewQuestion[]>(DEMO_SAMPLE_QUESTIONS)

// 当前显示的样题索引
const currentQuestionIndex = ref(0)

/**
 * @description 页面加载时获取参数并加载数据
 */
onLoad((options: any) => {
  if (options.jobId) {
    pageParams.value.jobId = options.jobId
  }
  if (options.categoryId) {
    pageParams.value.categoryId = options.categoryId
  }

  // 加载岗位数据
  loadJobDetail()
})

/**
 * @description 加载岗位详情数据
 */
const loadJobDetail = async () => {
  loading.value = true

  try {
    // 获取岗位ID
    const jobId = parseInt(pageParams.value.jobId)

    console.log('正在加载岗位详情...', { jobId })

    // 调用API获取岗位详情
    const detailResponse = await getJobDetail(jobId)
    console.log('岗位详情API响应:', detailResponse)

    if (detailResponse.code === 200 && detailResponse.data) {
      // 更新岗位详情
      const apiJobDetail = detailResponse.data

      // 转换API数据格式到页面需要的格式
      jobDetail.value = {
        id: apiJobDetail.id,
        categoryId: apiJobDetail.categoryId || 1,
        name: apiJobDetail.name,
        company: apiJobDetail.company,
        logo: apiJobDetail.logo || '/static/images/company-logo.png',
        difficulty: apiJobDetail.difficulty,
        duration: apiJobDetail.duration || 30,
        questionCount: apiJobDetail.questionCount,
        interviewers: apiJobDetail.interviewers || 156,
        passRate: apiJobDetail.passRate || '68%',
        tags: apiJobDetail.tags || [],
        description: apiJobDetail.description,
        requirements: apiJobDetail.requirements || [],
        interviewProcess: apiJobDetail.interviewProcess || DEMO_JOB_DETAIL.interviewProcess,
        skillPoints: apiJobDetail.skillPoints || DEMO_JOB_DETAIL.skillPoints,
        benefits: apiJobDetail.benefits || DEMO_JOB_DETAIL.benefits,
      }

      // 设置收藏状态
      isFavorited.value = apiJobDetail.isFavorited || false

      console.log('岗位详情加载成功')
    }

    // 加载相关岗位
    try {
      const relatedResponse = await getRelatedJobs(jobId, 3)
      if (relatedResponse.code === 200 && relatedResponse.data?.relatedJobs) {
        relatedJobs.value = relatedResponse.data.relatedJobs.map((job: any) => ({
          id: job.id,
          categoryId: jobDetail.value.categoryId,
          name: job.name,
          company: job.company,
          difficulty: job.difficulty,
          duration: 30, // 模拟数据
          questionCount: 10, // 模拟数据
          interviewers: 100, // 模拟数据
          passRate: '70%', // 模拟数据
          tags: job.tags || [],
        }))
      }
    } catch (error) {
      console.warn('加载相关岗位失败:', error)
    }

    // 加载示例问题
    try {
      const questionsResponse = await getSampleQuestions(jobId, 4)
      console.log(questionsResponse);
      if (questionsResponse.code === 200 && questionsResponse.data?.questions) {
        sampleQuestions.value = questionsResponse.data.questions.map((q: any) => ({
          id: q.questionId,
          type: 'technical', // 默认类型
          question: q.content,
          difficulty: q.difficulty,
          timeLimit: 5, // 默认时间
          tags: q.tags || [], // 使用 tags 数据
        }))
        console.log(sampleQuestions.value);
      }
    } catch (error) {
      console.warn('加载示例问题失败:', error)
    }

    uni.showToast({
      title: '数据加载成功',
      icon: 'success',
      duration: 1500,
    })

  } catch (error) {
    console.error('加载岗位详情失败:', error)

    // API调用失败，使用演示数据
    console.log('API调用失败，使用演示数据')

    // 根据传入的jobId动态调整演示数据
    const dynamicJobDetail = { ...DEMO_JOB_DETAIL }
    if (pageParams.value.jobId) {
      dynamicJobDetail.id = parseInt(pageParams.value.jobId)
    }
    if (pageParams.value.categoryId) {
      dynamicJobDetail.categoryId = parseInt(pageParams.value.categoryId)
    }

    jobDetail.value = dynamicJobDetail
    relatedJobs.value = DEMO_RELATED_JOBS
    sampleQuestions.value = DEMO_SAMPLE_QUESTIONS

    // 显示用户友好的提示信息
    uni.showToast({
      title: '使用演示数据',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 获取难度级别文本
 */
const getDifficultyText = computed(() => {
  const level = jobDetail.value.difficulty
  const difficultyMap = {
    1: '入门级',
    2: '初级',
    3: '中级',
    4: '高级',
    5: '专家级',
  }
  return difficultyMap[level] || '未知'
})

/**
 * @description 获取难度颜色
 */
const getDifficultyColor = computed(() => {
  const level = jobDetail.value.difficulty
  const colorMap = {
    1: '#10B981',
    2: '#059669',
    3: '#F59E0B',
    4: '#DC2626',
    5: '#7C2D12',
  }
  return colorMap[level] || '#6B7280'
})

/**
 * @description 切换收藏状态
 */
const toggleFavoriteStatus = async () => {
  try {
    const newFavoriteStatus = !isFavorited.value

    // 调用API更新收藏状态
    const response = await toggleFavorite(jobDetail.value.id, newFavoriteStatus)

    if (response.code === 200) {
      isFavorited.value = newFavoriteStatus
      uni.showToast({
        title: response.message || (isFavorited.value ? '已收藏' : '已取消收藏'),
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)

    // API调用失败，仍然更新本地状态（演示模式）
    isFavorited.value = !isFavorited.value
    uni.showToast({
      title: isFavorited.value ? '已收藏（演示）' : '已取消收藏（演示）',
      icon: 'success',
      duration: 1500,
    })
  }
}

/**
 * @description 开始模拟面试
 */
const startInterview = () => {
  uni.showModal({
    title: '开始面试',
    content: `确定开始《${jobDetail.value.name}》的模拟面试吗？预计耗时${jobDetail.value.duration}分钟。`,
    confirmText: '开始面试',
    cancelText: '再想想',
    success: (res) => {
      if (res.confirm) {
        // 跳转到面试房间页面
        uni.navigateTo({
          url: `/pages/interview/room?jobId=${jobDetail.value.id}&jobName=${encodeURIComponent(jobDetail.value.name)}&company=${encodeURIComponent(jobDetail.value.company)}&duration=${jobDetail.value.duration}&questionCount=${jobDetail.value.questionCount}`,
        })
      }
    },
  })
}

/**
 * @description 查看样题
 */
const viewSampleQuestions = () => {
  showSampleQuestions.value = true
}

/**
 * @description 关闭样题弹窗
 */
const closeSampleQuestions = () => {
  showSampleQuestions.value = false
  currentQuestionIndex.value = 0 // 重置索引
}

/**
 * @description 从样题弹窗开始面试
 */
const startFromSampleModal = () => {
  closeSampleQuestions()
  startInterview()
}

/**
 * @description 切换到上一个样题
 */
const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

/**
 * @description 切换到下一个样题
 */
const nextQuestion = () => {
  if (currentQuestionIndex.value < sampleQuestions.value.length - 1) {
    currentQuestionIndex.value++
  }
}

/**
 * @description 获取当前显示的样题
 */
const currentQuestion = computed(() => {
  return sampleQuestions.value[currentQuestionIndex.value] || null
})

/**
 * @description 获取题目类型文本
 */
const getQuestionTypeText = (type: string) => {
  const typeMap = {
    technical: '技术问题',
    project: '项目经验',
    problem: '问题解决',
    behavior: '行为面试',
    product: '产品设计',
    analysis: '数据分析',
    design: '方案设计',
  }
  return typeMap[type] || '其他'
}

/**
 * @description 获取题目难度颜色
 */
const getQuestionDifficultyColor = (difficulty: number) => {
  const colorMap = {
    1: '#10B981',
    2: '#059669',
    3: '#F59E0B',
    4: '#DC2626',
    5: '#7C2D12',
  }
  return colorMap[difficulty] || '#6B7280'
}

/**
 * @description 查看相关岗位
 */
const viewRelatedJob = (jobId: number) => {
  // 获取相关岗位的categoryId
  const relatedJob = relatedJobs.value.find(job => job.id === jobId)
  const categoryId = relatedJob?.categoryId || jobDetail.value.categoryId

  uni.redirectTo({
    url: `/pages/interview/job-detail?jobId=${jobId}&categoryId=${categoryId}`,
  })
}

/**
 * @description 查看更多相关岗位
 */
const viewMoreJobs = () => {
  uni.navigateTo({
    url: `/pages/interview/select?categoryId=${jobDetail.value.categoryId}`,
  })
}



/**
 * @description 返回岗位列表
 */
const goBack = () => {
  uni.navigateBack({
    delta: 1,
  })
}
</script>

<template>
  <view class="job-detail-container">
    <!-- 顶部导航栏 -->
    <HeadBar title="岗位详情" :show-back="true" @back-click="goBack" />

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <text class="i-mdi-loading loading-icon"></text>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view v-else class="main-content" scroll-y show-scrollbar="false">
      <!-- 岗位基本信息 -->
      <view class="job-basic-info">
        <view class="job-header">
          <view class="job-title-section">
            <view class="title-content">
              <text class="job-title">{{ jobDetail.name }}</text>
              <text class="company-name">{{ jobDetail.company }}</text>
            </view>
            <button class="favorite-btn" @click="toggleFavoriteStatus">
              <text :class="[isFavorited ? 'i-mdi-heart' : 'i-mdi-heart-outline', 'favorite-icon']"></text>
            </button>
          </view>

          <!-- 难度和统计信息 -->
          <view class="difficulty-stats">
            <view class="difficulty-badge" :style="{ backgroundColor: getDifficultyColor }">
              <text class="difficulty-text">{{ getDifficultyText }}</text>
            </view>
            <view class="stats-info">
              <view class="stat-item">
                <text class="i-mdi-clock stat-icon"></text>
                <text class="stat-text">{{ jobDetail.duration }}分钟</text>
              </view>
              <view class="stat-item">
                <text class="i-mdi-help-circle stat-icon"></text>
                <text class="stat-text">{{ jobDetail.questionCount }}道题</text>
              </view>
              <view class="stat-item">
                <text class="i-mdi-account-group stat-icon"></text>
                <text class="stat-text">{{ jobDetail.interviewers }}人已练习</text>
              </view>
            </view>
          </view>

          <!-- 通过率 -->
          <view class="pass-rate-section">
            <text class="pass-rate-label">通过率</text>
            <text class="pass-rate-value">{{ jobDetail.passRate }}</text>
          </view>

          <!-- 技能标签 -->
          <view class="tags-section">
            <text class="tags-label">核心技能：</text>
            <view class="tags-list">
              <text class="tag" v-for="tag in jobDetail.tags" :key="tag">{{ tag }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 岗位描述 -->
      <view class="job-description-section">
        <view class="section-header">
          <text class="section-title">岗位描述</text>
        </view>
        <text class="description-text">{{ jobDetail.description }}</text>
      </view>

      <!-- 岗位要求 -->
      <view class="requirements-section">
        <view class="section-header">
          <text class="section-title">岗位要求</text>
        </view>
        <view class="requirements-list">
          <view class="requirement-item" v-for="(req, index) in jobDetail.requirements" :key="index">
            <text class="i-mdi-check-circle requirement-icon"></text>
            <text class="requirement-text">{{ req }}</text>
          </view>
        </view>
      </view>

      <!-- 面试流程 -->
      <view class="interview-process-section">
        <view class="section-header">
          <text class="section-title">面试流程</text>
          <text class="total-time">共{{ jobDetail.duration }}分钟</text>
        </view>
        <view class="process-steps">
          <view class="step-item" v-for="step in jobDetail.interviewProcess" :key="step.step">
            <view class="step-number">{{ step.step }}</view>
            <view class="step-content">
              <text class="step-name">{{ step.name }}</text>
              <text class="step-duration">{{ step.duration }}分钟</text>
              <text class="step-description">{{ step.description }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 技能考查重点 -->
      <view class="skill-points-section">
        <view class="section-header">
          <text class="section-title">技能考查重点</text>
        </view>
        <view class="skill-points-list">
          <view class="skill-point" v-for="skill in jobDetail.skillPoints" :key="skill.name">
            <view class="skill-info">
              <text class="skill-name">{{ skill.name }}</text>
              <text class="skill-weight">{{ skill.weight }}%</text>
            </view>
            <view class="skill-level" :class="skill.level">
              <view class="level-bar">
                <view class="level-fill" :style="{ width: skill.weight + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 练习收益 -->
      <view class="benefits-section">
        <view class="section-header">
          <text class="section-title">练习收益</text>
        </view>
        <view class="benefits-list">
          <view class="benefit-item" v-for="(benefit, index) in jobDetail.benefits" :key="index">
            <text class="i-mdi-star benefit-icon"></text>
            <text class="benefit-text">{{ benefit }}</text>
          </view>
        </view>
      </view>

      <!-- 相关岗位推荐 -->
      <view class="related-jobs-section" v-if="relatedJobs.length > 0">
        <view class="section-header">
          <text class="section-title">相关岗位</text>
          <text class="more-jobs" @click="viewMoreJobs">查看更多</text>
        </view>
        <view class="related-jobs-list">
          <view class="related-job-item" v-for="job in relatedJobs" :key="job.id" @click="viewRelatedJob(job.id)">
            <view class="job-info">
              <text class="job-name">{{ job.name }}</text>
              <text class="job-company">{{ job.company }}</text>
              <view class="job-meta">
                <text class="job-difficulty">难度{{ job.difficulty }}</text>
                <text class="job-duration">{{ job.duration }}分钟</text>
                <text class="job-pass-rate">通过率{{ job.passRate }}</text>
              </view>
            </view>
            <text class="i-mdi-chevron-right arrow-icon"></text>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <button class="action-btn secondary-btn" @click="viewSampleQuestions">
        <text class="i-mdi-file-document-outline"></text>
        <text>查看样题</text>
      </button>
      <button class="action-btn primary-btn" @click="startInterview">
        <text class="i-mdi-play-circle"></text>
        <text>开始面试</text>
      </button>
    </view>

    <!-- 样题弹窗 -->
    <view class="sample-questions-modal" v-if="showSampleQuestions" @click="closeSampleQuestions">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">样题预览</text>
          <button class="close-btn" @click="closeSampleQuestions">
            <text class="i-mdi-close"></text>
          </button>
        </view>

        <view class="modal-body">
          <view class="questions-intro">
            <text class="intro-title">面试题目示例</text>
            <text class="intro-desc">
              以下是{{ jobDetail.name }}岗位的典型面试题目，帮助你提前了解面试内容和难度
            </text>
          </view>

          <!-- 样题导航 -->
          <view class="question-navigation">
            <view class="nav-info">
              <text class="current-index">{{ currentQuestionIndex + 1 }}</text>
              <text class="total-count">/ {{ sampleQuestions.length }}</text>
            </view>
            <view class="nav-dots">
              <view
                class="nav-dot"
                v-for="(_, index) in sampleQuestions"
                :key="index"
                :class="{ active: index === currentQuestionIndex }"
                @click="currentQuestionIndex = index"
              ></view>
            </view>
          </view>

          <!-- 当前样题显示 -->
          <view class="current-question" v-if="currentQuestion">
            <view class="question-item">
              <view class="question-header">
                <view class="question-number">{{ currentQuestionIndex + 1 }}</view>
                <view class="question-meta">
                  <text class="question-type">{{ getQuestionTypeText(currentQuestion.type) }}</text>
                  <view class="difficulty-indicator"
                    :style="{ backgroundColor: getQuestionDifficultyColor(currentQuestion.difficulty) }">
                    <text class="difficulty-text">难度{{ currentQuestion.difficulty }}</text>
                  </view>
                </view>
              </view>

              <text class="question-content">{{ currentQuestion.question }}</text>

              <view class="question-footer">
                <view class="time-info">
                  <text class="i-mdi-clock time-icon"></text>
                  <text class="time-text">建议回答时间：{{ currentQuestion.timeLimit }}分钟</text>
                </view>
                <view class="question-tags" v-if="currentQuestion.tags && currentQuestion.tags.length > 0">
                  <text class="tags-label">🏷️ 相关标签：</text>
                  <view class="tags-container">
                    <text class="tag-item" v-for="tag in currentQuestion.tags" :key="tag">{{ tag }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 切换按钮 -->
          <view class="question-controls">
            <button
              class="control-btn prev-btn"
              :disabled="currentQuestionIndex === 0"
              @click="prevQuestion"
            >
              <text class="i-mdi-chevron-left"></text>
              <text>上一题</text>
            </button>
            <button
              class="control-btn next-btn"
              :disabled="currentQuestionIndex === sampleQuestions.length - 1"
              @click="nextQuestion"
            >
              <text>下一题</text>
              <text class="i-mdi-chevron-right"></text>
            </button>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn secondary" @click="closeSampleQuestions">稍后再看</button>
          <button class="modal-btn primary" @click="startFromSampleModal">开始面试</button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.job-detail-container {
  position: relative;
  min-height: 100vh;
  background: #f5f7fa;

  // CSS变量定义
  --primary-color: #00c9a7;
  --primary-light: #4fd1c7;
  --secondary-color: #7c3aed;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --border-color: #e5e7eb;
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --radius-sm: 12rpx;
  --radius-md: 16rpx;
  --radius-lg: 20rpx;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 88rpx);
  background: var(--bg-primary);

  .loading-content {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    align-items: center;

    .loading-icon {
      font-size: 64rpx;
      color: var(--primary-color);
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 28rpx;
      color: var(--text-secondary);
    }
  }
}

// 主要内容
.main-content {
  height: calc(100vh - 88rpx - 120rpx);
  overflow-y: auto;
}

// 岗位基本信息
.job-basic-info {
  padding: 32rpx;
  margin-bottom: 20rpx;
  color: white;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));

  .job-header {
    .job-title-section {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .title-content {
        flex: 1;

        .job-title {
          display: block;
          margin-bottom: 8rpx;
          font-size: 36rpx;
          font-weight: 700;
          line-height: 1.3;
        }

        .company-name {
          display: block;
          font-size: 26rpx;
          opacity: 0.9;
        }
      }

      .favorite-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72rpx;
        height: 72rpx;
        font-size: 36rpx;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(0.9);
        }

        .favorite-icon {
          font-size: 36rpx;
          color: #fff;
        }
      }
    }

    .difficulty-stats {
      margin-bottom: 24rpx;

      .difficulty-badge {
        display: inline-block;
        padding: 8rpx 20rpx;
        margin-bottom: 16rpx;
        border-radius: var(--radius-lg);

        .difficulty-text {
          font-size: 24rpx;
          font-weight: 600;
          color: #fff;
        }
      }

      .stats-info {
        display: flex;
        flex-wrap: wrap;
        gap: 24rpx;

        .stat-item {
          display: flex;
          gap: 8rpx;
          align-items: center;

          .stat-icon {
            font-size: 24rpx;
            opacity: 0.9;
          }

          .stat-text {
            font-size: 24rpx;
            opacity: 0.9;
          }
        }
      }
    }

    .pass-rate-section {
      display: flex;
      gap: 12rpx;
      align-items: center;
      padding: 16rpx 24rpx;
      margin-bottom: 24rpx;
      background: rgba(255, 255, 255, 0.15);
      border-radius: var(--radius-md);

      .pass-rate-label {
        font-size: 24rpx;
        opacity: 0.9;
      }

      .pass-rate-value {
        font-size: 32rpx;
        font-weight: 700;
      }
    }

    .tags-section {
      .tags-label {
        display: block;
        margin-bottom: 12rpx;
        font-size: 24rpx;
        opacity: 0.9;
      }

      .tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;

        .tag {
          padding: 8rpx 16rpx;
          font-size: 22rpx;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.2);
          border-radius: var(--radius-sm);
        }
      }
    }
  }
}

// 通用章节样式
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: 700;
    color: var(--text-primary);
  }

  .total-time,
  .more-reviews,
  .more-jobs {
    font-size: 24rpx;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      color: var(--primary-light);
      transform: scale(0.95);
    }
  }
}

// 岗位描述
.job-description-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .description-text {
    font-size: 26rpx;
    line-height: 1.8;
    color: var(--text-secondary);
  }
}

// 岗位要求
.requirements-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .requirements-list {
    .requirement-item {
      display: flex;
      gap: 12rpx;
      align-items: flex-start;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .requirement-icon {
        flex-shrink: 0;
        margin-top: 4rpx;
        font-size: 24rpx;
        color: var(--success-color);
      }

      .requirement-text {
        font-size: 26rpx;
        line-height: 1.6;
        color: var(--text-secondary);
      }
    }
  }
}

// 面试流程
.interview-process-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .process-steps {
    .step-item {
      position: relative;
      display: flex;
      gap: 20rpx;
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;

        &::after {
          display: none;
        }
      }

      &::after {
        position: absolute;
        top: 52rpx;
        left: 26rpx;
        width: 2rpx;
        height: 40rpx;
        content: '';
        background: var(--border-color);
      }

      .step-number {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 52rpx;
        height: 52rpx;
        font-size: 24rpx;
        font-weight: 600;
        color: white;
        background: var(--primary-color);
        border-radius: 50%;
      }

      .step-content {
        flex: 1;
        padding-top: 6rpx;

        .step-name {
          display: block;
          margin-bottom: 4rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: var(--text-primary);
        }

        .step-duration {
          display: block;
          margin-bottom: 8rpx;
          font-size: 22rpx;
          color: var(--primary-color);
        }

        .step-description {
          display: block;
          font-size: 24rpx;
          line-height: 1.5;
          color: var(--text-secondary);
        }
      }
    }
  }
}

// 技能考查重点
.skill-points-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .skill-points-list {
    .skill-point {
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .skill-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .skill-name {
          font-size: 26rpx;
          font-weight: 500;
          color: var(--text-primary);
        }

        .skill-weight {
          font-size: 24rpx;
          color: var(--text-secondary);
        }
      }

      .skill-level {
        .level-bar {
          height: 12rpx;
          overflow: hidden;
          background: var(--bg-secondary);
          border-radius: 6rpx;

          .level-fill {
            height: 100%;
            background: var(--primary-color);
            border-radius: 6rpx;
            transition: width 0.3s ease;
          }
        }

        &.high .level-fill {
          background: var(--primary-color);
        }

        &.medium .level-fill {
          background: var(--warning-color);
        }

        &.low .level-fill {
          background: var(--success-color);
        }
      }
    }
  }
}

// 练习收益
.benefits-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .benefits-list {
    .benefit-item {
      display: flex;
      gap: 12rpx;
      align-items: flex-start;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .benefit-icon {
        flex-shrink: 0;
        margin-top: 4rpx;
        font-size: 24rpx;
        color: var(--accent-color);
      }

      .benefit-text {
        font-size: 26rpx;
        line-height: 1.6;
        color: var(--text-secondary);
      }
    }
  }
}

// 用户评价
.reviews-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .reviews-list {
    .review-item {
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);

      &:last-child {
        margin-bottom: 0;
      }

      .review-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .user-info {
          display: flex;
          gap: 12rpx;
          align-items: center;

          .user-avatar {
            width: 48rpx;
            height: 48rpx;
            border-radius: 50%;
          }

          .username {
            font-size: 26rpx;
            font-weight: 500;
            color: var(--text-primary);
          }
        }

        .rating-info {
          display: flex;
          flex-direction: column;
          gap: 4rpx;
          align-items: flex-end;

          .stars {
            display: flex;
            gap: 4rpx;

            .star {
              font-size: 20rpx;
              color: var(--border-color);

              &.active {
                color: var(--accent-color);
              }
            }
          }

          .review-date {
            font-size: 20rpx;
            color: var(--text-tertiary);
          }
        }
      }

      .review-comment {
        font-size: 24rpx;
        line-height: 1.6;
        color: var(--text-secondary);
      }
    }
  }
}

// 相关岗位
.related-jobs-section {
  padding: 32rpx;
  margin-bottom: 20rpx;
  background: var(--bg-primary);
  border-radius: var(--radius-md);

  .related-jobs-list {
    .related-job-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background: #e8f7f4;
        transform: scale(0.98);
      }

      .job-info {
        flex: 1;

        .job-name {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: var(--text-primary);
        }

        .job-company {
          display: block;
          margin-bottom: 12rpx;
          font-size: 24rpx;
          color: var(--text-secondary);
        }

        .job-meta {
          display: flex;
          gap: 16rpx;

          .job-difficulty,
          .job-duration,
          .job-pass-rate {
            font-size: 22rpx;
            color: var(--text-tertiary);
          }
        }
      }

      .arrow-icon {
        font-size: 32rpx;
        color: var(--text-tertiary);
      }
    }
  }
}

// 底部占位
.bottom-placeholder {
  height: 40rpx;
}

// 底部操作栏
.bottom-action-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  gap: 20rpx;
  padding: 20rpx 32rpx 40rpx;
  background: var(--bg-primary);
  border-top: 1rpx solid var(--border-color);

  .action-btn {
    display: flex;
    flex: 1;
    gap: 8rpx;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    font-size: 28rpx;
    font-weight: 600;
    border: none;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    text:first-child {
      font-size: 32rpx;
    }

    &.secondary-btn {
      color: var(--text-secondary);
      background: var(--bg-secondary);
      border: 2rpx solid var(--border-color);

      &:active {
        color: var(--primary-color);
        background: var(--bg-primary);
        border-color: var(--primary-color);
      }
    }

    &.primary-btn {
      color: #fff;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

      &:active {
        box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
      }
    }
  }
}

// 样题弹窗样式
.sample-questions-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(0, 0, 0, 0.6);

  .modal-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 640rpx;
    max-height: 80vh;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    animation: modalSlideIn 0.3s ease;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      border-bottom: 1rpx solid var(--border-color);

      .modal-title {
        font-size: 32rpx;
        font-weight: 700;
        color: var(--text-primary);
      }

      .close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        font-size: 32rpx;
        color: var(--text-tertiary);
        background: var(--bg-secondary);
        border: none;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background: var(--border-color);
          transform: scale(0.9);
        }
      }
    }

    .modal-body {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;

      .questions-intro {
        padding: 32rpx;
        border-bottom: 1rpx solid var(--border-color);

        .intro-title {
          display: block;
          margin-bottom: 12rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: var(--text-primary);
        }

        .intro-desc {
          display: block;
          font-size: 24rpx;
          line-height: 1.6;
          color: var(--text-secondary);
        }
      }

      .question-navigation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid var(--border-color);

        .nav-info {
          display: flex;
          align-items: baseline;
          gap: 4rpx;

          .current-index {
            font-size: 32rpx;
            font-weight: 700;
            color: var(--primary-color);
          }

          .total-count {
            font-size: 24rpx;
            color: var(--text-secondary);
          }
        }

        .nav-dots {
          display: flex;
          gap: 12rpx;
          align-items: center;

          .nav-dot {
            width: 16rpx;
            height: 16rpx;
            background: var(--border-color);
            border-radius: 50%;
            transition: all 0.3s ease;
            cursor: pointer;

            &.active {
              background: var(--primary-color);
              transform: scale(1.2);
            }

            &:active {
              transform: scale(0.9);
            }
          }
        }
      }

      .current-question {
        flex: 1;
        overflow-y: auto;

        .question-item {
          padding: 32rpx;

          .question-header {
            display: flex;
            gap: 20rpx;
            align-items: center;
            margin-bottom: 16rpx;

            .question-number {
              display: flex;
              flex-shrink: 0;
              align-items: center;
              justify-content: center;
              width: 48rpx;
              height: 48rpx;
              font-size: 24rpx;
              font-weight: 600;
              color: white;
              background: var(--primary-color);
              border-radius: 50%;
            }

            .question-meta {
              display: flex;
              gap: 12rpx;
              align-items: center;

              .question-type {
                padding: 6rpx 12rpx;
                font-size: 22rpx;
                color: var(--primary-color);
                background: rgba(0, 201, 167, 0.1);
                border-radius: var(--radius-sm);
              }

              .difficulty-indicator {
                padding: 6rpx 12rpx;
                border-radius: var(--radius-sm);

                .difficulty-text {
                  font-size: 20rpx;
                  font-weight: 500;
                  color: white;
                }
              }
            }
          }

          .question-content {
            display: block;
            margin-bottom: 20rpx;
            font-size: 26rpx;
            line-height: 1.7;
            color: var(--text-primary);
          }

          .question-footer {
            .time-info {
              display: flex;
              gap: 8rpx;
              align-items: center;
              margin-bottom: 16rpx;

              .time-icon {
                font-size: 24rpx;
                color: var(--text-tertiary);
              }

              .time-text {
                font-size: 22rpx;
                color: var(--text-tertiary);
              }
            }

            .question-tags {
              padding: 16rpx;
              background: rgba(0, 201, 167, 0.08);
              border-left: 4rpx solid var(--primary-color);
              border-radius: var(--radius-sm);

              .tags-label {
                display: block;
                margin-bottom: 12rpx;
                font-size: 22rpx;
                font-weight: 600;
                color: var(--primary-color);
              }

              .tags-container {
                display: flex;
                flex-wrap: wrap;
                gap: 8rpx;

                .tag-item {
                  padding: 6rpx 12rpx;
                  font-size: 20rpx;
                  font-weight: 500;
                  color: var(--primary-color);
                  background: rgba(0, 201, 167, 0.15);
                  border: 1rpx solid rgba(0, 201, 167, 0.3);
                  border-radius: var(--radius-sm);
                  transition: all 0.3s ease;

                  &:active {
                    background: rgba(0, 201, 167, 0.25);
                    transform: scale(0.95);
                  }
                }
              }
            }
          }
        }
      }

      .question-controls {
        display: flex;
        gap: 20rpx;
        padding: 24rpx 32rpx;
        border-top: 1rpx solid var(--border-color);

        .control-btn {
          display: flex;
          flex: 1;
          gap: 8rpx;
          align-items: center;
          justify-content: center;
          height: 72rpx;
          font-size: 26rpx;
          font-weight: 500;
          border: 2rpx solid var(--border-color);
          border-radius: var(--radius-md);
          background: var(--bg-primary);
          color: var(--text-secondary);
          transition: all 0.3s ease;

          &:not(:disabled) {
            &:active {
              background: var(--bg-secondary);
              transform: scale(0.98);
            }
          }

          &:disabled {
            opacity: 0.4;
            cursor: not-allowed;
          }

          &.prev-btn:not(:disabled) {
            border-color: var(--primary-color);
            color: var(--primary-color);

            &:active {
              background: rgba(0, 201, 167, 0.1);
            }
          }

          &.next-btn:not(:disabled) {
            border-color: var(--primary-color);
            color: var(--primary-color);

            &:active {
              background: rgba(0, 201, 167, 0.1);
            }
          }

          text:first-child,
          text:last-child {
            font-size: 28rpx;
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 20rpx;
      padding: 32rpx;
      border-top: 1rpx solid var(--border-color);

      .modal-btn {
        flex: 1;
        height: 80rpx;
        font-size: 28rpx;
        font-weight: 600;
        border: none;
        border-radius: var(--radius-md);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
        }

        &.secondary {
          color: var(--text-secondary);
          background: var(--bg-secondary);
          border: 2rpx solid var(--border-color);

          &:active {
            color: var(--primary-color);
            background: var(--bg-primary);
            border-color: var(--primary-color);
          }
        }

        &.primary {
          color: white;
          background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
          box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

          &:active {
            box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.9);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 响应式设计
@media (max-width: 750px) {
  .job-basic-info {
    padding: 24rpx;
  }

  .job-description-section,
  .requirements-section,
  .interview-process-section,
  .skill-points-section,
  .benefits-section,
  .reviews-section,
  .related-jobs-section {
    padding: 24rpx;
  }

  .bottom-action-bar {
    gap: 12rpx;
    padding: 16rpx 24rpx 32rpx;

    .action-btn {
      height: 80rpx;
      font-size: 26rpx;
    }
  }

  .sample-questions-modal {
    padding: 20rpx;

    .modal-content {
      max-height: 85vh;

      .modal-header {
        padding: 24rpx;

        .modal-title {
          font-size: 28rpx;
        }

        .close-btn {
          width: 48rpx;
          height: 48rpx;
          font-size: 28rpx;
        }
      }

      .modal-body {
        .questions-intro {
          padding: 24rpx;

          .intro-title {
            font-size: 26rpx;
          }

          .intro-desc {
            font-size: 22rpx;
          }
        }

        .question-navigation {
          padding: 20rpx 24rpx;

          .nav-info {
            .current-index {
              font-size: 28rpx;
            }

            .total-count {
              font-size: 22rpx;
            }
          }

          .nav-dots {
            gap: 10rpx;

            .nav-dot {
              width: 14rpx;
              height: 14rpx;
            }
          }
        }

        .current-question {
          .question-item {
            padding: 24rpx;

            .question-content {
              font-size: 24rpx;
            }
          }
        }

        .question-controls {
          gap: 16rpx;
          padding: 20rpx 24rpx;

          .control-btn {
            height: 64rpx;
            font-size: 24rpx;

            text:first-child,
            text:last-child {
              font-size: 24rpx;
            }
          }
        }
      }

      .modal-footer {
        gap: 16rpx;
        padding: 24rpx;

        .modal-btn {
          height: 72rpx;
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>
