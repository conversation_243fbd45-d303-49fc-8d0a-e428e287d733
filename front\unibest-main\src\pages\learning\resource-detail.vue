<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

/**
 * @description 学习资源详情预览页
 * 展示资源的详细信息，包括视频预览、课程大纲、用户评价等
 */

// 页面参数
const resourceId = ref('')

// 资源详情数据
const resourceDetail = ref({
  id: 1,
  title: '职场沟通技巧与表达能力提升',
  type: 'video',
  difficulty: '中等',
  duration: '1小时45分',
  target: '表达能力',
  description:
    '帮助您掌握职场沟通技巧，提升面试表达能力和自信心，针对您的表达能力短板专门推荐。本课程通过理论讲解结合实战演练的方式，全面提升您的职场沟通能力。',
  tags: ['沟通技巧', '表达能力', '职场', '面试技巧'],
  rating: 4.6,
  studentCount: 856,
  cover: '/static/course2.jpg',
  videoUrl: '/static/video/preview.mp4',
  priority: 'high',
  targetCapability: 'communicationSkills',
  improvementPoints: 15,
  category: '能力提升',
  instructor: {
    name: '资深HR导师',
    avatar: '/static/avatar/instructor1.jpg',
    title: '高级人力资源专家',
    experience: '10年HR经验',
    description: '拥有丰富的面试官经验，帮助上千名求职者成功入职心仪企业',
  },
  isBookmarked: false,
  price: 0, // 0表示免费
  originalPrice: 199,
  learningGoals: [
    '掌握职场沟通的基本原则和技巧',
    '提升面试时的表达能力和自信心',
    '学会在不同场景下的沟通策略',
    '培养良好的职场人际关系',
  ],
  chapters: [
    {
      id: 1,
      title: '职场沟通基础理论',
      duration: '25分钟',
      isFree: true,
      description: '介绍职场沟通的基本概念和重要性',
    },
    {
      id: 2,
      title: '有效表达技巧',
      duration: '30分钟',
      isFree: true,
      description: '学习如何清晰、准确地表达自己的观点',
    },
    {
      id: 3,
      title: '面试沟通实战',
      duration: '35分钟',
      isFree: false,
      description: '模拟面试场景，实战演练沟通技巧',
    },
    {
      id: 4,
      title: '处理沟通冲突',
      duration: '15分钟',
      isFree: false,
      description: '学会在职场中妥善处理沟通冲突',
    },
  ],
  reviews: [
    {
      id: 1,
      userName: '小王同学',
      avatar: '/static/avatar/user1.jpg',
      rating: 5,
      content: '课程内容非常实用，老师讲解很清晰，对我的面试帮助很大！',
      time: '2024-01-15',
      helpful: 23,
    },
    {
      id: 2,
      userName: '张三',
      avatar: '/static/avatar/user2.jpg',
      rating: 4,
      content: '总体来说不错，但希望能有更多实战案例。',
      time: '2024-01-10',
      helpful: 15,
    },
    {
      id: 3,
      userName: '李四',
      avatar: '/static/avatar/user3.jpg',
      rating: 5,
      content: '非常推荐！学完后面试通过率明显提高了。',
      time: '2024-01-08',
      helpful: 31,
    },
  ],
})

// 页面状态
const isLoading = ref(true)
const activeTab = ref('overview') // overview, chapters, reviews
const isVideoPlaying = ref(false)

/**
 * @description 获取页面参数
 */
const getPageParams = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  if (currentPage.options?.id) {
    resourceId.value = currentPage.options.id
  }
}

/**
 * @description 获取资源详情数据
 */
const fetchResourceDetail = async () => {
  try {
    isLoading.value = true
    // 模拟API调用
    setTimeout(() => {
      // 根据不同的resourceId返回不同的数据
      updateResourceData()
      isLoading.value = false
    }, 1000)
  } catch (error) {
    console.error('获取资源详情失败:', error)
    isLoading.value = false
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

/**
 * @description 根据resourceId更新资源数据
 */
const updateResourceData = () => {
  const resourceMap = {
    '1': {
      title: '职场沟通技巧与表达能力提升',
      type: 'video',
      target: '表达能力',
    },
    '2': {
      title: '创新思维训练与设计思维方法论',
      type: 'practice',
      target: '创新能力',
    },
    '3': {
      title: '前端面试必备：JavaScript深入解析',
      type: 'video',
      target: '专业知识',
    },
  }

  const data = resourceMap[resourceId.value]
  if (data) {
    Object.assign(resourceDetail.value, data)
  }
}

/**
 * @description 获取难度样式
 */
const getDifficultyStyle = computed(() => {
  const styles = {
    简单: 'bg-green-100 text-green-700',
    中等: 'bg-yellow-100 text-yellow-700',
    困难: 'bg-red-100 text-red-700',
  }
  return styles[resourceDetail.value.difficulty] || 'bg-gray-100 text-gray-700'
})

/**
 * @description 切换收藏状态
 */
const toggleBookmark = () => {
  resourceDetail.value.isBookmarked = !resourceDetail.value.isBookmarked
  uni.showToast({
    title: resourceDetail.value.isBookmarked ? '已收藏' : '已取消收藏',
    icon: 'success',
    duration: 1500,
  })
  // 触觉反馈
  uni.vibrateShort({
    type: 'light',
  })
}

/**
 * @description 开始学习
 */
const startLearning = () => {
  uni.navigateTo({
    url: `/pages/learning/learning?id=${resourceDetail.value.id}&type=${resourceDetail.value.type}`,
    fail: () => {
      uni.showToast({
        title: '页面开发中',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 播放预览视频
 */
const playPreviewVideo = () => {
  if (resourceDetail.value.type === 'video') {
    isVideoPlaying.value = true
    uni.showToast({
      title: '开始播放预览',
      icon: 'success',
    })
  }
}

/**
 * @description 切换标签页
 */
const switchTab = (tab: string) => {
  activeTab.value = tab
  // 触觉反馈
  uni.vibrateShort({
    type: 'light',
  })
}

/**
 * @description 点赞评价
 */
const likeReview = (reviewId: number) => {
  const review = resourceDetail.value.reviews.find((r) => r.id === reviewId)
  if (review) {
    review.helpful += 1
    uni.showToast({
      title: '点赞成功',
      icon: 'success',
      duration: 1000,
    })
  }
}

/**
 * @description 分享资源
 */
const shareResource = () => {
  uni.share({
    provider: 'weixin',
    type: 0,
    title: resourceDetail.value.title,
    summary: resourceDetail.value.description,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success',
      })
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 返回上一页
 */
const goBack = () => {
  uni.navigateBack()
}

// 页面初始化
onMounted(() => {
  getPageParams()
  fetchResourceDetail()
})
</script>

<template>
  <view class="resource-detail-container">
    <HeadBar
      :title="resourceDetail.title"
      :show-back="true"
      :show-right-button="true"
      right-text="分享"
      @back="goBack"
      @right-click="shareResource"
    />

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容 -->
    <scroll-view v-else class="main-content" scroll-y>
      <view class="content-wrapper">
        <!-- 资源封面和基本信息 -->
        <view class="resource-header">
          <view class="cover-section">
            <image
              class="resource-cover"
              :src="resourceDetail.cover"
              mode="aspectFill"
              @click="playPreviewVideo"
            />
            <view
              v-if="resourceDetail.type === 'video'"
              class="play-button"
              @click="playPreviewVideo"
            >
              <view class="i-mdi-play play-icon"></view>
            </view>
            <!-- 收藏按钮 -->
            <view class="bookmark-btn" @click="toggleBookmark">
              <view
                :class="resourceDetail.isBookmarked ? 'i-mdi-heart' : 'i-mdi-heart-outline'"
                class="bookmark-icon"
                :style="{ color: resourceDetail.isBookmarked ? '#ff6b6b' : '#94a3b8' }"
              ></view>
            </view>
          </view>

          <view class="info-section">
            <text class="resource-title">{{ resourceDetail.title }}</text>

            <view class="meta-info">
              <view class="difficulty-tag" :class="getDifficultyStyle">
                <text class="difficulty-text">{{ resourceDetail.difficulty }}</text>
              </view>
              <text class="resource-target">针对：{{ resourceDetail.target }}</text>
            </view>

            <view class="stats-row">
              <view class="stat-item">
                <view class="i-mdi-star stat-icon"></view>
                <text class="stat-text">{{ resourceDetail.rating }}</text>
              </view>
              <view class="stat-item">
                <view class="i-mdi-account-group stat-icon"></view>
                <text class="stat-text">{{ resourceDetail.studentCount }}人学习</text>
              </view>
              <view class="stat-item">
                <view class="i-mdi-clock-outline stat-icon"></view>
                <text class="stat-text">{{ resourceDetail.duration }}</text>
              </view>
            </view>

            <!-- 价格信息 -->
            <view class="price-section">
              <text v-if="resourceDetail.price === 0" class="free-tag">免费</text>
              <view v-else class="price-info">
                <text class="current-price">¥{{ resourceDetail.price }}</text>
                <text v-if="resourceDetail.originalPrice" class="original-price">
                  ¥{{ resourceDetail.originalPrice }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 讲师信息 -->
        <view class="instructor-section">
          <view class="instructor-header">
            <text class="section-title">
              <view class="i-mdi-account-tie section-icon"></view>
              讲师介绍
            </text>
          </view>
          <view class="instructor-info">
            <image
              class="instructor-avatar"
              :src="resourceDetail.instructor.avatar"
              mode="aspectFill"
            />
            <view class="instructor-details">
              <text class="instructor-name">{{ resourceDetail.instructor.name }}</text>
              <text class="instructor-title">{{ resourceDetail.instructor.title }}</text>
              <text class="instructor-experience">{{ resourceDetail.instructor.experience }}</text>
              <text class="instructor-desc">{{ resourceDetail.instructor.description }}</text>
            </view>
          </view>
        </view>

        <!-- 标签页切换 -->
        <view class="tab-section">
          <view class="tab-header">
            <view
              class="tab-item"
              :class="{ 'tab-active': activeTab === 'overview' }"
              @click="switchTab('overview')"
            >
              <text class="tab-text">课程介绍</text>
            </view>
            <view
              class="tab-item"
              :class="{ 'tab-active': activeTab === 'chapters' }"
              @click="switchTab('chapters')"
            >
              <text class="tab-text">课程大纲</text>
            </view>
            <view
              class="tab-item"
              :class="{ 'tab-active': activeTab === 'reviews' }"
              @click="switchTab('reviews')"
            >
              <text class="tab-text">用户评价</text>
            </view>
          </view>

          <!-- 课程介绍 -->
          <view v-if="activeTab === 'overview'" class="tab-content">
            <view class="description-section">
              <text class="description-text">{{ resourceDetail.description }}</text>
            </view>

            <view class="goals-section">
              <text class="goals-title">
                <view class="i-mdi-target goals-icon"></view>
                学习目标
              </text>
              <view class="goals-list">
                <view
                  v-for="(goal, index) in resourceDetail.learningGoals"
                  :key="index"
                  class="goal-item"
                >
                  <view class="i-mdi-check-circle goal-icon"></view>
                  <text class="goal-text">{{ goal }}</text>
                </view>
              </view>
            </view>

            <view class="tags-section">
              <text class="tags-title">相关标签</text>
              <view class="tags-list">
                <text v-for="(tag, index) in resourceDetail.tags" :key="index" class="tag">
                  {{ tag }}
                </text>
              </view>
            </view>
          </view>

          <!-- 课程大纲 -->
          <view v-if="activeTab === 'chapters'" class="tab-content">
            <view class="chapters-list">
              <view
                v-for="(chapter, index) in resourceDetail.chapters"
                :key="chapter.id"
                class="chapter-item"
              >
                <view class="chapter-header">
                  <view class="chapter-number">{{ index + 1 }}</view>
                  <view class="chapter-info">
                    <text class="chapter-title">{{ chapter.title }}</text>
                    <text class="chapter-duration">{{ chapter.duration }}</text>
                  </view>
                  <view v-if="chapter.isFree" class="free-badge">
                    <text class="free-text">免费</text>
                  </view>
                </view>
                <text class="chapter-description">{{ chapter.description }}</text>
              </view>
            </view>
          </view>

          <!-- 用户评价 -->
          <view v-if="activeTab === 'reviews'" class="tab-content">
            <view class="reviews-summary">
              <view class="rating-overview">
                <text class="rating-score">{{ resourceDetail.rating }}</text>
                <view class="rating-stars">
                  <view
                    v-for="i in 5"
                    :key="i"
                    :class="
                      i <= Math.floor(resourceDetail.rating) ? 'i-mdi-star' : 'i-mdi-star-outline'
                    "
                    class="star-icon"
                  ></view>
                </view>
                <text class="rating-count">基于{{ resourceDetail.reviews.length }}条评价</text>
              </view>
            </view>

            <view class="reviews-list">
              <view v-for="review in resourceDetail.reviews" :key="review.id" class="review-item">
                <view class="review-header">
                  <image class="reviewer-avatar" :src="review.avatar" mode="aspectFill" />
                  <view class="reviewer-info">
                    <text class="reviewer-name">{{ review.userName }}</text>
                    <view class="review-rating">
                      <view
                        v-for="i in 5"
                        :key="i"
                        :class="i <= review.rating ? 'i-mdi-star' : 'i-mdi-star-outline'"
                        class="review-star"
                      ></view>
                    </view>
                    <text class="review-time">{{ review.time }}</text>
                  </view>
                </view>
                <text class="review-content">{{ review.content }}</text>
                <view class="review-actions">
                  <button class="like-btn" @click="likeReview(review.id)">
                    <view class="i-mdi-thumb-up like-icon"></view>
                    <text class="like-text">有用 {{ review.helpful }}</text>
                  </button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <button class="start-btn" @click="startLearning">
        <view class="i-mdi-rocket-launch btn-icon"></view>
        <text>开始学习</text>
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.resource-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

// Loading 状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #e2e8f0;
    border-top: 4rpx solid #00c9a7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 24rpx;
    color: #64748b;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 主要内容
.main-content {
  flex: 1;
  background: transparent;
}

.content-wrapper {
  padding: 0 0 120rpx;
}

// 资源头部
.resource-header {
  background: white;
  margin-bottom: 20rpx;

  .cover-section {
    position: relative;
    height: 400rpx;

    .resource-cover {
      width: 100%;
      height: 100%;
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(10rpx);
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        font-size: 60rpx;
        color: white;
        margin-left: 8rpx;
      }
    }

    .bookmark-btn {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

      .bookmark-icon {
        font-size: 32rpx;
      }
    }
  }

  .info-section {
    padding: 30rpx;

    .resource-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #1e293b;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }

    .meta-info {
      display: flex;
      align-items: center;
      gap: 20rpx;
      margin-bottom: 20rpx;

      .difficulty-tag {
        padding: 8rpx 16rpx;
        font-size: 22rpx;
        font-weight: 500;
        border-radius: 12rpx;

        &.bg-green-100 {
          color: #16a34a;
          background: #dcfce7;
        }

        &.bg-yellow-100 {
          color: #d97706;
          background: #fef3c7;
        }

        &.bg-red-100 {
          color: #dc2626;
          background: #fee2e2;
        }
      }

      .resource-target {
        font-size: 24rpx;
        color: #00c9a7;
        background: rgba(0, 201, 167, 0.1);
        padding: 6rpx 12rpx;
        border-radius: 10rpx;
      }
    }

    .stats-row {
      display: flex;
      gap: 30rpx;
      margin-bottom: 20rpx;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .stat-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }

        .stat-text {
          font-size: 22rpx;
          color: #64748b;
        }
      }
    }

    .price-section {
      .free-tag {
        background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
        color: white;
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: bold;
      }

      .price-info {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .current-price {
          font-size: 32rpx;
          font-weight: bold;
          color: #ff6b6b;
        }

        .original-price {
          font-size: 24rpx;
          color: #94a3b8;
          text-decoration: line-through;
        }
      }
    }
  }
}

// 讲师信息
.instructor-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .instructor-header {
    margin-bottom: 20rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #1e293b;
      display: flex;
      align-items: center;
      gap: 12rpx;

      .section-icon {
        font-size: 32rpx;
        color: #00c9a7;
      }
    }
  }

  .instructor-info {
    display: flex;
    gap: 20rpx;

    .instructor-avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .instructor-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .instructor-name {
        font-size: 26rpx;
        font-weight: bold;
        color: #1e293b;
      }

      .instructor-title {
        font-size: 22rpx;
        color: #00c9a7;
      }

      .instructor-experience {
        font-size: 20rpx;
        color: #64748b;
      }

      .instructor-desc {
        font-size: 22rpx;
        color: #64748b;
        line-height: 1.4;
      }
    }
  }
}

// 标签页
.tab-section {
  background: white;

  .tab-header {
    display: flex;
    border-bottom: 2rpx solid #f1f5f9;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 30rpx 20rpx;
      position: relative;

      &.tab-active {
        .tab-text {
          color: #00c9a7;
          font-weight: bold;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 4rpx;
          background: #00c9a7;
          border-radius: 2rpx;
        }
      }

      .tab-text {
        font-size: 26rpx;
        color: #64748b;
      }
    }
  }

  .tab-content {
    padding: 30rpx;
  }
}

// 课程介绍
.description-section {
  margin-bottom: 40rpx;

  .description-text {
    font-size: 26rpx;
    color: #475569;
    line-height: 1.6;
  }
}

.goals-section {
  margin-bottom: 40rpx;

  .goals-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;

    .goals-icon {
      font-size: 32rpx;
      color: #00c9a7;
    }
  }

  .goals-list {
    .goal-item {
      display: flex;
      align-items: flex-start;
      gap: 12rpx;
      margin-bottom: 16rpx;

      .goal-icon {
        font-size: 24rpx;
        color: #16a34a;
        margin-top: 4rpx;
        flex-shrink: 0;
      }

      .goal-text {
        font-size: 24rpx;
        color: #475569;
        line-height: 1.5;
      }
    }
  }
}

.tags-section {
  .tags-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 16rpx;
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    .tag {
      background: #f1f5f9;
      color: #64748b;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
    }
  }
}

// 课程大纲
.chapters-list {
  .chapter-item {
    border: 2rpx solid #f1f5f9;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;

    .chapter-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 12rpx;

      .chapter-number {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        background: #00c9a7;
        color: white;
        font-size: 22rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .chapter-info {
        flex: 1;

        .chapter-title {
          font-size: 26rpx;
          font-weight: bold;
          color: #1e293b;
          margin-bottom: 6rpx;
        }

        .chapter-duration {
          font-size: 22rpx;
          color: #64748b;
        }
      }

      .free-badge {
        background: #dcfce7;
        color: #16a34a;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;

        .free-text {
          font-size: 20rpx;
          font-weight: bold;
        }
      }
    }

    .chapter-description {
      font-size: 24rpx;
      color: #64748b;
      line-height: 1.4;
      margin-left: 64rpx;
    }
  }
}

// 用户评价
.reviews-summary {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;

  .rating-overview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;

    .rating-score {
      font-size: 48rpx;
      font-weight: bold;
      color: #1e293b;
    }

    .rating-stars {
      display: flex;
      gap: 8rpx;

      .star-icon {
        font-size: 32rpx;
        color: #fbbf24;
      }
    }

    .rating-count {
      font-size: 22rpx;
      color: #64748b;
    }
  }
}

.reviews-list {
  .review-item {
    border-bottom: 2rpx solid #f1f5f9;
    padding: 30rpx 0;

    &:last-child {
      border-bottom: none;
    }

    .review-header {
      display: flex;
      gap: 16rpx;
      margin-bottom: 16rpx;

      .reviewer-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
      }

      .reviewer-info {
        flex: 1;

        .reviewer-name {
          font-size: 24rpx;
          font-weight: bold;
          color: #1e293b;
          margin-bottom: 8rpx;
        }

        .review-rating {
          display: flex;
          gap: 4rpx;
          margin-bottom: 8rpx;

          .review-star {
            font-size: 20rpx;
            color: #fbbf24;
          }
        }

        .review-time {
          font-size: 20rpx;
          color: #94a3b8;
        }
      }
    }

    .review-content {
      font-size: 24rpx;
      color: #475569;
      line-height: 1.5;
      margin-bottom: 16rpx;
    }

    .review-actions {
      .like-btn {
        background: transparent;
        border: 2rpx solid #e2e8f0;
        border-radius: 20rpx;
        padding: 8rpx 16rpx;
        display: flex;
        align-items: center;
        gap: 8rpx;

        .like-icon {
          font-size: 20rpx;
          color: #64748b;
        }

        .like-text {
          font-size: 20rpx;
          color: #64748b;
        }

        &:active {
          background: #f1f5f9;
        }
      }
    }
  }
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);

  .start-btn {
    width: 100%;
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    color: white;
    border: none;
    font-size: 28rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);

    .btn-icon {
      font-size: 32rpx;
    }

    &:active {
      transform: scale(0.98);
    }
  }
}
</style>
