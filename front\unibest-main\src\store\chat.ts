import { defineStore } from 'pinia'
// @ts-ignore
import { ref, computed } from 'vue'
import { chatApi, type ChatRequestDto, type ChatResponseDto, type ChatSession as BackendChatSession, type ChatMessage as BackendChatMessage, type Agent } from '@/service/chat-api'
import { ChatApiService } from '@/service/chat-api'

/**
 * @description AI助手配置接口（兼容现有结构）
 */
export interface AIAgent {
  id: string
  name: string
  description: string
  icon: string
  color: string
}

/**
 * @description 消息接口（兼容现有结构）
 */
export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: number
  attachments?: Array<{
    type: 'image' | 'file' | 'voice'
    url: string
    name: string
    size?: number
    metadata?: Record<string, any>
  }>
  isStreaming?: boolean // 新增：是否为流式消息
  isError?: boolean // 新增：是否为错误状态
  think?: string // 新增：AI思考过程内容
  showThink?: boolean // 新增：是否显示思考过程
}

/**
 * @description 会话接口（兼容现有结构）
 */
export interface ChatSession {
  id: string
  title: string
  agentId: string
  messages: Message[]
  createdAt: number
  updatedAt: number
  archived?: boolean
}

/**
 * @description 默认AI助手配置（作为后端数据的备用）
 */
export const AI_AGENTS: AIAgent[] = [
  {
    id: 'general',
    name: '通用助手',
    description: '全能AI助手，帮助您解决各种问题',
    icon: 'i-carbon-bot',
    color: '#00c9a7'
  },
  {
    id: 'resume',
    name: '简历顾问',
    description: '专业简历分析和优化建议',
    icon: 'i-carbon-document-tasks',
    color: '#3b82f6'
  },
  {
    id: 'interview',
    name: '面试教练',
    description: '面试技巧指导和模拟练习',
    icon: 'i-carbon-user-speaker',
    color: '#8b5cf6'
  },
  {
    id: 'skill',
    name: '技能评估师',
    description: '技能水平评估和学习路径规划',
    icon: 'i-carbon-certificate-check',
    color: '#f59e0b'
  },
  {
    id: 'mock',
    name: '模拟面试官',
    description: '真实面试场景模拟和反馈',
    icon: 'i-carbon-video',
    color: '#ef4444'
  },
  {
    id: 'learning',
    name: '学习导师',
    description: '个性化学习计划和指导',
    icon: 'i-carbon-education',
    color: '#10b981'
  }
]

/**
 * @description 聊天状态管理
 */
export const useChatStore = defineStore('chat', () => {
  // 使用ref确保深度响应式，解决消息更新问题
  const sessionList = ref<ChatSession[]>([])
  const currentSessionId = ref<string>('')
  const currentAgentId = ref<string>('general')
  const isLoading = ref(false)
  const availableAgents = ref<Agent[]>([])

  // SSE控制器状态
  const currentSSEController = ref<{ close: () => void } | null>(null)

  // 计算属性
  const currentSession = computed(() => {
    return sessionList.value.find(session => session.id === currentSessionId.value) || null
  })

  const currentAgent = computed(() => {
    const agentId = currentAgentId.value
    console.log('Store currentAgent computed 计算中:', { agentId, availableAgents: availableAgents.value.length })

    // 优先从后端数据中查找（注意：后端数据使用type字段作为ID）
    const backendAgent = availableAgents.value.find(agent => agent.type === agentId || agent.id === agentId)
    if (backendAgent) {
      const result = {
        id: backendAgent.type || backendAgent.id,
        name: backendAgent.name,
        description: backendAgent.description,
        icon: backendAgent.icon || 'i-carbon-bot',
        color: backendAgent.color || '#00c9a7'
      }
      console.log('Store currentAgent computed 找到后端智能体:', result)
      return result
    }

    // 备用：从默认配置中查找
    const defaultAgent = AI_AGENTS.find(agent => agent.id === agentId) || AI_AGENTS[0]
    console.log('Store currentAgent computed 使用默认智能体:', defaultAgent)
    return defaultAgent
  })

  /**
   * @description 生成唯一ID
   */
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * @description 将后端会话数据转换为前端格式
   */
  const convertBackendSession = (backendSession: BackendChatSession): ChatSession => {
    const now = Date.now()
    return {
      id: backendSession.id,
      title: backendSession.title || '新对话',
      agentId: backendSession.agentType || 'general',
      messages: [], // 消息单独加载
      createdAt: backendSession.createdAt || now,
      updatedAt: backendSession.updatedAt || backendSession.createdAt || now,
      archived: backendSession.archived || false
    }
  }

  /**
   * @description 将后端消息数据转换为前端格式
   */
  const convertBackendMessage = (backendMessage: BackendChatMessage): Message => {
    return {
      id: backendMessage.id,
      role: backendMessage.role,
      content: backendMessage.content,
      timestamp: backendMessage.createdAt,
      attachments: backendMessage.attachments
    }
  }

  /**
   * @description 初始化：加载代理列表
   */
  const initializeAgents = async (): Promise<void> => {
    try {
      const agents = await chatApi.getEnabledAgents()
      availableAgents.value = agents
      console.log('availableAgents', availableAgents.value)
    } catch (error) {
      console.error('加载代理列表失败:', error)
      // 使用默认配置
    }
  }

  /**
   * @description 创建新会话
   */
  const createSession = async (): Promise<string> => {
    try {
      const backendSession = await chatApi.createSession(currentAgentId.value)
      const session = convertBackendSession(backendSession)
      
      // 添加到本地列表
      sessionList.value = [...sessionList.value, session]
      currentSessionId.value = session.id
      
      return session.id
    } catch (error) {
      console.error('创建会话失败:', error)
      
      // 降级处理：创建本地会话
      const sessionId = generateId()
      const newSession: ChatSession = {
        id: sessionId,
        title: '新对话',
        agentId: currentAgentId.value,
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now()
      }

      sessionList.value = [...sessionList.value, newSession]
      currentSessionId.value = sessionId
      
      return sessionId
    }
  }

  /**
   * @description 加载用户会话列表
   */
  const loadUserSessions = async (): Promise<void> => {
    try {
      const response = await chatApi.getUserSessions(1, 50)
      const backendSessions = (response as any).records || (response as any).data || []
      
      const sessions = backendSessions.map(convertBackendSession)
      sessionList.value = sessions
      
      // 如果有会话且没有当前会话，设置第一个为当前会话
      if (sessions.length > 0 && !currentSessionId.value) {
        currentSessionId.value = sessions[0].id
      }
    } catch (error) {
      console.error('加载会话列表失败:', error)
    }
  }

  /**
   * @description 加载会话消息（倒序排列）
   */
  const loadSessionMessages = async (sessionId: string): Promise<void> => {
    try {
      const response = await chatApi.getSessionMessages(sessionId, 1, 100)
      const backendMessages = (response as any).records || (response as any).data || []
      
      // 先转换为前端格式，再倒序排列
      const messages = backendMessages.map(convertBackendMessage).reverse()
      
      // 更新会话消息
      const sessionIndex = sessionList.value.findIndex(s => s.id === sessionId)
      if (sessionIndex !== -1) {
        const updatedSessions = [...sessionList.value]
        updatedSessions[sessionIndex] = {
          ...updatedSessions[sessionIndex],
          messages
        }
        sessionList.value = updatedSessions
      }
    } catch (error) {
      console.error('加载会话消息失败:', error)
    }
  }

  /**
   * @description 切换会话
   */
  const switchSession = async (sessionId: string): Promise<void> => {
    const session = sessionList.value.find(s => s.id === sessionId)
    if (session) {
      currentSessionId.value = sessionId
      currentAgentId.value = session.agentId
      
      // 如果消息为空，从后端加载
      if (session.messages.length === 0) {
        await loadSessionMessages(sessionId)
      }
    }
  }

  /**
   * @description 切换AI助手
   */
  const switchAgent = async (agentId: string): Promise<void> => {
    console.log('Store switchAgent 开始:', {
      oldAgentId: currentAgentId.value,
      newAgentId: agentId
    })

    // 更新当前助手ID
    currentAgentId.value = agentId

    // 如果没有当前会话，创建新会话
    if (!currentSessionId.value) {
      await createSession()
    } else {
      // 更新当前会话的助手
      const sessionIndex = sessionList.value.findIndex(s => s.id === currentSessionId.value)
      if (sessionIndex !== -1) {
        const updatedSessions = [...sessionList.value]
        updatedSessions[sessionIndex] = {
          ...updatedSessions[sessionIndex],
          agentId,
          updatedAt: Date.now()
        }
        sessionList.value = updatedSessions
      }
    }

    // 保存到本地存储
    await saveToStorage()

    console.log('Store switchAgent 完成:', {
      currentAgentId: currentAgentId.value,
      currentAgent: currentAgent.value
    })
  }

  /**
   * @description 发送消息并获取回复（流式响应）
   */
  const sendMessage = async (content: string, attachments?: Message['attachments']): Promise<void> => {
    // 确保有当前会话
    if (!currentSessionId.value) {
      await createSession()
    }

    // 添加用户消息到本地
    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content,
      timestamp: Date.now(),
      attachments
    }

    addMessageToSession(userMessage)

    // 创建一个空的AI消息用于流式更新
    const assistantMessage: Message = {
      id: generateId(),
      role: 'assistant',
      content: '',
      timestamp: Date.now(),
      isStreaming: true // 标记为流式消息
    }

    addMessageToSession(assistantMessage)

    try {
      setLoading(true)

      // 构建请求数据
      const request: ChatRequestDto = {
        message: content,
        sessionId: currentSessionId.value,
        agentType: currentAgentId.value,
        attachments: attachments?.map(att => ({
          type: att.type,
          url: att.url,
          name: att.name,
          size: att.size,
          metadata: att.metadata
        })),
        stream: true
      }

      // 使用SSE流式API
      const sseController = ChatApiService.sendMessageStream(
        request,
        // onMessage: 每次接收到流式数据时的回调
        (chunk: string) => {
          updateStreamingMessage(assistantMessage.id, chunk)
        },
        // onError: 错误回调
        (error: string) => {
          console.error('流式响应错误:', error)
          // 立即停止加载状态
          setLoading(false)
          // 更新消息为错误状态
          finishStreamingMessage(assistantMessage.id, '抱歉，出现了错误，请重试。', true)
          uni.showToast({
            title: '发送失败，请重试',
            icon: 'none'
          })
        },
        // onComplete: 完成回调
        () => {
          finishStreamingMessage(assistantMessage.id)
          updateSessionTitleIfNeeded(content)
          setLoading(false)
        }
      )

      // 存储SSE控制器，用于取消连接
      currentSSEController.value = sseController

    } catch (error) {
      console.error('发送消息失败:', error)
      // 立即停止加载状态
      setLoading(false)
      // 更新消息为错误状态
      finishStreamingMessage(assistantMessage.id, '抱歉，出现了错误，请重试。', true)
      uni.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      })
    }
  }

  /**
   * @description 添加消息到会话
   */
  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>): void => {
    const newMessage: Message = {
      ...message,
      id: generateId(),
      timestamp: Date.now()
    }

    addMessageToSession(newMessage)
  }

  /**
   * @description 内部方法：添加消息到指定会话
   */
  const addMessageToSession = (message: Message): void => {
    const sessionIndex = sessionList.value.findIndex(s => s.id === currentSessionId.value)
    if (sessionIndex !== -1) {
      // 直接修改消息数组，触发响应式更新
      sessionList.value[sessionIndex].messages.push(message)
      sessionList.value[sessionIndex].updatedAt = Date.now()
      
      // 手动触发响应式更新（确保页面立即更新）
      sessionList.value = [...sessionList.value]
    }
  }

  /**
   * @description 更新会话标题（如果需要）
   */
  const updateSessionTitleIfNeeded = async (content: string): Promise<void> => {
    const session = currentSession.value
    if (session && session.title === '新对话' && session.messages.filter(m => m.role === 'user').length === 1) {
      const title = content.length > 20 ? content.substring(0, 20) + '...' : content
      
      try {
        await chatApi.updateSessionTitle(session.id, title)
        
        // 更新本地标题
        const sessionIndex = sessionList.value.findIndex(s => s.id === session.id)
        if (sessionIndex !== -1) {
          const updatedSessions = [...sessionList.value]
          updatedSessions[sessionIndex] = {
            ...updatedSessions[sessionIndex],
            title
          }
          sessionList.value = updatedSessions
        }
      } catch (error) {
        console.error('更新会话标题失败:', error)
      }
    }
  }

  /**
   * @description 删除会话
   */
  const deleteSession = async (sessionId: string): Promise<void> => {
    try {
      await chatApi.deleteSession(sessionId)
      
      // 从本地列表中移除
      sessionList.value = sessionList.value.filter(s => s.id !== sessionId)
      
      // 如果删除的是当前会话，清空当前会话ID
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = ''
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      uni.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  }

  /**
   * @description 清空所有数据
   */
  const clearAllData = (): void => {
    sessionList.value = []
    currentSessionId.value = ''
    uni.removeStorageSync('chat-data')
  }

  /**
   * @description 设置加载状态
   */
  const setLoading = (loading: boolean): void => {
    isLoading.value = loading
  }

  /**
   * @description 保存到本地存储（仅保存基本信息）
   */
  const saveToStorage = (): void => {
    try {
      const data = {
        currentSessionId: currentSessionId.value,
        currentAgentId: currentAgentId.value
      }
      uni.setStorageSync('chat-data', JSON.stringify(data))
    } catch (error) {
      console.error('保存聊天数据失败:', error)
    }
  }

  /**
   * @description 从本地存储加载（主要从后端获取数据）
   */
  const loadFromStorage = async (): Promise<void> => {
    try {
      const dataStr = uni.getStorageSync('chat-data')
      if (dataStr) {
        const data = JSON.parse(dataStr)
        currentSessionId.value = data.currentSessionId || ''
        currentAgentId.value = data.currentAgentId || 'general'
      }

      // 初始化代理列表
      await initializeAgents()
      
      // 加载会话列表
      await loadUserSessions()
      
      // 如果有当前会话，加载其消息
      if (currentSessionId.value) {
        await loadSessionMessages(currentSessionId.value)
      }
      
    } catch (error) {
      console.error('加载聊天数据失败:', error)
      // 创建默认会话
      await createSession()
    }
  }

  /**
   * @description 更新流式消息内容
   * @param messageId 消息ID
   * @param chunk 新的内容块
   */
  const updateStreamingMessage = (messageId: string, chunk: string): void => {
    const sessionIndex = sessionList.value.findIndex(s => s.id === currentSessionId.value)
    if (sessionIndex !== -1) {
      const session = sessionList.value[sessionIndex]
      const messageIndex = session.messages.findIndex(m => m.id === messageId)
      
      if (messageIndex !== -1) {
        // 创建消息的深拷贝，而不是直接修改
        const updatedSessions = JSON.parse(JSON.stringify(sessionList.value))
        // 追加新内容
        updatedSessions[sessionIndex].messages[messageIndex].content += chunk
        updatedSessions[sessionIndex].updatedAt = Date.now()
        // 替换整个数组，避免直接修改原始对象
        sessionList.value = updatedSessions
      }
    }
  }

  /**
   * @description 完成流式消息
   * @param messageId 消息ID
   * @param finalContent 最终内容（可选）
   * @param isError 是否为错误状态
   */
  const finishStreamingMessage = (messageId: string, finalContent?: string, isError: boolean = false): void => {
    const sessionIndex = sessionList.value.findIndex(s => s.id === currentSessionId.value)
    if (sessionIndex !== -1) {
      const session = sessionList.value[sessionIndex]
      const messageIndex = session.messages.findIndex(m => m.id === messageId)
      
      if (messageIndex !== -1) {
        // 创建会话列表的深拷贝
        const updatedSessions = JSON.parse(JSON.stringify(sessionList.value))
        const message = updatedSessions[sessionIndex].messages[messageIndex]
        
        // 更新消息状态
        message.isStreaming = false
        message.isError = isError
        
        if (finalContent !== undefined) {
          message.content = finalContent
        }
        
        updatedSessions[sessionIndex].updatedAt = Date.now()
        // 使用新的数组替换原数组，避免直接修改
        sessionList.value = updatedSessions
      }
    }
  }

  /**
   * @description 取消当前的流式响应
   */
  const cancelCurrentStream = (): void => {
    if (currentSSEController.value) {
      currentSSEController.value.close()
      currentSSEController.value = null
      setLoading(false)
    }
  }

  /**
   * @description 切换消息的think显示状态
   * @param messageId 消息ID
   */
  const toggleMessageThink = (messageId: string): void => {
    const sessionIndex = sessionList.value.findIndex(s => s.id === currentSessionId.value)
    if (sessionIndex !== -1) {
      const session = sessionList.value[sessionIndex]
      const messageIndex = session.messages.findIndex(m => m.id === messageId)

      if (messageIndex !== -1) {
        const message = session.messages[messageIndex]

        // 如果没有showThink属性，则初始化为false
        if (typeof message.showThink === 'undefined') {
          message.showThink = false
        }

        // 切换显示状态
        message.showThink = !message.showThink

        // 更新会话的最后更新时间
        session.updatedAt = Date.now()
      }
    }
  }

  /**
   * @description 重置消息错误状态，准备重试
   * @param messageId 消息ID
   */
  const resetMessageError = (messageId: string): void => {
    const sessionIndex = sessionList.value.findIndex(s => s.id === currentSessionId.value)
    if (sessionIndex !== -1) {
      const session = sessionList.value[sessionIndex]
      const messageIndex = session.messages.findIndex(m => m.id === messageId)

      if (messageIndex !== -1) {
        // 创建会话列表的深拷贝
        const updatedSessions = JSON.parse(JSON.stringify(sessionList.value))
        const message = updatedSessions[sessionIndex].messages[messageIndex]

        // 重置消息状态为流式状态
        message.isError = false
        message.isStreaming = true
        message.content = ''

        updatedSessions[sessionIndex].updatedAt = Date.now()
        // 使用新的数组替换原数组
        sessionList.value = updatedSessions
      }
    }
  }

  return {
    // 状态
    sessionList,
    currentSessionId,
    currentAgentId,
    isLoading,
    availableAgents,

    // 计算属性
    currentSession,
    currentAgent,

    // 方法
    initializeAgents,
    createSession,
    loadUserSessions,
    loadSessionMessages,
    switchSession,
    switchAgent,
    sendMessage,
    addMessage,
    deleteSession,
    clearAllData,
    setLoading,
    saveToStorage,
    loadFromStorage,
    cancelCurrentStream,
    updateStreamingMessage,
    finishStreamingMessage,
    toggleMessageThink,
    resetMessageError
  }
})
