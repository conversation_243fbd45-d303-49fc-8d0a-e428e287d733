<script setup lang="ts">
import { ref, computed, onUnmounted, watch, onMounted, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// #ifdef H5
import H5Camera from '@/components/camera/H5Camera.vue'
// #endif
// #ifndef H5
import MiniCamera from '@/components/camera/MiniCamera.vue'
// #endif

// 导入新创建的组件
import SmartSuggestion from '@/components/SmartSuggestion.vue'
import EmotionAnalysis from '@/components/EmotionAnalysis.vue'
import InterviewTimer from '@/components/InterviewTimer.vue'
import ScreenshotButton from '@/components/ScreenshotButton.vue'

/**
 * @description 模拟面试房间页面
 * 提供真实的面试环境和实时反馈
 * 核心面试界面包含视频窗口、问题显示区、计时器等
 */
onLoad(() => {
  uni.preloadPage({
    url: '/pages/interview/result',
  })
  uni.preloadPage({
    url: '/pages/interview/detail',
  })
})

// 面试会话参数
const sessionParams = ref({
  sessionId: '',
  jobId: 0,
  mode: 'standard',
  isDemo: false,
  jobName: '',
  company: '',
})

// WebSocket连接管理
const websocket = ref<any>(null)
const wsStatus = ref({
  connected: false,
  connecting: false,
  reconnecting: false,
  reconnectCount: 0,
  maxReconnectCount: 5,
  reconnectInterval: 1000, // 初始重连间隔1秒
  lastHeartbeat: 0,
})

// WebSocket配置
const getWebSocketUrl = () => {
  // #ifdef H5
  // 开发环境直连后端，避免代理影响WebSocket连接
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'ws://127.0.0.1:8080/interview/ws'
  }
  return 'wss://api.example.com/interview/ws'
  // #endif
  // #ifndef H5
  return 'wss://api.example.com/interview/ws' // 小程序环境默认使用生产地址
  // #endif
}

const wsConfig = ref({
  url: getWebSocketUrl(),
  heartbeatInterval: 30000, // 心跳间隔30秒
  reconnectDelay: 1000, // 重连延迟
})

// WebSocket消息队列
const messageQueue = ref<Array<any>>([])
const heartbeatTimer = ref<number | null>(null)

// 媒体录制管理
const mediaRecorder = ref<any>(null)
const recorderManager = ref<any>(null) // uni-app录音管理器
const recordedChunks = ref<Blob[]>([])
const videoStream = ref<MediaStream | null>(null)
const audioStream = ref<MediaStream | null>(null)
const cameraStatus = ref({
  isReady: false,
  isLoading: false,
  error: '',
  isFrontCamera: true, // 默认使用前置摄像头
})
const audioStatus = ref({
  isReady: false,
  isLoading: false,
  error: '',
})


// 录音数据管理
const questionRecordings = ref<
  Array<{
    questionId: number
    questionText: string
    audioBlob: Blob | null
    startTime: number
    endTime: number
    duration: number
  }>
>([])

const currentRecording = ref({
  isRecording: false,
  startTime: 0,
  questionId: 0,
})

// 录音时长显示
const recordingDuration = ref(0)
const recordingTimer = ref<number | null>(null)



// 下载配置
const downloadConfig = ref({
  enableAutoDownload: true, // 是否启用自动下载
  downloadFormat: 'webm', // 下载格式
  includeMetadata: true, // 是否包含元数据文件
  compressionLevel: 0.8, // 压缩级别 (0-1)
})

// uni-app摄像头上下文
const cameraContext = ref<any>(null)
// H5摄像头组件引用
// #ifdef H5
const h5CameraRef = ref(null)
// #endif
// #ifndef H5
const miniCameraRef = ref(null)
// #endif

// 演示题目数据
const questionBank = ref([
  {
    id: 1,
    type: 'technical',
    question: '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
    tips: '建议使用STAR法则来回答：Situation(情况)、Task(任务)、Action(行动)、Result(结果)',
    timeLimit: 180,
    difficulty: 3,
    expectedKeywords: ['Vue', 'React', 'Angular', '组件化', '响应式', '虚拟DOM'],
    evaluationPoints: ['技术深度', '实际应用', '场景分析'],
  },
  {
    id: 2,
    type: 'experience',
    question: '请描述一个你在项目中遇到的技术难题，以及你是如何解决的？',
    tips: '重点说明问题的复杂性、解决思路和最终效果',
    timeLimit: 240,
    difficulty: 4,
    expectedKeywords: ['问题分析', '解决方案', '技术选型', '效果评估'],
    evaluationPoints: ['问题理解', '解决能力', '技术深度'],
  },
  {
    id: 3,
    type: 'soft_skill',
    question: '在团队协作中，你如何处理与同事的技术分歧？',
    tips: '展现你的沟通能力和团队合作精神',
    timeLimit: 180,
    difficulty: 2,
    expectedKeywords: ['沟通', '协作', '妥协', '共识'],
    evaluationPoints: ['沟通能力', '团队意识', '解决冲突'],
  },
  {
    id: 4,
    type: 'architecture',
    question: '请设计一个高并发的电商系统架构，并说明关键技术选型。',
    tips: '考虑系统的可扩展性、可用性和性能',
    timeLimit: 300,
    difficulty: 5,
    expectedKeywords: ['微服务', '负载均衡', '缓存', '数据库', '消息队列'],
    evaluationPoints: ['架构设计', '技术选型', '系统思维'],
  },
  {
    id: 5,
    type: 'career',
    question: '你的职业规划是什么？为什么选择我们公司？',
    tips: '结合个人发展和公司发展方向来回答',
    timeLimit: 180,
    difficulty: 2,
    expectedKeywords: ['职业发展', '技能提升', '公司文化', '个人价值'],
    evaluationPoints: ['职业规划', '求职动机', '价值匹配'],
  },
  {
    id: 6,
    type: 'engineering',
    question: '请介绍你对前端工程化的理解，包括构建工具、代码规范等。',
    tips: '从开发效率和代码质量两个维度来阐述',
    timeLimit: 240,
    difficulty: 4,
    expectedKeywords: ['webpack', 'vite', 'eslint', 'prettier', 'CI/CD'],
    evaluationPoints: ['工程化理解', '工具使用', '规范意识'],
  },
  {
    id: 7,
    type: 'decision',
    question: '在技术选型时，你会考虑哪些因素？请举例说明。',
    tips: '平衡技术先进性、团队能力、项目需求等因素',
    timeLimit: 200,
    difficulty: 3,
    expectedKeywords: ['技术成熟度', '学习成本', '维护成本', '性能', '生态'],
    evaluationPoints: ['决策能力', '技术判断', '综合考虑'],
  },
  {
    id: 8,
    type: 'ux',
    question: '如何优化网页的加载性能和用户体验？',
    tips: '从技术优化和用户感知两个角度来回答',
    timeLimit: 220,
    difficulty: 4,
    expectedKeywords: ['懒加载', '代码分割', '缓存策略', 'CDN', '用户反馈'],
    evaluationPoints: ['性能优化', '用户体验', '技术实现'],
  },
  {
    id: 9,
    type: 'technical',
    question: '请解释JavaScript的事件循环机制，以及异步编程的最佳实践。',
    tips: '结合具体例子来说明概念和应用',
    timeLimit: 240,
    difficulty: 4,
    expectedKeywords: ['事件循环', 'Promise', 'async/await', '微任务', '宏任务'],
    evaluationPoints: ['基础理论', '实际应用', '代码质量'],
  },
  {
    id: 10,
    type: 'question',
    question: '你有什么问题想要了解关于我们公司或这个职位的？',
    tips: '展现你对公司和职位的关注和思考',
    timeLimit: 120,
    difficulty: 1,
    expectedKeywords: ['公司发展', '团队文化', '技术栈', '成长机会'],
    evaluationPoints: ['求职态度', '关注重点', '沟通能力'],
  },
])

// 面试状态管理
const interviewState = ref({
  isStarted: false,
  isPaused: false,
  isRecording: false,
  currentQuestion: 0,
  timeRemaining: 1800, // 30分钟
  totalQuestions: questionBank.value.length, // 使用题库的实际数量
  startTime: null,
})

// 添加摄像头检测状态
const cameraCheckState = ref({
  isCameraRequired: true, // 是否强制要求摄像头
  isCameraChecked: false, // 是否已检查摄像头
  isCameraReady: false, // 摄像头是否就绪
  checkMessage: '', // 检查消息
})

// 当前问题数据 - 从题库中获取
const currentQuestionData = computed(() => {
  return questionBank.value[interviewState.value.currentQuestion] || questionBank.value[0]
})

// 用户回答
const userAnswer = ref('')

// 时间提醒状态
const timeReminder = ref({
  show: false,
  type: 'info', // info, warning, danger
  icon: 'i-mdi-clock',
  message: '',
  questionStartTime: 0,
  shown: false,
  lastReminder: 0,
})

// 加载状态
const loadingState = ref({
  isInitializing: false,
  message: '',
  progress: 0,
})

// 添加到script部分的顶部，定义必要的响应式变量
const showUserVideo = ref(true)

// 动画相关状态
const isQuestionChanging = ref(true)
const showCompletionAnimation = ref(false)
const showInterviewCompletion = ref(false)
const completionProgress = ref(0)

// 实时建议数据
const realTimeSuggestions = ref([])

// 定时器管理
let timer: number | null = null
const timers: number[] = []

// 定时器管理工具
const addTimer = (timerId: number) => {
  timers.push(timerId)
  return timerId
}

const clearAllTimers = () => {
  timers.forEach((id) => clearTimeout(id))
  timers.length = 0
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

/**
 * @description 初始化WebSocket连接
 */
const initWebSocket = () => {
  if (wsStatus.value.connecting || wsStatus.value.connected) {
    return
  }

  wsStatus.value.connecting = true

  try {
    // #ifdef H5
    // H5环境使用原生WebSocket
    websocket.value = new WebSocket(wsConfig.value.url)
    // #endif

    // #ifndef H5
    // 小程序环境使用uni.connectSocket
    websocket.value = uni.connectSocket({
      url: wsConfig.value.url,
      success: () => {
        console.log('WebSocket连接创建成功')
      },
      fail: (error: any) => {
        console.error('WebSocket连接创建失败:', error)
        handleWebSocketError()
      },
    })
    // #endif

    setupWebSocketEvents()
  } catch (error) {
    console.error('WebSocket初始化失败:', error)
    handleWebSocketError()
  }
}

/**
 * @description 设置WebSocket事件监听
 */
const setupWebSocketEvents = () => {
  if (!websocket.value) return

  // #ifdef H5
  // H5环境事件监听
  websocket.value.onopen = handleWebSocketOpen
  websocket.value.onmessage = handleWebSocketMessage
  websocket.value.onclose = handleWebSocketClose
  websocket.value.onerror = handleWebSocketError
  // #endif

  // #ifndef H5
  // 小程序环境事件监听
  websocket.value.onOpen = handleWebSocketOpen
  websocket.value.onMessage = handleWebSocketMessage
  websocket.value.onClose = handleWebSocketClose
  websocket.value.onError = handleWebSocketError
  // #endif
}

/**
 * @description WebSocket连接打开处理
 */
const handleWebSocketOpen = () => {
  console.log('WebSocket连接已建立')
  wsStatus.value.connected = true
  wsStatus.value.connecting = false
  wsStatus.value.reconnecting = false
  wsStatus.value.reconnectCount = 0
  wsStatus.value.reconnectInterval = 1000 // 重置重连间隔

  // 发送连接确认消息
  sendWebSocketMessage({
    type: 'connect',
    sessionId: sessionParams.value.sessionId,
    timestamp: Date.now(),
  })

  // 启动心跳检测
  startHeartbeat()

  // 发送队列中的消息
  flushMessageQueue()
}

/**
 * @description WebSocket消息接收处理
 */
const handleWebSocketMessage = (event: any) => {
  try {
    let data: any
    // #ifdef H5
    data = JSON.parse(event.data)
    // #endif

    // #ifndef H5
    data = JSON.parse(event.data)
    // #endif

    console.log('收到WebSocket消息:', data)

    switch (data.type) {
      case 'suggestion':
        handleSuggestionMessage(data)
        break
      case 'emotion_result':
        handleEmotionMessage(data)
        break
      case 'heartbeat':
        wsStatus.value.lastHeartbeat = Date.now()
        break
      case 'error':
        console.error('服务端错误:', data.message)
        break
      default:
        console.warn('未知消息类型:', data.type)
    }
  } catch (error) {
    console.error('WebSocket消息解析失败:', error)
  }
}

/**
 * @description 发送WebSocket消息
 */
const sendWebSocketMessage = (message: any) => {
  if (!websocket.value || !wsStatus.value.connected) {
    // 如果连接未建立，将消息加入队列
    messageQueue.value.push(message)
    return
  }

  try {
    const messageStr = JSON.stringify(message)

    // #ifdef H5
    websocket.value.send(messageStr)
    // #endif

    // #ifndef H5
    uni.sendSocketMessage({
      data: messageStr,
      success: () => {
        console.log('WebSocket消息发送成功')
      },
      fail: (error: any) => {
        console.error('WebSocket消息发送失败:', error)
        // 发送失败时重新加入队列
        messageQueue.value.push(message)
      },
    })
    // #endif
  } catch (error) {
    console.error('WebSocket消息发送异常:', error)
    messageQueue.value.push(message)
  }
}

/**
 * @description 发送队列中的消息
 */
const flushMessageQueue = () => {
  while (messageQueue.value.length > 0) {
    const message = messageQueue.value.shift()
    if (message) {
      sendWebSocketMessage(message)
    }
  }
}

/**
 * @description 启动心跳检测
 */
const startHeartbeat = () => {
  stopHeartbeat() // 先停止之前的心跳

  heartbeatTimer.value = setInterval(() => {
    if (wsStatus.value.connected) {
      sendWebSocketMessage({
        type: 'heartbeat',
        timestamp: Date.now(),
      })
    }
  }, wsConfig.value.heartbeatInterval)
}

/**
 * @description 停止心跳检测
 */
const stopHeartbeat = () => {
  if (heartbeatTimer.value) {
    clearInterval(heartbeatTimer.value)
    heartbeatTimer.value = null
  }
}

/**
 * @description 处理智能建议消息
 */
const handleSuggestionMessage = (data: any) => {
  if (smartSuggestionRef.value && data.data) {
    smartSuggestionRef.value.addSuggestion({
      id: data.data.id || Date.now(),
      type: data.data.type || 'general',
      level: data.data.level || 'medium',
      icon: data.data.icon || 'fa fa-lightbulb',
      title: data.data.title || '智能建议',
      message: data.data.message || '',
      action: data.data.action,
    })
  }
}

/**
 * @description 处理表情分析消息
 */
const handleEmotionMessage = (data: any) => {
  if (emotionAnalysisRef.value && data.data) {
    emotionAnalysisRef.value.setEmotionData(data.data.emotions || {})

    // 触发更新事件
    handleEmotionUpdate({
      data: data.data.emotions || {},
      primary: data.data.primary || { type: 'neutral', value: 0 },
    })
  }
}

/**
 * @description 发送表情检测请求
 */
const sendEmotionDetectionRequest = () => {
  if (!wsStatus.value.connected || !interviewState.value.isRecording) {
    return
  }

  try {
    let imageData: string | null = null

    // #ifdef H5
    if (h5CameraRef.value) {
      imageData = h5CameraRef.value.captureFrame(false) // 低质量图像用于分析
    }
    // #endif

    // #ifndef H5
    if (miniCameraRef.value) {
      // 小程序环境下获取图像数据的逻辑
      // 这里需要根据实际的小程序摄像头组件API来实现
    }
    // #endif

    if (imageData) {
      sendWebSocketMessage({
        type: 'emotion_request',
        sessionId: sessionParams.value.sessionId,
        timestamp: Date.now(),
        data: {
          imageData: imageData.split(',')[1], // 移除data:image/jpeg;base64,前缀
          questionId: interviewState.value.currentQuestion,
        },
      })
    }
  } catch (error) {
    console.error('发送表情检测请求失败:', error)
  }
}

/**
 * @description 关闭WebSocket连接
 */
const closeWebSocket = () => {
  stopHeartbeat()

  if (websocket.value) {
    // #ifdef H5
    websocket.value.close()
    // #endif

    // #ifndef H5
    uni.closeSocket()
    // #endif

    websocket.value = null
  }

  wsStatus.value.connected = false
  wsStatus.value.connecting = false
  wsStatus.value.reconnecting = false
}

/**
 * @description WebSocket连接关闭处理
 */
const handleWebSocketClose = () => {
  console.log('WebSocket连接已关闭')
  wsStatus.value.connected = false
  wsStatus.value.connecting = false

  // 停止心跳检测
  stopHeartbeat()

  // 如果不是主动关闭，则尝试重连
  if (interviewState.value.isStarted && !wsStatus.value.reconnecting) {
    attemptReconnect()
  }
}

/**
 * @description WebSocket错误处理
 */
const handleWebSocketError = () => {
  console.error('WebSocket连接错误')
  wsStatus.value.connected = false
  wsStatus.value.connecting = false

  // 尝试重连
  if (interviewState.value.isStarted) {
    attemptReconnect()
  }
}

/**
 * @description 尝试重连WebSocket
 */
const attemptReconnect = () => {
  if (wsStatus.value.reconnectCount >= wsStatus.value.maxReconnectCount) {
    console.error('WebSocket重连次数已达上限')
    uni.showToast({
      title: '网络连接失败，请检查网络',
      icon: 'none',
    })
    return
  }

  wsStatus.value.reconnecting = true
  wsStatus.value.reconnectCount++

  console.log(
    `WebSocket重连中... (${wsStatus.value.reconnectCount}/${wsStatus.value.maxReconnectCount})`,
  )

  setTimeout(() => {
    initWebSocket()
    // 指数退避算法增加重连间隔
    wsStatus.value.reconnectInterval = Math.min(wsStatus.value.reconnectInterval * 2, 30000)
  }, wsStatus.value.reconnectInterval)
}

/**
 * @description 获取情绪标签
 * @param emotion 情绪类型
 */
const getEmotionLabel = (emotion: string): string => {
  const labels: Record<string, string> = {
    happy: '开心',
    neutral: '平静',
    sad: '悲伤',
    angry: '生气',
    surprised: '惊讶',
    fear: '恐惧',
    disgusted: '厌恶',
    contempt: '轻蔑',
    confused: '困惑',
  }
  return labels[emotion] || emotion
}

/**
 * @description 获取情绪颜色
 * @param emotion 情绪类型
 */
const getEmotionColor = (emotion: string): string => {
  const colors: Record<string, string> = {
    happy: '#52C41A',
    neutral: '#1890FF',
    sad: '#722ED1',
    angry: '#F5222D',
    surprised: '#FA8C16',
    fear: '#EB2F96',
    disgusted: '#FA541C',
    contempt: '#13C2C2',
    confused: '#FAAD14',
  }
  return colors[emotion] || '#999'
}

/**
 * @description 获取问题类型标签
 * @param type 问题类型
 */
const getQuestionTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    technical: '技术',
    experience: '经验',
    soft_skill: '软技能',
    architecture: '架构',
    career: '职业',
    engineering: '工程化',
    decision: '决策',
    ux: '用户体验',
    question: '提问',
  }
  return labels[type] || type
}

/**
 * @description 格式化时间显示
 * @param seconds 秒数
 */
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * @description 格式化录音时长显示
 */
const formatRecordingDuration = (): string => {
  const minutes = Math.floor(recordingDuration.value / 60)
  const seconds = recordingDuration.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * @description 开始录音时长计时器
 */
const startRecordingTimer = () => {
  recordingDuration.value = 0
  recordingTimer.value = setInterval(() => {
    if (currentRecording.value.isRecording && currentRecording.value.startTime) {
      recordingDuration.value = Math.floor((Date.now() - currentRecording.value.startTime) / 1000)
    }
  }, 1000)
}

/**
 * @description 停止录音时长计时器
 */
const stopRecordingTimer = () => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }
  recordingDuration.value = 0
}

/**
 * @description 切换摄像头
 */
const switchCamera = async () => {
  cameraStatus.value.isFrontCamera = !cameraStatus.value.isFrontCamera

  try {
    // #ifdef H5
    if (h5CameraRef.value) {
      await h5CameraRef.value.switchCamera()
    }
    // #endif

    // #ifndef H5
    if (miniCameraRef.value) {
      await miniCameraRef.value.switchCamera()
    }
    // #endif

    console.log('摄像头切换成功:', cameraStatus.value.isFrontCamera ? '前置' : '后置')
  } catch (error) {
    console.error('切换摄像头失败:', error)
    cameraStatus.value.error = '切换摄像头失败'
  }
}

/**
 * @description 初始化媒体设备
 */
const initMediaDevices = async () => {
  try {
    cameraStatus.value.isLoading = true
    audioStatus.value.isLoading = true
    cameraStatus.value.error = ''
    audioStatus.value.error = ''

    // #ifdef H5
    // H5环境下初始化录音设备
    await initH5AudioRecording()
    // H5环境下初始化摄像头
    await initH5Camera()
    // #endif

    // #ifndef H5
    // uni-app环境下初始化录音设备
    await initUniAudioRecording()
    // uni-app环境下初始化摄像头
    await initMiniCamera()
    // #endif

    cameraStatus.value.isReady = true
    cameraStatus.value.isLoading = false
    audioStatus.value.isReady = true
    audioStatus.value.isLoading = false
  } catch (error) {
    cameraStatus.value.error = '设备初始化失败'
    audioStatus.value.error = '录音设备初始化失败'
    cameraStatus.value.isLoading = false
    audioStatus.value.isLoading = false
  }
}

/**
 * @description H5环境下初始化录音设备
 */
const initH5AudioRecording = async () => {
  // #ifdef H5
  try {
    // 请求麦克风权限，使用更兼容的音频配置
    audioStream.value = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 44100,
        channelCount: 1,
        sampleSize: 16,
      },
    })

    // 检测支持的录音格式，优先使用兼容性更好的格式
    let mimeType = 'audio/webm;codecs=opus'
    if (MediaRecorder.isTypeSupported('audio/mp4')) {
      mimeType = 'audio/mp4'
    } else if (MediaRecorder.isTypeSupported('audio/webm')) {
      mimeType = 'audio/webm'
    } else if (MediaRecorder.isTypeSupported('audio/ogg;codecs=opus')) {
      mimeType = 'audio/ogg;codecs=opus'
    }

    console.log('使用录音格式:', mimeType)

    // 创建MediaRecorder，使用检测到的最佳格式
    mediaRecorder.value = new MediaRecorder(audioStream.value, {
      mimeType: mimeType,
      audioBitsPerSecond: 128000, // 128kbps，确保音质
    })

    // 设置录音事件监听
    setupRecordingEvents()

    console.log('H5录音设备初始化成功，格式:', mimeType)
  } catch (error) {
    console.error('H5录音设备初始化失败:', error)
    throw error
  }
  // #endif
}

/**
 * @description uni-app环境下初始化录音设备
 */
const initUniAudioRecording = async () => {
  // #ifndef H5
  try {
    // 创建录音管理器
    recorderManager.value = uni.getRecorderManager()

    // 设置录音事件监听
    setupUniRecordingEvents()
  } catch (error) {
    throw error
  }
  // #endif
}

/**
 * @description H5环境下初始化摄像头
 */
const initH5Camera = async () => {
  // #ifdef H5
  try {
    if (h5CameraRef.value) {
      await h5CameraRef.value.initCamera()
    }
  } catch (error) {
    throw error
  }
  // #endif
}

/**
 * @description 小程序环境下初始化摄像头
 */
const initMiniCamera = async () => {
  // #ifndef H5
  try {
    if (miniCameraRef.value) {
      await miniCameraRef.value.initCamera()
    }
  } catch (error) {
    throw error
  }
  // #endif
}

/**
 * @description 设置H5录音事件监听
 */
const setupRecordingEvents = () => {
  // #ifdef H5
  if (!mediaRecorder.value) return

  // 录音数据可用事件
  mediaRecorder.value.ondataavailable = (event: BlobEvent) => {
    if (event.data && event.data.size > 0) {
      recordedChunks.value.push(event.data)
    }
  }

  // 录音停止事件
  mediaRecorder.value.onstop = () => {

    if (recordedChunks.value.length > 0) {
      // 计算总大小
      const totalSize = recordedChunks.value.reduce((total, chunk) => total + chunk.size, 0)

      // 使用MediaRecorder的实际mimeType创建Blob
      const mimeType = mediaRecorder.value.mimeType || 'audio/webm'
      const audioBlob = new Blob(recordedChunks.value, { type: mimeType })


      // 验证Blob是否有效
      if (audioBlob.size > 0) {
        saveQuestionRecording(audioBlob)
      } else {
        console.error('录音Blob大小为0，录音可能失败')
        audioStatus.value.error = '录音数据无效'
      }

      recordedChunks.value = [] // 清空录音数据
    } else {
      console.error('没有录音数据可保存')
      audioStatus.value.error = '没有录音数据'
    }
  }

  // 录音开始事件
  mediaRecorder.value.onstart = () => {
    console.log('H5录音开始事件触发')
    recordedChunks.value = [] // 确保开始时清空之前的数据
    currentRecording.value.isRecording = true
    interviewState.value.isRecording = true
  }

  // 录音暂停事件
  mediaRecorder.value.onpause = () => {
    console.log('录音暂停')
  }

  // 录音恢复事件
  mediaRecorder.value.onresume = () => {
    console.log('录音恢复')
  }

  // 录音错误事件
  mediaRecorder.value.onerror = (event: any) => {
    console.error('录音错误:', event.error)
    audioStatus.value.error = `录音过程中发生错误: ${event.error?.message || '未知错误'}`

    // 重置录音状态
    currentRecording.value.isRecording = false
    interviewState.value.isRecording = false
  }
  // #endif
}

/**
 * @description 设置uni-app录音事件监听
 */
const setupUniRecordingEvents = () => {
  // #ifndef H5
  if (!recorderManager.value) return

  // 录音开始事件
  recorderManager.value.onStart(() => {
    console.log('uni-app录音开始事件触发')
    currentRecording.value.isRecording = true
    interviewState.value.isRecording = true
  })

  // 录音停止事件
  recorderManager.value.onStop((res: any) => {
    console.log('录音停止，文件路径:', res.tempFilePath)
    // 将临时文件转换为Blob并保存
    convertTempFileToBlob(res.tempFilePath)
  })

  // 录音错误事件
  recorderManager.value.onError((res: any) => {
    console.error('录音错误:', res.errMsg)
    audioStatus.value.error = '录音过程中发生错误'
  })
  // #endif
}

/**
 * @description 摄像头准备就绪处理
 */
const handleCameraReady = () => {
  cameraStatus.value.isReady = true
  cameraStatus.value.isLoading = false
}

/**
 * @description 摄像头错误处理
 */
const handleCameraError = (error: any) => {
  cameraStatus.value.error = '摄像头启动失败'
  cameraStatus.value.isLoading = false
}

/**
 * @description 扫码处理
 */
const handleScanCode = (event: any) => {
  console.log('扫码结果:', event)
}

/**
 * @description H5摄像头准备就绪
 */
const handleH5CameraReady = () => {
  cameraStatus.value.isReady = true
  cameraStatus.value.isLoading = false
  // 标记摄像头已就绪
  cameraCheckState.value.isCameraReady = true
  cameraCheckState.value.isCameraChecked = true
  cameraCheckState.value.checkMessage = '摄像头已就绪'
}

/**
 * @description H5摄像头错误处理
 */
const handleH5CameraError = (error) => {
  cameraStatus.value.error = 'H5摄像头启动失败'
  cameraStatus.value.isLoading = false
  // 标记摄像头检查失败
  cameraCheckState.value.isCameraReady = false
  cameraCheckState.value.isCameraChecked = true
  cameraCheckState.value.checkMessage = '摄像头启动失败，请允许浏览器访问摄像头'
}

/**
 * @description H5摄像头关闭处理
 */
const handleH5CameraClose = () => {
  cameraStatus.value.isReady = false
  // 标记摄像头未就绪
  cameraCheckState.value.isCameraReady = false
}

/**
 * @description 小程序摄像头准备就绪
 */
const handleMiniCameraReady = () => {
  cameraStatus.value.isReady = true
  cameraStatus.value.isLoading = false
  console.log('小程序摄像头准备就绪')
  // 标记摄像头已就绪
  cameraCheckState.value.isCameraReady = true
  cameraCheckState.value.isCameraChecked = true
  cameraCheckState.value.checkMessage = '摄像头已就绪'
}

/**
 * @description 小程序摄像头错误处理
 */
const handleMiniCameraError = (error) => {
  cameraStatus.value.error = '小程序摄像头启动失败'
  cameraStatus.value.isLoading = false
  console.error('小程序摄像头错误:', error)
  // 标记摄像头检查失败
  cameraCheckState.value.isCameraReady = false
  cameraCheckState.value.isCameraChecked = true
  cameraCheckState.value.checkMessage = '摄像头启动失败，请允许小程序访问摄像头'
}

/**
 * @description 小程序摄像头关闭处理
 */
const handleMiniCameraClose = () => {
  cameraStatus.value.isReady = false
  console.log('小程序摄像头已关闭')
  // 标记摄像头未就绪
  cameraCheckState.value.isCameraReady = false
}

/**
 * @description 检测录音质量
 * @param audioBlob 录音Blob数据
 */
const checkRecordingQuality = (audioBlob: Blob): { isValid: boolean; issues: string[] } => {
  const issues: string[] = []

  // 检查文件大小
  if (audioBlob.size === 0) {
    issues.push('录音文件大小为0')
  } else if (audioBlob.size < 1000) {
    issues.push('录音文件过小，可能录音时间太短')
  }

  // 检查文件类型
  if (!audioBlob.type || audioBlob.type === '') {
    issues.push('录音文件类型未知')
  } else if (!audioBlob.type.startsWith('audio/')) {
    issues.push(`录音文件类型错误: ${audioBlob.type}`)
  }

  // 估算录音时长（基于文件大小的粗略估算）
  const estimatedDuration = audioBlob.size / (128000 / 8) // 假设128kbps
  if (estimatedDuration < 0.5) {
    issues.push('录音时长过短')
  }

  return {
    isValid: issues.length === 0,
    issues,
  }
}

/**
 * @description 保存问题录音数据
 */
const saveQuestionRecording = (audioBlob: Blob) => {
  const endTime = Date.now()
  const startTime = currentRecording.value.startTime
  const duration = endTime - startTime

  // 检测录音质量
  const qualityCheck = checkRecordingQuality(audioBlob)

  console.log('录音质量检测结果:', {
    size: audioBlob.size,
    type: audioBlob.type,
    duration: Math.round(duration / 1000),
    isValid: qualityCheck.isValid,
    issues: qualityCheck.issues,
  })

  if (!qualityCheck.isValid) {
    console.warn('录音质量问题:', qualityCheck.issues)

    // 显示质量警告，但仍然保存录音
    uni.showModal({
      title: '录音质量警告',
      content: `录音可能存在问题：${qualityCheck.issues.join(', ')}。是否仍要保存？`,
      success: (res: any) => {
        if (res.confirm) {
          // 用户确认保存
          saveRecordingData(audioBlob, startTime, endTime, duration)
        } else {
          // 用户取消，重置状态
          currentRecording.value.isRecording = false
          currentRecording.value.startTime = 0
        }
      },
    })
  } else {
    // 质量正常，直接保存
    saveRecordingData(audioBlob, startTime, endTime, duration)
  }
}

/**
 * @description 实际保存录音数据
 */
const saveRecordingData = (audioBlob: Blob, startTime: number, endTime: number, duration: number) => {
  questionRecordings.value.push({
    questionId: currentRecording.value.questionId,
    questionText: currentQuestionData.value.question,
    audioBlob,
    startTime,
    endTime,
    duration,
  })

  console.log(
    `问题 ${currentRecording.value.questionId + 1} 录音保存成功，时长: ${Math.round(duration / 1000)}秒，大小: ${audioBlob.size} bytes`,
  )

  // 显示保存成功提示
  uni.showToast({
    title: `录音已保存 (${Math.round(duration / 1000)}s)`,
    icon: 'success',
    duration: 1500,
  })

  // 重置当前录音状态
  currentRecording.value.isRecording = false
  currentRecording.value.startTime = 0
}

/**
 * @description 将uni-app临时文件转换为Blob
 */
const convertTempFileToBlob = async (tempFilePath: string) => {
  // #ifndef H5
  try {
    // 读取临时文件
    const fileManager = uni.getFileSystemManager()
    fileManager.readFile({
      filePath: tempFilePath,
      success: (res: any) => {
        // 将ArrayBuffer转换为Blob
        const arrayBuffer = res.data
        const audioBlob = new Blob([arrayBuffer], { type: 'audio/mp3' })
        saveQuestionRecording(audioBlob)
      },
      fail: (error: any) => {
        console.error('读取录音文件失败:', error)
        audioStatus.value.error = '录音文件读取失败'
      },
    })
  } catch (error) {
    console.error('转换录音文件失败:', error)
  }
  // #endif
}

/**
 * @description 开始录音
 */
const startRecording = async () => {
  if (!audioStatus.value.isReady) {
    console.warn('录音设备未准备就绪')
    uni.showToast({
      title: '录音设备未就绪',
      icon: 'none',
    })
    return
  }

  try {
    currentRecording.value.startTime = Date.now()
    currentRecording.value.questionId = interviewState.value.currentQuestion
    currentRecording.value.isRecording = true

    // 立即设置录制状态，确保UI能立即响应
    interviewState.value.isRecording = true

    // 开始录音时长计时器
    startRecordingTimer()

    // #ifdef H5
    if (mediaRecorder.value) {
      // 检查MediaRecorder状态
      console.log('MediaRecorder状态:', mediaRecorder.value.state)

      if (mediaRecorder.value.state === 'inactive') {
        recordedChunks.value = [] // 清空之前的录音数据

        // 使用较短的时间片来确保数据及时收集
        mediaRecorder.value.start(500) // 每500ms收集一次数据，确保数据连续性

        console.log('H5录音开始，格式:', mediaRecorder.value.mimeType)

        // 显示录音开始提示
        uni.showToast({
          title: '开始录音',
          icon: 'none',
          duration: 1000,
        })
      } else {
        console.warn('MediaRecorder状态不正确:', mediaRecorder.value.state)
        throw new Error(`MediaRecorder状态错误: ${mediaRecorder.value.state}`)
      }
    } else {
      throw new Error('MediaRecorder未初始化')
    }
    // #endif

    // #ifndef H5
    if (recorderManager.value) {
      recorderManager.value.start({
        duration: 600000, // 最长10分钟
        sampleRate: 44100,
        numberOfChannels: 1,
        encodeBitRate: 192000,
        format: 'mp3',
      })
      console.log('uni-app录音开始')

      // 显示录音开始提示
      uni.showToast({
        title: '开始录音',
        icon: 'none',
        duration: 1000,
      })
    } else {
      throw new Error('录音管理器未初始化')
    }
    // #endif
  } catch (error) {
    console.error('开始录音失败:', error)
    audioStatus.value.error = `开始录音失败: ${error instanceof Error ? error.message : '未知错误'}`

    // 重置录音状态
    currentRecording.value.isRecording = false
    interviewState.value.isRecording = false
    stopRecordingTimer()

    // 显示错误提示
    uni.showModal({
      title: '录音失败',
      content: `录音启动失败: ${error instanceof Error ? error.message : '未知错误'}`,
      showCancel: false,
    })
  }
}

/**
 * @description 停止录音
 */
const stopRecording = async () => {
  if (!currentRecording.value.isRecording) {
    console.log('录音未在进行中，无需停止')
    return
  }

  try {
    console.log('开始停止录音...')

    // 停止录音时长计时器
    stopRecordingTimer()

    // #ifdef H5
    if (mediaRecorder.value) {
      console.log('MediaRecorder当前状态:', mediaRecorder.value.state)

      if (mediaRecorder.value.state === 'recording') {
        // 停止录音，这会触发onstop事件
        mediaRecorder.value.stop()
        console.log('H5录音停止指令已发送')

        // 显示停止录音提示
        uni.showToast({
          title: '录音已停止',
          icon: 'none',
          duration: 1000,
        })
      } else if (mediaRecorder.value.state === 'paused') {
        // 如果是暂停状态，先恢复再停止
        mediaRecorder.value.resume()
        setTimeout(() => {
          mediaRecorder.value.stop()
        }, 100)
        console.log('从暂停状态恢复并停止录音')
      } else {
        console.warn('MediaRecorder状态不是recording:', mediaRecorder.value.state)
      }
    } else {
      console.error('MediaRecorder不存在')
    }
    // #endif

    // #ifndef H5
    if (recorderManager.value) {
      recorderManager.value.stop()
      console.log('uni-app录音停止')

      // 显示停止录音提示
      uni.showToast({
        title: '录音已停止',
        icon: 'none',
        duration: 1000,
      })
    } else {
      console.error('录音管理器不存在')
    }
    // #endif

    // 立即设置录制状态为停止（在事件处理之前）
    interviewState.value.isRecording = false
    currentRecording.value.isRecording = false

  } catch (error) {
    console.error('停止录音失败:', error)
    audioStatus.value.error = `停止录音失败: ${error instanceof Error ? error.message : '未知错误'}`

    // 强制重置录音状态
    interviewState.value.isRecording = false
    currentRecording.value.isRecording = false

    // 显示错误提示
    uni.showModal({
      title: '停止录音失败',
      content: `录音停止时发生错误: ${error instanceof Error ? error.message : '未知错误'}`,
      showCancel: false,
    })
  }
}


/**
 * @description 开始面试
 */
const startInterview = async () => {
  // 检查摄像头状态，如果摄像头未就绪则不允许开始面试
  if (cameraCheckState.value.isCameraRequired && !cameraCheckState.value.isCameraReady) {
    uni.showModal({
      title: '摄像头未就绪',
      content: '面试需要开启前置摄像头，请允许访问摄像头后再开始面试',
      showCancel: false,
      success: () => {
        // 尝试重新初始化摄像头
        initMediaDevices()
      },
    })
    return
  }

  interviewState.value.isStarted = true
  interviewState.value.startTime = Date.now()

  // 初始化WebSocket连接
  // TODO 暂时关闭
  // initWebSocket()
  // 启动表情检测定时器
  // startEmotionDetection()


  // 启动计时器
  startTimer()

  // 开始第一题的录音
  await startRecording()


  console.log('面试开始，第一题录音已启动')
}

/**
 * @description 启动表情检测
 */
const startEmotionDetection = () => {
  // 每3秒发送一次表情检测请求
  const emotionTimer = setInterval(() => {
    if (interviewState.value.isRecording && wsStatus.value.connected) {
      sendEmotionDetectionRequest()
    }
  }, 3000)

  // 将定时器添加到管理列表
  addTimer(emotionTimer)
}

/**
 * @description 启动计时器
 */
const startTimer = () => {
  timer = setInterval(() => {
    if (!interviewState.value.isPaused && interviewState.value.timeRemaining > 0) {
      interviewState.value.timeRemaining--

      // 时间提醒
      checkTimeReminder()
    }
  }, 1000)
}

/**
 * @description 检查时间提醒
 */
const checkTimeReminder = () => {
  const remaining = interviewState.value.timeRemaining

  if (remaining === 300 && !timeReminder.value.shown) {
    // 5分钟提醒
    showTimeReminder('warning', '还剩5分钟')
  } else if (remaining === 60) {
    // 1分钟提醒
    showTimeReminder('danger', '还剩1分钟')
  }
}

/**
 * @description 显示时间提醒
 */
const showTimeReminder = (type: string, message: string) => {
  timeReminder.value = {
    show: true,
    type,
    icon: type === 'danger' ? 'i-mdi-alert' : 'i-mdi-clock',
    message,
    questionStartTime: Date.now(),
    shown: true,
    lastReminder: Date.now(),
  }

  // 3秒后自动隐藏
  setTimeout(() => {
    timeReminder.value.show = false
  }, 3000)
}

/**
 * @description 关闭时间提醒
 */
const dismissTimeReminder = () => {
  timeReminder.value.show = false
}

/**
 * @description 切换暂停状态
 */
const togglePause = () => {
  interviewState.value.isPaused = !interviewState.value.isPaused
}

/**
 * @description 下一题
 */
const nextQuestion = async () => {
  if (interviewState.value.currentQuestion >= interviewState.value.totalQuestions - 1) {
    return
  }

  // 停止当前题目的录音
  await stopRecording()

  // 显示题目切换动画
  isQuestionChanging.value = true

  // 显示答题完成动画
  showCompletionAnimation.value = true
  setTimeout(() => {
    showCompletionAnimation.value = false
  }, 2000)

  // 切换到下一题
  setTimeout(async () => {
    interviewState.value.currentQuestion++
    isQuestionChanging.value = false
    userAnswer.value = '' // 清空答案

    // 开始新题目的录音
    await startRecording()

    console.log(`切换到第 ${interviewState.value.currentQuestion + 1} 题，录音已启动`)
  }, 500)
}

/**
 * @description 结束面试
 */
const endInterview = async () => {
  // 停止当前录音
  await stopRecording()

  // 显示面试完成动画
  showInterviewCompletion.value = true

  // 模拟生成报告进度
  let progress = 0
  const progressTimer = setInterval(() => {
    progress += Math.random() * 10
    completionProgress.value = Math.min(100, Math.floor(progress))

    if (completionProgress.value >= 100) {
      clearInterval(progressTimer)
      setTimeout(() => {
        // 跳转到结果页面
        uni.redirectTo({
          url: '/pages/interview/result',
        })
      }, 1000)
    }
  }, 200)

  // 清理资源
  cleanup()
}

/**
 * @description 生成录音文件名
 * @param recording 录音数据
 * @param index 文件索引
 * @param format 文件格式
 */
const generateRecordingFileName = (recording: any, index: number, format: string = 'webm'): string => {
  const interviewDate = new Date(interviewState.value.startTime || Date.now())
  const dateStr = interviewDate.toISOString().slice(0, 19).replace(/[T:]/g, '-')
  const questionNum = (recording.questionId + 1).toString().padStart(2, '0')
  const duration = Math.round(recording.duration / 1000)

  // 构建文件名：面试日期_问题编号_时长_索引.格式
  return `interview_${dateStr}_Q${questionNum}_${duration}s_${index + 1}.${format}`
}

/**
 * @description 生成元数据文件内容
 */
const generateMetadataContent = (): string => {
  const metadata = {
    interviewInfo: {
      sessionId: sessionParams.value.sessionId,
      jobId: sessionParams.value.jobId,
      jobName: sessionParams.value.jobName,
      company: sessionParams.value.company,
      mode: sessionParams.value.mode,
      startTime: interviewState.value.startTime,
      endTime: Date.now(),
      totalDuration: Date.now() - (interviewState.value.startTime || 0),
      totalQuestions: interviewState.value.totalQuestions,
      completedQuestions: questionRecordings.value.length,
    },
    recordings: questionRecordings.value.map((recording, index) => ({
      fileName: generateRecordingFileName(recording, index, downloadConfig.value.downloadFormat),
      questionId: recording.questionId,
      questionText: recording.questionText,
      startTime: recording.startTime,
      endTime: recording.endTime,
      duration: recording.duration,
      durationFormatted: formatTime(Math.round(recording.duration / 1000)),
    })),
    downloadInfo: {
      downloadTime: Date.now(),
      format: downloadConfig.value.downloadFormat,
      totalFiles: questionRecordings.value.length,
      compressionLevel: downloadConfig.value.compressionLevel,
    },
  }

  return JSON.stringify(metadata, null, 2)
}

/**
 * @description 上传录音数据到后台（保留原有功能）
 */
const uploadRecordingsToServer = async () => {
  if (questionRecordings.value.length === 0) {
    console.warn('没有录音数据需要上传')
    return
  }

  try {
    console.log(`开始上传 ${questionRecordings.value.length} 个录音文件`)

    // 创建FormData用于上传文件
    const formData = new FormData()

    // 添加面试基本信息
    formData.append('sessionId', sessionParams.value.sessionId)
    formData.append('jobId', sessionParams.value.jobId.toString())
    formData.append('totalQuestions', interviewState.value.totalQuestions.toString())
    formData.append(
      'interviewDuration',
      (Date.now() - (interviewState.value.startTime || 0)).toString(),
    )

    // 添加每个问题的录音文件
    questionRecordings.value.forEach((recording: any, index: number) => {
      if (recording.audioBlob) {
        // 创建文件名
        const fileName = generateRecordingFileName(recording, index, 'webm')
        formData.append(`audio_${index}`, recording.audioBlob, fileName)

        // 添加录音元数据
        formData.append(
          `metadata_${index}`,
          JSON.stringify({
            questionId: recording.questionId,
            questionText: recording.questionText,
            startTime: recording.startTime,
            endTime: recording.endTime,
            duration: recording.duration,
          }),
        )
      }
    })
    console.log(formData);

    // // 发送到后台
    // const response = await fetch('/interview/upload-recordings', {
    //   method: 'POST',
    //   body: formData,
    // })

    // if (response.ok) {
    //   const result = await response.json()
    //   console.log('录音上传成功:', result)
    // } else {
    //   throw new Error(`上传失败: ${response.status} ${response.statusText}`)
    // }
  } catch (error) {
    console.error('上传录音数据失败:', error)
    // 可以在这里添加重试逻辑或者本地存储
    throw error
  }
}

/**
 * @description 退出面试
 */
const exitInterview = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出面试吗？当前进度将不会保存。',
    success: (res) => {
      if (res.confirm) {
        cleanup()
        uni.navigateBack()
      }
    },
  })
}

/**
 * @description 清理资源
 */
const cleanup = () => {
  // 清理定时器
  clearAllTimers()

  // 停止录音计时器
  stopRecordingTimer()

  // 停止录制
  if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
    mediaRecorder.value.stop()
  }

  // 停止uni-app录音
  // #ifndef H5
  if (recorderManager.value) {
    try {
      recorderManager.value.stop()
    } catch (error) {
      console.warn('停止录音管理器失败:', error)
    }
  }
  // #endif

  // 关闭摄像头流
  if (videoStream.value) {
    videoStream.value.getTracks().forEach((track: any) => track.stop())
  }

  // 关闭音频流
  if (audioStream.value) {
    audioStream.value.getTracks().forEach((track: any) => track.stop())
  }

  // 清理WebSocket连接
  closeWebSocket()

  // 清理录音数据
  recordedChunks.value = []
  currentRecording.value.isRecording = false
}

// 窗口大小变化处理
const handleResize = () => {
  // 重新计算布局
  nextTick(() => {
    // 弹窗现在使用CSS居中，不需要JavaScript重新定位
    console.log('窗口大小已变化，弹窗将自动重新居中')
  })
}

// 页面加载时初始化
onMounted(() => {
  initMediaDevices()

  // 监听窗口大小变化
  // #ifdef H5
  window.addEventListener('resize', handleResize)
  window.addEventListener('orientationchange', handleResize)
  // #endif

  // uni-app 环境下监听屏幕旋转
  // #ifndef H5
  uni.onWindowResize(handleResize)
  // #endif

  // 定时检查摄像头状态
  const cameraCheckTimer = setInterval(() => {
    checkCameraStatus()
  }, 2000)

  // 将定时器添加到管理列表
  addTimer(cameraCheckTimer)
})

// 页面卸载时清理资源
onUnmounted(() => {
  cleanup()

  // 清理事件监听器
  // #ifdef H5
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('orientationchange', handleResize)
  // #endif

  // #ifndef H5
  uni.offWindowResize(handleResize)
  // #endif
})

/**
 * @description 截取摄像头当前画面并直接下载到本地
 */
const takeScreenshot = async () => {
  try {
    // #ifdef H5
    if (h5CameraRef.value) {
      // 在H5环境下使用高清截图
      const imageData = h5CameraRef.value.captureFrame(true) // 传递true参数表示使用高清模式
      console.log('imageData', imageData)
      if (imageData) {
        const timestamp = Date.now()
        // 直接创建下载链接并触发下载
        const link = document.createElement('a')
        link.href = imageData
        link.download = `interview-screenshot-${timestamp}.jpg`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 显示提示
        uni.showToast({
          title: '截图已保存',
          icon: 'success',
        })

        console.log('H5摄像头截图成功并已下载')
      }
    }
    // #endif

    // #ifndef H5
    if (miniCameraRef.value) {
      // 在小程序环境下使用高质量拍照
      const imagePath = await miniCameraRef.value.takePhoto({
        quality: 'high',
        success: (res) => {
          console.log('小程序摄像头截图成功:', res)
        },
      })

      if (imagePath) {
        // 小程序环境下直接保存到相册
        await uni.saveImageToPhotosAlbum({
          filePath: imagePath,
          success: () => {
            uni.showToast({
              title: '截图已保存到相册',
              icon: 'success',
            })
          },
          fail: (err) => {
            console.error('保存到相册失败:', err)
            uni.showToast({
              title: '保存到相册失败',
              icon: 'none',
            })
          },
        })

        console.log('小程序摄像头截图成功并已保存到相册:', imagePath)
      }
    }
    // #endif
  } catch (error) {
    console.error('截图失败:', error)
    uni.showToast({
      title: '截图失败',
      icon: 'none',
    })
  }
}

/**
 * @description 检查摄像头状态
 */
const checkCameraStatus = () => {
  // 如果摄像头已就绪，则返回true
  if (cameraStatus.value.isReady) {
    cameraCheckState.value.isCameraReady = true
    cameraCheckState.value.isCameraChecked = true
    cameraCheckState.value.checkMessage = '摄像头已就绪'
    return true
  }

  // 如果摄像头有错误，则返回false
  if (cameraStatus.value.error) {
    cameraCheckState.value.isCameraReady = false
    cameraCheckState.value.isCameraChecked = true
    cameraCheckState.value.checkMessage = cameraStatus.value.error
    return false
  }

  // 如果摄像头正在加载，则等待
  if (cameraStatus.value.isLoading) {
    cameraCheckState.value.isCameraChecked = false
    cameraCheckState.value.checkMessage = '正在检测摄像头...'
    return false
  }

  // 默认返回false
  cameraCheckState.value.isCameraReady = false
  cameraCheckState.value.isCameraChecked = true
  cameraCheckState.value.checkMessage = '摄像头未就绪'
  return false
}

// 智能建议相关
const smartSuggestionRef = ref(null)

/**
 * @description 处理智能建议关闭
 * @param id 建议ID
 */
const handleSuggestionDismiss = (id) => {
  const index = realTimeSuggestions.value.findIndex((s) => s.id === id)
  if (index > -1) {
    realTimeSuggestions.value.splice(index, 1)
  }
}

/**
 * @description 处理关闭所有智能建议
 */
const dismissAllSuggestions = () => {
  realTimeSuggestions.value = []
}

/**
 * @description 处理智能建议动作
 * @param suggestion 建议对象
 */
const handleSuggestionAction = (suggestion) => {
  console.log('执行建议动作:', suggestion)
  // 这里可以根据建议类型执行不同的动作
  uni.showToast({
    title: `执行了"${suggestion.action}"操作`,
    icon: 'none',
  })
}

// 情绪分析相关
const emotionAnalysisRef = ref(null)
const emotionData = ref({
  happy: 78,
  neutral: 12,
  sad: 5,
  angry: 2,
  surprised: 3,
})

/**
 * @description 处理情绪数据更新
 * @param data 情绪数据
 */
const handleEmotionUpdate = (data) => {
  emotionData.value = data.data
  console.log('情绪数据更新:', data.primary)
}

// 计时器相关
const interviewTimerRef = ref(null)

/**
 * @description 处理计时器超时
 */
const handleTimerTimeout = () => {
  console.log('面试时间到!')
  uni.showModal({
    title: '面试时间结束',
    content: '您的面试时间已结束，请点击"结束面试"按钮完成面试',
    showCancel: false,
  })
}

/**
 * @description 处理计时器提醒
 * @param data 提醒数据
 */
const handleTimerReminder = (data) => {
  console.log('计时器提醒:', data)
  // 显示提醒
  showTimeReminder(data.type, data.message)
}

// 截图相关
const screenshotButtonRef = ref(null)

/**
 * @description 处理截图
 * @param data 截图参数
 */
const handleCapture = async (data) => {
  try {
    let imageData = null

    // #ifdef H5
    if (h5CameraRef.value) {
      // 在H5环境下使用高清截图
      imageData = h5CameraRef.value.captureFrame(data.quality === 'high')
    }
    // #endif

    // #ifndef H5
    if (miniCameraRef.value) {
      // 在小程序环境下使用高质量拍照
      imageData = await miniCameraRef.value.takePhoto({
        quality: data.quality,
      })
    }
    // #endif

    if (imageData) {
      // 直接下载或保存到相册
      // #ifdef H5
      // H5环境下直接下载
      const link = document.createElement('a')
      link.href = imageData
      link.download = `interview-screenshot-${data.timestamp}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      // #endif

      // #ifndef H5
      // 小程序环境下保存到相册
      await uni.saveImageToPhotosAlbum({
        filePath: imageData,
        success: () => {
          uni.showToast({
            title: '截图已保存到相册',
            icon: 'success',
          })
        },
        fail: (err) => {
          console.error('保存到相册失败:', err)
          uni.showToast({
            title: '保存到相册失败',
            icon: 'none',
          })
        },
      })
      // #endif

      uni.showToast({
        title: '截图成功',
        icon: 'success',
      })
    }
  } catch (error) {
    console.error('截图失败:', error)
    uni.showToast({
      title: '截图失败',
      icon: 'none',
    })
  }
}

const getQuestionCardClass = () => {
  return {
    'question-changing': isQuestionChanging,
  }
}
</script>

<template>
  <view class="interview-room">
    <!-- 全屏加载遮罩 -->
    <view class="loading-overlay" v-if="loadingState.isInitializing">
      <view class="loading-content">
        <view class="loading-spinner">
          <text class="i-mdi-loading spinner-icon"></text>
        </view>
        <text class="loading-message">{{ loadingState.message || '正在加载...' }}</text>
      </view>
    </view>

    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="back-btn" @click="exitInterview">
        <text class="i-mdi-chevron-left"></text>
      </view>
      <view class="header-center">
        <text class="title">智能面试助手</text>
        <text class="subtitle">
          第 {{ interviewState.currentQuestion + 1 }}/{{ interviewState.totalQuestions }} 题
        </text>
      </view>

      <!-- 集成计时器组件 -->
      <InterviewTimer ref="interviewTimerRef" :total-time="interviewState.timeRemaining"
        :is-paused="interviewState.isPaused" :auto-start="interviewState.isStarted" @timeout="handleTimerTimeout"
        @reminder="handleTimerReminder" />

    </view>

    <!-- 主内容区域 -->
    <view class="main-content">
      <!-- 面试场景区域 -->
      <view class="interview-scene" v-if="!loadingState.isInitializing">
        <!-- 右侧内容区域 - 占据30%空间 -->
        <view class="side-content">
          <!-- 摄像头预览区域 -->
          <view class="camera-container">
            <view class="camera-wrapper" :class="{
              loading: cameraStatus.isLoading,
              error: cameraStatus.error,
              hidden: !showUserVideo,
            }">
              <!-- H5环境摄像头组件 -->
              <!-- #ifdef H5 -->
              <H5Camera ref="h5CameraRef" :autoInit="true" :initialFacing="'user'" :definition="true"
                :forceFrontCamera="true" @ready="handleH5CameraReady" @error="handleH5CameraError"
                @close="handleH5CameraClose" />
              <!-- #endif -->

              <!-- 小程序环境摄像头组件 -->
              <!-- #ifndef H5 -->
              <MiniCamera ref="miniCameraRef" :autoInit="true" :initialFacing="'front'" :forceFrontCamera="true"
                @ready="handleMiniCameraReady" @error="handleMiniCameraError" @close="handleMiniCameraClose" />
              <!-- #endif -->

              <!-- 摄像头隐藏提示 -->
              <view class="camera-hidden-notice" v-if="!showUserVideo && cameraStatus.isReady">
                <text class="fa fa-eye-slash notice-icon"></text>
                <text class="notice-text">摄像头已隐藏</text>
              </view>

              <!-- 加载中或错误状态显示 -->
              <view class="camera-status" v-if="cameraStatus.isLoading || cameraStatus.error">
                <view v-if="cameraStatus.isLoading" class="loading-spinner">
                  <text class="fa fa-spinner fa-spin spinner-icon"></text>
                  <text class="status-text">加载中...</text>
                </view>
                <view v-else-if="cameraStatus.error" class="error-info">
                  <text class="fa fa-exclamation-circle error-icon"></text>
                  <text class="error-text">{{ cameraStatus.error }}</text>
                  <button class="retry-btn" @click="initMediaDevices">重试</button>
                </view>
              </view>

              <!-- 麦克风状态指示器 -->
              <view class="mic-status" :class="{ active: audioStatus.isReady && interviewState.isRecording }">
                <text class="i-mdi-microphone" v-if="audioStatus.isReady"></text>
                <text class="i-mdi-microphone-off" v-else></text>
              </view>

              <!-- 摄像头控制面板 -->
              <view class="camera-controls-panel" v-if="cameraStatus.isReady">
                <button class="control-btn-mini" @click="switchCamera">
                  <text class="i-mdi-camera-flip"></text>
                </button>

                <!-- 集成截图按钮组件 -->
                <ScreenshotButton ref="screenshotButtonRef" size="small" :disabled="!cameraStatus.isReady"
                  :show-text="false" @capture="handleCapture" />
              </view>
            </view>
          </view>

          <!-- 集成情绪分析组件 -->
          <EmotionAnalysis ref="emotionAnalysisRef" :is-recording="interviewState.isRecording" :auto-update="true"
            :interval="3000" @update="handleEmotionUpdate" />
        </view>
      </view>

      <!-- 问题卡片 -->
      <view class="question-card" :class="getQuestionCardClass()">
        <transition name="question-slide" mode="out-in">
          <view class="question-content" :key="interviewState.currentQuestion">
            <view class="question-header">
              <view class="question-number">
                <text class="number-text">{{ interviewState.currentQuestion + 1 }}</text>
                <text class="total-text">/{{ interviewState.totalQuestions }}</text>
              </view>
              <view class="question-type-badge" :class="`type-${currentQuestionData.type}`">
                <text class="type-text">{{ getQuestionTypeLabel(currentQuestionData.type) }}</text>
              </view>
            </view>

            <text class="question-text">{{ currentQuestionData.question }}</text>

            <view class="tips-section" v-if="currentQuestionData.tips">
              <view class="tips-icon">
                <text class="i-mdi-lightbulb"></text>
              </view>
              <text class="tips-text">{{ currentQuestionData.tips }}</text>
            </view>

            <view class="question-progress">
              <view class="progress-bar">
                <view class="progress-fill" :style="{
                  width:
                    ((interviewState.currentQuestion + 1) / interviewState.totalQuestions) * 100 +
                    '%',
                }"></view>
              </view>
            </view>
          </view>
        </transition>
      </view>

      <!-- 录制状态悬浮窗 -->
      <view class="recording-status-float" v-if="interviewState.isRecording">
        <text class="status-text">录制中</text>
        <text class="question-count">第{{ interviewState.currentQuestion + 1 }}题</text>
      </view>
      <view class="recording-placeholder-float" v-else-if="interviewState.isStarted">
        <text class="placeholder-text">录制暂停</text>
      </view>
      <view class="recording-placeholder-float" v-else>
        <text class="placeholder-text">准备录制</text>
      </view>

      <!-- 语音状态指示器悬浮窗 -->
      <view class="voice-indicator-float" v-if="interviewState.isRecording">
        <text class="i-mdi-waveform voice-icon"></text>
        <text class="voice-text">正在录音</text>
        <text class="recording-duration">{{ formatRecordingDuration() }}</text>
      </view>

      <!-- 录音统计悬浮窗 -->
      <view class="recording-stats-float" v-if="interviewState.isStarted && questionRecordings.length > 0">
        <text class="stats-text">已录制: {{ questionRecordings.length }}题</text>
      </view>

      <!-- 时间提醒悬浮窗 -->
      <view class="time-reminder-float" v-if="timeReminder.show">
        <view class="reminder-content" :class="timeReminder.type">
          <text class="reminder-icon" :class="timeReminder.icon"></text>
          <text class="reminder-text">{{ timeReminder.message }}</text>
          <button class="dismiss-reminder" @click="dismissTimeReminder">
            <text class="i-mdi-close"></text>
          </button>
        </view>
      </view>

    </view>

    <!-- 底部控制区域 -->
    <view class="footer-controls">
      <!-- 未开始状态 -->
      <template v-if="!interviewState.isStarted">
        <button class="start-interview-btn" @click="startInterview" :disabled="!cameraCheckState.isCameraReady"
          :class="{ 'btn-disabled': !cameraCheckState.isCameraReady }">
          <text class="i-mdi-play"></text>
          <text>开始面试</text>
        </button>
      </template>

      <!-- 进行中状态 -->
      <template v-else>
        <view class="controls-group">
          <view class="control-btn-wrapper">
            <button class="control-btn pause-btn" @click="togglePause"
              :class="{ 'btn-active': interviewState.isPaused }">
              <text :class="interviewState.isPaused ? 'i-mdi-play' : 'i-mdi-pause'"></text>
            </button>
            <text class="btn-label">{{ interviewState.isPaused ? '继续' : '暂停' }}</text>
          </view>

          <view class="control-btn-wrapper">
            <button class="control-btn next-btn" @click="nextQuestion"
              :disabled="interviewState.currentQuestion >= interviewState.totalQuestions - 1" :class="{
                'btn-disabled': interviewState.currentQuestion >= interviewState.totalQuestions - 1,
              }">
              <text class="i-mdi-skip-next"></text>
            </button>
            <text class="btn-label">下一题</text>
          </view>

          <button class="end-btn" @click="endInterview">
            <text class="i-mdi-stop"></text>
            <text>结束面试</text>
          </button>
        </view>
      </template>
    </view>

    <!-- 答题完成庆祝动画 -->
    <view class="completion-celebration" v-if="showCompletionAnimation">
      <view class="celebration-content">
        <view class="celebration-icon">
          <text class="i-mdi-check-circle-outline"></text>
        </view>
        <text class="celebration-text">题目回答完成！</text>
        <view class="celebration-particles">
          <view class="particle" v-for="i in 6" :key="i" :style="{ '--delay': i * 0.1 + 's' }"></view>
        </view>
      </view>
    </view>

    <!-- 面试完成动画 -->
    <view class="interview-completion" v-if="showInterviewCompletion">
      <view class="completion-overlay"></view>
      <view class="completion-content">
        <view class="completion-icon">
          <text class="i-mdi-trophy-outline"></text>
        </view>
        <text class="completion-title">面试完成！</text>
        <text class="completion-subtitle">正在生成面试报告...</text>
        <view class="completion-progress">
          <view class="progress-circle">
            <view class="progress-fill" :style="{ transform: `rotate(${completionProgress * 3.6}deg)` }"></view>
          </view>
          <text class="progress-text">{{ completionProgress }}%</text>
        </view>
      </view>
    </view>


  </view>
</template>

<style lang="scss" scoped>
.interview-room {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(to bottom, #f8fbfd, #edf4f8);
}

// 加载遮罩
.loading-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(6px);

  .loading-content {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    align-items: center;

    .loading-spinner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border-radius: 50%;
      box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.3);

      .spinner-icon {
        font-size: 40rpx;
        color: #fff;
        animation: spin 1.5s linear infinite;
      }
    }

    .loading-message {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }
  }
}

// 顶部导航栏
.header {
  position: sticky;
  top: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  background: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

  .back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    color: #00c9a7;
    background: #f5f9fc;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 201, 167, 0.1);
      transform: scale(0.95);
    }
  }

  .header-center {
    flex: 1;
    text-align: center;

    .title {
      margin-bottom: 4rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .subtitle {
      font-size: 24rpx;
      color: #666;
    }
  }

  .time-display {
    min-width: 100rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    font-weight: 600;
    color: #00c9a7;
    text-align: center;
    background: rgba(0, 201, 167, 0.1);
    border-radius: 20rpx;
  }

  // WebSocket状态指示器
  .ws-status-indicator {
    display: flex;
    gap: 8rpx;
    align-items: center;
    padding: 8rpx 12rpx;
    font-size: 20rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &.connected {
      color: #52c41a;
      background: rgba(82, 196, 26, 0.1);
    }

    &.connecting {
      color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
    }

    &.reconnecting {
      color: #faad14;
      background: rgba(250, 173, 20, 0.1);
    }

    .status-icon {
      font-size: 24rpx;

      &.i-mdi-loading {
        animation: spin 1s linear infinite;
      }
    }

    .status-text {
      font-size: 20rpx;
      font-weight: 500;
    }
  }
}

// 主内容区域
.main-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 20rpx;
  min-height: 0;
  /* 允许内容收缩 */
  padding: 20rpx;
  overflow: hidden;
  /* 防止内容溢出 */
}

// 面试场景区域
.interview-scene {
  display: flex;
  flex-shrink: 0;
  /* 防止被压缩 */
  gap: 20rpx;
  min-height: 400rpx;
  /* 改为最小高度 */
  max-height: 50vh;

  /* 限制最大高度为视口高度的50% */
  /* 响应式设计 */
  @media (max-height: 800px) {
    min-height: 300rpx;
    max-height: 40vh;
  }

  @media (max-height: 600px) {
    min-height: 250rpx;
    max-height: 35vh;
  }
}

// 右侧内容区域
.side-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 20rpx;
  min-height: 0;
  /* 允许内容收缩 */
}

// 摄像头容器
.camera-container {
  position: relative;
  flex: 1;
  min-height: 200rpx;
  /* 确保最小高度 */
  aspect-ratio: 16/9;
  /* 保持宽高比 */
  overflow: hidden;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .camera-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    &.loading {
      background: #f5f5f5;
    }

    &.error {
      background: #fff2f0;
    }

    &.hidden {
      .video-stream {
        opacity: 0;
      }
    }

    .video-stream {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
    }

    .camera-hidden-notice {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      flex-direction: column;
      gap: 10rpx;
      align-items: center;
      color: #666;
      transform: translate(-50%, -50%);

      .notice-icon {
        font-size: 48rpx;
      }

      .notice-text {
        font-size: 24rpx;
      }
    }

    .camera-status {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);

      .loading-spinner {
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        align-items: center;

        .spinner-icon {
          font-size: 48rpx;
          color: #00c9a7;
        }

        .status-text {
          font-size: 28rpx;
          color: #666;
        }
      }

      .error-info {
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        align-items: center;

        .error-icon {
          font-size: 48rpx;
          color: #ff4d4f;
        }

        .error-text {
          font-size: 28rpx;
          color: #666;
          text-align: center;
        }

        .retry-btn {
          padding: 12rpx 24rpx;
          font-size: 24rpx;
          color: #fff;
          background: #00c9a7;
          border: none;
          border-radius: 8rpx;
          transition: all 0.3s ease;

          &:active {
            background: #00a085;
            transform: scale(0.95);
          }
        }
      }
    }

    .mic-status {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      font-size: 24rpx;
      color: #fff;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      transition: all 0.3s ease;

      &.active {
        background: rgba(0, 201, 167, 0.8);
        animation: pulse 2s infinite;
      }
    }

    .camera-controls-panel {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      display: flex;
      gap: 12rpx;

      .control-btn-mini {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        font-size: 24rpx;
        color: #fff;
        background: rgba(0, 0, 0, 0.6);
        border: none;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background: rgba(0, 201, 167, 0.8);
          transform: scale(0.9);
        }
      }
    }
  }
}

// 情绪检测区域
.emotion-detection {
  padding: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  .detection-header {
    display: flex;
    gap: 12rpx;
    align-items: center;
    margin-bottom: 20rpx;

    .detection-icon {
      font-size: 32rpx;
      color: #00c9a7;
    }

    .detection-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .emotion-indicators {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .emotion-item {
      display: flex;
      gap: 12rpx;
      align-items: center;

      .emotion-label {
        min-width: 80rpx;
        font-size: 24rpx;
        color: #666;
      }

      .emotion-bar-container {
        flex: 1;
        height: 12rpx;
        overflow: hidden;
        background: #f0f0f0;
        border-radius: 6rpx;

        .emotion-bar {
          height: 100%;
          border-radius: 6rpx;
          transition: width 0.3s ease;
        }
      }

      .emotion-value {
        min-width: 60rpx;
        font-size: 24rpx;
        font-weight: 600;
        color: #333;
        text-align: right;
      }
    }
  }
}

// 问题卡片
.question-card {
  padding: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &.question-changing {
    opacity: 0.8;
    transform: scale(0.98);
  }

  .question-content {
    .question-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .question-number {
        display: flex;
        gap: 4rpx;
        align-items: baseline;

        .number-text {
          font-size: 48rpx;
          font-weight: 700;
          color: #00c9a7;
        }

        .total-text {
          font-size: 28rpx;
          color: #999;
        }
      }

      .question-type-badge {
        padding: 8rpx 16rpx;
        font-size: 22rpx;
        font-weight: 500;
        border-radius: 20rpx;

        &.type-technical {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }

        &.type-experience {
          color: #52c41a;
          background: rgba(82, 196, 26, 0.1);
        }

        &.type-soft_skill {
          color: #fa8c16;
          background: rgba(250, 140, 22, 0.1);
        }
      }
    }

    .question-text {
      margin-bottom: 24rpx;
      font-size: 32rpx;
      font-weight: 500;
      line-height: 1.6;
      color: #333;
    }

    .tips-section {
      display: flex;
      gap: 12rpx;
      align-items: flex-start;
      padding: 20rpx;
      margin-bottom: 24rpx;
      background: #f8fbfd;
      border-radius: 12rpx;

      .tips-icon {
        margin-top: 2rpx;
        font-size: 28rpx;
        color: #faad14;
      }

      .tips-text {
        flex: 1;
        font-size: 26rpx;
        line-height: 1.5;
        color: #666;
      }
    }

    .question-progress {
      .progress-bar {
        height: 8rpx;
        overflow: hidden;
        background: #f0f0f0;
        border-radius: 4rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
          border-radius: 4rpx;
          transition: width 0.3s ease;
        }
      }
    }
  }
}

// 悬浮窗样式
.recording-status-float,
.recording-placeholder-float {
  position: fixed;
  top: 200rpx;
  right: 30rpx;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;

  /* 确保在小屏幕上也能正常显示 */
  @media (max-width: 480rpx) {
    top: 180rpx;
    right: 20rpx;
    padding: 10rpx 16rpx;
  }

  .status-text,
  .placeholder-text {
    font-size: 24rpx;
    font-weight: 600;
    color: #fff;

    @media (max-width: 480rpx) {
      font-size: 22rpx;
    }
  }

  .question-count {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);

    @media (max-width: 480rpx) {
      font-size: 18rpx;
    }
  }
}

.recording-status-float {
  background: rgba(0, 201, 167, 0.9);
  animation: pulse 2s infinite;
}

.voice-indicator-float {
  position: fixed;
  top: 300rpx;
  right: 30rpx;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;

  /* 响应式适配 */
  @media (max-width: 480rpx) {
    top: 280rpx;
    right: 20rpx;
    padding: 10rpx 16rpx;
  }

  .voice-icon {
    font-size: 24rpx;
    color: #00c9a7;
    animation: pulse 1.5s infinite;

    @media (max-width: 480rpx) {
      font-size: 22rpx;
    }
  }

  .voice-text {
    font-size: 22rpx;
    color: #fff;

    @media (max-width: 480rpx) {
      font-size: 20rpx;
    }
  }

  .recording-duration {
    font-family: 'Courier New', monospace;
    font-size: 20rpx;
    font-weight: 600;
    color: #00c9a7;

    @media (max-width: 480rpx) {
      font-size: 18rpx;
    }
  }
}

.recording-stats-float {
  position: fixed;
  top: 400rpx;
  left: 30rpx;
  z-index: 100;
  padding: 8rpx 16rpx;
  background: rgba(0, 201, 167, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;

  /* 响应式适配 */
  @media (max-width: 480rpx) {
    top: 380rpx;
    left: 20rpx;
    padding: 6rpx 12rpx;
  }

  .stats-text {
    font-size: 20rpx;
    font-weight: 500;
    color: #fff;

    @media (max-width: 480rpx) {
      font-size: 18rpx;
    }
  }
}

// 时间提醒悬浮窗
.time-reminder-float {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 200;
  /* 确保在所有设备上都能正确居中 */
  max-width: calc(100vw - 80rpx);
  max-width: calc(100dvw - 80rpx);
  /* 动态视口宽度 */
  transform: translate(-50%, -50%);
  animation: slideInDown 0.3s ease;

  /* 响应式适配 */
  @media (max-width: 480rpx) {
    max-width: calc(100vw - 40rpx);
    max-width: calc(100dvw - 40rpx);
  }

  .reminder-content {
    display: flex;
    gap: 12rpx;
    align-items: center;
    padding: 20rpx 30rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

    /* 响应式适配 */
    @media (max-width: 480rpx) {
      gap: 10rpx;
      padding: 16rpx 24rpx;
    }

    &.warning {
      border-left: 6rpx solid #faad14;
    }

    &.danger {
      border-left: 6rpx solid #ff4d4f;
    }

    .reminder-icon {
      flex-shrink: 0;
      /* 防止图标被压缩 */
      font-size: 32rpx;

      @media (max-width: 480rpx) {
        font-size: 28rpx;
      }

      &.i-mdi-clock {
        color: #faad14;
      }

      &.i-mdi-alert {
        color: #ff4d4f;
      }
    }

    .reminder-text {
      flex: 1;
      /* 允许文本伸缩 */
      font-size: 28rpx;
      font-weight: 500;
      color: #333;

      @media (max-width: 480rpx) {
        font-size: 26rpx;
      }
    }

    .dismiss-reminder {
      display: flex;
      flex-shrink: 0;
      /* 防止按钮被压缩 */
      align-items: center;
      justify-content: center;
      width: 40rpx;
      height: 40rpx;
      font-size: 20rpx;
      color: #999;
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      transition: all 0.3s ease;

      @media (max-width: 480rpx) {
        width: 36rpx;
        height: 36rpx;
        font-size: 18rpx;
      }

      &:active {
        background: #e6e6e6;
        transform: scale(0.9);
      }
    }
  }
}

// 文本输入区域
.input-area {
  padding: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  .text-input {
    width: 100%;
    min-height: 120rpx;
    padding: 16rpx;
    font-size: 28rpx;
    line-height: 1.5;
    color: #333;
    resize: none;
    background: #f8f9fa;
    border: 2rpx solid transparent;
    border-radius: 12rpx;
    transition: all 0.3s ease;

    &:focus {
      background: #fff;
      border-color: #00c9a7;
      box-shadow: 0 0 0 4rpx rgba(0, 201, 167, 0.1);
    }

    &::placeholder {
      color: #999;
    }
  }

  .input-meta {
    display: flex;
    justify-content: flex-end;
    margin-top: 12rpx;

    .char-count {
      font-size: 24rpx;
      color: #999;
    }
  }
}

// 智能建议悬浮窗
.smart-suggestions-container {
  position: fixed;
  top: 320rpx;
  right: 30rpx;
  z-index: 100;
  width: 400rpx;
  max-height: 500rpx;
  overflow: hidden;

  /* 响应式适配 */
  @media (max-width: 768rpx) {
    top: 300rpx;
    right: 20rpx;
    width: 350rpx;
  }

  @media (max-width: 480rpx) {
    top: 280rpx;
    right: 15rpx;
    width: 300rpx;
  }
}

.footer-controls {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: flex;
  flex-direction: row;
  gap: 30rpx;
  align-items: center;
  justify-content: center;
  padding: 35rpx 30rpx 40rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  box-shadow: 0 -12rpx 32rpx rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &::before {
    position: absolute;
    top: 0;
    left: 50%;
    width: 60rpx;
    height: 6rpx;
    margin-top: 12rpx;
    content: '';
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3rpx;
    transform: translateX(-50%);
  }

  .start-interview-btn {
    position: relative;
    display: flex;
    gap: 16rpx;
    align-items: center;
    padding: 28rpx 70rpx;
    overflow: hidden;
    font-size: 34rpx;
    font-weight: 600;
    color: #fff;
    letter-spacing: 2rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    border: none;
    border-radius: 60rpx;
    box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      content: '';
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #00b396 0%, #45c4b8 100%);
      box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.5);
      transform: translateY(-4rpx);

      &::before {
        left: 100%;
      }
    }

    &:active {
      background: linear-gradient(135deg, #00a085 0%, #3fb8a8 100%);
      box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);
      transform: translateY(-2rpx) scale(0.98);
    }

    &.btn-disabled {
      cursor: not-allowed;
      background: linear-gradient(135deg, #cccccc 0%, #d9d9d9 100%);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      opacity: 0.6;

      &:hover,
      &:active {
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        transform: none;
      }

      &::before {
        display: none;
      }
    }

    .i-mdi-play {
      margin-right: 4rpx;
      font-size: 38rpx;
    }

    // 添加脉冲动画效果
    &:not(.btn-disabled) {
      animation: pulse-glow 2s infinite;
    }
  }

  @keyframes pulse-glow {

    0%,
    100% {
      box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.4);
    }

    50% {
      box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.6), 0 0 0 8rpx rgba(0, 201, 167, 0.1);
    }
  }

  // 进行中状态的控制按钮组
  .controls-group {
    display: flex;
    gap: 40rpx;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx;

    .control-btn-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 8rpx;
      align-items: center;

      .btn-label {
        font-size: 22rpx;
        font-weight: 500;
        color: #666;
        letter-spacing: 0.5rpx;
        transition: color 0.3s ease;
      }

      &:hover .btn-label {
        color: #333;
      }

      // 添加分隔线效果
      &:not(:last-child)::after {
        position: absolute;
        top: 50%;
        right: -20rpx;
        width: 1rpx;
        height: 60rpx;
        content: '';
        background: rgba(0, 0, 0, 0.08);
        transform: translateY(-50%);
      }
    }

    .end-btn {
      position: relative;
      margin-left: 15rpx;

      &::before {
        position: absolute;
        top: 50%;
        left: -27.5rpx;
        width: 1rpx;
        height: 50rpx;
        content: '';
        background: rgba(255, 77, 79, 0.2);
        transform: translateY(-50%);
      }
    }
  }

  .control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90rpx;
    height: 90rpx;
    font-size: 36rpx;
    color: #666;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border: 2rpx solid rgba(0, 0, 0, 0.08);
    border-radius: 50%;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: rgba(0, 0, 0, 0.12);
      box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
      transform: translateY(-2rpx);
    }

    &:active {
      background: #f0f0f0;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      transform: scale(0.95);
    }

    &.btn-active {
      color: #fff;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border-color: #00c9a7;
      box-shadow: 0 8rpx 25rpx rgba(0, 201, 167, 0.3);

      &:hover {
        background: linear-gradient(135deg, #00b396 0%, #45c4b8 100%);
        box-shadow: 0 10rpx 30rpx rgba(0, 201, 167, 0.4);
      }
    }

    &.btn-disabled {
      color: #bbb;
      cursor: not-allowed;
      background: #f5f5f5;
      border-color: #e0e0e0;
      opacity: 0.4;

      &:hover,
      &:active {
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
        transform: none;
      }
    }

    &.pause-btn {
      color: #666;
      background: rgba(255, 255, 255, 0.95);
      border: 2rpx solid #e0e0e0;

      &.btn-active {
        color: #fff;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        border-color: #00c9a7;
      }

      &:hover:not(.btn-active) {
        color: #00c9a7;
        background: rgba(0, 201, 167, 0.05);
        border-color: #00c9a7;
      }
    }

    &.next-btn {
      color: #1890ff;
      background: rgba(24, 144, 255, 0.05);
      border: 2rpx solid rgba(24, 144, 255, 0.2);

      &:hover:not(.btn-disabled) {
        color: #fff;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        border-color: #1890ff;
        box-shadow: 0 8rpx 25rpx rgba(24, 144, 255, 0.3);
      }

      &.btn-disabled {
        color: #bbb;
        background: #f5f5f5;
        border-color: #e0e0e0;
      }
    }
  }

  .end-btn {
    display: flex;
    gap: 16rpx;
    align-items: center;
    padding: 22rpx 50rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #fff;
    letter-spacing: 1rpx;
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    border: none;
    border-radius: 50rpx;
    box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);
    transition: all 0.3s ease;

    &:active {
      background: linear-gradient(135deg, #d9363e 0%, #e05252 100%);
      box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.2);
      transform: scale(0.95);
    }

    &:hover {
      background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
      box-shadow: 0 10rpx 28rpx rgba(255, 77, 79, 0.4);
      transform: translateY(-2rpx);
    }
  }

  // 响应式适配
  @media (max-width: 768rpx) {
    gap: 20rpx;
    padding: 24rpx 20rpx;

    .start-interview-btn {
      padding: 20rpx 40rpx;
      font-size: 28rpx;
    }

    .control-btn {
      width: 80rpx;
      height: 80rpx;
      font-size: 32rpx;
    }

    .end-btn {
      padding: 18rpx 36rpx;
      font-size: 28rpx;
    }
  }

  @media (max-width: 480rpx) {
    gap: 16rpx;
    padding: 20rpx 16rpx;

    .controls-group {
      gap: 20rpx;
    }

    .control-btn {
      width: 70rpx;
      height: 70rpx;
      font-size: 28rpx;
    }

    .end-btn {
      padding: 16rpx 30rpx;
      font-size: 26rpx;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

@keyframes modalFadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes modalContentSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20rpx);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.02) translateY(-5rpx);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 保留原有动画作为备用 */
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.7) translateY(-50rpx);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-10rpx);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

// 完成动画
.completion-celebration {
  position: fixed;
  inset: 0;
  /* 使用 inset 简化定位 */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  /* 防止初始渲染时的位置跳跃 */
  opacity: 0;
  animation: modalFadeIn 0.3s ease forwards;

  .celebration-content {
    display: flex;
    flex-direction: column;
    gap: 30rpx;
    align-items: center;
    padding: 60rpx;
    background: #fff;
    border-radius: 20rpx;
    animation: celebrationSlideIn 0.5s ease;

    .celebration-icon {
      font-size: 120rpx;
      color: #52c41a;
      animation: pulse 1s infinite;
    }

    .celebration-text {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .celebration-particles {
      position: relative;
      width: 200rpx;
      height: 100rpx;

      .particle {
        position: absolute;
        width: 12rpx;
        height: 12rpx;
        background: #00c9a7;
        border-radius: 50%;
        animation: float 2s infinite;
        animation-delay: var(--delay);

        &:nth-child(1) {
          top: 20%;
          left: 10%;
        }

        &:nth-child(2) {
          top: 60%;
          left: 30%;
        }

        &:nth-child(3) {
          top: 10%;
          left: 50%;
        }

        &:nth-child(4) {
          top: 50%;
          left: 70%;
        }

        &:nth-child(5) {
          top: 30%;
          left: 85%;
        }

        &:nth-child(6) {
          top: 80%;
          left: 20%;
        }
      }
    }
  }
}

.interview-completion {
  position: fixed;
  inset: 0;
  /* 使用 inset 简化定位 */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 防止初始渲染时的位置跳跃 */
  opacity: 0;
  animation: modalFadeIn 0.3s ease forwards;

  .completion-overlay {
    position: absolute;
    inset: 0;
    /* 使用 inset 简化定位 */
    background: linear-gradient(135deg, rgba(0, 201, 167, 1) 0%, rgba(79, 209, 199, 0.9) 100%);
    backdrop-filter: blur(10px);
  }

  .completion-content {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 40rpx;
    align-items: center;
    padding: 80rpx 60rpx;
    background: #fff;
    border-radius: 30rpx;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
    animation: completionSlideIn 0.5s ease;

    .completion-icon {
      font-size: 160rpx;
      color: #faad14;
      animation: pulse 2s infinite;
    }

    .completion-title {
      font-size: 48rpx;
      font-weight: 700;
      color: #333;
    }

    .completion-subtitle {
      font-size: 28rpx;
      color: #666;
    }

    .completion-progress {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .progress-circle {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        overflow: hidden;
        border: 8rpx solid #f0f0f0;
        border-radius: 50%;

        .progress-fill {
          position: absolute;
          top: -8rpx;
          left: -8rpx;
          width: 120rpx;
          height: 120rpx;
          border: 8rpx solid #00c9a7;
          border-right-color: transparent;
          border-bottom-color: transparent;
          border-radius: 50%;
          transition: transform 0.3s ease;
          transform-origin: center;
        }
      }

      .progress-text {
        position: absolute;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }
  }
}

@keyframes float {

  0%,
  100% {
    opacity: 1;
    transform: translateY(0) rotate(0deg);
  }

  50% {
    opacity: 0.7;
    transform: translateY(-30rpx) rotate(180deg);
  }
}

/* 新增的弹窗动画 */
@keyframes celebrationSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-30rpx);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes completionSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20rpx);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 全局响应式优化 */
@media (max-height: 600px) {
  .interview-room {
    .header {
      padding: 16rpx 30rpx;

      .header-center .title {
        font-size: 28rpx;
      }

      .header-center .subtitle {
        font-size: 22rpx;
      }
    }

    .main-content {
      gap: 16rpx;
      padding: 16rpx;
    }

    .question-card {
      padding: 24rpx;

      .question-content .question-text {
        font-size: 28rpx;
      }
    }

    .footer-controls {
      padding: 24rpx;
    }
  }
}

/* 超小屏幕优化 */
@media (max-width: 480rpx) {
  .interview-room {
    .interview-scene {
      flex-direction: column;
      min-height: 300rpx;
      max-height: 60vh;
    }

    .side-content {
      min-height: 200rpx;
    }

    .camera-container {
      min-height: 180rpx;
    }

    .emotion-detection {
      padding: 16rpx;

      .detection-header {
        margin-bottom: 16rpx;

        .detection-title {
          font-size: 26rpx;
        }
      }

      .emotion-indicators .emotion-item {
        gap: 10rpx;

        .emotion-label {
          min-width: 70rpx;
          font-size: 22rpx;
        }

        .emotion-value {
          min-width: 50rpx;
          font-size: 22rpx;
        }
      }
    }
  }
}

/* 摄像头控制面板样式 */
.camera-controls-panel {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  z-index: 10;
  display: flex;
  gap: 12rpx;
}

.control-btn-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 24rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:active {
    background: rgba(0, 201, 167, 0.8);
    transform: scale(0.9);
  }
}

/* 录音下载进度弹窗样式 */
.download-progress-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;

  .download-modal-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
  }

  .download-modal-content {
    position: relative;
    width: 100%;
    max-width: 600rpx;
    padding: 60rpx 40rpx;
    margin: 40rpx;
    background: white;
    border-radius: 24rpx;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
    animation: downloadModalSlideIn 0.3s ease-out;

    .download-header {
      margin-bottom: 40rpx;
      text-align: center;

      .download-title {
        display: block;
        margin-bottom: 16rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }

      .download-subtitle {
        display: block;
        font-size: 28rpx;
        color: #666;
      }
    }

    .download-progress-container {
      margin-bottom: 30rpx;

      .progress-bar {
        width: 100%;
        height: 12rpx;
        margin-bottom: 16rpx;
        overflow: hidden;
        background: #f0f0f0;
        border-radius: 6rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4a90e2, #50c878);
          border-radius: 6rpx;
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #4a90e2;
        text-align: center;
      }
    }

    .download-status {
      margin-bottom: 30rpx;
      text-align: center;

      .current-file {
        display: block;
        margin-bottom: 8rpx;
        font-size: 24rpx;
        color: #666;
        word-break: break-all;
      }

      .file-count {
        display: block;
        font-size: 26rpx;
        font-weight: 500;
        color: #333;
      }
    }

    .download-errors {
      margin-bottom: 30rpx;

      .error-title {
        display: block;
        margin-bottom: 16rpx;
        font-size: 28rpx;
        font-weight: bold;
        color: #e74c3c;
      }

      .error-list {
        max-height: 200rpx;
        margin-bottom: 20rpx;
        overflow-y: auto;

        .error-item {
          padding: 12rpx;
          margin-bottom: 8rpx;
          background: #fef2f2;
          border-radius: 8rpx;

          .error-filename {
            display: block;
            margin-bottom: 4rpx;
            font-size: 24rpx;
            font-weight: 500;
            color: #333;
          }

          .error-message {
            display: block;
            font-size: 22rpx;
            color: #e74c3c;
          }
        }
      }

      .retry-download-btn {
        width: 100%;
        padding: 24rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: white;
        background: #e74c3c;
        border: none;
        border-radius: 12rpx;
      }
    }

    .download-actions {
      text-align: center;

      .close-download-btn {
        padding: 24rpx 60rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: white;
        background: #4a90e2;
        border: none;
        border-radius: 12rpx;
      }
    }
  }
}

/* 下载弹窗动画 */
@keyframes downloadModalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-30rpx);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 全局响应式优化 */
@media (max-height: 600px) {
  // ... existing code ...
}
</style>
