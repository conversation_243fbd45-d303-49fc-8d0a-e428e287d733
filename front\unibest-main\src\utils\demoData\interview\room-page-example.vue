<template>
  <view class="interview-room">
    <!-- 全屏加载遮罩 -->
    <view class="loading-overlay" v-if="loadingState.isInitializing">
      <view class="loading-content">
        <view class="loading-spinner">
          <text class="i-mdi-loading spinner-icon"></text>
        </view>
        <text class="loading-message">{{ loadingState.message || '正在加载...' }}</text>
      </view>
    </view>

    <!-- 演示模式指示器 -->
    <view class="demo-mode-indicator" v-if="isDemoMode">
      <text class="demo-mode-text">演示模式</text>
    </view>

    <!-- 页面内容 -->
    <!-- ... 其他页面内容 ... -->
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, watch, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// #ifdef H5
import H5Camera from '@/components/camera/H5Camera.vue'
// #endif
import {
  getSessionInfo,
  getQuestion,
  submitAnswer,
  endInterview as endInterviewApi,
  submitFeedback,
  checkDevices,
  getSessionStatus,
  comprehensiveAnalysis,
  analyzeAudio,
  analyzeVideo,
  analyzeText,
  createAudioStreamAnalysis,
  createVideoStreamAnalysis,
  getAnalysisHistory,
} from '@/service/interview-room'
import {
  createRealTimeAnalysisManager,
  generateSuggestionsFromAudio,
  generateSuggestionsFromVideo,
  generateSuggestionsFromText,
  updateEmotionFromDetection,
  type RealTimeAnalysisManager,
  type SmartSuggestion,
} from '@/utils/realtime-analysis'
import type {
  SessionInfo,
  InterviewQuestion,
  SubmitAnswerRequest,
  EndInterviewRequest,
  FeedbackRequest,
  MultimodalAnalysisResult,
  AudioAnalysisResult,
  VideoAnalysisResult,
  TextAnalysisResult,
} from '@/service/interview-room'
import {
  demoInterviewer,
  demoCompany,
  demoJob,
  demoQuestionDetails,
  demoSuggestionTemplates,
  demoScoringStandards,
  demoReportTemplate,
  demoInterviewFlow,
} from './demo-data'
import VMS from '@/libs/vms-web-sdk/vms-web-sdk-2.0.0.esm.min'

// 导入演示数据集成
import {
  integrateInterviewRoomDemoData,
  cleanupInterviewRoomDemoData,
} from '@/utils/demoData/interview/integration'
import { interviewRoomHelper } from '@/utils/demoData/interview/roomHelper'

// 演示模式标志
const isDemoMode = ref(false)

// 面试会话参数
const sessionParams = ref({
  sessionId: '',
  jobId: 0,
  mode: 'standard',
  isDemo: false,
  jobName: '',
  company: '',
})

// WebSocket连接
let websocket: any = null
const wsStatus = ref({
  connected: false,
  reconnecting: false,
  reconnectCount: 0,
})

// 媒体录制管理
const mediaRecorder = ref<any>(null)
const recordedChunks = ref<Blob[]>([])
const videoStream = ref<MediaStream | null>(null)
const cameraStatus = ref({
  isReady: false,
  isLoading: false,
  error: '',
  isFrontCamera: true, // 默认使用前置摄像头
})
const audioStatus = ref({
  isReady: false,
  isLoading: false,
  error: '',
})

// uni-app摄像头上下文
const cameraContext = ref<any>(null)
// 录音管理器
const recorderManager = ref<any>(null)
// H5摄像头组件引用
// #ifdef H5
const h5CameraRef = ref(null)
// #endif

// 面试状态管理
const interviewState = ref({
  isStarted: false,
  isPaused: false,
  isRecording: false,
  currentQuestion: 0,
  timeRemaining: 1800, // 30分钟
  totalQuestions: 10,
  startTime: null,
})

// 当前问题数据
const currentQuestionData = ref({
  id: 1,
  type: 'technical',
  question: '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
  tips: '建议使用STAR法则来回答：Situation(情况)、Task(任务)、Action(行动)、Result(结果)',
  timeLimit: 180, // 3分钟
  difficulty: 3,
  expectedKeywords: ['Vue', 'React', 'Angular', '组件化', '响应式', '虚拟DOM'],
  evaluationPoints: ['技术深度', '实际应用', '场景分析'],
})

// 用户回答
const userAnswer = ref('')

// 加载状态
const loadingState = ref({
  isInitializing: false,
  message: '',
  progress: 0,
})

// 在script setup部分的顶部添加
const showUserVideo = ref(true)
const emotionData = ref({
  happy: 78,
  neutral: 12,
  sad: 5,
  angry: 2,
  surprised: 3,
})

/**
 * @description 初始化页面
 */
const init = async () => {
  // 设置加载状态
  loadingState.value.isInitializing = true
  loadingState.value.message = '正在初始化面试环境...'

  try {
    // 集成演示数据
    const { isDemoMode: demoMode } = integrateInterviewRoomDemoData()
    isDemoMode.value = demoMode

    // 获取会话信息
    await loadSessionInfo()

    // 初始化媒体设备
    await initMediaDevices()

    // 加载面试问题
    await loadQuestions()

    // 初始化完成
    loadingState.value.isInitializing = false

    // 如果是演示模式，显示提示
    if (isDemoMode.value) {
      uni.showToast({
        title: '演示模式已启用',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('初始化失败:', error)
    loadingState.value.message = '初始化失败，请重试'

    // 显示错误提示
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

/**
 * @description 加载会话信息
 */
const loadSessionInfo = async () => {
  loadingState.value.message = '正在加载会话信息...'
  loadingState.value.progress = 20

  try {
    // 从URL参数中获取会话ID
    const query = uni.getStorageSync('interview_params') || {}
    const sessionId = query.sessionId || ''

    if (!sessionId) {
      throw new Error('会话ID不能为空')
    }

    // 获取会话信息
    const res = await getSessionInfo(sessionId)

    if (res.code !== 200 || !res.data) {
      throw new Error(res.message || '获取会话信息失败')
    }

    // 更新会话参数
    sessionParams.value = {
      sessionId,
      jobId: res.data.jobId || 0,
      mode: res.data.mode || 'standard',
      isDemo: isDemoMode.value || res.data.isDemo || false,
      jobName: res.data.jobName || '',
      company: res.data.company || '',
    }

    console.log('会话信息加载成功:', sessionParams.value)
  } catch (error) {
    console.error('加载会话信息失败:', error)

    // 如果是演示模式，使用演示数据
    if (isDemoMode.value) {
      // 使用演示数据
      sessionParams.value = {
        sessionId: 'demo-session-' + Date.now(),
        jobId: 1001,
        mode: 'standard',
        isDemo: true,
        jobName: demoJob.title,
        company: demoCompany.name,
      }

      console.log('使用演示会话信息:', sessionParams.value)
    } else {
      throw error
    }
  }
}

/**
 * @description 加载面试问题
 */
const loadQuestions = async () => {
  loadingState.value.message = '正在加载面试问题...'
  loadingState.value.progress = 60

  try {
    // 获取第一个问题
    const res = await getQuestion(sessionParams.value.sessionId)

    if (res.code !== 200 || !res.data) {
      throw new Error(res.message || '获取面试问题失败')
    }

    // 更新当前问题数据
    currentQuestionData.value = {
      id: res.data.id || 1,
      type: res.data.type || 'technical',
      question: res.data.content || '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
      tips:
        res.data.hints?.join('\n') ||
        '建议使用STAR法则来回答：Situation(情况)、Task(任务)、Action(行动)、Result(结果)',
      timeLimit: res.data.timeLimit || 180,
      difficulty: res.data.difficulty || 3,
      expectedKeywords: res.data.metadata?.skills || [
        'Vue',
        'React',
        'Angular',
        '组件化',
        '响应式',
        '虚拟DOM',
      ],
      evaluationPoints: res.data.answerPoints || ['技术深度', '实际应用', '场景分析'],
    }

    // 更新面试状态
    interviewState.value.totalQuestions = 10 // 假设总共有10个问题

    console.log('面试问题加载成功:', currentQuestionData.value)
  } catch (error) {
    console.error('加载面试问题失败:', error)

    // 如果是演示模式，使用演示数据
    if (isDemoMode.value) {
      // 使用演示数据
      currentQuestionData.value = {
        id: 1,
        type: 'technical',
        question: '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
        tips: '建议使用STAR法则来回答：Situation(情况)、Task(任务)、Action(行动)、Result(结果)',
        timeLimit: 180,
        difficulty: 3,
        expectedKeywords: ['Vue', 'React', 'Angular', '组件化', '响应式', '虚拟DOM'],
        evaluationPoints: ['技术深度', '实际应用', '场景分析'],
      }

      console.log('使用演示问题数据:', currentQuestionData.value)
    } else {
      throw error
    }
  }
}

/**
 * @description 初始化媒体设备
 */
const initMediaDevices = async () => {
  loadingState.value.message = '正在初始化媒体设备...'
  loadingState.value.progress = 40

  try {
    // 检查设备权限
    const res = await checkDevices()

    if (res.code !== 200 || !res.data) {
      throw new Error(res.message || '检查设备权限失败')
    }

    // 更新设备状态
    cameraStatus.value.isReady = res.data.camera
    audioStatus.value.isReady = res.data.microphone

    // 如果设备就绪，初始化媒体流
    if (cameraStatus.value.isReady && audioStatus.value.isReady) {
      // 初始化媒体流
      // 这里省略具体实现
    }

    console.log('媒体设备初始化成功')
  } catch (error) {
    console.error('初始化媒体设备失败:', error)

    // 如果是演示模式，使用演示数据
    if (isDemoMode.value) {
      // 使用演示数据
      cameraStatus.value.isReady = true
      audioStatus.value.isReady = true

      console.log('使用演示媒体设备状态')
    } else {
      throw error
    }
  }
}

/**
 * @description 创建WebSocket连接
 * @param url WebSocket连接URL
 */
const createWebSocketConnection = (url: string) => {
  // 使用演示数据系统提供的WebSocket创建函数
  // 这样在WebSocket连接失败时，会自动使用模拟的WebSocket连接
  return interviewRoomHelper.createWebSocket(url)
}

/**
 * @description 页面卸载时清理资源
 */
onUnmounted(() => {
  console.log('页面卸载，开始清理资源...')

  // 清理演示数据集成
  cleanupInterviewRoomDemoData()

  // 关闭WebSocket连接
  if (websocket) {
    websocket.close()
    websocket = null
  }

  // 关闭媒体流
  if (videoStream.value) {
    videoStream.value.getTracks().forEach((track: MediaStreamTrack) => track.stop())
    videoStream.value = null
  }

  console.log('资源清理完成')
})

// 页面加载时初始化
onMounted(() => {
  init()
})
</script>

<style lang="scss" scoped>
.interview-room {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
}

.spinner-icon {
  font-size: 80rpx;
  color: #fff;
  animation: spin 1.5s linear infinite;
}

.loading-message {
  font-size: 28rpx;
  color: #fff;
  text-align: center;
}

.demo-mode-indicator {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  z-index: 100;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #fff;
  background-color: rgba(255, 87, 34, 0.8);
  border-radius: 8rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
