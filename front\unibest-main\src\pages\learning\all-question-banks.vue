<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import SearchBox from '@/components/SearchBox.vue'
// @ts-ignore
import Notification from '@/components/Notification.vue'
// @ts-ignore
import {
  getQuestionBankList,
  getMajorQuestionBankStatistics,
  getMajorQuestionBankFilterCounts,
  resetMajorQuestionBankFilters,
  toggleQuestionBankBookmark,
  // @ts-ignore
} from '@/service/learning'
// @ts-ignore
import type {
  QuestionBank,
  QuestionBankVO,
  QuestionBankListParams,
  // @ts-ignore
} from '@/types/learning'

// 页面参数
const majorId = ref('')
const majorName = ref('')

// 页面状态
const isLoading = ref(true)
const isProgressAnimated = ref(false)
const isRefreshing = ref(false)
const isLoadingMore = ref(false)
const isSearching = ref(false)

// 通知状态
const notificationVisible = ref(false)
const notificationMessage = ref('')
const notificationType = ref<'success' | 'error' | 'info' | 'warning'>('info')

// 搜索和筛选
const searchQuery = ref('')
const selectedFilter = ref('all')
const sortType = ref('default')

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const totalCount = ref(0)

// 筛选选项
const filterOptions = ref([
  { key: 'all', label: '全部题库', count: 0 },
  { key: 'easy', label: '简单', count: 0 },
  { key: 'medium', label: '中等', count: 0 },
  { key: 'hard', label: '困难', count: 0 },
  { key: 'bookmarked', label: '已收藏', count: 0 },
  { key: 'completed', label: '已完成', count: 0 },
])

// 排序选项
const sortOptions = ref([
  { key: 'default', label: '默认排序' },
  { key: 'difficulty', label: '按难度' },
  { key: 'progress', label: '按进度' },
  { key: 'popularity', label: '按热度' },
])

// 题库数据
const allQuestionBanks = ref<QuestionBankVO[]>([])

// 统计数据
const rawStatistics = ref({
  totalBanks: 0,
  totalQuestions: 0,
  averageProgress: 0,
  bookmarkedBanks: 0,
  completedBanks: 0,
})

// 计算属性 - 统计信息
const statistics = computed(() => {
  return {
    totalBanks: rawStatistics.value.totalBanks,
    totalQuestions: rawStatistics.value.totalQuestions,
    averageProgress: rawStatistics.value.averageProgress,
    completedBanks: rawStatistics.value.completedBanks,
    bookmarkedBanks: rawStatistics.value.bookmarkedBanks,
  }
})

/**
 * @description 显示通知
 * @param message 通知内容
 * @param type 通知类型
 */
const showNotification = (
  message: string,
  type: 'success' | 'error' | 'info' | 'warning' = 'info',
): void => {
  notificationMessage.value = message
  notificationType.value = type
  notificationVisible.value = true
}

// 计算属性 - 过滤后的题库列表（用于显示）
const filteredBanks = computed(() => {
  return allQuestionBanks.value
})

/**
 * @description 加载题库列表数据
 * @param refresh 是否刷新数据
 */
const loadQuestionBanks = async (refresh = false): Promise<void> => {
  try {
    if (refresh) {
      currentPage.value = 1
      allQuestionBanks.value = []
      hasMore.value = true
    }

    if (!hasMore.value && !refresh) {
      return
    }

    isLoading.value = refresh

    // 构建查询参数
    const params: QuestionBankListParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      filter: selectedFilter.value,
      keyword: searchQuery.value || undefined,
      orderBy: sortType.value === 'default' ? undefined : sortType.value,
      orderDirection: 'desc',
    }

    const res = await getQuestionBankList({
      params,
    })
    console.log(res)

    if (res.code === 200) {
      const data = res.data

      if (refresh) {
        allQuestionBanks.value = data.list
        currentPage.value = 1
      } else {
        allQuestionBanks.value = [...allQuestionBanks.value, ...data.list]
      }

      totalCount.value = data.total
      hasMore.value =
        data.list.length === pageSize.value && allQuestionBanks.value.length < totalCount.value

      if (!refresh) {
        currentPage.value++
      }
    } else {
      showNotification(res.message || '加载失败', 'error')
    }
  } catch (error: any) {
    console.error('加载题库列表失败:', error)

    // 根据错误类型显示不同的提示
    let errorMessage = '加载失败，请稍后重试'
    if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
      errorMessage = '网络连接失败，请检查网络后重试'
    } else if (error.code === 'TIMEOUT') {
      errorMessage = '请求超时，请重试'
    }

    showNotification(errorMessage, 'error')
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 加载统计信息
 */
const loadStatistics = async (): Promise<void> => {
  try {
    const res = await getMajorQuestionBankStatistics(majorId.value)
    if (res.code === 200) {
      rawStatistics.value = res.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

/**
 * @description 加载筛选选项计数
 */
const loadFilterCounts = async (): Promise<void> => {
  try {
    const res = await getMajorQuestionBankFilterCounts(majorId.value)
    if (res.code === 200) {
      const counts = res.data
      filterOptions.value.forEach((option) => {
        if (counts[option.key] !== undefined) {
          option.count = counts[option.key]
        }
      })
    }
  } catch (error) {
    console.error('加载筛选计数失败:', error)
  }
}

/**
 * @description 搜索输入处理（实时搜索）
 * @param value 输入的搜索值
 */
let searchTimer: number | null = null
const handleSearchInput = (value: string): void => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 防抖处理，500ms后执行搜索
  searchTimer = setTimeout(() => {
    if (searchQuery.value !== value) {
      searchQuery.value = value
      refreshData()
    }
  }, 500)
}

/**
 * @description 搜索确认处理（点击搜索按钮）
 * @param value 确认搜索的值
 */
const handleSearchConfirm = async (value: string): Promise<void> => {
  if (isSearching.value) return

  isSearching.value = true

  try {
    // 清除防抖定时器
    if (searchTimer) {
      clearTimeout(searchTimer)
      searchTimer = null
    }

    searchQuery.value = value
    await refreshData()

    showNotification(value ? `搜索"${value}"完成` : '搜索完成', 'success')
  } catch (error) {
    console.error('搜索失败:', error)
    showNotification('搜索失败，请重试', 'error')
  } finally {
    isSearching.value = false
  }
}

/**
 * @description 清除搜索
 */
const clearSearch = async (): Promise<void> => {
  if (isSearching.value) return

  // 清除防抖定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
    searchTimer = null
  }

  searchQuery.value = ''
  await refreshData()

  showNotification('已清除搜索条件', 'success')
}

/**
 * @description 应用筛选
 * @param filter 筛选条件
 */
const applyFilterOption = (filter: string): void => {
  if (selectedFilter.value !== filter) {
    selectedFilter.value = filter
    refreshData()
  }
}

/**
 * @description 应用排序
 * @param sort 排序方式
 */
const applySortOption = (sort: string): void => {
  if (sortType.value !== sort) {
    sortType.value = sort
    refreshData()
  }
}

/**
 * @description 重置筛选
 */
const resetFilters = async (): Promise<void> => {
  try {
    const res = await resetMajorQuestionBankFilters(majorId.value)
    if (res.code === 200) {
      selectedFilter.value = 'all'
      sortType.value = 'default'
      searchQuery.value = ''

      allQuestionBanks.value = res.data.list
      totalCount.value = res.data.total

      showNotification('已重置筛选', 'success')

      // 重新加载相关数据
      await Promise.all([loadStatistics(), loadFilterCounts()])
    }
  } catch (error) {
    console.error('重置筛选失败:', error)
    showNotification('重置失败', 'error')
  }
}

/**
 * @description 获取难度样式
 * @param difficulty 难度等级
 * @returns CSS类名字符串
 */
const getDifficultyStyle = (difficulty: string): string => {
  const styles: Record<string, string> = {
    简单: 'difficulty-easy',
    中等: 'difficulty-medium',
    困难: 'difficulty-hard',
  }
  return styles[difficulty] || 'difficulty-default'
}

/**
 * @description 获取进度条样式
 * @param progress 进度百分比
 * @returns 样式对象
 */
const getProgressStyle = (progress: number) => {
  return {
    width: isProgressAnimated.value ? `${progress}%` : '0%',
  }
}

/**
 * @description 格式化数字
 * @param num 数字
 * @returns 格式化后的字符串
 */
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * @description 打开题库详情
 * @param bank 题库对象
 */
const openQuestionBank = (bank: QuestionBankVO): void => {
  uni.navigateTo({
    url: `/pages/learning/question-bank?id=${bank.id}&major=${majorId.value}&title=${encodeURIComponent(bank.title)}`,
    fail: (err) => {
      console.error('页面跳转失败:', err)
      showNotification('页面跳转失败', 'error')
    },
  })
}

/**
 * @description 开始练习
 * @param bank 题库对象
 */
const startPractice = (bank: QuestionBankVO): void => {
  uni.navigateTo({
    url: `/pages/learning/practice?bankId=${bank.id}&major=${majorId.value}`,
  })
}

/**
 * @description 刷新数据
 */
const refreshData = async (): Promise<void> => {
  // 重置分页状态
  currentPage.value = 1
  hasMore.value = true
  await Promise.all([loadQuestionBanks(true), loadStatistics(), loadFilterCounts()])
}

/**
 * @description 下拉刷新处理
 */
const handleRefresh = async (): Promise<void> => {
  if (isRefreshing.value) return

  isRefreshing.value = true

  // 触觉反馈
  // #ifdef APP-PLUS || MP-WEIXIN
  uni.vibrateShort({
    type: 'light',
  })
  // #endif

  try {
    await refreshData()
    showNotification('刷新成功', 'success')
  } catch (error) {
    console.error('刷新失败:', error)
    showNotification('刷新失败', 'error')
  } finally {
    isRefreshing.value = false
  }
}

/**
 * @description 停止下拉刷新
 */
const handleRefreshRestore = (): void => {
  isRefreshing.value = false
}

/**
 * @description 触底加载更多（带节流）
 */
let scrollTimer: number | null = null
const handleScrollToLower = (): void => {
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  scrollTimer = setTimeout(() => {
    if (!isLoadingMore.value && hasMore.value && !isLoading.value) {
      loadMore()
    }
  }, 200)
}

/**
 * @description 加载更多数据
 */
const loadMore = async (): Promise<void> => {
  if (isLoadingMore.value || !hasMore.value) return

  isLoadingMore.value = true
  try {
    await loadQuestionBanks(false)
  } catch (error) {
    console.error('加载更多失败:', error)
    showNotification('加载失败', 'error')
  } finally {
    isLoadingMore.value = false
  }
}

/**
 * @description 页面初始化
 */
const initPage = async (): Promise<void> => {
  try {
    // 加载数据
    await refreshData()

    // 启动进度条动画
    setTimeout(() => {
      isProgressAnimated.value = true
    }, 300)
  } catch (error) {
    console.error('页面初始化失败:', error)
    showNotification('页面加载失败', 'error')
  } finally {
    // 确保加载状态被重置
    setTimeout(() => {
      isLoading.value = false
    }, 300)
  }
}

// 页面加载时获取参数
onLoad((options) => {
  majorId.value = options.major || ''
  majorName.value = decodeURIComponent(options.majorName || '全部专业')
  if (majorId.value === '') {
    uni.navigateTo({
      url: '/pages/404/404',
    })
  }
})

// 页面挂载时初始化
onMounted(() => {
  initPage()
})
</script>

<template>
  <view class="page-container">
    <HeadBar :title="`${majorName || '全部'}题库`" />

    <!-- 主要内容区域 -->
    <scroll-view
      scroll-y
      class="main-content"
      :style="{ height: 'calc(100vh - 56px)' }"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
      @refresherrestore="handleRefreshRestore"
      @scrolltolower="handleScrollToLower"
      :lower-threshold="100"
    >
      <view class="content-wrapper">
        <!-- 页面头部 -->
        <view class="header-section">
          <view class="title-section">
            <text class="main-title">{{ majorName || '全部' }}题库</text>
            <text class="sub-title">
              共 {{ statistics.totalBanks }} 个题库，{{ statistics.totalQuestions }} 道题目
            </text>
          </view>

          <!-- 统计卡片 -->
          <view class="stats-cards">
            <view class="stat-card">
              <text class="stat-number">{{ statistics.averageProgress }}%</text>
              <text class="stat-label">平均进度</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ statistics.bookmarkedBanks }}</text>
              <text class="stat-label">已收藏</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ statistics.completedBanks }}</text>
              <text class="stat-label">已完成</text>
            </view>
          </view>

          <!-- 搜索框 -->
          <SearchBox
            v-model="searchQuery"
            placeholder="搜索题库、题目或知识点..."
            search-button-text="搜索"
            :show-search-button="true"
            :show-clear-button="true"
            :loading="isSearching"
            @search="handleSearchConfirm"
            @input="handleSearchInput"
            @clear="clearSearch"
          />
        </view>

        <!-- 筛选和排序 -->
        <view class="filter-section">
          <view class="filter-header">
            <text class="filter-title">筛选排序</text>
            <view class="reset-btn" @click="resetFilters">重置</view>
          </view>

          <!-- 筛选标签 -->
          <scroll-view show-scrollbar="false" scroll-x class="filter-scroll">
            <view class="filter-tags">
              <view
                v-for="option in filterOptions"
                :key="option.key"
                class="filter-tag"
                :class="{ 'filter-tag-active': selectedFilter === option.key }"
                @click="applyFilterOption(option.key)"
              >
                {{ option.label }}
                <text class="filter-count">({{ option.count }})</text>
              </view>
            </view>
          </scroll-view>

          <!-- 排序选项 -->
          <scroll-view show-scrollbar="false" scroll-x class="sort-scroll">
            <view class="sort-tags">
              <view
                v-for="option in sortOptions"
                :key="option.key"
                class="sort-tag"
                :class="{ 'sort-tag-active': sortType === option.key }"
                @click="applySortOption(option.key)"
              >
                {{ option.label }}
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 题库列表 -->
        <view class="banks-section">
          <view v-if="isLoading" class="loading-container">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>

          <view v-else-if="filteredBanks.length === 0" class="empty-container">
            <view class="empty-icon">
              <text class="i-fa-solid-book"></text>
            </view>
            <text class="empty-title">暂无题库</text>
            <text class="empty-desc">试试调整筛选条件或搜索关键词</text>
          </view>

          <view v-else class="banks-grid">
            <view
              v-for="(bank, index) in filteredBanks"
              :key="bank.id"
              class="bank-card"
              :style="{ animationDelay: `${index * 0.1}s` }"
              @click="openQuestionBank(bank)"
            >
              <!-- 题库信息 -->
              <view class="bank-content">
                <text class="bank-title">{{ bank.title }}</text>
                <text class="bank-description">{{ bank.description }}</text>

                <!-- 标签 -->
                <view class="bank-tags">
                  <view class="difficulty-tag" :class="getDifficultyStyle(bank.difficulty)">
                    {{ bank.difficulty }}
                  </view>
                </view>
                <!-- 统计信息 -->
                <view class="bank-stats">
                  <view class="stat-item">
                    <text class="stat-value">{{ bank.totalQuestions || 100 }}</text>
                    <text class="stat-label">题目</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-value">{{ formatNumber(bank.practiceCount || 100) }}</text>
                    <text class="stat-label">练习</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-value">{{ bank.progress || 50 }}%</text>
                    <text class="stat-label">进度</text>
                  </view>
                </view>

                <!-- 进度条 -->
                <view class="progress-container">
                  <view class="progress-bar">
                    <view
                      class="progress-fill"
                      :style="getProgressStyle(bank.progress || 50)"
                    ></view>
                  </view>
                  <text class="progress-text">{{ bank.progress || 50 }}%</text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="bank-actions">
                <view class="action-btn secondary" @click.stop="openQuestionBank(bank)">
                  查看详情
                </view>
                <view class="action-btn primary" @click.stop="startPractice(bank)">开始练习</view>
              </view>
            </view>

            <!-- 加载更多提示 -->
            <view v-if="hasMore && !isLoading" class="load-more-container">
              <view v-if="isLoadingMore" class="load-more-loading">
                <view class="loading-spinner-small"></view>
                <text class="load-more-text">加载中...</text>
              </view>
              <view v-else class="load-more-hint">
                <text class="load-more-text">上拉加载更多</text>
              </view>
            </view>

            <!-- 没有更多数据提示 -->
            <view v-if="!hasMore && filteredBanks.length > 0" class="no-more-container">
              <text class="no-more-text">已加载全部数据</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 通知组件 -->
    <Notification
      :visible="notificationVisible"
      :message="notificationMessage"
      :type="notificationType"
      :duration="3000"
      position="top"
      :closable="true"
      @update:visible="notificationVisible = $event"
    />
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  padding: 0;
}

.content-wrapper {
  padding: 20rpx;
}

/* 头部区域 */
.header-section {
  margin-bottom: 30rpx;
}

.title-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.main-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 10rpx;
}

.sub-title {
  display: block;
  font-size: 28rpx;
  color: #64748b;
}

/* 统计卡片 */
.stats-cards {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  flex: 1;
  margin: 0 10rpx;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #00c9a7;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #64748b;
}

/* 搜索框区域 */
.search-box-container {
  margin-bottom: 20rpx;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 30rpx;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.reset-btn {
  font-size: 28rpx;
  color: #00c9a7;
  padding: 10rpx 20rpx;
  border: 2rpx solid #00c9a7;
  border-radius: 20rpx;
  background: white;
}

.filter-scroll,
.sort-scroll {
  margin-bottom: 20rpx;
}

.filter-tags,
.sort-tags {
  display: flex;
  white-space: nowrap;
  padding: 0 10rpx;
}

.filter-tag,
.sort-tag {
  background: white;
  border: 2rpx solid #e2e8f0;
  border-radius: 24rpx;
  padding: 15rpx 25rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #64748b;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tag-active,
.sort-tag-active {
  background: #00c9a7;
  border-color: #00c9a7;
  color: white;
}

.filter-count {
  margin-left: 8rpx;
  font-size: 22rpx;
  opacity: 0.8;
}

/* 题库列表 */
.banks-section {
  margin-bottom: 40rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #00c9a7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #64748b;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  margin-bottom: 30rpx;

  .i-fa-solid-book {
    font-size: 120rpx;
    color: #cbd5e1;
  }
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #64748b;
}

.banks-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.bank-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bank-card:active {
  transform: scale(0.98);
}

/* 题库头部 */
.bank-header {
  height: 120rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background: #00c9a7;
}

.color-blue,
.color-green,
.color-purple,
.color-indigo,
.color-red,
.color-orange {
  background: #00c9a7;
}

.bank-icon-container {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bank-icon {
  font-size: 40rpx;
  color: white;
}

.bank-bookmark {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bookmark-icon {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;

  &.bookmarked {
    color: #fbbf24;
    transform: scale(1.1);
  }
}

/* 题库内容 */
.bank-content {
  padding: 30rpx;
}

.bank-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 15rpx;
}

.bank-description {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.bank-tags {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 25rpx;
}

.difficulty-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.difficulty-easy {
  background: #dcfce7;
  color: #16a34a;
}

.difficulty-medium {
  background: #fef3c7;
  color: #d97706;
}

.difficulty-hard {
  background: #fecaca;
  color: #dc2626;
}

.difficulty-default {
  background: #f1f5f9;
  color: #64748b;
}

.bank-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 5rpx;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #64748b;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f1f5f9;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #00c9a7;
  border-radius: 4rpx;
  transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.progress-text {
  font-size: 24rpx;
  color: #64748b;
  min-width: 60rpx;
}

/* 操作按钮 */
.bank-actions {
  display: flex;
  padding: 0 30rpx 30rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f8fafc;
  color: #64748b;
  border: 2rpx solid #e2e8f0;
}

.action-btn.primary {
  background: #00c9a7;
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 加载更多提示 */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.load-more-loading {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e2e8f0;
  border-top: 3rpx solid #00c9a7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-hint {
  display: flex;
  justify-content: center;
}

.load-more-text {
  font-size: 26rpx;
  color: #64748b;
}

.no-more-container {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #94a3b8;
  position: relative;
}

.no-more-text::before,
.no-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1rpx;
  background: #e2e8f0;
}

.no-more-text::before {
  left: -80rpx;
}

.no-more-text::after {
  right: -80rpx;
}
</style>
