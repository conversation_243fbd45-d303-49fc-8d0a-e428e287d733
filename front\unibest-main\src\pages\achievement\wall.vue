<template>
  <view class="achievement-wall-page">
    <!-- 导航栏 -->
    <HeadBar
      title="成就徽章墙"
      :show-back="true"
      :show-right-button="true"
      right-icon="i-fa-solid-share-alt"
      @back-click="goBack"
      @right-click="shareAchievements"
    />

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载徽章数据...</text>
      </view>
    </view>

    <!-- 主内容区域 -->
    <view v-else class="content-area">
      <!-- 徽章管理器 -->
      <view class="badge-manager-section">
        <BadgeManager
          ref="badgeManagerRef"
          :show-pin-controls="true"
          :columns-per-row="3"
          :show-category-filter="true"
          :show-stats="true"
          @badge-click="onBadgeClick"
          @pin-changed="onPinChanged"
        />
      </view>

      <!-- 提示信息 -->
      <view class="tips-section">
        <view class="tips-card">
          <text class="tips-title">
            <text class="i-fa-solid-lightbulb tips-icon"></text>
            使用提示
          </text>
          <view class="tips-content">
            <text class="tip-item">• 点击徽章查看详细信息</text>
            <text class="tip-item">• 长按徽章可设置置顶，最多4个</text>
            <text class="tip-item">• 置顶徽章将在个人中心优先显示</text>
            <text class="tip-item">• 继续使用APP解锁更多成就</text>
          </view>
        </view>
      </view>

      <!-- 底部空白区域，防止内容被遮挡 -->
      <view class="bottom-spacing"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getAllBadges, refreshBadges, type Badge } from '../../service/achievement'
import HeadBar from '@/components/HeadBar.vue'
import BadgeManager from '@/components/BadgeManager.vue'

/**
 * @description 成就徽章墙页面
 * <AUTHOR>
 */

// 页面状态
const isLoading = ref(true)
const badgeManagerRef = ref<any>(null)
const isRefreshing = ref(false)

/**
 * @description 处理徽章点击事件
 * @param badge 点击的徽章
 */
const onBadgeClick = (badge: Badge) => {
  // 显示徽章详情弹窗
  const modalContent = badge.unlocked
    ? `${badge.desc}\n\n解锁时间: ${badge.unlockedAt}\n稀有度: ${getRarityLabel(badge.rarity)}`
    : `${badge.desc}\n\n该徽章尚未解锁，继续努力吧！`

  uni.showModal({
    title: badge.title,
    content: modalContent,
    showCancel: false,
    confirmText: '我知道了',
    confirmColor: '#00c9a7',
  })
}

/**
 * @description 处理置顶状态变更
 * @param badgeId 徽章ID
 * @param isPinned 是否置顶
 */
const onPinChanged = (badgeId: string, isPinned: boolean) => {
  console.log(`徽章 ${badgeId} 置顶状态变更为:`, isPinned)
}

/**
 * @description 获取稀有度标签
 * @param rarity 稀有度
 */
const getRarityLabel = (rarity: Badge['rarity']): string => {
  const rarityMap = {
    common: '普通',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
  }
  return rarityMap[rarity || 'common']
}

/**
 * @description 返回上一页
 */
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      // 如果无法返回，则跳转到个人中心
      uni.switchTab({
        url: '/pages/user/center',
      })
    },
  })
}

/**
 * @description 分享徽章成就
 */
const shareAchievements = () => {
  const allBadges = getAllBadges()
  const unlockedCount = allBadges.filter((badge) => badge.unlocked).length
  const totalCount = allBadges.length
  const progress = Math.round((unlockedCount / totalCount) * 100)

  // 模拟分享功能
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      const shareText = `我在面试助手中已获得${unlockedCount}个成就徽章，完成度${progress}%！快来挑战吧！`

      switch (res.tapIndex) {
        case 0:
        case 1:
          uni.showToast({
            title: '分享功能开发中',
            icon: 'none',
          })
          break
        case 2:
          // #ifdef H5
          if (navigator.clipboard) {
            navigator.clipboard.writeText(shareText)
            uni.showToast({
              title: '已复制到剪贴板',
              icon: 'success',
            })
          } else {
            uni.showToast({
              title: '复制失败',
              icon: 'error',
            })
          }
          // #endif
          // #ifndef H5
          uni.setClipboardData({
            data: shareText,
            success: () => {
              uni.showToast({
                title: '已复制到剪贴板',
                icon: 'success',
              })
            },
          })
          // #endif
          break
      }
    },
  })
}

/**
 * @description 刷新徽章数据
 */
const refreshData = async () => {
  try {
    isRefreshing.value = true

    // 调用API刷新徽章数据
    const success = await refreshBadges()

    if (success) {
      uni.showToast({
        title: '刷新成功',
        icon: 'success',
      })

      // 刷新徽章管理器数据
      if (badgeManagerRef.value) {
        badgeManagerRef.value.loadData()
      }
    } else {
      uni.showToast({
        title: '刷新失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
    })
  } finally {
    isRefreshing.value = false
  }
}

/**
 * @description 页面初始化
 */
const initPage = async () => {
  try {
    isLoading.value = true

    // 刷新徽章数据（从服务器获取或使用缓存）
    await refreshBadges()

    // 刷新徽章管理器数据
    if (badgeManagerRef.value) {
      badgeManagerRef.value.loadData()
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  } finally {
    isLoading.value = false
  }
}

// 页面生命周期
onMounted(() => {
  initPage()
})

// 监听页面显示，当从其他页面返回时刷新数据
// #ifdef APP-PLUS || H5
const onShow = () => {
  if (badgeManagerRef.value) {
    badgeManagerRef.value.loadPinnedIds()
  }
}
// #endif

// 导出页面方法供uni-app调用
defineExpose({
  onShow,
})
</script>

<style lang="scss" scoped>
.achievement-wall-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7ff 0%, #e8f4f8 50%, #f0f8ff 100%);
  overflow-x: hidden;

  // 添加装饰背景元素
  &::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 25% 25%, rgba(0, 201, 167, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(79, 209, 199, 0.06) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
    animation: backgroundShift 25s ease-in-out infinite;
  }

  // 装饰圆圈
  &::after {
    content: '';
    position: fixed;
    top: 30%;
    right: -15%;
    width: 400rpx;
    height: 400rpx;
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.08), rgba(79, 209, 199, 0.04));
    border-radius: 50%;
    backdrop-filter: blur(30rpx);
    -webkit-backdrop-filter: blur(30rpx);
    animation: floating 10s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 24rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #00c9a7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

// 内容区域
.content-area {
  position: relative;
  z-index: 1;
  padding: 32rpx 20rpx;
}

// 页面标题区域
.page-header {
  position: relative;
  margin: 40rpx 0 48rpx 0;
  padding: 48rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(25rpx) saturate(180%);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.12);
  text-align: center;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 2;
}

.page-title {
  display: block;
  margin-bottom: 16rpx;
  font-size: 44rpx;
  font-weight: bold;
  color: #1a202c;
}

.title-icon {
  margin-right: 16rpx;
  font-size: 40rpx;
  color: #00c9a7;
}

.page-subtitle {
  font-size: 28rpx;
  color: #718096;
  line-height: 1.5;
}

.header-decoration {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
}

.decoration-1 {
  top: 20rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1), rgba(79, 209, 199, 0.05));
  animation: floating 6s ease-in-out infinite;
}

.decoration-2 {
  bottom: 30rpx;
  left: 60rpx;
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, rgba(79, 209, 199, 0.08), rgba(0, 201, 167, 0.04));
  animation: floating 8s ease-in-out infinite reverse;
}

.decoration-3 {
  top: 50%;
  left: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.06), rgba(79, 209, 199, 0.03));
  animation: floating 7s ease-in-out infinite;
}

// 徽章管理器区域
.badge-manager-section {
  position: relative;
  margin-bottom: 32rpx;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(25rpx) saturate(180%);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.12);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -20%;
    right: -20%;
    width: 200rpx;
    height: 200rpx;
    background: radial-gradient(circle, rgba(0, 201, 167, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 8s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

// 提示信息区域
.tips-section {
  margin-bottom: 32rpx;
}

.tips-card {
  padding: 24rpx;
  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(251, 191, 36, 0.2);
  box-shadow: 0 6rpx 20rpx rgba(251, 191, 36, 0.1);
}

.tips-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #92400e;
}

.tips-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #f59e0b;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #b45309;
  line-height: 1.5;
}

// 底部空白区域
.bottom-spacing {
  width: 100%;
  height: 100rpx;
  background: transparent;
}

// 动画定义
@keyframes backgroundShift {
  0%,
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(-2%) translateY(-1%) rotate(1deg);
  }
  50% {
    transform: translateX(2%) translateY(1%) rotate(-1deg);
  }
  75% {
    transform: translateX(-1%) translateY(2%) rotate(0.5deg);
  }
}

@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(5deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式适配
@media (min-width: 768px) {
  .content-area {
    max-width: 1200rpx;
    margin: 0 auto;
  }

  .page-header {
    padding: 64rpx 48rpx;
  }

  .badge-manager-section {
    padding: 64rpx 48rpx;
  }
}
</style>
