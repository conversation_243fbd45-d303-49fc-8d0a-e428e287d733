/**
 * 讯飞数字人API服务
 * <AUTHOR>
 * @date 2025-07-20
 */

import { http } from '@/utils/http'

// 数字人启动请求参数
export interface AvatarStartRequest {
  avatarId?: string
  sceneId?: string
  vcn?: string
  width?: number
  height?: number
  background?: string
  enableAlpha?: boolean
  protocol?: string
  fps?: number
  bitrate?: number
  timeout?: number
}

// 数字人文本请求参数
export interface AvatarTextRequest {
  text: string
  vcn?: string
  speed?: number
  pitch?: number
  volume?: number
}

// 数字人会话信息
export interface AvatarSession {
  sessionId: string
  avatarId: string
  sceneId: string
  vcn: string
  width: number
  height: number
  streamUrl?: string
  connected: boolean
  createTime: string
  lastActiveTime: string
  statusDesc: string
}

// API响应格式 - 与后端R<T>类型对应
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 与前端http工具返回类型对应
export interface IResData<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 启动数字人会话
 */
export const startAvatarSession = (params: AvatarStartRequest): Promise<IResData<AvatarSession>> => {
  return http<AvatarSession>({
    url: '/app/avatar/start',
    method: 'POST',
    data: params,
  })
}

/**
 * 发送文本驱动
 */
export const sendTextDriver = (sessionId: string, params: AvatarTextRequest): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/text-driver/${sessionId}`,
    method: 'POST',
    data: params,
  })
}

/**
 * 发送文本交互
 */
export const sendTextInteract = (sessionId: string, params: AvatarTextRequest): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/text-interact/${sessionId}`,
    method: 'POST',
    data: params,
  })
}

/**
 * 发送音频驱动
 */
export const sendAudioDriver = (sessionId: string, audioData: string, status: number): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/audio-driver/${sessionId}`,
    method: 'POST',
    data: {
      audioData,
      status,
    },
  })
}

/**
 * 重置数字人
 */
export const resetAvatar = (sessionId: string): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/reset/${sessionId}`,
    method: 'POST',
  })
}

/**
 * 停止数字人会话
 */
export const stopAvatarSession = (sessionId: string): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/stop/${sessionId}`,
    method: 'POST',
  })
}

/**
 * 发送心跳
 */
export const sendHeartbeat = (sessionId: string): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/heartbeat/${sessionId}`,
    method: 'POST',
  })
}

/**
 * 发送动作指令
 */
export const sendAction = (sessionId: string, actionType: string, actionValue: string): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/action/${sessionId}`,
    method: 'POST',
    data: {
      actionType,
      actionValue,
    },
  })
}

/**
 * 获取会话信息
 */
export const getSessionInfo = (sessionId: string): Promise<IResData<AvatarSession>> => {
  return http<AvatarSession>({
    url: `/app/avatar/session/${sessionId}`,
    method: 'GET',
  })
}

/**
 * 检查会话状态
 */
export const checkSessionStatus = (sessionId: string): Promise<IResData<boolean>> => {
  return http<boolean>({
    url: `/app/avatar/status/${sessionId}`,
    method: 'GET',
  })
}

/**
 * 获取推流地址
 */
export const getStreamUrl = (sessionId: string): Promise<IResData<string>> => {
  return http<string>({
    url: `/app/avatar/stream-url/${sessionId}`,
    method: 'GET',
  })
}

/**
 * 更新推流地址
 */
export const updateStreamUrl = (sessionId: string): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/update-stream-url/${sessionId}`,
    method: 'POST',
  })
}

/**
 * 获取推流状态
 */
export const getStreamStatus = (sessionId: string): Promise<IResData<any>> => {
  return http<any>({
    url: `/app/avatar/stream-status/${sessionId}`,
    method: 'GET',
  })
}

// 测试接口
/**
 * 测试配置信息
 */
export const testConfig = (): Promise<IResData<any>> => {
  return http<any>({
    url: '/app/avatar/test/config',
    method: 'GET',
  })
}

/**
 * 快速测试启动
 */
export const quickStart = (): Promise<IResData<AvatarSession>> => {
  return http<AvatarSession>({
    url: '/app/avatar/test/quick-start',
    method: 'POST',
  })
}

/**
 * 快速测试文本
 */
export const quickText = (sessionId: string, text?: string): Promise<IResData<void>> => {
  return http<void>({
    url: `/app/avatar/test/quick-text/${sessionId}`,
    method: 'POST',
    data: { text },
  })
}

/**
 * 完整测试流程
 */
export const fullTest = (): Promise<IResData<string>> => {
  return http<string>({
    url: '/app/avatar/test/full-test',
    method: 'POST',
  })
}

/**
 * 调试WebSocket消息
 */
export const debugWebSocket = (): Promise<IResData<string>> => {
  return http<string>({
    url: '/app/avatar/test/debug-websocket',
    method: 'POST',
  })
}
