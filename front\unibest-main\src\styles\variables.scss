// ==================== 智能面试系统 - 设计系统变量 ====================

// ==================== 颜色系统 ====================
// 主色调 - 基于index.vue的青绿色系
$primary-color: #00c9a7;
$primary-color-light: #00b294;
$primary-color-dark: #00b39a;
$primary-gradient: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);

// 辅助色彩
$secondary-color: #3b82f6;
$accent-color: #ff9800;
$success-color: #4caf50;
$warning-color: #ffc107;
$error-color: #ff4d4f;
$info-color: #2196f3;

// 中性色彩
$text-primary: #222;
$text-secondary: #666;
$text-tertiary: #999;
$text-disabled: #ccc;
$text-inverse: #fff;

// 背景色彩
$bg-primary: #fff;
$bg-secondary: #f8fafc;
$bg-tertiary: #f1f5f9;
$bg-gradient: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);

// 边框色彩
$border-light: #f0f0f0;
$border-medium: #e0e0e0;
$border-dark: #d0d0d0;

// ==================== 字体系统 ====================
// 字体大小 (rpx单位，适配uni-app)
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-2xl: 40rpx;
$font-size-3xl: 48rpx;

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.6;

// ==================== 间距系统 ====================
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-base: 16rpx;
$spacing-md: 20rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-2xl: 40rpx;
$spacing-3xl: 48rpx;
$spacing-4xl: 64rpx;

// ==================== 圆角系统 ====================
$radius-xs: 4rpx;
$radius-sm: 8rpx;
$radius-base: 12rpx;
$radius-md: 16rpx;
$radius-lg: 20rpx;
$radius-xl: 24rpx;
$radius-2xl: 32rpx;
$radius-full: 50%;

// ==================== 阴影系统 ====================
$shadow-xs: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
$shadow-sm: 0 4rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
$shadow-md: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
$shadow-lg: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-xl: 0 16rpx 40rpx rgba(0, 0, 0, 0.15);

// 特殊阴影 - 主色调阴影
$shadow-primary: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
$shadow-primary-lg: 0 12rpx 32rpx rgba(0, 201, 167, 0.35);

// ==================== 层级系统 ====================
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// ==================== 动画系统 ====================
$transition-fast: 0.15s ease-out;
$transition-base: 0.3s ease-out;
$transition-slow: 0.5s ease-out;

// 缓动函数
$ease-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
$ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);
$ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);

// ==================== 断点系统 ====================
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// ==================== 组件特定变量 ====================
// 按钮
$btn-height-sm: 64rpx;
$btn-height-base: 80rpx;
$btn-height-lg: 96rpx;
$btn-padding-sm: 16rpx 24rpx;
$btn-padding-base: 20rpx 32rpx;
$btn-padding-lg: 24rpx 40rpx;

// 卡片
$card-padding: 32rpx;
$card-radius: $radius-xl;
$card-shadow: $shadow-base;

// 导航栏
$navbar-height: 120rpx;
$navbar-padding: 32rpx 40rpx;

// 底部导航
$tabbar-height: 120rpx;
$tabbar-padding: 16rpx;

// ==================== 状态颜色 ====================
// 成功状态
$success-bg: rgba(76, 175, 80, 0.1);
$success-border: rgba(76, 175, 80, 0.3);

// 警告状态
$warning-bg: rgba(255, 193, 7, 0.1);
$warning-border: rgba(255, 193, 7, 0.3);

// 错误状态
$error-bg: rgba(255, 77, 79, 0.1);
$error-border: rgba(255, 77, 79, 0.3);

// 信息状态
$info-bg: rgba(33, 150, 243, 0.1);
$info-border: rgba(33, 150, 243, 0.3);

// ==================== 特殊效果 ====================
// 玻璃形态效果
$glass-bg: rgba(255, 255, 255, 0.1);
$glass-border: rgba(255, 255, 255, 0.2);
$glass-backdrop: blur(20px);

// 渐变背景
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $primary-color-light 100%);
$gradient-secondary: linear-gradient(135deg, $secondary-color 0%, #1e40af 100%);
$gradient-bg: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);

// ==================== 智能面试系统专用色彩 ====================
// 能力评估相关
$ability-excellent: #4caf50;
$ability-good: #00c9a7;
$ability-average: #ffc107;
$ability-poor: #ff9800;
$ability-critical: #ff4d4f;

// 面试状态相关
$interview-active: #00c9a7;
$interview-pending: #ffc107;
$interview-completed: #4caf50;
$interview-failed: #ff4d4f;

// 数据可视化色板
$chart-colors: (#00c9a7, #4caf50, #ffc107, #ff9800, #9c27b0, #2196f3, #ff4d4f, #607d8b);
