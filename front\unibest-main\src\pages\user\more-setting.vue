<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
import { logout as logoutApi } from '../../service/auth'

/**
 * @description 账户管理菜单
 */
const accountMenus = ref([
  {
    icon: 'person',
    color: 'menu-blue',
    title: '个人信息',
    desc: '修改个人资料和头像',
    url: '/pages/user/profile',
  },
  {
    icon: 'calendar',
    color: 'menu-green',
    title: '学习计划',
    desc: '制定专属学习计划',
    url: '/pages/learning/plan',
  },
  {
    icon: 'file-text',
    color: 'menu-purple',
    title: '简历管理',
    desc: '上传和管理个人简历',
    url: '/pages/user/resume',
  },
  {
    icon: 'settings',
    color: 'menu-orange',
    title: '偏好设置',
    desc: '面试难度和类型偏好',
    url: '/pages/user/preference-settings',
  },
])

/**
 * @description 应用设置菜单
 */
const appMenus = ref([
  {
    icon: 'shield',
    color: 'menu-gray',
    title: '隐私与安全',
    desc: '数据隐私和安全设置',
    url: '/pages/user/privacy',
  },
  {
    icon: 'info',
    color: 'menu-indigo',
    title: '关于我们',
    desc: '版本信息和用户协议',
    url: '/pages/about/index',
  },
  {
    icon: 'chat',
    color: 'menu-yellow',
    title: '意见反馈',
    desc: '帮助我们改进产品',
    url: '/pages/user/feedback',
  },
])

/**
 * @description 跳转页面
 */
const goToPage = (url: string) => {
  if (url) {
    uni.navigateTo({
      url,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        uni.showToast({
          title: '页面暂未开放',
          icon: 'none',
        })
      },
    })
  }
}

/**
 * @description 退出登录
 */
const logout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 显示加载提示
          uni.showLoading({
            title: '退出中...',
            mask: true,
          })

          // 调用后端退出登录接口
          await logoutApi()

          // 隐藏加载提示
          uni.hideLoading()

          // 清理本地存储的用户信息
          uni.removeStorageSync('userInfo')
          uni.removeStorageSync('token')
          uni.removeStorageSync('refreshToken')

          // 显示成功提示
          uni.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 1500,
          })

          // 延迟跳转到登录页面
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/auth/login',
            })
          }, 1500)
        } catch (error) {
          // 隐藏加载提示
          uni.hideLoading()

          console.error('退出登录失败:', error)

          // 即使接口调用失败，也清理本地数据并跳转
          uni.removeStorageSync('userInfo')
          uni.removeStorageSync('token')
          uni.removeStorageSync('refreshToken')

          uni.showToast({
            title: '退出登录失败，但已清理本地数据',
            icon: 'none',
            duration: 2000,
          })

          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/auth/login',
            })
          }, 2000)
        }
      }
    },
  })
}

onLoad(() => {
  // 预加载用户中心页面
  uni.preloadPage({
    url: '/pages/user/center',
  })

  // 预加载账户管理相关页面
  uni.preloadPage({
    url: '/pages/user/profile', // 个人信息
  })
  uni.preloadPage({
    url: '/pages/learning/plan', // 学习计划
  })
  uni.preloadPage({
    url: '/pages/user/resume', // 简历管理
  })
  uni.preloadPage({
    url: '/pages/user/preference-settings', // 偏好设置
  })

  // 预加载应用设置相关页面
  uni.preloadPage({
    url: '/pages/user/privacy', // 隐私与安全
  })
  uni.preloadPage({
    url: '/pages/about/index', // 关于我们
  })
  uni.preloadPage({
    url: '/pages/user/feedback', // 意见反馈
  })

  // 预加载登录页面（退出登录时可能跳转）
  uni.preloadPage({
    url: '/pages/auth/login', // 登录页面
  })
})
</script>

<template>
  <view class="setting-page">
    <!-- 顶部状态栏 -->
    <HeadBar title="更多设置" :show-back="true" />
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 账户管理 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-user-cog section-icon"></text>
            账户管理
          </text>
          <text class="section-subtitle">管理你的个人信息和设置</text>
        </view>
        <view class="menu-list">
          <view
            class="menu-item"
            v-for="(item, idx) in accountMenus"
            :key="idx"
            @click="goToPage(item.url)"
          >
            <view class="menu-icon" :class="item.color">
              <text
                :class="
                  item.icon === 'person'
                    ? 'i-fa-solid-user'
                    : item.icon === 'calendar'
                      ? 'i-fa-solid-calendar-alt'
                      : item.icon === 'file-text'
                        ? 'i-fa-solid-file-alt'
                        : item.icon === 'settings'
                          ? 'i-fa-solid-cog'
                          : ''
                "
              ></text>
            </view>
            <view class="menu-info">
              <text class="menu-title">{{ item.title }}</text>
              <text class="menu-desc">{{ item.desc }}</text>
            </view>
            <text class="i-fa-solid-chevron-right menu-arrow"></text>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-mobile-alt section-icon"></text>
            应用设置
          </text>
          <text class="section-subtitle">自定义应用行为和偏好</text>
        </view>
        <view class="menu-list">
          <view
            class="menu-item"
            v-for="(item, idx) in appMenus"
            :key="idx"
            @click="goToPage(item.url)"
          >
            <view class="menu-icon" :class="item.color">
              <text
                :class="
                  item.icon === 'bell'
                    ? 'i-fa-solid-bell'
                    : item.icon === 'shield'
                      ? 'i-fa-solid-shield-alt'
                      : item.icon === 'info'
                        ? 'i-fa-solid-info-circle'
                        : item.icon === 'chat'
                          ? 'i-fa-solid-comment-dots'
                          : ''
                "
              ></text>
            </view>
            <view class="menu-info">
              <text class="menu-title">{{ item.title }}</text>
              <text class="menu-desc">{{ item.desc }}</text>
            </view>
            <view class="menu-action">
              <text class="i-fa-solid-chevron-right menu-arrow"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-btn" @click="logout">
        <text class="i-fa-solid-sign-out-alt logout-icon"></text>
        <text>退出登录</text>
      </view>

      <!-- 底部空白区域 -->
      <view class="bottom-spacing"></view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 页面容器 - 与个人中心统一的毛玻璃风格
.setting-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  background: linear-gradient(135deg, #f5f7ff 0%, #e8f4f8 50%, #f0f8ff 100%);

  // 装饰背景元素
  &::before {
    position: fixed;
    top: -50%;
    left: -50%;
    z-index: 0;
    width: 200%;
    height: 200%;
    pointer-events: none;
    content: '';
    background: radial-gradient(circle at 30% 20%, rgba(0, 201, 167, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 80%, rgba(79, 209, 199, 0.08) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
  }

  // 装饰圆圈
  &::after {
    position: fixed;
    top: 20%;
    right: -10%;
    z-index: 0;
    width: 300rpx;
    height: 300rpx;
    pointer-events: none;
    content: '';
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.1), rgba(79, 209, 199, 0.05));
    -webkit-backdrop-filter: blur(20rpx);
    backdrop-filter: blur(20rpx);
    border-radius: 50%;
    animation: floating 8s ease-in-out infinite;
  }
}

// 内容区域
.content-area {
  position: relative;
  z-index: 1;
  width: 100%;
  /* #ifndef H5 */
  padding-top: 32rpx; // 非H5端的顶部间距
  /* #endif */
}

// 用户信息卡片 - 采用ProfileCard的风格
.user-card {
  position: relative;
  padding: 48rpx 32rpx 32rpx 32rpx;
  margin: 20rpx 0 32rpx 0;
  overflow: hidden;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.2);
}

.user-bg-deco {
  position: absolute;
  background: #fff;
  border-radius: 50%;
  opacity: 0.12;
}

.deco1 {
  top: 24rpx;
  right: 48rpx;
  width: 120rpx;
  height: 120rpx;
  animation: floating 5s ease-in-out infinite;
}

.deco2 {
  bottom: 24rpx;
  left: 96rpx;
  width: 72rpx;
  height: 72rpx;
  animation: floating 4s ease-in-out infinite reverse;
}

.user-content {
  position: relative;
  z-index: 1;
}

.user-profile {
  display: flex;
  align-items: center;
}

.avatar-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  margin-right: 32rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(6rpx);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s;

  &:active {
    transform: scale(0.95);
  }
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
}

.user-name {
  display: block;
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}

.user-major {
  display: block;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  opacity: 0.92;
}

.user-level {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
}

// 通用section样式 - 与个人中心统一
.section {
  position: relative;
  padding: 48rpx 32rpx 32rpx 32rpx;
  margin: 20rpx 0 32rpx 0;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  backdrop-filter: blur(25rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.12);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  // 装饰圆圈
  &::before {
    position: absolute;
    top: 24rpx;
    right: 48rpx;
    z-index: 1;
    width: 80rpx;
    height: 80rpx;
    pointer-events: none;
    content: '';
    background: rgba(0, 201, 167, 0.08);
    border-radius: 50%;
    animation: floating 5s ease-in-out infinite;
  }

  &::after {
    position: absolute;
    bottom: 24rpx;
    left: 96rpx;
    z-index: 1;
    width: 48rpx;
    height: 48rpx;
    pointer-events: none;
    content: '';
    background: rgba(79, 209, 199, 0.06);
    border-radius: 50%;
    animation: floating 4s ease-in-out infinite reverse;
  }

  // 悬停效果（H5端）
  @media (hover: hover) {
    &:hover {
      background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
      -webkit-backdrop-filter: blur(30rpx) saturate(200%);
      backdrop-filter: blur(30rpx) saturate(200%);
      box-shadow: 0 24rpx 60rpx rgba(0, 201, 167, 0.18);
      transform: translateY(-8rpx);
    }
  }

  // 确保内容在装饰层之上
  > * {
    position: relative;
    z-index: 2;
  }
}

// Section 标题样式
.section-header {
  padding-bottom: 20rpx;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.1);
}

.section-title {
  display: flex;
  gap: 12rpx;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: 700;
  color: #222;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.section-icon {
  font-size: 28rpx;
  color: #00c9a7;
  text-shadow: 0 2rpx 4rpx rgba(0, 201, 167, 0.2);
}

.section-subtitle {
  font-size: 24rpx;
  line-height: 1.5;
  color: #666;
  opacity: 0.9;
}

// 菜单列表
.menu-list {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.6);
  -webkit-backdrop-filter: blur(10rpx);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
}

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // 分割线
  &:not(:last-child)::after {
    position: absolute;
    right: 24rpx;
    bottom: 0;
    left: 100rpx;
    height: 1rpx;
    content: '';
    background: rgba(0, 201, 167, 0.1);
  }

  // 激活效果
  &:active {
    background: rgba(0, 201, 167, 0.1);
    -webkit-backdrop-filter: blur(15rpx);
    backdrop-filter: blur(15rpx);
    transform: translateX(8rpx);
  }

  // 悬停效果（H5端）
  @media (hover: hover) {
    &:hover {
      background: rgba(255, 255, 255, 0.5);
      -webkit-backdrop-filter: blur(15rpx);
      backdrop-filter: blur(15rpx);
      transform: translateX(4rpx);
    }
  }
}

// 菜单图标
.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  font-size: 24rpx;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;

  .menu-item:active & {
    transform: scale(1.1);
  }
}

// 菜单图标颜色
.menu-blue {
  color: #2563eb;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
}

.menu-green {
  color: #16a34a;
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
}

.menu-purple {
  color: #7c3aed;
  background: linear-gradient(135deg, #ede9fe, #ddd6fe);
}

.menu-orange {
  color: #f59e0b;
  background: linear-gradient(135deg, #ffedd5, #fed7aa);
}

.menu-red {
  color: #ef4444;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.menu-gray {
  color: #6b7280;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
}

.menu-indigo {
  color: #6366f1;
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
}

.menu-yellow {
  color: #eab308;
  background: linear-gradient(135deg, #fef9c3, #fef08a);
}

// 菜单信息
.menu-info {
  flex: 1;
  margin-right: 16rpx;
}

.menu-title {
  display: block;
  margin-bottom: 4rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #222;
  letter-spacing: 0.5rpx;
}

.menu-desc {
  display: block;
  font-size: 24rpx;
  line-height: 1.4;
  color: #666;
  opacity: 0.9;
}

// 菜单操作区域
.menu-action {
  display: flex;
  align-items: center;
}

.menu-arrow {
  font-size: 20rpx;
  color: #ccc;
  transition: all 0.3s ease;

  .menu-item:active & {
    color: #00c9a7;
    transform: translateX(4rpx);
  }
}

// 退出登录按钮 - 与个人中心统一样式
.logout-btn {
  position: relative;
  display: flex;
  gap: 16rpx;
  align-items: center;
  justify-content: center;
  width: calc(100% - 48rpx);
  height: 96rpx;
  margin: 64rpx 24rpx 48rpx 24rpx;
  overflow: hidden;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  -webkit-backdrop-filter: blur(20rpx) saturate(150%);
  backdrop-filter: blur(20rpx) saturate(150%);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  box-shadow: 0 16rpx 40rpx rgba(255, 107, 107, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  // 按钮装饰效果
  &::before {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
  }

  // 悬停效果
  @media (hover: hover) {
    &:hover {
      background: linear-gradient(135deg, #ff5252 0%, #ff1744 100%);
      box-shadow: 0 24rpx 60rpx rgba(255, 107, 107, 0.4);
      transform: translateY(-4rpx) scale(1.02);

      &::before {
        left: 100%;
      }
    }
  }

  // 激活效果
  &:active {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    transform: translateY(-2rpx) scale(0.98);
  }
}

.logout-icon {
  font-size: 28rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  animation: iconPulse 2s ease-in-out infinite;
}

// 底部间距
.bottom-spacing {
  width: 100%;
  height: 100rpx;
  background: transparent;
}

// 动画定义
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(5deg);
  }
}

@keyframes backgroundShift {
  0%,
  100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-2%) translateY(-1%);
  }
  50% {
    transform: translateX(1%) translateY(-2%);
  }
  75% {
    transform: translateX(-1%) translateY(1%);
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .setting-page {
    .section {
      padding: 32rpx 24rpx;
      margin: 16rpx 0 24rpx 0;
      border-radius: 24rpx;
    }

    .user-card {
      padding: 32rpx 24rpx;
      margin: 16rpx 0 24rpx 0;
      border-radius: 24rpx;
    }

    .logout-btn {
      width: calc(100% - 32rpx);
      height: 88rpx;
      margin: 48rpx 16rpx 40rpx 16rpx;
      font-size: 30rpx;
      border-radius: 44rpx;
    }
  }
}

// H5端特殊样式优化
/* #ifdef H5 */
.setting-page {
  .section {
    -webkit-backdrop-filter: blur(35rpx) saturate(200%);
    backdrop-filter: blur(35rpx) saturate(200%);

    &:hover {
      -webkit-backdrop-filter: blur(40rpx) saturate(220%);
      backdrop-filter: blur(40rpx) saturate(220%);
    }
  }

  .menu-item {
    cursor: pointer;
    user-select: none;
  }

  .logout-btn {
    cursor: pointer;
    user-select: none;
  }
}
/* #endif */

// 小程序端优化
/* #ifdef MP */
.setting-page {
  .section {
    -webkit-backdrop-filter: blur(20rpx) saturate(150%);
    backdrop-filter: blur(20rpx) saturate(150%);

    &:active {
      background: rgba(255, 255, 255, 0.9);
      transform: scale(0.98);
    }
  }

  .logout-btn {
    &:active {
      transform: scale(0.95);
    }
  }
}
/* #endif */

// 暗黑模式支持
@media (prefers-color-scheme: dark) {
  .setting-page {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);

    &::before {
      background: radial-gradient(circle at 30% 20%, rgba(0, 201, 167, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(79, 209, 199, 0.1) 0%, transparent 50%);
    }
  }

  .section {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(25rpx) saturate(150%);
    backdrop-filter: blur(25rpx) saturate(150%);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.4);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
    }
  }

  .menu-list {
    background: rgba(255, 255, 255, 0.02);
  }

  .menu-item {
    background: rgba(255, 255, 255, 0.02);

    &:active {
      background: rgba(0, 201, 167, 0.08);
    }
  }
}

// 滚动条美化（H5端）
/* #ifdef H5 */
::-webkit-scrollbar {
  width: 8rpx;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.6), rgba(79, 209, 199, 0.4));
  -webkit-backdrop-filter: blur(10rpx);
  backdrop-filter: blur(10rpx);
  border-radius: 4rpx;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.8), rgba(79, 209, 199, 0.6));
  }
}
/* #endif */
</style>
