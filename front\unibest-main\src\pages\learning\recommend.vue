<script setup lang="ts">
// @ts-ignore
import { ref, computed, onMounted, nextTick, watch } from 'vue'
// @ts-ignore
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import SearchBox from '@/components/SearchBox.vue'
// @ts-ignore
import LoadingSkeleton from '@/components/LoadingSkeleton.vue'
// @ts-ignore
import LoadingSpinner from '@/components/LoadingSpinner.vue'  
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
import {
  getRecommendedVideos,
  getRecommendedQuestionBanks,
  getRecommendedBooks,
  getUserCapabilities,
  getRecommendationStatistics,
  recordRecommendationFeedback,
  type RecommendedVideo,
  type RecommendedQuestionBank,
  type RecommendedBook,
  type UserCapabilities,
  type RecommendationQuery
} from '@/service/learning/recommend'

/**
 * @description 学习资源推荐页
 * 基于用户面试表现和能力短板，提供个性化学习资源推荐
 * 推荐三种类型的学习资源：推荐视频、推荐题库、推荐书籍
 * 实现"发现问题->分析问题->解决问题->验证问题"的闭环式成长路径
 */

// 页面加载状态
const isPageLoaded = ref(false)
const isProgressAnimated = ref(false)
const isLoading = ref(true)

// 能力评估卡片折叠状态
const isCapabilityCardCollapsed = ref(false)

// API调用加载状态
const isLoadingVideos = ref(false)
const isLoadingQuestionBanks = ref(false)
const isLoadingBooks = ref(false)
const isLoadingCapabilities = ref(false)

// 错误状态
const errorMessage = ref('')

// 用户能力数据 - 从API获取
const userCapabilities = ref<UserCapabilities | null>({
  capabilities: {
    professionalKnowledge: 75,
    communicationSkills: 68,
    logicalThinking: 85,
    problemSolving: 72,
    innovation: 60,
    pressureHandling: 78,
  },
  weaknesses: [],
  lastUpdated: new Date().toISOString()
})

// 搜索关键词
const searchQuery = ref('')

// 当前选中的标签页
const activeTab = ref('video')

// 标签页选项
const tabOptions = ref([
  { key: 'video', name: '推荐视频', icon: 'i-mdi-play-circle' },
  { key: 'question', name: '推荐题库', icon: 'i-mdi-help-circle' },
  { key: 'book', name: '推荐书籍', icon: 'i-mdi-book-open-page-variant' },
])

// API响应数据
const recommendedVideos = ref<RecommendedVideo[]>([])
const recommendedQuestionBanks = ref<RecommendedQuestionBank[]>([])
const recommendedBooks = ref<RecommendedBook[]>([])

// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 当前显示的推荐数据
const currentRecommendations = computed(() => {
  let data: any[] = []

  switch (activeTab.value) {
    case 'video':
      data = recommendedVideos.value
      break
    case 'question':
      data = recommendedQuestionBanks.value
      break
    case 'book':
      data = recommendedBooks.value
      break
    default:
      data = recommendedVideos.value
  }

  // 搜索筛选
  if (!searchQuery.value) return data
  console.log(data);

  const query = searchQuery.value.toLowerCase()
  return data.filter(
    (item: any) =>
      item.title.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      item.tags?.some((tag: string) => tag.toLowerCase().includes(query)),
  )
})

// 当前加载状态
const currentLoadingState = computed(() => {
  switch (activeTab.value) {
    case 'video':
      return isLoadingVideos.value
    case 'question':
      return isLoadingQuestionBanks.value
    case 'book':
      return isLoadingBooks.value
    default:
      return false
  }
})

// 计算薄弱环节
const weakCapabilities = computed(() => {
  if (!userCapabilities.value?.capabilities) {
    return []
  }

  const capabilities = userCapabilities.value.capabilities
  const sorted = Object.entries(capabilities)
    .sort(([, a], [, b]) => (a as number) - (b as number))
    .slice(0, 3)

  const nameMap: Record<string, string> = {
    professionalKnowledge: '专业知识',
    communicationSkills: '表达能力',
    logicalThinking: '逻辑思维',
    problemSolving: '问题解决',
    innovation: '创新能力',
    pressureHandling: '抗压能力',
  }

  return sorted.map(([key, value]) => ({
    key,
    name: nameMap[key] || key,
    value: value as number,
    improvement: 100 - (value as number),
  }))
})

/**
 * @description 获取推荐视频数据
 */
const fetchRecommendedVideos = async () => {
  try {
    isLoadingVideos.value = true
    errorMessage.value = ''

    const params: RecommendationQuery = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      searchQuery: searchQuery.value || undefined
    }

    const response = await getRecommendedVideos(params)
    if (response.code === 200 && response.data) {
      recommendedVideos.value = response.data.items || []
      pagination.value.total = response.data.total || 0
    } else {
      throw new Error(response.message || '获取推荐视频失败')
    }
  } catch (error: any) {
    console.error('获取推荐视频失败:', error)
    errorMessage.value = error.message || '获取推荐视频失败，请稍后重试'
    uni.showToast({
      title: '获取推荐视频失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isLoadingVideos.value = false
  }
}

/**
 * @description 获取推荐题库数据
 */
const fetchRecommendedQuestionBanks = async () => {
  try {
    isLoadingQuestionBanks.value = true
    errorMessage.value = ''

    const params: RecommendationQuery = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      searchQuery: searchQuery.value || undefined
    }

    const response = await getRecommendedQuestionBanks(params)
    if (response.code === 200 && response.data) {
      recommendedQuestionBanks.value = response.data.items || []
      pagination.value.total = response.data.total || 0
    } else {
      throw new Error(response.message || '获取推荐题库失败')
    }
  } catch (error: any) {
    console.error('获取推荐题库失败:', error)
    errorMessage.value = error.message || '获取推荐题库失败，请稍后重试'
    uni.showToast({
      title: '获取推荐题库失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isLoadingQuestionBanks.value = false
  }
}

/**
 * @description 获取推荐书籍数据
 */
const fetchRecommendedBooks = async () => {
  try {
    isLoadingBooks.value = true
    errorMessage.value = ''

    const params: RecommendationQuery = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      searchQuery: searchQuery.value || undefined
    }

    const response = await getRecommendedBooks(params)
    if (response.code === 200 && response.data) {
      recommendedBooks.value = response.data.items || []
      pagination.value.total = response.data.total || 0
    } else {
      throw new Error(response.message || '获取推荐书籍失败')
    }
  } catch (error: any) {
    console.error('获取推荐书籍失败:', error)
    errorMessage.value = error.message || '获取推荐书籍失败，请稍后重试'
    uni.showToast({
      title: '获取推荐书籍失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isLoadingBooks.value = false
  }
}

/**
 * @description 获取用户能力评估数据
 */
const fetchUserCapabilities = async () => {
  try {
    isLoadingCapabilities.value = true

    const response = await getUserCapabilities()
    console.log(response);
    if (response.code === 200 && response.data) {
      userCapabilities.value = response.data
    } else {
      throw new Error(response.message || '获取用户能力评估失败')
    }
  } catch (error: any) {
    console.error('获取用户能力评估失败:', error)
    // 使用默认数据作为降级方案
    userCapabilities.value = {
      capabilities: {
        professionalKnowledge: 75,
        communicationSkills: 68,
        logicalThinking: 85,
        problemSolving: 72,
        innovation: 60,
        pressureHandling: 78,
      },
      weaknesses: [],
      lastUpdated: new Date().toISOString()
    }
  } finally {
    isLoadingCapabilities.value = false
  }
}

/**
 * @description 根据当前标签页获取对应数据
 */
const fetchCurrentTabData = async () => {
  switch (activeTab.value) {
    case 'video':
      await fetchRecommendedVideos()
      break
    case 'question':
      await fetchRecommendedQuestionBanks()
      break
    case 'book':
      await fetchRecommendedBooks()
      break
  }
}

/**
 * @description 获取难度样式
 * @param difficulty 难度等级
 */
const getDifficultyStyle = (difficulty: string) => {
  const styles: Record<string, string> = {
    简单: 'bg-green-100 text-green-700',
    中等: 'bg-yellow-100 text-yellow-700',
    困难: 'bg-red-100 text-red-700',
  }
  return styles[difficulty] || 'bg-gray-100 text-gray-700'
}

/**
 * @description 获取优先级颜色
 * @param priority 优先级
 */
const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    high: '#ff6b6b',
    medium: '#feca57',
    low: '#48cab2',
  }
  return colors[priority] || '#48cab2'
}

/**
 * @description 获取优先级文本
 * @param priority 优先级
 */
const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    high: '强烈推荐',
    medium: '建议学习',
    low: '可选学习',
  }
  return texts[priority] || '推荐'
}

/**
 * @description 切换标签页
 * @param tabKey 标签页key
 */
const switchTab = (tabKey: string) => {
  if (activeTab.value === tabKey) return

  activeTab.value = tabKey
  // 重置分页
  pagination.value.pageNum = 1
  // watch监听器会自动处理API调用

  // 添加触觉反馈
  uni.vibrateShort({
    type: 'light',
  })
}

/**
 * @description 处理搜索
 * @param value 搜索值
 */
const handleSearch = (value: string) => {
  searchQuery.value = value
  // watch监听器会自动处理API调用
}

/**
 * @description 清除搜索
 */
const clearSearch = () => {
  searchQuery.value = ''
  // watch监听器会自动处理API调用
}

/**
 * @description 跳转到视频页面
 */
const goToVideoPage = () => {
  uni.navigateTo({
    url: '/pages/learning/video',
  })
}

/**
 * @description 跳转到题库页面
 */
const goToQuestionBankPage = () => {
  uni.navigateTo({
    url: '/pages/learning/all-question-banks',
  })
}

/**
 * @description 跳转到书籍页面
 */
const goToBookPage = () => {
  uni.navigateTo({
    url: '/pages/learning/book',
  })
}

/**
 * @description 开始学习
 * @param resource 资源对象
 */
const startLearning = async (resource: any) => {
  console.log('开始学习:', resource)
  uni.showModal({
    title: '开始学习',
    content: `确定要开始学习《${resource.title}》吗？`,
    confirmText: '开始学习',
    success: async (res) => {
      if (res.confirm) {
        try {
          // // 记录学习行为反馈
          // await recordRecommendationFeedback({
          //   resourceType: resource.type,
          //   resourceId: resource.id,
          //   action: 'start-learning'
          // })

          // // 跳转到学习页面
          // uni.navigateTo({
          //   url: `/pages/learning/learning?id=${resource.id}&type=${resource.type}`,
          // })
          // 判断是视频还是题库还是书籍
          if (resource.type === 'video') {
            uni.navigateTo({
              url: `/pages/learning/video-player?id=${resource.id}`,
            })
          } else if (resource.type === 'question-bank') {
            console.log(resource);
            uni.navigateTo({
              url: `/pages/learning/all-questions?bankId=${resource.value}`,
            })
          } else if (resource.type === 'book') {
            uni.navigateTo({
              url: `/pages/learning/book-reader?id=${resource.id}`,
            })
          }
          // 触觉反馈
          uni.vibrateShort({
            type: 'medium',
          })
        } catch (error: any) {
          console.error('开始学习失败:', error)
          uni.showToast({
            title: '操作失败，请稍后重试',
            icon: 'none',
            duration: 2000,
          })
        }
      }
    },
  })
}


/**
 * @description 切换收藏状态
 * @param resource 资源对象
 */
const toggleBookmark = async (resource: any) => {
  const action = resource.isBookmarked ? 'unbookmark' : 'bookmark'

  try {
    // 调用API记录反馈
    await recordRecommendationFeedback({
      resourceType: resource.type,
      resourceId: resource.id,
      action: action
    })

    // 更新本地状态
    resource.isBookmarked = !resource.isBookmarked

    uni.showToast({
      title: resource.isBookmarked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500,
    })

    // 触觉反馈
    uni.vibrateShort({
      type: 'light',
    })
  } catch (error: any) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

/**
 * @description 切换能力评估卡片折叠状态
 */
const toggleCapabilityCard = () => {
  isCapabilityCardCollapsed.value = !isCapabilityCardCollapsed.value

  // 添加触觉反馈
  uni.vibrateShort({
    type: 'light',
  })
}

/**
 * @description 获取动画延迟
 * @param index 索引
 */
const getAnimationDelay = (index: number) => {
  return `${index * 0.1}s`
}

/**
 * @description 页面初始化
 */
const initPage = async () => {
  isLoading.value = true
  await nextTick()

  try {
    // 并行获取用户能力评估和当前标签页数据
    await Promise.all([
      fetchUserCapabilities(),
      fetchCurrentTabData()
    ])

    isPageLoaded.value = true

    // 延迟启动进度动画
    setTimeout(() => {
      isProgressAnimated.value = true
    }, 500)
  } catch (error: any) {
    console.error('页面初始化失败:', error)
    uni.showToast({
      title: '页面加载失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isLoading.value = false
  }
}

// 监听搜索关键词变化，实现防抖搜索
let searchTimer: any = null
watch(searchQuery, () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(async () => {
    pagination.value.pageNum = 1
    await fetchCurrentTabData()
  }, 500) // 500ms防抖
})

// 监听标签页切换
watch(activeTab, async () => {
  pagination.value.pageNum = 1
  await fetchCurrentTabData()
})

// 页面加载时执行初始化
onLoad(() => {
  initPage()

  uni.preloadPage({
    url:'/pages/learning/video-player',
  })
  uni.preloadPage({
    url:'/pages/learning/all-questions',
  })
  uni.preloadPage({
    url:'/pages/learning/book-reader',
  })
})
</script>

<template>
  <view class="recommend-container">
    <!-- 加载骨架屏 -->
    <LoadingSkeleton v-if="isLoading" />

    <!-- 主要内容区域 -->
    <scroll-view v-else class="main-content" scroll-y>
      <HeadBar :title="'个性化学习推荐'" />
      <view class="content-wrapper">
        <!-- 头部欢迎区域 -->
        <view class="welcome-section">
          <view class="welcome-content">
            <text class="welcome-title">个性化学习推荐</text>
            <text class="welcome-subtitle">基于您的面试表现，为您量身定制学习路径</text>
          </view>
          <view class="welcome-decoration">
            <view class="i-mdi-lightbulb-on decoration-icon"></view>
          </view>
        </view>

        <!-- 能力评估卡片 -->
        <view class="capability-card" :class="{ collapsed: isCapabilityCardCollapsed }">
          <view class="card-header" @click="toggleCapabilityCard">
            <view class="card-header-content">
              <text class="card-title">
                <view class="i-mdi-chart-donut card-title-icon"></view>
                您的能力评估
              </text>
            </view>
            <view class="collapse-btn" :class="{ collapsed: isCapabilityCardCollapsed }">
              <view class="i-mdi-chevron-down collapse-icon"></view>
            </view>
          </view>

          <!-- 可折叠的内容区域 -->
          <view class="capability-content" :class="{ collapsed: isCapabilityCardCollapsed }">
            <view class="capability-list">
              <view class="capability-item">
                <text class="capability-label">专业知识</text>
                <view class="capability-bar">
                  <view class="capability-progress" :style="{
                    width: isProgressAnimated ? userCapabilities.capabilities.professionalKnowledge + '%' : '0%',
                  }"></view>
                </view>
                <text class="capability-value">{{ userCapabilities.capabilities.professionalKnowledge }}%</text>
              </view>

              <view class="capability-item">
                <text class="capability-label">表达能力</text>
                <view class="capability-bar">
                  <view class="capability-progress weak" :style="{
                    width: isProgressAnimated ? userCapabilities.capabilities.communicationSkills + '%' : '0%',
                  }"></view>
                </view>
                <text class="capability-value weak">{{ userCapabilities.capabilities.communicationSkills }}%</text>
              </view>

              <view class="capability-item">
                <text class="capability-label">逻辑思维</text>
                <view class="capability-bar">
                  <view class="capability-progress" :style="{
                    width: isProgressAnimated ? userCapabilities.capabilities.logicalThinking + '%' : '0%',
                  }"></view>
                </view>
                <text class="capability-value">{{ userCapabilities.capabilities.logicalThinking }}%</text>
              </view>

              <view class="capability-item">
                <text class="capability-label">创新能力</text>
                <view class="capability-bar">
                  <view class="capability-progress weak"
                    :style="{ width: isProgressAnimated ? userCapabilities.capabilities.innovation + '%' : '0%' }"></view>
                </view>
                <text class="capability-value weak">{{ userCapabilities.capabilities.innovation }}%</text>
              </view>
            </view>

            <!-- 薄弱环节提示 -->
            <view class="weak-points">
              <text class="weak-title">
                <view class="i-mdi-lightbulb-on weak-title-icon"></view>
                需要重点提升的能力：
              </text>
              <view class="weak-tags">
                <view v-for="item in weakCapabilities" :key="item.key" class="weak-tag">
                  <text class="weak-tag-text">{{ item.name }}</text>
                  <text class="weak-tag-score">{{ item.value }}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-section">
          <SearchBox v-model="searchQuery" placeholder="搜索学习资源..." :show-search-button="false" @input="handleSearch"
            @clear="clearSearch" />
        </view>

        <!-- 标签页导航 -->
        <view class="tab-navigation">
          <view class="tab-list">
            <view v-for="tab in tabOptions" :key="tab.key" class="tab-item" :class="{ active: activeTab === tab.key }"
              @click="switchTab(tab.key)">
              <view :class="tab.icon" class="tab-icon"></view>
              <text class="tab-text">{{ tab.name }}</text>
            </view>
          </view>
        </view>

        <!-- 推荐内容区域 -->
        <view class="recommend-section">
          <view class="section-header">
            <view class="section-title-wrapper">
              <view :class="tabOptions.find(t => t.key === activeTab)?.icon" class="section-icon"></view>
              <text class="section-title">{{tabOptions.find(t => t.key === activeTab)?.name}}</text>
              <text class="section-subtitle">
                {{ activeTab === 'video' ? '精选优质视频课程' :
                  activeTab === 'question' ? '精选面试题库练习' :
                    '精选专业书籍阅读' }}
              </text>
            </view>
            <button class="more-btn" @click="activeTab === 'video' ? goToVideoPage() :
              activeTab === 'question' ? goToQuestionBankPage() :
                goToBookPage()">
              <text>查看更多</text>
              <view class="i-mdi-chevron-right"></view>
            </button>
          </view>

          <!-- 加载状态 -->
          <view v-if="currentLoadingState" class="loading-section">
            <LoadingSpinner/>
          </view>

          <!-- 错误状态 -->
          <view v-else-if="errorMessage" class="error-state">
            <view class="i-mdi-alert-circle-outline error-icon"></view>
            <text class="error-title">加载失败</text>
            <text class="error-desc">{{ errorMessage }}</text>
            <button class="retry-btn" @click="fetchCurrentTabData">
              <view class="i-mdi-refresh"></view>
              <text>重试</text>
            </button>
          </view>

          <!-- 视频内容 -->
          <view v-else-if="activeTab === 'video'" class="content-list">
            <view v-if="currentRecommendations.length > 0" class="video-list">
              <view v-for="(video, index) in currentRecommendations" :key="video.id" class="video-card"
                :style="{ animationDelay: getAnimationDelay(index) }">
                <!-- 优先级标签 -->
                <view class="priority-tag" :style="{ backgroundColor: getPriorityColor(video.priority) }">
                  <text class="priority-text">{{ getPriorityText(video.priority) }}</text>
                </view>

                <view class="resource-content">
                  <!-- 封面图 -->
                  <image class="resource-cover" :src="video.cover" mode="aspectFill"></image>
                  <view class="resource-info">
                    <view class="resource-header">
                      <text class="resource-title">{{ video.title }}</text>
                      <view class="resource-meta">
                        <view class="difficulty-tag" :class="getDifficultyStyle(video.difficulty)">
                          <text class="difficulty-text">{{ video.difficulty }}</text>
                        </view>
                        <text class="resource-target">针对：{{ video.target }}</text>
                      </view>
                    </view>
                    <text class="resource-description">{{ video.description }}</text>
                    <view class="resource-details">
                      <view class="detail-item">
                        <view class="i-mdi-clock-outline detail-icon"></view>
                        <text class="detail-text">{{ video.duration }}</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-star detail-icon"></view>
                        <text class="detail-text">{{ video.rating }}</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-account-group detail-icon"></view>
                        <text class="detail-text">{{ video.studentCount }}人学习</text>
                      </view>
                      <view class="detail-item price-item">
                        <view class="i-mdi-currency-usd detail-icon"></view>
                        <text v-if="video.price === 0" class="price-text free">免费</text>
                        <text v-else class="price-text">¥{{ video.price }}</text>
                      </view>
                    </view>

                    <view class="resource-tags">
                      <text class="tag" v-for="(tag, index) in video.tags" :key="index">
                        {{ tag }}
                      </text>
                    </view>
                  </view>
                </view>

                <view class="resource-actions">
                  <button class="action-btn favorite-btn" :class="{ favorited: video.isBookmarked }"
                    @click.stop="toggleBookmark(video)">
                    <view :class="video.isBookmarked ? 'i-mdi-heart' : 'i-mdi-heart-outline'" class="btn-icon"></view>
                    <text>{{ video.isBookmarked ? '已收藏' : '收藏' }}</text>
                  </button>
                  <button class="action-btn start-btn" @click.stop="startLearning(video)">
                    <view class="i-mdi-play btn-icon"></view>
                    <text>开始学习</text>
                  </button>
                </view>
              </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-state">
              <view class="i-mdi-video-off-outline empty-icon"></view>
              <text class="empty-title">暂无匹配的视频课程</text>
              <text class="empty-desc">尝试搜索其他关键词</text>
            </view>
          </view>
          <!-- 题库内容 -->
          <view v-else-if="activeTab === 'question'" class="content-list">
            <view v-if="currentRecommendations.length > 0" class="question-bank-list">
              <view v-for="(bank, index) in currentRecommendations" :key="bank.id" class="question-bank-card"
                :style="{ animationDelay: getAnimationDelay(index) }">
                <!-- 优先级标签 -->
                <view class="priority-tag" :style="{ backgroundColor: getPriorityColor(bank.priority) }">
                  <text class="priority-text">{{ getPriorityText(bank.priority) }}</text>
                </view>
                <view class="resource-content">
                  <view class="resource-info">
                    <view class="resource-header">
                      <text class="resource-title">{{ bank.title }}</text>
                      <view class="resource-meta">
                        <view class="difficulty-tag" :class="getDifficultyStyle(bank.difficulty)">
                          <text class="difficulty-text">{{ bank.difficulty }}</text>
                        </view>
                        <text class="resource-target">针对：{{ bank.target }}</text>
                      </view>
                    </view>

                    <text class="resource-description">{{ bank.description }}</text>

                    <view class="resource-details">
                      <view class="detail-item">
                        <view class="i-mdi-help-circle detail-icon"></view>
                        <text class="detail-text">{{ bank.questionCount }}题</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-star detail-icon"></view>
                        <text class="detail-text">{{ bank.rating }}</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-account-group detail-icon"></view>
                        <text class="detail-text">{{ bank.studentCount }}人练习</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-clock-outline detail-icon"></view>
                        <text class="detail-text">{{ bank.estimatedTime }}</text>
                      </view>
                    </view>

                    <view class="resource-tags">
                      <text class="tag" v-for="(tag, index) in bank.tags" :key="index">
                        {{ tag }}
                      </text>
                    </view>
                  </view>
                </view>

                <view class="resource-actions">
                  <button class="action-btn favorite-btn" :class="{ favorited: bank.isBookmarked }"
                    @click.stop="toggleBookmark(bank)">
                    <view :class="bank.isBookmarked ? 'i-mdi-heart' : 'i-mdi-heart-outline'" class="btn-icon"></view>
                    <text>{{ bank.isBookmarked ? '已收藏' : '收藏' }}</text>
                  </button>
                  <button class="action-btn start-btn" @click.stop="startLearning(bank)">
                    <view class="i-mdi-pencil btn-icon"></view>
                    <text>开始练习</text>
                  </button>
                </view>
              </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-state">
              <view class="i-mdi-help-circle-outline empty-icon"></view>
              <text class="empty-title">暂无匹配的题库</text>
              <text class="empty-desc">尝试搜索其他关键词</text>
            </view>
          </view>

          <!-- 书籍内容 -->
          <view v-else-if="activeTab === 'book'" class="content-list">
            <view v-if="currentRecommendations.length > 0" class="book-list">
              <view v-for="(book, index) in currentRecommendations" :key="book.id" class="book-card"
                :style="{ animationDelay: getAnimationDelay(index) }">
                <!-- 优先级标签 -->
                <view class="priority-tag" :style="{ backgroundColor: getPriorityColor(book.priority) }">
                  <text class="priority-text">{{ getPriorityText(book.priority) }}</text>
                </view>
                <view class="resource-content">
                  <!-- 封面图 -->
                  <image class="resource-cover" :src="book.cover" mode="aspectFill"></image>

                  <view class="resource-info">
                    <view class="resource-header">
                      <text class="resource-title">{{ book.title }}</text>
                      <view class="resource-meta">
                        <view class="difficulty-tag" :class="getDifficultyStyle(book.difficulty)">
                          <text class="difficulty-text">{{ book.difficulty }}</text>
                        </view>
                        <text class="resource-target">针对：{{ book.target }}</text>
                      </view>
                    </view>

                    <text class="resource-description">{{ book.description }}</text>

                    <view class="resource-details">
                      <view class="detail-item">
                        <view class="i-mdi-book detail-icon"></view>
                        <text class="detail-text">{{ book.pageCount }}页</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-star detail-icon"></view>
                        <text class="detail-text">{{ book.rating }}</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-account-group detail-icon"></view>
                        <text class="detail-text">{{ book.readerCount }}人阅读</text>
                      </view>
                      <view class="detail-item">
                        <view class="i-mdi-clock-outline detail-icon"></view>
                        <text class="detail-text">{{ book.estimatedTime }}</text>
                      </view>
                    </view>

                    <view class="resource-tags">
                      <text class="tag" v-for="(tag, index) in book.tags" :key="index">
                        {{ tag }}
                      </text>
                    </view>
                  </view>
                </view>

                <view class="resource-actions">
                  <button class="action-btn favorite-btn" :class="{ favorited: book.isBookmarked }"
                    @click.stop="toggleBookmark(book)">
                    <view :class="book.isBookmarked ? 'i-mdi-heart' : 'i-mdi-heart-outline'" class="btn-icon"></view>
                    <text>{{ book.isBookmarked ? '已收藏' : '收藏' }}</text>
                  </button>
                  <button class="action-btn start-btn" @click.stop="startLearning(book)">
                    <view class="i-mdi-book-open btn-icon"></view>
                    <text>开始阅读</text>
                  </button>
                </view>
              </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-state">
              <view class="i-mdi-book-outline empty-icon"></view>
              <text class="empty-title">暂无匹配的书籍</text>
              <text class="empty-desc">尝试搜索其他关键词</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.recommend-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

// 主要内容区域
.main-content {
  flex: 1;
  background: transparent;
}

.content-wrapper {
  padding: 0 20rpx 160rpx;
}

// 欢迎区域
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  margin: 20rpx 0 32rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);
  position: relative;
  overflow: hidden;

  .welcome-content {
    flex: 1;

    .welcome-title {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: white;
    }

    .welcome-subtitle {
      font-size: 24rpx;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .welcome-decoration {
    .decoration-icon {
      font-size: 64rpx;
      color: rgba(255, 255, 255, 0.3);
    }
  }

}

// 能力评估卡片
.capability-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  margin: 0 0 32rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    padding-bottom: 40rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    transform: translateY(-2rpx);
  }

  .card-header {
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
    }

    .card-header-content {
      flex: 1;

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        .card-title-icon {
          font-size: 36rpx;
          color: #00c9a7;
          margin-right: 12rpx;
        }
      }

      .card-subtitle {
        font-size: 24rpx;
        color: #666;
      }
    }

    .collapse-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 201, 167, 0.1);
      border-radius: 50%;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: rgba(0, 201, 167, 0.15);
        transform: scale(1.05);
      }

      &.collapsed {
        background: rgba(0, 201, 167, 0.2);

        .collapse-icon {
          transform: rotate(-180deg);
        }
      }

      .collapse-icon {
        font-size: 32rpx;
        color: #00c9a7;
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .capability-content {
    max-height: 1000rpx;
    opacity: 1;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);

    &.collapsed {
      max-height: 0;
      opacity: 0;
      transform: translateY(-20rpx);
      margin-bottom: 0;
      padding-top: 0;
    }
    .capability-list {
      margin-bottom: 30rpx;

      .capability-item {
        display: flex;
        align-items: center;
        margin-bottom: 25rpx;
        opacity: 1;
        transform: translateX(0);
        transition: all 0.3s ease;

        &:nth-child(1) { transition-delay: 0.1s; }
        &:nth-child(2) { transition-delay: 0.15s; }
        &:nth-child(3) { transition-delay: 0.2s; }
        &:nth-child(4) { transition-delay: 0.25s; }

        .capability-label {
          font-size: 26rpx;
          color: #333;
          width: 140rpx;
        }

        .capability-bar {
          flex: 1;
          height: 20rpx;
          background: #f0f0f0;
          border-radius: 10rpx;
          margin: 0 20rpx;
          overflow: hidden;

          .capability-progress {
            height: 100%;
            background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
            border-radius: 10rpx;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
              animation: shimmer 2s infinite;
            }

            &.weak {
              background: linear-gradient(90deg, #ff6b6b 0%, #feca57 100%);
            }
          }
        }

        .capability-value {
          font-size: 24rpx;
          font-weight: bold;
          color: #00c9a7;
          width: 80rpx;

          &.weak {
            color: #ff6b6b;
          }
        }
      }
    }

    // 折叠状态下的能力项动画
    &.collapsed .capability-item {
      opacity: 0;
      transform: translateX(-30rpx);
      transition-delay: 0s;
    }

    .weak-points {
      padding-top: 20rpx;
      border-top: 1rpx solid #f0f0f0;
      opacity: 1;
      transform: translateY(0);
      transition: all 0.3s ease 0.3s;

      .weak-title {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;

        .weak-title-icon {
          font-size: 28rpx;
          color: #fbbf24;
          margin-right: 8rpx;
        }
      }

      .weak-tags {
        display: flex;
        gap: 12rpx;
        flex-wrap: wrap;

        .weak-tag {
          display: flex;
          align-items: center;
          gap: 8rpx;
          background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
          border: 1rpx solid #fecaca;
          border-radius: 20rpx;
          padding: 8rpx 16rpx;
          opacity: 1;
          transform: scale(1);
          transition: all 0.2s ease;

          &:nth-child(1) { transition-delay: 0.35s; }
          &:nth-child(2) { transition-delay: 0.4s; }
          &:nth-child(3) { transition-delay: 0.45s; }

          .weak-tag-text {
            font-size: 22rpx;
            color: #dc2626;
          }

          .weak-tag-score {
            font-size: 20rpx;
            font-weight: bold;
            color: #ef4444;
          }
        }
      }
    }

    // 折叠状态下的薄弱环节动画
    &.collapsed .weak-points {
      opacity: 0;
      transform: translateY(20rpx);
      transition-delay: 0s;
    }

    &.collapsed .weak-tag {
      opacity: 0;
      transform: scale(0.8);
      transition-delay: 0s;
    }
  }
}

// 搜索框
.search-section {
  margin-bottom: 32rpx;
}

// 标签页导航
.tab-navigation {
  margin-bottom: 32rpx;

  .tab-list {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    padding: 8rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20rpx 16rpx;
      border-radius: 16rpx;
      transition: all 0.3s ease;
      cursor: pointer;

      &.active {
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);

        .tab-icon {
          color: white;
          transform: scale(1.1);
        }

        .tab-text {
          color: white;
          font-weight: bold;
        }
      }

      &:active {
        transform: scale(0.98);
      }

      .tab-icon {
        font-size: 32rpx;
        color: #64748b;
        margin-bottom: 8rpx;
        transition: all 0.3s ease;
      }

      .tab-text {
        font-size: 22rpx;
        color: #64748b;
        transition: all 0.3s ease;
      }
    }
  }
}

// 推荐区域
.recommend-section {
  margin-bottom: 40rpx;

  .section-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .section-title-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12rpx;

      .section-icon {
        font-size: 36rpx;
        color: #00c9a7;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1e293b;
        margin-right: 16rpx;
      }

      .section-subtitle {
        font-size: 24rpx;
        line-height: 1.4;
        color: #64748b;
      }
    }

    .more-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      background: rgba(0, 201, 167, 0.1);
      border: 1rpx solid rgba(0, 201, 167, 0.2);
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #00c9a7;
      transition: all 0.2s ease;

      &:active {
        background: rgba(0, 201, 167, 0.2);
        transform: scale(0.98);
      }

      .i-mdi-chevron-right {
        font-size: 20rpx;
      }
    }
  }

  .content-list {

    .video-list,
    .question-bank-list,
    .book-list {

      .video-card,
      .question-bank-card,
      .book-card,
      .resource-card {
        margin-bottom: 24rpx;
        overflow: hidden;
        background: white;
        border: 2rpx solid #f1f5f9;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
        position: relative;
        opacity: 0;
        transition: all 0.3s ease;
        transform: translateY(30rpx);
        animation: slideInUp 0.6s ease-out forwards;

        &:active {
          box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
          transform: translateY(-4rpx);
        }

        .priority-tag {
          position: absolute;
          top: 20rpx;
          right: 20rpx;
          background: #ff6b6b;
          border-radius: 20rpx;
          padding: 8rpx 20rpx;
          z-index: 10;

          .priority-text {
            font-size: 20rpx;
            color: #fff;
            font-weight: bold;
          }
        }

        .bookmark-section {
          position: absolute;
          top: 20rpx;
          right: 20rpx;
          z-index: 10;

          .bookmark-btn {
            width: 80rpx;
            height: 80rpx;
            background: rgba(255, 255, 255, 0.9);
            border: 2rpx solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            &.bookmarked {
              background: #fef3cd;
              border-color: #fbbf24;

              .bookmark-icon {
                color: #f59e0b;
              }
            }

            &:active {
              transform: scale(0.95);
            }

            .bookmark-icon {
              font-size: 28rpx;
              color: #94a3b8;
            }
          }
        }

        .resource-content {
          display: flex;
          padding: 30rpx;

          .resource-cover {
            width: 180rpx;
            height: 120rpx;
            border-radius: 16rpx;
            margin-right: 30rpx;
            flex-shrink: 0;
          }

          .resource-info {
            flex: 1;

            .resource-header {
              margin-bottom: 30rpx;

              .resource-title {
                font-size: 30rpx;
                font-weight: bold;
                color: #333;
                line-height: 1.4;
                margin-bottom: 12rpx;
              }

              .resource-meta {
                display: flex;
                align-items: center;
                gap: 16rpx;
                flex-wrap: wrap;

                .difficulty-tag {
                  padding: 6rpx 16rpx;
                  font-size: 20rpx;
                  font-weight: 500;
                  border-radius: 12rpx;

                  &.bg-green-100 {
                    color: #16a34a;
                    background: #dcfce7;
                  }

                  &.bg-yellow-100 {
                    color: #d97706;
                    background: #fef3c7;
                  }

                  &.bg-red-100 {
                    color: #dc2626;
                    background: #fee2e2;
                  }
                }

                .resource-target {
                  font-size: 22rpx;
                  color: #00c9a7;
                  background: rgba(0, 201, 167, 0.1);
                  padding: 4rpx 12rpx;
                  border-radius: 10rpx;
                }
              }
            }

            .resource-description {
              font-size: 24rpx;
              color: #666;
              line-height: 1.5;
              margin-bottom: 20rpx;
            }

            .resource-details {
              display: flex;
              gap: 24rpx;
              margin-bottom: 20rpx;
              flex-wrap: wrap;

              .detail-item {
                display: flex;
                align-items: center;

                .detail-icon {
                  font-size: 24rpx;
                  color: #00c9a7;
                  margin-right: 6rpx;
                }

                .detail-text {
                  font-size: 20rpx;
                  color: #999;
                }

                &.price-item {
                  .price-text {
                    font-size: 20rpx;
                    font-weight: bold;
                    color: #00c9a7;

                    &.free {
                      color: #10b981;
                    }
                  }
                }
              }
            }

            .progress-section {
              margin: 20rpx 0;
              padding: 16rpx 0;
              border-top: 1rpx solid #f0f0f0;

              .progress-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12rpx;

                .progress-label {
                  font-size: 22rpx;
                  color: #666;
                }

                .progress-value {
                  font-size: 22rpx;
                  font-weight: bold;
                  color: #00c9a7;
                }
              }

              .progress-bar {
                width: 100%;
                height: 8rpx;
                background: #f0f0f0;
                border-radius: 4rpx;
                overflow: hidden;
                margin-bottom: 8rpx;

                .progress-fill {
                  height: 100%;
                  background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
                  border-radius: 4rpx;
                  transition: width 0.3s ease;
                }
              }

              .progress-detail {
                font-size: 20rpx;
                color: #999;
              }
            }

            .resource-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 12rpx;

              .tag {
                background: #f0f0f0;
                font-size: 20rpx;
                color: #666;
                padding: 6rpx 16rpx;
                border-radius: 20rpx;
              }
            }
          }
        }

        .resource-actions {
          display: flex;
          padding: 0 30rpx 30rpx;
          gap: 20rpx;

          .action-btn {
            flex: 1;
            height: 70rpx;
            border-radius: 35rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;
            font-size: 26rpx;
            font-weight: bold;
            border: none;

            .btn-icon {
              font-size: 32rpx;
            }

            &.favorite-btn {
              background: linear-gradient(135deg, #fef3cd 0%, #fed6e3 100%);
              color: #333;
              border: 2rpx solid #fbbf24;

              &.favorited {
                background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
                color: white;

                .btn-icon {
                  color: white;
                }
              }

              .btn-icon {
                color: #333;
              }
            }

            &.start-btn {
              background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
              color: #fff;

              .btn-icon {
                color: #fff;
              }
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 40rpx;

      .empty-icon {
        margin-bottom: 32rpx;
        font-size: 120rpx;
        color: #cbd5e1;
      }

      .empty-title {
        margin-bottom: 16rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #475569;
      }

      .empty-desc {
        margin-bottom: 40rpx;
        font-size: 24rpx;
        line-height: 1.5;
        color: #64748b;
        text-align: center;
      }

      .empty-action-btn {
        display: flex;
        gap: 12rpx;
        align-items: center;
        padding: 20rpx 32rpx;
        font-size: 24rpx;
        font-weight: 500;
        color: white;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        border: none;
        border-radius: 24rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }
    }

    // 错误状态
    .error-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 40rpx;

      .error-icon {
        margin-bottom: 32rpx;
        font-size: 120rpx;
        color: #ef4444;
      }

      .error-title {
        margin-bottom: 16rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #dc2626;
      }

      .error-desc {
        margin-bottom: 40rpx;
        font-size: 24rpx;
        line-height: 1.5;
        color: #64748b;
        text-align: center;
      }

      .retry-btn {
        display: flex;
        gap: 12rpx;
        align-items: center;
        padding: 20rpx 32rpx;
        font-size: 24rpx;
        font-weight: 500;
        color: white;
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        border: none;
        border-radius: 24rpx;
        box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.3);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }
    }

  }

  // 浮动操作按钮
  .floating-action-btn {
    position: fixed;
    bottom: 120rpx;
    right: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    border-radius: 60rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.4);
    z-index: 100;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
    }

    .fab-icon {
      font-size: 36rpx;
      color: white;
      margin-bottom: 4rpx;
    }

    .fab-text {
      font-size: 18rpx;
      color: white;
      font-weight: 500;
    }
  }

  // 动画效果
  @keyframes slideInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  // 响应式设计
  @media screen and (max-width: 750rpx) {
    .welcome-section {
      padding: 24rpx 20rpx;

      .capability-test-btn {
        position: static;
        margin-top: 20rpx;
        align-self: flex-start;
      }
    }

    .capability-card {
      padding: 30rpx;

      .capability-list .capability-item {
        .capability-label {
          width: 120rpx;
          font-size: 24rpx;
        }

        .capability-bar {
          margin: 0 16rpx;
        }

        .capability-value {
          width: 70rpx;
          font-size: 22rpx;
        }
      }
    }

    .resource-card {
      .resource-content {
        padding: 24rpx;

        .resource-cover {
          width: 160rpx;
          height: 100rpx;
          margin-right: 24rpx;
        }

        .resource-info {
          .resource-header .resource-title {
            font-size: 28rpx;
          }

          .resource-description {
            font-size: 22rpx;
          }
        }
      }

      .resource-actions {
        padding: 0 24rpx 24rpx;

        .action-btn {
          height: 64rpx;
          font-size: 24rpx;

          .btn-icon {
            font-size: 28rpx;
          }
        }
      }
    }

    .floating-action-btn {
      width: 100rpx;
      height: 100rpx;
      bottom: 100rpx;
      right: 30rpx;

      .fab-icon {
        font-size: 32rpx;
      }

      .fab-text {
        font-size: 16rpx;
      }
    }
  }
}
</style>
