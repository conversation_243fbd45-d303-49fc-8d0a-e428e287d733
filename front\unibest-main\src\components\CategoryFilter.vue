<!--
/**
 * CategoryFilter - 分类筛选组件
 * 
 * @description 一个可复用的分类筛选组件，支持水平滚动显示、超出数量时显示"更多"按钮
 * 
 * @features
 * - 支持自定义标题和选项
 * - 固定显示最多7个分类标签，保持界面简洁
 * - 超过7个分类时，在标题行显示"更多"按钮
 * - 重置按钮位于标题行右侧，方便快速重置
 * - 点击"更多"弹出模态框显示所有选项
 * - 支持重置功能
 * - 通过事件将选择结果传递给父组件
 * 
 * @usage
 * <CategoryFilter
 *   title="分类筛选"
 *   :category-options="categoryOptions"
 *   :selected-category="selectedCategory"
 *   max-width="100%"
 *   :show-icon="true"
 *   @change="handleCategoryChange"
 *   @reset="handleCategoryReset"
 * />
 * 
 * @props
 * - title: 筛选标题 (可选，默认: '分类筛选')
 * - categoryOptions: 分类选项数组 (必需)
 * - selectedCategory: 当前选中的分类 (可选，默认: 'all')
 * - maxWidth: 组件最大宽度 (可选，默认: '100%')
 * - showIcon: 是否显示图标 (可选，默认: true)
 * 
 * @events
 * - change: 分类改变时触发，参数为选中的分类key
 * - reset: 重置时触发
 * 
 * @improvements
 * - 简化逻辑：固定显示7个分类，超过则显示更多按钮
 * - 优化布局：更多和重置按钮移至标题行右侧
 * - 提升体验：重置按钮始终显示，操作更直观
 * - 减少复杂度：移除自适应算法，提高组件稳定性
 * 
 * <AUTHOR>
 */
-->

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { Ref } from 'vue'
// @ts-ignore
import Notification from '@/components/Notification.vue'

// 定义分类选项类型
interface CategoryOption {
  key: string
  name: string
  icon: string
}

// 定义 Props 接口
interface Props {
  title?: string
  categoryOptions: CategoryOption[]
  selectedCategory?: string
  maxWidth?: string
  showIcon?: boolean
}

// 定义 Emits 接口
interface Emits {
  (event: 'change', category: string): void
  (event: 'reset'): void
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  title: '分类筛选',
  selectedCategory: 'all',
  maxWidth: '100%',
  showIcon: true,
})

// Emits 定义
const emits = defineEmits<Emits>()

// 响应式数据
const showMoreModal: Ref<boolean> = ref(false)
const filterContainerRef: Ref<any> = ref(null)
const filterListRef: Ref<any> = ref(null)
const visibleOptions: Ref<CategoryOption[]> = ref([])
const hasMoreOptions: Ref<boolean> = ref(false)
const currentSelected: Ref<string> = ref(props.selectedCategory)
const maxVisibleCount = 7 // 最大显示数量
const notificationVisible: Ref<boolean> = ref(false)
const notificationType: Ref<'success' | 'error' | 'info' | 'warning'> = ref('info')
const notificationMessage: Ref<string> = ref('')

// 计算属性
const containerStyle = computed(() => ({
  maxWidth: props.maxWidth,
}))

/**
 * @description 计算哪些选项可以在主界面显示
 */
const calculateVisibleOptions = (): void => {
  // 简化逻辑：固定显示最多7个分类
  if (props.categoryOptions.length > maxVisibleCount) {
    hasMoreOptions.value = true
    visibleOptions.value = props.categoryOptions.slice(0, maxVisibleCount)
  } else {
    hasMoreOptions.value = false
    visibleOptions.value = props.categoryOptions
  }
}

/**
 * @description 选择分类
 * @param category 分类的 key 值
 */
const selectCategory = (category: string): void => {
  currentSelected.value = category
  emits('change', category)
}

/**
 * @description 获取分类选中状态的样式
 * @param category 分类的 key 值
 * @returns 返回激活状态的样式类名
 */
const getCategoryStyle = (category: string): string => {
  return category === currentSelected.value ? 'filter-active' : ''
}

/**
 * @description 显示更多选项弹窗
 */
const showMoreOptions = (): void => {
  showMoreModal.value = true
}

/**
 * @description 关闭更多选项弹窗
 */
const closeMoreModal = (): void => {
  showMoreModal.value = false
}

/**
 * @description 在弹窗中选择分类
 * @param category 分类的 key 值
 */
const selectCategoryInModal = (category: string): void => {
  selectCategory(category)
  closeMoreModal()
}

/**
 * @description 重置分类选择
 */
const resetCategory = (): void => {
  const defaultCategory = props.categoryOptions[0]?.key || 'all'
  currentSelected.value = defaultCategory
  emits('reset')
  emits('change', defaultCategory)
  closeMoreModal()
  notificationType.value = 'success'
  notificationMessage.value = '重置分类成功'
  notificationVisible.value = true
}

// 监听 props 变化
const updateSelectedCategory = (newCategory: string): void => {
  currentSelected.value = newCategory
}

// 页面挂载后计算可见选项
onMounted(() => {
  calculateVisibleOptions()
})

// 暴露给父组件的方法
defineExpose({
  updateSelectedCategory,
  calculateVisibleOptions,
})
</script>

<template>
  <view class="category-filter" :style="containerStyle">
    <!-- 筛选标题 -->
    <view class="filter-header">
      <view class="filter-title-wrapper">
        <view class="i-mdi-filter-variant filter-header-icon"></view>
        <text class="filter-title">{{ title }}</text>
      </view>

      <view class="filter-header-actions">
        <!-- 更多按钮 -->
        <view v-if="hasMoreOptions" class="filter-more-btn" @click="showMoreOptions">
          <view class="i-mdi-dots-horizontal filter-more-icon"></view>
        </view>

        <!-- 重置按钮 -->
        <view class="filter-reset-btn" @click="resetCategory">
          <view class="i-mdi-refresh filter-reset-icon"></view>
        </view>
      </view>
    </view>

    <!-- 筛选选项 -->
    <view ref="filterContainerRef" class="filter-container">
      <scroll-view class="filter-scroll" scroll-x>
        <view ref="filterListRef" class="filter-list">
          <!-- 可见的筛选选项 -->
          <view
            v-for="option in visibleOptions"
            :key="option.key"
            class="filter-item"
            :class="getCategoryStyle(option.key)"
            @click="selectCategory(option.key)"
          >
            <view v-if="showIcon" :class="option.icon" class="filter-item-icon"></view>
            <text class="filter-text">{{ option.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 更多选项弹窗 -->
    <view v-if="showMoreModal" class="modal-overlay" @click="closeMoreModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择{{ title }}</text>
          <button class="modal-close-btn" @click="closeMoreModal">
            <view class="i-mdi-close modal-close-icon"></view>
          </button>
        </view>

        <view class="modal-body">
          <view class="modal-options">
            <view
              v-for="option in categoryOptions"
              :key="option.key"
              class="modal-option"
              :class="getCategoryStyle(option.key)"
              @click="selectCategoryInModal(option.key)"
            >
              <view class="modal-option-content">
                <view v-if="showIcon" :class="option.icon" class="modal-option-icon"></view>
                <text class="modal-option-text">{{ option.name }}</text>
              </view>
              <view
                v-if="option.key === currentSelected"
                class="i-mdi-check modal-check-icon"
              ></view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-reset-btn" @click="resetCategory">
            <view class="i-mdi-refresh modal-btn-icon"></view>
            <text class="modal-btn-text">重置</text>
          </button>
          <button class="modal-confirm-btn" @click="closeMoreModal">
            <text class="modal-btn-text">确定</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 通知 -->
    <Notification
      :visible="notificationVisible"
      :type="notificationType"
      :message="notificationMessage"
    />
  </view>
</template>

<style lang="scss" scoped>
.category-filter {
  width: 100%;

  .filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .filter-title-wrapper {
      display: flex;
      gap: 12rpx;
      align-items: center;

      .filter-header-icon {
        font-size: 28rpx;
        color: #00c9a7;
      }

      .filter-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .filter-header-actions {
      display: flex;
      gap: 16rpx;

      .filter-more-btn,
      .filter-reset-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        background: #f8fafc;
        border: 2rpx solid #e2e8f0;
        border-radius: 12rpx;
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          background: #f1f5f9;
        }

        .filter-more-icon,
        .filter-reset-icon {
          font-size: 24rpx;
          color: #64748b;
        }
      }

      .filter-more-btn {
        &:hover {
          border-color: #00c9a7;
          .filter-more-icon {
            color: #00c9a7;
          }
        }
      }

      .filter-reset-btn {
        &:hover {
          border-color: #00c9a7;
          .filter-reset-icon {
            color: #00c9a7;
          }
        }
      }
    }
  }

  .filter-container {
    width: 100%;

    .filter-scroll {
      white-space: nowrap;

      .filter-list {
        display: inline-flex;
        gap: 16rpx;
        padding: 0 8rpx;
        min-width: 100%;

        .filter-item {
          display: flex;
          gap: 8rpx;
          align-items: center;
          padding: 16rpx 24rpx;
          font-size: 26rpx;
          font-weight: 500;
          color: #64748b;
          white-space: nowrap;
          background: white;
          border: 2rpx solid #e2e8f0;
          border-radius: 32rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
          transition: all 0.3s ease;
          flex-shrink: 0;
          min-height: 64rpx;
          box-sizing: border-box;

          &:active {
            transform: scale(0.98);
          }

          &.filter-active {
            color: white;
            background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
            border-color: #00c9a7;
            box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);

            .filter-item-icon {
              color: white;
            }
          }

          &.filter-more {
            color: #00c9a7;
            border-color: #00c9a7;
            border-style: dashed;
            min-width: 80rpx;
            justify-content: center;

            .filter-item-icon {
              color: #00c9a7;
            }
          }

          .filter-item-icon {
            font-size: 28rpx;
            color: #94a3b8;
            flex-shrink: 0;
          }

          .filter-text {
            flex-shrink: 0;
            line-height: 1.2;
          }
        }
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      width: 90%;
      max-width: 600rpx;
      max-height: 80vh;
      background: white;
      border-radius: 24rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32rpx 32rpx 24rpx;
        border-bottom: 1rpx solid #e2e8f0;

        .modal-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #1e293b;
        }

        .modal-close-btn {
          padding: 8rpx;
          background: transparent;
          border: none;

          .modal-close-icon {
            font-size: 32rpx;
            color: #64748b;
          }
        }
      }

      .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 24rpx 32rpx;

        .modal-options {
          .modal-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20rpx 0;
            border-bottom: 1rpx solid #f1f5f9;
            transition: all 0.2s ease;

            &:last-child {
              border-bottom: none;
            }

            &:active {
              background: #f8fafc;
            }

            &.filter-active {
              .modal-option-content {
                .modal-option-icon {
                  color: #00c9a7;
                }

                .modal-option-text {
                  color: #00c9a7;
                  font-weight: 600;
                }
              }

              .modal-check-icon {
                color: #00c9a7;
              }
            }

            .modal-option-content {
              display: flex;
              align-items: center;
              gap: 16rpx;

              .modal-option-icon {
                font-size: 32rpx;
                color: #64748b;
              }

              .modal-option-text {
                font-size: 28rpx;
                color: #1e293b;
              }
            }

            .modal-check-icon {
              font-size: 32rpx;
              color: transparent;
            }
          }
        }
      }

      .modal-footer {
        display: flex;
        gap: 16rpx;
        padding: 24rpx 32rpx 32rpx;
        border-top: 1rpx solid #e2e8f0;

        .modal-reset-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          padding: 20rpx;
          background: #f8fafc;
          border: 2rpx solid #e2e8f0;
          border-radius: 16rpx;
          transition: all 0.2s ease;

          &:active {
            background: #f1f5f9;
          }

          .modal-btn-icon {
            font-size: 24rpx;
            color: #64748b;
          }

          .modal-btn-text {
            font-size: 26rpx;
            color: #64748b;
          }
        }

        .modal-confirm-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20rpx;
          background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
          border: none;
          border-radius: 16rpx;
          transition: all 0.2s ease;

          &:active {
            transform: scale(0.98);
          }

          .modal-btn-text {
            font-size: 26rpx;
            font-weight: 500;
            color: white;
          }
        }
      }
    }
  }
}
</style>
