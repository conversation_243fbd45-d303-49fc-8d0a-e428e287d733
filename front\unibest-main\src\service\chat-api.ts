/**
 * @description 聊天API服务 - 对接后端AgentController
 * <AUTHOR>
 */

import { http, httpPost, httpGet } from '@/utils/http'
import { upload } from '@/utils/api'

/**
 * @description 聊天请求数据结构
 */
export interface ChatRequestDto {
  /** 消息内容 */
  message: string
  /** 会话ID（可选，新会话时为空） */
  sessionId?: string
  /** 代理类型 */
  agentType?: string
  /** 附件列表 */
  attachments?: Array<{
    type: 'image' | 'file' | 'voice'
    url: string
    name: string
    size?: number
    metadata?: Record<string, any>
  }>
  /** 是否需要流式响应 */
  stream?: boolean
}

/**
 * @description 聊天响应数据结构
 */
export interface ChatResponseDto {
  /** 回复消息内容 */
  message: string
  /** 会话ID */
  sessionId: string
  /** 消息ID */
  messageId: string
  /** 响应时间戳 */
  timestamp: number
  /** 额外元数据 */
  metadata?: Record<string, any>
}

/**
 * @description 聊天会话数据结构
 */
export interface ChatSession {
  /** 会话ID */
  id: string
  /** 会话标题 */
  title: string
  /** 使用的代理类型 */
  agentType: string
  /** 是否归档 */
  archived: boolean
  /** 创建时间 */
  createdAt: number
  /** 更新时间 */
  updatedAt: number
  /** 消息数量 */
  messageCount?: number
}

/**
 * @description 聊天消息数据结构
 */
export interface ChatMessage {
  /** 消息ID */
  id: string
  /** 会话ID */
  sessionId: string
  /** 消息角色 */
  role: 'user' | 'assistant'
  /** 消息内容 */
  content: string
  /** 创建时间 */
  createdAt: number
  /** 附件列表 */
  attachments?: Array<{
    type: 'image' | 'file' | 'voice'
    url: string
    name: string
    size?: number
    metadata?: Record<string, any>
  }>
}

/**
 * @description AI代理数据结构
 */
export interface Agent {
  /** 代理ID */
  id: string
  /** 代理类型 */
  type: string
  /** 代理名称 */
  name: string
  /** 代理描述 */
  description: string
  /** 代理图标 */
  icon: string
  /** 代理颜色 */
  color: string
  /** 是否启用 */
  enabled: boolean
  /** 快速操作列表 */
  quickActions?: QuickAction[]
}

/**
 * @description 快速操作数据结构
 */
export interface QuickAction {
  /** 操作ID */
  id: string
  /** 操作标题 */
  title: string
  /** 操作描述 */
  description: string
  /** 操作图标 */
  icon: string
  /** 操作模板 */
  template: string
}

/**
 * @description 文件上传结果
 */
export interface FileUploadResult {
  /** 文件URL */
  url: string
  /** 文件名 */
  name: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * @description 语音转文字结果
 */
export interface SpeechToTextResult {
  /** 识别的文字 */
  text: string
  /** 识别语言 */
  language?: string
  /** 识别置信度 */
  confidence?: number
  /** 语音时长 */
  duration?: number
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * @description Ollama服务状态
 */
export interface OllamaServiceStatus {
  /** 服务是否可用 */
  available: boolean
  /** 服务版本 */
  version?: string
  /** 错误信息 */
  error?: string
}

/**
 * @description Ollama模型信息
 */
export interface OllamaModelInfo {
  /** 模型名称 */
  name: string
  /** 模型大小 */
  size?: string
  /** 模型描述 */
  description?: string
  /** 支持的功能 */
  capabilities?: string[]
}

/**
 * @description 聊天API服务类
 */
export class ChatApiService {
  /**
   * @description 发送聊天消息（同步响应）
   * @param request 聊天请求
   * @returns 聊天响应
   */
  static async sendMessage(request: ChatRequestDto): Promise<any> {
    const response = await httpPost<any>('/app/chat/send', request)
    return response.data
  }

  /**
   * @description 发送聊天消息（流式响应）
   * @param request 聊天请求
   * @param onMessage 消息回调
   * @param onError 错误回调
   * @param onComplete 完成回调
   * @returns SSE连接控制器
   */
  static sendMessageStream(
    request: ChatRequestDto,
    onMessage: (data: string) => void,
    onError: (error: string) => void,
    onComplete: () => void,
  ): { close: () => void } {
    // H5环境下使用真实的SSE
    try {
      // 获取认证token
      const token = uni.getStorageSync('token')
      const baseUrl = 'http://localhost:8080'
      const sseUrl = `${baseUrl}/app/chat/send/stream?${new URLSearchParams({
        message: request.message,
        sessionId: request.sessionId || '',
        agentType: request.agentType || 'general',
        satoken: token,
      }).toString()}`

      // 创建EventSource连接
      const eventSource = new EventSource(sseUrl)
      let isCompleted = false // 标记是否已完成

      // 监听token事件
      eventSource.addEventListener('token', (event) => {
        try {
          const data = JSON.parse(event.data)
          if (data.token) {
            onMessage(data.token)
          }
        } catch (e) {
          console.warn('解析token事件失败:', e)
          // 降级处理：直接使用原始数据
          onMessage(event.data)
        }
      })

      // 监听完成事件
      eventSource.addEventListener('complete', (event) => {
        isCompleted = true
        onComplete()
        eventSource.close()
      })

      // 监听错误事件
      eventSource.addEventListener('error', (event) => {
        try {
          // 错误事件的data属性在MessageEvent中才有
          const messageEvent = event as MessageEvent
          if (messageEvent.data) {
            const data = JSON.parse(messageEvent.data)
            onError(data.data || data.message || '服务器错误')
          } else {
            onError('服务器错误')
          }
        } catch (e) {
          onError('服务器错误')
        }
        eventSource.close()
      })

      // 监听会话事件
      eventSource.addEventListener('session', (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('会话创建:', data.sessionId)
        } catch (e) {
          console.warn('解析会话事件失败:', e)
        }
      })

      // 监听开始事件
      eventSource.addEventListener('start', (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('流式响应开始:', data.messageId)
        } catch (e) {
          console.warn('解析开始事件失败:', e)
        }
      })

      // 通用消息处理器（作为备份）
      eventSource.onmessage = (event) => {
        console.log('收到通用SSE消息:', event.data)
        try {
          const data = JSON.parse(event.data)
          if (data.type === 'token' && data.token) {
            onMessage(data.token)
          } else if (data.type === 'complete' || data.type === 'done') {
            isCompleted = true
            onComplete()
            eventSource.close()
          } else if (data.type === 'error') {
            onError(data.data || data.message || '服务器错误')
            eventSource.close()
          } else if (data.type === 'message' || data.type === 'content') {
            onMessage(data.data || data.content || data.message || event.data)
          }
        } catch (e) {
          // 如果不是JSON格式，直接作为消息内容处理
          onMessage(event.data)
        }
      }

      // 错误处理器
      eventSource.onerror = (event) => {
        console.error('SSE连接错误:', event)

        // 如果已经正常完成，不报告错误
        if (isCompleted) {
          console.log('SSE连接已正常完成，忽略关闭事件')
          return
        }

        // 检查连接状态
        if (eventSource.readyState === EventSource.CLOSED) {
          console.log('SSE连接已关闭')
          if (!isCompleted) {
            onError('连接意外断开')
          }
        } else if (eventSource.readyState === EventSource.CONNECTING) {
          console.log('SSE连接重连中...')
        } else {
          onError('连接错误')
        }

        if (!isCompleted) {
          eventSource.close()
        }
      }

      return {
        close: () => {
          isCompleted = true
          eventSource.close()
        },
      }
    } catch (error) {
      console.error('创建SSE连接失败:', error)
    }
  }

  /**
   * @description 创建新会话
   * @param agentType 代理类型
   * @param title 会话标题（可选）
   * @returns 会话信息
   */
  static async createSession(agentType: string, title?: string): Promise<ChatSession> {
    const params: Record<string, string> = { agentType }
    if (title) params.title = title

    const response = await httpPost<ChatSession>('/app/chat/session/create', null, params)
    return response.data
  }

  /**
   * @description 获取用户会话列表
   * @param pageNum 页码
   * @param pageSize 每页大小
   * @returns 会话分页结果
   */
  static async getUserSessions(pageNum: number = 1, pageSize: number = 20) {
    const response = await httpGet('/app/chat/sessions', { pageNum, pageSize })
    return response.data
  }

  /**
   * @description 获取会话详情
   * @param sessionId 会话ID
   * @returns 会话详情
   */
  static async getSessionDetail(sessionId: string): Promise<ChatSession> {
    const response = await httpGet<ChatSession>(`/app/chat/session/${sessionId}`)
    return response.data
  }

  /**
   * @description 获取会话消息列表
   * @param sessionId 会话ID
   * @param pageNum 页码
   * @param pageSize 每页大小
   * @returns 消息分页结果
   */
  static async getSessionMessages(sessionId: string, pageNum: number = 1, pageSize: number = 50) {
    const response = await httpGet(`/app/chat/session/${sessionId}/messages`, { pageNum, pageSize })
    return response.data
  }

  /**
   * @description 删除会话
   * @param sessionId 会话ID
   * @returns 操作结果
   */
  static async deleteSession(sessionId: string): Promise<boolean> {
    const response = await http.delete(`/app/chat/session/${sessionId}`)
    return response.code === 200
  }

  /**
   * @description 清空会话消息
   * @param sessionId 会话ID
   * @returns 操作结果
   */
  static async clearSessionMessages(sessionId: string): Promise<boolean> {
    const response = await httpPost(`/app/chat/session/${sessionId}/clear`)
    return response.code === 200
  }

  /**
   * @description 更新会话标题
   * @param sessionId 会话ID
   * @param title 新标题
   * @returns 操作结果
   */
  static async updateSessionTitle(sessionId: string, title: string): Promise<boolean> {
    const response = await http.put(`/app/chat/session/${sessionId}/title`, null, { title })
    return response.code === 200
  }

  /**
   * @description 归档/取消归档会话
   * @param sessionId 会话ID
   * @param archived 是否归档
   * @returns 操作结果
   */
  static async archiveSession(sessionId: string, archived: boolean): Promise<boolean> {
    const response = await http.put(`/app/chat/session/${sessionId}/archive`, null, { archived })
    return response.code === 200
  }

  /**
   * @description 获取用户聊天统计信息
   * @returns 统计信息
   */
  static async getUserChatStats(): Promise<Record<string, any>> {
    const response = await httpGet('/app/chat/stats')
    return response.data
  }

  // ========== Agent 相关接口 ==========

  /**
   * @description 获取所有启用的代理列表
   * @returns 代理列表
   */
  static async getEnabledAgents(): Promise<Agent[]> {
    const response = await httpGet<Agent[]>('/app/chat/agents')
    return response.data
  }

  /**
   * @description 根据类型获取代理信息
   * @param agentType 代理类型
   * @returns 代理信息
   */
  static async getAgentByType(agentType: string): Promise<Agent> {
    const response = await httpGet<Agent>(`/app/chat/agent/${agentType}`)
    return response.data
  }

  /**
   * @description 获取代理的快速操作列表
   * @param agentType 代理类型
   * @returns 快速操作列表
   */
  static async getQuickActions(agentType: string): Promise<QuickAction[]> {
    const response = await httpGet<QuickAction[]>(`/app/chat/agent/${agentType}/quick-actions`)
    return response.data
  }

  // ========== 文件上传相关接口 ==========

  /**
   * @description 上传聊天附件
   * @param file 文件路径或File对象
   * @param type 文件类型
   * @returns 上传结果
   */
  static async uploadFile(file: string | File, type: string = 'file'): Promise<FileUploadResult> {
    let filePath: string

    // 处理不同类型的文件输入
    if (typeof file === 'string') {
      filePath = file
    } else {
      // H5环境下的File对象处理
      // 这里需要根据实际情况调整
      filePath = file.name
    }

    const response = await upload('/app/chat/upload', filePath, 'file', { type })
    return response.data
  }

  /**
   * @description 语音转文字
   * @param audioFile 音频文件路径
   * @returns 转换结果
   */
  static async speechToText(audioFile: string): Promise<SpeechToTextResult> {
    const response = await upload('/app/chat/speech-to-text', audioFile, 'audio')
    return response.data
  }

  // ========== Ollama 服务相关接口 ==========

  /**
   * @description 获取可用模型列表
   * @returns 模型列表
   */
  static async getAvailableModels(): Promise<string[]> {
    const response = await httpGet<string[]>('/app/chat/ollama/models')
    return response.data
  }

  /**
   * @description 检查Ollama服务状态
   * @returns 服务状态
   */
  static async checkOllamaStatus(): Promise<OllamaServiceStatus> {
    const response = await httpGet<OllamaServiceStatus>('/app/chat/ollama/status')
    return response.data
  }

  /**
   * @description 获取模型信息
   * @param modelName 模型名称
   * @returns 模型信息
   */
  static async getModelInfo(modelName: string): Promise<OllamaModelInfo> {
    const response = await httpGet<OllamaModelInfo>(`/app/chat/ollama/model/${modelName}/info`)
    return response.data
  }
}

// 导出单例实例
export const chatApi = ChatApiService
