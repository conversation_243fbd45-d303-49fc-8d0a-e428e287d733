<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

/**
 * @description 情绪分析组件
 * 在面试过程中实时显示用户的情绪分析结果
 */
const props = defineProps({
  // 是否处于录制状态
  isRecording: {
    type: Boolean,
    default: false
  },
  // 是否自动更新情绪数据
  autoUpdate: {
    type: Boolean,
    default: true
  },
  // 更新间隔(毫秒)
  interval: {
    type: Number,
    default: 3000
  }
})

// 组件事件
const emit = defineEmits(['update'])

// 情绪数据
const emotionData = ref<Record<string, number>>({
  happy: 0,
  neutral: 0,
  sad: 0,
  angry: 0,
  surprised: 0,
  fear: 0,
  disgusted: 0,
})

// 主要情绪
const primaryEmotion = computed(() => {
  let maxEmotion = 'neutral'
  let maxValue = 0
  
  Object.entries(emotionData.value).forEach(([emotion, value]) => {
    if (value > maxValue) {
      maxValue = value
      maxEmotion = emotion
    }
  })
  
  return {
    type: maxEmotion,
    value: maxValue
  }
})

// 定时器
let updateTimer: ReturnType<typeof setInterval> | null = null

/**
 * @description 获取情绪标签
 * @param emotion 情绪类型
 */
const getEmotionLabel = (emotion: string): string => {
  const labels: Record<string, string> = {
    happy: '开心',
    neutral: '平静',
    sad: '悲伤',
    angry: '生气',
    surprised: '惊讶',
    fear: '恐惧',
    disgusted: '厌恶',
    contempt: '轻蔑',
    confused: '困惑',
  }
  return labels[emotion] || emotion
}

/**
 * @description 获取情绪颜色
 * @param emotion 情绪类型
 */
const getEmotionColor = (emotion: string): string => {
  const colors: Record<string, string> = {
    happy: '#52C41A',
    neutral: '#1890FF',
    sad: '#722ED1',
    angry: '#F5222D',
    surprised: '#FA8C16',
    fear: '#EB2F96',
    disgusted: '#FA541C',
    contempt: '#13C2C2',
    confused: '#FAAD14',
  }
  return colors[emotion] || '#999'
}

/**
 * @description 更新情绪数据(已移除模拟逻辑，现在通过WebSocket接收真实数据)
 */
const updateEmotionData = () => {
  // 此方法已废弃，情绪数据现在通过WebSocket从服务端接收
  console.log('情绪数据现在通过WebSocket接收，不再使用模拟数据')

  // 发送更新事件
  emit('update', {
    data: { ...emotionData.value },
    primary: primaryEmotion.value
  })
}

/**
 * @description 设置情绪数据
 * @param data 情绪数据对象
 */
const setEmotionData = (data: Record<string, number>) => {
  emotionData.value = { ...data }
}

// 暴露方法给父组件
defineExpose({
  updateEmotionData,
  setEmotionData,
  getEmotionLabel,
  getEmotionColor,
  emotionData,
  primaryEmotion
})

// 生命周期钩子
onMounted(() => {
  console.log('EmotionAnalysis组件已挂载，等待WebSocket数据')

  // 初始化情绪数据为默认值
  emotionData.value = {
    happy: 0,
    neutral: 100,
    sad: 0,
    angry: 0,
    surprised: 0,
    fear: 0,
    disgusted: 0,
  }
})

onUnmounted(() => {
  // 清理定时器
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
})

// 监听录制状态变化
watch(() => props.isRecording, (newValue) => {
  console.log('录制状态变化:', newValue ? '开始录制' : '停止录制')
})
</script>

<template>
  <div class="emotion-analysis">
    <div class="analysis-header">
      <i class="fa fa-smile detection-icon"></i>
      <span class="detection-title">情绪分析</span>
    </div>
    <div class="emotion-indicators">
      <div class="emotion-item" v-for="(value, emotion) in emotionData" :key="emotion">
        <span class="emotion-label">{{ getEmotionLabel(emotion) }}</span>
        <div class="emotion-bar-container">
          <div
            class="emotion-bar"
            :style="{ width: value + '%', background: getEmotionColor(emotion) }"
          ></div>
        </div>
        <span class="emotion-value">{{ value }}%</span>
      </div>
    </div>
    <div class="primary-emotion" v-if="primaryEmotion.value > 0">
      <span class="primary-label">主要情绪:</span>
      <span class="primary-value" :style="{ color: getEmotionColor(primaryEmotion.type) }">
        {{ getEmotionLabel(primaryEmotion.type) }} ({{ primaryEmotion.value }}%)
      </span>
    </div>
  </div>
</template>

<style scoped>
.emotion-analysis {
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.analysis-header {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-bottom: 10px;
}

.detection-icon {
  font-size: 16px;
  color: #00c9a7;
}

.detection-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.emotion-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.emotion-item {
  display: flex;
  gap: 6px;
  align-items: center;
}

.emotion-label {
  min-width: 40px;
  font-size: 12px;
  color: #666;
}

.emotion-bar-container {
  flex: 1;
  height: 6px;
  overflow: hidden;
  background: #f0f0f0;
  border-radius: 3px;
}

.emotion-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.emotion-value {
  min-width: 30px;
  font-size: 12px;
  font-weight: 600;
  color: #333;
  text-align: right;
}

.primary-emotion {
  display: flex;
  gap: 6px;
  align-items: center;
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px dashed #eee;
}

.primary-label {
  font-size: 12px;
  color: #666;
}

.primary-value {
  font-size: 12px;
  font-weight: 600;
}
</style> 