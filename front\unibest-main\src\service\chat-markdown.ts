/**
 * @description 聊天API服务
 * 提供聊天相关的API接口，包含防抖和请求合并优化
 */

export interface ChatMessage {
  message: string
  sessionId: string
  attachments?: Array<{
    type: 'image' | 'file'
    url: string
    name: string
  }>
}

export interface ChatResponse {
  success: boolean
  data: {
    message: string
    sessionId: string
  }
  error?: string
}

/**
 * @description 请求防抖器
 */
class RequestDebouncer {
  private timers = new Map<string, number>()
  
  /**
   * @description 防抖执行函数
   * @param key 防抖键
   * @param fn 执行函数
   * @param delay 延迟时间
   */
  debounce<T>(key: string, fn: () => Promise<T>, delay: number = 300): Promise<T> {
    return new Promise((resolve, reject) => {
      // 清除之前的定时器
      const existingTimer = this.timers.get(key)
      if (existingTimer) {
        clearTimeout(existingTimer)
      }
      
      // 设置新的定时器
      const timer = setTimeout(async () => {
        try {
          const result = await fn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.timers.delete(key)
        }
      }, delay)
      
      this.timers.set(key, timer)
    })
  }
}

/**
 * @description 请求批处理器
 */
class RequestBatcher {
  private batches = new Map<string, {
    requests: Array<{
      data: ChatMessage
      resolve: (value: ChatResponse) => void
      reject: (error: any) => void
    }>
    timer: number
  }>()
  
  /**
   * @description 添加请求到批次
   * @param batchKey 批次键
   * @param data 请求数据
   * @param delay 批处理延迟
   */
  add(batchKey: string, data: ChatMessage, delay: number = 50): Promise<ChatResponse> {
    return new Promise((resolve, reject) => {
      let batch = this.batches.get(batchKey)
      
      if (!batch) {
        batch = {
          requests: [],
          timer: 0
        }
        this.batches.set(batchKey, batch)
      }
      
      // 添加请求到批次
      batch.requests.push({ data, resolve, reject })
      
      // 清除之前的定时器
      if (batch.timer) {
        clearTimeout(batch.timer)
      }
      
      // 设置新的批处理定时器
      batch.timer = setTimeout(() => {
        this.flush(batchKey)
      }, delay)
    })
  }
  
  /**
   * @description 执行批次请求
   * @param batchKey 批次键
   */
  private async flush(batchKey: string) {
    const batch = this.batches.get(batchKey)
    if (!batch || batch.requests.length === 0) return
    
    const requests = [...batch.requests]
    this.batches.delete(batchKey)
    
    // 并发执行所有请求
    await Promise.allSettled(
      requests.map(async ({ data, resolve, reject }) => {
        try {
          const result = await this.executeRequest(data)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
    )
  }
  
  /**
   * @description 执行单个请求
   * @param data 请求数据
   */
  private async executeRequest(data: ChatMessage): Promise<ChatResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    // 模拟AI回复
    const responses = [
      "我理解您的问题，让我为您详细分析一下...",
      "根据您提供的信息，我建议采取以下几个步骤...",
      "这是一个很好的问题，让我从多个角度来解答...",
      "基于我的分析，以下是一些实用的建议...",
      "您提到的这个情况确实需要注意，建议您..."
    ]
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)]
    
    return {
      success: true,
      data: {
        message: randomResponse,
        sessionId: data.sessionId
      }
    }
  }
}

// 全局实例
const debouncer = new RequestDebouncer()
const batcher = new RequestBatcher()

/**
 * @description 聊天API接口
 */
export const chatApi = {
  /**
   * @description 发送消息
   * @param data 消息数据
   * @returns Promise<ChatResponse>
   */
  async sendMessage(data: ChatMessage): Promise<ChatResponse> {
    try {
      // 使用防抖优化
      return await debouncer.debounce(
        `send-${data.sessionId}`,
        async () => {
          // 模拟API调用
          const response = await fetch('/api/chat/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          
          return await response.json()
        },
        300
      )
    } catch (error) {
      console.error('发送消息失败:', error)
      
      // 降级处理：返回模拟响应
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const mockResponses = [
        "感谢您的提问，我正在为您分析相关信息...",
        "根据您的描述，我建议您可以考虑以下几个方面...",
        "这个问题确实值得深入探讨，让我为您详细解答...",
        "基于我的理解，以下是一些可能有帮助的建议..."
      ]
      
      return {
        success: true,
        data: {
          message: mockResponses[Math.floor(Math.random() * mockResponses.length)],
          sessionId: data.sessionId
        }
      }
    }
  },
  
  /**
   * @description 批量发送消息
   * @param messages 消息列表
   * @returns Promise<ChatResponse[]>
   */
  async batchSendMessages(messages: ChatMessage[]): Promise<ChatResponse[]> {
    try {
      const promises = messages.map(message => 
        batcher.add('chat-batch', message, 100)
      )
      
      return await Promise.all(promises)
    } catch (error) {
      console.error('批量发送消息失败:', error)
      throw error
    }
  },
  
  /**
   * @description 上传附件
   * @param file 文件对象
   * @returns Promise<{url: string, name: string}>
   */
  async uploadFile(file: File): Promise<{url: string, name: string}> {
    try {
      // 模拟文件上传
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 返回模拟的文件URL
      return {
        url: URL.createObjectURL(file),
        name: file.name
      }
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  },
  
  /**
   * @description 获取会话历史
   * @param sessionId 会话ID
   * @param limit 限制数量
   * @returns Promise<Message[]>
   */
  async getSessionHistory(sessionId: string, limit: number = 50): Promise<any[]> {
    try {
      // 使用防抖优化
      return await debouncer.debounce(
        `history-${sessionId}`,
        async () => {
          const response = await fetch(`/api/chat/sessions/${sessionId}/messages?limit=${limit}`)
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          
          return await response.json()
        },
        500
      )
    } catch (error) {
      console.error('获取会话历史失败:', error)
      return []
    }
  }
}
