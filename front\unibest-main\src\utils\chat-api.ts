/**
 * @description 聊天API工具类
 * <AUTHOR>
 */

import { http, httpSSE, SSEController } from './http'

// 聊天请求DTO
export interface ChatRequestDto {
  message: string
  sessionId?: string
  agentType?: string
}

// 聊天响应DTO
export interface ChatResponseDto {
  messageId: string
  content: string
  sessionId: string
  timestamp: number
}

/**
 * 聊天API类
 */
export class ChatApi {
  /**
   * 发送聊天消息（普通请求）
   * @param request 聊天请求
   * @returns Promise<ChatResponseDto>
   */
  static async sendMessage(request: ChatRequestDto): Promise<ChatResponseDto> {
    return http.post<ChatResponseDto>('/app/chat/send', request)
  }

  /**
   * 发送聊天消息（流式响应）
   * @param request 聊天请求
   * @param onMessage 消息回调
   * @param onError 错误回调
   * @param onComplete 完成回调
   * @returns SSE连接控制器
   */
  static sendMessageStream(
    request: ChatRequestDto,
    onMessage: (data: string) => void,
    onError: (error: string) => void,
    onComplete: () => void,
  ): SSEController {
    return httpSSE({
      url: '/app/chat/send/stream',
      query: {
        message: request.message,
        sessionId: request.sessionId || '',
        agentType: request.agentType || 'general',
        satoken: uni.getStorageSync('token'),
      },
      onMessage,
      onError,
      onComplete,
      // 可选：自定义事件监听器
      eventListeners: {
        session: (event) => {
          try {
            const data = JSON.parse(event.data)
            console.log('会话创建:', data.sessionId)
          } catch (e) {
            console.warn('解析会话事件失败:', e)
          }
        },
        start: (event) => {
          try {
            const data = JSON.parse(event.data)
            console.log('流式响应开始:', data.messageId)
          } catch (e) {
            console.warn('解析开始事件失败:', e)
          }
        },
      },
    })
  }

  /**
   * 获取聊天历史
   * @param sessionId 会话ID
   * @returns Promise<ChatResponseDto[]>
   */
  static async getChatHistory(sessionId: string): Promise<ChatResponseDto[]> {
    return http.get<ChatResponseDto[]>('/app/chat/history', { sessionId })
  }

  /**
   * 创建新会话
   * @returns Promise<{sessionId: string}>
   */
  static async createSession(): Promise<{ sessionId: string }> {
    return http.post<{ sessionId: string }>('/app/chat/session')
  }

  /**
   * 删除会话
   * @param sessionId 会话ID
   * @returns Promise<void>
   */
  static async deleteSession(sessionId: string): Promise<void> {
    return http.delete<void>('/app/chat/session', { sessionId })
  }
}
