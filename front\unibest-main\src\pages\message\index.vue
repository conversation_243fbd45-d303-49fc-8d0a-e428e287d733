
<template>
  <view class="message-page">
    <!-- 自定义头部区域 -->
    <view class="custom-header">
      <HeadBar title="消息中心" :show-back="true" />
      <!-- 搜索按钮 -->
      <view class="search-button" @click="showSearchBox">
        <text class="i-mdi-magnify search-button-icon"></text>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view v-if="showSearch" class="search-bar">
      <view class="search-input-wrapper">
        <text class="i-mdi-magnify search-icon"></text>
        <input
          v-model="searchKeyword"
          class="search-input"
          type="text"
          placeholder="搜索消息内容..."
          confirm-type="search"
          @confirm="handleSearch"
        />
        <text v-if="searchKeyword" class="i-mdi-close search-clear" @click="clearSearch"></text>
        <text class="i-mdi-close search-close" @click="hideSearchBox"></text>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="action-left">
        <text class="action-button" @click="markAllAsRead">
          <text class="i-mdi-check-all action-icon"></text>
          全部已读
        </text>
      </view>
      <view class="action-right">
        <text class="message-count">共 {{ messages.length }} 条消息</text>
      </view>
    </view>

    <!-- 标签栏 -->
    <view class="tab-bar">
      <scroll-view class="tab-scroll" scroll-x>
        <view class="tab-list">
          <view
            v-for="tab in tabs"
            :key="tab.key"
            class="tab-item"
            :class="{ active: selectedTab === tab.key }"
            @click="switchTab(tab.key)"
          >
            <text :class="tab.icon" class="tab-icon"></text>
            <text class="tab-label">{{ tab.label }}</text>
            <view v-if="tab.key === 'all' && unreadCount > 0" class="tab-badge">
              {{ unreadCount > 99 ? '99+' : unreadCount }}
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 消息列表 -->
    <scroll-view
      class="message-list"
      scroll-y
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      refresher-enabled
    >
      <!-- 加载骨架屏 -->
      <view v-if="loading" class="skeleton-container">
        <view v-for="i in 5" :key="i" class="skeleton-item">
          <view class="skeleton-avatar"></view>
          <view class="skeleton-content">
            <view class="skeleton-title"></view>
            <view class="skeleton-text"></view>
            <view class="skeleton-text skeleton-text-short"></view>
          </view>
        </view>
      </view>

      <!-- 空状态显示 -->
      <view v-else-if="filteredMessages.length === 0" class="empty-state">
        <text class="i-mdi-message-outline empty-icon"></text>
        <text class="empty-text">
          {{ searchKeyword ? '未找到相关消息' : '暂无消息' }}
        </text>
        <text class="empty-desc">
          {{ searchKeyword ? '尝试使用其他关键词搜索' : '当有新消息时，会在这里显示' }}
        </text>
        <view v-if="searchKeyword" class="empty-action" @click="hideSearchBox">
          <text class="i-mdi-refresh empty-action-icon"></text>
          <text class="empty-action-text">清空搜索</text>
        </view>
      </view>

      <!-- 消息列表 -->
      <view v-else class="message-items">
        <view
          v-for="(message, index) in filteredMessages"
          :key="message.id"
          class="message-item"
          :class="{ unread: !message.isRead }"
          :style="{ animationDelay: `${index * 0.05}s` }"
          @click="handleMessageClick(message)"
          @longpress="onLongPress(message)"
        >
          <!-- 消息图标 -->
          <view class="message-avatar" :style="getMessageAvatarStyle(message.type)">
            <text :class="messageIcons[message.type]" class="message-avatar-icon"></text>
          </view>

          <!-- 消息内容 -->
          <view class="message-content">
            <view class="message-header">
              <view class="message-title-wrapper">
                <text class="message-title">{{ message.title }}</text>
                <text class="message-type">{{ getMessageTypeName(message.type) }}</text>
              </view>
              <text class="message-time">{{ formatTime(message.time) }}</text>
            </view>
            <text class="message-text">{{ message.content }}</text>
            <view v-if="message.actionUrl" class="message-action">
              <text class="i-mdi-arrow-right message-action-icon"></text>
              <text class="message-action-text">点击查看详情</text>
            </view>
          </view>

          <!-- 未读标识 -->
          <view v-if="!message.isRead" class="unread-dot"></view>
        </view>
      </view>
    </scroll-view>

    <!-- Toast Modal 组件 -->
    <!-- 全部标记为已读确认 -->
    <ToastModal
      :visible="showMarkAllReadModal"
      title="确认操作"
      :content="`确定要将${unreadCount}条未读消息标记为已读吗？`"
      type="confirm"
      confirm-text="确定"
      cancel-text="取消"
      @update:visible="showMarkAllReadModal = $event"
      @confirm="confirmMarkAllAsRead"
    />

    <!-- 消息详情展示 -->
    <ToastModal
      :visible="showMessageDetailModal"
      :title="currentMessage?.title || ''"
      :content="currentMessage?.content || ''"
      type="info"
      :show-cancel="false"
      confirm-text="知道了"
      @update:visible="showMessageDetailModal = $event"
    />

    <!-- 删除消息确认 -->
    <ToastModal
      :visible="showDeleteConfirmModal"
      title="确认删除"
      content="确定要删除这条消息吗？删除后无法恢复。"
      type="warning"
      confirm-text="删除"
      confirm-type="danger"
      cancel-text="取消"
      @update:visible="showDeleteConfirmModal = $event"
      @confirm="confirmDeleteMessage"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'

// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import ToastModal from '@/components/ToastModal.vue'

// 消息类型定义
interface Message {
  id: string
  type: 'system' | 'interview' | 'study' | 'achievement'
  title: string
  content: string
  time: string
  isRead: boolean
  avatar?: string
  actionUrl?: string
}

// 演示数据
const demoMessages: Message[] = [
  {
    id: '1',
    type: 'system',
    title: '系统更新通知',
    content: '智能面试助手已更新至最新版本，新增AI语音评测功能，快来体验吧！',
    time: '2025-01-15 10:30',
    isRead: false,
  },
  {
    id: '2',
    type: 'interview',
    title: '面试结果已出',
    content: '您的前端工程师模拟面试已完成，总分85分，快来查看详细报告吧！',
    time: '2025-01-15 09:15',
    isRead: false,
    actionUrl: '/pages/interview/result?id=123',
  },
  {
    id: '3',
    type: 'study',
    title: '学习计划提醒',
    content: '今日学习任务：JavaScript高级特性练习，还有2个题目未完成。',
    time: '2025-01-15 08:00',
    isRead: true,
    actionUrl: '/pages/learning/practice',
  },
  {
    id: '4',
    type: 'achievement',
    title: '恭喜获得新成就',
    content: '连续学习7天！您已获得"坚持不懈"徽章，继续保持哦！',
    time: '2025-01-14 20:00',
    isRead: true,
  },
  {
    id: '5',
    type: 'system',
    title: '维护通知',
    content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用。',
    time: '2025-01-14 16:30',
    isRead: true,
  },
]

// 响应式数据
const messages = ref<Message[]>([])
const refreshing = ref(false)
const selectedTab = ref('all')
const searchKeyword = ref('')
const showSearch = ref(false)
const loading = ref(true) // 添加加载状态

// Modal控制状态
const showMarkAllReadModal = ref(false)
const showMessageDetailModal = ref(false)
const showDeleteConfirmModal = ref(false)
const currentMessage = ref<Message | null>(null)
const currentDeleteMessageId = ref('')

// 标签配置
const tabs = [
  { key: 'all', label: '全部', icon: 'i-mdi-message-text' },
  { key: 'system', label: '系统', icon: 'i-mdi-cog' },
  { key: 'interview', label: '面试', icon: 'i-mdi-account-tie' },
  { key: 'study', label: '学习', icon: 'i-mdi-book-open' },
  { key: 'achievement', label: '成就', icon: 'i-mdi-trophy' },
]

// 消息类型图标映射
const messageIcons = {
  system: 'i-mdi-cog',
  interview: 'i-mdi-account-tie',
  study: 'i-mdi-book-open',
  achievement: 'i-mdi-trophy',
}

// 消息类型颜色映射
const messageColors = {
  system: '#00C9A7',
  interview: '#4FD1C7',
  study: '#7C3AED',
  achievement: '#F59E0B',
}

// 计算属性：过滤后的消息
const filteredMessages = computed(() => {
  let filtered = messages.value

  // 按类型过滤
  if (selectedTab.value !== 'all') {
    filtered = filtered.filter((msg) => msg.type === selectedTab.value)
  }

  // 按搜索关键词过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    filtered = filtered.filter(
      (msg) =>
        msg.title.toLowerCase().includes(keyword) || msg.content.toLowerCase().includes(keyword),
    )
  }

  return filtered
})

// 计算属性：未读消息数量
const unreadCount = computed(() => {
  return messages.value.filter((msg) => !msg.isRead).length
})

/**
 * @description 初始化消息数据
 */
const initMessages = async () => {
  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 返回模拟数据
    messages.value = [...demoMessages]
  } catch (error) {
    console.error('初始化消息失败', error)
    uni.showToast({
      title: '加载消息失败',
      icon: 'none',
    })
  } finally {
    // 添加一个小延迟，使骨架屏动画效果更明显
    setTimeout(() => {
      loading.value = false
    }, 300)
  }
}

/**
 * @description 切换标签
 * @param tab 标签key
 */
const switchTab = async (tab: string) => {
  selectedTab.value = tab
  loading.value = true
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    if (tab !== 'all') {
      // 过滤特定类型的消息
      messages.value = demoMessages.filter(msg => msg.type === tab)
    } else {
      // 返回所有消息
      messages.value = [...demoMessages]
    }
  } catch (error) {
    console.error('按类型获取消息失败', error)
  } finally {
    loading.value = false
  }
}

/**
 * @description 标记消息为已读
 * @param messageId 消息ID
 */
const markAsRead = async (messageId: string) => {
  const message = messages.value.find((msg) => msg.id === messageId)
  if (message && !message.isRead) {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    message.isRead = true
    
    // 同时更新演示数据中的状态
    const demoMsg = demoMessages.find(msg => msg.id === messageId)
    if (demoMsg) {
      demoMsg.isRead = true
    }
    
    return true
  }
  return false
}

/**
 * @description 标记消息为未读
 * @param messageId 消息ID
 */
const markAsUnread = async (messageId: string) => {
  const message = messages.value.find((msg) => msg.id === messageId)
  if (message) {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    message.isRead = false
    
    // 同时更新演示数据中的状态
    const demoMsg = demoMessages.find(msg => msg.id === messageId)
    if (demoMsg) {
      demoMsg.isRead = false
    }
    
    return true
  }
  return false
}

/**
 * @description 处理消息点击
 * @param message 消息对象
 */
const handleMessageClick = async (message: Message) => {
  // 标记为已读
  if (!message.isRead) {
    await markAsRead(message.id)
  }

  // 如果有跳转链接，则跳转
  if (message.actionUrl) {
    uni.navigateTo({
      url: message.actionUrl,
    })
  } else {
    // 显示消息详情
    currentMessage.value = message
    showMessageDetailModal.value = true
  }
}

/**
 * @description 全部标记为已读
 */
const markAllAsRead = () => {
  if (unreadCount.value === 0) {
    uni.showToast({
      title: '暂无未读消息',
      icon: 'none',
    })
    return
  }

  showMarkAllReadModal.value = true
}

/**
 * @description 确认全部标记为已读
 */
const confirmMarkAllAsRead = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  messages.value.forEach((msg) => {
    msg.isRead = true
  })
  
  // 同时更新演示数据中的状态
  demoMessages.forEach(msg => {
    msg.isRead = true
  })
  
  uni.showToast({
    title: '已全部标记为已读',
    icon: 'success',
  })
  
  showMarkAllReadModal.value = false
}

/**
 * @description 长按消息项
 * @param message 消息对象
 */
const onLongPress = (message: Message) => {
  uni.vibrateShort()
  uni.showActionSheet({
    itemList: [message.isRead ? '标记为未读' : '标记为已读', '删除消息', '复制内容'],
    success: async (res) => {
      switch (res.tapIndex) {
        case 0:
          // 切换已读/未读状态
          if (message.isRead) {
            const success = await markAsUnread(message.id)
            if (success) {
              uni.showToast({
                title: '已标记为未读',
                icon: 'success',
              })
            }
          } else {
            const success = await markAsRead(message.id)
            if (success) {
              uni.showToast({
                title: '已标记为已读',
                icon: 'success',
              })
            }
          }
          break
        case 1:
          handleDeleteMessage(message.id)
          break
        case 2:
          copyMessageContent(message)
          break
      }
    },
  })
}

/**
 * @description 删除消息
 * @param messageId 消息ID
 */
const handleDeleteMessage = (messageId: string) => {
  currentDeleteMessageId.value = messageId
  showDeleteConfirmModal.value = true
}

/**
 * @description 确认删除消息
 */
const confirmDeleteMessage = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const index = messages.value.findIndex((msg) => msg.id === currentDeleteMessageId.value)
  if (index > -1) {
    messages.value.splice(index, 1)
    
    // 同时更新演示数据
    const demoIndex = demoMessages.findIndex(msg => msg.id === currentDeleteMessageId.value)
    if (demoIndex > -1) {
      demoMessages.splice(demoIndex, 1)
    }
    
    uni.showToast({
      title: '删除成功',
      icon: 'success',
    })
  }
  
  showDeleteConfirmModal.value = false
  currentDeleteMessageId.value = ''
}

/**
 * @description 复制消息内容
 * @param message 消息对象
 */
const copyMessageContent = (message: Message) => {
  uni.setClipboardData({
    data: `${message.title}\n${message.content}`,
    success: () => {
      uni.showToast({
        title: '已复制到剪贴板',
        icon: 'success',
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error',
      })
    },
  })
}

/**
 * @description 显示搜索框
 */
const showSearchBox = () => {
  showSearch.value = true
}

/**
 * @description 隐藏搜索框
 */
const hideSearchBox = () => {
  showSearch.value = false
  searchKeyword.value = ''
}

/**
 * @description 清空搜索
 */
const clearSearch = () => {
  searchKeyword.value = ''
}

/**
 * @description 搜索消息
 */
const handleSearch = async () => {
  loading.value = true
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    if (!searchKeyword.value.trim()) {
      messages.value = [...demoMessages]
    } else {
      const keyword = searchKeyword.value.toLowerCase().trim()
      messages.value = demoMessages.filter(
        msg => 
          msg.title.toLowerCase().includes(keyword) || 
          msg.content.toLowerCase().includes(keyword)
      )
    }
  } catch (error) {
    console.error('搜索消息失败', error)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 获取消息类型中文名称
 * @param type 消息类型
 */
const getMessageTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    system: '系统消息',
    interview: '面试消息',
    study: '学习消息',
    achievement: '成就消息',
  }
  return typeMap[type] || '未知消息'
}

/**
 * @description 下拉刷新
 */
const onRefresh = async () => {
  refreshing.value = true
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    await initMessages()
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('刷新失败', error)
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'none',
    })
  } finally {
    refreshing.value = false
  }
}

/**
 * @description 格式化时间
 * @param timeStr 时间字符串
 */
const formatTime = (timeStr: string) => {
  const now = new Date()
  const msgTime = new Date(timeStr)
  const diff = now.getTime() - msgTime.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor(diff / (1000 * 60))

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * @description 获取消息头像样式
 * @param type 消息类型
 */
const getMessageAvatarStyle = (type: string) => {
  return {
    backgroundColor: messageColors[type],
  }
}

onLoad(() => {
  initMessages()
  showSearch.value = false
})

onShow(() => {
  // 页面显示时刷新数据
  initMessages()
})
</script>

<style lang="scss" scoped>
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.message-page {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  padding-bottom: 120rpx;
  background: linear-gradient(135deg, #f8fffe 0%, #e8f8f5 50%, #ffffff 100%);
  overflow-x: hidden;

  &::before {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;
    content: '';
    background: radial-gradient(circle at 20% 80%, rgba(0, 201, 167, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(79, 209, 199, 0.1) 0%, transparent 50%);
  }
}

// 骨架屏样式
.skeleton-container {
  padding: 24rpx 0;
}

.skeleton-item {
  display: flex;
  align-items: flex-start;
  width: 100%;
  padding: 28rpx 24rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  overflow: hidden;
}

.skeleton-avatar {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-content {
  flex: 1;
  min-width: 0;
}

.skeleton-title {
  width: 60%;
  height: 32rpx;
  margin-bottom: 16rpx;
  border-radius: 6rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-text {
  width: 90%;
  height: 24rpx;
  margin-bottom: 12rpx;
  border-radius: 6rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-text-short {
  width: 40%;
}

// 消息列表项动画
.message-item {
  opacity: 0;
  animation: fadeInUp 0.5s ease forwards;
}

// 自定义头部区域
.custom-header {
  position: relative;
  z-index: 3;
}

.search-button {
  position: absolute;
  top: 50%;
  right: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  transform: translateY(-50%);

  &:active {
    background: rgba(0, 201, 167, 0.1);
    transform: translateY(-50%) scale(0.9);
  }
}

.search-button-icon {
  font-size: 36rpx;
  color: #00c9a7;
}

// 搜索栏
.search-bar {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  animation: slideInRight 0.3s ease;
  box-sizing: border-box;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 80rpx;
  padding: 0 32rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  box-sizing: border-box;
}

.search-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
  color: #999999;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.search-placeholder {
  color: #cccccc;
}

.search-clear {
  margin-left: 16rpx;
  font-size: 32rpx;
  color: #999999;
  transition: color 0.3s ease;

  &:active {
    color: #666666;
  }
}

.search-close {
  margin-left: 16rpx;
  font-size: 32rpx;
  color: #00c9a7;
  transition: all 0.3s ease;

  &:active {
    color: #4fd1c7;
    transform: scale(0.9);
  }
}

// 操作栏
.action-bar {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
}

.action-left {
  display: flex;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 24rpx;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

.action-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.action-right {
  display: flex;
  align-items: center;
}

.message-count {
  font-size: 24rpx;
  color: #999999;
}

// 标签栏
.tab-bar {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 0 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;

  &::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1rpx;
    content: '';
    background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  }
}

.tab-scroll {
  width: 100%;
  white-space: nowrap;
}

.tab-list {
  display: flex;
  align-items: center;
  width: max-content;
  padding: 24rpx 0;
}

.tab-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12rpx 22rpx;
  margin-right: 16rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  transition: all 0.3s ease;

  &.active {
    color: #ffffff;
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
    transform: scale(1.05);
  }
}

.tab-icon {
  margin-right: 8rpx;
  font-size: 16rpx;
}

.tab-label {
  font-size: 24rpx;
  font-weight: 500;
}

.tab-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  line-height: 1;
  color: #ffffff;
  text-align: center;
  background: #ff4757;
  border-radius: 20rpx;
}

// 消息列表
.message-list {
  flex: 1;
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  color: #999999;
}

.empty-icon {
  margin-bottom: 32rpx;
  font-size: 120rpx;
  opacity: 0.5;
  animation: float 3s ease-in-out infinite;
}

.empty-text {
  margin-bottom: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.empty-desc {
  margin-bottom: 32rpx;
  font-size: 28rpx;
  line-height: 1.5;
  text-align: center;
  opacity: 0.7;
}

.empty-action {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 32rpx;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

.empty-action-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.empty-action-text {
  font-weight: 500;
}

.message-items {
  padding: 24rpx 0;
}

.message-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;
  max-width: 100%;
  padding: 28rpx 14rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateY(30rpx);
  animation: fadeInUp 0.6s ease forwards;
  box-sizing: border-box;

  &:active {
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    transform: scale(0.98) translateY(0);
  }

  &.unread {
    background: linear-gradient(135deg, #ffffff 0%, #f0fffe 100%);
    border-left: 6rpx solid #00c9a7;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      pointer-events: none;
      content: '';
      background: linear-gradient(
        135deg,
        rgba(0, 201, 167, 0.05) 0%,
        rgba(79, 209, 199, 0.05) 100%
      );
      border-radius: 24rpx;
    }
  }

  &:hover {
    box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.08);
    transform: translateY(-2rpx);
  }
}

.message-avatar {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 50%;
}

.message-avatar-icon {
  font-size: 36rpx;
  color: #ffffff;
}

.message-content {
  position: relative;
  z-index: 1;
  flex: 1;
  min-width: 0;
  padding: 24rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.message-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12rpx;
  box-sizing: border-box;
}

.message-title-wrapper {
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 120rpx);
}

.message-title {
  display: block;
  margin-bottom: 4rpx;
  overflow: hidden;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-type {
  display: inline-block;
  padding: 2rpx 8rpx;
  font-size: 22rpx;
  color: #999999;
  background: #f0f0f0;
  border-radius: 8rpx;
}

.message-time {
  flex-shrink: 0;
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #999999;
}

.message-text {
  display: -webkit-box;
  margin-bottom: 8rpx;
  overflow: hidden;
  font-size: 28rpx;
  line-height: 1.5;
  color: #666666;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.message-action {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  margin-top: 12rpx;
  background: rgba(0, 201, 167, 0.1);
  border: 1rpx solid rgba(0, 201, 167, 0.2);
  border-radius: 16rpx;
}

.message-action-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #00c9a7;
}

.message-action-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #00c9a7;
}

.unread-dot {
  position: absolute;
  top: 32rpx;
  right: 24rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .message-page {
    width: 100vw;
  }

  .search-button {
    right: 24rpx;
    width: 56rpx;
    height: 56rpx;
  }

  .search-button-icon {
    font-size: 32rpx;
  }

  .search-bar {
    width: 100%;
    padding: 20rpx 24rpx;
  }

  .search-input-wrapper {
    width: 100%;
    height: 72rpx;
    padding: 0 24rpx;
  }

  .action-bar {
    width: 100%;
    padding: 20rpx 24rpx;
  }

  .tab-bar {
    width: 100%;
    padding: 0 24rpx;
  }

  .tab-item {
    padding: 12rpx 20rpx;
    margin-right: 24rpx;
  }

  .message-list {
    width: 100%;
    padding: 0 24rpx;
  }

  .message-item {
    width: 100%;
    padding: 24rpx 20rpx;
    margin-bottom: 12rpx;
  }

  .message-avatar {
    width: 64rpx;
    height: 64rpx;
    margin-right: 20rpx;
  }

  .message-avatar-icon {
    font-size: 28rpx;
  }

  .message-title {
    font-size: 30rpx;
  }

  .message-text {
    font-size: 26rpx;
  }

  .message-type {
    font-size: 20rpx;
  }

  .empty-state {
    padding: 80rpx 0;
  }

  .empty-icon {
    font-size: 100rpx;
  }
}

// 超小屏幕适配
@media screen and (max-width: 480rpx) {
  .message-page {
    width: 100vw;
  }

  .search-bar,
  .action-bar,
  .tab-bar {
    padding-left: 16rpx;
    padding-right: 16rpx;
  }

  .message-list {
    padding: 0 16rpx;
  }

  .message-item {
    padding: 20rpx 16rpx;
  }

  .search-button {
    right: 16rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .search-button-icon {
    font-size: 28rpx;
  }
}
</style>
