/**
 * 岗位详情接口服务
 * 提供岗位详细信息和相关操作接口
 */

import { httpGet, httpPost } from '@/utils/http'
import { IResData } from '@/types/interview-select'

// API 端点常量
const JOB_DETAIL_API_ENDPOINTS = {
  GET_JOB_DETAIL: '/app/interview/job/detail',
  GET_SAMPLE_QUESTIONS: '/app/interview/job/sample-questions',
  GET_RELATED_JOBS: '/app/interview/job/related',
  TOGGLE_FAVORITE: '/app/interview/job/favorite',
  GET_JOB_STATISTICS: '/app/interview/job/statistics',
  GET_INTERVIEW_MODES: '/app/interview/job/modes',
  CHECK_USER_READINESS: '/app/interview/job/readiness',
  SHARE_JOB: '/app/interview/job/share',
}

// 岗位详情（匹配后端JobDetailResponse结构）
export interface JobDetail {
  id: number
  name: string
  company: string
  categoryId: number
  categoryName?: string
  difficulty: number
  description: string
  requirements: string[]
  skills: string[]
  responsibilities: string[]
  logo: string
  interviewCount: number
  questionCount: number
  rating: number
  isFavorited: boolean
  createdTime: string
  updatedTime: string
  // 新增的前端需要的字段
  duration: number  // 面试时长（分钟）
  interviewers: number  // 已练习人数
  passRate: string  // 通过率
  tags: string[]  // 技能标签
  interviewProcess: InterviewStep[]  // 面试流程
  skillPoints: SkillPoint[]  // 技能考查重点
  benefits: string[]  // 练习收益
}

// 面试流程步骤
export interface InterviewStep {
  step: number
  name: string
  duration: number
  description: string
}

// 技能考查点
export interface SkillPoint {
  name: string
  weight: number
  level: string  // high, medium, low
}

// 示例问题
export interface SampleQuestion {
  id: number
  question: string
  category: string
  difficulty: number
  type: string
  hintAvailable: boolean
}

// 相关岗位
export interface RelatedJob {
  id: number
  name: string
  company: string
  logo: string
  difficulty: number
  matchScore: number
  tags: string[]
}

// 岗位统计数据
export interface JobStatistics {
  totalInterviews: number
  passRate: number
  averageScore: number
  mostMissedQuestions: Array<{
    category: string
    failRate: number
  }>
  difficultyRating: number
  averageDuration: number
  skillGaps: Array<{
    skill: string
    gapPercentage: number
  }>
}

// 面试模式
export interface InterviewMode {
  id: string
  name: string
  description: string
  duration: number
  questionCount: number
  difficulty: number
  features: string[]
}

// 用户准备度
export interface UserReadiness {
  overallReadiness: number
  skillMatches: Array<{
    skill: string
    userLevel: number
    requiredLevel: number
    match: number
  }>
  missingSkills: string[]
  strengthSkills: string[]
  recommendedPreparation: Array<{
    type: 'skill' | 'knowledge' | 'practice'
    description: string
    priority: 'high' | 'medium' | 'low'
  }>
  estimatedSuccessRate: number
}

/**
 * @description 获取岗位详情
 * @param jobId 岗位ID
 * @returns 岗位详细信息
 */
export async function getJobDetail(jobId: number): Promise<IResData<JobDetail>> {
  return httpGet<JobDetail>(JOB_DETAIL_API_ENDPOINTS.GET_JOB_DETAIL, { jobId })
}

/**
 * @description 获取示例问题
 * @param jobId 岗位ID
 * @param count 问题数量（可选）
 * @returns 示例问题列表
 */
export async function getSampleQuestions(jobId: number, count?: number): Promise<IResData<SampleQuestion[]>> {
  return httpGet<SampleQuestion[]>(JOB_DETAIL_API_ENDPOINTS.GET_SAMPLE_QUESTIONS, { 
    jobId,
    count
  })
}

/**
 * @description 获取相关岗位
 * @param jobId 当前岗位ID
 * @param limit 数量限制（可选）
 * @returns 相关岗位列表
 */
export async function getRelatedJobs(jobId: number, limit?: number): Promise<IResData<RelatedJob[]>> {
  return httpGet<RelatedJob[]>(JOB_DETAIL_API_ENDPOINTS.GET_RELATED_JOBS, { 
    jobId, 
    limit 
  })
}

/**
 * @description 切换岗位收藏状态
 * @param jobId 岗位ID
 * @param isFavorited 是否收藏
 * @returns 收藏状态
 */
export async function toggleFavorite(jobId: number, isFavorited: boolean): Promise<IResData<{ 
  jobId: number
  isFavorited: boolean
}>> {
  return httpPost<{ jobId: number, isFavorited: boolean }>(
    JOB_DETAIL_API_ENDPOINTS.TOGGLE_FAVORITE, 
    { jobId, isFavorited }
  )
}

/**
 * @description 获取岗位统计数据
 * @param jobId 岗位ID
 * @returns 统计数据
 */
export async function getJobStatistics(jobId: number): Promise<IResData<JobStatistics>> {
  return httpGet<JobStatistics>(JOB_DETAIL_API_ENDPOINTS.GET_JOB_STATISTICS, { jobId })
}

/**
 * @description 获取面试模式列表
 * @param jobId 岗位ID
 * @returns 面试模式列表
 */
export async function getInterviewModes(jobId: number): Promise<IResData<InterviewMode[]>> {
  return httpGet<InterviewMode[]>(JOB_DETAIL_API_ENDPOINTS.GET_INTERVIEW_MODES, { jobId })
}

/**
 * @description 检查用户准备度
 * @param jobId 岗位ID
 * @param userId 用户ID（可选）
 * @returns 用户准备度数据
 */
export async function checkUserReadiness(jobId: number, userId?: string): Promise<IResData<UserReadiness>> {
  return httpGet<UserReadiness>(JOB_DETAIL_API_ENDPOINTS.CHECK_USER_READINESS, { 
    jobId, 
    userId 
  })
}

/**
 * @description 分享岗位信息
 * @param jobId 岗位ID
 * @param platform 分享平台
 * @returns 分享结果
 */
export async function shareJob(jobId: number, platform: 'wechat' | 'qq' | 'weibo' | 'link'): Promise<IResData<{
  success: boolean
  shareUrl: string
}>> {
  return httpPost<{ success: boolean, shareUrl: string }>(
    JOB_DETAIL_API_ENDPOINTS.SHARE_JOB, 
    { jobId, platform }
  )
} 