<script setup lang="ts">
import { ref } from 'vue'

/**
 * @description 截图按钮组件
 * 在面试过程中提供截取摄像头画面的功能
 */
const props = defineProps({
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 按钮类型
  type: {
    type: String,
    default: 'primary' // primary, default, text
  },
  // 按钮尺寸
  size: {
    type: String,
    default: 'normal' // small, normal, large
  },
  // 截图质量
  quality: {
    type: String,
    default: 'high' // low, medium, high
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 是否显示文本
  showText: {
    type: Boolean,
    default: true
  },
  // 自定义按钮文本
  buttonText: {
    type: String,
    default: '截图'
  },
  // 自定义图标
  icon: {
    type: String,
    default: 'fa fa-camera'
  }
})

// 组件事件
const emit = defineEmits(['capture', 'error'])

// 状态
const isCapturing = ref(false)
const previewUrl = ref('')
const showPreview = ref(false)

/**
 * @description 截取画面
 */
const captureScreen = async () => {
  if (props.disabled || isCapturing.value) return
  
  try {
    isCapturing.value = true
    
    // 发送截图事件，让父组件处理实际的截图逻辑
    emit('capture', { 
      quality: props.quality,
      timestamp: Date.now()
    })
    
    // 模拟截图延迟
    setTimeout(() => {
      isCapturing.value = false
    }, 300)
    
  } catch (error) {
    console.error('截图失败:', error)
    isCapturing.value = false
    emit('error', { error })
  }
}

/**
 * @description 显示截图预览
 * @param imageUrl 图片URL
 */
const showImagePreview = (imageUrl: string) => {
  previewUrl.value = imageUrl
  showPreview.value = true
}

/**
 * @description 关闭预览
 */
const closePreview = () => {
  showPreview.value = false
  previewUrl.value = ''
}

/**
 * @description 下载图片
 */
const downloadImage = () => {
  if (!previewUrl.value) return
  
  // 创建下载链接
  const link = document.createElement('a')
  link.href = previewUrl.value
  link.download = `interview-screenshot-${Date.now()}.jpg`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // 关闭预览
  closePreview()
}

// 暴露方法给父组件
defineExpose({
  captureScreen,
  showImagePreview,
  closePreview,
  downloadImage
})
</script>

<template>
  <div class="screenshot-button-container">
    <!-- 截图按钮 -->
    <button 
      class="screenshot-button" 
      :class="[
        `type-${type}`, 
        `size-${size}`, 
        { 'disabled': disabled, 'capturing': isCapturing }
      ]"
      @click="captureScreen"
      :disabled="disabled || isCapturing"
    >
      <i :class="icon" v-if="showIcon"></i>
      <span class="button-text" v-if="showText">{{ isCapturing ? '截图中...' : buttonText }}</span>
    </button>
    
    <!-- 图片预览弹窗 -->
    <div class="image-preview" v-if="showPreview">
      <div class="preview-overlay" @click="closePreview"></div>
      <div class="preview-content">
        <div class="preview-header">
          <span class="preview-title">截图预览</span>
          <button class="close-button" @click="closePreview">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="preview-body">
          <img :src="previewUrl" class="preview-image" alt="截图预览" />
        </div>
        <div class="preview-footer">
          <button class="download-button" @click="downloadImage">
            <i class="fa fa-download"></i>
            <span>下载</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.screenshot-button-container {
  display: inline-block;
}

.screenshot-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

/* 按钮类型 */
.screenshot-button.type-primary {
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  color: #fff;
  box-shadow: 0 2px 6px rgba(0, 201, 167, 0.3);
}

.screenshot-button.type-primary:hover {
  background: linear-gradient(135deg, #00b396 0%, #45c0b6 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 201, 167, 0.4);
}

.screenshot-button.type-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 201, 167, 0.3);
}

.screenshot-button.type-default {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
}

.screenshot-button.type-default:hover {
  background: #e6e6e6;
}

.screenshot-button.type-text {
  background: transparent;
  color: #00c9a7;
  padding: 4px 8px;
}

.screenshot-button.type-text:hover {
  background: rgba(0, 201, 167, 0.05);
}

/* 按钮尺寸 */
.screenshot-button.size-small {
  padding: 4px 12px;
  font-size: 12px;
}

.screenshot-button.size-normal {
  padding: 8px 16px;
  font-size: 14px;
}

.screenshot-button.size-large {
  padding: 12px 24px;
  font-size: 16px;
}

/* 状态样式 */
.screenshot-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.screenshot-button.capturing {
  position: relative;
  overflow: hidden;
}

.screenshot-button.capturing::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: capturing 1s infinite;
}

@keyframes capturing {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 图片预览弹窗 */
.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

.preview-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: zoomIn 0.3s ease;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 50%;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #f5f5f5;
  color: #666;
}

.preview-body {
  flex: 1;
  overflow: auto;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 4px;
}

.preview-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.download-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #00c9a7;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-button:hover {
  background: #00b396;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 