/**
 * @description 优化的Markdown渲染器
 * 实现缓存、流式渲染、Web Worker支持等性能优化
 */

interface MarkdownCache {
  content: string
  rendered: string
  timestamp: number
  hits: number
}

interface RenderOptions {
  useCache?: boolean
  streaming?: boolean
  maxCacheSize?: number
  cacheExpiry?: number
}

export class OptimizedMarkdownRenderer {
  private static instance: OptimizedMarkdownRenderer
  private cache = new Map<string, MarkdownCache>()
  private renderQueue: Array<{
    content: string
    resolve: (result: string) => void
    reject: (error: Error) => void
  }> = []
  private isProcessing = false
  private worker: Worker | null = null

  private readonly defaultOptions: Required<RenderOptions> = {
    useCache: true,
    streaming: false,
    maxCacheSize: 100,
    cacheExpiry: 10 * 60 * 1000 // 10分钟
  }

  private constructor() {
    this.initializeWorker()
  }

  static getInstance(): OptimizedMarkdownRenderer {
    if (!OptimizedMarkdownRenderer.instance) {
      OptimizedMarkdownRenderer.instance = new OptimizedMarkdownRenderer()
    }
    return OptimizedMarkdownRenderer.instance
  }

  /**
   * @description 初始化Web Worker（如果支持）
   */
  private initializeWorker(): void {
    // #ifdef H5
    if (typeof Worker !== 'undefined') {
      try {
        // 创建内联Worker
        const workerScript = `
          // 简化的Markdown渲染逻辑
          function renderMarkdown(content) {
            // 基础Markdown转换
            return content
              .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
              .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
              .replace(/\`(.*?)\`/g, '<code>$1</code>')
              .replace(/^# (.*$)/gim, '<h1>$1</h1>')
              .replace(/^## (.*$)/gim, '<h2>$1</h2>')
              .replace(/^### (.*$)/gim, '<h3>$1</h3>')
              .replace(/\\n/g, '<br>')
          }

          self.onmessage = function(e) {
            const { id, content } = e.data
            try {
              const result = renderMarkdown(content)
              self.postMessage({ id, result, success: true })
            } catch (error) {
              self.postMessage({ id, error: error.message, success: false })
            }
          }
        `

        const blob = new Blob([workerScript], { type: 'application/javascript' })
        this.worker = new Worker(URL.createObjectURL(blob))

        this.worker.onmessage = (e) => {
          const { id, result, error, success } = e.data
          this.handleWorkerMessage(id, result, error, success)
        }

        this.worker.onerror = (error) => {
          console.warn('Markdown Worker error:', error)
          this.worker = null
        }
      } catch (error) {
        console.warn('Failed to create Markdown Worker:', error)
        this.worker = null
      }
    }
    // #endif
  }

  /**
   * @description 处理Worker消息
   */
  private handleWorkerMessage(id: string, result: string, error: string, success: boolean): void {
    const queueIndex = this.renderQueue.findIndex(item => 
      this.generateCacheKey(item.content) === id
    )

    if (queueIndex !== -1) {
      const queueItem = this.renderQueue[queueIndex]
      this.renderQueue.splice(queueIndex, 1)

      if (success) {
        queueItem.resolve(result)
      } else {
        queueItem.reject(new Error(error))
      }
    }
  }

  /**
   * @description 渲染Markdown内容
   * @param content 原始内容
   * @param options 渲染选项
   */
  async render(content: string, options: RenderOptions = {}): Promise<string> {
    const opts = { ...this.defaultOptions, ...options }
    const cacheKey = this.generateCacheKey(content)

    // 检查缓存
    if (opts.useCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      
      // 检查缓存是否过期
      if (Date.now() - cached.timestamp < opts.cacheExpiry) {
        cached.hits++
        return cached.rendered
      } else {
        this.cache.delete(cacheKey)
      }
    }

    // 如果支持Worker且内容较长，使用Worker渲染
    if (this.worker && content.length > 500) {
      return this.renderWithWorker(content, cacheKey, opts)
    }

    // 否则使用主线程渲染
    return this.renderInMainThread(content, cacheKey, opts)
  }

  /**
   * @description 使用Worker渲染
   */
  private renderWithWorker(content: string, cacheKey: string, options: Required<RenderOptions>): Promise<string> {
    return new Promise((resolve, reject) => {
      this.renderQueue.push({ content, resolve, reject })

      if (this.worker) {
        this.worker.postMessage({ id: cacheKey, content })
      } else {
        // Worker不可用，回退到主线程
        this.renderInMainThread(content, cacheKey, options)
          .then(resolve)
          .catch(reject)
      }
    })
  }

  /**
   * @description 在主线程渲染
   */
  private async renderInMainThread(content: string, cacheKey: string, options: Required<RenderOptions>): Promise<string> {
    try {
      // 使用requestIdleCallback优化性能（如果支持）
      const rendered = await this.performRender(content)

      // 缓存结果
      if (options.useCache) {
        this.cacheResult(cacheKey, content, rendered, options.maxCacheSize)
      }

      return rendered
    } catch (error) {
      console.error('Markdown rendering failed:', error)
      return content // 降级返回原始内容
    }
  }

  /**
   * @description 执行实际渲染
   */
  private performRender(content: string): Promise<string> {
    return new Promise((resolve) => {
      // 使用requestIdleCallback进行非阻塞渲染
      const renderFn = () => {
        try {
          const rendered = this.basicMarkdownRender(content)
          resolve(rendered)
        } catch (error) {
          resolve(content)
        }
      }

      // #ifdef H5
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(renderFn, { timeout: 100 })
      } else {
        setTimeout(renderFn, 0)
      }
      // #endif

      // #ifndef H5
      setTimeout(renderFn, 0)
      // #endif
    })
  }

  /**
   * @description 基础Markdown渲染
   */
  private basicMarkdownRender(content: string): string {
    return content
      // 标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 粗体和斜体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 代码
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
      // 列表
      .replace(/^\* (.+)$/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
      // 换行
      .replace(/\n/g, '<br>')
  }

  /**
   * @description 缓存渲染结果
   */
  private cacheResult(key: string, content: string, rendered: string, maxSize: number): void {
    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= maxSize) {
      this.evictLeastUsed()
    }

    this.cache.set(key, {
      content,
      rendered,
      timestamp: Date.now(),
      hits: 1
    })
  }

  /**
   * @description 删除最少使用的缓存项
   */
  private evictLeastUsed(): void {
    let leastUsedKey = ''
    let minHits = Infinity

    for (const [key, cache] of this.cache.entries()) {
      if (cache.hits < minHits) {
        minHits = cache.hits
        leastUsedKey = key
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey)
    }
  }

  /**
   * @description 生成缓存键
   */
  private generateCacheKey(content: string): string {
    // 简单的哈希函数
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(36)
  }

  /**
   * @description 流式渲染（用于长文本）
   */
  async renderStream(content: string, onChunk: (chunk: string) => void): Promise<void> {
    const chunks = this.splitIntoChunks(content, 1000) // 每1000字符一个块

    for (const chunk of chunks) {
      const rendered = await this.render(chunk, { useCache: false })
      onChunk(rendered)
      
      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  }

  /**
   * @description 将内容分割成块
   */
  private splitIntoChunks(content: string, chunkSize: number): string[] {
    const chunks: string[] = []
    for (let i = 0; i < content.length; i += chunkSize) {
      chunks.push(content.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * @description 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * @description 获取缓存统计
   */
  getCacheStats(): { size: number; hitRate: number } {
    const totalHits = Array.from(this.cache.values()).reduce((sum, cache) => sum + cache.hits, 0)
    const avgHits = this.cache.size > 0 ? totalHits / this.cache.size : 0
    
    return {
      size: this.cache.size,
      hitRate: avgHits
    }
  }

  /**
   * @description 销毁渲染器
   */
  destroy(): void {
    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }
    this.cache.clear()
    this.renderQueue = []
  }
}

// 导出单例实例
export const optimizedMarkdownRenderer = OptimizedMarkdownRenderer.getInstance()

// 导出便捷函数
export async function renderMarkdown(content: string, options?: RenderOptions): Promise<string> {
  return optimizedMarkdownRenderer.render(content, options)
}

export function renderMarkdownStream(content: string, onChunk: (chunk: string) => void): Promise<void> {
  return optimizedMarkdownRenderer.renderStream(content, onChunk)
}
