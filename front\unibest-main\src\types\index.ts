/**
 * 类型定义统一导出
 */

// 导出认证相关类型
export * from './auth'

// 导出反馈相关类型
export * from './feedback'

// 导出面试相关类型
export * from './interview'
export * from './interview-select'

// 重新导出常用类型别名
export type {
  // 基础类型
  ApiResponse,

  // 用户相关
  UserInfo,
  ExtendedUserInfo,

  // 表单数据类型
  LoginFormData,
  RegisterFormData,
  ForgetPasswordFormData,
  FormErrors,

  // 验证相关
  ValidationRule,
  FormValidationRules,
  PasswordStrengthInfo,

  // 通知相关
  NotificationConfig,
  CodeSendStatus,

  // 枚举类型
  LoginMethod,
  NotificationType,
  UserStatus,
  PasswordStrength,
} from './auth'

// 导出反馈相关类型别名
export type {
  // 反馈相关
  SubmitFeedbackParams,
  FeedbackDetail,
  FeedbackListParams,
  FeedbackListResponse,
  FeedbackStats,

  // 反馈枚举
  FeedbackType,
  FeedbackStatus,
} from './feedback'

// 导出常量
export {
  // 客户端配置
  DEFAULT_CLIENT_CONFIG,

  // API端点
  AUTH_API_ENDPOINTS,

  // 选项列表
  MAJOR_OPTIONS,
  GRADE_OPTIONS,

  // 验证相关
  VALIDATION_PATTERNS,
  VALIDATION_MESSAGES,

  // 通知消息
  NOTIFICATION_MESSAGES,

  // 存储键名
  STORAGE_KEYS,

  // 路由路径
  ROUTE_PATHS,

  // 时间常量
  TIME_CONSTANTS,

  // 密码强度配置
  PASSWORD_STRENGTH_CONFIG,

  // 默认值
  DEFAULT_AVATAR,
  DEV_DEFAULTS,
} from './auth-constants'
