/**
 * 面试选择页面服务模块
 * 统一管理面试选择页面相关的API接口
 */

import { httpGet, httpPost } from '@/utils/http'
import type { DifficultyLevel } from '@/types/interview'
import type {
  IResData,
  JobItem,
  JobCategory,
  InterviewMode,
  GetJobListParams,
  GetJobListResponse,
  GetCategoriesParams,
  GetCategoriesResponse,
  GetInterviewModesParams,
  GetInterviewModesResponse,
  GetSearchSuggestionsParams,
  GetSearchSuggestionsResponse,
  CreateInterviewSessionParams,
  CreateInterviewSessionResponse,
  FavoriteJobSelectParams,
  DeviceCheckResult,
  JobStatistics,
} from '@/types/interview-select'

// API 端点常量
const INTERVIEW_SELECT_API_ENDPOINTS = {
  GET_JOB_LIST: '/app/interview/jobs/list',
  GET_CATEGORIES: '/app/interview/categories',
  GET_INTERVIEW_MODES: '/app/interview/modes',
  GET_SEARCH_SUGGESTIONS: '/app/interview/search/suggestions',
  CREATE_SESSION: '/app/interview/session/create',
  FAVORITE_JOB: '/app/interview/job/favorite',
  CHECK_DEVICE: '/app/interview/device/check',
  GET_STATISTICS: '/app/interview/statistics',
}

// 演示数据 - 岗位分类
const DEMO_CATEGORIES: JobCategory[] = [
  {
    id: 1,
    name: '技术类',
    icon: 'i-mdi-code-braces',
    color: '#00C9A7',
    description: '软件开发、技术架构等技术岗位',
    jobCount: 156,
  },
  {
    id: 2,
    name: '产品类',
    icon: 'i-mdi-lightbulb-outline',
    color: '#4FD1C7',
    description: '产品规划、需求分析等产品岗位',
    jobCount: 89,
  },
  {
    id: 3,
    name: '设计类',
    icon: 'i-mdi-palette-outline',
    color: '#7C3AED',
    description: 'UI设计、交互设计等设计岗位',
    jobCount: 64,
  },
  {
    id: 4,
    name: '运营类',
    icon: 'i-mdi-chart-line',
    color: '#F59E0B',
    description: '内容运营、用户运营等运营岗位',
    jobCount: 72,
  },
  {
    id: 5,
    name: '市场类',
    icon: 'i-mdi-store',
    color: '#EF4444',
    description: '市场推广、品牌营销等市场岗位',
    jobCount: 48,
  },
  {
    id: 6,
    name: '人力资源',
    icon: 'i-mdi-account-group',
    color: '#8B5CF6',
    description: '招聘、培训、薪酬等人力资源岗位',
    jobCount: 35,
  },
  {
    id: 7,
    name: '行政类',
    icon: 'i-mdi-file-document-outline',
    color: '#06B6D4',
    description: '行政管理、事务协调等行政岗位',
    jobCount: 28,
  },
  {
    id: 8,
    name: '财务类',
    icon: 'i-mdi-calculator',
    color: '#10B981',
    description: '财务分析、会计核算等财务岗位',
    jobCount: 42,
  },
]

// 演示数据 - 面试模式
const DEMO_INTERVIEW_MODES: InterviewMode[] = [
  {
    id: 'standard',
    name: '标准模式',
    description: '常规面试流程，适合日常训练',
    icon: 'i-mdi-clipboard-list',
    color: '#00C9A7',
    duration: 30,
    difficulty: 3 as DifficultyLevel,
    features: ['基础题目', '项目经验', '技能评估'],
  },
  {
    id: 'pressure',
    name: '压力模式',
    description: '高强度面试，挑战心理承受能力',
    icon: 'i-mdi-flash',
    color: '#F59E0B',
    duration: 45,
    difficulty: 5 as DifficultyLevel,
    features: ['高难度题目', '时间压力', '连续提问'],
  },
  {
    id: 'specialized',
    name: '专项模式',
    description: '针对特定技能的深度面试',
    icon: 'i-mdi-target',
    color: '#7C3AED',
    duration: 60,
    difficulty: 4 as DifficultyLevel,
    features: ['专业深度', '技术细节', '场景应用'],
  },
]

// 演示数据 - 岗位列表
const DEMO_JOBS: JobItem[] = [
  {
    id: 101,
    categoryId: 1,
    name: '前端开发工程师',
    company: '腾讯科技',
    logo: '/static/images/tencent-logo.png',
    difficulty: 3 as DifficultyLevel,
    duration: 30,
    questionCount: 10,
    tags: ['Vue.js', 'React', 'JavaScript', 'TypeScript'],
    description: '负责Web前端开发，熟悉现代前端框架',
    interviewers: 156,
    passRate: '68%',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
  },
  {
    id: 102,
    categoryId: 1,
    name: '后端开发工程师',
    company: '阿里巴巴',
    logo: '/static/images/alibaba-logo.png',
    difficulty: 4 as DifficultyLevel,
    duration: 45,
    questionCount: 15,
    tags: ['Java', 'Spring Boot', 'MySQL', 'Redis'],
    description: '负责服务端开发和系统架构设计',
    interviewers: 203,
    passRate: '72%',
    createdAt: '2024-01-16T09:15:00Z',
    updatedAt: '2024-01-21T14:45:00Z',
  },
  {
    id: 103,
    categoryId: 1,
    name: '全栈开发工程师',
    company: '字节跳动',
    logo: '/static/images/bytedance-logo.png',
    difficulty: 5 as DifficultyLevel,
    duration: 60,
    questionCount: 20,
    tags: ['Node.js', 'React', 'MongoDB', 'Docker'],
    description: '全栈开发，掌握前后端技术栈',
    interviewers: 89,
    passRate: '58%',
    createdAt: '2024-01-17T11:30:00Z',
    updatedAt: '2024-01-22T16:20:00Z',
  },
  {
    id: 201,
    categoryId: 2,
    name: '产品经理',
    company: '腾讯科技',
    logo: '/static/images/tencent-logo.png',
    difficulty: 3 as DifficultyLevel,
    duration: 45,
    questionCount: 12,
    tags: ['需求分析', '产品设计', '用户研究', '项目管理'],
    description: '负责产品规划和需求管理',
    interviewers: 134,
    passRate: '65%',
    createdAt: '2024-01-18T13:00:00Z',
    updatedAt: '2024-01-23T09:10:00Z',
  },
  {
    id: 202,
    categoryId: 2,
    name: '产品运营',
    company: '美团',
    logo: '/static/images/meituan-logo.png',
    difficulty: 2 as DifficultyLevel,
    duration: 30,
    questionCount: 10,
    tags: ['活动策划', '用户增长', '数据分析', '内容运营'],
    description: '负责产品运营推广和用户增长',
    interviewers: 98,
    passRate: '73%',
    createdAt: '2024-01-19T15:45:00Z',
    updatedAt: '2024-01-24T11:55:00Z',
  },
  {
    id: 301,
    categoryId: 3,
    name: 'UI设计师',
    company: '小米科技',
    logo: '/static/images/xiaomi-logo.png',
    difficulty: 3 as DifficultyLevel,
    duration: 40,
    questionCount: 8,
    tags: ['UI设计', 'Figma', '交互设计', '设计规范'],
    description: '负责产品界面设计和用户体验优化',
    interviewers: 76,
    passRate: '70%',
    createdAt: '2024-01-20T10:15:00Z',
    updatedAt: '2024-01-25T14:30:00Z',
  },
  {
    id: 401,
    categoryId: 4,
    name: '内容运营专员',
    company: '快手',
    logo: '/static/images/kuaishou-logo.png',
    difficulty: 2 as DifficultyLevel,
    duration: 35,
    questionCount: 9,
    tags: ['内容策划', '社群运营', '创意文案', '数据分析'],
    description: '负责内容策划和社群运营工作',
    interviewers: 112,
    passRate: '75%',
    createdAt: '2024-01-21T08:45:00Z',
    updatedAt: '2024-01-26T12:40:00Z',
  },
  {
    id: 501,
    categoryId: 5,
    name: '市场营销专员',
    company: '滴滴出行',
    logo: '/static/images/didi-logo.png',
    difficulty: 3 as DifficultyLevel,
    duration: 40,
    questionCount: 11,
    tags: ['市场推广', '品牌营销', '渠道管理', '活动策划'],
    description: '负责品牌推广和市场营销活动',
    interviewers: 87,
    passRate: '66%',
    createdAt: '2024-01-22T12:00:00Z',
    updatedAt: '2024-01-27T17:15:00Z',
  },
]

/**
 * @description 获取岗位列表
 * @param params 请求参数
 * @returns 岗位列表响应
 */
export async function getJobList(params: GetJobListParams): Promise<IResData<GetJobListResponse>> {
  try {
    // 尝试调用真实API
    const response = await httpGet<GetJobListResponse>(
      INTERVIEW_SELECT_API_ENDPOINTS.GET_JOB_LIST,
      {
        page: params.page,
        pageSize: params.pageSize,
        categoryId: params.categoryId,
        keyword: params.keyword,
        sortBy: params.sortBy,
        sortOrder: params.sortOrder,
        filters: params.filters,
      },
    )
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示数据:', error)

    // API调用失败时使用演示数据
    await new Promise((resolve) => setTimeout(resolve, 300))

    let filteredJobs = [...DEMO_JOBS]

    // 应用分类筛选
    if (params.categoryId) {
      filteredJobs = filteredJobs.filter((job) => job.categoryId === params.categoryId)
    }

    // 应用关键词搜索
    if (params.keyword && params.keyword.trim()) {
      const keyword = params.keyword.toLowerCase()
      filteredJobs = filteredJobs.filter(
        (job) =>
          job.name.toLowerCase().includes(keyword) ||
          job.company.toLowerCase().includes(keyword) ||
          job.tags.some((tag) => tag.toLowerCase().includes(keyword)),
      )
    }

    // 应用排序
    if (params.sortBy) {
      filteredJobs = applySorting(filteredJobs, params.sortBy, params.sortOrder || 'desc')
    }

    // 应用分页
    const total = filteredJobs.length
    const startIndex = (params.page - 1) * params.pageSize
    const endIndex = startIndex + params.pageSize
    const paginatedJobs = filteredJobs.slice(startIndex, endIndex)

    return {
      code: 200,
      message: '获取成功',
      data: {
        jobs: paginatedJobs,
        total,
        page: params.page,
        pageSize: params.pageSize,
        hasMore: endIndex < total,
        categories: DEMO_CATEGORIES,
        recommendedJobs: filteredJobs.slice(0, 3),
      },
    }
  }
}

/**
 * @description 获取岗位分类列表
 * @param params 请求参数
 * @returns 分类列表响应
 */
export async function getCategories(
  params: GetCategoriesParams = {},
): Promise<IResData<GetCategoriesResponse>> {
  try {
    // 尝试调用真实API
    const response = await httpGet<GetCategoriesResponse>(
      INTERVIEW_SELECT_API_ENDPOINTS.GET_CATEGORIES,
      {
        includeJobCount: params.includeJobCount,
      },
    )
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示数据:', error)

    // API调用失败时使用演示数据
    await new Promise((resolve) => setTimeout(resolve, 200))

    return {
      code: 200,
      message: '获取成功',
      data: {
        categories: DEMO_CATEGORIES,
        total: DEMO_CATEGORIES.length,
      },
    }
  }
}

/**
 * @description 获取面试模式列表
 * @param params 请求参数
 * @returns 面试模式列表响应
 */
export async function getInterviewModes(
  params: GetInterviewModesParams = {},
): Promise<IResData<GetInterviewModesResponse>> {
  try {
    // 尝试调用真实API
    const response = await httpGet<GetInterviewModesResponse>(
      INTERVIEW_SELECT_API_ENDPOINTS.GET_INTERVIEW_MODES,
      params,
    )
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示数据:', error)

    // API调用失败时使用演示数据
    await new Promise((resolve) => setTimeout(resolve, 150))

    return {
      code: 200,
      message: '获取成功',
      data: {
        modes: DEMO_INTERVIEW_MODES,
      },
    }
  }
}

/**
 * @description 获取搜索建议
 * @param params 请求参数
 * @returns 搜索建议响应
 */
export async function getSearchSuggestions(
  params: GetSearchSuggestionsParams,
): Promise<IResData<GetSearchSuggestionsResponse>> {
  try {
    // 尝试调用真实API
    const response = await httpGet<GetSearchSuggestionsResponse>(
      INTERVIEW_SELECT_API_ENDPOINTS.GET_SEARCH_SUGGESTIONS,
      {
        keyword: params.keyword,
        limit: params.limit || 5,
      },
    )
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示数据:', error)

    // API调用失败时使用演示数据
    await new Promise((resolve) => setTimeout(resolve, 100))

    const keyword = params.keyword.toLowerCase()
    const suggestionTexts = [
      '前端开发工程师',
      '产品经理',
      'Java开发',
      'Python开发',
      'UI设计师',
      '数据分析师',
      '运营专员',
      '测试工程师',
    ]
      .filter((item) => item.toLowerCase().includes(keyword))
      .slice(0, params.limit || 5)

    // 转换为SuggestionItem格式
    const suggestions = suggestionTexts.map((text) => ({
      text,
      type: 'keyword',
      entityId: undefined,
    }))

    return {
      code: 200,
      message: '获取成功',
      data: {
        suggestions,
        originalQuery: params.keyword,
      },
    }
  }
}

/**
 * @description 创建面试会话
 * @param params 请求参数
 * @returns 面试会话响应
 */
export async function createInterviewSession(
  params: CreateInterviewSessionParams,
): Promise<IResData<CreateInterviewSessionResponse>> {
  try {
    // 尝试调用真实API
    const response = await httpPost<CreateInterviewSessionResponse>(
      INTERVIEW_SELECT_API_ENDPOINTS.CREATE_SESSION,
      {
        jobId: params.jobId,
        mode: params.mode,
        resumeUrl: params.resumeUrl,
        customizedQuestions: params.customizedQuestions,
      },
    )
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示数据:', error)

    // API调用失败时返回演示会话
    await new Promise((resolve) => setTimeout(resolve, 500))

    const job = DEMO_JOBS.find((j) => j.id === params.jobId)
    const mode = DEMO_INTERVIEW_MODES.find((m) => m.id === params.mode)

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    return {
      code: 200,
      message: '会话创建成功',
      data: {
        sessionId,
        jobId: params.jobId,
        mode: params.mode,
        estimatedDuration: mode?.duration || job?.duration || 30,
        questionCount: job?.questionCount || 10,
        sessionToken: `token_${sessionId}`,
        expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2小时后过期
      },
    }
  }
}

/**
 * @description 收藏/取消收藏岗位
 * @param params 请求参数
 * @returns API响应
 */
export async function favoriteJob(params: FavoriteJobSelectParams): Promise<IResData<void>> {
  try {
    // 尝试调用真实API
    const response = await httpPost<void>(INTERVIEW_SELECT_API_ENDPOINTS.FAVORITE_JOB, {
      jobId: params.jobId,
      isFavorited: params.isFavorited,
    })
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示响应:', error)

    // API调用失败时返回成功响应
    await new Promise((resolve) => setTimeout(resolve, 200))

    return {
      code: 200,
      message: params.isFavorited ? '收藏成功' : '取消收藏成功',
      data: undefined,
    }
  }
}

/**
 * @description 设备检测
 * @returns 设备检测结果
 */
export async function checkDevice(): Promise<IResData<DeviceCheckResult>> {
  try {
    // 尝试调用真实API
    const response = await httpGet<DeviceCheckResult>(INTERVIEW_SELECT_API_ENDPOINTS.CHECK_DEVICE)
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示检测:', error)

    // 模拟设备检测过程
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 简单的设备检测模拟
    const result: DeviceCheckResult = {
      camera: true,
      microphone: true,
      network: true,
      allPassed: true,
      errorMessages: [],
    }

    // 随机模拟一些检测失败的情况（用于测试）
    if (Math.random() < 0.1) {
      // 10%概率检测失败
      result.camera = false
      result.allPassed = false
      result.errorMessages = ['摄像头权限未授权']
    }

    return {
      code: 200,
      message: '设备检测完成',
      data: result,
    }
  }
}

/**
 * @description 获取统计信息
 * @returns 统计信息响应
 */
export async function getStatistics(): Promise<IResData<JobStatistics>> {
  try {
    // 尝试调用真实API
    const response = await httpGet<JobStatistics>(INTERVIEW_SELECT_API_ENDPOINTS.GET_STATISTICS)
    return response
  } catch (error) {
    console.warn('API调用失败，使用演示数据:', error)

    // API调用失败时使用演示数据
    await new Promise((resolve) => setTimeout(resolve, 200))

    return {
      code: 200,
      message: '获取成功',
      data: {
        totalJobs: DEMO_JOBS.length,
        totalInterviews: 2846,
        avgPassRate: 68.5,
        popularCategories: [
          { categoryId: 1, name: '技术类', jobCount: 156 },
          { categoryId: 2, name: '产品类', jobCount: 89 },
          { categoryId: 4, name: '运营类', jobCount: 72 },
        ],
        hotJobs: DEMO_JOBS.slice(0, 5),
      },
    }
  }
}

/**
 * @description 应用排序逻辑
 * @param jobs 待排序的岗位列表
 * @param sortBy 排序字段
 * @param sortOrder 排序顺序
 * @returns 排序后的岗位列表
 */
function applySorting(jobs: JobItem[], sortBy: string, sortOrder: 'asc' | 'desc'): JobItem[] {
  const sortedJobs = [...jobs]

  switch (sortBy) {
    case 'smart':
      // 智能排序：综合考虑热度、通过率、难度等因素
      return sortedJobs.sort((a, b) => {
        const scoreA = calculateSmartScore(a)
        const scoreB = calculateSmartScore(b)
        return sortOrder === 'desc' ? scoreB - scoreA : scoreA - scoreB
      })

    case 'hot':
      // 热门优先：按面试人数排序
      return sortedJobs.sort((a, b) => {
        const result = b.interviewers - a.interviewers
        return sortOrder === 'desc' ? result : -result
      })

    case 'difficulty':
      // 按难度排序
      return sortedJobs.sort((a, b) => {
        const result = a.difficulty - b.difficulty
        return sortOrder === 'desc' ? -result : result
      })

    case 'duration':
      // 按时长排序
      return sortedJobs.sort((a, b) => {
        const result = a.duration - b.duration
        return sortOrder === 'desc' ? -result : result
      })

    case 'pass_rate':
      // 按通过率排序
      return sortedJobs.sort((a, b) => {
        const passRateA = parseFloat(a.passRate.replace('%', ''))
        const passRateB = parseFloat(b.passRate.replace('%', ''))
        const result = passRateB - passRateA
        return sortOrder === 'desc' ? result : -result
      })

    case 'created_time':
      // 按创建时间排序
      return sortedJobs.sort((a, b) => {
        const timeA = new Date(a.createdAt || '').getTime()
        const timeB = new Date(b.createdAt || '').getTime()
        const result = timeB - timeA
        return sortOrder === 'desc' ? result : -result
      })

    default:
      return sortedJobs
  }
}

/**
 * @description 计算智能排序分数
 * @param job 岗位信息
 * @returns 智能分数
 */
function calculateSmartScore(job: JobItem): number {
  // 热度分数 (30%)
  const hotScore = (job.interviewers / 300) * 30

  // 通过率分数 (25%) - 适中的通过率更好
  const passRate = parseFloat(job.passRate.replace('%', ''))
  const passRateScore = ((100 - Math.abs(passRate - 65)) / 100) * 25

  // 难度分数 (20%) - 中等难度优先
  const difficultyScore = ((5 - Math.abs(job.difficulty - 3)) / 5) * 20

  // 时长分数 (15%) - 30-45分钟为最佳
  const durationScore = ((60 - Math.abs(job.duration - 37.5)) / 60) * 15

  // 题目数量分数 (10%) - 10-15题为最佳
  const questionScore = ((20 - Math.abs(job.questionCount - 12.5)) / 20) * 10

  return hotScore + passRateScore + difficultyScore + durationScore + questionScore
}
