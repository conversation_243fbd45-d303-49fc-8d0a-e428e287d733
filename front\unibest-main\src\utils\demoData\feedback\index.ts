/**
 * @description 用户反馈演示数据模块
 * 提供用户反馈相关的演示数据和生成器
 */

import { demoDataManager } from '../DemoDataManager'
import feedbackData, {
  createDemoFeedbackDetail,
  createDemoFeedbackList,
  createDemoFeedbackStats,
} from './feedbackData'
import type {
  FeedbackDetail,
  FeedbackStats,
  FeedbackStatus,
  FeedbackListParams,
  FeedbackListResponse,
} from './feedbackData'

// API路径常量
const API_PATHS = {
  GET_FEEDBACK_LIST: 'feedback/getUserFeedbackList',
  GET_FEEDBACK_STATS: 'feedback/getFeedbackStats',
  GET_FEEDBACK_DETAIL: 'feedback/getFeedbackDetail',
  SUBMIT_FEEDBACK: 'feedback/submitFeedback',
  DELETE_FEEDBACK: 'feedback/deleteFeedback',
}

// 注册反馈演示数据
export const registerFeedbackDemoData = () => {
  // 注册获取反馈列表演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.GET_FEEDBACK_LIST,
    (params: FeedbackListParams) => {
      const listResponse = createDemoFeedbackList(params)
      return {
        code: 200,
        message: 'success',
        rows: listResponse.rows,
        total: listResponse.total,
      }
    },
  )

  // 注册获取反馈统计数据演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_FEEDBACK_STATS, () => {
    const stats = createDemoFeedbackStats()
    return {
      code: 200,
      message: 'success',
      data: stats,
    }
  })

  // 注册获取反馈详情演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.GET_FEEDBACK_DETAIL,
    (params: { id: string }) => {
      // 从ID中提取数字部分
      const idMatch = params.id.match(/\d+/)
      const idNumber = idMatch ? parseInt(idMatch[0]) : 1

      // 根据ID生成不同类型和状态的反馈
      const types = ['功能建议', '内容问题', '使用问题', '其他']
      const statuses: FeedbackStatus[] = ['PENDING', 'PROCESSING', 'RESOLVED', 'CLOSED']

      const typeIndex = idNumber % types.length
      const statusIndex = Math.floor(idNumber / types.length) % statuses.length

      const detail = createDemoFeedbackDetail(params.id, types[typeIndex], statuses[statusIndex])

      return {
        code: 200,
        message: 'success',
        data: detail,
      }
    },
  )

  // 注册提交反馈演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.SUBMIT_FEEDBACK, (params: any) => {
    const id = 'demo-feedback-' + Date.now()
    return {
      code: 200,
      message: 'success',
      data: {
        id,
        type: params.type || '功能建议',
        status: 'PENDING' as FeedbackStatus,
        content: params.content || '这是一条演示反馈',
        createTime: Date.now(),
        updateTime: Date.now(),
      },
    }
  })

  // 注册删除反馈演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.DELETE_FEEDBACK, (params: { id: string }) => {
    return {
      code: 200,
      message: 'success',
      data: true,
    }
  })

  console.log('[FeedbackDemoData] 注册用户反馈演示数据完成')
}

// 导出所有模块
export {
  feedbackData,
  createDemoFeedbackDetail,
  createDemoFeedbackList,
  createDemoFeedbackStats,
  API_PATHS,
}

// 导出类型
export type {
  FeedbackDetail,
  FeedbackStats,
  FeedbackStatus,
  FeedbackListParams,
  FeedbackListResponse,
}

// 默认导出
export default {
  registerFeedbackDemoData,
  API_PATHS,
}
