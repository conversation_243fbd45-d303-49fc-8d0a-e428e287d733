/**
 * @description 演示数据系统入口
 * 提供演示数据系统的所有功能
 */

import { demoDataManager } from './DemoDataManager'
import { demoDataOptimizer } from './DemoDataOptimizer'
import {
  apiInterceptor,
  setupAxiosInterceptors,
  setupUniRequestInterceptors,
} from './ApiInterceptor'
import * as interviewDemoData from './interview'
import * as feedbackDemoData from './feedback'
import * as chatDemoData from './chat'
import {
  integrateInterviewRoomDemoData,
  cleanupInterviewRoomDemoData,
} from './interview/integration'

/**
 * 初始化演示数据系统
 * 注册所有演示数据和拦截器
 */
export const initDemoDataSystem = () => {
  console.log('[DemoDataSystem] 开始初始化演示数据系统')

  // 注册面试房间演示数据
  interviewDemoData.registerInterviewDemoData()

  // 注册用户反馈演示数据
  feedbackDemoData.registerFeedbackDemoData()

  // 注册AI聊天演示数据
  chatDemoData.registerChatDemoData()

  // 设置请求拦截器
  setupUniRequestInterceptors()

  // 预加载常用演示数据
  demoDataOptimizer.preloadCommonDemoData()

  console.log('[DemoDataSystem] 演示数据系统初始化完成')
}

/**
 * 集成演示数据到面试房间页面
 */
export const setupInterviewRoomDemoData = () => {
  return integrateInterviewRoomDemoData()
}

/**
 * 清理面试房间页面的演示数据
 */
export const cleanupInterviewRoomDemoData = () => {
  return cleanupInterviewRoomDemoData()
}

/**
 * 检查是否启用演示数据
 */
export const isDemoDataEnabled = () => {
  return demoDataManager.isEnabled()
}

/**
 * 设置是否启用演示数据
 * @param enabled 是否启用
 */
export const setDemoDataEnabled = (enabled: boolean) => {
  demoDataManager.setEnabled(enabled)
}

/**
 * 获取演示数据系统状态
 */
export const getDemoDataSystemStatus = () => {
  return {
    enabled: demoDataManager.isEnabled(),
    memoryUsage: demoDataOptimizer.getMemoryUsage(),
  }
}

// 导出所有模块
export {
  demoDataManager,
  demoDataOptimizer,
  apiInterceptor,
  setupAxiosInterceptors,
  setupUniRequestInterceptors,
  interviewDemoData,
  feedbackDemoData,
  chatDemoData,
}

// 默认导出
export default {
  initDemoDataSystem,
  setupInterviewRoomDemoData,
  cleanupInterviewRoomDemoData,
  isDemoDataEnabled,
  setDemoDataEnabled,
  getDemoDataSystemStatus,
  demoDataManager,
  demoDataOptimizer,
  apiInterceptor,
  interviewDemoData,
  feedbackDemoData,
  chatDemoData,
}
