// uno.config.ts
import {
  type Preset,
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import { presetApplet, presetRemRpx, transformerAttributify } from 'unocss-applet'

// @see https://unocss.dev/presets/legacy-compat
// import { presetLegacyCompat } from '@unocss/preset-legacy-compat'

const isMp = process.env?.UNI_PLATFORM?.startsWith('mp') ?? false

const presetsUnoResolved: Preset[] = []
if (isMp) {
  // 使用小程序预设
  presetsUnoResolved.push(presetApplet(), presetRemRpx())
} else {
  presetsUnoResolved.push(
    // 非小程序用官方预设
    presetUno(),
    // 支持css class属性化
    presetAttributify(),
  )
}
export default defineConfig({
  presets: [
    ...presetsUnoResolved,
    // 支持图标，需要搭配图标库，eg: @iconify-json/carbon, 使用 `<button class="i-carbon-sun dark:i-carbon-moon" />`
    presetIcons({
      collections: {
        'fa-solid': () => import('@iconify-json/fa-solid/icons.json').then(i => i.default),
        'fa-brands': () => import('@iconify-json/fa-brands/icons.json').then(i => i.default),
        'mdi': () => import('@iconify-json/mdi/icons.json').then(i => i.default)
      },
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    // 将颜色函数 (rgb()和hsl()) 从空格分隔转换为逗号分隔，更好的兼容性app端，example：
    // `rgb(255 0 0)` -> `rgb(255, 0, 0)`
    // `rgba(255 0 0 / 0.5)` -> `rgba(255, 0, 0, 0.5)`
    // 与群友的正常写法冲突，先去掉！（2024-05-25）
    // presetLegacyCompat({
    //   commaStyleColorFunction: true,
    // }) as Preset,
  ],
  /**
   * 自定义快捷语句
   * @see https://github.com/unocss/unocss#shortcuts
   */
  shortcuts: [['center', 'flex justify-center items-center']],
  /**
   * 安全列表 - 确保动态类名被正确生成
   * @see https://unocss.dev/config/#safelist
   */
  safelist: [
    // 文件类型图标
    'i-mdi-file-pdf-box',
    'i-mdi-file-word-box',
    'i-mdi-file-document',
    // 其他常用图标
    'i-mdi-plus',
    'i-mdi-dots-vertical',
    'i-mdi-file-upload',
    'i-mdi-lightbulb',
    'i-mdi-check-circle',
    'i-mdi-pencil',
    'i-mdi-delete',
    // 题库相关图标
    'i-mdi-laptop',
    'i-mdi-brain',
    'i-mdi-chart-bar',
    'i-mdi-code-braces',
    'i-mdi-shield-check',
    'i-mdi-database',
    'i-mdi-server',
    'i-mdi-sitemap',
    'i-mdi-bug',
    'i-mdi-code-branch',
    'i-mdi-laptop-code',
    'i-mdi-account-group',
    'i-mdi-book-open-variant',
    'i-mdi-bookmark',
    'i-mdi-bookmark-outline',
    // FontAwesome 图标 - 认证页面使用
    'i-fa-solid-arrow-left',
    'i-fa-solid-arrow-right',
    'i-fa-solid-key',
    'i-fa-solid-check',
    'i-fa-solid-check-circle',
    'i-fa-solid-exclamation-circle',
    'i-fa-solid-info-circle',
    'i-fa-solid-mobile',
    'i-fa-solid-shield',
    'i-fa-solid-lock',
  ],
  transformers: [
    // 启用 @apply 功能
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
    // Don't change the following order
    transformerAttributify({
      // 解决与第三方框架样式冲突问题
      prefixedOnly: true,
      prefix: 'fg',
    }),
  ],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
  ],
})

/**
 * 最终这一套组合下来会得到：
 * mp 里面：mt-4 => margin-top: 32rpx  == 16px
 * h5 里面：mt-4 => margin-top: 1rem == 16px
 *
 * 如果是传统方式写样式，则推荐设计稿设置为 750，这样设计稿1px，代码写1rpx。
 * rpx是响应式的，可以让不同设备的屏幕显示效果保持一致。
 */
