<template>
  <view class="loading-skeleton">
    <!-- 导航栏骨架 -->
    <view v-if="showNav" class="nav-skeleton">
      <view class="nav-item skeleton-item"></view>
      <view class="nav-center skeleton-item"></view>
      <view class="nav-item skeleton-item"></view>
    </view>

    <!-- 欢迎区域骨架 -->
    <view v-if="showWelcome" class="welcome-skeleton">
      <view class="avatar-skeleton skeleton-item"></view>
      <view class="title-skeleton skeleton-item"></view>
      <view class="desc-skeleton skeleton-item"></view>
    </view>

    <!-- 消息列表骨架 -->
    <view v-if="showMessages" class="messages-skeleton">
      <view v-for="i in messageCount" :key="i" class="message-skeleton">
        <view class="message-avatar skeleton-item"></view>
        <view class="message-content">
          <view class="message-line skeleton-item" :style="{ width: getRandomWidth() }"></view>
          <view class="message-line skeleton-item" :style="{ width: getRandomWidth() }"></view>
          <view v-if="Math.random() > 0.5" class="message-line skeleton-item" :style="{ width: getRandomWidth() }"></view>
        </view>
      </view>
    </view>

    <!-- 输入区域骨架 -->
    <view v-if="showInput" class="input-skeleton">
      <view class="input-field skeleton-item"></view>
      <view class="input-button skeleton-item"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  showNav?: boolean
  showWelcome?: boolean
  showMessages?: boolean
  showInput?: boolean
  messageCount?: number
}>(), {
  showNav: false,
  showWelcome: false,
  showMessages: false,
  showInput: false,
  messageCount: 3
})

const getRandomWidth = () => {
  const widths = ['60%', '80%', '45%', '90%', '70%']
  return widths[Math.floor(Math.random() * widths.length)]
}
</script>

<style lang="scss" scoped>
.loading-skeleton {
  padding: 24rpx;
}

.skeleton-item {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

.nav-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 32rpx;

  .nav-item {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }

  .nav-center {
    width: 200rpx;
    height: 64rpx;
    border-radius: 32rpx;
  }
}

.welcome-skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;

  .avatar-skeleton {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    margin-bottom: 40rpx;
  }

  .title-skeleton {
    width: 240rpx;
    height: 48rpx;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
  }

  .desc-skeleton {
    width: 320rpx;
    height: 32rpx;
    border-radius: 16rpx;
  }
}

.messages-skeleton {
  margin-bottom: 40rpx;

  .message-skeleton {
    display: flex;
    align-items: flex-start;
    padding: 24rpx;
    margin-bottom: 32rpx;

    &:nth-child(even) {
      flex-direction: row-reverse;

      .message-content {
        margin-right: 24rpx;
        margin-left: 0;
        align-items: flex-end;
      }
    }

    .message-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .message-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 24rpx;

      .message-line {
        height: 32rpx;
        margin-bottom: 12rpx;
        border-radius: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.input-skeleton {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: white;
  border-top: 1px solid #f0f0f0;

  .input-field {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
  }

  .input-button {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
