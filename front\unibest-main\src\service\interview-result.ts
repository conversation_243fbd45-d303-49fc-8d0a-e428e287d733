/**
 * 面试结果接口服务
 * 提供面试结果和分析相关接口
 */

import { httpGet, httpPost } from '@/utils/http'
import { IResData } from '@/types/interview-select'

// API 端点常量
const INTERVIEW_RESULT_API_ENDPOINTS = {
  GET_RESULT_SUMMARY: '/interview/result/summary',
  GET_RESULT_DETAIL: '/interview/result/detail',
  GET_PERFORMANCE_METRICS: '/interview/result/metrics',
  SAVE_TO_HISTORY: '/interview/result/save',
  SHARE_RESULT: '/interview/result/share',
  GET_IMPROVEMENT_PLAN: '/interview/result/improvement-plan',
  GET_LEARNING_RESOURCES: '/interview/result/learning-resources',
}

// 面试结果摘要
export interface InterviewResultSummary {
  id: string
  sessionId: string
  jobName: string
  company: string
  mode: string
  date: string
  duration: string
  totalScore: number
  rank: 'excellent' | 'good' | 'average' | 'poor'
  rankText: string
  percentile: number
  answeredQuestions: number
  totalQuestions: number
  status: 'completed' | 'partial' | 'cancelled'
  topStrengths: string[]
  topWeaknesses: string[]
}

// 维度得分
export interface DimensionScore {
  dimension: string
  score: number
  maxScore: number
  percentile: number
  description: string
  strengths: string[]
  weaknesses: string[]
  recommendations: string[]
}

// 问题答案分析
export interface QuestionAnalysis {
  id: string
  questionId: string
  question: string
  category: string
  difficulty: number
  answer: string
  score: number
  audioScore?: number
  videoScore?: number
  textScore?: number
  feedback: string
  strengths: string[]
  weaknesses: string[]
  keywordMatches: string[]
  idealAnswer: string
  timeSpent: number
  timeLimit: number
}

// 详细结果
export interface InterviewResultDetail extends InterviewResultSummary {
  dimensionScores: DimensionScore[]
  questionAnalyses: QuestionAnalysis[]
  overallFeedback: string
  audioMetrics?: {
    clarity: number
    fluency: number
    confidence: number
    pace: number
    overall: number
  }
  videoMetrics?: {
    eyeContact: number
    posture: number
    expressions: number
    gestures: number
    overall: number
  }
}

// 性能指标
export interface PerformanceMetrics {
  technical: number
  communication: number
  problemSolving: number
  teamwork: number
  leadership: number
  creativity: number
  detailedMetrics: Record<string, number>
  industryAverage: Record<string, number>
  skillGaps: string[]
  skillStrengths: string[]
}

// 学习资源
export interface LearningResource {
  id: string
  title: string
  type: 'course' | 'article' | 'video' | 'book' | 'exercise'
  description: string
  duration: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  url: string
  imageUrl?: string
  tags: string[]
  relevanceScore: number
}

// 提升计划
export interface ImprovementPlan {
  areas: Array<{
    name: string
    currentLevel: number
    targetLevel: number
    timeframe: string
    actions: string[]
  }>
  shortTermGoals: string[]
  mediumTermGoals: string[]
  longTermGoals: string[]
  recommendedResources: LearningResource[]
}

// 分享请求
export interface ShareResultRequest {
  resultId: string
  platform: 'wechat' | 'qq' | 'weibo' | 'link'
  content?: string
}

/**
 * @description 获取面试结果摘要
 * @param resultId 结果ID或会话ID
 * @returns 面试结果摘要
 */
export async function getResultSummary(resultId: string): Promise<IResData<InterviewResultSummary>> {
  return httpGet<InterviewResultSummary>(INTERVIEW_RESULT_API_ENDPOINTS.GET_RESULT_SUMMARY, { resultId })
}

/**
 * @description 获取面试结果详情
 * @param resultId 结果ID或会话ID
 * @returns 面试结果详情
 */
export async function getResultDetail(resultId: string): Promise<IResData<InterviewResultDetail>> {
  return httpGet<InterviewResultDetail>(INTERVIEW_RESULT_API_ENDPOINTS.GET_RESULT_DETAIL, { resultId })
}

/**
 * @description 获取性能指标
 * @param resultId 结果ID
 * @returns 性能指标数据
 */
export async function getPerformanceMetrics(resultId: string): Promise<IResData<PerformanceMetrics>> {
  return httpGet<PerformanceMetrics>(INTERVIEW_RESULT_API_ENDPOINTS.GET_PERFORMANCE_METRICS, { resultId })
}

/**
 * @description 保存到历史记录
 * @param resultId 结果ID
 * @param title 自定义标题（可选）
 * @returns 保存结果
 */
export async function saveToHistory(resultId: string, title?: string): Promise<IResData<{ success: boolean, historyId: string }>> {
  return httpPost<{ success: boolean, historyId: string }>(
    INTERVIEW_RESULT_API_ENDPOINTS.SAVE_TO_HISTORY, 
    { resultId, title }
  )
}

/**
 * @description 分享结果
 * @param data 分享请求数据
 * @returns 分享结果
 */
export async function shareResult(data: ShareResultRequest): Promise<IResData<{ 
  success: boolean
  shareUrl: string
}>> {
  return httpPost<{ success: boolean, shareUrl: string }>(
    INTERVIEW_RESULT_API_ENDPOINTS.SHARE_RESULT, 
    data
  )
}

/**
 * @description 获取提升计划
 * @param resultId 结果ID
 * @param userId 用户ID（可选）
 * @returns 提升计划
 */
export async function getImprovementPlan(resultId: string, userId?: string): Promise<IResData<ImprovementPlan>> {
  return httpGet<ImprovementPlan>(
    INTERVIEW_RESULT_API_ENDPOINTS.GET_IMPROVEMENT_PLAN, 
    { resultId, userId }
  )
}

/**
 * @description 获取学习资源推荐
 * @param resultId 结果ID
 * @param limit 数量限制
 * @returns 学习资源列表
 */
export async function getLearningResources(resultId: string, limit?: number): Promise<IResData<LearningResource[]>> {
  return httpGet<LearningResource[]>(
    INTERVIEW_RESULT_API_ENDPOINTS.GET_LEARNING_RESOURCES, 
    { resultId, limit }
  )
} 