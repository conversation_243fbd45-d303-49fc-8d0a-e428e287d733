/**
 * 新用户引导相关类型定义
 */

// 初始能力评估结果类型
export interface InitialAbilityAssessment {
  professionalKnowledge: number
  logicalThinking: number
  languageExpression: number
  stressResistance: number
  teamCollaboration: number
  innovation: number
  overallScore: number
}

// 用户成长阶段
export type UserGrowthStage = 'new_user' | 'beginner' | 'intermediate' | 'advanced' | 'expert'

// 用户成长档案
export interface UserGrowthProfile {
  userId: string
  currentStage: UserGrowthStage
  joinDate: string
  lastActiveDate: string
  totalInterviews: number
  initialAssessment: InitialAbilityAssessment
  currentAssessment: InitialAbilityAssessment
  improvementRate: number
  targetPosition: string
  learningGoals: string[]
  achievements: string[]
  continuousLearningDays: number
  completedCourses: number
}

// 评估问题选项
export interface AssessmentOption {
  id: string
  text: string
  score: number
}

// 评估问题
export interface AssessmentQuestion {
  id: string
  type: 'single' | 'scale'
  category: string
  question: string
  options?: AssessmentOption[]
  minValue?: number
  maxValue?: number
}

// 评估结果
export interface AssessmentResult {
  questionId: string
  answer: string | number
  score: number
  category: string
}

// 详细能力报告
export interface DetailedAbilityReport {
  strengths: string[]
  weaknesses: string[]
  recommendations: {
    resources: {
      title: string
      type: string
      url: string
      description: string
    }[]
    practices: {
      title: string
      description: string
      difficulty: string
    }[]
  }
}
