/**
 * 意见反馈服务模块
 * 统一管理所有反馈相关的API接口
 */

import { httpGet, httpPost } from '@/utils/http'
import type {
  ApiResponse,
  SubmitFeedbackParams,
  FeedbackDetail,
  FeedbackListParams,
  FeedbackListResponse,
  FeedbackStats,
} from '@/types/feedback'

/**
 * 提交意见反馈
 * @param params 反馈提交参数
 * @returns API响应
 */
export function submitFeedback(params: {
  params: SubmitFeedbackParams
}): Promise<ApiResponse<{ id: string }>> {
  // 获取系统信息用于上报
  const systemInfo = uni.getSystemInfoSync()

  const requestData = {
    ...params.params,
    deviceInfo:
      `${systemInfo.brand || ''} ${systemInfo.model || ''} ${systemInfo.system || ''}`.trim(),
    appVersion: '1.0.0', // 可以从配置文件读取
    platform: __UNI_PLATFORM__,
  }

  return httpPost<{ id: string }>('/app/feedback', requestData)
}

/**
 * 获取反馈详情
 * @param id 反馈ID
 * @returns 反馈详情响应
 */
export function getFeedbackDetail(id: string): Promise<ApiResponse<FeedbackDetail>> {
  return httpGet<FeedbackDetail>(`/app/feedback/${id}`)
}

/**
 * 获取用户反馈列表
 * @param params 查询参数
 * @returns 反馈列表响应
 */
export function getUserFeedbackList(
  params?: FeedbackListParams,
): Promise<ApiResponse<FeedbackListResponse>> {
  return httpGet<FeedbackListResponse>('/app/feedback/list', params)
}

/**
 * 获取反馈统计信息
 * @returns 反馈统计响应
 */
export function getFeedbackStats(): Promise<ApiResponse<FeedbackStats>> {
  return httpGet<FeedbackStats>('/app/feedback/stats')
}

/**
 * 删除反馈
 * @param id 反馈ID
 * @returns API响应
 */
export function deleteFeedback(id: string): Promise<ApiResponse<void>> {
  return httpPost<void>(`/app/feedback/${id}/delete`)
}
