---
description: 
globs: 
alwaysApply: true
---
---

description:
globs:
alwaysApply: false
---

# UniApp 项目规范文档

## 项目概述

本项目是基于 uniapp 的最佳实践框架项目，使用 Vue3 + Vite5 + Pnpm + TypeScript + UnoCSS 技术栈进行开发。项目支持多平台部署，包括微信小程序、H5、APP、支付宝小程序、钉钉小程序、抖音小程序等。

## 技术栈

- **前端框架**：Vue3
- **开发语言**：TypeScript
- **构建工具**：Vite5
- **包管理器**：Pnpm
- **CSS框架**：UnoCSS + tailwindcss
- **跨平台框架**：UniApp
- **Icon 图标库**：(必须使用uni-icons + font-awesome 图标库中的图标)

## 注释规则

- 所有注释都要为中文
- 所有方法都要有注释 去解释这个方法是怎么用的
- 例如 :
/**

- @description 显示通知栏
- @param message 通知内容
- @param type 通知类型（info/success/error）
- @param duration 显示时长（毫秒）
 */
function showNotification(message: string, type = 'info', duration = 3000) {

- 注释必须简明扼要，避免冗长的解释

## 目录结构

``` txt
src/
├── components/        # 公共组件目录
├── hooks/             # 自定义钩子函数
├── interceptors/      # 拦截器目录
├── layouts/           # 布局组件目录
├── pages/             # 页面目录
├── pages-sub/         # 分包页面目录
├── service/           # API服务目录
├── static/            # 静态资源目录
├── store/             # 状态管理目录
├── style/             # 全局样式目录
├── types/             # 类型定义目录
├── uni_modules/       # uni-app模块目录
└── utils/             # 工具函数目录
```

## 页面结构

项目包含以下主要页面：

- 登录页
- 注册页
- 个人中心页
- 面试岗位选择页
- 模拟面试页
- 面试结果页
- 学习资源推荐页
- 历史记录页

## 代码规范

### 命名规范

- **组件**：多单词组件名，使用 PascalCase，如 `UserProfile.vue`
- **变量**：使用 camelCase，如 `userName`
- **函数**：使用 camelCase，如 `getUserInfo()`
- **CSS类名**：使用 kebab-case，如 `user-profile`

### 代码风格

#### Prettier 配置

```json
{
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "semi": false,
  "trailingComma": "all",
  "endOfLine": "auto",
  "htmlWhitespaceSensitivity": "ignore"
}
```

#### ESLint 配置

项目使用了多种 ESLint 规则集成，包括：

- eslint:recommended
- plugin:@typescript-eslint/recommended
- plugin:vue/vue3-essential
- plugin:import/recommended
- standard
- prettier
- plugin:prettier/recommended

主要自定义规则包括：

- 关闭未使用变量警告
- 允许使用 console
- 关闭组件命名限制
- 关闭 any 类型限制
- 等等

#### Stylelint 配置

项目使用了以下 Stylelint 配置：

- stylelint-config-recommended
- stylelint-config-recommended-scss
- stylelint-config-recommended-vue/scss
- stylelint-config-html/vue
- stylelint-config-recess-order

自定义规则：

- 支持 rpx 单位（小程序特有单位）
- 支持 page 选择器（小程序特有选择器）
- 支持 v-deep 和 deep 伪类

### Git 提交规范

使用 Commitlint 规范提交信息，类型包括：

- feat: 新功能
- fix: 修复问题
- perf: 性能优化
- style: 代码风格调整
- docs: 文档更新
- test: 测试相关
- refactor: 代码重构
- build: 构建相关
- ci: CI配置相关
- chore: 其他修改
- revert: 回滚
- wip: 开发中
- workflow: 工作流相关
- types: 类型定义
- release: 发布相关

## VS Code 配置

### 推荐扩展

- vue.volar
- stylelint.vscode-stylelint
- esbenp.prettier-vscode
- dbaeumer.vscode-eslint
- antfu.unocss
- antfu.iconify
- evils.uniapp-vscode
- uni-helper.uni-helper-vscode
- uni-helper.uni-app-schemas-vscode
- uni-helper.uni-highlight-vscode
- uni-helper.uni-ui-snippets-vscode
- uni-helper.uni-app-snippets-vscode
- mrmlnc.vscode-json5
- streetsidesoftware.code-spell-checker

### 编辑器设置

```json
{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "stylelint.validate": ["css", "scss", "vue", "html"],
  "stylelint.enable": true,
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "files.associations": {
    "pages.json": "jsonc",
    "manifest.json": "jsonc"
  }
}
```

## 多平台开发注意事项

1. 使用条件编译处理平台差异

   ```js
   // #ifdef H5
   // 仅在 H5 平台下编译
   // #endif
   
   // #ifdef APP-PLUS
   // 仅在APP下编译
   // #endif
   ```

2. 样式适配
   - 使用 rpx 单位进行响应式布局
   - 注意不同平台样式差异，必要时使用条件编译

3. API 兼容
   - 优先使用 uni API 保证多端兼容
   - 平台特有 API 使用条件编译包裹

4. 组件选择
   - 优先使用 uni-ui 等跨平台组件
   - 自定义组件需考虑多端兼容性

## 开发流程

1. 代码提交前会自动执行：
   - prettier 格式化代码
   - eslint 检查并修复 JS/TS/Vue 代码
   - stylelint 检查并修复样式代码

2. Git 提交时：
   - 使用 commitlint 检查提交消息格式
   - 建议使用 `pnpm cz` 命令规范化提交消息

## 编译部署

使用以下命令编译不同平台版本：

- H5: `pnpm build:h5`
- APP: 通过 HBuilderX 云打包
- 其他平台: 参考 package.json 中的构建命令

## 页面css样式规范

- 要兼容H5端和APP端
- 这俩端的样式要一致
- 强制要求不要使用 :class='{...}'

## 组件

- 组件要支持H5端和APP端
- 组件的样式要一致
- 组件的props要规范
- 组件的事件要规范
- 组件的插槽要规范
- 组件的文档要规范
- 组件的demo要规范
- src/components 是存放自定义组件的位置
- 组件的props要使用defineProps定义
- 组件的事件要使用defineEmits定义
- 组件的插槽要使用slot定义
- 组件的文档要使用/**/注释定义
- 组件的demo要使用.vue文件定义
- 组件的样式要使用scss定义
- 组件的样式要使用BEM规范定义
- 组件的样式要使用变量定义
- 组件的样式要使用mixin定义
- 组件的样式要使用extends定义

# 页面性能规范

# UniApp-X 页面性能优化规范

## 概述

基于 [官方性能优化指南](https://doc.dcloud.net.cn/uni-app-x/performance.html)，本文档提供了针对 uni-app-x 应用的完整性能优化方案，确保应用在各平台（特别是 Android）上达到原生级别的性能表现。

## 核心优化原则

### 1. DOM 元素数量控制

**问题**：Android 对 DOM 数量和层次更加苛刻，过多的 DOM 元素会导致渲染性能下降。

**解决方案**：

- **目标**：单页面 DOM 元素控制在 200 个以内
- **监控**：使用 HBuilderX 真机运行时的性能数据监控
- **优化手段**：

  ```vue
  <!-- ❌ 错误：过度嵌套 -->
  <view class="container">
    <view class="wrapper">
      <view class="inner">
        <view class="content">
          <text>内容</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- ✅ 正确：扁平化结构 -->
  <view class="content">
    <text>内容</text>
  </view>
  ```

### 2. 响应式数据优化

**使用 shallowRef 和 markRaw 减少深度响应式开销**：

```typescript
// ✅ 对于复杂对象使用 shallowRef
const userProfile = shallowRef(null)
const complexData = shallowRef([])

// ✅ 对于不需要响应式的数据使用 markRaw
userProfile.value = markRaw(apiResponse.data)

// ❌ 避免深度响应式
const userProfile = ref({
  // 大量嵌套对象...
})
```

## 组件渲染优化

### 1. 懒加载组件

**避免首屏阻塞，提升加载速度**：

```vue
<template>
  <!-- 占位符显示 -->
  <view v-if="!shouldLoadComponent" class="placeholder">
    <text>加载中...</text>
  </view>
  
  <!-- 懒加载组件 -->
  <HeavyComponent v-else :data="componentData" />
</template>

<script setup>
const shouldLoadComponent = ref(false)

onMounted(() => {
  // 延迟加载非关键组件
  setTimeout(() => {
    shouldLoadComponent.value = true
  }, 100)
})
</script>
```

### 2. 条件渲染优化

```vue
<!-- ✅ 使用 v-show 代替频繁切换的 v-if -->
<view v-show="isVisible" class="modal">Modal Content</view>

<!-- ✅ 对于确定不会变化的条件使用 v-if -->
<view v-if="hasPermission" class="admin-panel">Admin Content</view>
```

### 3. 列表渲染优化

```vue
<!-- ✅ 为列表项提供稳定的 key -->
<view v-for="item in list" :key="item.id" class="list-item">
  {{ item.name }}
</view>

<!-- ✅ 对于大型列表考虑虚拟滚动 -->
<list-view :data="largeList" :item-height="80" />
```

## 样式性能优化

### 1. GPU 加速

**为动画元素启用硬件加速**：

```scss
.animated-element {
  // ✅ 启用 GPU 加速
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  
  &:hover {
    transform: translateY(-2px) translateZ(0);
  }
}
```

### 2. 布局优化

```scss
.container {
  // ✅ 使用 contain 属性限制重排范围
  contain: layout style paint;
  
  // ✅ 避免强制同步布局
  .item {
    // 指定明确的尺寸
    width: 200rpx;
    height: 100rpx;
  }
}
```

### 3. 滚动性能优化

```scss
.scroll-container {
  // ✅ 优化滚动性能
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  
  // ✅ 避免不必要的重绘
  .scroll-item {
    contain: layout;
  }
}
```

## 数据加载优化

### 1. 防抖和节流

```typescript
// ✅ 防抖处理高频操作
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timer: number | null = null
  return ((...args: any[]) => {
    if (timer) clearTimeout(timer)
    timer = window.setTimeout(() => func(...args), delay)
  }) as T
}

// ✅ 应用到事件处理
const handleClick = debounce(() => {
  // 处理点击事件
}, 300)
```

### 2. 并行加载优化

```typescript
// ✅ 使用 Promise.allSettled 并行加载
async function loadDashboard() {
  const [userResult, statsResult, tasksResult] = await Promise.allSettled([
    loadUserData(),
    loadStats(),
    loadTasks()
  ])
  
  // 处理各个结果...
}
```

## 内存管理

### 1. 组件清理

```typescript
// ✅ 页面卸载时清理资源
onUnload(() => {
  // 清理定时器
  if (timer) clearTimeout(timer)
  
  // 清理事件监听
  eventBus.off('event', handler)
  
  // 清理缓存
  cache.clear()
})
```

### 2. 避免内存泄漏

```typescript
// ❌ 可能导致内存泄漏
const intervalId = setInterval(() => {
  // 更新数据
}, 1000)

// ✅ 正确的清理方式
onMounted(() => {
  const intervalId = setInterval(() => {
    // 更新数据
  }, 1000)
  
  onUnmounted(() => {
    clearInterval(intervalId)
  })
})
```

## 网络请求优化

### 1. 请求合并

```typescript
// ✅ 合并多个请求
class RequestBatcher {
  private batch: string[] = []
  private timer: number | null = null
  
  add(url: string) {
    this.batch.push(url)
    
    if (this.timer) clearTimeout(this.timer)
    this.timer = window.setTimeout(() => {
      this.flush()
    }, 50)
  }
  
  private async flush() {
    if (this.batch.length === 0) return
    
    const urls = [...this.batch]
    this.batch = []
    
    // 批量请求
    await this.batchRequest(urls)
  }
}
```

### 2. 请求优先级

```typescript
// ✅ 关键数据优先加载
async function loadPageData() {
  // 优先加载关键数据
  const criticalData = await loadCriticalData()
  
  // 并行加载次要数据
  Promise.all([
    loadSecondaryData(),
    loadOptionalData()
  ])
  
  return criticalData
}
```

## 动画性能优化

### 1. 使用 Transform 代替位置属性

```scss
// ❌ 避免直接修改 position 属性
.move-element {
  left: 100px; // 触发重排
  top: 100px;  // 触发重排
}

// ✅ 使用 transform
.move-element {
  transform: translate(100px, 100px); // 只触发合成
}
```

### 2. 合理使用 will-change

```scss
.animated-element {
  // ✅ 动画开始前声明
  will-change: transform;
  
  &.animating {
    transform: translateX(100px);
  }
  
  // ✅ 动画结束后移除
  &.animation-complete {
    will-change: auto;
  }
}
```

## 页面生命周期优化

### 1. 分阶段初始化

```typescript
onLoad(() => {
  // 立即执行关键初始化
  initCriticalFeatures()
  
  // 使用 requestAnimationFrame 延迟次要初始化
  requestAnimationFrame(() => {
    initSecondaryFeatures()
  })
  
  // 使用 setTimeout 延迟可选功能
  setTimeout(() => {
    initOptionalFeatures()
  }, 0)
})
```

### 2. 页面预加载

```typescript
// ✅ 预加载关键页面
onShow(() => {
  // 预加载用户可能访问的页面
  uni.preloadPage({
    url: '/pages/next/page'
  })
})
```

## 性能监控指标

### 关键指标

1. **DOM 数量**：< 200 个元素
2. **内存使用**：< 150MB
3. **FPS**：> 50 帧/秒
4. **首屏渲染**：< 800ms
5. **页面切换**：< 300ms

### 监控方法

```typescript
// ✅ 性能监控
class PerformanceMonitor {
  static measurePageLoad() {
    const start = Date.now()
    
    onMounted(() => {
      const loadTime = Date.now() - start
      console.log(`页面加载耗时: ${loadTime}ms`)
      
      // 上报性能数据
      this.reportMetrics({
        loadTime,
        memoryUsage: this.getMemoryUsage()
      })
    })
  }
  
  static getMemoryUsage() {
    // 获取内存使用情况
    return performance.memory?.usedJSHeapSize || 0
  }
}
```

## 平台特定优化

### Android 优化

1. **减少 View 层级**：每层嵌套增加 5-10ms 渲染时间
2. **避免频繁创建组件**：使用对象池复用组件实例
3. **优化图片加载**：使用 WebP 格式，启用硬件解码

### H5 优化

1. **启用 Gzip 压缩**
2. **使用 CDN 加速静态资源**
3. **实现 Service Worker 缓存**

## 最佳实践检查清单

### 开发阶段

- [ ] 控制单页面 DOM 数量 < 200
- [ ] 使用 shallowRef 处理复杂对象
- [ ] 实现组件懒加载
- [ ] 添加防抖处理高频操作
- [ ] 使用 transform 实现动画
- [ ] 添加性能监控代码

### 测试阶段

- [ ] 真机测试性能指标
- [ ] 检查内存使用情况
- [ ] 验证滚动流畅度
- [ ] 测试弱网环境表现

### 发布前

- [ ] 开启代码压缩
- [ ] 优化图片资源
- [ ] 清理无用代码
- [ ] 验证所有平台性能

## 常见问题解决

### 问题 1：列表滚动卡顿

**解决方案**：

1. 使用虚拟滚动组件
2. 减少列表项 DOM 复杂度
3. 启用 GPU 加速

### 问题 2：页面切换慢

**解决方案**：

1. 实现页面预加载
2. 优化页面初始化逻辑
3. 使用骨架屏提升感知性能

### 问题 3：内存持续增长

**解决方案**：

1. 检查事件监听器清理
2. 避免闭包引用大对象
3. 定期清理缓存数据
