<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 页面参数
const resourceId = ref('')

// 资源详情数据
const resourceDetail = ref({
  id: 1,
  title: '面试综合技巧进阶',
  description: '跨行业面试核心要点，提升综合表达能力和面试技巧',
  cover: '/static/course-cover.jpg',
  instructor: '张教授',
  duration: '2小时30分',
  difficulty: 3,
  rating: 4.9,
  students: 1234,
  price: 299,
  chapters: [
    {
      id: 1,
      title: '第一章：面试基础准备',
      duration: '30分钟',
      isCompleted: true,
      lessons: [
        { id: 1, title: '面试前的准备工作', duration: '10分钟', isCompleted: true },
        { id: 2, title: '常见面试问题分析', duration: '15分钟', isCompleted: true },
        { id: 3, title: '面试心理调节', duration: '5分钟', isCompleted: false },
      ],
    },
    {
      id: 2,
      title: '第二章：专业技能展示',
      duration: '45分钟',
      isCompleted: false,
      lessons: [
        { id: 4, title: '技能介绍技巧', duration: '20分钟', isCompleted: false },
        { id: 5, title: '案例分析方法', duration: '15分钟', isCompleted: false },
        { id: 6, title: '实战演练', duration: '10分钟', isCompleted: false },
      ],
    },
    {
      id: 3,
      title: '第三章：沟通表达技巧',
      duration: '35分钟',
      isCompleted: false,
      lessons: [
        { id: 7, title: '语言表达技巧', duration: '15分钟', isCompleted: false },
        { id: 8, title: '肢体语言运用', duration: '10分钟', isCompleted: false },
        { id: 9, title: '应对突发问题', duration: '10分钟', isCompleted: false },
      ],
    },
  ],
  tags: ['面试技巧', '沟通表达', '职业发展', '综合能力'],
  features: ['资深面试官授课', '真实面试场景模拟', '个性化学习路径', '24小时答疑服务'],
})

// 是否已收藏
const isFavorited = ref(false)

// 是否已加入学习
const isEnrolled = ref(false)

// 当前展开的章节
const expandedChapter = ref<number | null>(null)

/**
 * 页面加载时获取资源ID
 */
onLoad((options: any) => {
  if (options?.id) {
    resourceId.value = options.id
    loadResourceDetail(options.id)
  }
})

/**
 * 加载资源详情数据
 * @param id 资源ID
 */
const loadResourceDetail = async (id: string) => {
  try {
    // 这里应该调用API获取详情数据
    console.log('加载资源详情:', id)
    // 模拟API调用
    // const detail = await resourceApi.getDetail(id)
    // resourceDetail.value = detail
  } catch (error) {
    console.error('加载资源详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    })
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  uni.navigateBack()
}

/**
 * 切换收藏状态
 */
const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  uni.showToast({
    title: isFavorited.value ? '已收藏' : '已取消收藏',
    icon: 'success',
  })
}

/**
 * 分享资源
 */
const shareResource = () => {
  // #ifdef MP-WEIXIN
  uni.showShareMenu({
    withShareTicket: true,
  })
  // #endif

  // #ifdef H5
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none',
  })
  // #endif
}

/**
 * 开始学习
 */
const startLearning = () => {
  if (!isEnrolled.value) {
    uni.showModal({
      title: '开始学习',
      content: `确定要开始学习《${resourceDetail.value.title}》吗？`,
      success: (res) => {
        if (res.confirm) {
          isEnrolled.value = true
          uni.showToast({
            title: '已加入学习计划',
            icon: 'success',
          })
          // 跳转到第一个未完成的课程
          const firstUncompletedLesson = findFirstUncompletedLesson()
          if (firstUncompletedLesson) {
            goToLesson(firstUncompletedLesson)
          }
        }
      },
    })
  } else {
    // 继续学习
    const firstUncompletedLesson = findFirstUncompletedLesson()
    if (firstUncompletedLesson) {
      goToLesson(firstUncompletedLesson)
    } else {
      uni.showToast({
        title: '课程已全部完成',
        icon: 'success',
      })
    }
  }
}

/**
 * 查找第一个未完成的课程
 */
const findFirstUncompletedLesson = () => {
  for (const chapter of resourceDetail.value.chapters) {
    for (const lesson of chapter.lessons) {
      if (!lesson.isCompleted) {
        return lesson
      }
    }
  }
  return null
}

/**
 * 跳转到课程
 * @param lesson 课程信息
 */
const goToLesson = (lesson: any) => {
  uni.navigateTo({
    url: `/pages/learning/lesson?id=${lesson.id}&resourceId=${resourceId.value}`,
  })
}

/**
 * 切换章节展开状态
 * @param chapterId 章节ID
 */
const toggleChapter = (chapterId: number) => {
  expandedChapter.value = expandedChapter.value === chapterId ? null : chapterId
}

/**
 * 获取难度星级
 * @param difficulty 难度等级
 */
const getDifficultyStars = (difficulty: number) => {
  return '★'.repeat(difficulty) + '☆'.repeat(5 - difficulty)
}

/**
 * 获取完成进度
 */
const getCompletionProgress = () => {
  const totalLessons = resourceDetail.value.chapters.reduce(
    (total, chapter) => total + chapter.lessons.length,
    0,
  )
  const completedLessons = resourceDetail.value.chapters.reduce(
    (total, chapter) => total + chapter.lessons.filter((lesson) => lesson.isCompleted).length,
    0,
  )
  return totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
}

// 页面初始化处理
const initPage = () => {
  console.log('学习详情页面加载完成')
}

// 页面加载时执行初始化
initPage()
</script>

<template>
  <view class="detail-container">
    <!-- 顶部导航 -->
    <view class="nav-bar bg-gradient-to-r from-teal-500 to-teal-600 text-white">
      <view class="status-bar"></view>
      <view class="nav-content flex items-center justify-between px-4 py-3">
        <view class="flex items-center">
          <button class="nav-btn" @click="goBack">
            <view class="i-fa-solid-arrow-left text-lg"></view>
          </button>
          <text class="ml-3 text-lg font-medium">课程详情</text>
        </view>
        <view class="flex items-center space-x-3">
          <button class="nav-btn" @click="toggleFavorite">
            <view
              class="text-lg"
              :class="isFavorited ? 'i-fa-solid-heart text-red-300' : 'i-fa-solid-heart-o'"
            ></view>
          </button>
          <button class="nav-btn" @click="shareResource">
            <view class="i-fa-solid-share text-lg"></view>
          </button>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view scroll-y class="detail-content">
      <!-- 课程头部信息 -->
      <view class="course-header bg-white p-6">
        <text class="course-title text-2xl font-bold text-gray-800 mb-4 block">
          {{ resourceDetail.title }}
        </text>

        <view class="course-meta flex items-center mb-4 text-sm text-gray-600">
          <text class="mr-4">👨‍🏫 {{ resourceDetail.instructor }}</text>
          <text class="mr-4">⏱️ {{ resourceDetail.duration }}</text>
          <text>{{ getDifficultyStars(resourceDetail.difficulty) }}</text>
        </view>

        <view class="course-stats flex items-center mb-4">
          <view class="stat-item mr-6">
            <view class="flex items-center">
              <view class="i-fa-solid-star text-yellow-500 mr-1"></view>
              <text class="font-bold text-gray-800">{{ resourceDetail.rating }}</text>
            </view>
          </view>
          <view class="stat-item mr-6">
            <view class="flex items-center">
              <view class="i-fa-solid-users text-blue-500 mr-1"></view>
              <text class="text-gray-600">{{ resourceDetail.students }} 人学习</text>
            </view>
          </view>
          <view class="stat-item">
            <text class="text-teal-600 font-bold text-lg">¥{{ resourceDetail.price }}</text>
          </view>
        </view>

        <text class="course-desc text-gray-600 leading-relaxed">
          {{ resourceDetail.description }}
        </text>
      </view>

      <!-- 学习进度 -->
      <view v-if="isEnrolled" class="progress-section bg-white mt-4 p-6">
        <view class="flex items-center justify-between mb-4">
          <text class="text-lg font-bold text-gray-800">学习进度</text>
          <text class="text-teal-600 font-bold">{{ getCompletionProgress() }}%</text>
        </view>
        <view class="progress-bar bg-gray-200 rounded-full h-2 overflow-hidden">
          <view
            class="h-2 bg-teal-500 rounded-full transition-all duration-500"
            :style="{ width: getCompletionProgress() + '%' }"
          ></view>
        </view>
      </view>

      <!-- 课程大纲 -->
      <view class="course-outline bg-white mt-4 p-6">
        <text class="text-lg font-bold text-gray-800 mb-4 block">课程大纲</text>

        <view class="chapters-list">
          <view
            v-for="chapter in resourceDetail.chapters"
            :key="chapter.id"
            class="chapter-item border border-gray-200 rounded-lg mb-3 overflow-hidden"
          >
            <!-- 章节标题 -->
            <view
              class="chapter-header bg-gray-50 p-4 flex items-center justify-between"
              @click="toggleChapter(chapter.id)"
            >
              <view class="flex-1">
                <text class="font-bold text-gray-800 block">{{ chapter.title }}</text>
                <text class="text-sm text-gray-500">{{ chapter.duration }}</text>
              </view>
              <view class="flex items-center">
                <view
                  v-if="chapter.isCompleted"
                  class="i-fa-solid-check-circle text-green-500 mr-3"
                ></view>
                <view
                  class="transition-transform duration-200"
                  :class="expandedChapter === chapter.id ? 'rotate-180' : ''"
                >
                  <view class="i-fa-solid-chevron-down text-gray-400"></view>
                </view>
              </view>
            </view>

            <!-- 课程列表 -->
            <view v-if="expandedChapter === chapter.id" class="lessons-list">
              <view
                v-for="lesson in chapter.lessons"
                :key="lesson.id"
                class="lesson-item p-4 border-t border-gray-100 flex items-center justify-between"
                @click="goToLesson(lesson)"
              >
                <view class="flex items-center flex-1">
                  <view
                    class="w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3"
                    :class="
                      lesson.isCompleted ? 'bg-green-500 border-green-500' : 'border-gray-300'
                    "
                  >
                    <view
                      v-if="lesson.isCompleted"
                      class="i-fa-solid-check text-white text-xs"
                    ></view>
                  </view>
                  <view class="flex-1">
                    <text class="text-gray-800 block">{{ lesson.title }}</text>
                    <text class="text-sm text-gray-500">{{ lesson.duration }}</text>
                  </view>
                </view>
                <view class="i-fa-solid-play text-teal-500"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 课程特色 -->
      <view class="course-features bg-white mt-4 p-6">
        <text class="text-lg font-bold text-gray-800 mb-4 block">课程特色</text>
        <view class="features-list">
          <view
            v-for="feature in resourceDetail.features"
            :key="feature"
            class="feature-item flex items-center mb-3"
          >
            <view class="i-fa-solid-check-circle text-green-500 mr-3"></view>
            <text class="text-gray-700">{{ feature }}</text>
          </view>
        </view>
      </view>

      <!-- 标签 -->
      <view class="course-tags bg-white mt-4 p-6">
        <text class="text-lg font-bold text-gray-800 mb-4 block">相关标签</text>
        <view class="tags-list flex flex-wrap">
          <view
            v-for="tag in resourceDetail.tags"
            :key="tag"
            class="tag bg-teal-50 text-teal-600 px-3 py-1 rounded-full text-sm mr-2 mb-2"
          >
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="h-20"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions bg-white border-t border-gray-200 p-4 flex items-center space-x-4">
      <button
        class="flex-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white py-3 rounded-lg font-bold text-center"
        @click="startLearning"
      >
        {{ isEnrolled ? '继续学习' : '开始学习' }}
      </button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.status-bar {
  // #ifdef H5
  height: 0;
  // #endif

  // #ifdef MP-WEIXIN
  height: var(--status-bar-height);
  // #endif
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  margin-top: 80px;
  height: calc(100vh - 80px - 80px);

  // #ifdef MP-WEIXIN
  margin-top: calc(80px + var(--status-bar-height));
  height: calc(100vh - 80px - var(--status-bar-height) - 80px);
  // #endif
}

.course-header {
  border-bottom: 1px solid #f0f0f0;
}

.chapter-item {
  .rotate-180 {
    transform: rotate(180deg);
  }
}

.lesson-item {
  transition: background-color 0.2s;

  // #ifdef H5
  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: #e9ecef;
  }
  // #endif
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

// 间距调整
.space-x-3 > view:not(:first-child) {
  margin-left: 12px;
}

.space-x-4 > view:not(:first-child) {
  margin-left: 16px;
}
</style>
