<script setup lang="ts">
/**
 * 无数据展示组件
 * 用于显示暂无数据、暂无资源等空状态
 * <AUTHOR>
 */

interface Props {
  /** 显示的图标类名 */
  icon?: string
  /** 主要提示文本 */
  text?: string
  /** 描述文本 */
  description?: string
  /** 图标大小 */
  iconSize?: string
  /** 是否显示操作按钮 */
  showAction?: boolean
  /** 操作按钮文本 */
  actionText?: string
  /** 自定义样式类名 */
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'i-fa-solid-info-circle',
  text: '暂无数据',
  description: '',
  iconSize: '60rpx',
  showAction: false,
  actionText: '重新加载',
  customClass: '',
})

/**
 * 定义组件事件
 */
const emit = defineEmits<{
  /** 点击操作按钮时触发 */
  action: []
}>()

/**
 * 处理操作按钮点击
 */
const handleAction = () => {
  emit('action')
  console.log('点击操作按钮')
}
</script>

<template>
  <view class="no-data" :class="customClass">
    <view class="no-data__icon" :style="{ fontSize: iconSize }">
      <view :class="icon"></view>
    </view>
    <text class="no-data__text">{{ text }}</text>
    <text v-if="description" class="no-data__description">{{ description }}</text>
    <button v-if="showAction" class="no-data__action" @click="handleAction">
      {{ actionText }}
    </button>
  </view>
</template>

<style lang="scss" scoped>
/**
 * 无数据组件样式
 * 兼容H5端和微信小程序端
 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 200rpx;

  // 动画效果
  animation: fadeIn 0.6s ease-out;

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 24rpx;
    color: #cbd5e1;
    background: #f8fafc;
    border-radius: 50%;
    font-size: 60rpx;
    transition: all 0.3s ease;

    // H5端悬停效果
    // #ifdef H5
    &:hover {
      color: #94a3b8;
      transform: scale(1.05);
    }
    // #endif
  }

  &__text {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: #64748b;
    margin-bottom: 8rpx;
    text-align: center;
  }

  &__description {
    display: block;
    font-size: 24rpx;
    color: #94a3b8;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 32rpx;
    max-width: 400rpx;
  }

  &__action {
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);
    color: white;
    border: none;
    border-radius: 24rpx;
    font-size: 26rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.2);
    transition: all 0.2s ease;

    // H5端悬停效果
    // #ifdef H5
    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.3);
    }
    // #endif

    &:active {
      transform: scale(0.98);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 不同状态的图标颜色变体
.no-data--error {
  .no-data__icon {
    color: #ef4444;
    background: #fef2f2;
  }
}

.no-data--warning {
  .no-data__icon {
    color: #f59e0b;
    background: #fffbeb;
  }
}

.no-data--info {
  .no-data__icon {
    color: #3b82f6;
    background: #eff6ff;
  }
}

.no-data--success {
  .no-data__icon {
    color: #10b981;
    background: #f0fdf4;
  }
}

// 紧凑模式
.no-data--compact {
  padding: 40rpx 20rpx;
  min-height: 120rpx;

  .no-data__icon {
    width: 80rpx;
    height: 80rpx;
    font-size: 40rpx;
    margin-bottom: 16rpx;
  }

  .no-data__text {
    font-size: 26rpx;
  }

  .no-data__description {
    font-size: 22rpx;
    margin-bottom: 24rpx;
  }
}
</style>
