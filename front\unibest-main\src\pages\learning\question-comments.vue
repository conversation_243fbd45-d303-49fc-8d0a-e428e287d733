<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad, onReachBottom, onPullDownRefresh } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import type { Comment } from '@/types/question'
// @ts-ignore
import { questionApi } from '@/service/question'

// 页面参数
const questionId = ref('')

// 页面状态
const isLoading = ref(true)
const isLoadingMore = ref(false)
const isSubmittingComment = ref(false)
const isSubmittingReply = ref(false)
const commentContent = ref('')
const replyContent = ref('')
const showReplyBox = ref<number | null>(null)
const showCommentModal = ref(false)
const replyToUser = ref('')
const replyToId = ref<number | null>(null)
const expandedReplies = ref<Set<number>>(new Set())

// 评论列表数据
const comments = ref<Comment[]>([])
const pageNo = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const total = ref(0)

/**
 * 加载评论列表
 * @description 从API获取评论数据，支持分页加载
 * @param isRefresh 是否为刷新操作
 */
const loadComments = async (isRefresh = false) => {
  try {
    // 刷新时重置状态
    if (isRefresh) {
      pageNo.value = 1
      hasMore.value = true
      comments.value = []
      isLoading.value = true
    } else if (!hasMore.value) {
      return // 没有更多数据时直接返回
    } else {
      isLoadingMore.value = true
    }

    const response = await questionApi.getComments({
      questionId: questionId.value,
      page: pageNo.value,
      pageSize: pageSize.value,
      orderBy: 'createTime',
      orderDirection: 'desc',
    })
    console.log(response)

    if (response.code === 200) {
      const { list, total: totalCount, hasMore: hasMoreData } = response.data

      if (isRefresh) {
        comments.value = list || []
        // 重置展开状态
        expandedReplies.value = new Set()
      } else {
        comments.value = [...comments.value, ...(list || [])]
      }

      // 只展开回复数量小于等于4条的评论，超过4条的需要用户手动展开
      if (list && list.length > 0) {
        list.forEach((comment: Comment) => {
          if (comment.replies && comment.replies.length > 0 && comment.replies.length <= 4) {
            const commentId = typeof comment.id === 'string' ? parseInt(comment.id) : comment.id
            expandedReplies.value.add(commentId)
          }
        })
      }

      total.value = totalCount || 0
      hasMore.value =
        hasMoreData !== undefined ? hasMoreData : list && list.length >= pageSize.value

      // 只有成功加载数据时才增加页码
      if (list && list.length > 0) {
        pageNo.value++
      }
    } else {
      throw new Error(response.message || '获取评论失败')
    }
  } catch (error) {
    console.error('加载评论失败:', error)

    // 只在第一次加载时显示错误提示
    if (isRefresh || pageNo.value === 1) {
      uni.showToast({
        title: error.message || '加载失败，请重试',
        icon: 'none',
        duration: 2000,
      })
    }
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

/**
 * 发表评论
 * @description 提交新的评论内容
 */
const submitComment = async () => {
  if (!commentContent.value.trim()) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none',
    })
    return
  }

  if (commentContent.value.length > 500) {
    uni.showToast({
      title: '评论内容不能超过500字',
      icon: 'none',
    })
    return
  }

  try {
    isSubmittingComment.value = true

    const response = await questionApi.createComment({
      questionId: questionId.value,
      content: commentContent.value.trim(),
    })

    if (response.code === 200) {
      // 添加新评论到列表头部
      comments.value.unshift(response.data)
      total.value++

      // 清空输入并关闭弹窗
      commentContent.value = ''
      showCommentModal.value = false

      uni.showToast({
        title: '评论成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.message || '评论失败')
    }
  } catch (error) {
    console.error('提交评论失败:', error)
    uni.showToast({
      title: error.message || '评论失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isSubmittingComment.value = false
  }
}

/**
 * 打开评论弹窗
 * @description 显示评论输入弹窗
 */
const openCommentModal = () => {
  showCommentModal.value = true
}

/**
 * 关闭评论弹窗
 * @description 关闭评论输入弹窗并清空内容
 */
const closeCommentModal = () => {
  showCommentModal.value = false
  commentContent.value = ''
}

/**
 * 关闭回复框
 * @description 关闭回复输入框并清空状态
 */
const closeReplyBox = () => {
  showReplyBox.value = null
  replyContent.value = ''
  replyToUser.value = ''
  replyToId.value = null
}

/**
 * 切换点赞状态
 * @description 为评论点赞或取消点赞
 * @param comment 评论对象
 */
const toggleLike = async (comment: Comment) => {
  try {
    const response = await questionApi.likeComment(comment.id.toString())

    if (response.code === 200) {
      // 更新本地数据
      comment.likes = response.data.likeCount
      // 更新点赞状态
      comment.liked = response.data.isLiked

      uni.showToast({
        title: response.data.message || (response.data.isLiked ? '点赞成功' : '取消点赞'),
        icon: 'success',
        duration: 1000,
      })
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error: any) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: error.message || '操作失败，请重试',
      icon: 'none',
      duration: 1500,
    })
  }
}

/**
 * 显示回复框
 * @description 切换指定评论的回复输入框显示状态
 * @param comment 评论对象
 * @param replyTo 回复的用户名（可选，用于嵌套回复）
 */
const showReply = (comment: Comment, replyTo?: string) => {
  if (!comment) {
    console.error('评论对象为空')
    uni.showToast({
      title: '评论数据错误',
      icon: 'none',
    })
    return
  }

  const id = getCommentId(comment)

  if (id === 0) {
    console.error('无效的评论ID')
    uni.showToast({
      title: '评论ID无效',
      icon: 'none',
    })
    return
  }

  if (showReplyBox.value === id) {
    // 如果当前已经显示该评论的回复框，则关闭
    showReplyBox.value = null
    replyContent.value = ''
    replyToUser.value = ''
    replyToId.value = null
  } else {
    // 显示回复框
    showReplyBox.value = id
    replyContent.value = ''
    replyToUser.value = replyTo || comment.author
    replyToId.value = replyTo ? id : null

    // 给用户反馈
    uni.showToast({
      title: `回复 @${replyToUser.value}`,
      icon: 'none',
      duration: 1000,
    })
  }
}

/**
 * 提交回复
 * @description 对指定评论进行回复
 * @param comment 要回复的评论
 */
const submitReply = async (comment: Comment) => {
  if (!replyContent.value.trim()) {
    uni.showToast({
      title: '请输入回复内容',
      icon: 'none',
    })
    return
  }

  if (replyContent.value.length > 200) {
    uni.showToast({
      title: '回复内容不能超过200字',
      icon: 'none',
    })
    return
  }

  try {
    isSubmittingReply.value = true

    const response = await questionApi.createComment({
      questionId: questionId.value,
      content: replyContent.value.trim(),
      parentId: comment.id.toString(),
      replyTo: replyToUser.value !== comment.author ? replyToUser.value : undefined,
    })

    if (response.code === 200) {
      // 添加回复到对应评论的回复列表
      if (!comment.replies) {
        comment.replies = []
      }

      // 设置回复的关联信息
      if (replyToUser.value && replyToUser.value !== comment.author) {
        response.data.replyTo = replyToUser.value
      }

      comment.replies.push(response.data)

      // 自动展开回复列表
      const commentId = getCommentId(comment)
      if (commentId !== 0) {
        expandedReplies.value.add(commentId)
      }

      // 清空回复输入
      closeReplyBox()

      uni.showToast({
        title: '回复成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.message || '回复失败')
    }
  } catch (error) {
    console.error('提交回复失败:', error)
    uni.showToast({
      title: error.message || '回复失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isSubmittingReply.value = false
  }
}

/**
 * 回复点赞
 * @description 为回复点赞或取消点赞
 * @param reply 回复对象
 * @param comment 所属评论对象
 */
const toggleReplyLike = async (reply: Comment, comment: Comment) => {
  try {
    const response = await questionApi.likeComment(reply.id.toString())

    if (response.code === 200) {
      // 更新本地数据
      reply.likes = response.data.likeCount
      reply.liked = response.data.isLiked

      uni.showToast({
        title: response.data.message || (response.data.isLiked ? '点赞成功' : '取消点赞'),
        icon: 'success',
        duration: 1000,
      })
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error: any) {
    console.error('回复点赞操作失败:', error)
    uni.showToast({
      title: error.message || '操作失败，请重试',
      icon: 'none',
      duration: 1500,
    })
  }
}

/**
 * 切换回复列表展开状态
 * @description 展开或收起评论的回复列表
 * @param commentId 评论ID
 */
const toggleRepliesExpanded = (commentId: number) => {
  if (!commentId) {
    console.error('无效的评论ID')
    return
  }

  const id = typeof commentId === 'string' ? parseInt(commentId) : commentId
  const newSet = new Set(expandedReplies.value)

  if (newSet.has(id)) {
    newSet.delete(id)
  } else {
    newSet.add(id)
  }

  expandedReplies.value = newSet
}

/**
 * 检查回复列表是否已展开
 * @description 判断指定评论的回复列表是否展开
 * @param commentId 评论ID
 */
const isRepliesExpanded = (commentId: number): boolean => {
  const id = typeof commentId === 'string' ? parseInt(commentId) : commentId
  return expandedReplies.value.has(id)
}

/**
 * 格式化回复显示文本
 * @description 根据回复内容生成显示文本
 * @param reply 回复对象
 */
const formatReplyText = (reply: Comment): string => {
  if (reply.replyTo && reply.replyTo !== reply.author) {
    return `回复 @${reply.replyTo} ${reply.content}`
  }
  return reply.content
}

/**
 * 获取评论ID
 * @description 确保评论ID为数字类型
 * @param comment 评论对象
 */
const getCommentId = (comment: Comment): number => {
  if (!comment || !comment.id) {
    console.error('评论对象或ID无效:', comment)
    return 0
  }
  return typeof comment.id === 'string' ? parseInt(comment.id) : comment.id
}

/**
 * 页面触底加载更多
 * @description 触底时自动加载更多评论
 */
const handleReachBottom = () => {
  if (!isLoading.value && !isLoadingMore.value && hasMore.value) {
    loadComments()
  }
}

/**
 * 下拉刷新
 * @description 下拉刷新评论列表
 */
const handlePullDownRefresh = async () => {
  await loadComments(true)
  uni.stopPullDownRefresh()
}

/**
 * 计算输入框显示行数
 * @description 根据内容动态调整输入框高度
 */
const inputLineCount = computed(() => {
  const lines = commentContent.value.split('\n').length
  return Math.min(Math.max(lines, 3), 6)
})

/**
 * 计算回复输入框显示行数
 * @description 根据回复内容动态调整输入框高度
 */
const replyInputLineCount = computed(() => {
  const lines = replyContent.value.split('\n').length
  return Math.min(Math.max(lines, 2), 4)
})

/**
 * 获取评论总数显示文本
 * @description 格式化评论总数的显示
 */
const commentCountText = computed(() => {
  if (total.value === 0) {
    return '暂无评论'
  }
  return `共 ${total.value} 条评论`
})

/**
 * 页面加载初始化
 * @description 获取页面参数并初始化数据
 * @param options 页面参数
 */
onLoad((options: any) => {
  console.log('开发环境 - 评论页面加载参数:', options)

  if (options && options.id) {
    questionId.value = options.id

    // 参数验证
    if (!questionId.value) {
      uni.showToast({
        title: '题目ID不能为空',
        icon: 'none',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return
    }

    // 加载评论数据
    loadComments(true)
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }

  // 添加测试数据用于开发调试
  setTimeout(() => {
    if (comments.value.length === 0) {
      console.log('添加测试评论数据')
      comments.value = [
        {
          id: 1,
          avatar: '😀',
          author: '张三',
          time: '2分钟前',
          content: '这道题考查的是基础算法，需要注意边界条件的处理。我觉得可以用动态规划来解决。',
          likes: 5,
          isLiked: false,
          replies: [
            {
              id: 11,
              avatar: '😊',
              author: '李四',
              time: '1分钟前',
              content: '感谢分享！你的思路很清晰。',
              likes: 2,
              isLiked: false,
            },
            {
              id: 12,
              avatar: '🤔',
              author: '王五',
              time: '1分钟前',
              content: '我也觉得动态规划是个好方法',
              likes: 1,
              isLiked: false,
            },
            {
              id: 13,
              avatar: '😎',
              author: '赵六',
              time: '30秒前',
              content: '能详细说说状态转移方程吗？',
              likes: 0,
              isLiked: false,
            },
            {
              id: 14,
              avatar: '😀',
              author: '张三',
              time: '20秒前',
              content: '回复 @赵六 当然可以，dp[i] = max(dp[i-1], dp[i-2] + nums[i])',
              likes: 3,
              isLiked: false,
              replyTo: '赵六',
            },
            {
              id: 15,
              avatar: '👍',
              author: '小明',
              time: '10秒前',
              content: '学到了，谢谢大佬们！',
              likes: 1,
              isLiked: false,
            },
            {
              id: 16,
              avatar: '🔥',
              author: '小红',
              time: '5秒前',
              content: '回复 @张三 这个解释很棒！',
              likes: 0,
              isLiked: false,
              replyTo: '张三',
            },
          ],
        },
        {
          id: 2,
          avatar: '🤔',
          author: '王五',
          time: '5分钟前',
          content: '有没有更优化的解法？时间复杂度能再降低吗？',
          likes: 3,
          isLiked: true,
          replies: [
            {
              id: 21,
              avatar: '💡',
              author: '算法大师',
              time: '3分钟前',
              content: '可以试试贪心算法',
              likes: 5,
              isLiked: false,
            },
            {
              id: 22,
              avatar: '📚',
              author: '学习者',
              time: '2分钟前',
              content: '贪心算法在这里适用吗？',
              likes: 1,
              isLiked: false,
            },
          ],
        },
        {
          id: 3,
          avatar: '💪',
          author: '努力学习',
          time: '10分钟前',
          content: '刚开始学算法，这题对新手来说有点难度',
          likes: 8,
          isLiked: false,
          replies: [],
        },
      ]
      total.value = 3

      // 根据回复数量设置初始展开状态
      comments.value.forEach((comment: Comment) => {
        if (comment.replies && comment.replies.length > 0 && comment.replies.length <= 4) {
          const commentId = getCommentId(comment)
          expandedReplies.value.add(commentId)
        }
      })
    }
  }, 1000)
})

/**
 * 页面触底事件
 * @description 页面滚动到底部时触发
 */
onReachBottom(() => {
  handleReachBottom()
})

/**
 * 下拉刷新事件
 * @description 页面下拉刷新时触发
 */
onPullDownRefresh(() => {
  handlePullDownRefresh()
})
</script>

<template>
  <view class="comments-page">
    <HeadBar title="题目讨论" :show-back="true" />

    <!-- 评论列表 -->
    <scroll-view
      scroll-y
      class="comments-scroll"
      :style="{ height: 'calc(100vh - 88rpx)' }"
      @scrolltolower="handleReachBottom"
    >
      <!-- 加载状态 -->
      <view v-if="isLoading && comments.length === 0" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 评论列表 -->
      <view v-else class="comments-list">
        <view v-for="comment in comments" :key="comment.id" class="comment-card">
          <!-- 主评论 -->
          <view class="comment-main">
            <view class="comment-header">
              <view class="user-avatar">
                <text class="avatar-emoji">{{ comment.avatar }}</text>
              </view>
              <view class="user-info">
                <text class="user-name">{{ comment.author || '匿名用户' }}</text>
                <text class="comment-time">{{ comment.time }}</text>
              </view>
            </view>

            <text class="comment-content">{{ comment.content }}</text>

            <view class="comment-actions">
              <view class="action-item" @click.stop="toggleLike(comment)">
                <view
                  class="action-icon"
                  :class="comment.liked ? 'i-mdi-thumb-up' : 'i-mdi-thumb-up-outline'"
                  :style="{ color: comment.liked ? '#00c9a7' : '#64748b' }"
                ></view>
                <text class="action-text" :style="{ color: comment.liked ? '#00c9a7' : '#64748b' }">
                  {{ comment.likes }}
                </text>
              </view>
              <view class="action-item" @click.stop="showReply(comment)">
                <view class="action-icon i-mdi-reply"></view>
                <text class="action-text">回复</text>
              </view>
            </view>

            <!-- 回复输入框 -->
            <view v-if="showReplyBox === getCommentId(comment)" class="reply-input-box">
              <view class="reply-input-header">
                <text class="reply-tip">回复 @{{ replyToUser }}</text>
                <view class="close-reply" @click.stop="closeReplyBox">
                  <view class="close-icon i-mdi-close"></view>
                </view>
              </view>
              <textarea
                v-model="replyContent"
                class="reply-textarea"
                :rows="replyInputLineCount"
                placeholder="说点什么..."
                :maxlength="200"
                :focus="showReplyBox === getCommentId(comment)"
              />
              <view class="reply-actions">
                <text class="reply-char-count">{{ replyContent.length }}/200</text>
                <view class="reply-buttons">
                  <button class="cancel-btn" @click.stop="closeReplyBox">取消</button>
                  <button
                    class="reply-btn"
                    @click.stop="submitReply(comment)"
                    :disabled="!replyContent.trim() || isSubmittingReply"
                  >
                    {{ isSubmittingReply ? '发布中...' : '回复' }}
                  </button>
                </view>
              </view>
            </view>

            <!-- 回复列表 -->
            <view v-if="comment.replies && comment.replies.length > 0" class="replies-container">
              <!-- 显示前4条回复或全部回复 -->
              <view class="replies-list">
                <view
                  v-for="reply in comment.replies.length > 4 &&
                  !isRepliesExpanded(getCommentId(comment))
                    ? comment.replies.slice(0, 4)
                    : comment.replies"
                  :key="reply.id"
                  class="reply-item"
                >
                  <view class="reply-content">
                    <text class="reply-author">{{ reply.author }}</text>
                    <text class="reply-separator">：</text>
                    <text class="reply-text">{{ formatReplyText(reply) }}</text>
                  </view>
                  <view class="reply-meta">
                    <text class="reply-time">{{ reply.time }}</text>
                    <view class="reply-actions">
                      <view class="reply-action-item" @click.stop="toggleReplyLike(reply, comment)">
                        <view
                          class="reply-action-icon"
                          :class="reply.liked ? 'i-mdi-thumb-up' : 'i-mdi-thumb-up-outline'"
                          :style="{ color: reply.liked ? '#00c9a7' : '#94a3b8' }"
                        ></view>
                        <text class="reply-like-count">{{ reply.likes || 0 }}</text>
                      </view>
                      <view
                        class="reply-action-item"
                        @click.stop="showReply(comment, reply.author)"
                      >
                        <text class="reply-action-text">回复</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 展开/收起控制 - 只有超过4条回复时才显示 -->
              <view
                v-if="comment.replies.length > 4"
                class="replies-toggle"
                @click.stop="toggleRepliesExpanded(getCommentId(comment))"
              >
                <text class="toggle-text">
                  {{
                    isRepliesExpanded(getCommentId(comment))
                      ? '收起回复'
                      : `查看全部${comment.replies.length}条回复`
                  }}
                </text>
                <view
                  class="toggle-icon"
                  :class="
                    isRepliesExpanded(getCommentId(comment))
                      ? 'i-mdi-chevron-up'
                      : 'i-mdi-chevron-down'
                  "
                ></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多状态 -->
        <view v-if="!isLoading && !isLoadingMore && hasMore" class="load-more">
          <text class="load-more-text">上拉加载更多</text>
        </view>

        <!-- 没有更多 -->
        <view v-else-if="!hasMore && comments.length > 0" class="no-more">
          <text class="no-more-text">没有更多评论了</text>
        </view>

        <!-- 空状态 -->
        <view v-else-if="comments.length === 0" class="empty-state">
          <view class="empty-icon i-mdi-comment-off-outline"></view>
          <text class="empty-text">暂无评论，来发表第一条评论吧</text>
        </view>
      </view>
    </scroll-view>

    <!-- 悬浮评论按钮 -->
    <view class="float-comment-btn" @click="openCommentModal">
      <view class="comment-icon i-mdi-comment-plus-outline"></view>
    </view>

    <!-- 评论弹窗 -->
    <view v-if="showCommentModal" class="comment-modal-overlay" @click="closeCommentModal">
      <view class="comment-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">发表评论</text>
          <view class="close-btn" @click="closeCommentModal">
            <view class="close-icon i-mdi-close"></view>
          </view>
        </view>

        <view class="modal-content">
          <textarea
            v-model="commentContent"
            class="comment-textarea"
            :rows="inputLineCount"
            placeholder="分享你的解题思路、经验或疑问..."
            :maxlength="500"
            :adjust-position="true"
            :focus="showCommentModal"
          />
          <view class="input-tools">
            <text class="char-count">{{ commentContent.length }}/500</text>
            <button
              class="submit-btn"
              @click="submitComment"
              :disabled="!commentContent.trim() || isSubmittingComment"
              :loading="isSubmittingComment"
            >
              {{ isSubmittingComment ? '发布中...' : '发表评论' }}
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
/* 页面容器 */
.comments-page {
  min-height: 100vh;
  background: #f8fafc;
}
/* 悬浮评论按钮 */
.float-comment-btn {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.4);
  transition: all 0.3s;

  &:active {
    transform: scale(0.9);
  }

  .comment-icon {
    font-size: 48rpx;
    color: #ffffff;
  }
}

/* 评论弹窗 */
.comment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.comment-modal {
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f1f5f9;

  .modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56rpx;
    height: 56rpx;
    background: #f8fafc;
    border-radius: 50%;
    transition: all 0.3s;

    &:active {
      background: #e2e8f0;
      transform: scale(0.9);
    }

    .close-icon {
      font-size: 28rpx;
      color: #64748b;
    }
  }
}

.modal-content {
  padding: 32rpx;
}

.comment-textarea {
  box-sizing: border-box;
  width: 100%;
  min-height: 160rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #1e293b;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.3s;

  &:focus {
    background: #ffffff;
    border-color: #00c9a7;
  }
}

.input-tools {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
}

.char-count {
  font-size: 24rpx;
  color: #94a3b8;
}

.submit-btn {
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border: none;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:disabled {
    opacity: 0.5;
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
  }
}
/* 评论滚动区 */
.comments-scroll {
  background: #f8fafc;
}
/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 24rpx;
    border: 6rpx solid #e2e8f0;
    border-top: 6rpx solid #00c9a7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 28rpx;
    color: #64748b;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* 评论列表 */
.comments-list {
  padding: 24rpx;
}

.comment-card {
  margin-bottom: 24rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.comment-main {
  padding: 32rpx;
}

.comment-header {
  display: flex;
  gap: 16rpx;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 50%;

  .avatar-emoji {
    font-size: 36rpx;
  }
}

.user-info {
  flex: 1;

  .user-name {
    display: block;
    margin-bottom: 4rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .comment-time {
    font-size: 24rpx;
    color: #94a3b8;
  }
}

.comment-content {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #475569;
  word-break: break-word;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  gap: 32rpx;
}

.action-item {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 12rpx 24rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s;

  &:active {
    background: #e2e8f0;
    transform: scale(0.95);
  }

  .action-icon {
    font-size: 28rpx;
    color: #64748b;
  }

  .action-text {
    font-size: 24rpx;
    color: #64748b;
  }
}
/* 回复输入框 */
.reply-input-box {
  padding: 24rpx;
  margin-top: 20rpx;
  background: #ffffff;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.reply-input-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;

  .reply-tip {
    font-size: 26rpx;
    font-weight: 500;
    color: #00c9a7;
  }

  .close-reply {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    background: #f1f5f9;
    border-radius: 50%;
    transition: all 0.3s;

    &:active {
      background: #e2e8f0;
      transform: scale(0.9);
    }

    .close-icon {
      font-size: 24rpx;
      color: #64748b;
    }
  }
}

.reply-textarea {
  box-sizing: border-box;
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #1e293b;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:focus {
    background: #ffffff;
    border-color: #00c9a7;
  }
}

.reply-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reply-char-count {
  font-size: 24rpx;
  color: #94a3b8;
}

.reply-buttons {
  display: flex;
  gap: 16rpx;
}

.cancel-btn,
.reply-btn {
  padding: 12rpx 28rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  border-radius: 20rpx;
  transition: all 0.3s;
}

.cancel-btn {
  color: #64748b;
  background: #f1f5f9;

  &:active {
    background: #e2e8f0;
  }
}

.reply-btn {
  color: #ffffff;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);

  &:disabled {
    opacity: 0.5;
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
  }
}
/* 回复容器 */
.replies-container {
  margin-top: 20rpx;
  padding: 16rpx 0;
  background: #f8fafc;
  border-radius: 12rpx;
}

/* 回复列表 */
.replies-list {
  padding: 0 20rpx;
}

.reply-item {
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e2e8f0;

  &:last-child {
    border-bottom: none;
  }
}

.reply-content {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  line-height: 1.5;

  .reply-author {
    font-weight: 600;
    color: #1e293b;
  }

  .reply-separator {
    color: #64748b;
  }

  .reply-text {
    color: #475569;
    word-break: break-word;
  }
}

.reply-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .reply-time {
    font-size: 22rpx;
    color: #94a3b8;
  }
}

.reply-actions {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.reply-action-item {
  display: flex;
  gap: 4rpx;
  align-items: center;
  padding: 4rpx 8rpx;
  background: #ffffff;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:active {
    background: #e2e8f0;
    transform: scale(0.95);
  }

  .reply-action-icon {
    font-size: 20rpx;
    color: #94a3b8;
  }

  .reply-like-count {
    font-size: 20rpx;
    color: #94a3b8;
  }

  .reply-action-text {
    font-size: 20rpx;
    color: #64748b;
  }
}

/* 展开/收起控制 */
.replies-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  margin-top: 8rpx;
  transition: all 0.3s;

  &:active {
    background: #e2e8f0;
  }

  .toggle-text {
    font-size: 24rpx;
    color: #00c9a7;
  }

  .toggle-icon {
    font-size: 24rpx;
    color: #00c9a7;
    transition: all 0.3s;
  }
}
/* 加载更多 */
.load-more,
.no-more {
  padding: 32rpx;
  text-align: center;

  .load-more-text,
  .no-more-text {
    font-size: 26rpx;
    color: #94a3b8;
  }
}
/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 24rpx;
    font-size: 120rpx;
    color: #cbd5e1;
  }

  .empty-text {
    font-size: 28rpx;
    color: #94a3b8;
  }
}
/* 平台适配样式 */
// #ifdef H5
.comments-scroll::-webkit-scrollbar {
  width: 6rpx;
}

.comments-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3rpx;
}

.comments-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 201, 167, 0.6);
  border-radius: 3rpx;
}

.comments-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 201, 167, 0.8);
}

/* H5端悬浮按钮位置调整 */
.float-comment-btn {
  bottom: 80rpx;
}
// #endif

// #ifdef APP-PLUS
/* APP端悬浮按钮位置调整 */
.float-comment-btn {
  bottom: 120rpx;
}

/* APP端弹窗优化 */
.comment-modal {
  border-radius: 24rpx 24rpx 0 0;
}
// #endif

/* 小屏设备适配 */
@media (max-height: 600px) {
  .comment-modal {
    max-height: 90vh;
  }

  .modal-content {
    padding: 24rpx;
  }

  .comment-textarea {
    min-height: 120rpx;
  }
}
</style>
