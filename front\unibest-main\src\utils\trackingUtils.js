/**
 * 用户行为埋点工具类
 * 用于收集和发送用户行为数据到后端成就系统
 * 
 * <AUTHOR>
 */

import request from '@/utils/request'
import { getToken } from '@/utils/auth'

class TrackingUtils {
  constructor() {
    this.sessionId = this.generateSessionId()
    this.eventQueue = []
    this.batchSize = 10
    this.flushInterval = 5000 // 5秒
    this.maxRetries = 3
    this.isEnabled = true
    
    // 启动定时批量发送
    this.startBatchFlush()
  
    // 页面卸载时发送剩余数据
    this.setupPageUnloadHandler()
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 获取用户ID
   */
  getUserId() {
    // 这里需要根据实际的用户状态管理来获取用户ID
    // 可能从 Vuex store、localStorage 或其他地方获取
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    return userInfo.userId || null
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }

  /**
   * 获取页面信息
   */
  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      referrer: document.referrer
    }
  }

  /**
   * 记录单个事件
   * @param {string} eventType - 事件类型
   * @param {object} eventData - 事件数据
   * @param {boolean} immediate - 是否立即发送
   */
  track(eventType, eventData = {}, immediate = false) {
    if (!this.isEnabled) {
      return
    }

    const userId = this.getUserId()
    if (!userId) {
      console.warn('TrackingUtils: 用户未登录，跳过埋点')
      return
    }

    const trackEvent = {
      eventType,
      userId,
      eventData,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      ...this.getPageInfo(),
      deviceInfo: JSON.stringify(this.getDeviceInfo())
    }

    if (immediate) {
      this.sendEvent(trackEvent)
    } else {
      this.eventQueue.push(trackEvent)
      
      // 如果队列满了，立即发送
      if (this.eventQueue.length >= this.batchSize) {
        this.flushEvents()
      }
    }
  }

  /**
   * 批量记录事件
   * @param {Array} events - 事件数组
   */
  trackBatch(events) {
    if (!this.isEnabled || !Array.isArray(events)) {
      return
    }

    const userId = this.getUserId()
    if (!userId) {
      console.warn('TrackingUtils: 用户未登录，跳过批量埋点')
      return
    }

    const trackEvents = events.map(event => ({
      eventType: event.eventType,
      userId,
      eventData: event.eventData || {},
      timestamp: event.timestamp || Date.now(),
      sessionId: this.sessionId,
      ...this.getPageInfo(),
      deviceInfo: JSON.stringify(this.getDeviceInfo())
    }))

    this.sendBatchEvents(trackEvents)
  }

  /**
   * 发送单个事件
   * @param {object} event - 事件对象
   * @param {number} retryCount - 重试次数
   */
  async sendEvent(event, retryCount = 0) {
    try {
      await request({
        url: '/app/track/event',
        method: 'post',
        data: event,
        headers: {
          'Authorization': 'Bearer ' + getToken()
        }
      })
      
      console.debug('TrackingUtils: 事件发送成功', event.eventType)
    } catch (error) {
      console.error('TrackingUtils: 事件发送失败', error)
      
      // 重试机制
      if (retryCount < this.maxRetries) {
        setTimeout(() => {
          this.sendEvent(event, retryCount + 1)
        }, Math.pow(2, retryCount) * 1000) // 指数退避
      }
    }
  }

  /**
   * 批量发送事件
   * @param {Array} events - 事件数组
   * @param {number} retryCount - 重试次数
   */
  async sendBatchEvents(events, retryCount = 0) {
    if (!events || events.length === 0) {
      return
    }

    try {
      await request({
        url: '/app/track/events',
        method: 'post',
        data: events,
        headers: {
          'Authorization': 'Bearer ' + getToken()
        }
      })
      
      console.debug('TrackingUtils: 批量事件发送成功', events.length)
    } catch (error) {
      console.error('TrackingUtils: 批量事件发送失败', error)
      
      // 重试机制
      if (retryCount < this.maxRetries) {
        setTimeout(() => {
          this.sendBatchEvents(events, retryCount + 1)
        }, Math.pow(2, retryCount) * 1000) // 指数退避
      }
    }
  }

  /**
   * 刷新事件队列
   */
  flushEvents() {
    if (this.eventQueue.length === 0) {
      return
    }

    const events = [...this.eventQueue]
    this.eventQueue = []
    this.sendBatchEvents(events)
  }

  /**
   * 启动定时批量发送
   */
  startBatchFlush() {
    setInterval(() => {
      this.flushEvents()
    }, this.flushInterval)
  }

  /**
   * 设置页面卸载处理器
   */
  setupPageUnloadHandler() {
    window.addEventListener('beforeunload', () => {
      // 页面卸载时立即发送剩余数据
      if (this.eventQueue.length > 0) {
        // 使用 sendBeacon API 确保数据能够发送
        const events = [...this.eventQueue]
        const data = JSON.stringify(events)
        
        if (navigator.sendBeacon) {
          navigator.sendBeacon('/app/track/events', data)
        } else {
          // 降级到同步请求
          this.flushEvents()
        }
      }
    })
  }

  /**
   * 启用/禁用埋点
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
  }

  /**
   * 设置批量大小
   * @param {number} size - 批量大小
   */
  setBatchSize(size) {
    this.batchSize = size
  }

  /**
   * 设置刷新间隔
   * @param {number} interval - 间隔时间（毫秒）
   */
  setFlushInterval(interval) {
    this.flushInterval = interval
  }

  // ==================== 预定义的埋点方法 ====================

  /**
   * 用户登录埋点
   */
  trackLogin(loginMethod = 'password') {
    this.track('LOGIN', {
      loginMethod,
      loginTime: new Date().toISOString()
    }, true)
  }

  /**
   * 视频观看埋点
   * @param {string} videoId - 视频ID
   * @param {string} videoTitle - 视频标题
   * @param {number} duration - 观看时长（秒）
   */
  trackVideoWatch(videoId, videoTitle, duration = 0) {
    this.track('VIDEO_WATCH', {
      videoId,
      videoTitle,
      watchDuration: duration,
      watchTime: new Date().toISOString()
    })
  }

  /**
   * 评论埋点
   * @param {string} targetType - 目标类型（video, article等）
   * @param {string} targetId - 目标ID
   * @param {string} commentContent - 评论内容
   */
  trackComment(targetType, targetId, commentContent) {
    this.track('COMMENT', {
      targetType,
      targetId,
      commentLength: commentContent.length,
      commentTime: new Date().toISOString()
    })
  }

  /**
   * 点赞埋点
   * @param {string} targetType - 目标类型
   * @param {string} targetId - 目标ID
   */
  trackLike(targetType, targetId) {
    this.track('LIKE', {
      targetType,
      targetId,
      likeTime: new Date().toISOString()
    })
  }

  /**
   * 学习时长埋点
   * @param {number} minutes - 学习时长（分钟）
   * @param {string} subject - 学习科目
   */
  trackStudyTime(minutes, subject = '') {
    this.track('STUDY_TIME', {
      studyMinutes: minutes,
      subject,
      studyDate: new Date().toISOString().split('T')[0]
    })
  }

  /**
   * 分享埋点
   * @param {string} contentType - 内容类型
   * @param {string} contentId - 内容ID
   * @param {string} platform - 分享平台
   */
  trackShare(contentType, contentId, platform) {
    this.track('SHARE', {
      contentType,
      contentId,
      platform,
      shareTime: new Date().toISOString()
    })
  }

  /**
   * 页面访问埋点
   * @param {string} pageName - 页面名称
   * @param {object} pageParams - 页面参数
   */
  trackPageView(pageName, pageParams = {}) {
    this.track('PAGE_VIEW', {
      pageName,
      pageParams,
      visitTime: new Date().toISOString()
    })
  }
}

// 创建全局实例
const trackingUtils = new TrackingUtils()

export default trackingUtils
