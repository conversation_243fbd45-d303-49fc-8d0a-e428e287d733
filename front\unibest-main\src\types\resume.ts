/**
 * 用户简历相关类型定义
 */

/**
 * 简历基本信息接口
 */
export interface ResumeInfo {
  /** 简历ID */
  id: string
  /** 简历名称 */
  name: string
  /** 原始文件名 */
  originalName: string
  /** 文件大小 */
  size: string
  /** 文件URL */
  url: string
  /** 创建日期 */
  date: string
  /** 是否为默认简历 */
  isDefault: boolean
  /** 状态 */
  status?: string
}

/**
 * 简历详情接口
 */
export interface ResumeDetail {
  /** 简历主键 */
  resumeId: number
  /** 用户ID */
  userId: number
  /** 简历名称 */
  resumeName: string
  /** 原始文件名 */
  originalName: string
  /** 文件路径 */
  filePath: string
  /** 文件URL地址 */
  fileUrl: string
  /** 文件大小(字节) */
  fileSize: number
  /** 文件大小(格式化后) */
  fileSizeStr: string
  /** 文件后缀名 */
  fileSuffix: string
  /** 是否默认简历(0否 1是) */
  isDefault: number
  /** 状态(0正常 1停用) */
  status: string
  /** 对象存储ID */
  ossId: number
  /** 备注 */
  remark?: string
  /** 创建时间 */
  createTime: string
  /** 创建人 */
  createBy: number
  /** 创建人名称 */
  createByName?: string
  /** 更新时间 */
  updateTime: string
}

/**
 * 简历上传结果接口
 */
export interface ResumeUploadResult {
  /** 简历ID */
  resumeId: number
  /** 简历名称 */
  resumeName: string
  /** 文件URL地址 */
  fileUrl: string
  /** 文件大小(格式化后) */
  fileSizeStr: string
  /** 是否默认简历 */
  isDefault: number
}

/**
 * 简历查询参数接口
 */
export interface ResumeQueryParams {
  /** 用户ID */
  userId?: number
  /** 简历名称 */
  resumeName?: string
  /** 状态 */
  status?: string
  /** 是否默认简历 */
  isDefault?: number
  /** 页码 */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
}

/**
 * 简历操作类型枚举
 */
export enum ResumeActionType {
  /** 设为默认 */
  SET_DEFAULT = 'setDefault',
  /** 重命名 */
  RENAME = 'rename',
  /** 删除 */
  DELETE = 'delete',
  /** 下载 */
  DOWNLOAD = 'download',
  /** 预览 */
  PREVIEW = 'preview',
}

/**
 * 简历预览内容接口 - 结构化简历数据
 */
export interface ResumePreviewContent {
  /** 简历基本信息 */
  basicInfo: ResumeBasicInfo
  /** 个人信息 */
  personalInfo: PersonalInfo
  /** 教育经历 */
  educationList: EducationExperience[]
  /** 工作经验 */
  workExperienceList: WorkExperience[]
  /** 技能特长 */
  skillList: Skill[]
  /** 项目经历 */
  projectList?: ProjectExperience[]
  /** 获奖情况 */
  awardList?: Award[]
  /** 证书资质 */
  certificateList?: Certificate[]
  /** 语言能力 */
  languageList?: Language[]
  /** 兴趣爱好 */
  hobbies?: string[]
  /** 自我评价 */
  selfEvaluation?: string
}

/**
 * 简历基本信息
 */
export interface ResumeBasicInfo {
  /** 简历ID */
  resumeId: number
  /** 简历名称 */
  resumeName: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 是否默认简历 */
  isDefault: boolean
}

/**
 * 个人信息
 */
export interface PersonalInfo {
  /** 姓名 */
  name: string
  /** 性别 */
  gender?: string
  /** 年龄 */
  age?: number
  /** 出生日期 */
  birthDate?: string
  /** 手机号码 */
  phone?: string
  /** 邮箱地址 */
  email?: string
  /** 微信号 */
  wechat?: string
  /** QQ号 */
  qq?: string
  /** 现居地址 */
  address?: string
  /** 头像URL */
  avatar?: string
  /** 个人简介 */
  summary?: string
  /** 求职意向 */
  jobIntention?: JobIntention
}

/**
 * 求职意向
 */
export interface JobIntention {
  /** 期望职位 */
  position?: string
  /** 期望行业 */
  industry?: string
  /** 期望城市 */
  city?: string
  /** 期望薪资 */
  salary?: string
  /** 工作性质 */
  jobType?: string
}

/**
 * 教育经历
 */
export interface EducationExperience {
  /** ID */
  id?: number
  /** 学校名称 */
  school: string
  /** 专业 */
  major: string
  /** 学历 */
  degree: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 是否在读 */
  isStudying?: boolean
  /** GPA */
  gpa?: string
  /** 主要课程 */
  mainCourses?: string[]
  /** 描述 */
  description?: string
}

/**
 * 工作经验
 */
export interface WorkExperience {
  /** ID */
  id?: number
  /** 公司名称 */
  company: string
  /** 职位 */
  position: string
  /** 部门 */
  department?: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 是否在职 */
  isWorking?: boolean
  /** 工作内容 */
  description?: string
  /** 工作成就 */
  achievements?: string[]
  /** 薪资 */
  salary?: string
}

/**
 * 技能特长
 */
export interface Skill {
  /** ID */
  id?: number
  /** 技能名称 */
  name: string
  /** 技能类型 */
  type?: string
  /** 熟练度 (1-5) */
  level?: number
  /** 描述 */
  description?: string
}

/**
 * 项目经历
 */
export interface ProjectExperience {
  /** ID */
  id?: number
  /** 项目名称 */
  name: string
  /** 项目角色 */
  role: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 项目描述 */
  description?: string
  /** 技术栈 */
  technologies?: string[]
  /** 项目链接 */
  url?: string
  /** 项目成果 */
  achievements?: string[]
}

/**
 * 获奖情况
 */
export interface Award {
  /** ID */
  id?: number
  /** 奖项名称 */
  name: string
  /** 颁发机构 */
  issuer: string
  /** 获奖时间 */
  time: string
  /** 奖项级别 */
  level?: string
  /** 描述 */
  description?: string
}

/**
 * 证书资质
 */
export interface Certificate {
  /** ID */
  id?: number
  /** 证书名称 */
  name: string
  /** 颁发机构 */
  issuer: string
  /** 获得时间 */
  time: string
  /** 有效期 */
  validUntil?: string
  /** 证书编号 */
  number?: string
  /** 描述 */
  description?: string
}

/**
 * 语言能力
 */
export interface Language {
  /** ID */
  id?: number
  /** 语言名称 */
  name: string
  /** 熟练度 */
  level: string
  /** 证书 */
  certificate?: string
  /** 描述 */
  description?: string
}

/**
 * API响应数据结构
 */
export interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
}

/**
 * 分页响应数据结构
 */
export interface PageResponse<T = any> {
  /** 总记录数 */
  total: number
  /** 当前页数据 */
  rows: T[]
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
}
