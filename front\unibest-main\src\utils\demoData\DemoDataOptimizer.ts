/**
 * @description 演示数据优化器
 * 优化演示数据的加载性能和内存占用
 */

import { demoDataManager } from './DemoDataManager'
import { hybridDemoDataStore } from './DemoDataStore'

/**
 * 演示数据优化器
 * 提供演示数据的预加载、缓存和懒加载功能
 */
export class DemoDataOptimizer {
  private static instance: DemoDataOptimizer
  private preloadedPaths: Set<string> = new Set()
  private lazyLoadQueue: Array<{ path: string; priority: number; params?: Record<string, any> }> =
    []
  private isProcessingQueue: boolean = false
  private memoryUsageStats: {
    preloadCount: number
    cacheHits: number
    cacheMisses: number
    totalRequests: number
    lastCleanup: number
    errorCount: number
  } = {
    preloadCount: 0,
    cacheHits: 0,
    cacheMisses: 0,
    totalRequests: 0,
    lastCleanup: Date.now(),
    errorCount: 0,
  }

  // 缓存访问频率记录
  private cacheAccessFrequency: Map<string, number> = new Map()

  // 缓存大小限制（默认100个项）
  private maxCacheSize: number = 100

  // 自动清理间隔（默认5分钟）
  private cleanupInterval: number = 5 * 60 * 1000

  // 私有构造函数，确保单例模式
  private constructor() {
    // 初始化时设置定期清理缓存的定时器
    setInterval(() => this.autoCleanupCache(), this.cleanupInterval)
    console.log('[DemoDataOptimizer] 初始化完成，已设置自动缓存清理')
  }

  // 获取单例实例
  public static getInstance(): DemoDataOptimizer {
    if (!DemoDataOptimizer.instance) {
      DemoDataOptimizer.instance = new DemoDataOptimizer()
    }
    return DemoDataOptimizer.instance
  }

  /**
   * 预加载演示数据
   * @param paths API路径列表
   * @param params 请求参数
   */
  public preloadDemoData(paths: string[], params?: Record<string, any>): void {
    // 使用批处理方式预加载，避免一次性加载过多数据导致性能问题
    const batchSize = 5
    const batchLoad = (startIndex: number) => {
      if (startIndex >= paths.length) {
        console.log(`[DemoDataOptimizer] 预加载完成，共 ${paths.length} 个API路径`)
        return
      }

      const endIndex = Math.min(startIndex + batchSize, paths.length)
      const batch = paths.slice(startIndex, endIndex)

      batch.forEach((path) => {
        if (!this.preloadedPaths.has(path)) {
          try {
            // 获取演示数据并缓存
            demoDataManager.getDemoData(path, params)

            // 标记为已预加载
            this.preloadedPaths.add(path)
            this.memoryUsageStats.preloadCount++

            // 初始化访问频率
            this.cacheAccessFrequency.set(path, 1)

            // 设置为持久化缓存
            if (hybridDemoDataStore.setPersistKey) {
              hybridDemoDataStore.setPersistKey(path, true)
            }

            console.log(`[DemoDataOptimizer] 预加载演示数据: ${path}`)
          } catch (error) {
            this.memoryUsageStats.errorCount++
            console.error(`[DemoDataOptimizer] 预加载演示数据失败: ${path}`, error)
          }
        }
      })

      // 使用setTimeout进行下一批加载，避免阻塞主线程
      setTimeout(() => batchLoad(endIndex), 10)
    }

    // 开始批量加载
    batchLoad(0)
  }

  /**
   * 懒加载演示数据
   * @param path API路径
   * @param params 请求参数
   * @param priority 优先级（0-10，数字越大优先级越高）
   */
  public lazyLoadDemoData(path: string, params?: Record<string, any>, priority: number = 5): void {
    // 如果已经预加载，则不需要懒加载
    if (this.preloadedPaths.has(path)) {
      // 更新访问频率
      this.updateAccessFrequency(path)
      return
    }

    // 检查队列中是否已存在该路径
    const existingIndex = this.lazyLoadQueue.findIndex((item) => item.path === path)
    if (existingIndex >= 0) {
      // 如果已存在且新的优先级更高，则更新优先级
      if (this.lazyLoadQueue[existingIndex].priority < priority) {
        this.lazyLoadQueue[existingIndex].priority = priority
        this.lazyLoadQueue[existingIndex].params = params

        // 重新排序队列
        this.lazyLoadQueue.sort((a, b) => b.priority - a.priority)
      }
      return
    }

    // 添加到懒加载队列
    this.lazyLoadQueue.push({ path, params, priority })

    // 按优先级排序
    this.lazyLoadQueue.sort((a, b) => b.priority - a.priority)

    // 如果队列没有在处理中，则开始处理
    if (!this.isProcessingQueue) {
      this.processLazyLoadQueue()
    }
  }

  /**
   * 处理懒加载队列
   */
  private processLazyLoadQueue(): void {
    // 标记为处理中
    this.isProcessingQueue = true

    // 如果队列为空，则结束处理
    if (this.lazyLoadQueue.length === 0) {
      this.isProcessingQueue = false
      return
    }

    // 获取队列中的第一个项
    const item = this.lazyLoadQueue.shift()
    if (!item) {
      this.isProcessingQueue = false
      return
    }

    // 在下一个事件循环中加载数据
    setTimeout(() => {
      try {
        // 获取演示数据并缓存
        demoDataManager.getDemoData(item.path, item.params)

        // 标记为已预加载
        this.preloadedPaths.add(item.path)
        this.memoryUsageStats.preloadCount++

        // 初始化访问频率
        this.cacheAccessFrequency.set(item.path, 1)

        console.log(`[DemoDataOptimizer] 懒加载演示数据: ${item.path}`)
      } catch (error) {
        this.memoryUsageStats.errorCount++
        console.error(`[DemoDataOptimizer] 懒加载演示数据失败: ${item.path}`, error)
      }

      // 继续处理队列中的下一个项
      this.processLazyLoadQueue()
    }, 0)
  }

  /**
   * 更新缓存访问频率
   * @param path API路径
   */
  private updateAccessFrequency(path: string): void {
    const frequency = this.cacheAccessFrequency.get(path) || 0
    this.cacheAccessFrequency.set(path, frequency + 1)
  }

  /**
   * 自动清理缓存
   * 当缓存大小超过限制时，清理访问频率最低的项
   */
  private autoCleanupCache(): void {
    // 记录最后清理时间
    this.memoryUsageStats.lastCleanup = Date.now()

    // 如果缓存大小未超过限制，不需要清理
    if (this.preloadedPaths.size <= this.maxCacheSize) {
      return
    }

    console.log(`[DemoDataOptimizer] 开始自动清理缓存，当前缓存大小: ${this.preloadedPaths.size}`)

    // 按访问频率排序
    const sortedEntries = Array.from(this.cacheAccessFrequency.entries()).sort(
      (a, b) => a[1] - b[1],
    )

    // 计算需要删除的数量
    const deleteCount = this.preloadedPaths.size - this.maxCacheSize

    // 删除访问频率最低的项
    for (let i = 0; i < deleteCount && i < sortedEntries.length; i++) {
      const path = sortedEntries[i][0]

      // 不清理持久化缓存
      if (hybridDemoDataStore.isPersistKey && hybridDemoDataStore.isPersistKey(path)) {
        continue
      }

      this.preloadedPaths.delete(path)
      this.cacheAccessFrequency.delete(path)
    }

    console.log(`[DemoDataOptimizer] 缓存清理完成，当前缓存大小: ${this.preloadedPaths.size}`)
  }

  /**
   * 清除预加载的演示数据
   * @param paths API路径列表，如果不指定则清除所有
   */
  public clearPreloadedData(paths?: string[]): void {
    if (paths) {
      // 清除指定路径的预加载数据
      paths.forEach((path) => {
        // 不清理持久化缓存
        if (hybridDemoDataStore.isPersistKey && hybridDemoDataStore.isPersistKey(path)) {
          return
        }

        this.preloadedPaths.delete(path)
        this.cacheAccessFrequency.delete(path)
      })
    } else {
      // 清除所有非持久化的预加载数据
      if (hybridDemoDataStore.isPersistKey) {
        // 保留持久化缓存
        const persistPaths = Array.from(this.preloadedPaths).filter(
          (path) => hybridDemoDataStore.isPersistKey && hybridDemoDataStore.isPersistKey(path),
        )

        this.preloadedPaths.clear()
        this.cacheAccessFrequency.clear()

        // 重新添加持久化缓存
        persistPaths.forEach((path) => {
          this.preloadedPaths.add(path)
          this.cacheAccessFrequency.set(path, 1)
        })

        this.memoryUsageStats.preloadCount = persistPaths.length
      } else {
        // 没有持久化功能，清除所有
        this.preloadedPaths.clear()
        this.cacheAccessFrequency.clear()
        this.memoryUsageStats.preloadCount = 0
      }
    }

    console.log(`[DemoDataOptimizer] 清除预加载数据完成，当前缓存大小: ${this.preloadedPaths.size}`)
  }

  /**
   * 获取内存使用情况
   * @returns 内存使用情况
   */
  public getMemoryUsage(): {
    preloadedCount: number
    queueLength: number
    cacheHits: number
    cacheMisses: number
    hitRate: number
    totalRequests: number
    errorCount: number
    lastCleanup: string
    cacheSize: number
    maxCacheSize: number
  } {
    const hitRate =
      this.memoryUsageStats.totalRequests > 0
        ? (this.memoryUsageStats.cacheHits / this.memoryUsageStats.totalRequests) * 100
        : 0

    return {
      preloadedCount: this.preloadedPaths.size,
      queueLength: this.lazyLoadQueue.length,
      cacheHits: this.memoryUsageStats.cacheHits,
      cacheMisses: this.memoryUsageStats.cacheMisses,
      hitRate: Math.round(hitRate * 100) / 100, // 保留两位小数
      totalRequests: this.memoryUsageStats.totalRequests,
      errorCount: this.memoryUsageStats.errorCount,
      lastCleanup: new Date(this.memoryUsageStats.lastCleanup).toLocaleString(),
      cacheSize: this.preloadedPaths.size,
      maxCacheSize: this.maxCacheSize,
    }
  }

  /**
   * 优化获取演示数据
   * 在demoDataManager.getDemoData的基础上增加缓存命中统计
   * @param path API路径
   * @param params 请求参数
   * @returns 演示数据
   */
  public getOptimizedDemoData<T>(path: string, params?: Record<string, any>): T {
    this.memoryUsageStats.totalRequests++

    // 检查是否已预加载
    if (this.preloadedPaths.has(path)) {
      this.memoryUsageStats.cacheHits++
      // 更新访问频率
      this.updateAccessFrequency(path)
    } else {
      this.memoryUsageStats.cacheMisses++
      // 添加到预加载集合，下次直接命中
      this.preloadedPaths.add(path)
      this.memoryUsageStats.preloadCount++
      // 初始化访问频率
      this.cacheAccessFrequency.set(path, 1)
    }

    try {
      // 使用demoDataManager获取数据
      return demoDataManager.getDemoData<T>(path, params)
    } catch (error) {
      this.memoryUsageStats.errorCount++
      console.error(`[DemoDataOptimizer] 获取优化演示数据失败: ${path}`, error)
      throw error
    }
  }

  /**
   * 预加载常用演示数据
   * 预加载应用中最常用的API路径
   */
  public preloadCommonDemoData(): void {
    // 预加载面试相关的常用API
    this.preloadDemoData([
      'interview-room/getSessionInfo',
      'interview-room/getQuestions',
      'interview-room/getSessionStatus',
      'interview-room/checkDevices',
    ])

    // 预加载反馈相关的常用API
    this.preloadDemoData(['feedback/list', 'feedback/stats'])

    // 预加载AI聊天相关的常用API
    this.preloadDemoData(['chat/agents', 'chat/sessions'])

    console.log('[DemoDataOptimizer] 预加载常用演示数据完成')
  }

  /**
   * 预加载面试房间相关的演示数据
   * 针对面试房间页面预加载所有可能需要的API
   */
  public preloadInterviewRoomData(): void {
    this.preloadDemoData([
      'interview-room/getSessionInfo',
      'interview-room/getQuestion',
      'interview-room/getQuestions',
      'interview-room/submitAnswer',
      'interview-room/endInterview',
      'interview-room/submitFeedback',
      'interview-room/checkDevices',
      'interview-room/getSessionStatus',
      'interview-room/analyzeAudio',
      'interview-room/analyzeVideo',
      'interview-room/analyzeText',
      'interview-room/comprehensiveAnalysis',
    ])

    console.log('[DemoDataOptimizer] 预加载面试房间演示数据完成')
  }

  /**
   * 设置缓存大小限制
   * @param size 最大缓存项数量
   */
  public setMaxCacheSize(size: number): void {
    if (size < 10) {
      console.warn('[DemoDataOptimizer] 缓存大小不能小于10，已设置为10')
      this.maxCacheSize = 10
    } else {
      this.maxCacheSize = size
    }

    // 设置后立即检查是否需要清理
    if (this.preloadedPaths.size > this.maxCacheSize) {
      this.autoCleanupCache()
    }
  }

  /**
   * 设置自动清理间隔
   * @param intervalMs 清理间隔（毫秒）
   */
  public setCleanupInterval(intervalMs: number): void {
    if (intervalMs < 10000) {
      console.warn('[DemoDataOptimizer] 清理间隔不能小于10秒，已设置为10秒')
      this.cleanupInterval = 10000
    } else {
      this.cleanupInterval = intervalMs
    }
  }

  /**
   * 重置统计数据
   */
  public resetStats(): void {
    this.memoryUsageStats = {
      preloadCount: this.preloadedPaths.size,
      cacheHits: 0,
      cacheMisses: 0,
      totalRequests: 0,
      lastCleanup: this.memoryUsageStats.lastCleanup,
      errorCount: 0,
    }

    console.log('[DemoDataOptimizer] 统计数据已重置')
  }
}

// 导出单例实例
export const demoDataOptimizer = DemoDataOptimizer.getInstance()

// 导出默认实例
export default demoDataOptimizer
