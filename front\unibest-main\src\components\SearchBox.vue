<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import type { Ref } from 'vue'

// 定义组件属性
interface Props {
  /** 搜索框占位符文本 */
  placeholder?: string
  /** 搜索框的值，支持v-model */
  modelValue?: string
  /** 是否禁用搜索框 */
  disabled?: boolean
  /** 搜索按钮文本 */
  searchButtonText?: string
  /** 是否显示搜索按钮 */
  showSearchButton?: boolean
  /** 是否显示清除按钮 */
  showClearButton?: boolean
  /** 搜索框最大行数 */
  maxLines?: number
  /** 搜索框最小高度 */
  minHeight?: number
  /** 是否自动聚焦 */
  autoFocus?: boolean
  /** 是否正在搜索（显示加载状态） */
  loading?: boolean
}

// 定义组件事件
interface Emits {
  /** 更新modelValue值 */
  (event: 'update:modelValue', value: string): void
  /** 点击搜索按钮触发 */
  (event: 'search', value: string): void
  /** 点击清除按钮触发 */
  (event: 'clear'): void
  /** 输入内容变化触发 */
  (event: 'input', value: string): void
  /** 获得焦点触发 */
  (event: 'focus', focusEvent: any): void
  /** 失去焦点触发 */
  (event: 'blur', blurEvent: any): void
}

// 设置默认属性值
const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索内容...',
  modelValue: '',
  disabled: false,
  searchButtonText: '搜索',
  showSearchButton: true,
  showClearButton: true,
  maxLines: 3,
  minHeight: 96, // rpx单位 - 增加最小高度以确保更好的居中效果
  autoFocus: false,
  loading: false,
})

// 定义事件
const emit = defineEmits<Emits>()

// 响应式数据
const inputValue: Ref<string> = ref(props.modelValue)
const isFocused: Ref<boolean> = ref(false)
const textareaRef: Ref<any> = ref(null)

// 计算属性
const computedValue = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    inputValue.value = value
    emit('update:modelValue', value)
  },
})

// 计算搜索框样式
const searchBoxStyle = computed(() => {
  const baseHeight = props.minHeight
  return {
    minHeight: `${baseHeight}rpx`,
    borderColor: isFocused.value ? '#00c9a7' : '#e2e8f0',
    boxShadow: isFocused.value
      ? '0 4rpx 12rpx rgba(0, 201, 167, 0.15)'
      : '0 4rpx 12rpx rgba(0, 0, 0, 0.05)',
  }
})

// 计算输入框样式
const inputStyle = computed(() => {
  const lineHeight = 44 // rpx - 增加行高以更好地居中显示
  const maxHeight = lineHeight * props.maxLines
  return {
    maxHeight: `${maxHeight}rpx`,
    minHeight: `${lineHeight}rpx`,
    lineHeight: `${lineHeight}rpx`,
  }
})

/**
 * @description 处理输入内容变化
 * @param event 输入事件对象
 */
const handleInput = (event: any): void => {
  const value = event.detail.value || ''
  computedValue.value = value
  emit('input', value)
}

/**
 * @description 处理搜索按钮点击
 */
const handleSearch = (): void => {
  if (props.disabled || props.loading) {
    return
  }

  const trimmedValue = computedValue.value.trim()
  if (!trimmedValue) {
    uni.showToast({
      title: '请输入搜索内容',
      icon: 'none',
      duration: 1500,
    })
    return
  }

  emit('search', trimmedValue)
}

/**
 * @description 处理清除按钮点击
 */
const handleClear = (): void => {
  if (props.disabled) {
    return
  }

  computedValue.value = ''
  emit('clear')
}

/**
 * @description 处理输入框获得焦点
 * @param focusEvent 焦点事件对象
 */
const handleFocus = (focusEvent: any): void => {
  isFocused.value = true
  emit('focus', focusEvent)
}

/**
 * @description 处理输入框失去焦点
 * @param blurEvent 焦点事件对象
 */
const handleBlur = (blurEvent: any): void => {
  isFocused.value = false
  emit('blur', blurEvent)
}

/**
 * @description 手动聚焦到输入框
 */
const focus = (): void => {
  nextTick(() => {
    console.log(textareaRef.value)
    if (textareaRef.value) {
      textareaRef.value.focus()
    }
  })
}

/**
 * @description 手动使输入框失去焦点
 */
const blur = (): void => {
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.blur()
    }
  })
}

// 计算搜索框类
const getSearchBoxClass = computed(() => {
  return {
    'search-box--focused': isFocused.value,
    'search-box--disabled': props.disabled,
  }
})

// 暴露组件方法
defineExpose({
  focus,
  blur,
})
</script>

<template>
  <view class="search-box-container">
    <view class="search-box" :class="getSearchBoxClass" :style="searchBoxStyle">
      <!-- 搜索图标 -->
      <view class="search-icon-wrapper">
        <view class="i-mdi-magnify search-icon"></view>
      </view>

      <!-- 搜索输入框 -->
      <textarea
        ref="textareaRef"
        class="search-input"
        :style="inputStyle"
        :placeholder="placeholder"
        :value="computedValue"
        :disabled="disabled"
        :auto-focus="autoFocus"
        :maxlength="-1"
        show-confirm-bar
        confirm-type="search"
        auto-height
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleSearch"
      />

      <!-- 右侧按钮区域 -->
      <view class="button-area">
        <!-- 清除按钮 -->
        <button
          v-if="showClearButton && computedValue && !disabled"
          class="action-btn clear-btn"
          @click="handleClear"
        >
          <view class="i-mdi-close clear-icon"></view>
        </button>

        <!-- 搜索按钮 -->
        <button
          v-if="showSearchButton"
          class="action-btn search-btn"
          :class="{
            'search-btn--loading': loading,
            'search-btn--disabled': disabled || !computedValue.trim(),
          }"
          :disabled="disabled || loading || !computedValue.trim()"
          @click="handleSearch"
        >
          <!-- 加载图标 -->
          <view v-if="loading" class="i-mdi-loading loading-icon"></view>
          <!-- 搜索图标 -->
          <view v-else class="i-mdi-magnify search-btn-icon"></view>
          <!-- 按钮文本 -->
          <text class="search-btn-text">
            {{ loading ? '搜索中...' : searchButtonText }}
          </text>
        </button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-box-container {
  width: 100%;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 10rpx 24rpx;
  background: white;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 16rpx;

  &--focused {
    border-color: #00c9a7;
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.15);
  }

  &--disabled {
    background: #f8fafc;
    border-color: #e2e8f0;
    opacity: 0.6;
  }
}

.search-icon-wrapper {
  .search-icon {
    font-size: 32rpx;
    color: #64748b;
    transition: color 0.3s ease;
  }
}

.search-box--focused .search-icon {
  color: #00c9a7;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #1e293b;
  background: transparent;
  overflow-y: auto;
  border: none;
  outline: none;
  resize: none;
  word-wrap: break-word;
  word-break: break-all;
  vertical-align: middle;
  line-height: 1.4;

  &::placeholder {
    color: #94a3b8;
  }

  &:disabled {
    color: #94a3b8;
    cursor: not-allowed;
  }
}

.button-area {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:active {
    transform: scale(0.95);
  }
}

.clear-btn {
  width: 48rpx;
  height: 48rpx;
  background: rgba(148, 163, 184, 0.1);

  &:hover {
    background: rgba(148, 163, 184, 0.2);
  }

  .clear-icon {
    font-size: 24rpx;
    color: #94a3b8;
  }
}

.search-btn {
  min-width: 120rpx;
  height: 48rpx;
  padding: 0 20rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  gap: 8rpx;

  &:not(&--disabled):not(&--loading):hover {
    background: linear-gradient(135deg, #00b297 0%, #45c7bd 100%);
    transform: translateY(-1rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);
  }

  &--disabled {
    background: #e2e8f0;
    cursor: not-allowed;

    .search-btn-icon,
    .search-btn-text {
      color: #94a3b8;
    }
  }

  &--loading {
    background: #00c9a7;
    cursor: not-allowed;
  }

  .search-btn-icon,
  .loading-icon {
    font-size: 20rpx;
    color: white;
  }

  .loading-icon {
    animation: spin 1s linear infinite;
  }

  .search-btn-text {
    font-size: 24rpx;
    font-weight: 500;
    color: white;
    white-space: nowrap;
  }
}

// 动画效果
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .search-box {
    padding: 16rpx 20rpx;
    gap: 12rpx;
  }

  .search-input {
    font-size: 26rpx;
  }

  .search-btn {
    min-width: 100rpx;
    height: 44rpx;
    padding: 0 16rpx;

    .search-btn-text {
      font-size: 22rpx;
    }

    .search-btn-icon,
    .loading-icon {
      font-size: 18rpx;
    }
  }

  .clear-btn {
    width: 44rpx;
    height: 44rpx;

    .clear-icon {
      font-size: 22rpx;
    }
  }
}
</style>
