<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import Notification from '@/components/Notification.vue'
// @ts-ignore
import Loading from '@/components/Loading.vue'
// @ts-ignore
import {
  getQuestionList,
  getQuestionStatistics,
  toggleQuestionBookmark as toggleQuestionBookmarkApi,
  getQuestionBankDetail as getBankInfoApi,
  type Question,
  type QuestionQueryParams,
  type QuestionStatistics,
  // @ts-ignore
} from '@/service/learning'
// 引入类型定义
import type { Ref } from 'vue'

// 定义筛选选项类型
interface FilterOption {
  key: string
  label: string
  count?: number
}

// 定义排序选项类型
interface SortOption {
  key: string
  label: string
}

// 页面参数
const bankId: Ref<string> = ref('')
const majorId: Ref<string> = ref('')
const bankTitle: Ref<string> = ref('题库信息')

// 页面状态
const isLoading: Ref<boolean> = ref(true)
const searchQuery: Ref<string> = ref('')
const selectedDifficulty: Ref<string> = ref('all')
const sortType: Ref<string> = ref('default')

// 分页参数
const currentPage: Ref<number> = ref(1)
const pageSize: Ref<number> = ref(20)
const hasMore: Ref<boolean> = ref(true)
const loadingMore: Ref<boolean> = ref(false)

// 筛选选项
const difficultyOptions: Ref<FilterOption[]> = ref([
  { key: 'all', label: '全部难度', count: 0 },
  { key: 'easy', label: '简单', count: 0 },
  { key: 'medium', label: '中等', count: 0 },
  { key: 'hard', label: '困难', count: 0 },
])

// 排序选项
const sortOptions: Ref<SortOption[]> = ref([
  { key: 'default', label: '默认排序' },
  { key: 'difficulty', label: '按难度' },
  { key: 'popularity', label: '按热度' },
  { key: 'latest', label: '最新发布' },
])

// 题目数据
const allQuestions: Ref<Question[]> = ref([])
const totalCount: Ref<number> = ref(0)

// 统计数据
const questionStatistics: Ref<QuestionStatistics> = ref({
  totalQuestions: 0,
  easyCount: 0,
  mediumCount: 0,
  hardCount: 0,
  averageCorrectRate: 0,
})

// 通知相关
const notificationVisible = ref(false)
const notificationMessage = ref('...')
const notificationType = ref<'success' | 'error' | 'info' | 'warning'>('info')

// 过滤和排序后的题目列表
const filteredQuestions = computed(() => {
  let questions = [...allQuestions.value]

  // 应用搜索过滤
  if (searchQuery.value) {
    questions = questions.filter(
      (question) =>
        question.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        question.content?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        question.tags?.some((tag) => tag.toLowerCase().includes(searchQuery.value.toLowerCase())),
    )
  }

  // 应用难度筛选
  if (selectedDifficulty.value !== 'all') {
    questions = questions.filter((question) => {
      const difficultyMap = { easy: '简单', medium: '中等', hard: '困难' }
      return question.difficulty === difficultyMap[selectedDifficulty.value]
    })
  }

  // 应用排序
  questions = applySorting(questions)

  return questions
})

// 统计信息
const statistics = computed(() => {
  const questions = filteredQuestions.value
  return {
    totalQuestions: questions.length,
    easyCount: questions.filter((q) => q.difficulty === '简单').length,
    mediumCount: questions.filter((q) => q.difficulty === '中等').length,
    hardCount: questions.filter((q) => q.difficulty === '困难').length,
    averageCorrectRate: Math.round(
      questions.reduce((sum, q) => sum + q.correctRate, 0) / questions.length || 0,
    ),
  }
})

/**
 * @description 显示通知栏
 * @param message 通知内容
 * @param type 通知类型（info/success/error）
 */
function showNotification(
  message: string,
  type: 'info' | 'success' | 'error' | 'warning' = 'info',
): void {
  notificationMessage.value = message
  notificationType.value = type
  notificationVisible.value = true
}

/**
 * @description 应用排序功能
 * @param questions 题目数组
 * @returns 排序后的题目数组
 */
function applySorting(questions: Question[]): Question[] {
  switch (sortType.value) {
    case 'difficulty':
      return questions.sort((a, b) => {
        const difficultyOrder: Record<string, number> = { 简单: 1, 中等: 2, 困难: 3 }
        return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
      })
    case 'popularity':
      return questions.sort((a, b) => b.practiceCount - a.practiceCount)
    case 'latest':
      return questions.sort(
        (a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime(),
      )
    default:
      return questions
  }
}

/**
 * @description 搜索处理功能，支持防抖优化
 * @param event 输入事件对象
 */
let searchTimer: number | null = null
const handleSearch = (event: any): void => {
  const value = event.detail.value || event.target.value

  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    searchQuery.value = value
  }, 300)
}

/**
 * @description 清除搜索关键词
 */
const clearSearch = (): void => {
  searchQuery.value = ''
  onSearchChange()
}

/**
 * @description 应用难度筛选条件
 * @param difficulty 难度条件
 */
const applyDifficultyFilter = (difficulty: string): void => {
  if (selectedDifficulty.value !== difficulty) {
    selectedDifficulty.value = difficulty
    onFilterChange()
  }
}

/**
 * @description 应用排序方式
 * @param sort 排序方式
 */
const applySortOption = (sort: string): void => {
  if (sortType.value !== sort) {
    sortType.value = sort
    onSortChange()
  }
}

/**
 * @description 重置所有筛选和排序条件
 */
const resetFilters = (): void => {
  selectedDifficulty.value = 'all'
  sortType.value = 'default'
  searchQuery.value = ''
  showNotification('已重置筛选条件', 'success')
  refreshData()
}

/**
 * @description 根据难度返回对应的CSS类名
 * @param difficulty 难度等级
 * @returns CSS类名字符串
 */
const getDifficultyStyle = (difficulty: string): string => {
  const styles: Record<string, string> = {
    简单: 'difficulty-easy',
    中等: 'difficulty-medium',
    困难: 'difficulty-hard',
  }
  return styles[difficulty] || 'difficulty-default'
}

/**
 * @description 将大数字格式化为易读的形式
 * @param num 数字
 * @returns 格式化后的字符串
 */
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

/**
 * @description 跳转到题目详情页面
 * @param question 题目对象
 */
const openQuestionDetail = (question: Question): void => {
  uni.navigateTo({
    url: `/pages/learning/question-detail?id=${question.id}&bankId=${bankId.value}`,
    fail: (err) => {
      console.error('页面跳转失败:', err)
      showNotification('页面跳转失败', 'error')
    },
  })
}

/**
 * @description 直接开始练习指定题目
 * @param question 题目对象
 */
const startQuestionPractice = (question: Question): void => {
  uni.navigateTo({
    url: `/pages/learning/practice?questionId=${question.id}&bankId=${bankId.value}&major=${majorId.value}`,
  })
}

/**
 * @description 切换题目的收藏状态
 * @param question 题目对象
 */
const toggleQuestionBookmark = async (question: Question): Promise<void> => {
  try {
    const newBookmarkStatus = !question.isBookmarked

    const res = await toggleQuestionBookmarkApi(question.id, {
      isBookmarked: newBookmarkStatus,
    })

    if (res.code === 200) {
      question.isBookmarked = newBookmarkStatus
      showNotification(newBookmarkStatus ? '已收藏题目' : '已取消收藏', 'success')
    } else {
      showNotification(res.message || '操作失败', 'error')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    showNotification('操作失败，请稍后重试', 'error')
  }
}

/**
 * @description 根据题库ID加载题目数据
 * @param refresh 是否刷新数据
 */
const loadQuestions = async (refresh = false): Promise<void> => {
  try {
    if (refresh) {
      currentPage.value = 1
      allQuestions.value = []
      hasMore.value = true
    }

    if (!hasMore.value && !refresh) {
      return
    }

    const loading = refresh ? isLoading : loadingMore
    loading.value = true

    // 构建查询参数
    const params: QuestionQueryParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    }

    // 添加搜索和筛选条件
    if (searchQuery.value) {
      params.keyword = searchQuery.value
    }
    if (selectedDifficulty.value !== 'all') {
      const difficultyMap = { easy: '简单', medium: '中等', hard: '困难' }
      params.difficulty = difficultyMap[selectedDifficulty.value]
    }

    // 添加排序条件
    switch (sortType.value) {
      case 'difficulty':
        params.orderBy = 'difficulty'
        params.orderDirection = 'asc'
        break
      case 'popularity':
        params.orderBy = 'practiceCount'
        params.orderDirection = 'desc'
        break
      case 'latest':
        params.orderBy = 'createTime'
        params.orderDirection = 'desc'
        break
    }

    const res = await getQuestionList(bankId.value, params)

    if (res.code === 200) {
      const data = res.data

      if (refresh) {
        allQuestions.value = data.list
      } else {
        allQuestions.value = [...allQuestions.value, ...data.list]
      }

      totalCount.value = data.total
      hasMore.value = data.list.length === pageSize.value

      if (!refresh) {
        currentPage.value++
      }

      // 更新难度筛选计数
      updateDifficultyFilterCounts()
    } else {
      showNotification(res.message || '加载失败', 'error')
    }
  } catch (error) {
    console.error('加载题目失败:', error)
    showNotification('加载失败，请稍后重试', 'error')
  } finally {
    isLoading.value = false
    loadingMore.value = false
  }
}

/**
 * @description 加载题库的统计信息
 */
const loadStatistics = async () => {
  try {
    const res = await getQuestionStatistics(bankId.value)

    if (res.code === 200) {
      questionStatistics.value = res.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

/**
 * @description 更新各难度选项的题目数量
 */
const updateDifficultyFilterCounts = () => {
  const questions = allQuestions.value

  difficultyOptions.value.forEach((option) => {
    switch (option.key) {
      case 'all':
        option.count = questions.length
        break
      case 'easy':
        option.count = questions.filter((q) => q.difficulty === '简单').length
        break
      case 'medium':
        option.count = questions.filter((q) => q.difficulty === '中等').length
        break
      case 'hard':
        option.count = questions.filter((q) => q.difficulty === '困难').length
        break
    }
  })
}

/**
 * @description 刷新题目列表数据
 */
const refreshData = async () => {
  await Promise.all([loadQuestions(true), loadStatistics()])
}

/**
 * @description 加载更多题目数据
 */
const loadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    loadQuestions(false)
  }
}

/**
 * @description 当筛选条件改变时重新加载数据
 */
const onFilterChange = () => {
  refreshData()
}

/**
 * @description 监听搜索变化，当用户停止输入500ms后才执行搜索
 */
let searchChangeTimer: number | null = null
const onSearchChange = () => {
  // 清除之前的定时器
  if (searchChangeTimer) {
    clearTimeout(searchChangeTimer)
  }
  // 如果搜索框为空，直接刷新数据
  // 设置新的定时器，用户停止输入500ms后才执行搜索
  searchChangeTimer = setTimeout(() => {
    refreshData()
  }, 1000)
}

// 监听排序变化
const onSortChange = () => {
  refreshData()
}

/**
 * @description 根据bankId获取题库信息
 * @param bankId 题库ID
 * @returns 题库信息
 */
const getBankInfo = async (bankId: string, majorId: string) => {
  const res = await getBankInfoApi({ params: { bankId, majorId } })
  return res.data
}

// 页面加载时获取参数
onLoad(async (options) => {
  bankId.value = options.bankId || ''
  majorId.value = options.major || ''
  // 根据bankId去获取题库的相关信息
  getBankInfo(bankId.value, majorId.value).then((res) => {
    bankTitle.value = res.title || '题库信息'
  })
})

// 页面挂载时初始化
onMounted(() => {
  refreshData()
})
</script>

<template>
  <view class="page-container">
    <HeadBar :title="`${bankTitle}`" />

    <!-- 主要内容区域 -->
    <scroll-view
      scroll-y
      class="main-content"
      :style="{ height: 'calc(100vh - 56px)' }"
      refresher-enabled
      :refresher-triggered="false"
      @scrolltolower="loadMore"
      @refresherrefresh="refreshData"
    >
      <view class="content-wrapper">
        <!-- 页面头部 -->
        <view class="header-section">
          <view class="title-section">
            <text class="main-title">{{ bankTitle }}</text>
            <text class="sub-title">
              共 {{ statistics.totalQuestions }} 道题目，平均正确率
              {{ statistics.averageCorrectRate }}%
            </text>
          </view>

          <!-- 统计卡片 -->
          <view class="stats-cards">
            <view class="stat-card">
              <text class="stat-number">{{ statistics.easyCount }}</text>
              <text class="stat-label">简单</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ statistics.mediumCount }}</text>
              <text class="stat-label">中等</text>
            </view>
            <view class="stat-card">
              <text class="stat-number">{{ statistics.hardCount }}</text>
              <text class="stat-label">困难</text>
            </view>
          </view>

          <!-- 搜索框 -->
          <view class="search-container">
            <input
              type="text"
              class="search-input"
              placeholder="搜索题目标题、内容或标签..."
              v-model="searchQuery"
              @input="onSearchChange"
              @confirm="onSearchChange"
            />
            <view class="search-icon">
              <text class="fa fa-search"></text>
            </view>
            <view v-if="searchQuery" class="clear-btn" @click="clearSearch">
              <text class="fa fa-times"></text>
            </view>
          </view>
        </view>

        <!-- 筛选和排序 -->
        <view class="filter-section">
          <view class="filter-header">
            <text class="filter-title">筛选排序</text>
            <view class="reset-btn" @click="resetFilters">重置</view>
          </view>

          <!-- 难度筛选 -->
          <scroll-view show-scrollbar="false" scroll-x class="filter-scroll">
            <view class="filter-tags">
              <view
                v-for="option in difficultyOptions"
                :key="option.key"
                class="filter-tag"
                :class="{ 'filter-tag-active': selectedDifficulty === option.key }"
                @click="applyDifficultyFilter(option.key)"
              >
                {{ option.label }}
                <text class="filter-count">({{ option.count }})</text>
              </view>
            </view>
          </scroll-view>

          <!-- 排序选项 -->
          <scroll-view show-scrollbar="false" scroll-x class="sort-scroll">
            <view class="sort-tags">
              <view
                v-for="option in sortOptions"
                :key="option.key"
                class="sort-tag"
                :class="{ 'sort-tag-active': sortType === option.key }"
                @click="applySortOption(option.key)"
              >
                {{ option.label }}
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 题目列表 -->
        <view class="questions-section">
          <!-- 加载中状态 -->
          <Loading
            v-if="isLoading"
            :visible="isLoading"
            text="加载中..."
            size="medium"
            type="spinner"
          />

          <!-- 空状态 -->
          <view v-else-if="filteredQuestions.length === 0" class="empty-container">
            <view class="empty-icon">
              <text class="fa fa-file-text-o"></text>
            </view>
            <text class="empty-title">暂无题目</text>
            <text class="empty-desc">试试调整筛选条件或搜索关键词</text>
          </view>

          <!-- 题目列表 -->
          <transition-group v-else name="question-list" tag="view" class="questions-list">
            <view
              v-for="(question, index) in filteredQuestions"
              :key="question.id"
              class="question-card"
              :style="{ animationDelay: `${index * 0.05}s` }"
              @click="openQuestionDetail(question)"
            >
              <!-- 题目头部 -->
              <view class="question-header">
                <view class="question-title-row">
                  <view
                    class="difficulty-dot"
                    :class="{
                      'dot-easy': question.difficulty === '简单',
                      'dot-medium': question.difficulty === '中等',
                      'dot-hard': question.difficulty === '困难',
                    }"
                  ></view>
                  <text class="question-title">{{ question.title }}</text>
                </view>
                <view class="question-actions">
                  <view class="bookmark-btn" @click.stop="toggleQuestionBookmark(question)">
                    <text
                      class="fa"
                      :class="question.isBookmarked ? 'fa-star' : 'fa-star-o'"
                    ></text>
                  </view>
                  <view class="difficulty-tag" :class="getDifficultyStyle(question.difficulty)">
                    {{ question.difficulty }}
                  </view>
                </view>
              </view>

              <!-- 题目信息 -->
              <view class="question-content">
                <text class="question-desc">{{ question.content }}</text>

                <!-- 题目标签 -->
                <view class="question-tags">
                  <view class="type-tag">{{ question.type }}</view>
                  <view v-for="tag in question.tags" :key="tag" class="tag">{{ tag }}</view>
                </view>

                <!-- 统计信息 -->
                <view class="question-stats">
                  <view class="stat-item">
                    <text class="stat-icon fa fa-users"></text>
                    <text class="stat-text">{{ formatNumber(question.practiceCount) }} 人练习</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon fa fa-bar-chart"></text>
                    <text class="stat-text">正确率 {{ question.correctRate }}%</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon fa fa-comments"></text>
                    <text class="stat-text">{{ question.commentCount }} 讨论</text>
                  </view>
                  <view class="stat-item">
                    <text class="stat-icon fa fa-clock-o"></text>
                    <text class="stat-text">约 {{ question.estimatedTime }} 分钟</text>
                  </view>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="question-actions-bottom">
                <view class="action-btn secondary" @click.stop="openQuestionDetail(question)">
                  <text class="fa fa-eye"></text>
                  <text class="btn-text">查看详情</text>
                </view>
                <view class="action-btn primary" @click.stop="startQuestionPractice(question)">
                  <text class="fa fa-play"></text>
                  <text class="btn-text">开始练习</text>
                </view>
              </view>
            </view>
          </transition-group>

          <!-- 加载更多指示器 -->
          <Loading
            v-if="loadingMore"
            :visible="loadingMore"
            text="加载更多..."
            size="small"
            type="spinner"
            padding="40rpx 0"
          />

          <!-- 没有更多数据 -->
          <view v-else-if="!hasMore && filteredQuestions.length > 0" class="no-more-container">
            <view class="no-more-line"></view>
            <text class="no-more-text">已显示全部题目</text>
            <view class="no-more-line"></view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 通知弹窗 -->
    <Notification
      :visible="notificationVisible"
      :message="notificationMessage"
      :type="notificationType"
      @update:visible="notificationVisible = $event"
      @close="notificationVisible = false"
    />
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-content {
  padding: 0;
}

.content-wrapper {
  padding: 20rpx;
}

/* 头部区域 */
.header-section {
  margin-bottom: 30rpx;
}

.title-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.main-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.sub-title {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 统计卡片 */
.stats-cards {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  flex: 1;
  margin: 0 10rpx;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #00c9a7;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 搜索框 */
.search-container {
  display: flex;
  position: relative;
  margin-bottom: 20rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  background: white;
  border-radius: 40rpx;
  padding: 0 100rpx 0 60rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #bdc3c7;
  font-size: 28rpx;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #c0392b;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 30rpx;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.reset-btn {
  font-size: 28rpx;
  color: #00c9a7;
  padding: 10rpx 20rpx;
  border: 2rpx solid #00c9a7;
  border-radius: 20rpx;
}

.filter-scroll,
.sort-scroll {
  margin-bottom: 20rpx;
}

.filter-tags,
.sort-tags {
  display: flex;
  white-space: nowrap;
  padding: 0 10rpx;
}

.filter-tag,
.sort-tag {
  background: white;
  border: 2rpx solid #ecf0f1;
  border-radius: 30rpx;
  padding: 15rpx 25rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #7f8c8d;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tag-active,
.sort-tag-active {
  background: #00c9a7;
  border-color: #00c9a7;
  color: white;
}

.filter-count {
  margin-left: 8rpx;
  font-size: 22rpx;
  opacity: 0.8;
}

/* 题目列表 */
.questions-section {
  margin-bottom: 40rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #bdc3c7;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  text-align: center;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.question-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.question-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.question-title-row {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 20rpx;
}

.difficulty-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.dot-easy {
  background: #52c41a;
}

.dot-medium {
  background: #faad14;
}

.dot-hard {
  background: #f5222d;
}

.question-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
}

.question-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.bookmark-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.bookmark-btn .fa-star {
  color: #ffc107;
  font-size: 24rpx;
}

.bookmark-btn .fa-star-o {
  color: #bdc3c7;
  font-size: 24rpx;
}

.difficulty-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.difficulty-easy {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.difficulty-medium {
  background: #fffbe6;
  color: #faad14;
  border: 1rpx solid #ffe58f;
}

.difficulty-hard {
  background: #fff2f0;
  color: #f5222d;
  border: 1rpx solid #ffccc7;
}

.question-content {
  margin-bottom: 25rpx;
}

.question-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.type-tag {
  background: #e8f4fd;
  color: #1890ff;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.question-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 20rpx;
  color: #7f8c8d;
}

.stat-text {
  font-size: 22rpx;
  color: #999;
}

.question-actions-bottom {
  display: flex;
  gap: 20rpx;
  margin-top: 25rpx;
  padding-top: 25rpx;
  border-top: 1rpx solid #f5f5f5;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn .fa {
  font-size: 24rpx;
}

.action-btn .btn-text {
  font-size: 26rpx;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.action-btn.secondary .fa {
  color: #666;
}

.action-btn.primary {
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  color: white;
}

.action-btn.primary .fa {
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

.no-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  animation: slideInUp 0.5s ease;
}

.no-more-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(to right, transparent, #e0e0e0, transparent);
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
  padding: 0 30rpx;
  white-space: nowrap;
}

/* 筛选标签点击动画 */
.filter-tag,
.sort-tag {
  position: relative;
  overflow: hidden;
}

.filter-tag::before,
.sort-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.filter-tag:active::before,
.sort-tag:active::before {
  left: 100%;
}

/* 搜索框聚焦动画 */
.search-input:focus {
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.2);
  border: 2rpx solid #00c9a7;
  transform: scale(1.02);
  transition: all 0.3s ease;
}

/* 统计卡片悬停动画 */
.stat-card {
  transform: translateY(0);
  transition: all 0.3s ease;
}

.stat-card:active {
  transform: translateY(-8rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

/* 收藏按钮点击动画 */
.bookmark-btn:active {
  transform: scale(1.2);
  background: #fff3cd;
}

.bookmark-btn .fa-star {
  transition: all 0.3s ease;
}

.bookmark-btn .fa-star-o {
  transition: all 0.3s ease;
}

/* 操作按钮点击反馈 */
.action-btn {
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition:
    width 0.6s,
    height 0.6s;
}

.action-btn:active::after {
  width: 300rpx;
  height: 300rpx;
}

/* 过渡动画 */
.question-list-enter-active,
.question-list-leave-active {
  transition: all 0.5s ease;
}

.question-list-enter-from {
  opacity: 0;
  transform: translateY(30rpx);
}

.question-list-leave-to {
  opacity: 0;
  transform: translateY(-30rpx);
}

.question-list-move {
  transition: transform 0.5s ease;
}

/* 统计图标动画 */
.stat-icon {
  transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
  color: #00c9a7;
  transform: scale(1.1);
}

/* 操作按钮图标动画 */
.action-btn .fa {
  transition: all 0.3s ease;
}

.action-btn:hover .fa {
  transform: scale(1.1);
}

/* 清除按钮动画 */
.clear-btn .fa {
  font-size: 20rpx;
  transition: all 0.2s ease;
}

.clear-btn:active .fa {
  transform: scale(1.2);
}
</style>
