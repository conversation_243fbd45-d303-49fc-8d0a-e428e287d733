/* eslint-disable */
// @ts-ignore
import request from '@/utils/request'
import { CustomRequestOptions } from '@/interceptors/request'

// 类型定义
export interface InterviewRecord {
  id: number
  jobName: string
  company: string
  icon: string
  difficulty: string
  questionCount: number
  date: string
  time: string
  duration: string
  totalScore: number
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'completed' | 'in-progress' | 'cancelled'
  timeAgo: string
  dimensions: {
    professional: number
    communication: number
    logic: number
    innovation: number
  }
  category: string
}

export interface Statistics {
  totalInterviews: number
  averageScore: number
  monthlyImprovement: number
  improvementPercent: number
  currentLevel: string
  nextLevelProgress: number
}

export interface PaginationParams {
  page: number
  pageSize: number
  category?: string
  status?: string
}

export interface LoadMoreParams extends PaginationParams {
  lastId?: number
}

export interface HistoryListResponse {
  records: InterviewRecord[]
  total: number
  hasMore: boolean
  page: number
  pageSize: number
}

export interface InterviewDetail extends InterviewRecord {
  questions: Array<{
    id: number
    question: string
    answer: string
    score: number
    feedback: string
  }>
}

export interface FilterOption {
  id: string
  name: string
  icon: string
  count: number
}

export interface HistoryApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * @description 获取面试历史记录
 * @param params 分页和筛选参数
 * @param options 请求选项
 */
export async function getHistoryRecords({
  params,
  options,
}: {
  params: PaginationParams
  options?: CustomRequestOptions
}) {
  return request<HistoryApiResponse<HistoryListResponse>>('/app/interview/history', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
    ...(options || {}),
  })
}

/**
 * @description 获取统计数据
 * @param options 请求选项
 */
export async function getStatistics({ options }: { options?: CustomRequestOptions } = {}) {
  return request<HistoryApiResponse<Statistics>>('/app/interview/user/statistics', {
    method: 'GET',
    ...(options || {}),
  })
}

/**
 * @description 加载更多记录
 * @param params 分页参数和最后记录ID
 * @param options 请求选项
 */
export async function getMoreHistoryRecords({
  params,
  options,
}: {
  params: LoadMoreParams
  options?: CustomRequestOptions
}) {
  return request<HistoryApiResponse<HistoryListResponse>>('/interview/history/more', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
    ...(options || {}),
  })
}

/**
 * @description 获取面试详情
 * @param id 面试记录ID
 * @param options 请求选项
 */
export async function getInterviewDetail({
  id,
  options,
}: {
  id: number
  options?: CustomRequestOptions
}) {
  return request<HistoryApiResponse<InterviewDetail>>(`/interview/job/detail/${id}`, {
    method: 'GET',
    ...(options || {}),
  })
}

/**
 * @description 删除面试记录
 * @param id 面试记录ID
 * @param options 请求选项
 */
export async function deleteInterviewRecord({
  id,
  options,
}: {
  id: number
  options?: CustomRequestOptions
}) {
  return request<HistoryApiResponse<{ success: boolean }>>(`/interview/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  })
}

/**
 * @description 获取筛选选项
 * @param options 请求选项
 */
export async function getFilterOptions({ options }: { options?: CustomRequestOptions } = {}) {
  return request<HistoryApiResponse<FilterOption[]>>('/interview/filter-options', {
    method: 'GET',
    ...(options || {}),
  })
}

/**
 * @description 批量删除面试记录
 * @param params 记录ID数组
 * @param options 请求选项
 */
export async function batchDeleteInterviewRecords({
  params,
  options,
}: {
  params: { ids: number[] }
  options?: CustomRequestOptions
}) {
  return request<HistoryApiResponse<{ success: boolean; deletedCount: number }>>(
    '/interview/batch-delete',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: params,
      ...(options || {}),
    },
  )
}

/**
 * @description 导出面试记录
 * @param params 导出参数
 * @param options 请求选项
 */
export async function exportInterviewRecords({
  params,
  options,
}: {
  params: {
    startDate?: string
    endDate?: string
    category?: string
    format?: 'pdf' | 'excel'
  }
  options?: CustomRequestOptions
}) {
  return request<Blob>('/interview/export', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
    responseType: 'blob',
    ...(options || {}),
  })
}
