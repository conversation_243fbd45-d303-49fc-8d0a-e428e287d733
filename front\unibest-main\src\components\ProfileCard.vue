<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'
import type { Badge } from '../service/achievement'

/**
 * @description 个人信息卡片组件
 * @prop userInfo 用户信息对象
 * @prop badges 用户徽章列表
 * <AUTHOR>
 */
interface UserInfo {
  name: string
  avatar: string
  major: string
  studyDays: number
  totalScore: number
  interviewCount: number
  avgScore: number
  badgeCount: number
}

const props = defineProps<{
  userInfo: UserInfo
  badges?: Badge[]
}>()

/**
 * @description 点击头像触发的事件
 * @description 点击徽章触发的事件
 */
const emit = defineEmits<{
  'avatar-click': []
  'badge-click': [badge: Badge]
  'badges-area-click': []
}>()

/**
 * @description 处理头像点击事件
 */
const onAvatarClick = () => {
  emit('avatar-click')
}

/**
 * @description 处理徽章区域点击事件（跳转到徽章页面）
 */
const onBadgesAreaClick = () => {
  emit('badges-area-click')
}

/**
 * @description 处理图片加载失败
 */
const onImageError = (e: any) => {
  console.error('头像加载失败', e)
  e.target.src = 'https://cdn.jsdelivr.net/gh/chuzhixin/image/vue-admin-beautiful/logo.png'
}

/**
 * @description 灰色占位徽章模板
 */
const placeholderBadges: Badge[] = [
  { id: 'placeholder-1', icon: 'trophy', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-2', icon: 'star', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-3', icon: 'medal', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-4', icon: 'fire', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-5', icon: 'graduation', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-6', icon: 'book', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-7', icon: 'rocket', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
  { id: 'placeholder-8', icon: 'crown', color: 'badge-gray', title: '待获得', desc: '努力解锁中...', unlocked: false },
]

/**
 * @description 获取要显示的徽章列表（固定4个）
 */
const displayBadges = computed(() => {
  const userBadges = props.badges || []
  const result: Badge[] = []
  
  // 先添加用户已获得的徽章，最多4个
  const maxUserBadges = Math.min(userBadges.length, 4)
  for (let i = 0; i < maxUserBadges; i++) {
    result.push(userBadges[i])
  }
  
  // 如果不足4个，随机添加占位徽章
  const remainingSlots = 4 - result.length
  if (remainingSlots > 0) {
    // 随机选择占位徽章
    const shuffledPlaceholders = [...placeholderBadges].sort(() => Math.random() - 0.5)
    for (let i = 0; i < remainingSlots; i++) {
      result.push(shuffledPlaceholders[i])
    }
  }
  
  return result
})

/**
 * @description 获取图标类名
 */
const getIconClass = (icon: string): string => {
  const iconMap: Record<string, string> = {
    trophy: 'i-fa-solid-trophy',
    fire: 'i-fa-solid-fire',
    star: 'i-fa-solid-star',
    medal: 'i-fa-solid-medal',
    crown: 'i-fa-solid-crown',
    gem: 'i-fa-solid-gem',
    rocket: 'i-fa-solid-rocket',
    graduation: 'i-fa-solid-user-graduate',
    book: 'i-fa-solid-book',
    lightbulb: 'i-fa-solid-lightbulb',
    target: 'i-fa-solid-bullseye',
    certificate: 'i-fa-solid-certificate',
    clock: 'i-fa-solid-clock',
    heart: 'i-fa-solid-heart',
    shield: 'i-fa-solid-shield',
  }
  return iconMap[icon] || 'i-fa-solid-award'
}
</script>

<template>
  <view class="profile-card">
    <view class="profile-bg-deco deco1"></view>
    <view class="profile-bg-deco deco2"></view>
    <view class="profile-content">
      <view class="profile-user">
        <view class="avatar-box" @click="onAvatarClick">
          <image
            :src="
              userInfo.avatar ||
              'https://cdn.jsdelivr.net/gh/chuzhixin/image/vue-admin-beautiful/logo.png'
            "
            class="avatar"
            mode="aspectFill"
            @error="onImageError"
          />
        </view>
        <view class="user-info">
          <!-- 用户名称和徽章组合 -->
          <view class="user-name-badges">
            <text class="user-name">{{ userInfo.name || '用户' }}</text>
            <!-- 徽章显示区域 -->
            <view class="user-badges" @click="onBadgesAreaClick">
              <view
                class="user-badge-item"
                v-for="badge in displayBadges"
                :key="badge.id"
              >
                <view class="user-badge-icon" :class="badge.color">
                  <text :class="getIconClass(badge.icon)" class="badge-icon-text"></text>
                </view>
              </view>
            </view>
          </view>
          <text class="user-major">{{ userInfo.major || '暂无专业信息' }}</text>
          <text class="user-desc">
            已学习 {{ userInfo.studyDays || 0 }} 天 · 总分 {{ userInfo.totalScore || 0 }}
          </text>
        </view>
      </view>
      <view class="profile-stats">
        <view class="stat-item">
          <text class="stat-value">{{ userInfo.interviewCount || 0 }}</text>
          <text class="stat-label">面试次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userInfo.avgScore || 0 }}</text>
          <text class="stat-label">平均分数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ userInfo.badgeCount || 0 }}</text>
          <text class="stat-label">获得徽章</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 个人信息卡片
.profile-card {
  position: relative;
  padding: 48rpx 32rpx 32rpx 32rpx;
  margin: 20rpx 0 32rpx 0;
  overflow: hidden;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.2);
}

.profile-bg-deco {
  position: absolute;
  background: #fff;
  border-radius: 50%;
  opacity: 0.12;
}

.deco1 {
  top: 24rpx;
  right: 48rpx;
  width: 120rpx;
  height: 120rpx;
  animation: floating 5s ease-in-out infinite;
}

.deco2 {
  bottom: 24rpx;
  left: 96rpx;
  width: 72rpx;
  height: 72rpx;
  animation: floating 4s ease-in-out infinite reverse;
}

@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-12rpx) rotate(5deg);
  }
}

.profile-content {
  position: relative;
  z-index: 1;
}

.profile-user {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.avatar-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  margin-right: 32rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(6rpx);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s;
  flex-shrink: 0;

  &:active {
    transform: scale(0.95);
  }
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border: 3rpx solid #fff;
  border-radius: 50%;
}

.user-info {
  flex: 1;
  min-width: 0;
}

// 用户名称和徽章组合
.user-name-badges {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

// 徽章显示区域
.user-badges {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 4rpx 8rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.2);
  }

  @media (hover: hover) {
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }
  }
}

.user-badge-item {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.9);
  }
}

.user-badge-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.badge-icon-text {
  font-size: 14rpx;
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.user-major {
  display: block;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  opacity: 0.92;
}

.user-desc {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
}

.profile-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(6rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.stat-value {
  display: block;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.85;
}

// 徽章颜色样式
.badge-gold {
  background: linear-gradient(135deg, #ffd700, #ffc300);
}

.badge-silver {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
}

.badge-bronze {
  background: linear-gradient(135deg, #cd7f32, #b87333);
}

.badge-blue {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.badge-green {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.badge-purple {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.badge-red {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.badge-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

// 灰色占位徽章
.badge-gray {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .profile-card {
    padding: 32rpx 24rpx;
  }

  .profile-user {
    margin-bottom: 24rpx;
  }

  .avatar-box {
    width: 80rpx;
    height: 80rpx;
    margin-right: 24rpx;
  }

  .avatar {
    width: 68rpx;
    height: 68rpx;
    border: 2rpx solid #fff;
  }

  .user-name {
    font-size: 30rpx;
  }

  .user-name-badges {
    gap: 12rpx;
    margin-bottom: 6rpx;
  }

  .user-badges {
    gap: 6rpx;
    padding: 3rpx 6rpx;
    border-radius: 16rpx;
  }

  .user-badge-icon {
    width: 24rpx;
    height: 24rpx;
  }

  .badge-icon-text {
    font-size: 12rpx;
  }

  .user-major {
    font-size: 24rpx;
    margin-bottom: 8rpx;
  }

  .user-desc {
    font-size: 20rpx;
  }

  .profile-stats {
    gap: 16rpx;
  }

  .stat-item {
    padding: 16rpx 0;
    border-radius: 16rpx;
  }

  .stat-value {
    font-size: 28rpx;
    margin-bottom: 6rpx;
  }

  .stat-label {
    font-size: 20rpx;
  }
}

// 小程序端优化
/* #ifdef MP */
.user-badges {
  &:active {
    background: rgba(255, 255, 255, 0.25);
  }
}

.user-badge-item {
  &:active {
    transform: scale(0.85);
  }
}
/* #endif */

// H5端优化
/* #ifdef H5 */
.user-badges {
  cursor: pointer;
  
  &:hover {
    background: rgba(255, 255, 255, 0.22);
  }
}

.user-badge-item {
  cursor: pointer;
  
  &:hover {
    transform: scale(1.1);
  }
}
/* #endif */
</style>
