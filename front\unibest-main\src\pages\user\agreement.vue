<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

/**
 * @description 用户服务协议页面
 * 包含平台使用协议、服务条款等内容
 */

// 协议更新时间
const lastUpdateTime = ref('2024年1月1日')

// 协议章节数据
const agreementSections = ref([
  {
    id: 'service-agreement',
    title: '服务协议',
    content: [
      '欢迎使用我们的面试辅导服务平台。在使用本平台之前，请仔细阅读并理解以下服务协议条款。',
      '通过访问或使用本平台，您同意接受本协议的约束。如果您不同意本协议的任何条款，请不要使用本平台。',
    ],
  },
  {
    id: 'account-rules',
    title: '账户使用规范',
    content: [
      '用户注册时必须提供真实、准确、完整的个人信息。',
      '用户应当妥善保管账户信息，不得将账户借给他人使用。',
      '禁止恶意注册多个账户或进行虚假注册。',
      '用户对其账户下的所有活动承担责任。',
    ],
  },
  {
    id: 'service-content',
    title: '服务内容说明',
    content: [
      '本平台提供面试辅导、学习资料、模拟面试等相关服务。',
      '平台有权根据实际情况调整服务内容和功能。',
      '用户应合理使用平台资源，不得进行恶意操作。',
      '平台提供的内容仅供学习参考，不保证面试成功。',
    ],
  },
  {
    id: 'payment-rules',
    title: '付费服务条款',
    content: [
      '用户购买付费服务前应仔细了解服务内容和价格。',
      '付费服务一经购买，除非存在技术问题，否则不予退款。',
      '平台有权根据市场情况调整服务价格。',
      '用户应通过合法渠道进行支付，禁止恶意退款。',
    ],
  },
  {
    id: 'content-copyright',
    title: '内容版权声明',
    content: [
      '平台提供的所有学习内容、题库、视频等均受版权保护。',
      '用户仅可个人学习使用，不得用于商业用途。',
      '禁止复制、传播、销售平台内容。',
      '用户上传的内容应确保不侵犯他人版权。',
    ],
  },
  {
    id: 'privacy-protection',
    title: '隐私保护承诺',
    content: [
      '我们承诺保护用户的个人隐私信息。',
      '用户数据仅用于提供更好的服务体验。',
      '未经用户同意，不会向第三方泄露个人信息。',
      '用户可随时查看、修改或删除个人信息。',
    ],
  },
  {
    id: 'platform-rights',
    title: '平台权利义务',
    content: [
      '平台有权对违规用户进行警告、限制或封号处理。',
      '平台有义务维护良好的学习环境和服务质量。',
      '平台有权根据法律法规要求配合相关调查。',
      '平台会持续优化服务，提升用户体验。',
    ],
  },
  {
    id: 'user-responsibilities',
    title: '用户责任义务',
    content: [
      '用户应遵守国家法律法规和平台规定。',
      '不得发布违法、有害、虚假信息。',
      '不得进行任何可能损害平台利益的行为。',
      '应当理性使用平台服务，维护良好秩序。',
    ],
  },
  {
    id: 'limitation-liability',
    title: '免责声明',
    content: [
      '平台提供的服务不保证绝对无误或不中断。',
      '用户因使用平台服务产生的任何损失，平台不承担责任。',
      '因不可抗力导致的服务中断，平台不承担责任。',
      '第三方服务造成的问题，平台不承担连带责任。',
    ],
  },
  {
    id: 'agreement-changes',
    title: '协议变更说明',
    content: [
      '平台有权根据实际情况修改本协议条款。',
      '协议变更后将在平台内公示，请用户及时关注。',
      '继续使用平台服务即视为同意变更后的协议。',
      '如不同意变更，用户可选择停止使用平台服务。',
    ],
  },
])

/**
 * @description 滚动到指定章节
 * @param sectionId 章节ID
 */
const scrollToSection = (sectionId: string): void => {
  const query = uni.createSelectorQuery()
  query
    .select(`#${sectionId}`)
    .boundingClientRect((data: any) => {
      if (data) {
        uni.pageScrollTo({
          scrollTop: data.top - 100,
          duration: 300,
        })
      }
    })
    .exec()
}

/**
 * @description 返回上一页
 */
const goBack = (): void => {
  uni.navigateBack()
}
</script>

<template>
  <view class="agreement-page">
    <HeadBar title="用户协议" :show-back="true" :show-right-button="false" />

    <!-- 协议导航 -->
    <view class="agreement-nav" style="margin-top: 80rpx">
      <view class="nav-title">协议目录</view>
      <scroll-view class="nav-scroll" scroll-x>
        <view class="nav-items">
          <view
            v-for="section in agreementSections"
            :key="section.id"
            class="nav-item"
            @click="scrollToSection(section.id)"
          >
            {{ section.title }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 协议内容 -->
    <view class="agreement-content">
      <view class="content-header">
        <text class="content-title">用户服务协议</text>
        <text class="update-time">最后更新：{{ lastUpdateTime }}</text>
      </view>

      <!-- 协议章节 -->
      <view
        v-for="(section, index) in agreementSections"
        :key="section.id"
        :id="section.id"
        class="agreement-section"
      >
        <view class="section-header">
          <text class="section-number">{{ index + 1 }}.</text>
          <text class="section-title">{{ section.title }}</text>
        </view>

        <view class="section-content">
          <view
            v-for="(paragraph, pIndex) in section.content"
            :key="pIndex"
            class="content-paragraph"
          >
            <text class="paragraph-number">{{ index + 1 }}.{{ pIndex + 1 }}</text>
            <text class="paragraph-text">{{ paragraph }}</text>
          </view>
        </view>
      </view>

      <!-- 协议底部 -->
      <view class="agreement-footer">
        <view class="footer-divider"></view>
        <view class="footer-info">
          <text class="footer-text">
            以上条款构成完整的用户服务协议。如有疑问，请联系客服获取帮助。
          </text>
          <text class="contact-info">客服邮箱：<EMAIL></text>
          <text class="contact-info">客服电话：400-123-4567</text>
        </view>

        <view class="agreement-actions">
          <button class="action-btn primary-btn" @click="goBack">
            <text class="i-fa-check" style="margin-right: 8rpx; font-size: 24rpx"></text>
            <text>我已阅读并同意</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.agreement-page {
  min-height: 100vh;
  background: #f8fafc;
}

// 协议导航
.agreement-nav {
  padding: 0 32rpx;
  margin-bottom: 32rpx;

  .nav-title {
    margin-bottom: 20rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .nav-scroll {
    white-space: nowrap;

    .nav-items {
      display: flex;
      gap: 16rpx;
      padding-bottom: 16rpx;

      .nav-item {
        flex-shrink: 0;
        padding: 12rpx 24rpx;
        background: white;
        border: 2rpx solid #e2e8f0;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #64748b;
        transition: all 0.3s ease;

        &:active {
          background: #00c9a7;
          border-color: #00c9a7;
          color: white;
          transform: scale(0.95);
        }
      }
    }
  }
}

// 协议内容
.agreement-content {
  padding: 0 32rpx 40rpx;

  .content-header {
    padding: 40rpx 32rpx;
    margin-bottom: 32rpx;
    background: white;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    text-align: center;

    .content-title {
      display: block;
      margin-bottom: 16rpx;
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .update-time {
      font-size: 24rpx;
      color: #64748b;
    }
  }
}

// 协议章节
.agreement-section {
  margin-bottom: 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    padding: 32rpx;
    background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
    border-bottom: 2rpx solid #e6fffa;

    .section-number {
      margin-right: 16rpx;
      font-size: 28rpx;
      font-weight: 700;
      color: #00c9a7;
    }

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }

  .section-content {
    padding: 32rpx;

    .content-paragraph {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .paragraph-number {
        flex-shrink: 0;
        width: 80rpx;
        margin-right: 16rpx;
        font-size: 22rpx;
        font-weight: 600;
        color: #00c9a7;
        line-height: 1.6;
      }

      .paragraph-text {
        flex: 1;
        font-size: 26rpx;
        color: #475569;
        line-height: 1.6;
      }
    }
  }
}

// 协议底部
.agreement-footer {
  margin-top: 40rpx;
  padding: 40rpx 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .footer-divider {
    height: 2rpx;
    margin-bottom: 32rpx;
    background: linear-gradient(to right, transparent, #e2e8f0, transparent);
  }

  .footer-info {
    margin-bottom: 40rpx;
    text-align: center;

    .footer-text {
      display: block;
      margin-bottom: 24rpx;
      font-size: 26rpx;
      color: #475569;
      line-height: 1.6;
    }

    .contact-info {
      display: block;
      margin-bottom: 8rpx;
      font-size: 24rpx;
      color: #64748b;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .agreement-actions {
    display: flex;
    justify-content: center;

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 300rpx;
      height: 88rpx;
      font-size: 28rpx;
      font-weight: 600;
      border-radius: 20rpx;
      border: none;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &.primary-btn {
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
      }
    }
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .agreement-content {
    padding: 0 20rpx 40rpx;
  }

  .agreement-section {
    .section-content {
      padding: 24rpx;

      .content-paragraph {
        .paragraph-number {
          width: 60rpx;
        }

        .paragraph-text {
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
