{"projectType": "software-xunfei-uniapp", "description": "基于uniapp的最佳实践框架项目，使用Vue3 + Vite5 + Pnpm + TypeScript + UnoCSS", "technologies": {"frontend": ["Vue3", "TypeScript", "Vite5", "Pnpm", "UnoCSS", "UniApp"], "platforms": ["微信小程序", "H5", "APP", "支付宝小程序", "钉钉小程序", "抖音小程序"]}, "codeStyle": {"prettier": {"singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "trailingComma": "all", "endOfLine": "auto", "htmlWhitespaceSensitivity": "ignore"}, "eslint": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:vue/vue3-essential", "plugin:import/recommended", "standard", "prettier", "plugin:prettier/recommended"], "rules": {"prettier/prettier": "error", "import/no-unresolved": "off", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "import/prefer-default-export": ["off"], "no-console": ["off"], "import/no-extraneous-dependencies": "off", "no-plusplus": "off", "no-shadow": "off", "vue/multi-word-component-names": "off", "@typescript-eslint/no-explicit-any": "off", "no-underscore-dangle": "off", "no-use-before-define": "off", "no-undef": "off", "no-unused-vars": "off", "no-param-reassign": "off", "@typescript-eslint/no-unused-vars": "off", "no-redeclare": "off", "@typescript-eslint/no-redeclare": "error"}}, "stylelint": {"extends": ["stylelint-config-recommended", "stylelint-config-recommended-scss", "stylelint-config-recommended-vue/scss", "stylelint-config-html/vue", "stylelint-config-recess-order"], "rules": {"prettier/prettier": true, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "export", "v-deep", "deep"]}], "unit-no-unknown": [true, {"ignoreUnits": ["rpx"]}], "selector-type-no-unknown": [true, {"ignoreTypes": ["page"]}], "comment-empty-line-before": "never", "custom-property-empty-line-before": "never", "no-empty-source": null, "comment-no-empty": null, "no-duplicate-selectors": null, "scss/comment-no-empty": null, "selector-class-pattern": null, "font-family-no-missing-generic-family-keyword": null}}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"body-leading-blank": [2, "always"], "footer-leading-blank": [1, "always"], "header-max-length": [2, "always", 108], "subject-empty": [2, "never"], "type-empty": [2, "never"], "subject-case": [0], "type-enum": [2, "always", ["feat", "fix", "perf", "style", "docs", "test", "refactor", "build", "ci", "chore", "revert", "wip", "workflow", "types", "release"]]}}}, "fileStructure": {"src": {"components": "公共组件目录", "hooks": "自定义钩子函数", "interceptors": "拦截器目录", "layouts": "布局组件目录", "pages": "页面目录", "pages-sub": "分包页面目录", "service": "API服务目录", "static": "静态资源目录", "store": "状态管理目录", "style": "全局样式目录", "types": "类型定义目录", "uni_modules": "uni-app模块目录", "utils": "工具函数目录"}}, "vscode": {"extensions": ["vue.volar", "stylelint.vscode-stylelint", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "antfu.unocss", "antfu.iconify", "evils.uniapp-vscode", "uni-helper.uni-helper-vscode", "uni-helper.uni-app-schemas-vscode", "uni-helper.uni-highlight-vscode", "uni-helper.uni-ui-snippets-vscode", "uni-helper.uni-app-snippets-vscode", "mrmlnc.vscode-json5", "streetsidesoftware.code-spell-checker"], "settings": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "stylelint.validate": ["css", "scss", "vue", "html"], "stylelint.enable": true, "css.validate": false, "less.validate": false, "scss.validate": false, "files.associations": {"pages.json": "jsonc", "manifest.json": "jsonc"}}}, "gitHooks": {"pre-commit": "npx lint-staged", "commit-msg": "npx commitlint --edit", "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --cache --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix"]}}, "pageStructure": {"pages": ["登录页", "注册页", "个人中心页", "面试岗位选择页", "模拟面试页", "面试结果页", "学习资源推荐页", "历史记录页"]}, "codeConventions": {"naming": {"components": "多单词组件名，使用PascalCase", "variables": "使用camelCase", "functions": "使用camelCase", "cssClasses": "使用kebab-case"}, "imports": {"preferRelativePaths": true, "noExtraneousDependencies": true}, "typescript": {"strictMode": true, "noUnusedVars": true, "noImplicitAny": false}}}