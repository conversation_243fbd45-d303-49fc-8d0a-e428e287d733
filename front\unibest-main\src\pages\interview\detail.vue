// @ts-nocheck
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getInterviewDetail, deleteInterviewRecord } from '@/service/app/interview'
import { getResultDetail, getImprovementPlan } from '@/service/interview-result'
import type { InterviewRecord } from '@/service/app/interview'

/**
 * @description 面试详情报告页面
 * 提供完整的面试分析报告和改进建议
 * 支持从历史记录或直接传参加载数据
 */

// 页面参数
const pageParams = ref({
  id: '',
  sessionId: '',
})

// 当前激活的选项卡
const activeTab = ref('overview')

// 加载状态
const loading = ref(true)

// 详细报告数据
const reportData = ref({
  basicInfo: {
    jobName: '前端开发工程师',
    company: '腾讯科技',
    date: '2023-06-15 14:30',
    duration: '25分钟',
    totalScore: 85,
    rank: '良好',
    mode: 'standard',
    answeredQuestions: 8,
    totalQuestions: 10,
  },
  dimensionScores: [
    {
      name: '专业知识水平',
      score: 85,
      fullScore: 100,
      description: '对前端技术有较深的理解',
      icon: 'i-mdi-code-braces',
    },
    {
      name: '语言表达能力',
      score: 80,
      fullScore: 100,
      description: '表达清晰，但需要提升自信度',
      icon: 'i-mdi-account-voice',
    },
    {
      name: '逻辑思维能力',
      score: 90,
      fullScore: 100,
      description: '逻辑思维清晰，分析问题有条理',
      icon: 'i-mdi-brain',
    },
    {
      name: '问题解决能力',
      score: 82,
      fullScore: 100,
      description: '能够分析问题并提供解决方案',
      icon: 'i-mdi-puzzle',
    },
    {
      name: '创新思维能力',
      score: 75,
      fullScore: 100,
      description: '创新思维有待提升',
      icon: 'i-mdi-lightbulb',
    },
    {
      name: '应变抗压能力',
      score: 88,
      fullScore: 100,
      description: '在压力下表现良好',
      icon: 'i-mdi-shield-check',
    },
  ],

  // 多模态分析数据
  multiModalAnalysis: {
    speech: {
      clarity: 85,
      fluency: 78,
      emotion: 82,
      pace: 75,
      logic: 80,
      analysis:
        '语音整体表现良好，发音清晰，语速适中。建议在重要观点时适当放慢语速，增强表达效果。',
      suggestions: [
        '注意语音语调的变化，避免过于平淡',
        '在表达技术概念时，可以适当使用专业术语',
        '回答问题时保持逻辑清晰，先总后分',
      ],
    },
    video: {
      eyeContact: 78,
      expression: 90,
      gesture: 73,
      posture: 85,
      confidence: 82,
      analysis:
        '视频表现整体良好，面部表情自然，坐姿端正。眼神交流需要加强，肢体语言可以更加丰富。',
      suggestions: [
        '保持与面试官的眼神交流，展现自信',
        '适当使用手势辅助表达，增强感染力',
        '注意面部表情的变化，保持亲和力',
      ],
    },
    text: {
      structure: 70,
      relevance: 85,
      depth: 75,
      keywords: 80,
      grammar: 92,
      analysis: '文本回答内容相关性高，语法正确。回答结构需要优化，建议使用STAR法则组织答案。',
      suggestions: [
        '使用STAR法则（情况-任务-行动-结果）组织回答',
        '增加具体的技术细节和项目经验',
        '结合实际案例说明技术应用场景',
      ],
    },
  },

  // 题目解析
  questionAnalysis: [
    {
      id: 1,
      question: '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
      category: 'technical',
      difficulty: 3,
      userAnswer: '我最熟悉Vue.js框架。Vue是一个渐进式的JavaScript框架，主要特点包括...',
      keywordMatches: ['Vue.js', '渐进式', '组件化', '响应式'],
      score: 85,
      strengths: ['技术理解深入', '回答结构清晰'],
      improvements: ['可以增加更多实际项目经验', '对比其他框架的优劣势'],
      suggestedAnswer:
        '建议从以下几个方面回答：1. 框架基本概念 2. 核心特性 3. 适用场景 4. 个人项目经验',
    },
    {
      id: 2,
      question: '描述一个你在项目中遇到的技术难题，以及你是如何解决的？',
      category: 'experience',
      difficulty: 4,
      userAnswer: '在之前的项目中，我遇到了性能优化的问题...',
      keywordMatches: ['性能优化', '解决方案', '实际经验'],
      score: 82,
      strengths: ['问题描述清晰', '解决方案具体'],
      improvements: ['可以量化优化效果', '展示技术深度'],
      suggestedAnswer: '使用STAR法则回答：情况描述→任务目标→采取行动→最终结果',
    },
  ],

  // 学习资源推荐
  learningResources: [
    {
      id: 1,
      title: '前端框架深度实战',
      type: 'course',
      description: '深入学习Vue.js、React等主流框架',
      duration: '40小时',
      difficulty: '中级',
      icon: 'i-mdi-play-circle',
      url: '/pages/learning/course-detail?id=1',
      tags: ['Vue.js', 'React', '实战项目'],
    },
    {
      id: 2,
      title: '技术面试技巧训练',
      type: 'practice',
      description: '针对性练习技术面试常见问题',
      duration: '20小时',
      difficulty: '中级',
      icon: 'i-mdi-account-group',
      url: '/pages/learning/practice?type=technical',
      tags: ['面试技巧', '技术问答', '实战练习'],
    },
    {
      id: 3,
      title: '前端性能优化专项',
      type: 'workshop',
      description: '系统学习前端性能优化方法和技巧',
      duration: '15小时',
      difficulty: '高级',
      icon: 'i-mdi-rocket',
      url: '/pages/learning/workshop?id=performance',
      tags: ['性能优化', '工程化', '最佳实践'],
    },
  ],

  // 个人提升计划
  improvementPlan: [
    {
      area: '技术深度',
      currentLevel: 75,
      targetLevel: 90,
      timeframe: '3个月',
      actions: ['深入学习框架原理和源码', '参与开源项目贡献', '实践复杂项目架构设计'],
      resources: ['前端框架源码解析', '开源项目实战', '架构设计模式'],
    },
    {
      area: '表达能力',
      currentLevel: 80,
      targetLevel: 88,
      timeframe: '2个月',
      actions: ['参加技术分享活动', '练习STAR法则回答', '模拟面试训练'],
      resources: ['演讲技巧课程', 'STAR法则训练', '模拟面试平台'],
    },
    {
      area: '项目经验',
      currentLevel: 82,
      targetLevel: 92,
      timeframe: '6个月',
      actions: ['主导完整项目开发', '学习项目管理方法', '总结项目最佳实践'],
      resources: ['项目管理课程', '技术架构设计', '团队协作工具'],
    },
  ],
})

/**
 * @description 加载面试详情数据
 */
const loadInterviewDetail = async () => {
  loading.value = true

  try {
    // 如果有ID，从历史记录中查找
    if (pageParams.value.id) {
      const historyRecords = uni.getStorageSync('interviewHistory') || []
      const record = historyRecords.find((item) => item.id === pageParams.value.id)

      if (record) {
        console.log('从历史记录加载面试详情:', record)

        // 更新基本信息
        reportData.value.basicInfo = {
          jobName: record.jobName || '前端开发工程师',
          company: record.company || '腾讯科技',
          date: record.date || new Date().toLocaleString(),
          duration: record.duration || '25分钟',
          totalScore: record.totalScore || 85,
          rank: getScoreLevel(record.totalScore || 85),
          mode: record.mode || 'standard',
          answeredQuestions: record.answeredQuestions || 8,
          totalQuestions: record.totalQuestions || 10,
        }

        // 更新维度分数
        if (record.dimensions) {
          reportData.value.dimensionScores = record.dimensions.map((dim) => ({
            name: dim.name,
            score: dim.value,
            fullScore: dim.max || 100,
            description: getDimensionDescription(dim.name, dim.value),
            icon: dim.icon || 'i-mdi-star',
          }))
        }

        // 更新多模态分析
        if (record.multiModalMetrics) {
          const metrics = record.multiModalMetrics
          reportData.value.multiModalAnalysis = {
            speech: {
              ...metrics.speech,
              analysis: generateSpeechAnalysis(metrics.speech),
              suggestions: generateSpeechSuggestions(metrics.speech),
            },
            video: {
              ...metrics.video,
              analysis: generateVideoAnalysis(metrics.video),
              suggestions: generateVideoSuggestions(metrics.video),
            },
            text: {
              ...metrics.text,
              analysis: generateTextAnalysis(metrics.text),
              suggestions: generateTextSuggestions(metrics.text),
            },
          }
        }

        // 更新学习资源推荐（基于弱项）
        updateLearningResources(record)

        // 更新个人提升计划
        updateImprovementPlan(record)
      }
    }

    // 如果有sessionId，从后端API获取详细数据
    if (pageParams.value.sessionId) {
      await loadDetailFromApi()
    }

    // 如果有ID但不是从历史记录加载，尝试从API获取
    if (pageParams.value.id && !pageParams.value.sessionId) {
      await loadDetailFromHistoryApi()
    }
  } catch (error) {
    console.error('加载面试详情失败:', error)
    uni.showToast({
      title: '加载失败，使用默认数据',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 从API加载详细数据
 */
const loadDetailFromApi = async () => {
  try {
    const response = await getResultDetail(pageParams.value.sessionId)
    if (response.code === 200 && response.data) {
      const detail = response.data

      // 更新基本信息
      reportData.value.basicInfo = {
        jobName: detail.jobName,
        company: detail.company,
        date: detail.date,
        duration: detail.duration,
        totalScore: detail.totalScore,
        rank: detail.rank,
        mode: detail.mode,
        answeredQuestions: detail.answeredQuestions,
        totalQuestions: detail.totalQuestions,
      }

      // 更新维度分数
      if (detail.dimensionScores) {
        reportData.value.dimensionScores = detail.dimensionScores.map((dim) => ({
          name: dim.dimension,
          score: dim.score,
          fullScore: dim.maxScore,
          description: dim.description,
          icon: getIconForDimension(dim.dimension),
        }))
      }

      // 更新多模态分析
      if (detail.audioMetrics || detail.videoMetrics) {
        reportData.value.multiModalAnalysis = {
          speech: detail.audioMetrics
            ? {
                clarity: detail.audioMetrics.clarity,
                fluency: detail.audioMetrics.fluency,
                emotion: detail.audioMetrics.confidence,
                pace: detail.audioMetrics.pace,
                logic: detail.audioMetrics.overall,
                analysis: detail.audioMetrics.feedback,
                suggestions: [],
              }
            : reportData.value.multiModalAnalysis.speech,
          video: detail.videoMetrics
            ? {
                eyeContact: detail.videoMetrics.eyeContact,
                expression: detail.videoMetrics.expressions,
                gesture: detail.videoMetrics.gestures,
                posture: detail.videoMetrics.posture,
                confidence: detail.videoMetrics.overall,
                analysis: detail.videoMetrics.feedback,
                suggestions: [],
              }
            : reportData.value.multiModalAnalysis.video,
          text: reportData.value.multiModalAnalysis.text,
        }
      }

      console.log('API详细数据加载成功')
    }
  } catch (error) {
    console.error('从API加载详细数据失败:', error)
    throw error
  }
}

/**
 * @description 从历史记录API加载数据
 */
const loadDetailFromHistoryApi = async () => {
  try {
    const response = await getInterviewDetail({
      params: { id: parseInt(pageParams.value.id) },
    })

    if (response.code === 200 && response.data) {
      const detail = response.data

      // 更新基本信息
      reportData.value.basicInfo = {
        jobName: detail.jobName,
        company: detail.company,
        date: detail.date,
        duration: detail.duration,
        totalScore: detail.totalScore,
        rank: getScoreLevel(detail.totalScore),
        mode: detail.mode || 'standard',
        answeredQuestions: detail.answeredQuestions || 0,
        totalQuestions: detail.totalQuestions || 0,
      }

      // 更新问题分析
      if (detail.questions) {
        reportData.value.questionAnalysis = detail.questions.map((q: any) => ({
          id: q.id,
          question: q.question,
          answer: q.answer,
          score: q.score,
          feedback: q.feedback,
          category: '技术问题',
          difficulty: 3,
          timeSpent: 180,
          timeLimit: 180,
          strengths: [],
          weaknesses: [],
          keywordMatches: [],
          idealAnswer: '理想答案示例',
        }))
      }

      console.log('历史记录API数据加载成功')
    }
  } catch (error) {
    console.error('从历史记录API加载数据失败:', error)
    throw error
  }
}

/**
 * @description 根据维度名称获取图标
 */
const getIconForDimension = (dimension: string): string => {
  const iconMap: Record<string, string> = {
    专业知识: 'i-mdi-code-braces',
    表达能力: 'i-mdi-account-voice',
    逻辑思维: 'i-mdi-brain',
    问题解决: 'i-mdi-puzzle',
    沟通能力: 'i-mdi-forum',
    综合素质: 'i-mdi-star',
    technical: 'i-mdi-code-braces',
    communication: 'i-mdi-account-voice',
    problemSolving: 'i-mdi-puzzle',
    teamwork: 'i-mdi-forum',
    leadership: 'i-mdi-star',
    creativity: 'i-mdi-lightbulb',
  }
  return iconMap[dimension] || 'i-mdi-circle'
}

/**
 * @description 根据维度名称和分数生成描述
 * @param name 维度名称
 * @param score 分数
 */
const getDimensionDescription = (name: string, score: number) => {
  if (score >= 90) return `${name}表现优秀，继续保持`
  if (score >= 80) return `${name}表现良好，有提升空间`
  if (score >= 70) return `${name}表现中等，需要重点改进`
  return `${name}需要大幅提升`
}

/**
 * @description 生成语音分析建议
 */
const generateSpeechAnalysis = (speech: any) => {
  const suggestions = []
  if (speech.clarity < 80) suggestions.push('发音清晰度需要改进')
  if (speech.fluency < 80) suggestions.push('语言流畅度有待提升')
  if (speech.emotion < 75) suggestions.push('语音情感表达可以更丰富')
  if (speech.pace < 75) suggestions.push('语速控制需要优化')
  if (speech.logic < 80) suggestions.push('语言逻辑性需要加强')

  return suggestions.length > 0
    ? `语音表现有以下需要改进的地方：${suggestions.join('，')}。`
    : '语音表现整体良好，继续保持。'
}

/**
 * @description 生成语音改进建议
 */
const generateSpeechSuggestions = (speech: any) => {
  const suggestions = []
  if (speech.clarity < 80) suggestions.push('练习发音，注意吐字清晰')
  if (speech.fluency < 80) suggestions.push('多做口语练习，提升表达流畅度')
  if (speech.emotion < 75) suggestions.push('注意语音语调变化，增强感染力')
  if (speech.pace < 75) suggestions.push('控制语速，重点内容可适当放慢')
  if (speech.logic < 80) suggestions.push('按逻辑顺序组织语言，先总后分')

  return suggestions.length > 0 ? suggestions : ['语音表现优秀，继续保持现有水平']
}

/**
 * @description 生成视频分析建议
 */
const generateVideoAnalysis = (video: any) => {
  const suggestions = []
  if (video.eyeContact < 75) suggestions.push('眼神交流不够充分')
  if (video.expression < 85) suggestions.push('面部表情可以更自然')
  if (video.gesture < 75) suggestions.push('肢体语言需要丰富')
  if (video.posture < 80) suggestions.push('坐姿仪态需要改进')
  if (video.confidence < 80) suggestions.push('自信程度有待提升')

  return suggestions.length > 0
    ? `视频表现有以下需要改进的地方：${suggestions.join('，')}。`
    : '视频表现整体优秀，非常符合面试要求。'
}

/**
 * @description 生成视频改进建议
 */
const generateVideoSuggestions = (video: any) => {
  const suggestions = []
  if (video.eyeContact < 75) suggestions.push('保持与面试官的眼神交流')
  if (video.expression < 85) suggestions.push('放松心情，保持自然表情')
  if (video.gesture < 75) suggestions.push('适当使用手势辅助表达')
  if (video.posture < 80) suggestions.push('保持端正坐姿，体现专业形象')
  if (video.confidence < 80) suggestions.push('增强自信，展现积极态度')

  return suggestions.length > 0 ? suggestions : ['视频表现出色，继续保持专业形象']
}

/**
 * @description 生成文本分析建议
 */
const generateTextAnalysis = (text: any) => {
  const suggestions = []
  if (text.structure < 75) suggestions.push('回答结构需要优化')
  if (text.relevance < 80) suggestions.push('内容相关性有待提升')
  if (text.depth < 75) suggestions.push('回答深度不够')
  if (text.keywords < 75) suggestions.push('技术关键词使用较少')
  if (text.grammar < 90) suggestions.push('语法表达需要改进')

  return suggestions.length > 0
    ? `文本回答有以下需要改进的地方：${suggestions.join('，')}。`
    : '文本回答质量很高，逻辑清晰，内容充实。'
}

/**
 * @description 生成文本改进建议
 */
const generateTextSuggestions = (text: any) => {
  const suggestions = []
  if (text.structure < 75) suggestions.push('使用STAR法则组织回答结构')
  if (text.relevance < 80) suggestions.push('确保回答紧扣问题核心')
  if (text.depth < 75) suggestions.push('增加具体案例和技术细节')
  if (text.keywords < 75) suggestions.push('使用更多相关技术术语')
  if (text.grammar < 90) suggestions.push('注意语法和表达的准确性')

  return suggestions.length > 0 ? suggestions : ['文本表达优秀，继续保持高质量回答']
}

/**
 * @description 更新学习资源推荐
 */
const updateLearningResources = (record: any) => {
  // 基于用户的薄弱环节推荐学习资源
  const weakAreas = []

  if (record.coreAbilities?.professionalKnowledge < 80) {
    weakAreas.push('专业知识')
  }
  if (record.coreAbilities?.communicationSkill < 80) {
    weakAreas.push('表达能力')
  }
  if (record.coreAbilities?.logicalThinking < 80) {
    weakAreas.push('逻辑思维')
  }

  // 根据薄弱环节调整推荐内容
  console.log('基于薄弱环节推荐学习资源:', weakAreas)
}

/**
 * @description 更新个人提升计划
 */
const updateImprovementPlan = (record: any) => {
  // 基于当前能力水平生成提升计划
  if (record.coreAbilities) {
    reportData.value.improvementPlan.forEach((plan) => {
      switch (plan.area) {
        case '技术深度':
          plan.currentLevel = record.coreAbilities.professionalKnowledge || 75
          break
        case '表达能力':
          plan.currentLevel = record.coreAbilities.communicationSkill || 80
          break
        case '项目经验':
          plan.currentLevel = record.coreAbilities.skillMatching || 82
          break
      }
    })
  }
}

/**
 * @description 页面加载时获取参数
 */
onLoad((options) => {
  pageParams.value = {
    id: options.id || '',
    sessionId: options.sessionId || '',
  }
})

/**
 * @description 页面挂载时加载数据
 */
onMounted(() => {
  loadInterviewDetail()
})

// 选项卡列表
const tabs = ref([
  { id: 'overview', name: '综合概览', icon: 'i-mdi-chart-donut' },
  { id: 'multimodal', name: '多模态分析', icon: 'i-mdi-eye' },
  { id: 'questions', name: '题目解析', icon: 'i-mdi-help-circle' },
  { id: 'improvement', name: '提升计划', icon: 'i-mdi-trending-up' },
])

/**
 * @description 获取分数颜色
 * @param score 分数
 */
const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#00C9A7'
  if (score >= 70) return '#faad14'
  if (score >= 60) return '#fa8c16'
  return '#f5222d'
}

/**
 * @description 获取等级标签
 * @param score 分数
 */
const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '中等'
  if (score >= 60) return '及格'
  return '不及格'
}

/**
 * @description 切换选项卡
 * @param tabId 选项卡ID
 */
const changeTab = (tabId: string) => {
  activeTab.value = tabId
}

/**
 * @description 开始学习资源
 * @param resource 学习资源
 */
const startLearning = (resource: any) => {
  uni.navigateTo({
    url: resource.url,
  })
}

/**
 * @description 分享报告
 */
const shareReport = () => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到QQ', '复制链接', '导出PDF'],
    success: (res) => {
      const options = ['微信', 'QQ', '复制链接', 'PDF']
      uni.showToast({
        title: `已选择${options[res.tapIndex]}`,
        icon: 'success',
      })
    },
  })
}

/**
 * @description 重新面试
 */
const retryInterview = () => {
  uni.showModal({
    title: '重新面试',
    content: '确定要重新进行此岗位的面试吗？',
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: '/pages/interview/select',
        })
      }
    },
  })
}

/**
 * @description 返回结果页
 */
const goBack = () => {
  uni.navigateBack()
}

/**
 * @description 加载详细报告数据
 */
const loadReportData = () => {
  // 获取报告ID并加载详细数据
  console.log('加载详细面试报告')
}

// 页面初始化
loadReportData()
</script>

<template>
  <view class="detail-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner">
        <view class="spinner"></view>
        <text class="loading-text">正在加载面试报告...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-else class="main-content">
      <!-- 顶部导航 -->
      <view class="nav-header">
        <button class="nav-back-btn" @click="goBack">
          <text class="i-mdi-chevron-left"></text>
        </button>
        <text class="nav-title">面试详情报告</text>
        <button class="nav-share-btn" @click="shareReport">
          <text class="i-mdi-share"></text>
        </button>
      </view>

      <!-- 基本信息卡片 -->
      <view class="basic-info-card">
        <view class="info-header">
          <view class="job-info">
            <text class="job-name">{{ reportData.basicInfo.jobName }}</text>
            <view class="company-info">
              <text class="i-mdi-office-building company-icon"></text>
              <text class="company-name">{{ reportData.basicInfo.company }}</text>
            </view>
            <view class="interview-meta">
              <text class="meta-item">
                <text class="i-mdi-clock-outline"></text>
                <text>{{ reportData.basicInfo.date }}</text>
              </text>
              <text class="meta-item">
                <text class="i-mdi-timer"></text>
                <text>用时 {{ reportData.basicInfo.duration }}</text>
              </text>
            </view>
          </view>

          <view class="score-section">
            <view class="score-circle">
              <text class="score-value">{{ reportData.basicInfo.totalScore }}</text>
            </view>
            <view
              class="score-rank"
              :style="{ color: getScoreColor(reportData.basicInfo.totalScore) }"
            >
              {{ reportData.basicInfo.rank }}
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="action-btn secondary-btn" @click="shareReport">
            <text class="i-mdi-share btn-icon"></text>
            <text>分享报告</text>
          </button>
          <button class="action-btn primary-btn" @click="retryInterview">
            <text class="i-mdi-refresh btn-icon"></text>
            <text>重新面试</text>
          </button>
        </view>
      </view>

      <!-- 选项卡导航 -->
      <scroll-view class="tab-scroll" scroll-x :show-scrollbar="false">
        <view class="tab-list">
          <view
            class="tab-item"
            :class="{ active: activeTab === item.id }"
            v-for="item in tabs"
            :key="item.id"
            @click="changeTab(item.id)"
          >
            <text :class="item.icon" class="tab-icon"></text>
            <text class="tab-name">{{ item.name }}</text>
          </view>
        </view>
      </scroll-view>

      <!-- 选项卡内容 -->
      <view class="tab-content">
        <!-- 综合概览 -->
        <view class="overview-content" v-if="activeTab === 'overview'">
          <view class="section-card">
            <view class="section-header">
              <text class="i-mdi-chart-box section-icon"></text>
              <text class="section-title">能力维度评估</text>
            </view>
            <view class="dimensions-grid">
              <view
                class="dimension-item"
                v-for="dimension in reportData.dimensionScores"
                :key="dimension.name"
              >
                <view class="dimension-header">
                  <view class="dimension-left">
                    <text :class="dimension.icon" class="dimension-icon"></text>
                    <text class="dimension-name">{{ dimension.name }}</text>
                  </view>
                  <view class="dimension-score">
                    <text class="score-text" :style="{ color: getScoreColor(dimension.score) }">
                      {{ dimension.score }}
                    </text>
                    <text class="score-max">/{{ dimension.fullScore }}</text>
                  </view>
                </view>
                <view class="dimension-bar">
                  <view
                    class="dimension-progress"
                    :style="{
                      width: (dimension.score / dimension.fullScore) * 100 + '%',
                      backgroundColor: getScoreColor(dimension.score),
                    }"
                  ></view>
                </view>
                <text class="dimension-desc">{{ dimension.description }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 多模态分析 -->
        <view class="multimodal-content" v-if="activeTab === 'multimodal'">
          <!-- 语音分析 -->
          <view class="section-card">
            <view class="section-header">
              <text class="i-mdi-microphone section-icon"></text>
              <text class="section-title">语音分析</text>
            </view>
            <view class="metric-grid">
              <view class="metric-item">
                <text class="metric-label">清晰度</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.speech.clarity) }"
                >
                  {{ reportData.multiModalAnalysis.speech.clarity }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">语速</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.speech.pace) }"
                >
                  {{ reportData.multiModalAnalysis.speech.pace }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">情感</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.speech.emotion) }"
                >
                  {{ reportData.multiModalAnalysis.speech.emotion }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">流畅度</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.speech.fluency) }"
                >
                  {{ reportData.multiModalAnalysis.speech.fluency }}%
                </text>
              </view>
            </view>
            <view class="analysis-text">
              <text class="i-mdi-information analysis-icon"></text>
              <text class="analysis-content">
                {{ reportData.multiModalAnalysis.speech.analysis }}
              </text>
            </view>
          </view>

          <!-- 视频分析 -->
          <view class="section-card">
            <view class="section-header">
              <text class="i-mdi-video section-icon"></text>
              <text class="section-title">视频分析</text>
            </view>
            <view class="metric-grid">
              <view class="metric-item">
                <text class="metric-label">眼神交流</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.video.eyeContact) }"
                >
                  {{ reportData.multiModalAnalysis.video.eyeContact }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">表情自然</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.video.expression) }"
                >
                  {{ reportData.multiModalAnalysis.video.expression }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">姿态得体</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.video.posture) }"
                >
                  {{ reportData.multiModalAnalysis.video.posture }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">手势表达</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.video.gesture) }"
                >
                  {{ reportData.multiModalAnalysis.video.gesture }}%
                </text>
              </view>
            </view>
            <view class="analysis-text">
              <text class="i-mdi-information analysis-icon"></text>
              <text class="analysis-content">
                {{ reportData.multiModalAnalysis.video.analysis }}
              </text>
            </view>
          </view>

          <!-- 内容分析 -->
          <view class="section-card">
            <view class="section-header">
              <text class="i-mdi-file-document section-icon"></text>
              <text class="section-title">内容分析</text>
            </view>
            <view class="metric-grid">
              <view class="metric-item">
                <text class="metric-label">相关性</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.text.relevance) }"
                >
                  {{ reportData.multiModalAnalysis.text.relevance }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">深度</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.text.depth) }"
                >
                  {{ reportData.multiModalAnalysis.text.depth }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">结构</text>
                <text
                  class="metric-value"
                  :style="{ color: getScoreColor(reportData.multiModalAnalysis.text.structure) }"
                >
                  {{ reportData.multiModalAnalysis.text.structure }}%
                </text>
              </view>
              <view class="metric-item">
                <text class="metric-label">语法准确</text>
                <text
                  class="metric-value"
                  :style="{
                    color: getScoreColor(reportData.multiModalAnalysis.text.grammar),
                  }"
                >
                  {{ reportData.multiModalAnalysis.text.grammar }}%
                </text>
              </view>
            </view>
            <view class="analysis-text">
              <text class="i-mdi-information analysis-icon"></text>
              <text class="analysis-content">
                {{ reportData.multiModalAnalysis.text.analysis }}
              </text>
            </view>
          </view>
        </view>

        <!-- 题目解析 -->
        <view class="questions-content" v-if="activeTab === 'questions'">
          <view
            class="question-card"
            v-for="(question, index) in reportData.questionAnalysis"
            :key="question.id"
          >
            <view class="question-header">
              <view class="question-number">
                <text class="i-mdi-numeric-{{ index + 1 }}-circle number-icon"></text>
                <text class="number-text">第{{ index + 1 }}题</text>
              </view>
              <text class="question-score" :style="{ color: getScoreColor(question.score) }">
                {{ question.score }}分
              </text>
            </view>

            <view class="question-content">
              <text class="i-mdi-help-circle question-icon"></text>
              <text class="question-text">{{ question.question }}</text>
            </view>

            <view class="answer-section">
              <text class="answer-title">您的回答</text>
              <text class="answer-text">{{ question.userAnswer }}</text>
            </view>

            <view class="scores-breakdown">
              <view class="score-item">
                <text class="i-mdi-microphone score-icon"></text>
                <text class="score-label">语音</text>
                <text class="score-value">{{ question.audioScore }}</text>
              </view>
              <view class="score-item">
                <text class="i-mdi-video score-icon"></text>
                <text class="score-label">视频</text>
                <text class="score-value">{{ question.videoScore }}</text>
              </view>
              <view class="score-item">
                <text class="i-mdi-file-document score-icon"></text>
                <text class="score-label">内容</text>
                <text class="score-value">{{ question.contentScore }}</text>
              </view>
            </view>

            <view class="feedback-section">
              <view class="feedback-group">
                <text class="group-title">
                  <text class="i-mdi-thumb-up group-icon"></text>
                  <text>优势</text>
                </text>
                <view class="feedback-list">
                  <text
                    class="feedback-item"
                    v-for="strength in question.strengths"
                    :key="strength"
                  >
                    • {{ strength }}
                  </text>
                </view>
              </view>

              <view class="feedback-group">
                <text class="group-title">
                  <text class="i-mdi-alert-circle group-icon"></text>
                  <text>改进点</text>
                </text>
                <view class="feedback-list">
                  <text
                    class="feedback-item"
                    v-for="improvement in question.improvements"
                    :key="improvement"
                  >
                    • {{ improvement }}
                  </text>
                </view>
              </view>

              <view class="feedback-group">
                <text class="group-title">
                  <text class="i-mdi-lightbulb group-icon"></text>
                  <text>建议</text>
                </text>
                <view class="feedback-list">
                  <text class="feedback-item">• {{ question.suggestedAnswer }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 提升计划 -->
        <view class="improvement-content" v-if="activeTab === 'improvement'">
          <view class="section-card">
            <view class="section-header">
              <text class="i-mdi-target section-icon"></text>
              <text class="section-title">优先提升项</text>
            </view>
            <view class="priority-item" v-for="item in reportData.improvementPlan" :key="item.area">
              <view class="priority-header">
                <text class="priority-area">{{ item.area }}</text>
                <view class="priority-progress">
                  <text class="current-score">{{ item.currentLevel }}</text>
                  <text class="i-mdi-arrow-right arrow"></text>
                  <text class="target-score">{{ item.targetLevel }}</text>
                </view>
              </view>

              <view class="methods-list">
                <text class="methods-title">
                  <text class="i-mdi-lightbulb"></text>
                  <text>改进方法：</text>
                </text>
                <view class="method-items">
                  <text class="method-item" v-for="method in item.actions" :key="method">
                    • {{ method }}
                  </text>
                </view>
              </view>

              <text class="timeline">
                <text class="i-mdi-clock-outline"></text>
                <text>预计时间：{{ item.timeframe }}</text>
              </text>
            </view>
          </view>

          <view class="section-card">
            <view class="section-header">
              <text class="i-mdi-school section-icon"></text>
              <text class="section-title">推荐学习资源</text>
            </view>
            <view class="resource-list">
              <view
                class="resource-item"
                v-for="resource in reportData.learningResources"
                :key="resource.id"
                @click="startLearning(resource)"
              >
                <view class="resource-icon">
                  <text :class="resource.icon"></text>
                </view>
                <view class="resource-content">
                  <text class="resource-title">{{ resource.title }}</text>
                </view>
                <text class="i-mdi-chevron-right resource-arrow"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;

    .spinner {
      width: 80rpx;
      height: 80rpx;
      border: 6rpx solid rgba(0, 201, 167, 0.2);
      border-top: 6rpx solid #00c9a7;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
      text-align: center;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.main-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 顶部导航
.nav-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 50rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);

  &::after {
    position: absolute;
    right: 0;
    bottom: -20rpx;
    left: 0;
    height: 20rpx;
    content: '';
    background: inherit;
    border-radius: 0 0 50% 50% / 0 0 100% 100%;
  }

  .nav-back-btn,
  .nav-share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90rpx;
    height: 90rpx;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10rpx);
    border: none;
    border-radius: 50%;
    transition: all 0.2s ease;

    text {
      font-size: 32rpx;
      color: #fff;
    }

    &:active {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0.95);
    }
  }

  .nav-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
    flex: 1;
    margin: 0 20rpx;
  }
}

// 基本信息卡片
.basic-info-card {
  padding: 30rpx;
  margin: 15rpx 20rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  .info-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 30rpx;

    // 响应式布局优化
    @media (max-width: 750rpx) {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 24rpx;
    }

    .job-info {
      flex: 1;
      min-width: 0; // 防止flex项目溢出

      .job-name {
        display: block;
        margin-bottom: 12rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        line-height: 1.3;
        word-break: break-word;
      }

      .company-info {
        display: flex;
        gap: 8rpx;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 16rpx;

        @media (max-width: 750rpx) {
          justify-content: center;
        }

        .company-icon {
          font-size: 20rpx;
          color: #999;
        }

        .company-name {
          font-size: 24rpx;
          color: #666;
        }
      }

      .interview-meta {
        display: flex;
        gap: 20rpx;
        flex-wrap: wrap;

        @media (max-width: 750rpx) {
          justify-content: center;
        }

        .meta-item {
          display: flex;
          gap: 6rpx;
          align-items: center;
          font-size: 20rpx;
          color: #999;

          text:first-child {
            font-size: 16rpx;
          }
        }
      }
    }

    .score-section {
      text-align: center;
      flex-shrink: 0;

      .score-circle {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 140rpx;
        height: 140rpx;
        margin: 0 auto 12rpx;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        border-radius: 50%;
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);

        .score-value {
          font-size: 48rpx;
          font-weight: bold;
          line-height: 1;
          color: #fff;
        }

        .score-max {
          position: absolute;
          top: 35rpx;
          right: 20rpx;
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.8);
        }

        .score-label {
          margin-top: 4rpx;
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .score-rank {
        font-size: 26rpx;
        font-weight: bold;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 16rpx;

    .action-btn {
      display: flex;
      flex: 1;
      gap: 8rpx;
      align-items: center;
      justify-content: center;
      min-height: 80rpx;
      padding: 0 20rpx;
      font-size: 26rpx;
      font-weight: 500;
      border: none;
      border-radius: 40rpx;
      transition: all 0.3s ease;
      white-space: nowrap;

      .btn-icon {
        font-size: 28rpx;
        flex-shrink: 0;
      }

      &:active {
        transform: scale(0.98);
      }

      &.primary-btn {
        color: #fff;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);

        &:hover {
          box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.4);
        }
      }

      &.secondary-btn {
        color: #666;
        background: #fff;
        border: 1rpx solid #e9ecef;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

        &:active {
          background: #f8f9fa;
        }

        &:hover {
          border-color: #00c9a7;
          color: #00c9a7;
        }
      }
    }
  }
}

// 选项卡导航
.tab-scroll {
  padding: 20rpx 0;

  .tab-list {
    display: inline-flex;
    gap: 16rpx;
    padding: 0 20rpx;

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 120rpx;
      padding: 16rpx 20rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 20rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10rpx);
      border: 1rpx solid rgba(255, 255, 255, 0.2);

      &.active {
        color: #fff;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
        transform: translateY(-2rpx);
        border-color: transparent;
      }

      &:not(.active):hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-1rpx);
      }

      .tab-icon {
        margin-bottom: 8rpx;
        font-size: 32rpx;
        transition: transform 0.3s ease;
      }

      .tab-name {
        font-size: 22rpx;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
      }

      &.active .tab-icon {
        transform: scale(1.1);
      }
    }
  }
}

// 选项卡内容
.tab-content {
  padding: 0 20rpx;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用卡片样式
.section-card {
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
    transform: translateY(-2rpx);
  }
}

.section-header {
  display: flex;
  gap: 12rpx;
  align-items: center;
  margin-bottom: 24rpx;

  .section-icon {
    font-size: 32rpx;
    color: #00c9a7;
    flex-shrink: 0;
  }

  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    flex: 1;
  }
}

// 能力维度网格
.dimensions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;

  // 响应式布局
  @media (max-width: 600rpx) {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .dimension-item {
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    transition: all 0.3s ease;
    border: 1rpx solid transparent;

    &:hover {
      background: #f1f3f4;
      border-color: #00c9a7;
      transform: translateY(-2rpx);
    }

    .dimension-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .dimension-left {
        display: flex;
        gap: 8rpx;
        align-items: center;
        flex: 1;
        min-width: 0;

        .dimension-icon {
          font-size: 24rpx;
          color: #00c9a7;
          flex-shrink: 0;
        }

        .dimension-name {
          font-size: 24rpx;
          font-weight: 500;
          color: #333;
          word-break: break-word;
        }
      }

      .dimension-score {
        display: flex;
        gap: 4rpx;
        align-items: baseline;
        flex-shrink: 0;

        .score-text {
          font-size: 32rpx;
          font-weight: bold;
        }

        .score-max {
          font-size: 20rpx;
          color: #999;
        }
      }
    }

    .dimension-bar {
      height: 8rpx;
      margin-bottom: 12rpx;
      overflow: hidden;
      background: #e9ecef;
      border-radius: 4rpx;

      .dimension-progress {
        height: 100%;
        border-radius: 4rpx;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          width: 20rpx;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
          border-radius: 0 4rpx 4rpx 0;
        }
      }
    }

    .dimension-desc {
      font-size: 22rpx;
      line-height: 1.5;
      color: #666;
    }
  }
}

// 指标网格
.metric-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;

  // 响应式布局
  @media (max-width: 500rpx) {
    grid-template-columns: 1fr;
  }

  .metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    transition: all 0.3s ease;
    border: 1rpx solid transparent;

    &:hover {
      background: #f1f3f4;
      border-color: #00c9a7;
      transform: translateY(-2rpx);
    }

    .metric-label {
      margin-bottom: 8rpx;
      font-size: 22rpx;
      color: #666;
      text-align: center;
    }

    .metric-value {
      font-size: 28rpx;
      font-weight: bold;
      text-align: center;
    }
  }
}

// 分析文本
.analysis-text {
  display: flex;
  gap: 12rpx;
  align-items: flex-start;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #00c9a7;

  .analysis-icon {
    margin-top: 2rpx;
    font-size: 24rpx;
    color: #00c9a7;
    flex-shrink: 0;
  }

  .analysis-content {
    flex: 1;
    font-size: 24rpx;
    line-height: 1.6;
    color: #666;
    word-break: break-word;
  }
}

// 题目卡片
.question-card {
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
    transform: translateY(-2rpx);
  }

  .question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    flex-wrap: wrap;
    gap: 12rpx;

    .question-number {
      display: flex;
      gap: 8rpx;
      align-items: center;

      .number-icon {
        font-size: 32rpx;
        color: #00c9a7;
      }

      .number-text {
        font-size: 26rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .question-score {
      font-size: 32rpx;
      font-weight: bold;
      padding: 8rpx 16rpx;
      background: rgba(0, 201, 167, 0.1);
      border-radius: 20rpx;
      border: 1rpx solid rgba(0, 201, 167, 0.2);
    }
  }

  .question-content {
    display: flex;
    gap: 12rpx;
    align-items: flex-start;
    padding: 20rpx;
    margin-bottom: 24rpx;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 12rpx;
    border-left: 4rpx solid #7c3aed;

    .question-icon {
      margin-top: 2rpx;
      font-size: 24rpx;
      color: #7c3aed;
      flex-shrink: 0;
    }

    .question-text {
      flex: 1;
      font-size: 26rpx;
      line-height: 1.6;
      color: #333;
      word-break: break-word;
    }
  }

  .answer-section {
    margin-bottom: 24rpx;

    .answer-title {
      display: block;
      margin-bottom: 12rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: #333;
    }

    .answer-text {
      display: block;
      padding: 20rpx;
      font-size: 22rpx;
      line-height: 1.6;
      color: #666;
      background: #f8f9fa;
      border-radius: 12rpx;
      border-left: 4rpx solid #00c9a7;
      word-break: break-word;
    }
  }

  .scores-breakdown {
    display: flex;
    justify-content: space-around;
    padding: 20rpx;
    margin-bottom: 24rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-radius: 16rpx;
    border: 1rpx solid #e9ecef;

    // 响应式布局
    @media (max-width: 500rpx) {
      flex-direction: column;
      gap: 16rpx;
    }

    .score-item {
      display: flex;
      flex-direction: column;
      gap: 8rpx;
      align-items: center;
      padding: 12rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 201, 167, 0.1);
        transform: translateY(-2rpx);
      }

      .score-icon {
        font-size: 28rpx;
        color: #00c9a7;
      }

      .score-label {
        font-size: 20rpx;
        color: #666;
        text-align: center;
      }

      .score-value {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }

  .feedback-section {
    .feedback-group {
      margin-bottom: 20rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border-left: 4rpx solid transparent;

      &:last-child {
        margin-bottom: 0;
      }

      &:nth-child(1) {
        border-left-color: #52c41a;
        background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
      }

      &:nth-child(2) {
        border-left-color: #fa8c16;
        background: linear-gradient(135deg, #fff7e6 0%, #fef3e2 100%);
      }

      &:nth-child(3) {
        border-left-color: #00c9a7;
        background: linear-gradient(135deg, #e6fffb 0%, #f0fdfa 100%);
      }

      .group-title {
        display: flex;
        gap: 8rpx;
        align-items: center;
        margin-bottom: 12rpx;
        font-size: 24rpx;
        font-weight: 500;
        color: #333;

        .group-icon {
          font-size: 22rpx;
          flex-shrink: 0;

          &.i-mdi-thumb-up {
            color: #52c41a;
          }

          &.i-mdi-alert-circle {
            color: #fa8c16;
          }

          &.i-mdi-lightbulb {
            color: #00c9a7;
          }
        }
      }

      .feedback-list {
        .feedback-item {
          display: block;
          margin-bottom: 8rpx;
          padding: 8rpx 0;
          font-size: 22rpx;
          line-height: 1.6;
          color: #666;
          word-break: break-word;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 提升计划
.priority-item {
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    border-color: #00c9a7;
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.1);
  }

  .priority-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    flex-wrap: wrap;
    gap: 12rpx;

    .priority-area {
      font-size: 26rpx;
      font-weight: bold;
      color: #333;
      flex: 1;
      min-width: 0;
    }

    .priority-progress {
      display: flex;
      gap: 8rpx;
      align-items: center;
      padding: 8rpx 16rpx;
      background: rgba(0, 201, 167, 0.1);
      border-radius: 20rpx;
      border: 1rpx solid rgba(0, 201, 167, 0.2);

      .current-score,
      .target-score {
        font-size: 24rpx;
        font-weight: bold;
        color: #00c9a7;
      }

      .arrow {
        font-size: 20rpx;
        color: #999;
      }
    }
  }

  .methods-list {
    margin-bottom: 16rpx;

    .methods-title {
      display: flex;
      gap: 6rpx;
      align-items: center;
      margin-bottom: 12rpx;
      font-size: 22rpx;
      font-weight: 500;
      color: #333;

      text:first-child {
        font-size: 20rpx;
        color: #00c9a7;
        flex-shrink: 0;
      }
    }

    .method-items {
      padding-left: 26rpx;

      .method-item {
        display: block;
        margin-bottom: 6rpx;
        font-size: 20rpx;
        line-height: 1.6;
        color: #666;
        word-break: break-word;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: -16rpx;
          top: 12rpx;
          width: 6rpx;
          height: 6rpx;
          background: #00c9a7;
          border-radius: 50%;
        }
      }
    }
  }

  .timeline {
    display: flex;
    gap: 6rpx;
    align-items: center;
    font-size: 20rpx;
    color: #999;
    padding: 8rpx 12rpx;
    background: rgba(250, 140, 22, 0.1);
    border-radius: 12rpx;
    border: 1rpx solid rgba(250, 140, 22, 0.2);

    text:first-child {
      font-size: 18rpx;
      color: #fa8c16;
      flex-shrink: 0;
    }
  }
}

// 学习资源列表
.resource-list {
  .resource-item {
    display: flex;
    align-items: center;
    padding: 24rpx;
    margin-bottom: 16rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-radius: 16rpx;
    border: 1rpx solid #e9ecef;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      background: #e9ecef;
      transform: scale(0.98);
    }

    &:hover {
      border-color: #00c9a7;
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.1);
    }

    .resource-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
      background: linear-gradient(135deg, #00c9a7, #4fd1c7);
      border-radius: 50%;
      flex-shrink: 0;
      box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.3);

      text {
        font-size: 28rpx;
        color: #fff;
      }
    }

    .resource-content {
      flex: 1;
      min-width: 0;

      .resource-title {
        font-size: 26rpx;
        font-weight: 500;
        color: #333;
        word-break: break-word;
        line-height: 1.4;
      }
    }

    .resource-arrow {
      font-size: 24rpx;
      color: #ccc;
      flex-shrink: 0;
      transition: all 0.3s ease;
    }

    &:hover .resource-arrow {
      color: #00c9a7;
      transform: translateX(4rpx);
    }
  }
}

// 响应式优化
@media (max-width: 750rpx) {
  .basic-info-card {
    margin: 15rpx 15rpx;
    padding: 24rpx;
  }

  .tab-content {
    padding: 0 15rpx;
  }

  .section-card {
    padding: 24rpx;
  }

  .question-card {
    padding: 24rpx;
  }
}

@media (max-width: 500rpx) {
  .nav-header {
    padding: 50rpx 20rpx 30rpx;
  }

  .basic-info-card {
    margin: 15rpx 10rpx;
    padding: 20rpx;
  }

  .tab-content {
    padding: 0 10rpx;
  }

  .section-card {
    padding: 20rpx;
  }

  .question-card {
    padding: 20rpx;
  }
}
</style>
