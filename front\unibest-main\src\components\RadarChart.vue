<script setup lang="ts">
import { ref, watch, computed, defineProps, onUnmounted } from 'vue'
import uCharts from '@qiun/ucharts'
import { onLoad } from '@dcloudio/uni-app'

/**
 * @description 多维度能力雷达图组件
 * @prop abilityData 能力数据数组
 * @prop width 宽度
 * @prop height 高度
 * <AUTHOR>
 */
interface AbilityDataItem {
  name: string
  current: number
  previous: number
  max: number
  color: string
}

interface Props {
  abilityData?: AbilityDataItem[]
  width?: number | string
  height?: number | string
  showComparison?: boolean
}

const props = defineProps<Props>()

// 生成唯一的canvas ID
const canvasId = ref(`radar-chart-${Date.now()}-${Math.floor(Math.random() * 1000)}`)

// uCharts 实例
let radarChart = null

// 安全的能力数据
const safeAbilityData = computed(() => {
  return props.abilityData || []
})

// 计算实际的宽高（统一使用px单位）
const actualWidth = computed(() => {
  const w = props.width || 600
  if (typeof w === 'number') {
    return uni.upx2px(w)
  }
  const value = parseInt(w.toString())
  if (w.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || uni.upx2px(600)
})

const actualHeight = computed(() => {
  const h = props.height || 600
  if (typeof h === 'number') {
    return uni.upx2px(h)
  }
  const value = parseInt(h.toString())
  if (h.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || uni.upx2px(600)
})

/**
 * @description 获取当前组件实例
 */
function getCurrentInstance() {
  // #ifdef MP-WEIXIN
  return getCurrentPages()[getCurrentPages().length - 1]
  // #endif
}

/**
 * @description 绘制雷达图
 */
const drawRadarChart = () => {
  if (!safeAbilityData.value || safeAbilityData.value.length === 0) {
    console.warn('能力数据为空，无法绘制雷达图')
    return
  }

  // 获取canvas绘图上下文
  const context = uni.createCanvasContext(canvasId.value, getCurrentInstance())
  if (!context) {
    console.error('无法创建Canvas上下文')
    return
  }

  // 准备类别数据（能力名称）
  const categories = safeAbilityData.value.map((item) => item.name)

  // 准备系列数据
  const seriesData = []

  // 当前能力数据
  const currentData = safeAbilityData.value.map((item) => item.current)
  seriesData.push({
    name: '当前能力',
    data: currentData,
    color: '#00c9a7',
    lineWidth: 3,
    areaOpacity: 0.3,
  })

  // 如果需要显示对比，添加历史数据
  if (props.showComparison || false) {
    const previousData = safeAbilityData.value.map((item) => item.previous)
    seriesData.push({
      name: '上次记录',
      data: previousData,
      color: '#94a3b8',
      lineWidth: 2,
      areaOpacity: 0.1,
    })
  }

  // 配置图表选项
  const chartOptions = {
    type: 'radar',
    context: context,
    canvasId: canvasId.value,
    width: actualWidth.value,
    height: actualHeight.value,
    // #ifdef MP-WEIXIN
    $this: getCurrentInstance(),
    // #endif
    animation: true,
    fontSize: uni.upx2px(22),
    fontColor: '#334155',
    background: 'transparent',
    pixelRatio: 1,
    dataLabel: true,
    legend: {
      show: props.showComparison || false,
      position: 'bottom',
      fontSize: uni.upx2px(20),
      fontColor: '#475569',
      itemGap: uni.upx2px(20),
      margin: uni.upx2px(10),
    },
    categories: categories,
    series: seriesData,
    extra: {
      radar: {
        gridColor: 'rgba(71, 85, 105, 0.2)',
        gridCount: 5,
        gridType: 'radar',
        max: 100,
        labelColor: '#475569',
        labelShow: true,
        fontSize: uni.upx2px(20),
        border: true,
        borderWidth: 1,
        borderColor: 'rgba(71, 85, 105, 0.3)',
      },
      tooltip: {
        showBox: true,
        bgColor: 'rgba(51, 65, 85, 0.95)',
        bgOpacity: 0.95,
        fontColor: '#ffffff',
        fontSize: uni.upx2px(22),
        borderRadius: 8,
        borderColor: 'rgba(0, 201, 167, 0.3)',
        borderWidth: 1,
        padding: [6, 10],
      },
    },
  }

  // 如果已存在图表实例，先销毁
  if (radarChart) {
    radarChart = null
  }

  // 创建新的图表实例
  try {
    radarChart = new uCharts(chartOptions)
  } catch (error) {
    console.error('创建uCharts雷达图失败:', error)
  }
}

// 暴露更新方法
const updateChart = () => {
  if (safeAbilityData.value && safeAbilityData.value.length > 0) {
    drawRadarChart()
  }
}

// 监听数据变化
watch(
  () => [safeAbilityData.value, props.showComparison || false],
  () => {
    setTimeout(() => {
      drawRadarChart()
    }, 100)
  },
  { deep: true },
)

// 组件挂载后初始化
onLoad(() => {
  setTimeout(() => {
    if (safeAbilityData.value && safeAbilityData.value.length > 0) {
      drawRadarChart()
    }
  }, 300)
})

// 组件卸载时清理
onUnmounted(() => {
  if (radarChart) {
    radarChart = null
  }
})

// 导出方法供父组件调用
defineExpose({
  updateChart,
})
</script>

<template>
  <view class="radar-chart">
    <view v-if="safeAbilityData.length > 0" class="chart-container">
      <!-- 图表画布 -->
      <view class="canvas-wrapper">
        <canvas
          :canvas-id="canvasId"
          :id="canvasId"
          :style="{
            width: actualWidth + 'px',
            height: actualHeight + 'px',
          }"
          class="radar-chart-canvas"
          disable-scroll="true"
        />
      </view>

      <!-- 能力数值显示 -->
      <view class="ability-values" v-if="!(showComparison || false)">
        <view class="ability-value-item" v-for="ability in safeAbilityData" :key="ability.name">
          <view class="value-indicator" :style="{ backgroundColor: ability.color }"></view>
          <text class="value-name">{{ ability.name }}</text>
          <text class="value-score">{{ ability.current }}分</text>
        </view>
      </view>

      <!-- 对比数据显示 -->
      <view class="comparison-values" v-else>
        <view class="comparison-header">
          <view class="legend-item">
            <view class="legend-color current"></view>
            <text class="legend-text">当前能力</text>
          </view>
          <view class="legend-item">
            <view class="legend-color previous"></view>
            <text class="legend-text">上次记录</text>
          </view>
        </view>

        <view class="comparison-list">
          <view class="comparison-item" v-for="ability in safeAbilityData" :key="ability.name">
            <text class="comparison-name">{{ ability.name }}</text>
            <view class="comparison-scores">
              <view class="score-group">
                <text class="score-value current">{{ ability.current }}</text>
                <text class="score-label">当前</text>
              </view>
              <view class="score-divider"></view>
              <view class="score-group">
                <text class="score-value previous">{{ ability.previous }}</text>
                <text class="score-label">之前</text>
              </view>
              <view class="score-change" :class="{ positive: ability.current > ability.previous }">
                <text class="change-value">
                  {{ ability.current > ability.previous ? '+' : ''
                  }}{{ ability.current - ability.previous }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <view class="empty-icon-wrapper">
        <text class="empty-icon i-fa-solid-crosshairs"></text>
        <view class="empty-decoration"></view>
      </view>
      <text class="empty-title">暂无能力数据</text>
      <text class="empty-desc">完成面试后将生成您的多维度能力雷达图</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.radar-chart {
  width: 100%;
  min-height: 400rpx;
  position: relative;
}

// 图表容器
.chart-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

// 画布包装器
.canvas-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.radar-chart-canvas {
  display: block;
  border-radius: 16rpx;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 能力数值显示
.ability-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(248, 250, 252, 0.7) 100%);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.08);
}

.ability-value-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.95);
  }
}

.value-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.value-name {
  font-size: 24rpx;
  color: #334155;
  flex: 1;
}

.value-score {
  font-size: 26rpx;
  font-weight: 600;
  color: #00c9a7;
}

// 对比数据显示
.comparison-values {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(248, 250, 252, 0.7) 100%);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.08);
}

.comparison-header {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;

  &.current {
    background: #00c9a7;
  }

  &.previous {
    background: #94a3b8;
  }
}

.legend-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

.comparison-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.comparison-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.95);
  }
}

.comparison-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #334155;
  flex: 1;
}

.comparison-scores {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.score-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.score-value {
  font-size: 28rpx;
  font-weight: 700;
  line-height: 1;

  &.current {
    color: #00c9a7;
  }

  &.previous {
    color: #94a3b8;
  }
}

.score-label {
  font-size: 20rpx;
  color: #64748b;
}

.score-divider {
  width: 1rpx;
  height: 32rpx;
  background: rgba(0, 201, 167, 0.2);
}

.score-change {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60rpx;
  padding: 6rpx 12rpx;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 16rpx;

  &.positive {
    background: rgba(0, 201, 167, 0.1);

    .change-value {
      color: #00c9a7;
    }
  }
}

.change-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #ef4444;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(248, 250, 252, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 80rpx;
    height: 80rpx;
    background: radial-gradient(circle, rgba(0, 201, 167, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 4s ease-in-out infinite;
  }
}

.empty-icon-wrapper {
  position: relative;
  margin-bottom: 32rpx;
}

.empty-icon {
  font-size: 64rpx;
  color: #cbd5e1;
  display: block;
}

.empty-decoration {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #475569;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.empty-desc {
  font-size: 24rpx;
  line-height: 1.6;
  color: #64748b;
  max-width: 400rpx;
}

// 动画定义
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-12rpx) rotate(3deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .ability-values {
    grid-template-columns: 1fr;
    gap: 12rpx;
    padding: 12rpx;
  }

  .ability-value-item {
    padding: 10rpx 12rpx;
  }

  .value-name {
    font-size: 22rpx;
  }

  .value-score {
    font-size: 24rpx;
  }

  .comparison-values {
    padding: 20rpx;
  }

  .comparison-header {
    gap: 24rpx;
  }

  .comparison-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
    padding: 16rpx;
  }

  .comparison-scores {
    width: 100%;
    justify-content: space-between;
  }

  .empty-state {
    min-height: 320rpx;
    padding: 32rpx 24rpx;
  }

  .empty-icon {
    font-size: 56rpx;
  }

  .empty-title {
    font-size: 28rpx;
  }

  .empty-desc {
    font-size: 22rpx;
  }
}

// H5端优化
/* #ifdef H5 */
.ability-value-item,
.comparison-item {
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.1);
  }
}
/* #endif */

// 小程序端优化
/* #ifdef MP */
.canvas-wrapper {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}

.ability-values,
.comparison-values {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}

.empty-state {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}
/* #endif */

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .canvas-wrapper {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
    border-color: rgba(0, 201, 167, 0.2);
  }

  .ability-values,
  .comparison-values {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(15, 23, 42, 0.7) 100%);
    border-color: rgba(0, 201, 167, 0.15);
  }

  .ability-value-item,
  .comparison-item {
    background: rgba(30, 41, 59, 0.8);
  }

  .value-name,
  .comparison-name {
    color: #e2e8f0;
  }

  .score-label,
  .legend-text {
    color: #94a3b8;
  }

  .empty-state {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5) 0%, rgba(15, 23, 42, 0.6) 100%);
    border-color: rgba(0, 201, 167, 0.15);
  }

  .empty-title {
    color: #e2e8f0;
  }

  .empty-desc {
    color: #94a3b8;
  }

  .empty-icon {
    color: #475569;
  }
}
</style>
