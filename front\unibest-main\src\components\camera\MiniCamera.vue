<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 组件属性
const props = defineProps({
  autoInit: {
    type: Boolean,
    default: true,
  },
  initialFacing: {
    type: String,
    default: 'front', // 'front' 或 'back'
  },
  // 添加强制前置摄像头属性
  forceFrontCamera: {
    type: Boolean,
    default: true, // 默认强制使用前置摄像头
  }
})

// 组件事件
const emit = defineEmits(['ready', 'error', 'close'])

// 状态
const isReady = ref(false)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const showVideo = ref(true)
const devicePosition = ref(props.initialFacing)
const needsPermission = ref(false)

// 初始化时强制使用前置摄像头
if (props.forceFrontCamera) {
  devicePosition.value = 'front'
}

// 引用
const cameraContext = ref(null)

// 调试状态
const debugStatus = ref('初始化中')

// 请求权限
const requestPermission = async () => {
  try {
    debugStatus.value = '请求权限中'
    
    // 小程序端权限请求
    const authResult = await uni.authorize({
      scope: 'scope.camera'
    })
    
    if (authResult[1] && authResult[1].errMsg === 'authorize:ok') {
      needsPermission.value = false
      await initCamera()
    } else {
      throw new Error('用户拒绝了摄像头权限')
    }
  } catch (error) {
    console.error('权限请求失败:', error)
    hasError.value = true
    errorMessage.value = '无法获取摄像头权限，请在设置中开启'
    debugStatus.value = '权限被拒绝'
    
    // 引导用户去设置页面
    uni.showModal({
      title: '权限申请',
      content: '面试需要摄像头权限才能正常进行，请前往设置页面开启',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          uni.openSetting()
        }
      }
    })
  }
}

// 初始化摄像头
const initCamera = async () => {
  if (isLoading.value) return

  debugStatus.value = '初始化摄像头'
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''
  needsPermission.value = false

  // 强制使用前置摄像头
  if (props.forceFrontCamera) {
    devicePosition.value = 'front'
  }

  try {
    // 检查摄像头权限
    const authSetting = await uni.getSetting()
    if (authSetting[1] && authSetting[1].authSetting['scope.camera'] === false) {
      needsPermission.value = true
      throw new Error('摄像头权限被拒绝')
    }

    // 创建摄像头上下文
    debugStatus.value = '创建摄像头上下文'
    cameraContext.value = uni.createCameraContext()

    // 摄像头初始化成功
    isReady.value = true
    debugStatus.value = '摄像头就绪'
    emit('ready', { context: cameraContext.value })

  } catch (error) {
    console.error('摄像头初始化失败:', error)
    hasError.value = true
    debugStatus.value = '初始化失败'

    if (error.message.includes('权限')) {
      errorMessage.value = '摄像头权限被拒绝'
      needsPermission.value = true
    } else {
      errorMessage.value = error.message || '摄像头初始化失败'
    }

    emit('error', { error })
  } finally {
    isLoading.value = false
  }
}

// 切换摄像头显示/隐藏
const toggleCamera = () => {
  showVideo.value = !showVideo.value
}

// 切换前后摄像头
const switchCamera = async () => {
  // 如果强制使用前置摄像头，则不允许切换
  if (props.forceFrontCamera) {
    console.log('强制使用前置摄像头模式，不允许切换')
    return
  }

  if (!cameraContext.value) {
    console.warn('摄像头上下文未初始化')
    return
  }

  try {
    devicePosition.value = devicePosition.value === 'front' ? 'back' : 'front'
    
    // 小程序端需要重新初始化摄像头来切换前后摄像头
    await initCamera()
    
    console.log('摄像头切换成功:', devicePosition.value)
  } catch (error) {
    console.error('切换摄像头失败:', error)
    errorMessage.value = '切换摄像头失败'
  }
}

// 关闭摄像头
const closeCamera = () => {
  if (cameraContext.value) {
    cameraContext.value = null
  }

  isReady.value = false
  emit('close')
}

// 拍照功能
const takePhoto = (options = {}) => {
  return new Promise((resolve, reject) => {
    if (!cameraContext.value) {
      reject(new Error('摄像头未初始化'))
      return
    }

    // 合并默认配置和传入的配置
    const photoOptions = {
      quality: options.quality || 'high',
      // 设置输出尺寸为原始尺寸，确保截取完整画面
      sizeType: ['original'],
      // 确保使用高质量压缩
      compressed: false,
      success: (res) => {
        console.log('拍照成功，图片路径:', res.tempImagePath)
        if (options.success) {
          options.success(res)
        }
        resolve(res.tempImagePath)
      },
      fail: (error) => {
        console.error('拍照失败:', error)
        if (options.fail) {
          options.fail(error)
        }
        reject(error)
      }
    }

    cameraContext.value.takePhoto(photoOptions)
  })
}

// 开始录像
const startRecord = () => {
  return new Promise((resolve, reject) => {
    if (!cameraContext.value) {
      reject(new Error('摄像头未初始化'))
      return
    }

    cameraContext.value.startRecord({
      success: (res) => {
        resolve(res)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// 停止录像
const stopRecord = () => {
  return new Promise((resolve, reject) => {
    if (!cameraContext.value) {
      reject(new Error('摄像头未初始化'))
      return
    }

    cameraContext.value.stopRecord({
      success: (res) => {
        resolve(res.tempThumbPath, res.tempVideoPath)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// 获取摄像头状态
const getCameraInfo = () => {
  return {
    isReady: isReady.value,
    devicePosition: devicePosition.value,
    hasError: hasError.value,
    errorMessage: errorMessage.value,
    context: cameraContext.value
  }
}

// 暴露方法给父组件
defineExpose({
  initCamera,
  closeCamera,
  toggleCamera,
  switchCamera,
  takePhoto,
  startRecord,
  stopRecord,
  getCameraInfo,
  requestPermission,
  isReady,
  hasError,
  errorMessage,
  debugStatus,
})

// 摄像头事件处理
const handleCameraReady = () => {
  console.log('小程序摄像头初始化完成')
  isReady.value = true
  isLoading.value = false
  debugStatus.value = '摄像头就绪'
  emit('ready', { context: cameraContext.value })
}

const handleCameraError = (error) => {
  console.error('小程序摄像头错误:', error)
  hasError.value = true
  isLoading.value = false
  errorMessage.value = '摄像头启动失败'
  debugStatus.value = '摄像头错误'
  emit('error', { error })
}

const handleScanCode = (event) => {
  console.log('扫码结果:', event)
  // 可以在这里处理扫码逻辑
}

// 生命周期钩子
onMounted(() => {
  if (props.autoInit) {
    initCamera()
    console.log('自动初始化小程序摄像头')
  }
})

onUnmounted(() => {
  closeCamera()
})
</script>

<template>
  <view class="mini-camera">
    <!-- 摄像头组件 -->
    <camera
      class="camera-stream"
      :device-position="devicePosition"
      flash="off"
      @initdone="handleCameraReady"
      @error="handleCameraError"
      @scancode="handleScanCode"
      mode="normal"
      resolution="medium"
      v-show="isReady && !hasError && showVideo"
    ></camera>

    <!-- 加载状态 -->
    <view class="camera-status" v-if="isLoading">
      <view class="loading-spinner">
        <text class="fa fa-spinner fa-spin spinner-icon"></text>
        <text class="status-text">正在启动摄像头...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="camera-error" v-if="hasError">
      <text class="fa fa-exclamation-circle error-icon"></text>
      <text class="error-text">{{ errorMessage }}</text>
      <button class="retry-btn" @click="initCamera">重试</button>
      <button class="permission-btn" @click="requestPermission" v-if="needsPermission">
        请求权限
      </button>
    </view>

    <!-- 摄像头隐藏提示 -->
    <view class="camera-hidden" v-if="isReady && !hasError && !showVideo">
      <text class="fa fa-eye-slash notice-icon"></text>
      <text class="notice-text">摄像头已隐藏</text>
    </view>

    <!-- 前置摄像头提示 -->
    <view class="camera-notice" v-if="isReady && !hasError && forceFrontCamera">
      <text class="notice-text">面试过程中需要使用前置摄像头</text>
    </view>

    <!-- 控制按钮 -->
    <view class="camera-controls" v-if="isReady && !hasError">
      <button class="control-btn" @click="toggleCamera">
        <text :class="showVideo ? 'fa fa-eye' : 'fa fa-eye-slash'"></text>
      </button>
      <button class="control-btn" @click="switchCamera" v-if="!forceFrontCamera">
        <text class="fa fa-camera"></text>
      </button>
    </view>

    <!-- 调试信息 -->
    <view class="debug-info" v-if="true">
      <view>状态: {{ debugStatus }}</view>
      <view v-if="errorMessage">错误: {{ errorMessage }}</view>
      <view>就绪: {{ isReady ? '是' : '否' }}</view>
      <view>加载中: {{ isLoading ? '是' : '否' }}</view>
      <view>设备位置: {{ devicePosition }}</view>
    </view>
  </view>
</template>

<style scoped>
.mini-camera {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8rpx;
}

.camera-stream {
  width: 100%;
  height: 100%;
}

.camera-status,
.camera-error,
.camera-hidden {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner-icon {
  margin-bottom: 20rpx;
  font-size: 80rpx;
  animation: spin 1s linear infinite;
}

.error-icon {
  margin-bottom: 20rpx;
  font-size: 80rpx;
  color: #ff4d4f;
}

.notice-icon {
  margin-bottom: 20rpx;
  font-size: 80rpx;
}

.status-text,
.error-text,
.notice-text {
  font-size: 28rpx;
  text-align: center;
}

.retry-btn,
.permission-btn {
  padding: 16rpx 32rpx;
  margin-top: 30rpx;
  color: white;
  background-color: #1890ff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.camera-controls {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  display: flex;
  gap: 20rpx;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  color: white;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  font-size: 32rpx;
}

.debug-info {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  padding: 16rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 24rpx;
  border-radius: 8rpx;
  z-index: 10;
}

.debug-info view {
  margin-bottom: 8rpx;
}

.camera-notice {
  position: absolute;
  top: 20rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.notice-text {
  font-size: 24rpx;
  color: white;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
