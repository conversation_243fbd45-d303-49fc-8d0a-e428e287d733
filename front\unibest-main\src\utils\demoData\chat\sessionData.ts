/**
 * @description 聊天会话演示数据
 * 提供聊天会话相关的演示数据
 */

import { getDemoAgentById } from './agentData'

// 聊天消息
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  think?: string
  showThink?: boolean
  isStreaming?: boolean
  isError?: boolean
  attachments?: Array<{
    type: 'image' | 'file'
    url: string
    name: string
  }>
}

// 聊天会话
export interface ChatSession {
  id: string
  title: string
  agentId: string
  messages: ChatMessage[]
  createTime: number
  updateTime: number
}

// 创建演示聊天消息
export const createDemoChatMessage = (
  role: 'user' | 'assistant',
  content: string,
  options: {
    id?: string
    think?: string
    showThink?: boolean
    isStreaming?: boolean
    isError?: boolean
    attachments?: Array<{
      type: 'image' | 'file'
      url: string
      name: string
    }>
  } = {},
): ChatMessage => {
  return {
    id: options.id || `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    role,
    content,
    think: options.think,
    showThink: options.showThink || false,
    isStreaming: options.isStreaming || false,
    isError: options.isError || false,
    attachments: options.attachments,
  }
}

// 创建演示聊天会话
export const createDemoChatSession = (
  agentId: string,
  options: {
    id?: string
    title?: string
    messages?: ChatMessage[]
    createTime?: number
    updateTime?: number
  } = {},
): ChatSession => {
  const now = Date.now()
  const createTime = options.createTime || now

  return {
    id: options.id || `session-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    title: options.title || getDefaultSessionTitle(agentId),
    agentId,
    messages: options.messages || [],
    createTime,
    updateTime: options.updateTime || createTime,
  }
}

// 获取默认会话标题
const getDefaultSessionTitle = (agentId: string): string => {
  const agent = getDemoAgentById(agentId)
  return agent ? `与${agent.name}的对话` : '新对话'
}

// 创建演示聊天会话列表
export const createDemoChatSessionList = (count: number = 10): ChatSession[] => {
  const sessions: ChatSession[] = []
  const now = Date.now()
  const agentIds = ['general', 'code', 'creative', 'business', 'education']

  for (let i = 0; i < count; i++) {
    const agentId = agentIds[Math.floor(Math.random() * agentIds.length)]
    const createTime = now - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000) // 随机1-30天前
    const updateTime = createTime + Math.floor(Math.random() * (now - createTime))

    // 创建随机消息
    const messages: ChatMessage[] = []
    const messageCount = Math.floor(Math.random() * 10) + 2 // 2-11条消息

    for (let j = 0; j < messageCount; j++) {
      const role = j % 2 === 0 ? 'user' : 'assistant'
      const content = getRandomMessage(role, agentId)

      messages.push(createDemoChatMessage(role, content))
    }

    // 创建会话
    const title = getSessionTitle(messages)

    sessions.push(
      createDemoChatSession(agentId, {
        id: `demo-session-${i + 1}`,
        title,
        messages,
        createTime,
        updateTime,
      }),
    )
  }

  // 按更新时间降序排序
  return sessions.sort((a, b) => b.updateTime - a.updateTime)
}

// 根据消息生成会话标题
const getSessionTitle = (messages: ChatMessage[]): string => {
  // 使用第一条用户消息作为标题
  const firstUserMessage = messages.find((msg) => msg.role === 'user')

  if (firstUserMessage) {
    // 截取前20个字符作为标题
    let title = firstUserMessage.content.trim().substring(0, 20)

    // 如果原始内容超过20个字符，添加省略号
    if (firstUserMessage.content.length > 20) {
      title += '...'
    }

    return title
  }

  return '新对话'
}

// 获取随机消息内容
const getRandomMessage = (role: 'user' | 'assistant', agentId: string): string => {
  if (role === 'user') {
    const userMessages = [
      '你好，能帮我解答一个问题吗？',
      '如何提高工作效率？',
      '推荐一些健康的早餐食谱',
      '如何学习一门新语言？',
      '最近有什么值得关注的科技新闻？',
      '如何保持良好的工作与生活平衡？',
      '有什么好的时间管理技巧？',
      '如何开始学习编程？',
      '推荐一些好看的电影',
      '如何提高英语口语？',
    ]

    return userMessages[Math.floor(Math.random() * userMessages.length)]
  } else {
    // 根据不同的助手类型返回不同的回复
    const assistantMessages: Record<string, string[]> = {
      general: [
        '你好！我很乐意帮你解答问题。请告诉我你想了解什么？',
        '提高工作效率的方法有很多，例如：制定明确的目标、使用番茄工作法、减少干扰源、定期休息等。你想了解哪一方面的具体技巧？',
        '健康的早餐选择包括：燕麦粥配水果和坚果、全麦面包配鸡蛋和牛油果、希腊酸奶配浆果和蜂蜜、蔬菜煎蛋卷等。这些都提供均衡的营养和持久的能量。',
        '学习新语言的有效方法包括：每天坚持学习、使用语言学习应用、找语言交换伙伴、沉浸在目标语言环境中、设定明确的学习目标等。',
      ],
      code: [
        '在React中使用Hooks可以让你在函数组件中使用状态和其他React特性。最常用的Hooks有useState、useEffect、useContext等。你想了解哪一个具体的Hook？',
        '我可以帮你优化代码。请分享你想要优化的JavaScript代码，我会提供具体的改进建议。',
        'Docker是一个开源的容器化平台，它允许开发者将应用及其依赖打包到一个可移植的容器中，然后发布到任何流行的Linux或Windows机器上。这简化了应用的部署和扩展过程。',
      ],
      creative: [
        '在寂静的黎明，一封神秘的信出现在艾米丽的门前。信封上没有邮戳，只有一行优雅的手写字："真相就在你的记忆深处"。艾米丽颤抖着手指打开信封...',
        'Logo设计灵感：1. 考虑使用负空间创造双重含义；2. 尝试简约几何形状组合；3. 探索字母形状的创意变形；4. 考虑色彩心理学的影响；5. 寻找能代表品牌核心价值的象征符号。',
        '提高创作灵感的方法：1. 改变环境，去新的地方；2. 阅读不同领域的作品；3. 进行头脑风暴；4. 尝试新的艺术形式；5. 与创意人士交流；6. 记录日常生活中的有趣观察。',
      ],
      business: [
        '制定有效营销策略的关键步骤：1. 明确目标受众；2. 设定具体、可衡量的目标；3. 分析竞争对手；4. 确定独特卖点；5. 选择合适的营销渠道；6. 制定内容策略；7. 设置预算；8. 实施并监测效果；9. 根据数据调整策略。',
        '当前电商行业趋势：1. 社交电商的崛起；2. 移动购物持续增长；3. 人工智能个性化推荐；4. 可持续和道德消费意识增强；5. 增强现实(AR)购物体验；6. 语音搜索和购物；7. 无接触配送选项扩展；8. 订阅模式的普及。',
        '提升团队协作效率的策略：1. 明确角色和责任；2. 建立有效的沟通渠道；3. 使用协作工具；4. 定期团队会议和回顾；5. 设定共同目标；6. 鼓励开放反馈；7. 认可和奖励团队成就；8. 投资团队建设活动。',
      ],
      education: [
        '有效学习数学的方法：1. 理解概念而不是死记硬背；2. 多做练习题巩固知识；3. 寻找实际应用场景；4. 使用可视化工具理解抽象概念；5. 加入学习小组讨论问题；6. 定期复习旧知识；7. 使用间隔重复法记忆公式；8. 遇到困难时寻求帮助。',
        '量子力学的基本原理涉及到微观世界的行为规律。核心概念包括：波粒二象性（粒子同时具有波和粒子的性质）、测不准原理（无法同时精确测量粒子的位置和动量）、量子叠加（粒子可以同时处于多个状态）、量子纠缠（两个粒子可以瞬时关联）等。',
        '学习编程的优质资源：1. 在线学习平台如Codecademy、freeCodeCamp；2. 交互式教程网站如LeetCode、HackerRank；3. 开源文档和教程；4. 编程社区如Stack Overflow、GitHub；5. 计算机科学课程如CS50；6. 编程博客和YouTube频道；7. 编程书籍如《Clean Code》、《Eloquent JavaScript》。',
      ],
    }

    const messages = assistantMessages[agentId] || assistantMessages['general']
    return messages[Math.floor(Math.random() * messages.length)]
  }
}

// 导出演示数据
export default {
  createDemoChatMessage,
  createDemoChatSession,
  createDemoChatSessionList,
}
