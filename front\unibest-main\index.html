<!doctype html>
<html build-time="%BUILD_TIME%">

<head>
  <meta charset="UTF-8" />
  <link rel="shortcut icon" href="favicon.svg" type="image/x-icon" />
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="global.css">
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&icon_names=search" />
  <script>
    var coverSupport =
      'CSS' in window &&
      typeof CSS.supports === 'function' &&
      (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=430, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') +
      '" />',
    )
  </script>
  <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"> -->
  <title>software-xunfei</title>
  <!--preload-links-->
  <!--app-context-->
</head>

<body>
  <div id="app"><!--app-html--></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>