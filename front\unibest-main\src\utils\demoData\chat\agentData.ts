/**
 * @description AI助手演示数据
 * 提供AI助手相关的演示数据
 */

// AI助手定义
export interface AIAgent {
  id: string
  name: string
  description: string
  icon: string
  color: string
  capabilities?: string[]
  examples?: string[]
}

// 创建演示AI助手列表
export const createDemoAgents = (): AIAgent[] => {
  return [
    {
      id: 'general',
      name: '通用助手',
      description: '可以回答各种问题的全能AI助手',
      icon: 'i-carbon-bot',
      color: '#00c9a7',
      capabilities: ['回答常见问题', '提供信息和建议', '帮助解决日常问题'],
      examples: ['如何提高工作效率？', '推荐一些健康的早餐食谱', '如何学习一门新语言？'],
    },
    {
      id: 'code',
      name: '代码助手',
      description: '专注于编程和技术问题的AI助手',
      icon: 'i-carbon-code',
      color: '#1890ff',
      capabilities: ['解答编程问题', '代码审查和优化', '技术概念解释'],
      examples: [
        '如何在React中使用Hooks？',
        '帮我优化这段JavaScript代码',
        '解释一下Docker的基本概念',
      ],
    },
    {
      id: 'creative',
      name: '创意助手',
      description: '帮助激发创意和提供创作灵感',
      icon: 'i-carbon-idea',
      color: '#722ed1',
      capabilities: ['创意写作和故事构思', '设计灵感和创意点子', '内容创作建议'],
      examples: ['帮我写一个短故事的开头', '给我一些Logo设计的灵感', '如何提高创作灵感？'],
    },
    {
      id: 'business',
      name: '商业助手',
      description: '提供商业和职场相关的建议和分析',
      icon: 'i-carbon-chart-line',
      color: '#fa8c16',
      capabilities: ['商业策略和分析', '市场趋势洞察', '职业发展建议'],
      examples: ['如何制定有效的营销策略？', '分析当前电商行业的趋势', '如何提升团队协作效率？'],
    },
    {
      id: 'education',
      name: '学习助手',
      description: '帮助学习和教育的AI助手',
      icon: 'i-carbon-book',
      color: '#52c41a',
      capabilities: ['学习方法指导', '知识点解析', '教育资源推荐'],
      examples: ['如何有效地学习数学？', '解释一下量子力学的基本原理', '推荐一些学习编程的资源'],
    },
  ]
}

// 根据ID获取演示AI助手
export const getDemoAgentById = (id: string): AIAgent | undefined => {
  return createDemoAgents().find((agent) => agent.id === id)
}

// 导出演示数据
export default {
  createDemoAgents,
  getDemoAgentById,
}
