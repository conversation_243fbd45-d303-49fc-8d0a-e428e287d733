<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

/**
 * 页面功能：练习题目答案结果页面
 * 显示：答案分析、评分、建议、再次练习等功能
 */

// 页面参数
const questionId = ref('')
const practiceMode = ref('')
const userAnswer = ref('')

// 页面状态
const isLoading = ref(true)
const showDetailAnalysis = ref(false)

// 结果数据
const practiceResult = ref({
  score: 0,
  level: '',
  correctness: '',
  highlights: [],
  issues: [],
  suggestions: [],
  timeSpent: 0,
  comparison: {
    better: 0,
    total: 0,
  },
})

// 题目信息
const questionInfo = ref({
  title: '',
  difficulty: '',
  type: '',
  tags: [],
})

// AI分析结果
const aiAnalysis = ref({
  strengths: [],
  weaknesses: [],
  recommendations: [],
  nextSteps: [],
})

/**
 * 方法名: loadPracticeResult
 * 功能: 加载练习结果数据
 * 参数: 无
 * 返回值: 无
 */
const loadPracticeResult = async () => {
  try {
    isLoading.value = true

    // 模拟获取练习结果数据
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 根据练习模式生成不同的结果
    const mockResults = generateMockResult(practiceMode.value)
    practiceResult.value = mockResults.result
    questionInfo.value = mockResults.question
    aiAnalysis.value = mockResults.analysis
  } catch (error) {
    console.error('加载练习结果失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * 方法名: generateMockResult
 * 功能: 生成模拟的练习结果数据
 * 参数: mode - 练习模式
 * 返回值: 结果数据对象
 */
const generateMockResult = (mode: string) => {
  const baseQuestion = {
    title: '快速排序算法实现',
    difficulty: '中等',
    type: mode === 'code' ? 'algorithm' : 'theory',
    tags: ['排序算法', '分治法', '递归'],
  }

  if (mode === 'code') {
    return {
      result: {
        score: 85,
        level: '良好',
        correctness: '算法实现正确，逻辑清晰',
        highlights: [
          '正确实现了快速排序的核心逻辑',
          '递归终止条件处理得当',
          '代码结构清晰，易于理解',
        ],
        issues: ['没有处理数组为空的边界情况', '可以考虑优化基准元素的选择'],
        suggestions: ['添加输入参数验证', '考虑使用三数取中法选择基准', '对小数组使用插入排序优化'],
        timeSpent: 1260, // 21分钟
        comparison: {
          better: 75,
          total: 100,
        },
      },
      question: baseQuestion,
      analysis: {
        strengths: [
          '算法思路清晰，分治思想运用正确',
          '代码实现规范，变量命名合理',
          '递归结构设计合理',
        ],
        weaknesses: ['边界条件处理不够完善', '缺少性能优化考虑'],
        recommendations: [
          '学习更多排序算法优化技巧',
          '练习处理各种边界情况',
          '了解算法复杂度分析方法',
        ],
        nextSteps: ['尝试实现归并排序对比学习', '练习更复杂的分治算法', '学习算法性能分析'],
      },
    }
  } else if (mode === 'voice') {
    return {
      result: {
        score: 78,
        level: '良好',
        correctness: '回答思路正确，表达清晰',
        highlights: ['语音表达清晰流畅', '思路逻辑性强', '关键概念解释准确'],
        issues: ['部分技术细节描述不够深入', '可以增加具体实例说明'],
        suggestions: ['多练习技术概念的口语表达', '增加实际应用场景的描述', '提高语音表达的自信度'],
        timeSpent: 180, // 3分钟
        comparison: {
          better: 68,
          total: 100,
        },
      },
      question: { ...baseQuestion, type: 'theory' },
      analysis: {
        strengths: ['语音表达自然流畅', '核心概念理解准确', '逻辑结构清晰'],
        weaknesses: ['技术深度有待提升', '实例举证不够充分'],
        recommendations: [
          '多练习技术面试常见问题',
          '准备更多实际项目经验分享',
          '提升技术表达的准确性',
        ],
        nextSteps: [
          '练习更多算法相关的口述题',
          '准备项目经验的结构化表达',
          '学习面试技巧和表达方法',
        ],
      },
    }
  } else if (mode === 'text') {
    return {
      result: {
        score: 82,
        level: '良好',
        correctness: '文字表达准确，逻辑清晰',
        highlights: ['答案结构化程度高', '关键知识点覆盖全面', '语言表达准确专业'],
        issues: ['可以增加更多代码示例', '实际应用场景描述不够'],
        suggestions: ['结合代码示例说明概念', '增加实际项目中的应用描述', '提高答案的实用性'],
        timeSpent: 900, // 15分钟
        comparison: {
          better: 72,
          total: 100,
        },
      },
      question: { ...baseQuestion, type: 'comprehensive' },
      analysis: {
        strengths: ['知识体系完整', '文字表达专业', '逻辑思路清晰'],
        weaknesses: ['缺少实践经验体现', '理论与实践结合不够'],
        recommendations: ['多实践编程练习', '积累项目开发经验', '学习更多实际应用案例'],
        nextSteps: ['进行更多代码实践', '学习系统设计相关知识', '参与开源项目贡献'],
      },
    }
  }

  // 默认返回
  return {
    result: {
      score: 80,
      level: '良好',
      correctness: '回答基本正确',
      highlights: ['思路清晰'],
      issues: ['可以更加深入'],
      suggestions: ['多加练习'],
      timeSpent: 600,
      comparison: { better: 70, total: 100 },
    },
    question: baseQuestion,
    analysis: {
      strengths: ['基础扎实'],
      weaknesses: ['经验不足'],
      recommendations: ['多加练习'],
      nextSteps: ['继续学习'],
    },
  }
}

/**
 * 方法名: getScoreColor
 * 功能: 根据分数获取对应的颜色
 * 参数: score - 分数
 * 返回值: 颜色值
 */
const getScoreColor = (score: number) => {
  if (score >= 90) return '#10b981'
  if (score >= 80) return '#3b82f6'
  if (score >= 70) return '#f59e0b'
  if (score >= 60) return '#ef4444'
  return '#6b7280'
}

/**
 * 方法名: getLevelText
 * 功能: 根据分数获取等级文字
 * 参数: score - 分数
 * 返回值: 等级文字
 */
const getLevelText = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '中等'
  if (score >= 60) return '及格'
  return '需要提高'
}

/**
 * 方法名: formatTime
 * 功能: 格式化时间显示
 * 参数: seconds - 秒数
 * 返回值: 格式化的时间字符串
 */
const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  }
  if (mins > 0) {
    return `${mins}分钟${secs}秒`
  }
  return `${secs}秒`
}

/**
 * 方法名: retryPractice
 * 功能: 重新练习
 * 参数: 无
 * 返回值: 无
 */
const retryPractice = () => {
  uni.navigateTo({
    url: `/pages/learning/practice-question?id=${questionId.value}`,
  })
}

/**
 * 方法名: viewSolution
 * 功能: 查看题目解析
 * 参数: 无
 * 返回值: 无
 */
const viewSolution = () => {
  uni.navigateTo({
    url: `/pages/learning/question-detail?id=${questionId.value}`,
  })
}

/**
 * 方法名: nextQuestion
 * 功能: 练习下一题
 * 参数: 无
 * 返回值: 无
 */
const nextQuestion = () => {
  // 这里可以根据题库推荐下一题
  uni.showToast({
    title: '正在为您推荐下一题...',
    icon: 'none',
  })

  setTimeout(() => {
    // 根据当前题目类型和难度推荐相似题目
    const nextQuestionId = generateNextQuestionId()
    uni.navigateTo({
      url: `/pages/learning/practice-question?id=${nextQuestionId}`,
    })
  }, 1500)
}

/**
 * 方法名: generateNextQuestionId
 * 功能: 生成下一题的ID
 * 参数: 无
 * 返回值: 题目ID
 */
const generateNextQuestionId = () => {
  // 简单的推荐逻辑，实际项目中会基于AI算法
  const currentType = questionInfo.value.type
  const difficulty = questionInfo.value.difficulty

  if (currentType === 'algorithm') {
    return `algorithms-${difficulty === '简单' ? '排序算法' : '动态规划'}-${Date.now()}`
  } else if (currentType === 'theory') {
    return `theory-${difficulty}-${Date.now()}`
  }

  return `practice-${Date.now()}`
}

/**
 * 方法名: sharePractice
 * 功能: 分享练习结果
 * 参数: 无
 * 返回值: 无
 */
const sharePractice = () => {
  uni.showToast({
    title: '分享链接已复制',
    icon: 'success',
  })
}

/**
 * 方法名: saveToPracticeHistory
 * 功能: 保存到练习记录
 * 参数: 无
 * 返回值: 无
 */
const saveToPracticeHistory = () => {
  uni.showToast({
    title: '已保存到练习记录',
    icon: 'success',
  })
}

/**
 * 方法名: percentageText
 * 功能: 计算百分比文字
 * 参数: 无
 * 返回值: 百分比字符串
 */
const percentageText = computed(() => {
  const { better, total } = practiceResult.value.comparison
  const percentage = Math.round((better / total) * 100)
  return `${percentage}%`
})

/**
 * 方法名: onLoad
 * 功能: 页面加载初始化
 * 参数: options - 页面参数
 * 返回值: 无
 */
onLoad((options: any) => {
  if (options) {
    questionId.value = options.questionId || ''
    practiceMode.value = options.mode || 'code'
    userAnswer.value = options.answer || ''

    loadPracticeResult()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="practice-result-page">
    <HeadBar title="练习结果" :show-back="true" />

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-animation">
        <view class="loading-circle"></view>
        <view class="loading-circle"></view>
        <view class="loading-circle"></view>
      </view>
      <text class="loading-text">正在分析您的答案...</text>
    </view>

    <!-- 结果内容 -->
    <scroll-view v-else scroll-y class="result-scroll" :style="{ height: 'calc(100vh - 88rpx)' }">
      <view class="result-container">
        <!-- 分数卡片 -->
        <view class="score-card">
          <view class="score-header">
            <view
              class="score-circle"
              :style="{ borderColor: getScoreColor(practiceResult.score) }"
            >
              <text class="score-number" :style="{ color: getScoreColor(practiceResult.score) }">
                {{ practiceResult.score }}
              </text>
              <text class="score-unit">分</text>
            </view>
            <view class="score-info">
              <text class="score-level" :style="{ color: getScoreColor(practiceResult.score) }">
                {{ getLevelText(practiceResult.score) }}
              </text>
              <text class="score-description">{{ practiceResult.correctness }}</text>
            </view>
          </view>

          <!-- 统计信息 -->
          <view class="score-stats">
            <view class="stat-item">
              <view class="stat-icon i-mdi-clock"></view>
              <text class="stat-label">用时</text>
              <text class="stat-value">{{ formatTime(practiceResult.timeSpent) }}</text>
            </view>
            <view class="stat-item">
              <view class="stat-icon i-mdi-chart-bar"></view>
              <text class="stat-label">超过</text>
              <text class="stat-value">{{ percentageText }} 的用户</text>
            </view>
          </view>
        </view>

        <!-- 题目信息 -->
        <view class="question-info-card">
          <view class="card-header">
            <view class="header-icon i-mdi-book-open"></view>
            <text class="card-title">题目信息</text>
          </view>
          <view class="question-details">
            <text class="question-title">{{ questionInfo.title }}</text>
            <view class="question-meta">
              <view class="difficulty-tag" :class="`difficulty-${questionInfo.difficulty}`">
                {{ questionInfo.difficulty }}
              </view>
              <view v-for="tag in questionInfo.tags" :key="tag" class="tag-item">
                {{ tag }}
              </view>
            </view>
          </view>
        </view>

        <!-- 答案分析 -->
        <view class="analysis-card">
          <view class="card-header">
            <view class="header-icon i-mdi-brain"></view>
            <text class="card-title">答案分析</text>
            <view class="toggle-btn" @click="showDetailAnalysis = !showDetailAnalysis">
              <view
                class="toggle-icon"
                :class="showDetailAnalysis ? 'i-mdi-chevron-up' : 'i-mdi-chevron-down'"
              ></view>
            </view>
          </view>

          <!-- 亮点 -->
          <view v-if="practiceResult.highlights.length > 0" class="analysis-section">
            <view class="section-header">
              <view class="section-icon i-mdi-star" style="color: #10b981"></view>
              <text class="section-title">答案亮点</text>
            </view>
            <view class="highlight-list">
              <view
                v-for="highlight in practiceResult.highlights"
                :key="highlight"
                class="highlight-item"
              >
                <view class="highlight-dot"></view>
                <text class="highlight-text">{{ highlight }}</text>
              </view>
            </view>
          </view>

          <!-- 问题 -->
          <view v-if="practiceResult.issues.length > 0" class="analysis-section">
            <view class="section-header">
              <view class="section-icon i-mdi-alert-circle" style="color: #f59e0b"></view>
              <text class="section-title">需要改进</text>
            </view>
            <view class="issue-list">
              <view v-for="issue in practiceResult.issues" :key="issue" class="issue-item">
                <view class="issue-dot"></view>
                <text class="issue-text">{{ issue }}</text>
              </view>
            </view>
          </view>

          <!-- 建议 -->
          <view v-if="practiceResult.suggestions.length > 0" class="analysis-section">
            <view class="section-header">
              <view class="section-icon i-mdi-lightbulb" style="color: #3b82f6"></view>
              <text class="section-title">改进建议</text>
            </view>
            <view class="suggestion-list">
              <view
                v-for="suggestion in practiceResult.suggestions"
                :key="suggestion"
                class="suggestion-item"
              >
                <view class="suggestion-dot"></view>
                <text class="suggestion-text">{{ suggestion }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- AI详细分析 -->
        <view v-if="showDetailAnalysis" class="ai-analysis-card">
          <view class="card-header">
            <view class="header-icon i-mdi-robot"></view>
            <text class="card-title">AI深度分析</text>
          </view>

          <!-- 优势分析 -->
          <view class="ai-section">
            <view class="ai-section-title">
              <view class="ai-icon i-mdi-check-circle" style="color: #10b981"></view>
              <text class="ai-title-text">优势分析</text>
            </view>
            <view class="ai-content">
              <text v-for="strength in aiAnalysis.strengths" :key="strength" class="ai-item">
                {{ strength }}
              </text>
            </view>
          </view>

          <!-- 薄弱环节 -->
          <view class="ai-section">
            <view class="ai-section-title">
              <view class="ai-icon i-mdi-alert-circle" style="color: #ef4444"></view>
              <text class="ai-title-text">薄弱环节</text>
            </view>
            <view class="ai-content">
              <text v-for="weakness in aiAnalysis.weaknesses" :key="weakness" class="ai-item">
                {{ weakness }}
              </text>
            </view>
          </view>

          <!-- 学习建议 -->
          <view class="ai-section">
            <view class="ai-section-title">
              <view class="ai-icon i-mdi-school" style="color: #8b5cf6"></view>
              <text class="ai-title-text">学习建议</text>
            </view>
            <view class="ai-content">
              <text
                v-for="recommendation in aiAnalysis.recommendations"
                :key="recommendation"
                class="ai-item"
              >
                {{ recommendation }}
              </text>
            </view>
          </view>

          <!-- 下一步计划 -->
          <view class="ai-section">
            <view class="ai-section-title">
              <view class="ai-icon i-mdi-target" style="color: #06b6d4"></view>
              <text class="ai-title-text">下一步计划</text>
            </view>
            <view class="ai-content">
              <text v-for="step in aiAnalysis.nextSteps" :key="step" class="ai-item">
                {{ step }}
              </text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <view class="button-row">
            <button class="action-btn secondary-btn" @click="retryPractice">
              <view class="btn-icon i-mdi-refresh"></view>
              <text class="btn-text">重新练习</text>
            </button>
            <button class="action-btn secondary-btn" @click="viewSolution">
              <view class="btn-icon i-mdi-book-open"></view>
              <text class="btn-text">查看解析</text>
            </button>
          </view>
          <view class="button-row">
            <button class="action-btn primary-btn" @click="nextQuestion">
              <view class="btn-icon i-mdi-arrow-right"></view>
              <text class="btn-text">下一题</text>
            </button>
          </view>
          <view class="button-row">
            <button class="action-btn outline-btn" @click="sharePractice">
              <view class="btn-icon i-mdi-share"></view>
              <text class="btn-text">分享结果</text>
            </button>
            <button class="action-btn outline-btn" @click="saveToPracticeHistory">
              <view class="btn-icon i-mdi-bookmark"></view>
              <text class="btn-text">保存记录</text>
            </button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
/* 页面容器 */
.practice-result-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 88rpx);

  .loading-animation {
    display: flex;
    gap: 12rpx;
    margin-bottom: 32rpx;

    .loading-circle {
      width: 16rpx;
      height: 16rpx;
      background: #00c9a7;
      border-radius: 50%;
      animation: loading-bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
      &:nth-child(3) {
        animation-delay: 0s;
      }
    }
  }

  .loading-text {
    font-size: 28rpx;
    color: #64748b;
  }
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
/* 结果滚动区 */
.result-scroll {
  background: transparent;
}

.result-container {
  padding: 32rpx 24rpx 48rpx;
}
/* 通用卡片样式 */
.score-card,
.question-info-card,
.analysis-card,
.ai-analysis-card {
  padding: 32rpx;
  margin-bottom: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
/* 分数卡片 */
.score-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2rpx solid #e2e8f0;

  .score-header {
    display: flex;
    gap: 32rpx;
    align-items: center;
    margin-bottom: 32rpx;
  }

  .score-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 160rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 8rpx solid;
    border-radius: 50%;

    .score-number {
      font-size: 48rpx;
      font-weight: bold;
    }

    .score-unit {
      margin-top: 4rpx;
      font-size: 24rpx;
      color: #64748b;
    }
  }

  .score-info {
    flex: 1;

    .score-level {
      display: block;
      margin-bottom: 12rpx;
      font-size: 36rpx;
      font-weight: bold;
    }

    .score-description {
      font-size: 26rpx;
      line-height: 1.6;
      color: #64748b;
    }
  }

  .score-stats {
    display: flex;
    gap: 32rpx;
  }

  .stat-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8rpx;
    align-items: center;
    padding: 24rpx;
    background: #f8fafc;
    border-radius: 16rpx;

    .stat-icon {
      font-size: 32rpx;
      color: #00c9a7;
    }

    .stat-label {
      font-size: 24rpx;
      color: #94a3b8;
    }

    .stat-value {
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }
}
/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;

  .header-icon {
    margin-right: 12rpx;
    font-size: 32rpx;
    color: #00c9a7;
  }

  .card-title {
    flex: 1;
    font-size: 32rpx;
    font-weight: bold;
    color: #1e293b;
  }

  .toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    background: #f8fafc;
    border-radius: 50%;
    transition: all 0.3s;

    &:active {
      background: #e2e8f0;
      transform: scale(0.9);
    }

    .toggle-icon {
      font-size: 24rpx;
      color: #64748b;
    }
  }
}
/* 题目信息卡片 */
.question-details {
  .question-title {
    display: block;
    margin-bottom: 16rpx;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 1.5;
    color: #1e293b;
  }

  .question-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
  }

  .difficulty-tag {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    font-weight: 600;
    border-radius: 16rpx;

    &.difficulty-简单 {
      color: #16a34a;
      background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    }

    &.difficulty-中等 {
      color: #d97706;
      background: linear-gradient(135deg, #fef3c7, #fde68a);
    }

    &.difficulty-困难 {
      color: #dc2626;
      background: linear-gradient(135deg, #fee2e2, #fecaca);
    }
  }

  .tag-item {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    color: #2563eb;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 16rpx;
  }
}
/* 分析卡片 */
.analysis-section {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    gap: 12rpx;
    align-items: center;
    margin-bottom: 16rpx;

    .section-icon {
      font-size: 28rpx;
    }

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }
}

.highlight-list,
.issue-list,
.suggestion-list {
  .highlight-item,
  .issue-item,
  .suggestion-item {
    display: flex;
    gap: 12rpx;
    align-items: flex-start;
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .highlight-dot,
    .issue-dot,
    .suggestion-dot {
      flex-shrink: 0;
      width: 12rpx;
      height: 12rpx;
      margin-top: 8rpx;
      border-radius: 50%;
    }

    .highlight-dot {
      background: #10b981;
    }

    .issue-dot {
      background: #f59e0b;
    }

    .suggestion-dot {
      background: #3b82f6;
    }

    .highlight-text,
    .issue-text,
    .suggestion-text {
      flex: 1;
      font-size: 26rpx;
      line-height: 1.6;
      color: #475569;
    }
  }
}
/* AI分析卡片 */
.ai-analysis-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2rpx solid #bfdbfe;

  .ai-section {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .ai-section-title {
      display: flex;
      gap: 12rpx;
      align-items: center;
      margin-bottom: 16rpx;

      .ai-icon {
        font-size: 28rpx;
      }

      .ai-title-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .ai-content {
      padding: 20rpx 24rpx;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 16rpx;

      .ai-item {
        display: block;
        margin-bottom: 12rpx;
        font-size: 26rpx;
        line-height: 1.6;
        color: #475569;

        &:last-child {
          margin-bottom: 0;
        }

        &::before {
          font-weight: bold;
          color: #00c9a7;
          content: '• ';
        }
      }
    }
  }
}
/* 操作按钮 */
.action-buttons {
  margin-top: 48rpx;

  .button-row {
    display: flex;
    gap: 16rpx;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .action-btn {
    display: flex;
    flex: 1;
    gap: 12rpx;
    align-items: center;
    justify-content: center;
    padding: 24rpx;
    font-size: 28rpx;
    font-weight: 600;
    border: none;
    border-radius: 24rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .btn-icon {
      font-size: 28rpx;
    }

    .btn-text {
      color: inherit;
    }

    &:active {
      transform: translateY(2rpx) scale(0.98);
    }

    &.primary-btn {
      color: #ffffff;
      background: linear-gradient(135deg, #00c9a7, #4fd1c7);
      box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

      &:active {
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
      }
    }

    &.secondary-btn {
      color: #ffffff;
      background: linear-gradient(135deg, #3b82f6, #60a5fa);
      box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);

      &:active {
        box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
      }
    }

    &.outline-btn {
      color: #64748b;
      background: #ffffff;
      border: 2rpx solid #e2e8f0;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

      &:active {
        background: #f8fafc;
        border-color: #cbd5e1;
      }
    }
  }
}
/* H5端滚动条优化 */
/* stylelint-disable selector-type-no-unknown */
// #ifdef H5
:deep(scroll-view)::-webkit-scrollbar {
  width: 6rpx;
}

:deep(scroll-view)::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3rpx;
}

:deep(scroll-view)::-webkit-scrollbar-thumb {
  background: rgba(0, 201, 167, 0.6);
  border-radius: 3rpx;
}

:deep(scroll-view)::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 201, 167, 0.8);
}
/* stylelint-enable selector-type-no-unknown */
// #endif
</style>
