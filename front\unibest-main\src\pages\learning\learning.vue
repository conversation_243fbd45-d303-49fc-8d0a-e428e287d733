<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

/**
 * @description 学习页面
 * 根据不同资源类型展示对应的学习内容：视频课程、文本资料、实战练习等
 */

// 页面参数
const resourceId = ref('')
const resourceType = ref('')

// 学习进度
const currentProgress = ref(0)
const totalDuration = ref(0)

// 页面状态
const isLoading = ref(true)
const isPlaying = ref(false)
const isFullscreen = ref(false)
const showControls = ref(true)

// 学习资源数据
const learningContent = ref({
  id: 1,
  title: '职场沟通技巧与表达能力提升',
  type: 'video', // video, article, practice, book
  totalDuration: '1小时45分',
  chapters: [
    {
      id: 1,
      title: '职场沟通基础理论',
      duration: '25分钟',
      type: 'video',
      videoUrl: '/static/video/chapter1.mp4',
      content: '',
      completed: false,
      progress: 0,
    },
    {
      id: 2,
      title: '有效表达技巧',
      duration: '30分钟',
      type: 'video',
      videoUrl: '/static/video/chapter2.mp4',
      content: '',
      completed: false,
      progress: 0,
    },
    {
      id: 3,
      title: '面试沟通实战',
      duration: '35分钟',
      type: 'video',
      videoUrl: '/static/video/chapter3.mp4',
      content: '',
      completed: false,
      progress: 0,
    },
    {
      id: 4,
      title: '处理沟通冲突',
      duration: '15分钟',
      type: 'article',
      content: '在职场中，沟通冲突是不可避免的。有效处理冲突需要掌握以下技巧...',
      completed: false,
      progress: 0,
    },
  ],
  currentChapter: 0,
  description: '帮助您掌握职场沟通技巧，提升面试表达能力和自信心',
  instructor: '资深HR导师',
  notes: [],
  bookmarks: [],
})

// 当前章节
const currentChapter = computed(() => {
  return learningContent.value.chapters[learningContent.value.currentChapter] || {}
})

// 总进度
const overallProgress = computed(() => {
  const chapters = learningContent.value.chapters
  const totalChapters = chapters.length
  const completedChapters = chapters.filter((c) => c.completed).length
  return Math.round((completedChapters / totalChapters) * 100)
})

// 笔记内容
const noteContent = ref('')
const showNoteDialog = ref(false)

/**
 * @description 获取页面参数
 */
const getPageParams = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$route?.query || {}

  if (options.id) {
    resourceId.value = options.id
  }
  if (options.type) {
    resourceType.value = options.type
  }
}

/**
 * @description 获取学习内容
 */
const fetchLearningContent = async () => {
  try {
    isLoading.value = true
    // 模拟API调用
    setTimeout(() => {
      updateLearningData()
      isLoading.value = false
    }, 1000)
  } catch (error) {
    console.error('获取学习内容失败:', error)
    isLoading.value = false
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

/**
 * @description 根据资源类型更新学习数据
 */
const updateLearningData = () => {
  const contentMap = {
    '1': {
      title: '职场沟通技巧与表达能力提升',
      type: 'video',
    },
    '2': {
      title: '创新思维训练与设计思维方法论',
      type: 'practice',
      chapters: [
        {
          id: 1,
          title: '创新思维基础',
          duration: '30分钟',
          type: 'article',
          content: '创新思维是指以新颖独创的方法解决问题的思维过程...',
          completed: false,
          progress: 0,
        },
        {
          id: 2,
          title: '设计思维实战',
          duration: '45分钟',
          type: 'practice',
          content: '通过实际案例练习设计思维的五个步骤...',
          completed: false,
          progress: 0,
        },
      ],
    },
    '3': {
      title: '前端面试必备：JavaScript深入解析',
      type: 'video',
    },
  }

  const data = contentMap[resourceId.value]
  if (data) {
    Object.assign(learningContent.value, data)
  }
}

/**
 * @description 播放/暂停视频
 */
const togglePlayPause = () => {
  if (currentChapter.value.type === 'video') {
    isPlaying.value = !isPlaying.value
    uni.showToast({
      title: isPlaying.value ? '开始播放' : '暂停播放',
      icon: 'none',
      duration: 1000,
    })
  }
}

/**
 * @description 切换章节
 */
const switchChapter = (index: number) => {
  if (index >= 0 && index < learningContent.value.chapters.length) {
    learningContent.value.currentChapter = index
    // 重置播放状态
    isPlaying.value = false
    currentProgress.value = 0

    uni.showToast({
      title: `切换到：${learningContent.value.chapters[index].title}`,
      icon: 'none',
    })
  }
}

/**
 * @description 上一章节
 */
const previousChapter = () => {
  if (learningContent.value.currentChapter > 0) {
    switchChapter(learningContent.value.currentChapter - 1)
  } else {
    uni.showToast({
      title: '已经是第一章节',
      icon: 'none',
    })
  }
}

/**
 * @description 下一章节
 */
const nextChapter = () => {
  const currentIndex = learningContent.value.currentChapter
  const totalChapters = learningContent.value.chapters.length

  if (currentIndex < totalChapters - 1) {
    // 标记当前章节为完成
    learningContent.value.chapters[currentIndex].completed = true
    learningContent.value.chapters[currentIndex].progress = 100

    switchChapter(currentIndex + 1)
  } else {
    // 课程完成
    completeCourse()
  }
}

/**
 * @description 完成课程
 */
const completeCourse = () => {
  // 标记最后一章节为完成
  const lastIndex = learningContent.value.chapters.length - 1
  learningContent.value.chapters[lastIndex].completed = true
  learningContent.value.chapters[lastIndex].progress = 100

  uni.showModal({
    title: '恭喜完成学习！',
    content: `您已完成《${learningContent.value.title}》的学习，是否查看学习报告？`,
    confirmText: '查看报告',
    cancelText: '返回',
    success: (res) => {
      if (res.confirm) {
        goToLearningReport()
      } else {
        goBack()
      }
    },
  })
}

/**
 * @description 查看学习报告
 */
const goToLearningReport = () => {
  uni.navigateTo({
    url: `/pages/learning/report?id=${resourceId.value}`,
    fail: () => {
      uni.showToast({
        title: '页面开发中',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 添加笔记
 */
const addNote = () => {
  showNoteDialog.value = true
}

/**
 * @description 保存笔记
 */
const saveNote = () => {
  if (noteContent.value.trim()) {
    const note = {
      id: Date.now(),
      content: noteContent.value.trim(),
      chapterId: currentChapter.value.id,
      chapterTitle: currentChapter.value.title,
      timestamp: new Date().toLocaleString(),
      progress: currentProgress.value,
    }

    learningContent.value.notes.push(note)
    noteContent.value = ''
    showNoteDialog.value = false

    uni.showToast({
      title: '笔记保存成功',
      icon: 'success',
    })
  }
}

/**
 * @description 取消笔记
 */
const cancelNote = () => {
  noteContent.value = ''
  showNoteDialog.value = false
}

/**
 * @description 添加书签
 */
const addBookmark = () => {
  const bookmark = {
    id: Date.now(),
    chapterId: currentChapter.value.id,
    chapterTitle: currentChapter.value.title,
    timestamp: new Date().toLocaleString(),
    progress: currentProgress.value,
  }

  learningContent.value.bookmarks.push(bookmark)

  uni.showToast({
    title: '书签添加成功',
    icon: 'success',
  })
}

/**
 * @description 控制栏显示/隐藏
 */
const toggleControls = () => {
  showControls.value = !showControls.value

  // 3秒后自动隐藏控制栏
  if (showControls.value && isPlaying.value) {
    setTimeout(() => {
      if (isPlaying.value) {
        showControls.value = false
      }
    }, 3000)
  }
}

/**
 * @description 全屏切换
 */
const toggleFullScreen = () => {
  isFullscreen.value = !isFullscreen.value

  // 设置屏幕方向
  uni.setScreenBrightness({
    value: isFullscreen.value ? 1 : 0.5,
  })
}

/**
 * @description 调整进度
 */
const seekTo = (progress: number) => {
  currentProgress.value = progress

  // 更新章节进度
  if (currentChapter.value) {
    currentChapter.value.progress = progress
  }
}

/**
 * @description 返回上一页
 */
const goBack = () => {
  // 保存学习进度
  saveLearningProgress()
  uni.navigateBack()
}

/**
 * @description 保存学习进度
 */
const saveLearningProgress = () => {
  // 模拟保存到服务器
  console.log('保存学习进度:', {
    resourceId: resourceId.value,
    currentChapter: learningContent.value.currentChapter,
    progress: currentProgress.value,
    notes: learningContent.value.notes,
    bookmarks: learningContent.value.bookmarks,
  })
}

/**
 * @description 格式化时间
 */
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 页面初始化
onMounted(() => {
  getPageParams()
  fetchLearningContent()
})

// 页面销毁时保存进度
onBeforeUnmount(() => {
  saveLearningProgress()
})
</script>

<template>
  <view class="learning-container" :class="{ 'fullscreen-mode': isFullscreen }">
    <HeadBar
      v-if="!isFullscreen"
      :title="learningContent.title"
      :show-back="true"
      :show-right-button="true"
      right-text="笔记"
      @back="goBack"
      @right-click="addNote"
    />

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载学习内容中...</text>
    </view>

    <!-- 主要内容 -->
    <view v-else class="learning-content">
      <!-- 视频播放区域 -->
      <view v-if="currentChapter.type === 'video'" class="video-section">
        <view class="video-container" @click="toggleControls">
          <image class="video-cover" src="/static/video/placeholder.jpg" mode="aspectFill" />

          <!-- 播放按钮 -->
          <view v-if="!isPlaying" class="play-overlay" @click.stop="togglePlayPause">
            <view class="i-mdi-play play-icon"></view>
          </view>

          <!-- 控制栏 -->
          <view v-if="showControls" class="video-controls">
            <view class="controls-top">
              <text class="video-title">{{ currentChapter.title }}</text>
              <button class="fullscreen-btn" @click.stop="toggleFullScreen">
                <view
                  :class="isFullscreen ? 'i-mdi-fullscreen-exit' : 'i-mdi-fullscreen'"
                  class="fullscreen-icon"
                ></view>
              </button>
            </view>

            <view class="controls-bottom">
              <button class="control-btn" @click.stop="togglePlayPause">
                <view :class="isPlaying ? 'i-mdi-pause' : 'i-mdi-play'" class="control-icon"></view>
              </button>

              <view class="progress-section">
                <text class="time-text">{{ formatTime(currentProgress) }}</text>
                <slider
                  class="progress-slider"
                  :value="currentProgress"
                  :max="totalDuration"
                  @change="seekTo"
                  active-color="#00c9a7"
                  background-color="rgba(255,255,255,0.3)"
                />
                <text class="time-text">{{ currentChapter.duration }}</text>
              </view>

              <button class="control-btn" @click.stop="addBookmark">
                <view class="i-mdi-bookmark control-icon"></view>
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 文章阅读区域 -->
      <view v-else-if="currentChapter.type === 'article'" class="article-section">
        <scroll-view class="article-content" scroll-y>
          <view class="article-header">
            <text class="article-title">{{ currentChapter.title }}</text>
            <text class="article-duration">预计阅读时间：{{ currentChapter.duration }}</text>
          </view>
          <view class="article-body">
            <text class="article-text">{{ currentChapter.content }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 实战练习区域 -->
      <view v-else-if="currentChapter.type === 'practice'" class="practice-section">
        <view class="practice-header">
          <text class="practice-title">{{ currentChapter.title }}</text>
          <text class="practice-desc">{{ currentChapter.content }}</text>
        </view>
        <view class="practice-content">
          <text class="coming-soon">实战练习功能开发中...</text>
        </view>
      </view>

      <!-- 章节导航 -->
      <view v-if="!isFullscreen" class="chapter-navigation">
        <view class="nav-header">
          <text class="nav-title">学习进度</text>
          <text class="progress-text">{{ overallProgress }}% 完成</text>
        </view>

        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: overallProgress + '%' }"></view>
        </view>

        <scroll-view class="chapters-list" scroll-x>
          <view class="chapters-container">
            <view
              v-for="(chapter, index) in learningContent.chapters"
              :key="chapter.id"
              class="chapter-item"
              :class="{
                'chapter-active': index === learningContent.currentChapter,
                'chapter-completed': chapter.completed,
              }"
              @click="switchChapter(index)"
            >
              <view class="chapter-number">{{ index + 1 }}</view>
              <text class="chapter-title">{{ chapter.title }}</text>
              <text class="chapter-duration">{{ chapter.duration }}</text>
              <view v-if="chapter.completed" class="chapter-check">
                <view class="i-mdi-check check-icon"></view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 底部操作栏 -->
      <view v-if="!isFullscreen" class="bottom-actions">
        <button
          class="action-btn prev-btn"
          @click="previousChapter"
          :disabled="learningContent.currentChapter === 0"
        >
          <view class="i-mdi-skip-previous btn-icon"></view>
          <text>上一章</text>
        </button>

        <button class="action-btn note-btn" @click="addNote">
          <view class="i-mdi-note-plus btn-icon"></view>
          <text>做笔记</text>
        </button>

        <button class="action-btn next-btn" @click="nextChapter">
          <text>
            {{
              learningContent.currentChapter === learningContent.chapters.length - 1
                ? '完成'
                : '下一章'
            }}
          </text>
          <view class="i-mdi-skip-next btn-icon"></view>
        </button>
      </view>
    </view>

    <!-- 笔记对话框 -->
    <view v-if="showNoteDialog" class="note-dialog-overlay" @click="cancelNote">
      <view class="note-dialog" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">添加学习笔记</text>
          <button class="close-btn" @click="cancelNote">
            <view class="i-mdi-close close-icon"></view>
          </button>
        </view>

        <view class="dialog-content">
          <textarea
            v-model="noteContent"
            class="note-textarea"
            placeholder="记录您的学习心得和要点..."
            maxlength="500"
            auto-focus
          />
          <text class="char-count">{{ noteContent.length }}/500</text>
        </view>

        <view class="dialog-actions">
          <button class="dialog-btn cancel-btn" @click="cancelNote">取消</button>
          <button class="dialog-btn save-btn" @click="saveNote" :disabled="!noteContent.trim()">
            保存笔记
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.learning-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #000;

  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
}

// Loading 状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background: #f8fafc;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #e2e8f0;
    border-top: 4rpx solid #00c9a7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 24rpx;
    color: #64748b;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 学习内容
.learning-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// 视频播放区域
.video-section {
  position: relative;
  background: #000;

  .video-container {
    position: relative;
    width: 100%;
    height: 400rpx;

    .fullscreen-mode & {
      height: 100vh;
    }

    .video-cover {
      width: 100%;
      height: 100%;
    }

    .play-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(10rpx);
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        font-size: 60rpx;
        color: white;
        margin-left: 8rpx;
      }
    }

    .video-controls {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.6) 0%,
        transparent 30%,
        transparent 70%,
        rgba(0, 0, 0, 0.8) 100%
      );
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 30rpx;

      .controls-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .video-title {
          flex: 1;
          font-size: 28rpx;
          color: white;
          font-weight: bold;
        }

        .fullscreen-btn {
          background: transparent;
          border: none;
          padding: 12rpx;

          .fullscreen-icon {
            font-size: 32rpx;
            color: white;
          }
        }
      }

      .controls-bottom {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .control-btn {
          background: transparent;
          border: none;
          padding: 12rpx;

          .control-icon {
            font-size: 36rpx;
            color: white;
          }
        }

        .progress-section {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 16rpx;

          .time-text {
            font-size: 22rpx;
            color: white;
            min-width: 80rpx;
            text-align: center;
          }

          .progress-slider {
            flex: 1;
          }
        }
      }
    }
  }
}

// 文章阅读区域
.article-section {
  flex: 1;
  background: white;

  .article-content {
    height: 100%;
    padding: 30rpx;

    .article-header {
      margin-bottom: 40rpx;

      .article-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #1e293b;
        margin-bottom: 16rpx;
        display: block;
      }

      .article-duration {
        font-size: 24rpx;
        color: #64748b;
      }
    }

    .article-body {
      .article-text {
        font-size: 28rpx;
        line-height: 1.8;
        color: #374151;
      }
    }
  }
}

// 实战练习区域
.practice-section {
  flex: 1;
  background: white;
  padding: 30rpx;

  .practice-header {
    margin-bottom: 40rpx;

    .practice-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #1e293b;
      margin-bottom: 16rpx;
      display: block;
    }

    .practice-desc {
      font-size: 26rpx;
      color: #64748b;
      line-height: 1.6;
    }
  }

  .practice-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400rpx;

    .coming-soon {
      font-size: 28rpx;
      color: #94a3b8;
    }
  }
}

// 章节导航
.chapter-navigation {
  background: white;
  padding: 30rpx;
  border-top: 2rpx solid #f1f5f9;

  .nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .nav-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #1e293b;
    }

    .progress-text {
      font-size: 24rpx;
      color: #00c9a7;
      font-weight: bold;
    }
  }

  .progress-bar {
    height: 8rpx;
    background: #f1f5f9;
    border-radius: 4rpx;
    margin-bottom: 30rpx;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
      border-radius: 4rpx;
      transition: width 0.3s ease;
    }
  }

  .chapters-list {
    white-space: nowrap;

    .chapters-container {
      display: inline-flex;
      gap: 20rpx;
      padding: 0 8rpx;

      .chapter-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx;
        min-width: 200rpx;
        background: #f8fafc;
        border: 2rpx solid #e2e8f0;
        border-radius: 16rpx;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &.chapter-active {
          background: rgba(0, 201, 167, 0.1);
          border-color: #00c9a7;

          .chapter-number {
            background: #00c9a7;
            color: white;
          }
        }

        &.chapter-completed {
          background: rgba(34, 197, 94, 0.1);
          border-color: #22c55e;

          .chapter-check {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            width: 32rpx;
            height: 32rpx;
            background: #22c55e;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .check-icon {
              font-size: 20rpx;
              color: white;
            }
          }
        }

        .chapter-number {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          background: #e2e8f0;
          color: #64748b;
          font-size: 22rpx;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12rpx;
        }

        .chapter-title {
          font-size: 24rpx;
          font-weight: bold;
          color: #1e293b;
          text-align: center;
          margin-bottom: 8rpx;
          line-height: 1.4;
        }

        .chapter-duration {
          font-size: 20rpx;
          color: #64748b;
        }
      }
    }
  }
}

// 底部操作栏
.bottom-actions {
  display: flex;
  gap: 16rpx;
  padding: 30rpx;
  background: white;
  border-top: 2rpx solid #f1f5f9;

  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    border: none;
    font-size: 26rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    transition: all 0.2s ease;

    .btn-icon {
      font-size: 28rpx;
    }

    &.prev-btn {
      background: #f1f5f9;
      color: #64748b;

      &:disabled {
        opacity: 0.5;
      }

      &:not(:disabled):active {
        background: #e2e8f0;
      }
    }

    &.note-btn {
      background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
      color: white;

      &:active {
        transform: scale(0.98);
      }
    }

    &.next-btn {
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      color: white;

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

// 笔记对话框
.note-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.note-dialog {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 2rpx solid #f1f5f9;

    .dialog-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #1e293b;
    }

    .close-btn {
      background: transparent;
      border: none;
      padding: 8rpx;

      .close-icon {
        font-size: 28rpx;
        color: #64748b;
      }
    }
  }

  .dialog-content {
    padding: 30rpx;

    .note-textarea {
      width: 100%;
      min-height: 200rpx;
      padding: 20rpx;
      border: 2rpx solid #e2e8f0;
      border-radius: 12rpx;
      font-size: 26rpx;
      line-height: 1.5;
      color: #374151;
      background: #f8fafc;
      margin-bottom: 16rpx;

      &::placeholder {
        color: #94a3b8;
      }

      &:focus {
        border-color: #00c9a7;
        background: white;
      }
    }

    .char-count {
      font-size: 22rpx;
      color: #94a3b8;
      text-align: right;
    }
  }

  .dialog-actions {
    display: flex;
    gap: 16rpx;
    padding: 30rpx;
    border-top: 2rpx solid #f1f5f9;

    .dialog-btn {
      flex: 1;
      height: 70rpx;
      border-radius: 35rpx;
      font-size: 26rpx;
      font-weight: bold;
      border: none;

      &.cancel-btn {
        background: #f1f5f9;
        color: #64748b;

        &:active {
          background: #e2e8f0;
        }
      }

      &.save-btn {
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        color: white;

        &:disabled {
          opacity: 0.5;
        }

        &:not(:disabled):active {
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
