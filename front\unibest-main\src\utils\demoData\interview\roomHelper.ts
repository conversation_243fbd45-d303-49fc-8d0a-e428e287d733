/**
 * @description 面试房间演示数据辅助函数
 * 提供面试房间页面使用演示数据的辅助函数
 */

import { initInterviewRoomDemoData, createMockWebSocket } from './roomInitializer'
import { demoDataManager } from '../DemoDataManager'
import { demoDataOptimizer } from '../DemoDataOptimizer'
import { API_PATHS } from './index'

/**
 * 面试房间演示数据辅助类
 * 提供面试房间页面使用演示数据的辅助方法
 */
export class InterviewRoomHelper {
  private static instance: InterviewRoomHelper
  private isInitialized: boolean = false
  private isDemoMode: boolean = false
  private sessionId: string = ''
  private mockWebSockets: Map<string, WebSocket> = new Map()

  // 私有构造函数，确保单例模式
  private constructor() {}

  // 获取单例实例
  public static getInstance(): InterviewRoomHelper {
    if (!InterviewRoomHelper.instance) {
      InterviewRoomHelper.instance = new InterviewRoomHelper()
    }
    return InterviewRoomHelper.instance
  }

  /**
   * 初始化面试房间演示数据
   * @param forceDemoMode 是否强制使用演示模式
   * @returns 是否使用演示模式
   */
  public init(forceDemoMode: boolean = false): boolean {
    if (this.isInitialized) {
      return this.isDemoMode
    }

    // 初始化演示数据
    initInterviewRoomDemoData()

    // 设置演示模式
    this.isDemoMode = forceDemoMode || !demoDataManager.isEnabled()
    this.isInitialized = true

    if (this.isDemoMode) {
      console.log('[InterviewRoomHelper] 面试房间使用演示模式')
      // 生成演示会话ID
      this.sessionId = `demo-session-${Date.now()}`
    }

    return this.isDemoMode
  }

  /**
   * 获取会话ID
   * @returns 会话ID
   */
  public getSessionId(): string {
    return this.sessionId || `demo-session-${Date.now()}`
  }

  /**
   * 设置会话ID
   * @param sessionId 会话ID
   */
  public setSessionId(sessionId: string): void {
    this.sessionId = sessionId
  }

  /**
   * 创建WebSocket连接
   * 如果是演示模式，则创建模拟的WebSocket连接
   * @param url WebSocket连接URL
   * @param options 配置选项
   * @returns WebSocket连接
   */
  public createWebSocket(url: string, options?: any): WebSocket {
    // 如果已经有相同URL的WebSocket连接，则返回已有的连接
    if (this.mockWebSockets.has(url)) {
      return this.mockWebSockets.get(url)!
    }

    // 创建WebSocket连接
    const ws = createMockWebSocket(url, options)

    // 如果是演示模式，则保存WebSocket连接
    if (this.isDemoMode) {
      this.mockWebSockets.set(url, ws)
    }

    return ws
  }

  /**
   * 关闭所有WebSocket连接
   */
  public closeAllWebSockets(): void {
    this.mockWebSockets.forEach((ws) => {
      try {
        ws.close()
      } catch (error) {
        console.error('[InterviewRoomHelper] 关闭WebSocket连接失败:', error)
      }
    })

    this.mockWebSockets.clear()
  }

  /**
   * 获取演示数据
   * 使用优化器获取演示数据，确保性能和可靠性
   * @param apiPath API路径
   * @param params 请求参数
   * @returns 演示数据
   */
  public getDemoData<T>(apiPath: string, params?: any): T {
    return demoDataOptimizer.getOptimizedDemoData<T>(apiPath, params)
  }

  /**
   * 检查API请求是否失败
   * 如果API请求失败，则使用演示数据
   * @param apiCall API请求函数
   * @param apiPath API路径
   * @param params 请求参数
   * @returns API请求结果
   */
  public async safeApiCall<T>(
    apiCall: () => Promise<T>,
    apiPath: string,
    params?: any,
  ): Promise<T> {
    try {
      // 尝试调用API
      return await apiCall()
    } catch (error) {
      console.warn(`[InterviewRoomHelper] API请求失败: ${apiPath}，使用演示数据`, error)

      // 如果API请求失败，则使用演示数据
      return this.getDemoData<T>(apiPath, params)
    }
  }

  /**
   * 获取会话信息
   * @param params 请求参数
   * @returns 会话信息
   */
  public async getSessionInfo(params?: any): Promise<any> {
    return this.getDemoData(API_PATHS.GET_SESSION_INFO, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 获取问题
   * @param id 问题ID
   * @returns 问题
   */
  public async getQuestion(id: number): Promise<any> {
    return this.getDemoData(API_PATHS.GET_QUESTION, { id })
  }

  /**
   * 获取问题列表
   * @param params 请求参数
   * @returns 问题列表
   */
  public async getQuestions(params?: any): Promise<any> {
    return this.getDemoData(API_PATHS.GET_QUESTIONS, params)
  }

  /**
   * 提交答案
   * @param params 请求参数
   * @returns 提交结果
   */
  public async submitAnswer(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.SUBMIT_ANSWER, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 结束面试
   * @param params 请求参数
   * @returns 结束结果
   */
  public async endInterview(params?: any): Promise<any> {
    // 关闭所有WebSocket连接
    this.closeAllWebSockets()

    return this.getDemoData(API_PATHS.END_INTERVIEW, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 提交反馈
   * @param params 请求参数
   * @returns 提交结果
   */
  public async submitFeedback(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.SUBMIT_FEEDBACK, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 检查设备
   * @param params 请求参数
   * @returns 检查结果
   */
  public async checkDevices(params?: any): Promise<any> {
    return this.getDemoData(API_PATHS.CHECK_DEVICES, params)
  }

  /**
   * 获取会话状态
   * @param params 请求参数
   * @returns 会话状态
   */
  public async getSessionStatus(params?: any): Promise<any> {
    return this.getDemoData(API_PATHS.GET_SESSION_STATUS, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 分析音频
   * @param params 请求参数
   * @returns 分析结果
   */
  public async analyzeAudio(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.ANALYZE_AUDIO, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 分析视频
   * @param params 请求参数
   * @returns 分析结果
   */
  public async analyzeVideo(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.ANALYZE_VIDEO, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 分析文本
   * @param params 请求参数
   * @returns 分析结果
   */
  public async analyzeText(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.ANALYZE_TEXT, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 综合分析
   * @param params 请求参数
   * @returns 分析结果
   */
  public async comprehensiveAnalysis(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.COMPREHENSIVE_ANALYSIS, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 创建音频流分析
   * @param params 请求参数
   * @returns 创建结果
   */
  public async createAudioStreamAnalysis(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.CREATE_AUDIO_STREAM_ANALYSIS, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 创建视频流分析
   * @param params 请求参数
   * @returns 创建结果
   */
  public async createVideoStreamAnalysis(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.CREATE_VIDEO_STREAM_ANALYSIS, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 获取分析历史
   * @param params 请求参数
   * @returns 分析历史
   */
  public async getAnalysisHistory(params: any): Promise<any> {
    return this.getDemoData(API_PATHS.GET_ANALYSIS_HISTORY, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 获取面试报告
   * @param params 请求参数
   * @returns 面试报告
   */
  public async getInterviewReport(params?: any): Promise<any> {
    return this.getDemoData(API_PATHS.GET_INTERVIEW_REPORT, {
      ...params,
      sessionId: this.sessionId,
    })
  }

  /**
   * 获取报告摘要
   * @param params 请求参数
   * @returns 报告摘要
   */
  public async getReportSummary(params?: any): Promise<any> {
    return this.getDemoData(API_PATHS.GET_REPORT_SUMMARY, {
      ...params,
      sessionId: this.sessionId,
    })
  }
}

// 导出单例实例
export const interviewRoomHelper = InterviewRoomHelper.getInstance()

// 导出默认实例
export default interviewRoomHelper
