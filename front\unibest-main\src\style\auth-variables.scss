/**
 * 认证页面统一样式变量
 * @description 为登录、注册、忘记密码页面提供统一的设计系统
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-18
 */

// ==================== 颜色系统 ====================
// 主色调
$primary-color: #00c9a7;
$primary-light: #33d4b8;
$primary-dark: #00b39a;
$primary-gradient: linear-gradient(135deg, #{$primary-color} 0%, #{$primary-dark} 100%);

// 功能色彩
$success-color: #4caf50;
$success-light: #81c784;
$success-dark: #388e3c;

$warning-color: #ff9800;
$warning-light: #ffb74d;
$warning-dark: #f57c00;

$error-color: #ff5252;
$error-light: #ff7979;
$error-dark: #d32f2f;

$info-color: #2196f3;
$info-light: #64b5f6;
$info-dark: #1976d2;

// 文本颜色
$text-primary: #333333;
$text-secondary: #666666;
$text-tertiary: #999999;
$text-disabled: #cccccc;
$text-white: #ffffff;

// 背景颜色
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-tertiary: #f5f5f5;
$bg-disabled: #e9ecef;

// 边框颜色
$border-primary: #e0e0e0;
$border-secondary: #f0f0f0;
$border-focus: #{$primary-color};
$border-error: #{$error-color};

// ==================== 阴影系统 ====================
$shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
$shadow-sm: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-lg: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
$shadow-xl: 0 24rpx 64rpx rgba(0, 0, 0, 0.20);

// 特殊阴影
$shadow-primary: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);
$shadow-error: 0 8rpx 32rpx rgba(255, 82, 82, 0.3);
$shadow-success: 0 8rpx 32rpx rgba(76, 175, 80, 0.3);

// ==================== 圆角系统 ====================
$radius-xs: 4rpx;
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 16rpx;
$radius-xl: 24rpx;
$radius-2xl: 32rpx;
$radius-full: 50%;

// ==================== 间距系统 ====================
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-2xl: 48rpx;
$spacing-3xl: 64rpx;

// ==================== 字体系统 ====================
$font-xs: 20rpx;
$font-sm: 24rpx;
$font-md: 28rpx;
$font-lg: 32rpx;
$font-xl: 36rpx;
$font-2xl: 48rpx;
$font-3xl: 64rpx;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// ==================== 动画系统 ====================
$transition-fast: 0.15s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// ==================== 组件尺寸 ====================
// 输入框
$input-height: 96rpx;
$input-padding-x: 30rpx;
$input-padding-y: 24rpx;

// 按钮
$button-height: 96rpx;
$button-height-sm: 72rpx;
$button-height-lg: 112rpx;

// Logo
$logo-size: 160rpx;
$logo-size-sm: 120rpx;

// ==================== 装饰元素 ====================
$decoration-gradient-1: linear-gradient(135deg, #e0f7f2 0%, #b5ece2 100%);
$decoration-gradient-2: linear-gradient(135deg, #d1f5ee 0%, #a5e0d5 100%);

// ==================== 响应式断点 ====================
$breakpoint-sm: 576rpx;
$breakpoint-md: 768rpx;
$breakpoint-lg: 992rpx;
$breakpoint-xl: 1200rpx;

// ==================== Z-index 层级 ====================
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-notification: 9999;

// ==================== 混合宏 ====================
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin button-reset {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  outline: none;
  
  &::after {
    border: none;
  }
}

@mixin input-focus {
  border-color: $border-focus;
  box-shadow: 0 0 0 4rpx rgba(0, 201, 167, 0.1);
}

@mixin input-error {
  border-color: $border-error;
  box-shadow: 0 0 0 4rpx rgba(255, 82, 82, 0.1);
}

// 渐变背景
@mixin gradient-primary {
  background: $primary-gradient;
}

@mixin gradient-success {
  background: linear-gradient(135deg, #{$success-color} 0%, #{$success-dark} 100%);
}

@mixin gradient-error {
  background: linear-gradient(135deg, #{$error-color} 0%, #{$error-dark} 100%);
}

// 卡片样式
@mixin card-base {
  background: $bg-primary;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
  transition: box-shadow $transition-normal ease;
}

@mixin card-hover {
  box-shadow: $shadow-md;
}

// 浮动动画
@mixin floating-animation($duration: 3s) {
  animation: floating $duration ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

// 脉冲动画
@mixin pulse-animation($duration: 2s) {
  animation: pulse $duration ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 旋转动画
@mixin spin-animation($duration: 1s) {
  animation: spin $duration linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// ==================== 响应式混合宏 ====================
@mixin mobile-small {
  @media (max-width: #{$breakpoint-sm - 1rpx}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1rpx}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1rpx}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin desktop-large {
  @media (min-width: #{$breakpoint-xl}) {
    @content;
  }
}

// 响应式字体大小
@mixin responsive-font($mobile-size, $desktop-size: null) {
  font-size: $mobile-size;

  @if $desktop-size {
    @include desktop {
      font-size: $desktop-size;
    }
  }
}

// 响应式间距
@mixin responsive-spacing($property, $mobile-value, $desktop-value: null) {
  #{$property}: $mobile-value;

  @if $desktop-value {
    @include desktop {
      #{$property}: $desktop-value;
    }
  }
}

// 响应式容器
@mixin responsive-container {
  width: 100%;
  max-width: 600rpx;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @include mobile-small {
    padding: 0 $spacing-md;
  }

  @include tablet {
    max-width: 800rpx;
    padding: 0 $spacing-xl;
  }

  @include desktop {
    max-width: 1000rpx;
    padding: 0 $spacing-2xl;
  }
}
