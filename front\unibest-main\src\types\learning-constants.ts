/**
 * 学习模块常量定义
 */

// API 端点常量
export const LEARNING_API_ENDPOINTS = {
  // 专业相关
  GET_MAJOR_LIST: '/app/learning/majors',

  // 题库相关
  GET_QUESTION_BANK_LIST: '/app/learning/question-banks',
  GET_QUESTION_BANK_DETAIL: '/app/learning/question-banks',
  GET_HOT_QUESTION_BANKS: '/app/learning/question-banks/hot',
  GET_NEW_QUESTION_BANKS: '/app/learning/question-banks/new',
  SEARCH_QUESTION_BANKS: '/app/learning/question-banks/search',
  TOGGLE_BOOKMARK: '/app/learning/question-banks/bookmark',
  TOGGLE_QUESTION_BANK_BOOKMARK: '/app/learning/question-banks/{bankId}/toggle-bookmark',
  GET_QUESTION_BANK_FULL_DETAIL: '/app/learning/question-banks/{bankId}/detail',
  GET_QUESTIONS_BY_CATEGORY: '/app/learning/question-banks/{bankId}/questions-by-category',
  GET_RECOMMENDED_QUESTIONS: '/app/learning/question-banks/{bankId}/recommended-questions',

  // 题目相关
  GET_QUESTION_LIST: '/app/learning/question-banks/{bankId}/questions',
  GET_QUESTION_STATISTICS: '/app/learning/question-banks/{bankId}/statistics',
  GET_QUESTION_DETAIL: '/app/learning/questions/{questionId}',
  SEARCH_QUESTIONS: '/app/learning/question-banks/{bankId}/questions/search',
  TOGGLE_QUESTION_BOOKMARK: '/app/learning/questions/{questionId}/bookmark',
  START_QUESTION_PRACTICE: '/app/learning/questions/{questionId}/practice',

  // 评论相关
  GET_QUESTION_COMMENTS: '/app/learning/questions/{questionId}/comments',
  CREATE_QUESTION_COMMENT: '/app/learning/questions/{questionId}/comments',
  DELETE_QUESTION_COMMENT: '/app/learning/comments/{commentId}',
  LIKE_QUESTION_COMMENT: '/app/learning/comments/{commentId}/like',

  // 练习相关
  SUBMIT_PRACTICE_RECORD: '/app/learning/practice/submit',
  GET_PRACTICE_HISTORY: '/app/learning/practice/history',
  GET_PRACTICE_STATS: '/app/learning/practice/stats',

  // 学习记录相关
  GET_LEARNING_PROGRESS: '/app/learning/progress',
  GET_USER_LEARNING_STATS: '/app/learning/stats',
  GET_RECENT_ACTIVITY: '/app/learning/activity/recent',

  // 推荐相关
  GET_RECOMMENDED_BANKS: '/app/learning/recommendations/banks',
  GET_RELATED_QUESTIONS: '/app/learning/questions/{questionId}/related',

  // 书籍相关
  GET_BOOK_LIST: '/app/learning/books/list',
  GET_BOOK_DETAIL: '/app/learning/books/{bookId}',
  GET_HOT_BOOKS: '/app/learning/books/hot',
  GET_NEW_BOOKS: '/app/learning/books/new',
  SEARCH_BOOKS: '/app/learning/books/search',
  GET_BOOK_CHAPTERS: '/app/learning/books/{bookId}/chapters',
  GET_CHAPTER_DETAIL: '/app/learning/books/chapter/{chapterId}',
  GET_READING_RECORD: '/app/learning/books/{bookId}/reading-record',
  UPDATE_READING_PROGRESS: '/app/learning/books/{bookId}/progress',
  GET_READING_STATS: '/app/learning/reading/stats',
  TOGGLE_BOOK_BOOKMARK: '/app/learning/books/{bookId}/bookmark',
  GET_RELATED_BOOKS: '/app/learning/books/{bookId}/related',

  // 其他
  REPORT_CONTENT: '/app/learning/report',
  GET_SEARCH_HISTORY: '/app/learning/search/history',
  SAVE_SEARCH_HISTORY: '/app/learning/search/history',
} as const

// 难度等级常量
export const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard',
} as const

export const DIFFICULTY_LABELS = {
  [DIFFICULTY_LEVELS.EASY]: '简单',
  [DIFFICULTY_LEVELS.MEDIUM]: '中等',
  [DIFFICULTY_LEVELS.HARD]: '困难',
} as const

// 题目类型常量
export const QUESTION_TYPES = {
  SINGLE_CHOICE: '单选题',
  MULTIPLE_CHOICE: '多选题',
  TRUE_FALSE: '判断题',
  PROGRAMMING: '编程题',
  SHORT_ANSWER: '简答题',
} as const

// 排序方式常量
export const SORT_TYPES = {
  DEFAULT: 'default',
  DIFFICULTY: 'difficulty',
  POPULARITY: 'popularity',
  LATEST: 'latest',
  PRACTICE_COUNT: 'practiceCount',
  CORRECT_RATE: 'correctRate',
  CREATE_TIME: 'createTime',
} as const

export const SORT_LABELS = {
  [SORT_TYPES.DEFAULT]: '默认排序',
  [SORT_TYPES.DIFFICULTY]: '按难度',
  [SORT_TYPES.POPULARITY]: '按热度',
  [SORT_TYPES.LATEST]: '最新发布',
  [SORT_TYPES.PRACTICE_COUNT]: '练习次数',
  [SORT_TYPES.CORRECT_RATE]: '正确率',
  [SORT_TYPES.CREATE_TIME]: '创建时间',
} as const

// 筛选类型常量
export const FILTER_TYPES = {
  ALL: 'all',
  COMPLETED: 'completed',
  UNCOMPLETED: 'uncompleted',
  BOOKMARKED: 'bookmarked',
  RECENT: 'recent',
} as const

export const FILTER_LABELS = {
  [FILTER_TYPES.ALL]: '全部',
  [FILTER_TYPES.COMPLETED]: '已完成',
  [FILTER_TYPES.UNCOMPLETED]: '未完成',
  [FILTER_TYPES.BOOKMARKED]: '已收藏',
  [FILTER_TYPES.RECENT]: '最近练习',
} as const

// 分页常量
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 10,
} as const

// 书籍分类常量
export const BOOK_CATEGORIES = {
  ALL: 'all',
  TECHNICAL: 'technical',
  BEHAVIORAL: 'behavioral',
  ALGORITHM: 'algorithm',
  RESUME: 'resume',
  SALARY: 'salary',
} as const

export const BOOK_CATEGORY_LABELS = {
  [BOOK_CATEGORIES.ALL]: '全部',
  [BOOK_CATEGORIES.TECHNICAL]: '技术面试',
  [BOOK_CATEGORIES.BEHAVIORAL]: '行为面试',
  [BOOK_CATEGORIES.ALGORITHM]: '算法题解',
  [BOOK_CATEGORIES.RESUME]: '简历指导',
  [BOOK_CATEGORIES.SALARY]: '薪资谈判',
} as const

// 书籍难度常量
export const BOOK_DIFFICULTY = {
  BEGINNER: '入门',
  INTERMEDIATE: '进阶',
  ADVANCED: '高级',
} as const

// 阅读主题常量
export const READING_THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SEPIA: 'sepia',
} as const

export const READING_THEME_LABELS = {
  [READING_THEMES.LIGHT]: '日间模式',
  [READING_THEMES.DARK]: '夜间模式',
  [READING_THEMES.SEPIA]: '护眼模式',
} as const

// 本地存储键名常量
export const STORAGE_KEYS = {
  SEARCH_HISTORY: 'learning_search_history',
  FILTER_PREFERENCES: 'learning_filter_preferences',
  SORT_PREFERENCES: 'learning_sort_preferences',
  RECENT_BANKS: 'learning_recent_banks',
  USER_LEARNING_STATS: 'learning_user_stats',
  READING_SETTINGS: 'reading_settings',
  BOOK_READING_RECORDS: 'book_reading_records',
} as const

// 默认配置
export const DEFAULT_CONFIG = {
  SEARCH_DEBOUNCE_TIME: 300,
  NOTIFICATION_DURATION: 3000,
  AUTO_SAVE_INTERVAL: 30000,
  MAX_SEARCH_HISTORY: 10,
  MAX_RECENT_ACTIVITY: 20,
} as const

// 状态常量
export const STATUS = {
  ACTIVE: '0',
  INACTIVE: '1',
  DELETED: '2',
} as const

// 通知类型常量
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  INFO: 'info',
  WARNING: 'warning',
} as const

// 图标常量
export const ICONS = {
  DIFFICULTY: {
    [DIFFICULTY_LEVELS.EASY]: '🟢',
    [DIFFICULTY_LEVELS.MEDIUM]: '🟡',
    [DIFFICULTY_LEVELS.HARD]: '🔴',
  },
  QUESTION_TYPE: {
    [QUESTION_TYPES.SINGLE_CHOICE]: '📝',
    [QUESTION_TYPES.MULTIPLE_CHOICE]: '☑️',
    [QUESTION_TYPES.TRUE_FALSE]: '❓',
    [QUESTION_TYPES.PROGRAMMING]: '💻',
    [QUESTION_TYPES.SHORT_ANSWER]: '✏️',
  },
  ACTION: {
    SEARCH: '🔍',
    BOOKMARK: '⭐',
    SHARE: '📤',
    DOWNLOAD: '📥',
    FILTER: '🔽',
    SORT: '🔀',
    REFRESH: '🔄',
    BACK: '⬅️',
    FORWARD: '➡️',
    UP: '⬆️',
    DOWN: '⬇️',
  },
} as const

// 颜色主题常量
export const THEME_COLORS = {
  PRIMARY: '#00c9a7',
  SECONDARY: '#f39c12',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  INFO: '#1890ff',
  TEXT_PRIMARY: '#2c3e50',
  TEXT_SECONDARY: '#7f8c8d',
  BACKGROUND: '#f5f7fa',
  BORDER: '#e9ecef',
} as const

// 导出类型
export type DifficultyLevel = (typeof DIFFICULTY_LEVELS)[keyof typeof DIFFICULTY_LEVELS]
export type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES]
export type SortType = (typeof SORT_TYPES)[keyof typeof SORT_TYPES]
export type FilterType = (typeof FILTER_TYPES)[keyof typeof FILTER_TYPES]
export type NotificationType = (typeof NOTIFICATION_TYPES)[keyof typeof NOTIFICATION_TYPES]
export type BookCategory = (typeof BOOK_CATEGORIES)[keyof typeof BOOK_CATEGORIES]
export type BookDifficulty = (typeof BOOK_DIFFICULTY)[keyof typeof BOOK_DIFFICULTY]
export type ReadingTheme = (typeof READING_THEMES)[keyof typeof READING_THEMES]

// 难度样式映射
export const DIFFICULTY_STYLE_MAP = {
  easy: 'bg-green-100 text-green-700',
  medium: 'bg-yellow-100 text-yellow-700',
  hard: 'bg-red-100 text-red-700',
} as const

// 学习相关通知消息
export const LEARNING_NOTIFICATION_MESSAGES = {
  SUCCESS: {
    BOOKMARK_ADDED: '收藏成功',
    BOOKMARK_REMOVED: '取消收藏',
    FILTER_RESET: '筛选已重置',
    MAJOR_SWITCHED: '已切换到{major}专业',
  },
  ERROR: {
    LOAD_BANKS_FAILED: '加载题库失败',
    SEARCH_FAILED: '搜索失败',
    BOOKMARK_FAILED: '收藏操作失败',
  },
} as const

// 学习相关本地存储键名
export const LEARNING_STORAGE_KEYS = {
  SELECTED_MAJOR: 'learning_selected_major',
  BOOKMARKED_BANKS: 'learning_bookmarked_banks',
  SEARCH_HISTORY: 'learning_search_history',
  FILTER_PREFERENCES: 'learning_filter_preferences',
  SORT_PREFERENCES: 'learning_sort_preferences',
  RECENT_BANKS: 'learning_recent_banks',
  USER_LEARNING_STATS: 'learning_user_stats',
} as const
