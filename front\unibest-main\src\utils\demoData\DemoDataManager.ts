/**
 * @description 演示数据管理器
 * 负责管理和提供演示数据，在API请求失败时自动提供演示数据
 */

// 演示数据管理器接口
export interface DemoDataManager {
  // 获取演示数据
  getDemoData<T>(apiPath: string, params?: any): T

  // 注册演示数据
  registerDemoData(apiPath: string, data: any): void

  // 注册演示数据生成器
  registerDemoDataGenerator(apiPath: string, generator: Function): void

  // 是否启用演示数据
  isEnabled(): boolean

  // 设置是否启用演示数据
  setEnabled(enabled: boolean): void
}

// 演示数据存储接口
export interface DemoDataStore {
  // 获取演示数据
  get<T>(key: string): T | null

  // 设置演示数据
  set(key: string, data: any): void

  // 检查是否存在演示数据
  has(key: string): boolean

  // 删除演示数据
  remove(key: string): void

  // 清空所有演示数据
  clear(): void
}

// 演示数据生成器类型
export type DemoDataGenerator = (params?: any) => any

// 演示数据管理器实现
class DemoDataManagerImpl implements DemoDataManager {
  private static instance: DemoDataManagerImpl
  private enabled: boolean = true
  private demoDataStore: DemoDataStore
  private demoDataGenerators: Map<string, DemoDataGenerator> = new Map()

  // 私有构造函数，确保单例模式
  private constructor(demoDataStore: DemoDataStore) {
    this.demoDataStore = demoDataStore

    // 根据环境设置是否启用演示数据
    this.enabled = process.env.NODE_ENV !== 'production'

    console.log(`[DemoDataManager] 初始化完成，演示数据${this.enabled ? '已启用' : '已禁用'}`)
  }

  // 获取单例实例
  public static getInstance(demoDataStore: DemoDataStore): DemoDataManagerImpl {
    if (!DemoDataManagerImpl.instance) {
      DemoDataManagerImpl.instance = new DemoDataManagerImpl(demoDataStore)
    }
    return DemoDataManagerImpl.instance
  }

  // 获取演示数据
  public getDemoData<T>(apiPath: string, params?: any): T {
    if (!this.enabled) {
      throw new Error('演示数据已禁用')
    }

    // 标准化API路径，移除查询参数和前导斜杠
    const normalizedPath = this.normalizeApiPath(apiPath)

    // 首先检查是否有注册的演示数据
    if (this.demoDataStore.has(normalizedPath)) {
      console.log(`[DemoDataManager] 使用预定义演示数据: ${normalizedPath}`)
      return this.demoDataStore.get<T>(normalizedPath)!
    }

    // 然后检查是否有注册的演示数据生成器
    if (this.demoDataGenerators.has(normalizedPath)) {
      console.log(`[DemoDataManager] 使用演示数据生成器: ${normalizedPath}`)
      const generator = this.demoDataGenerators.get(normalizedPath)!
      return generator(params) as T
    }

    // 如果没有找到匹配的演示数据或生成器，抛出错误
    throw new Error(`未找到API路径的演示数据: ${normalizedPath}`)
  }

  // 注册演示数据
  public registerDemoData(apiPath: string, data: any): void {
    const normalizedPath = this.normalizeApiPath(apiPath)
    this.demoDataStore.set(normalizedPath, data)
    console.log(`[DemoDataManager] 注册演示数据: ${normalizedPath}`)
  }

  // 注册演示数据生成器
  public registerDemoDataGenerator(apiPath: string, generator: Function): void {
    const normalizedPath = this.normalizeApiPath(apiPath)
    this.demoDataGenerators.set(normalizedPath, generator as DemoDataGenerator)
    console.log(`[DemoDataManager] 注册演示数据生成器: ${normalizedPath}`)
  }

  // 是否启用演示数据
  public isEnabled(): boolean {
    return this.enabled
  }

  // 设置是否启用演示数据
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled
    console.log(`[DemoDataManager] 演示数据${enabled ? '已启用' : '已禁用'}`)
  }

  // 标准化API路径
  private normalizeApiPath(apiPath: string): string {
    // 移除查询参数
    let path = apiPath.split('?')[0]

    // 移除前导斜杠
    if (path.startsWith('/')) {
      path = path.substring(1)
    }

    return path
  }
}

// 内存存储实现
class MemoryDemoDataStore implements DemoDataStore {
  private store: Map<string, any> = new Map()

  public get<T>(key: string): T | null {
    return this.store.has(key) ? (this.store.get(key) as T) : null
  }

  public set(key: string, data: any): void {
    this.store.set(key, data)
  }

  public has(key: string): boolean {
    return this.store.has(key)
  }

  public remove(key: string): void {
    this.store.delete(key)
  }

  public clear(): void {
    this.store.clear()
  }
}

// 导出单例实例
export const demoDataStore = new MemoryDemoDataStore()
export const demoDataManager = DemoDataManagerImpl.getInstance(demoDataStore)

// 导出默认实例
export default demoDataManager
