<template>
  <view v-if="visible" :class="loadingClasses">
    <!-- 遮罩层 -->
    <view v-if="overlay" class="ui-loading__overlay" @click="handleOverlayClick"></view>

    <!-- 加载内容 -->
    <view class="ui-loading__content">
      <!-- 加载图标 -->
      <view class="ui-loading__spinner">
        <view v-if="type === 'spinner'" class="ui-loading__spinner-icon">
          <text class="i-mdi-loading"></text>
        </view>

        <view v-else-if="type === 'dots'" class="ui-loading__dots">
          <view class="ui-loading__dot" v-for="i in 3" :key="i"></view>
        </view>

        <view v-else-if="type === 'pulse'" class="ui-loading__pulse">
          <view class="ui-loading__pulse-circle"></view>
        </view>

        <view v-else-if="type === 'bars'" class="ui-loading__bars">
          <view class="ui-loading__bar" v-for="i in 4" :key="i"></view>
        </view>

        <!-- 自定义图标 -->
        <text v-else-if="customIcon" :class="['ui-loading__custom-icon', customIcon]"></text>
      </view>

      <!-- 加载文本 -->
      <text v-if="text" class="ui-loading__text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 是否显示
  visible?: boolean
  // 加载类型
  type?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'custom'
  // 加载文本
  text?: string
  // 是否显示遮罩
  overlay?: boolean
  // 遮罩是否可点击关闭
  overlayClosable?: boolean
  // 尺寸
  size?: 'small' | 'medium' | 'large'
  // 颜色
  color?: string
  // 自定义图标
  customIcon?: string
  // 是否全屏
  fullscreen?: boolean
  // 自定义类名
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  type: 'spinner',
  text: '',
  overlay: true,
  overlayClosable: false,
  size: 'medium',
  color: '',
  customIcon: '',
  fullscreen: false,
  customClass: '',
})

const emit = defineEmits<{
  close: []
}>()

// 计算加载器类名
const loadingClasses = computed(() => {
  const classes = ['ui-loading', `ui-loading--${props.size}`, props.customClass]

  if (props.fullscreen) classes.push('ui-loading--fullscreen')
  if (props.overlay) classes.push('ui-loading--overlay')

  return classes.join(' ')
})

// 处理遮罩点击
const handleOverlayClick = () => {
  if (props.overlayClosable) {
    emit('close')
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables';
@import '@/styles/mixins';

.ui-loading {
  @include flex-center;
  position: relative;

  // 全屏模式
  &--fullscreen {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index-modal;
  }

  // 有遮罩模式
  &--overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index-modal;
  }

  // 尺寸样式
  &--small {
    .ui-loading__spinner {
      width: 48rpx;
      height: 48rpx;
    }

    .ui-loading__text {
      font-size: $font-size-sm;
    }
  }

  &--medium {
    .ui-loading__spinner {
      width: 64rpx;
      height: 64rpx;
    }

    .ui-loading__text {
      font-size: $font-size-base;
    }
  }

  &--large {
    .ui-loading__spinner {
      width: 80rpx;
      height: 80rpx;
    }

    .ui-loading__text {
      font-size: $font-size-lg;
    }
  }
}

// 遮罩层
.ui-loading__overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

// 加载内容
.ui-loading__content {
  @include flex-center;
  position: relative;
  z-index: 1;
  flex-direction: column;
  padding: $spacing-xl;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: $radius-xl;
  box-shadow: $shadow-lg;
}

// 加载图标容器
.ui-loading__spinner {
  @include flex-center;
  width: 64rpx;
  height: 64rpx;
  margin-bottom: $spacing-base;
}

// 旋转图标
.ui-loading__spinner-icon {
  font-size: 48rpx;
  color: $primary-color;
  animation: spin 1s linear infinite;
}

// 点状加载器
.ui-loading__dots {
  display: flex;
  gap: $spacing-xs;
}

.ui-loading__dot {
  width: 12rpx;
  height: 12rpx;
  background: $primary-color;
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
  &:nth-child(3) {
    animation-delay: 0s;
  }
}

// 脉冲加载器
.ui-loading__pulse {
  position: relative;
}

.ui-loading__pulse-circle {
  width: 40rpx;
  height: 40rpx;
  background: $primary-color;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

// 条状加载器
.ui-loading__bars {
  display: flex;
  gap: 6rpx;
  align-items: flex-end;
  height: 40rpx;
}

.ui-loading__bar {
  width: 6rpx;
  background: $primary-color;
  border-radius: 3rpx;
  animation: barScale 1.2s ease-in-out infinite;

  &:nth-child(1) {
    animation-delay: -0.9s;
  }
  &:nth-child(2) {
    animation-delay: -0.6s;
  }
  &:nth-child(3) {
    animation-delay: -0.3s;
  }
  &:nth-child(4) {
    animation-delay: 0s;
  }
}

// 自定义图标
.ui-loading__custom-icon {
  font-size: 48rpx;
  color: $primary-color;
  animation: spin 1s linear infinite;
}

// 加载文本
.ui-loading__text {
  margin-top: $spacing-base;
  font-size: $font-size-base;
  color: $text-primary;
  text-align: center;
}

// 动画定义
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    opacity: 0.5;
    transform: scale(0);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(0);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}

@keyframes barScale {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

// 内联加载器（不带遮罩）
.ui-loading--inline {
  position: relative;
  display: inline-flex;

  .ui-loading__content {
    padding: 0;
    background: transparent;
    box-shadow: none;
  }
}

// 按钮内加载器
.ui-loading--button {
  .ui-loading__spinner {
    width: 32rpx;
    height: 32rpx;
    margin: 0;
  }

  .ui-loading__spinner-icon {
    font-size: 24rpx;
  }

  .ui-loading__text {
    margin: 0;
    margin-left: $spacing-xs;
  }
}

// 卡片加载器
.ui-loading--card {
  .ui-loading__content {
    width: 100%;
    background: transparent;
    box-shadow: none;
  }
}

// 主题色变体
.ui-loading--primary {
  .ui-loading__spinner-icon,
  .ui-loading__dot,
  .ui-loading__pulse-circle,
  .ui-loading__bar,
  .ui-loading__custom-icon {
    color: $primary-color;
    background: $primary-color;
  }
}

.ui-loading--success {
  .ui-loading__spinner-icon,
  .ui-loading__dot,
  .ui-loading__pulse-circle,
  .ui-loading__bar,
  .ui-loading__custom-icon {
    color: $success-color;
    background: $success-color;
  }
}

.ui-loading--warning {
  .ui-loading__spinner-icon,
  .ui-loading__dot,
  .ui-loading__pulse-circle,
  .ui-loading__bar,
  .ui-loading__custom-icon {
    color: $warning-color;
    background: $warning-color;
  }
}

.ui-loading--error {
  .ui-loading__spinner-icon,
  .ui-loading__dot,
  .ui-loading__pulse-circle,
  .ui-loading__bar,
  .ui-loading__custom-icon {
    color: $error-color;
    background: $error-color;
  }
}
</style>
