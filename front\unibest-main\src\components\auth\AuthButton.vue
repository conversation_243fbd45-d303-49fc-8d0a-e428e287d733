<script setup lang="ts">
import { computed } from 'vue'

/**
 * 认证按钮组件
 * @description 统一的认证页面按钮组件，支持多种类型、状态和动画效果
 */

interface Props {
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  disabled?: boolean
  block?: boolean
  round?: boolean
  plain?: boolean
  icon?: string
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  loading: false,
  disabled: false,
  block: false,
  round: false,
  plain: false,
  icon: '',
  loadingText: '加载中...',
})

const emit = defineEmits<{
  click: [event: Event]
}>()

// 计算属性
const buttonClass = computed(() => {
  return [
    'auth-button',
    `auth-button--${props.type}`,
    `auth-button--${props.size}`,
    {
      'auth-button--loading': props.loading,
      'auth-button--disabled': props.disabled,
      'auth-button--block': props.block,
      'auth-button--round': props.round,
      'auth-button--plain': props.plain,
    },
  ]
})

const isClickable = computed(() => {
  return !props.loading && !props.disabled
})

// 方法
function handleClick(event: Event) {
  if (isClickable.value) {
    emit('click', event)
  }
}
</script>

<template>
  <button class="auth-button-wrapper" :class="buttonClass" @click="handleClick">
    <view class="auth-button-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="auth-button-loading">
        <text class="auth-button-spinner"></text>
        <text class="auth-button-text">{{ loadingText }}</text>
      </view>

      <!-- 正常状态 -->
      <view v-else class="auth-button-normal">
        <text v-if="icon" class="auth-button-icon" :class="icon"></text>
        <text class="auth-button-text">
          <slot></slot>
        </text>
      </view>
    </view>
  </button>
</template>

<style scoped lang="scss">
@import '@/style/auth-variables.scss';

.auth-button-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  font-weight: $font-weight-semibold;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all $transition-normal $ease-out-quart;
  @include button-reset;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    opacity: 0;
    transition: opacity $transition-normal ease;
  }

  &:active::before {
    opacity: 1;
  }

  &:active {
    transform: translateY(2rpx) scale(0.98);
  }

  &:not(.auth-button--loading):not(.auth-button--disabled):hover {
    transform: translateY(-4rpx);
  }
}

// 尺寸样式
.auth-button--small {
  height: $button-height-sm;
  padding: 0 $spacing-lg;
  font-size: $font-sm;
  border-radius: $radius-md;
}

.auth-button--medium {
  height: $button-height;
  padding: 0 $spacing-xl;
  font-size: $font-lg;
  border-radius: $radius-lg;
}

.auth-button--large {
  height: $button-height-lg;
  padding: 0 $spacing-2xl;
  font-size: $font-xl;
  border-radius: $radius-xl;
}

// 块级按钮
.auth-button--block {
  width: 100%;
}

// 圆角按钮
.auth-button--round {
  border-radius: $radius-full;
}

// 类型样式
.auth-button--primary {
  @include gradient-primary;
  color: $text-white;
  box-shadow: $shadow-primary;

  &:not(.auth-button--loading):not(.auth-button--disabled):hover {
    box-shadow: $shadow-lg;
  }

  &.auth-button--plain {
    background: transparent;
    color: $primary-color;
    border: 2rpx solid $primary-color;
    box-shadow: none;

    &:active {
      background: $primary-color;
      color: $text-white;
    }
  }
}

.auth-button--secondary {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  color: $text-primary;
  border: 2rpx solid $border-secondary;
  box-shadow: $shadow-sm;

  &:active {
    background: $bg-secondary;
  }

  &.auth-button--plain {
    background: transparent;
    border: 2rpx solid $text-secondary;
    color: $text-secondary;
  }
}

.auth-button--success {
  @include gradient-success;
  color: $text-white;
  box-shadow: $shadow-success;

  &.auth-button--plain {
    background: transparent;
    color: $success-color;
    border: 2rpx solid $success-color;
    box-shadow: none;
  }
}

.auth-button--warning {
  background: linear-gradient(135deg, #{$warning-color} 0%, #{$warning-dark} 100%);
  color: $text-white;
  box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.3);

  &.auth-button--plain {
    background: transparent;
    color: $warning-color;
    border: 2rpx solid $warning-color;
    box-shadow: none;
  }
}

.auth-button--error {
  @include gradient-error;
  color: $text-white;
  box-shadow: $shadow-error;

  &.auth-button--plain {
    background: transparent;
    color: $error-color;
    border: 2rpx solid $error-color;
    box-shadow: none;
  }
}

.auth-button--info {
  background: linear-gradient(135deg, #{$info-color} 0%, #{$info-dark} 100%);
  color: $text-white;
  box-shadow: 0 8rpx 32rpx rgba(33, 150, 243, 0.3);

  &.auth-button--plain {
    background: transparent;
    color: $info-color;
    border: 2rpx solid $info-color;
    box-shadow: none;
  }
}

// 状态样式
.auth-button--loading,
.auth-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;

  &:active {
    transform: none;
  }

  &:hover {
    transform: none;
    box-shadow: inherit;
  }
}

// 内容样式
.auth-button-content {
  @include flex-center;
  position: relative;
  z-index: 1;
}

.auth-button-loading,
.auth-button-normal {
  @include flex-center;
}

.auth-button-icon {
  margin-right: $spacing-sm;
  font-size: inherit;
}

.auth-button-text {
  font-size: inherit;
  font-weight: inherit;
}

.auth-button-spinner {
  width: 32rpx;
  height: 32rpx;
  margin-right: $spacing-sm;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid currentColor;
  border-radius: $radius-full;
  @include spin-animation;
}

// 响应式适配
@include mobile-small {
  .auth-button--small {
    height: 60rpx;
    font-size: $font-xs;
  }

  .auth-button--medium {
    height: 80rpx;
    font-size: $font-md;
  }

  .auth-button--large {
    height: 96rpx;
    font-size: $font-lg;
  }
}
</style>
