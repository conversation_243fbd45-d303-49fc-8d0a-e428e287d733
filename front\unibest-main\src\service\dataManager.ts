/**
 * 数据管理服务
 * 提供数据持久化、备份、恢复、验证等功能
 */

// @ts-expect-error - Type definition may not be available
import type { UserGrowthProfile } from '@/types/onboarding'
import { smartCache } from '@/utils/smartCache'
import { performanceMonitor } from '@/utils/performanceMonitor'

// 数据存储键名
export const STORAGE_KEYS = {
  USER_GROWTH_PROFILE: 'userGrowthProfile',
  USER_ABILITIES: 'userAbilities',
  LEARNING_BEHAVIORS: 'learningBehaviors',
  INITIAL_ASSESSMENT: 'hasCompletedInitialAssessment',
  ASSESSMENT_RESULT: 'initialAssessmentResult',
  CURRENT_USER: 'currentUser',
  ONBOARDING_SEEN: 'hasSeenOnboarding',
  ONBOARDING_SKIPPED: 'hasSkippedOnboarding',
  APP_SETTINGS: 'appSettings',
  DATA_BACKUP: 'dataBackup',
} as const

// 数据备份接口
export interface DataBackup {
  timestamp: string
  version: string
  data: {
    [key: string]: any
  }
  checksum?: string
}

// 数据验证结果
export interface DataValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

class DataManager {
  private readonly APP_VERSION = '1.0.0'
  private readonly MAX_BACKUPS = 5

  /**
   * @description 安全设置数据到本地存储
   * @param key 存储键名
   * @param data 要存储的数据
   * @param backup 是否创建备份
   */
  setData<T>(key: string, data: T, backup: boolean = true): boolean {
    performanceMonitor.startTiming('dataManager.setData')

    try {
      const serializedData = JSON.stringify(data)

      // 数据验证
      if (!this.validateDataSize(serializedData)) {
        console.warn('数据大小超限，存储失败')
        performanceMonitor.endTiming('dataManager.setData')
        return false
      }

      // 存储数据
      uni.setStorageSync(key, serializedData)

      // 同时更新智能缓存
      smartCache.set(key, data, {
        ttl: this.getTTLForKey(key),
        tags: this.getTagsForKey(key),
      })

      // 更新最后修改时间
      this.updateLastModified(key)

      // 创建备份
      if (backup) {
        this.createDataBackup()
      }

      performanceMonitor.endTiming('dataManager.setData')
      return true
    } catch (error) {
      console.error('数据存储失败:', error)
      performanceMonitor.endTiming('dataManager.setData')
      return false
    }
  }

  /**
   * @description 安全获取本地存储数据
   * @param key 存储键名
   * @param defaultValue 默认值
   */
  async getData<T>(key: string, defaultValue: T | null = null): Promise<T | null> {
    performanceMonitor.startTiming('dataManager.getData')

    try {
      // 首先尝试从智能缓存获取
      const cachedData = await smartCache.get<T>(key)
      if (cachedData !== null) {
        performanceMonitor.endTiming('dataManager.getData')
        performanceMonitor.incrementCounter('dataManager.cache-hits')
        return cachedData
      }

      // 缓存未命中，从本地存储获取
      const data = uni.getStorageSync(key)
      if (!data) {
        performanceMonitor.endTiming('dataManager.getData')
        performanceMonitor.incrementCounter('dataManager.cache-misses')
        return defaultValue
      }

      const parsedData = JSON.parse(data) as T

      // 将数据添加到缓存
      await smartCache.set(key, parsedData, {
        ttl: this.getTTLForKey(key),
        tags: this.getTagsForKey(key),
      })

      performanceMonitor.endTiming('dataManager.getData')
      performanceMonitor.incrementCounter('dataManager.cache-misses')
      return parsedData
    } catch (error) {
      console.error('数据读取失败:', error)
      performanceMonitor.endTiming('dataManager.getData')
      return defaultValue
    }
  }

  /**
   * @description 同步获取本地存储数据（不使用缓存）
   * @param key 存储键名
   * @param defaultValue 默认值
   */
  getDataSync<T>(key: string, defaultValue: T | null = null): T | null {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue

      return JSON.parse(data) as T
    } catch (error) {
      console.error('数据读取失败:', error)
      return defaultValue
    }
  }

  /**
   * @description 创建数据备份
   */
  createDataBackup(): boolean {
    try {
      const backup: DataBackup = {
        timestamp: new Date().toISOString(),
        version: this.APP_VERSION,
        data: {},
      }

      // 收集所有需要备份的数据 (使用同步方法避免异步问题)
      Object.values(STORAGE_KEYS).forEach((key) => {
        const data = this.getDataSync(key)
        if (data !== null) {
          backup.data[key] = data
        }
      })

      // 生成校验和
      backup.checksum = this.generateChecksum(backup.data)

      // 获取现有备份 (使用同步方法)
      const existingBackups = this.getDataSync<DataBackup[]>('dataBackupList', []) || []

      // 添加新备份
      existingBackups.push(backup)

      // 保持最大备份数量
      if (existingBackups.length > this.MAX_BACKUPS) {
        existingBackups.splice(0, existingBackups.length - this.MAX_BACKUPS)
      }

      // 保存备份列表
      uni.setStorageSync('dataBackupList', JSON.stringify(existingBackups))

      return true
    } catch (error) {
      console.error('数据备份失败:', error)
      return false
    }
  }

  /**
   * @description 获取存储使用情况
   */
  getStorageInfo(): { used: number; total: number; percentage: number } {
    try {
      const info = uni.getStorageInfoSync()
      return {
        used: info.currentSize,
        total: info.limitSize,
        percentage: Math.round((info.currentSize / info.limitSize) * 100),
      }
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return { used: 0, total: 0, percentage: 0 }
    }
  }

  /**
   * @description 数据完整性检查
   */
  performIntegrityCheck(): { isHealthy: boolean; issues: string[] } {
    const issues: string[] = []
    let isHealthy = true

    try {
      // 检查核心数据
      const coreKeys = [
        STORAGE_KEYS.USER_GROWTH_PROFILE,
        STORAGE_KEYS.USER_ABILITIES,
        STORAGE_KEYS.INITIAL_ASSESSMENT,
      ]

      for (const key of coreKeys) {
        const data = this.getDataSync(key)
        if (data === null && key !== STORAGE_KEYS.INITIAL_ASSESSMENT) {
          issues.push(`核心数据 ${key} 缺失`)
        }
      }

      // 检查存储空间
      const storageInfo = this.getStorageInfo()
      if (storageInfo.percentage > 90) {
        issues.push('存储空间使用率过高')
      }
    } catch (error) {
      isHealthy = false
      issues.push(`完整性检查异常: ${error.message}`)
    }

    return { isHealthy, issues }
  }

  /**
   * @description 验证数据大小
   */
  private validateDataSize(data: string): boolean {
    const sizeInBytes = new Blob([data]).size
    const maxSize = 1024 * 1024 // 1MB限制
    return sizeInBytes <= maxSize
  }

  /**
   * @description 更新最后修改时间
   */
  private updateLastModified(key: string): void {
    const modifiedTimes = this.getDataSync<Record<string, string>>('lastModifiedTimes', {}) || {}
    modifiedTimes[key] = new Date().toISOString()
    uni.setStorageSync('lastModifiedTimes', JSON.stringify(modifiedTimes))
  }

  /**
   * @description 生成数据校验和
   */
  private generateChecksum(data: any): string {
    const str = JSON.stringify(data, Object.keys(data).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  /**
   * @description 获取数据的TTL（生存时间）
   */
  private getTTLForKey(key: string): number {
    const ttlMap: Record<string, number> = {
      [STORAGE_KEYS.USER_ABILITIES]: 10 * 60 * 1000, // 10 minutes
      [STORAGE_KEYS.USER_GROWTH_PROFILE]: 30 * 60 * 1000, // 30 minutes
      [STORAGE_KEYS.CURRENT_USER]: 60 * 60 * 1000, // 1 hour
      [STORAGE_KEYS.LEARNING_BEHAVIORS]: 5 * 60 * 1000, // 5 minutes
      [STORAGE_KEYS.APP_SETTINGS]: 24 * 60 * 60 * 1000, // 24 hours
    }

    return ttlMap[key] || 15 * 60 * 1000 // Default 15 minutes
  }

  /**
   * @description 获取数据的标签用于缓存分组
   */
  private getTagsForKey(key: string): string[] {
    const tagMap: Record<string, string[]> = {
      [STORAGE_KEYS.USER_ABILITIES]: ['user', 'abilities'],
      [STORAGE_KEYS.USER_GROWTH_PROFILE]: ['user', 'profile'],
      [STORAGE_KEYS.CURRENT_USER]: ['user', 'basic'],
      [STORAGE_KEYS.LEARNING_BEHAVIORS]: ['user', 'behavior'],
      [STORAGE_KEYS.INITIAL_ASSESSMENT]: ['assessment'],
      [STORAGE_KEYS.ASSESSMENT_RESULT]: ['assessment'],
      [STORAGE_KEYS.ONBOARDING_SEEN]: ['onboarding'],
      [STORAGE_KEYS.ONBOARDING_SKIPPED]: ['onboarding'],
      [STORAGE_KEYS.APP_SETTINGS]: ['settings'],
    }

    return tagMap[key] || ['general']
  }

  /**
   * @description 清除特定标签的缓存
   */
  async clearCacheByTags(tags: string[]): Promise<void> {
    await smartCache.invalidateByTags(tags)
  }

  /**
   * @description 预热缓存
   */
  async warmCache(): Promise<void> {
    const criticalKeys = [
      STORAGE_KEYS.CURRENT_USER,
      STORAGE_KEYS.USER_ABILITIES,
      STORAGE_KEYS.USER_GROWTH_PROFILE,
    ]

    const warmEntries = []
    for (const key of criticalKeys) {
      const data = this.getDataSync(key)
      if (data !== null) {
        warmEntries.push({
          key,
          data,
          ttl: this.getTTLForKey(key),
          tags: this.getTagsForKey(key),
        })
      }
    }

    if (warmEntries.length > 0) {
      await smartCache.warmCache(warmEntries)
    }
  }

  /**
   * @description 获取缓存统计信息
   */
  getCacheStats() {
    return smartCache.getStats()
  }

  /**
   * @description 清理过期缓存
   */
  cleanupExpiredCache(): number {
    return smartCache.cleanup()
  }
}

// 导出单例实例
export const dataManager = new DataManager()

// 导出工具函数
export { DataManager }
