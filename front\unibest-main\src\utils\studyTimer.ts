/**
 * @description 用户活动时长记录工具
 * <AUTHOR>
 */

import { httpGet, httpPost } from './http'

// 活动类型枚举
export enum ActivityType {
  COURSE = 'course', // 课程学习
  INTERVIEW = 'interview', // 面试练习
  BOOK = 'book', // 书籍阅读
  VIDEO = 'video', // 视频学习
  EXERCISE = 'exercise', // 习题练习
  DOCUMENT = 'document', // 文档阅读
  OTHER = 'other', // 其他活动
}

// API接口地址
const ACTIVITY_API_ENDPOINTS = {
  START_ACTIVITY: '/api/activity/start',
  PAUSE_ACTIVITY: '/api/activity/pause',
  END_ACTIVITY: '/api/activity/end',
  GET_STATISTICS: '/api/activity/statistics',
  GET_HISTORY: '/api/activity/history',
  CLEAR_RECORDS: '/api/activity/clear',
  SYNC_SESSION: '/api/activity/sync',
}

// 演示数据
const DEMO_STATISTICS = {
  today: 3600000, // 1小时
  week: 18000000, // 5小时
  month: 72000000, // 20小时
  total: 360000000, // 100小时
  todayFormatted: '1小时0分钟',
  weekFormatted: '5小时0分钟',
  monthFormatted: '20小时0分钟',
  totalFormatted: '100小时0分钟',
  byType: {
    [ActivityType.COURSE]: { total: 144000000, totalFormatted: '40小时0分钟' },
    [ActivityType.INTERVIEW]: { total: 72000000, totalFormatted: '20小时0分钟' },
    [ActivityType.BOOK]: { total: 54000000, totalFormatted: '15小时0分钟' },
    [ActivityType.VIDEO]: { total: 36000000, totalFormatted: '10小时0分钟' },
    [ActivityType.EXERCISE]: { total: 36000000, totalFormatted: '10小时0分钟' },
    [ActivityType.DOCUMENT]: { total: 18000000, totalFormatted: '5小时0分钟' },
    [ActivityType.OTHER]: { total: 0, totalFormatted: '0分钟' },
  }
}

const DEMO_HISTORY: ActivitySession[] = [
  {
    id: '1',
    type: ActivityType.COURSE,
    startTime: Date.now() - 7200000,
    endTime: Date.now() - 3600000,
    duration: 3600000,
    isActive: false,
    activityName: '前端开发基础',
    categoryName: '第一章'
  },
  {
    id: '2',
    type: ActivityType.INTERVIEW,
    startTime: Date.now() - 10800000,
    endTime: Date.now() - 9000000,
    duration: 1800000,
    isActive: false,
    activityName: 'JavaScript面试',
    categoryName: '基础题'
  }
]

// 活动会话接口
export interface ActivitySession {
  id: string // 会话ID
  type: ActivityType // 活动类型
  startTime: number // 开始时间戳
  endTime?: number // 结束时间戳
  duration?: number // 持续时长（毫秒）
  isActive: boolean // 是否活跃

  // 通用活动信息
  activityId?: string // 活动ID
  activityName?: string // 活动名称

  // 分类信息
  categoryId?: string // 分类ID
  categoryName?: string // 分类名称

  // 额外信息，可以存储不同活动类型的特定数据
  metadata?: Record<string, any>
}

// 活动记录接口
export interface ActivityRecord {
  totalDuration: number // 总活动时长（毫秒）
  dailyRecords: Record<string, number> // 每日活动记录，key为日期（YYYY-MM-DD），value为时长
  typeRecords: Record<ActivityType, number> // 按类型统计的时长
  sessions: ActivitySession[] // 活动会话记录
}

/**
 * 活动时长记录器类
 */
export class ActivityTimer {
  private static instance: ActivityTimer
  private readonly storageKeyPrefix = 'user_activity_records'
  private currentSession: ActivitySession | null = null
  private heartbeatInterval: number | null = null
  private idleTimeout: number | null = null
  private idleThreshold = 5 * 60 * 1000 // 5分钟无活动视为空闲
  private heartbeatFrequency = 30 * 1000 // 30秒保存一次
  private userId: number = 1 // 默认用户ID

  /**
   * 获取单例实例
   */
  public static getInstance(): ActivityTimer {
    if (!ActivityTimer.instance) {
      ActivityTimer.instance = new ActivityTimer()
    }
    return ActivityTimer.instance
  }

  /**
   * 构造函数
   * 注意：为了支持继承，不再使用private修饰符
   */
  protected constructor() {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
    // 监听用户活动
    document.addEventListener('mousemove', this.resetIdleTimer.bind(this))
    document.addEventListener('keydown', this.resetIdleTimer.bind(this))
    document.addEventListener('click', this.resetIdleTimer.bind(this))
    document.addEventListener('scroll', this.resetIdleTimer.bind(this))
    // 监听页面卸载
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this))
  }

  /**
   * 设置用户ID，用于多用户环境
   * @param userId 用户ID
   */
  public setUserId(userId: string): void {
    if (userId && userId !== this.userId) {
      this.userId = userId
      // 如果切换用户，结束当前会话
      if (this.currentSession && this.currentSession.isActive) {
        this.pauseActivity()
      }
    }
  }

  /**
   * 获取当前存储键
   */
  private getStorageKey(): string {
    return `${this.storageKeyPrefix}_${this.userId}`
  }

  /**
   * 开始记录活动时长
   * @param type 活动类型
   * @param activityInfo 活动信息
   */
  public async startActivity(
    type: ActivityType,
    activityInfo?: {
      activityId?: string
      activityName?: string
      categoryId?: string
      categoryName?: string
      metadata?: Record<string, any>
    },
  ): Promise<void> {
    try {
      // 如果已经有活跃会话，先结束它
      if (this.currentSession && this.currentSession.isActive) {
        await this.pauseActivity()
      }

      // 创建新的活动会话
      this.currentSession = {
        id: this.generateSessionId(),
        type,
        startTime: Date.now(),
        isActive: true,
        ...activityInfo,
      }

      // 调用后端API开始活动
      await httpPost(ACTIVITY_API_ENDPOINTS.START_ACTIVITY, {
        sessionId: this.currentSession.id,
        type,
        userId: this.userId,
        ...activityInfo,
      })

      // 开始心跳检测
      this.startHeartbeat()
      // 重置空闲计时器
      this.resetIdleTimer()

      console.log(`活动计时开始 - ${type}`, this.currentSession)
    } catch (error) {
      console.warn('开始活动API调用失败，使用本地存储:', error)
      // 降级到本地存储
      this.startActivityLocal(type, activityInfo)
    }
  }

  /**
   * 暂停记录活动时长
   */
  public async pauseActivity(): Promise<void> {
    if (this.currentSession && this.currentSession.isActive) {
      const now = Date.now()
      const duration = now - this.currentSession.startTime

      try {
        // 调用后端API暂停活动
        await httpPost(ACTIVITY_API_ENDPOINTS.PAUSE_ACTIVITY, {
          sessionId: this.currentSession.id,
          duration,
          userId: this.userId,
        })
      } catch (error) {
        console.warn('暂停活动API调用失败，使用本地存储:', error)
      }

      // 更新当前会话
      this.currentSession.endTime = now
      this.currentSession.duration = duration
      this.currentSession.isActive = false

      // 保存到本地存储作为备份
      this.saveSession(this.currentSession)

      // 清除心跳和空闲计时器
      this.clearTimers()

      console.log(`活动计时暂停 - ${this.currentSession.type}`, this.currentSession)
    }
  }

  /**
   * 继续记录活动时长
   */
  public resumeActivity(): void {
    if (this.currentSession && !this.currentSession.isActive) {
      // 创建新的会话，保留之前的活动信息
      const { type, activityId, activityName, categoryId, categoryName, metadata } =
        this.currentSession
      this.startActivity(type, { activityId, activityName, categoryId, categoryName, metadata })
    }
  }

  /**
   * 结束记录活动时长
   */
  public async endActivity(): Promise<void> {
    try {
      if (this.currentSession) {
        await httpPost(ACTIVITY_API_ENDPOINTS.END_ACTIVITY, {
          sessionId: this.currentSession.id,
          userId: this.userId,
        })
      }
    } catch (error) {
      console.warn('结束活动API调用失败:', error)
    }

    await this.pauseActivity()
    this.currentSession = null
  }

  /**
   * 切换活动
   * @param type 活动类型
   * @param activityInfo 活动信息
   */
  public switchActivity(
    type: ActivityType,
    activityInfo?: {
      activityId?: string
      activityName?: string
      categoryId?: string
      categoryName?: string
      metadata?: Record<string, any>
    },
  ): void {
    // 结束当前会话
    this.pauseActivity()
    // 开始新会话
    this.startActivity(type, activityInfo)
  }

  /**
   * 获取今日活动时长（毫秒）
   * @param type 可选，指定活动类型
   */
  public getTodayDuration(type?: ActivityType): number {
    const records = this.getActivityRecords()
    const today = this.getDateString(new Date())

    if (!type) {
      return records.dailyRecords[today] || 0
    }

    // 按类型筛选今日会话
    return this.filterSessionsByDate(records.sessions, today)
      .filter((session) => session.type === type)
      .reduce((total, session) => total + (session.duration || 0), 0)
  }

  /**
   * 获取本周活动时长（毫秒）
   * @param type 可选，指定活动类型
   */
  public getWeekDuration(type?: ActivityType): number {
    const records = this.getActivityRecords()
    const now = new Date()
    const dayOfWeek = now.getDay() || 7 // 转换为周一为1，周日为7

    // 计算本周开始日期（周一）
    const weekStart = new Date(now)
    weekStart.setDate(now.getDate() - dayOfWeek + 1)

    if (!type) {
      // 计算所有类型的总时长
      let totalDuration = 0

      // 遍历本周每一天
      for (let i = 0; i < 7; i++) {
        const date = new Date(weekStart)
        date.setDate(weekStart.getDate() + i)
        const dateStr = this.getDateString(date)
        totalDuration += records.dailyRecords[dateStr] || 0
      }

      return totalDuration
    } else {
      // 按类型筛选本周会话
      return records.sessions
        .filter((session) => {
          if (!session.duration) return false
          const sessionDate = new Date(session.startTime)
          const diffDays = Math.floor(
            (now.getTime() - sessionDate.getTime()) / (24 * 60 * 60 * 1000),
          )
          return diffDays < 7 && session.type === type
        })
        .reduce((total, session) => total + (session.duration || 0), 0)
    }
  }

  /**
   * 获取本月活动时长（毫秒）
   * @param type 可选，指定活动类型
   */
  public getMonthDuration(type?: ActivityType): number {
    const records = this.getActivityRecords()
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth()

    if (!type) {
      // 计算所有类型的总时长
      let totalDuration = 0

      // 遍历本月每一天
      const daysInMonth = new Date(year, month + 1, 0).getDate()
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day)
        const dateStr = this.getDateString(date)
        totalDuration += records.dailyRecords[dateStr] || 0
      }

      return totalDuration
    } else {
      // 按类型筛选本月会话
      return records.sessions
        .filter((session) => {
          if (!session.duration) return false
          const sessionDate = new Date(session.startTime)
          return (
            sessionDate.getFullYear() === year &&
            sessionDate.getMonth() === month &&
            session.type === type
          )
        })
        .reduce((total, session) => total + (session.duration || 0), 0)
    }
  }

  /**
   * 获取总活动时长（毫秒）
   * @param type 可选，指定活动类型
   */
  public getTotalDuration(type?: ActivityType): number {
    const records = this.getActivityRecords()

    if (!type) {
      return records.totalDuration
    }

    return records.typeRecords[type] || 0
  }

  /**
   * 按活动类型获取统计数据
   * @param type 活动类型
   */
  public async getStatsByType(type: ActivityType): Promise<{
    today: number
    week: number
    month: number
    total: number
    todayFormatted: string
    weekFormatted: string
    monthFormatted: string
    totalFormatted: string
  }> {
    try {
      const response = await httpGet(ACTIVITY_API_ENDPOINTS.GET_STATISTICS, {
        userId: this.userId,
        type,
      })

      if (response.code === 200 && response.data) {
        return response.data
      }
      throw new Error('API返回数据格式错误')
    } catch (error) {
      console.warn('获取类型统计数据API调用失败，使用演示数据:', error)
      const typeStats = DEMO_STATISTICS.byType[type]
      return {
        today: Math.floor(typeStats.total * 0.05),
        week: Math.floor(typeStats.total * 0.2),
        month: Math.floor(typeStats.total * 0.6),
        total: typeStats.total,
        todayFormatted: this.formatDuration(Math.floor(typeStats.total * 0.05)),
        weekFormatted: this.formatDuration(Math.floor(typeStats.total * 0.2)),
        monthFormatted: this.formatDuration(Math.floor(typeStats.total * 0.6)),
        totalFormatted: typeStats.totalFormatted,
      }
    }
  }

  /**
   * 格式化时长为可读字符串
   * @param duration 时长（毫秒）
   * @returns 格式化后的字符串，如 "2小时30分钟"
   */
  public formatDuration(duration: number): string {
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟`
    } else {
      return `${seconds}秒`
    }
  }

  /**
   * 获取活动记录统计数据
   */
  public async getStatistics(): Promise<{
    today: number
    week: number
    month: number
    total: number
    todayFormatted: string
    weekFormatted: string
    monthFormatted: string
    totalFormatted: string
    byType: Record<
      ActivityType,
      {
        total: number
        totalFormatted: string
      }
    >
  }> {
    try {
      const response = await httpGet(ACTIVITY_API_ENDPOINTS.GET_STATISTICS, {
        userId: this.userId,
      })

      if (response.code === 200 && response.data) {
        return response.data
      }
      throw new Error('API返回数据格式错误')
    } catch (error) {
      console.warn('获取统计数据API调用失败，使用演示数据:', error)
      return DEMO_STATISTICS
    }
  }

  /**
   * 获取活动历史记录
   * @param options 查询选项
   */
  public async getHistory(options?: {
    type?: ActivityType
    startDate?: Date
    endDate?: Date
    limit?: number
  }): Promise<ActivitySession[]> {
    try {
      const response = await httpGet(ACTIVITY_API_ENDPOINTS.GET_HISTORY, {
        userId: this.userId,
        ...options,
        startDate: options?.startDate?.toISOString(),
        endDate: options?.endDate?.toISOString(),
      })

      if (response.code === 200 && response.data) {
        return response.data.sessions || []
      }
      throw new Error('API返回数据格式错误')
    } catch (error) {
      console.warn('获取历史记录API调用失败，使用演示数据:', error)
      let sessions = [...DEMO_HISTORY]

      // 按类型筛选
      if (options?.type) {
        sessions = sessions.filter((session) => session.type === options.type)
      }

      // 按数量限制
      if (options?.limit) {
        sessions = sessions.slice(0, options.limit)
      }

      return sessions
    }
  }

  /**
   * 清除所有活动记录
   * @param type 可选，指定清除特定类型的记录
   */
  public async clearRecords(type?: ActivityType): Promise<void> {
    try {
      await httpPost(ACTIVITY_API_ENDPOINTS.CLEAR_RECORDS, {
        userId: this.userId,
        type,
      })
    } catch (error) {
      console.warn('清除记录API调用失败，使用本地清除:', error)
    }

    // 同时清除本地存储
    if (!type) {
      localStorage.removeItem(this.getStorageKey())
    } else {
      // 本地清除特定类型记录的逻辑
      this.clearLocalRecords(type)
    }
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      // 页面不可见时暂停计时
      if (this.currentSession && this.currentSession.isActive) {
        this.pauseActivity()
      }
    } else {
      // 页面重新可见时恢复计时
      if (this.currentSession && !this.currentSession.isActive) {
        this.resumeActivity()
      }
    }
  }

  /**
   * 处理页面卸载事件
   */
  private handleBeforeUnload(): void {
    // 保存当前会话
    if (this.currentSession && this.currentSession.isActive) {
      this.pauseActivity()
    }
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.clearTimers()
    this.heartbeatInterval = window.setInterval(() => {
      this.syncSession()
    }, this.heartbeatFrequency)
  }

  /**
   * 重置空闲计时器
   */
  private resetIdleTimer(): void {
    if (this.idleTimeout) {
      clearTimeout(this.idleTimeout)
    }

    if (this.currentSession && this.currentSession.isActive) {
      this.idleTimeout = window.setTimeout(() => {
        // 空闲时间过长，暂停计时
        console.log('用户空闲，暂停活动计时')
        this.pauseActivity()
      }, this.idleThreshold)
    }
  }

  /**
   * 清除所有计时器
   */
  private clearTimers(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.idleTimeout) {
      clearTimeout(this.idleTimeout)
      this.idleTimeout = null
    }
  }

  /**
   * 保存会话记录
   * @param session 会话记录
   * @param isComplete 是否为完整会话（已结束）
   */
  private saveSession(session: ActivitySession, isComplete = true): void {
    if (!session.duration || session.duration < 1000) {
      // 忽略时长小于1秒的会话
      return
    }

    const records = this.getActivityRecords()
    const dateStr = this.getDateString(new Date(session.startTime))

    // 更新总时长
    records.totalDuration += session.duration

    // 更新类型时长
    if (!records.typeRecords[session.type]) {
      records.typeRecords[session.type] = 0
    }
    records.typeRecords[session.type] += session.duration

    // 更新日期记录
    if (!records.dailyRecords[dateStr]) {
      records.dailyRecords[dateStr] = 0
    }
    records.dailyRecords[dateStr] += session.duration

    // 保存会话记录
    if (isComplete) {
      records.sessions.push(session)
      // 只保留最近100条记录
      if (records.sessions.length > 100) {
        records.sessions.shift()
      }
    }

    // 保存到本地存储
    localStorage.setItem(this.getStorageKey(), JSON.stringify(records))
  }

  /**
   * 获取活动记录
   */
  private getActivityRecords(): ActivityRecord {
    const recordsStr = localStorage.getItem(this.getStorageKey())
    if (recordsStr) {
      try {
        return JSON.parse(recordsStr)
      } catch (e) {
        console.error('解析活动记录失败', e)
      }
    }

    // 返回默认记录
    return {
      totalDuration: 0,
      dailyRecords: {},
      typeRecords: {} as Record<ActivityType, number>,
      sessions: [],
    }
  }

  /**
   * 按日期筛选会话
   * @param sessions 会话列表
   * @param dateStr 日期字符串 YYYY-MM-DD
   */
  private filterSessionsByDate(sessions: ActivitySession[], dateStr: string): ActivitySession[] {
    return sessions.filter((session) => {
      const sessionDate = this.getDateString(new Date(session.startTime))
      return sessionDate === dateStr
    })
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  /**
   * 获取日期字符串（YYYY-MM-DD）
   */
  private getDateString(date: Date): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  /**
   * 同步会话到服务器（心跳）
   */
  private async syncSession(): Promise<void> {
    if (this.currentSession && this.currentSession.isActive) {
      try {
        const tempSession = { ...this.currentSession }
        tempSession.endTime = Date.now()
        tempSession.duration = tempSession.endTime - tempSession.startTime

        await httpPost(ACTIVITY_API_ENDPOINTS.SYNC_SESSION, {
          sessionId: tempSession.id,
          duration: tempSession.duration,
          userId: this.userId,
        })
      } catch (error) {
        console.warn('同步会话失败:', error)
        // 保存到本地作为备份
        if (this.currentSession) {
          const tempSession = { ...this.currentSession }
          tempSession.endTime = Date.now()
          tempSession.duration = tempSession.endTime - tempSession.startTime
          this.saveSession(tempSession, false)
        }
      }
    }
  }

  private startActivityLocal(
    type: ActivityType,
    activityInfo?: {
      activityId?: string
      activityName?: string
      categoryId?: string
      categoryName?: string
      metadata?: Record<string, any>
    },
  ): void {
    // 本地开始活动的逻辑
    this.currentSession = {
      id: this.generateSessionId(),
      type,
      startTime: Date.now(),
      isActive: true,
      ...activityInfo,
    }

    this.startHeartbeat()
    this.resetIdleTimer()
  }

  private clearLocalRecords(type: ActivityType): void {
    const records = this.getActivityRecords()
    records.sessions = records.sessions.filter((session) => session.type !== type)
    records.typeRecords[type] = 0
    
    // 重新计算相关统计
    records.totalDuration = records.sessions.reduce(
      (total, session) => total + (session.duration || 0),
      0,
    )

    localStorage.setItem(this.getStorageKey(), JSON.stringify(records))
  }
}

// 导出单例实例
export const activityTimer = ActivityTimer.getInstance()

// 为了向后兼容，保留原有的 StudyTimer 类和实例
/**
 * @deprecated 使用 ActivityTimer 替代
 */
export class StudyTimer extends ActivityTimer {
  private static studyInstance: StudyTimer

  /**
   * 获取单例实例
   */
  public static getInstance(): StudyTimer {
    if (!StudyTimer.studyInstance) {
      StudyTimer.studyInstance = new StudyTimer()
    }
    return StudyTimer.studyInstance
  }

  /**
   * 开始记录学习时长
   * @param courseInfo 课程信息
   */
  public startStudy(courseInfo?: {
    courseId?: string
    courseName?: string
    chapterId?: string
    chapterName?: string
  }): void {
    this.startActivity(ActivityType.COURSE, {
      activityId: courseInfo?.courseId,
      activityName: courseInfo?.courseName,
      categoryId: courseInfo?.chapterId,
      categoryName: courseInfo?.chapterName,
    })
  }

  /**
   * 暂停记录学习时长
   */
  public pauseStudy(): void {
    this.pauseActivity()
  }

  /**
   * 继续记录学习时长
   */
  public resumeStudy(): void {
    this.resumeActivity()
  }

  /**
   * 结束记录学习时长
   */
  public endStudy(): void {
    this.endActivity()
  }

  /**
   * 切换课程/章节
   * @param courseInfo 新的课程信息
   */
  public switchCourse(courseInfo: {
    courseId?: string
    courseName?: string
    chapterId?: string
    chapterName?: string
  }): void {
    this.switchActivity(ActivityType.COURSE, {
      activityId: courseInfo?.courseId,
      activityName: courseInfo?.courseName,
      categoryId: courseInfo?.chapterId,
      categoryName: courseInfo?.chapterName,
    })
  }

  /**
   * 清除所有学习记录
   */
  public clearAllRecords(): void {
    this.clearRecords(ActivityType.COURSE)
  }
}

// 导出单例实例
export const studyTimer = StudyTimer.getInstance()




