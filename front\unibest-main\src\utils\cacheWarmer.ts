/**
 * Cache Warmer
 * Proactively loads and caches critical data to improve perceived performance
 */

import { smartCache } from './smartCache'
import { dataLoader } from './dataLoader'
import { performanceMonitor } from './performanceMonitor'
import { dataManager } from '@/service/dataManager'

interface WarmupStrategy {
  key: string
  priority: number
  loader: () => Promise<any>
  ttl?: number
  tags?: string[]
  condition?: () => boolean
}

class CacheWarmer {
  private strategies: WarmupStrategy[] = []
  private isWarming = false
  private warmupPromise: Promise<void> | null = null

  constructor() {
    this.initializeStrategies()
  }

  /**
   * Start cache warming process
   */
  async warmCache(): Promise<void> {
    if (this.isWarming) {
      return this.warmupPromise || Promise.resolve()
    }

    this.isWarming = true
    performanceMonitor.startTiming('cacheWarmer.warmCache')

    this.warmupPromise = this.executeWarmup()

    try {
      await this.warmupPromise
      performanceMonitor.endTiming('cacheWarmer.warmCache')
      performanceMonitor.incrementCounter('cacheWarmer.success')
    } catch (error) {
      performanceMonitor.endTiming('cacheWarmer.warmCache')
      performanceMonitor.incrementCounter('cacheWarmer.error')
      console.error('Cache warming failed:', error)
    } finally {
      this.isWarming = false
      this.warmupPromise = null
    }
  }

  /**
   * Warm specific cache keys
   */
  async warmSpecific(keys: string[]): Promise<void> {
    const strategies = this.strategies.filter((s) => keys.includes(s.key))
    await this.executeStrategies(strategies)
  }

  /**
   * Add custom warming strategy
   */
  addStrategy(strategy: WarmupStrategy): void {
    this.strategies.push(strategy)
    // Sort by priority (higher priority first)
    this.strategies.sort((a, b) => b.priority - a.priority)
  }

  /**
   * Remove warming strategy
   */
  removeStrategy(key: string): void {
    this.strategies = this.strategies.filter((s) => s.key !== key)
  }

  /**
   * Check if cache warming is in progress
   */
  isWarmingInProgress(): boolean {
    return this.isWarming
  }

  /**
   * Get warming strategies
   */
  getStrategies(): WarmupStrategy[] {
    return [...this.strategies]
  }

  private async executeWarmup(): Promise<void> {
    // Filter strategies based on conditions
    const activeStrategies = this.strategies.filter(
      (strategy) => !strategy.condition || strategy.condition(),
    )

    await this.executeStrategies(activeStrategies)
  }

  private async executeStrategies(strategies: WarmupStrategy[]): Promise<void> {
    // Group strategies by priority
    const priorityGroups = new Map<number, WarmupStrategy[]>()

    for (const strategy of strategies) {
      if (!priorityGroups.has(strategy.priority)) {
        priorityGroups.set(strategy.priority, [])
      }
      priorityGroups.get(strategy.priority)!.push(strategy)
    }

    // Execute strategies by priority (highest first)
    const sortedPriorities = Array.from(priorityGroups.keys()).sort((a, b) => b - a)

    for (const priority of sortedPriorities) {
      const group = priorityGroups.get(priority)!

      // Execute strategies in the same priority group in parallel
      const promises = group.map((strategy) => this.executeStrategy(strategy))
      await Promise.allSettled(promises)
    }
  }

  private async executeStrategy(strategy: WarmupStrategy): Promise<void> {
    try {
      performanceMonitor.startTiming(`cacheWarmer.${strategy.key}`)

      // Check if already cached and fresh
      if (await smartCache.has(strategy.key)) {
        performanceMonitor.endTiming(`cacheWarmer.${strategy.key}`)
        performanceMonitor.incrementCounter('cacheWarmer.already-cached')
        return
      }

      // Load data
      const data = await strategy.loader()

      // Cache the data
      await smartCache.set(strategy.key, data, {
        ttl: strategy.ttl,
        tags: strategy.tags,
      })

      performanceMonitor.endTiming(`cacheWarmer.${strategy.key}`)
      performanceMonitor.incrementCounter('cacheWarmer.strategy-success')

      console.log(`Cache warmed for: ${strategy.key}`)
    } catch (error) {
      performanceMonitor.endTiming(`cacheWarmer.${strategy.key}`)
      performanceMonitor.incrementCounter('cacheWarmer.strategy-error')
      console.warn(`Failed to warm cache for ${strategy.key}:`, error)
    }
  }

  private initializeStrategies(): void {
    // Critical dashboard data (highest priority)
    this.addStrategy({
      key: 'dashboard.critical',
      priority: 100,
      loader: () => dataLoader.loadCriticalData({ priority: 'high' }),
      ttl: 5 * 60 * 1000, // 5 minutes
      tags: ['dashboard', 'critical'],
      condition: () => true, // Always load critical data
    })

    // User abilities data
    this.addStrategy({
      key: 'dashboard.abilities',
      priority: 80,
      loader: async () => {
        const { getUserAbilities } = await import('@/service/index/dashboard')
        const result = await getUserAbilities()
        return result.data
      },
      ttl: 10 * 60 * 1000, // 10 minutes
      tags: ['dashboard', 'abilities'],
      condition: () => this.hasCompletedAssessment(),
    })

    // Study stats data
    this.addStrategy({
      key: 'dashboard.stats',
      priority: 70,
      loader: async () => {
        const { getStudyStats } = await import('@/service/index/dashboard')
        const result = await getStudyStats()
        return result.data
      },
      ttl: 5 * 60 * 1000, // 5 minutes
      tags: ['dashboard', 'stats'],
    })

    // Smart tasks data
    this.addStrategy({
      key: 'dashboard.tasks',
      priority: 60,
      loader: async () => {
        const { getSmartTasks } = await import('@/service/index/dashboard')
        const result = await getSmartTasks({ params: { limit: 3 } })
        return result.data
      },
      ttl: 15 * 60 * 1000, // 15 minutes
      tags: ['dashboard', 'tasks'],
    })

    // User growth profile (lower priority)
    this.addStrategy({
      key: 'user.growth-profile',
      priority: 50,
      loader: () => dataManager.getData('userGrowthProfile'),
      ttl: 30 * 60 * 1000, // 30 minutes
      tags: ['user', 'profile'],
      condition: () => this.hasCompletedAssessment(),
    })

    // Learning plans data
    this.addStrategy({
      key: 'learning.plans',
      priority: 40,
      loader: () => {
        const plans = uni.getStorageSync('studyPlans')
        return plans ? JSON.parse(plans) : []
      },
      ttl: 10 * 60 * 1000, // 10 minutes
      tags: ['learning', 'plans'],
    })

    // App settings (lowest priority)
    this.addStrategy({
      key: 'app.settings',
      priority: 10,
      loader: () => dataManager.getData('appSettings', {}),
      ttl: 60 * 60 * 1000, // 1 hour
      tags: ['settings'],
    })
  }

  private hasCompletedAssessment(): boolean {
    try {
      const completed = uni.getStorageSync('hasCompletedInitialAssessment')
      return !!completed
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const cacheWarmer = new CacheWarmer()

// Export class for testing
export { CacheWarmer }
