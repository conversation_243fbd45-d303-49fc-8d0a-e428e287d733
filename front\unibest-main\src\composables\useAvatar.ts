/**
 * 数字人管理组合式函数
 * <AUTHOR>
 * @date 2025-07-20
 */

import { ref, reactive, onUnmounted } from 'vue'
import {
  startAvatarSession,
  sendTextDriver,
  sendTextInteract,
  sendAudioDriver,
  resetAvatar,
  stopAvatarSession,
  sendHeartbeat,
  sendAction,
  getSessionInfo,
  checkSessionStatus,
  getStreamUrl,
  type AvatarStartRequest,
  type AvatarTextRequest,
  type AvatarSession,
  type IResData,
} from '@/service/avatar'

export interface AvatarState {
  // 会话状态
  sessionId: string
  isConnected: boolean
  isLoading: boolean
  error: string
  
  // 数字人信息
  avatarId: string
  streamUrl: string
  
  // 控制状态
  isSpeaking: boolean
  isListening: boolean
  
  // 配置信息
  config: {
    width: number
    height: number
    vcn: string
    speed: number
    pitch: number
    volume: number
  }
}

export function useAvatar() {
  // 数字人状态
  const avatarState = reactive<AvatarState>({
    sessionId: '',
    isConnected: false,
    isLoading: false,
    error: '',
    avatarId: '',
    streamUrl: '',
    isSpeaking: false,
    isListening: false,
    config: {
      width: 720,
      height: 1280,
      vcn: 'x4_lingxiaoying_assist',
      speed: 50,
      pitch: 50,
      volume: 50,
    },
  })

  // 心跳定时器
  let heartbeatTimer: number | null = null
  // 推流地址检查定时器
  let streamUrlCheckTimer: number | null = null

  /**
   * 启动数字人会话
   */
  const startAvatar = async (params?: Partial<AvatarStartRequest>) => {
    try {
      avatarState.isLoading = true
      avatarState.error = ''

      const startParams: AvatarStartRequest = {
        width: avatarState.config.width,
        height: avatarState.config.height,
        vcn: avatarState.config.vcn,
        timeout: 30,
        ...params,
      }

      const response = await startAvatarSession(startParams)

      if (response.code === 200 && response.data) {
        const session = response.data
        avatarState.sessionId = session.sessionId
        avatarState.avatarId = session.avatarId
        avatarState.streamUrl = session.streamUrl || ''
        avatarState.isConnected = session.connected
        
        // 启动心跳
        startHeartbeat()

        // 如果没有推流地址，启动定期检查
        if (!avatarState.streamUrl) {
          startStreamUrlCheck()
        }

        console.log('数字人启动成功:', session)
        return session
      } else {
        throw new Error(response.message || '启动数字人失败')
      }
    } catch (error: any) {
      avatarState.error = error.message || '启动数字人失败'
      console.error('启动数字人失败:', error)
      throw error
    } finally {
      avatarState.isLoading = false
    }
  }

  /**
   * 发送文本让数字人播报
   */
  const speakText = async (text: string, options?: Partial<AvatarTextRequest>) => {
    if (!avatarState.sessionId || !avatarState.isConnected) {
      throw new Error('数字人未连接')
    }

    try {
      avatarState.isSpeaking = true
      
      const textParams: AvatarTextRequest = {
        text,
        vcn: avatarState.config.vcn,
        speed: avatarState.config.speed,
        pitch: avatarState.config.pitch,
        volume: avatarState.config.volume,
        ...options,
      }

      const response = await sendTextDriver(avatarState.sessionId, textParams)
      
      if (response.code !== 200) {
        throw new Error(response.message || '发送文本失败')
      }
      
      console.log('文本发送成功:', text)
      
      // 模拟播报时间（实际应该根据文本长度和语速计算）
      const speakDuration = Math.max(2000, text.length * 100)
      setTimeout(() => {
        avatarState.isSpeaking = false
      }, speakDuration)
      
    } catch (error: any) {
      avatarState.isSpeaking = false
      console.error('发送文本失败:', error)
      throw error
    }
  }

  /**
   * 发送交互文本（会走大模型理解）
   */
  const interactText = async (text: string, options?: Partial<AvatarTextRequest>) => {
    if (!avatarState.sessionId || !avatarState.isConnected) {
      throw new Error('数字人未连接')
    }

    try {
      avatarState.isSpeaking = true
      
      const textParams: AvatarTextRequest = {
        text,
        vcn: avatarState.config.vcn,
        speed: avatarState.config.speed,
        pitch: avatarState.config.pitch,
        volume: avatarState.config.volume,
        ...options,
      }

      const response = await sendTextInteract(avatarState.sessionId, textParams)
      
      if (response.code !== 200) {
        throw new Error(response.message || '发送交互文本失败')
      }
      
      console.log('交互文本发送成功:', text)
      
      // 交互模式播报时间会更长
      const speakDuration = Math.max(3000, text.length * 150)
      setTimeout(() => {
        avatarState.isSpeaking = false
      }, speakDuration)
      
    } catch (error: any) {
      avatarState.isSpeaking = false
      console.error('发送交互文本失败:', error)
      throw error
    }
  }

  /**
   * 发送音频数据
   */
  const sendAudio = async (audioData: string, status: number) => {
    if (!avatarState.sessionId || !avatarState.isConnected) {
      throw new Error('数字人未连接')
    }

    try {
      const response = await sendAudioDriver(avatarState.sessionId, audioData, status)
      
      if (response.code !== 200) {
        throw new Error(response.message || '发送音频失败')
      }
      
      console.log('音频发送成功, status:', status)
      
    } catch (error: any) {
      console.error('发送音频失败:', error)
      throw error
    }
  }

  /**
   * 重置数字人状态
   */
  const resetAvatarState = async () => {
    if (!avatarState.sessionId || !avatarState.isConnected) {
      return
    }

    try {
      const response = await resetAvatar(avatarState.sessionId)
      
      if (response.code !== 200) {
        throw new Error(response.message || '重置数字人失败')
      }
      
      avatarState.isSpeaking = false
      console.log('数字人重置成功')
      
    } catch (error: any) {
      console.error('重置数字人失败:', error)
      throw error
    }
  }

  /**
   * 停止数字人会话
   */
  const stopAvatar = async () => {
    if (!avatarState.sessionId) {
      return
    }

    try {
      // 停止心跳和推流地址检查
      stopHeartbeat()
      stopStreamUrlCheck()

      const response = await stopAvatarSession(avatarState.sessionId)
      
      if (response.code !== 200) {
        console.warn('停止数字人会话失败:', response.message)
      }
      
      // 重置状态
      avatarState.sessionId = ''
      avatarState.isConnected = false
      avatarState.streamUrl = ''
      avatarState.isSpeaking = false
      avatarState.isListening = false
      avatarState.error = ''
      
      console.log('数字人会话已停止')
      
    } catch (error: any) {
      console.error('停止数字人会话失败:', error)
    }
  }

  /**
   * 发送动作指令
   */
  const performAction = async (actionType: string, actionValue: string) => {
    if (!avatarState.sessionId || !avatarState.isConnected) {
      throw new Error('数字人未连接')
    }

    try {
      const response = await sendAction(avatarState.sessionId, actionType, actionValue)
      
      if (response.code !== 200) {
        throw new Error(response.message || '发送动作指令失败')
      }
      
      console.log('动作指令发送成功:', actionType, actionValue)
      
    } catch (error: any) {
      console.error('发送动作指令失败:', error)
      throw error
    }
  }

  /**
   * 启动心跳
   */
  const startHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
    }
    
    heartbeatTimer = setInterval(async () => {
      if (avatarState.sessionId && avatarState.isConnected) {
        try {
          await sendHeartbeat(avatarState.sessionId)
        } catch (error) {
          console.error('心跳发送失败:', error)
          // 心跳失败可能表示连接断开
          avatarState.isConnected = false
        }
      }
    }, 5000) // 每5秒发送一次心跳
  }

  /**
   * 停止心跳
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  /**
   * 启动推流地址检查
   */
  const startStreamUrlCheck = () => {
    if (streamUrlCheckTimer) {
      clearInterval(streamUrlCheckTimer)
    }

    let checkCount = 0
    const maxChecks = 30 // 最多检查30次（60秒）

    streamUrlCheckTimer = setInterval(async () => {
      if (avatarState.sessionId && avatarState.isConnected && !avatarState.streamUrl) {
        checkCount++
        console.log(`第${checkCount}次检查推流地址...`)

        try {
          const response = await getStreamUrl(avatarState.sessionId)
          if (response.code === 200 && response.data) {
            avatarState.streamUrl = response.data
            console.log('✅ 成功获取到推流地址:', response.data)
            // 获取到推流地址后停止检查
            stopStreamUrlCheck()

            // 显示成功提示
            uni.showToast({
              title: '数字人视频已准备就绪',
              icon: 'success',
            })
          } else {
            console.debug(`⏳ 推流地址尚未准备好 (${checkCount}/${maxChecks}):`, response.message)
          }
        } catch (error: any) {
          console.debug(`⏳ 推流地址尚未准备好 (${checkCount}/${maxChecks}):`, error.message)
        }

        // 达到最大检查次数后停止
        if (checkCount >= maxChecks) {
          console.warn('⚠️ 推流地址获取超时，请检查数字人服务状态')
          stopStreamUrlCheck()

          uni.showToast({
            title: '推流地址获取超时',
            icon: 'none',
          })
        }
      } else {
        // 如果会话已断开或已有推流地址，停止检查
        stopStreamUrlCheck()
      }
    }, 2000) // 每2秒检查一次
  }

  /**
   * 停止推流地址检查
   */
  const stopStreamUrlCheck = () => {
    if (streamUrlCheckTimer) {
      clearInterval(streamUrlCheckTimer)
      streamUrlCheckTimer = null
    }
  }

  /**
   * 检查会话状态
   */
  const checkStatus = async () => {
    if (!avatarState.sessionId) {
      return false
    }

    try {
      const response = await checkSessionStatus(avatarState.sessionId)
      
      if (response.code === 200) {
        avatarState.isConnected = response.data
        return response.data
      }
      
      return false
    } catch (error) {
      console.error('检查会话状态失败:', error)
      return false
    }
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    stopHeartbeat()
    stopStreamUrlCheck()
    if (avatarState.sessionId && avatarState.isConnected) {
      stopAvatar()
    }
  })

  return {
    avatarState,
    startAvatar,
    speakText,
    interactText,
    sendAudio,
    resetAvatarState,
    stopAvatar,
    performAction,
    checkStatus,
  }
}
