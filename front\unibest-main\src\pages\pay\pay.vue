<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
import {
  API_CONFIG,
  apiRequest,
  type PaymentOrderRequest,
  type PaymentOrderResponse,
} from '../../config/api'

// 定义商品类型枚举
type ProductType = 'book' | 'video' | 'question-bank' | 'course' | 'plan'

// 定义通用商品类型
interface Product {
  id: number
  type: ProductType
  title: string
  subtitle: string // 作者/讲师/描述等
  cover: string // 封面/缩略图
  price: number
  description: string
  originalPrice?: number
  category: string
  difficulty?: string
  duration?: string // 视频时长
  totalQuestions?: number // 题库题目数量
  chapters?: number // 书籍章节数
  tags: string[]
  extra?: Record<string, any> // 扩展字段
}

// 定义支付方式类型
interface PaymentMethod {
  key: string
  name: string
  image: string
  description: string
  available: boolean
}

// 页面状态
const isLoading = ref(false)
const isPageLoaded = ref(false)
const isCreatingOrder = ref(false)
const orderProgress = ref(0)
const orderStep = ref('') // 订单创建步骤提示

// 商品信息
const productInfo = ref<Product>({
  id: 0,
  type: 'book',
  title: '',
  subtitle: '',
  cover: '',
  price: 0,
  description: '',
  category: '',
  tags: [],
})

// 当前选中的支付方式
const selectedPaymentMethod = ref('alipay')

// 支付方式列表
const paymentMethods = ref<PaymentMethod[]>([
  {
    key: 'wechat',
    name: '微信支付',
    description: '微信安全支付',
    image: '../../static/svg/wechat-pay.svg',
    available: false,
  },
  {
    key: 'alipay',
    name: '支付宝支付',
    description: '支付宝安全支付',
    image: '../../static/svg/alipay-pay.svg',
    available: true,
  },
])

// 计算支付金额信息
const paymentInfo = computed(() => {
  const originalPrice = productInfo.value.originalPrice || productInfo.value.price
  const currentPrice = productInfo.value.price
  const discount = originalPrice > currentPrice ? originalPrice - currentPrice : 0

  return {
    originalPrice,
    currentPrice,
    discount,
    hasDiscount: discount > 0,
  }
})

// 获取商品类型显示文本
const productTypeText = computed(() => {
  const typeMap: Record<ProductType, string> = {
    book: '电子书籍',
    video: '视频课程',
    'question-bank': '题库练习',
    course: '专业课程',
    plan: '学习计划',
  }
  return typeMap[productInfo.value.type] || '商品'
})

// 获取商品类型图标
const productTypeIcon = computed(() => {
  const iconMap: Record<ProductType, string> = {
    book: 'i-fa-book-open',
    video: 'i-fa-play-circle',
    'question-bank': 'i-fa-question-circle',
    course: 'i-fa-graduation-cap',
    plan: 'i-fa-calendar-check',
  }
  return iconMap[productInfo.value.type] || 'i-fa-box'
})

/**
 * @description 获取商品详情信息
 * @param productId 商品ID
 * @param productType 商品类型
 */
const fetchProductInfo = async (productId: number, productType: ProductType): Promise<void> => {
  try {
    isLoading.value = true

    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 800))

    // 根据商品类型获取对应的模拟数据
    const product = getMockProductData(productId, productType)

    if (product) {
      productInfo.value = product
    } else {
      throw new Error('商品不存在')
    }
  } catch (error) {
    console.error('获取商品信息失败:', error)
    uni.showToast({
      title: '获取商品信息失败',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 获取模拟商品数据
 * @param productId 商品ID
 * @param productType 商品类型
 * @returns 返回商品数据或null
 */
const getMockProductData = (productId: number, productType: ProductType): Product | null => {
  // 书籍数据
  const mockBooks: Record<number, Product> = {
    1: {
      id: 1,
      type: 'book',
      title: '技术面试完全指南',
      subtitle: '张技术专家 著',
      cover: '../../static/images/book.jpg',
      price: 0,
      originalPrice: 59,
      description:
        '从基础技术问题到高级系统设计，全面覆盖技术面试的各个环节，帮助你在技术面试中脱颖而出。包含300+精选面试题，详细解析和答题技巧。',
      category: '技术面试',
      difficulty: '进阶',
      chapters: 15,
      tags: ['技术面试', 'Java', 'Python', '系统设计'],
    },
    2: {
      id: 2,
      type: 'book',
      title: '行为面试STAR法则',
      subtitle: 'HR王老师 著',
      cover: '../../static/images/book.jpg',
      price: 29,
      originalPrice: 39,
      description:
        '掌握STAR法则，学会用故事化的方式回答行为面试问题，展现你的能力和经验。通过大量案例和模板，助你轻松应对各种行为面试场景。',
      category: '行为面试',
      difficulty: '入门',
      chapters: 12,
      tags: ['行为面试', 'STAR法则', '沟通技巧'],
    },
  }

  // 视频数据
  const mockVideos: Record<number, Product> = {
    1: {
      id: 1,
      type: 'video',
      title: '面试表达技巧全攻略',
      subtitle: '张教授 主讲',
      cover: '../../static/images/video.png',
      price: 99,
      originalPrice: 129,
      description:
        '系统学习面试中的表达技巧，提升语言组织能力和逻辑思维表达。课程包含实战演练和案例分析。',
      category: '面试技巧',
      difficulty: '入门',
      duration: '45分钟',
      tags: ['面试技巧', '表达能力', '沟通'],
    },
    3: {
      id: 3,
      type: 'video',
      title: '项目经验分享技巧',
      subtitle: '王工程师 主讲',
      cover: '../../static/images/video.png',
      price: 149,
      originalPrice: 199,
      description:
        '如何在面试中有效地介绍自己的项目经验，突出技术亮点。包含项目介绍模板和实战技巧。',
      category: '项目经验',
      difficulty: '进阶',
      duration: '35分钟',
      tags: ['项目经验', '技术分享', '面试'],
    },
    5: {
      id: 5,
      type: 'video',
      title: 'JavaScript高级面试题详解',
      subtitle: '刘专家 主讲',
      cover: '../../static/images/video.png',
      price: 199,
      originalPrice: 249,
      description:
        '深度解析JavaScript面试中的核心问题，提升技术面试通过率。涵盖闭包、原型链、异步编程等核心概念。',
      category: '技术专业',
      difficulty: '高级',
      duration: '75分钟',
      tags: ['JavaScript', '前端开发', '面试题'],
    },
  }

  // 题库数据
  const mockQuestionBanks: Record<number, Product> = {
    1: {
      id: 1,
      type: 'question-bank',
      title: 'Java面试题库大全',
      subtitle: '包含500+精选题目',
      cover: '../../static/images/book.jpg',
      price: 68,
      originalPrice: 88,
      description:
        '精心整理的Java面试题库，涵盖基础语法、面向对象、集合框架、多线程、JVM等核心知识点。',
      category: 'Java开发',
      difficulty: '全面',
      totalQuestions: 500,
      tags: ['Java', '面试题库', '编程基础'],
    },
    2: {
      id: 2,
      type: 'question-bank',
      title: '算法与数据结构题库',
      subtitle: '包含300+经典题目',
      cover: '../../static/images/book.jpg',
      price: 89,
      originalPrice: 119,
      description: '经典算法与数据结构题目集合，包含详细解析和多种解法，助你轻松应对算法面试。',
      category: '算法题解',
      difficulty: '进阶',
      totalQuestions: 300,
      tags: ['算法', '数据结构', 'LeetCode'],
    },
  }

  // 根据商品类型返回对应数据
  switch (productType) {
    case 'book':
      return mockBooks[productId] || null
    case 'video':
      return mockVideos[productId] || null
    case 'question-bank':
      return mockQuestionBanks[productId] || null
    default:
      return null
  }
}

/**
 * @description 选择支付方式
 * @param methodKey 支付方式的key
 */
const selectPaymentMethod = (methodKey: string): void => {
  const method = paymentMethods.value.find((m) => m.key === methodKey)

  if (!method?.available) {
    uni.showToast({
      title: method?.key === 'wechat' ? '微信支付暂未开放' : '支付方式不可用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  selectedPaymentMethod.value = methodKey
}

/**
 * @description 创建订单流程
 */
const createOrder = async (): Promise<void> => {
  if (isCreatingOrder.value) return

  const method = paymentMethods.value.find((m) => m.key === selectedPaymentMethod.value)
  if (!method?.available) {
    uni.showToast({
      title: '请选择可用的支付方式',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  isCreatingOrder.value = true

  try {
    // 创建订单并跳转到确认支付页面
    await createOrderAndRedirect()
  } catch (error) {
    console.error('创建订单失败:', error)
    uni.showToast({
      title: '创建订单失败，请重试',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isCreatingOrder.value = false
  }
}

/**
 * @description 创建订单并跳转到确认支付页面
 */
const createOrderAndRedirect = async (): Promise<void> => {
  // 开始创建订单动画
  orderStep.value = '正在创建订单...'
  await simulateOrderCreationProgress()

  // 创建订单数据
  const orderData: PaymentOrderRequest = {
    productId: productInfo.value.id,
    productType: productInfo.value.type,
    productTitle: productInfo.value.title,
    amount: productInfo.value.price,
    paymentMethod: selectedPaymentMethod.value,
    userId: 1, // TODO: 从用户状态管理中获取
    remark: `购买商品：${productInfo.value.title}`,
  }

  orderStep.value = '正在生成订单信息...'

  // 调用后端API创建订单
  const orderResult = await apiRequest<PaymentOrderResponse>({
    url: API_CONFIG.PAYMENT.CREATE_ORDER,
    method: 'POST',
    data: orderData,
  })

  if (orderResult.code !== 200) {
    throw new Error(orderResult.msg || '创建订单失败')
  }

  const orderInfo = orderResult.data
  console.log('订单创建成功：', orderInfo)

  orderStep.value = '订单创建成功，正在跳转...'
  orderProgress.value = 100

  // 等待一下再跳转，让用户看到成功提示
  setTimeout(() => {
    // 跳转到订单确认支付页面，传递订单信息
    uni.redirectTo({
      url: `/pages/pay/pay-confirm?orderNo=${orderInfo.orderNo}&payToken=${orderInfo.payToken}&productId=${productInfo.value.id}&productType=${productInfo.value.type}&paymentMethod=${selectedPaymentMethod.value}`,
    })
  }, 800)
}

/**
 * @description 模拟订单创建进度动画
 */
const simulateOrderCreationProgress = async (): Promise<void> => {
  const steps = [
    { progress: 20, text: '验证商品信息...' },
    { progress: 40, text: '生成订单数据...' },
    { progress: 60, text: '保存订单信息...' },
    { progress: 80, text: '生成支付令牌...' },
    { progress: 100, text: '订单创建完成...' },
  ]

  for (const step of steps) {
    orderProgress.value = step.progress
    orderStep.value = step.text
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
}

/**
 * @description 返回上一页
 */
const goBack = (): void => {
  uni.navigateBack()
}

/**
 * @description 获取支付方式类名
 * @param methodKey 支付方式的key
 * @returns 支付方式类名
 */
const getPaymentMethodClass = (methodKey: string): string => {
  if (methodKey === selectedPaymentMethod.value) {
    return 'method-selected'
  }
  return 'method-disabled'
}

/**
 * @description 获取支付方式选中状态
 * @param methodKey 支付方式的key
 * @returns 支付方式选中状态
 */
const getRadioBtnClass = (methodKey: string): string => {
  if (methodKey === selectedPaymentMethod.value) {
    return 'radio-checked'
  }
  return 'radio-unchecked'
}

/**
 * @description 初始化页面动画效果
 */
const initPage = async (): Promise<void> => {
  await nextTick()
  setTimeout(() => {
    isPageLoaded.value = true
  }, 100)
}

/**
 * @description 打开用户协议
 */
const openAgreement = (): void => {
  uni.navigateTo({
    url: '/pages/user/agreement',
  })
}

/**
 * @description 打开隐私政策
 */
const openPrivacyPolicy = (): void => {
  uni.navigateTo({
    url: '/pages/user/privacy-policy',
  })
}

// 页面加载时获取参数
onLoad((options: any) => {
  const productId = parseInt(options.id || '1')
  const productType = (options.type || 'book') as ProductType

  console.log('支付页面参数:', { productId, productType })

  initPage()
  fetchProductInfo(productId, productType)
})
</script>

<template>
  <view class="pay-container">
    <HeadBar title="支付订单" :show-back="true" @back="goBack" />

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
      <view v-if="!isLoading" class="content-wrapper fade-in">
        <!-- 商品信息 -->
        <view class="product-section">
          <view class="product-card">
            <view class="product-cover-wrapper">
              <image class="product-cover" :src="productInfo.cover" mode="aspectFill" />
              <!-- 商品类型标签 -->
              <view class="product-type-badge">
                <view :class="productTypeIcon" class="type-icon"></view>
                <text class="type-text">{{ productTypeText }}</text>
              </view>
            </view>
            <view class="product-info">
              <text class="product-title">{{ productInfo.title }}</text>
              <text class="product-subtitle">{{ productInfo.subtitle }}</text>

              <!-- 商品特征标签 -->
              <view class="product-features">
                <view v-if="productInfo.difficulty" class="feature-tag">
                  <text class="feature-text">{{ productInfo.difficulty }}</text>
                </view>
                <view v-if="productInfo.duration" class="feature-tag">
                  <view class="i-fa-clock feature-icon"></view>
                  <text class="feature-text">{{ productInfo.duration }}</text>
                </view>
                <view v-if="productInfo.chapters" class="feature-tag">
                  <view class="i-fa-book-open feature-icon"></view>
                  <text class="feature-text">{{ productInfo.chapters }}章节</text>
                </view>
                <view v-if="productInfo.totalQuestions" class="feature-tag">
                  <view class="i-fa-question-circle feature-icon"></view>
                  <text class="feature-text">{{ productInfo.totalQuestions }}题</text>
                </view>
              </view>

              <view class="price-info">
                <text class="current-price">¥{{ paymentInfo.currentPrice }}</text>
                <text v-if="paymentInfo.hasDiscount" class="original-price">
                  ¥{{ paymentInfo.originalPrice }}
                </text>
                <view v-if="paymentInfo.hasDiscount" class="discount-badge">
                  <text class="discount-text">省¥{{ paymentInfo.discount }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 支付方式选择 -->
        <view class="payment-section">
          <view class="section-header">
            <text class="section-title">选择支付方式</text>
          </view>

          <view class="payment-methods">
            <view
              v-for="method in paymentMethods"
              :key="method.key"
              class="payment-method"
              :class="getPaymentMethodClass(method.key)"
              @click="selectPaymentMethod(method.key)"
            >
              <view class="method-icon-wrapper">
                <image :src="method.image" class="method-icon" mode="aspectFit" />
              </view>
              <view class="method-info">
                <text class="method-name">{{ method.name }}</text>
                <text class="method-desc">{{ method.description }}</text>
                <text v-if="!method.available" class="method-status">暂不可用</text>
              </view>
              <view class="method-selector">
                <view class="radio-btn" :class="getRadioBtnClass(method.key)"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单金额 -->
        <view class="amount-section">
          <view class="section-header">
            <text class="section-title">订单金额</text>
          </view>

          <view class="amount-details">
            <view class="amount-item">
              <text class="amount-label">商品金额</text>
              <text class="amount-value">
                ¥{{ paymentInfo.originalPrice || paymentInfo.currentPrice }}
              </text>
            </view>
            <view v-if="paymentInfo.hasDiscount" class="amount-item discount-item">
              <text class="amount-label">优惠金额</text>
              <text class="amount-value discount-value">-¥{{ paymentInfo.discount }}</text>
            </view>
            <view class="amount-divider"></view>
            <view class="amount-item total-item">
              <text class="amount-label">实付金额</text>
              <text class="amount-value total-value">¥{{ paymentInfo.currentPrice }}</text>
            </view>
          </view>
        </view>

        <!-- 支付协议 -->
        <view class="agreement-section">
          <view class="agreement-item">
            <text class="agreement-text">
              点击"创建订单"即表示您同意
              <text class="agreement-link" @click="openAgreement">《用户协议》</text>
              和
              <text class="agreement-link" @click="openPrivacyPolicy">《隐私政策》</text>
            </text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部支付按钮 -->
    <view class="bottom-bar">
      <view class="pay-info">
        <text class="pay-label">实付金额</text>
        <text class="pay-amount">¥{{ paymentInfo.currentPrice }}</text>
      </view>
      <button
        class="pay-btn"
        :class="{
          'pay-btn-loading': isCreatingOrder,
          'pay-btn-pulse': !isCreatingOrder,
        }"
        :disabled="isCreatingOrder"
        @click="createOrder"
      >
        <view v-if="isCreatingOrder" class="loading-icon">
          <view class="i-fa-spinner loading-spinner"></view>
        </view>
        <text class="pay-btn-text">{{ isCreatingOrder ? '创建中...' : '创建订单' }}</text>
      </button>
    </view>

    <!-- 创建订单进度遮罩 -->
    <view v-if="isCreatingOrder" class="payment-overlay">
      <view class="payment-modal">
        <!-- 订单图标动画 -->
        <view class="payment-icon-wrapper">
          <view class="payment-icon-bg">
            <view class="i-fa-solid-file-invoice payment-icon"></view>
          </view>
        </view>

        <!-- 订单创建进度条 -->
        <view class="payment-progress">
          <view class="progress-bg">
            <view class="progress-fill" :style="{ width: orderProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ orderProgress }}%</text>
        </view>

        <!-- 订单创建步骤提示 -->
        <view class="payment-step">
          <text class="step-text">{{ orderStep }}</text>
        </view>

        <!-- 订单安全提示 -->
        <view class="payment-security">
          <view class="security-item">
            <view class="i-fa-shield-alt security-icon"></view>
            <text class="security-text">安全创建</text>
          </view>
          <view class="security-item">
            <view class="i-fa-lock security-icon"></view>
            <text class="security-text">数据加密</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <LoadingCard :visible="isLoading" text="正在加载商品信息..." />
  </view>
</template>

<style scoped lang="scss">
.pay-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

// 主要内容区域
.main-content {
  flex: 1;
  padding-bottom: 140rpx; // 为底部按钮留出空间
}

.content-wrapper {
  padding: 0 20rpx;

  &.fade-in {
    animation: fadeIn 0.6s ease-out;
  }
}

// 商品信息
.product-section {
  margin: 20rpx 0 32rpx;

  .product-card {
    display: flex;
    align-items: flex-start;
    gap: 24rpx;
    padding: 32rpx;
    background: white;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .product-cover-wrapper {
      position: relative;
      width: 120rpx;
      height: 160rpx;
      flex-shrink: 0;
      border-radius: 12rpx;
      overflow: hidden;
      background: #f1f5f9;

      .product-cover {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .product-type-badge {
        position: absolute;
        top: 8rpx;
        left: 8rpx;
        display: flex;
        align-items: center;
        gap: 4rpx;
        padding: 6rpx 12rpx;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 20rpx;
        border-radius: 12rpx;

        .type-icon {
          font-size: 16rpx;
        }

        .type-text {
          font-size: 18rpx;
          font-weight: 500;
        }
      }
    }

    .product-info {
      flex: 1;

      .product-title {
        display: block;
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: #1e293b;
        line-height: 1.4;
      }

      .product-subtitle {
        display: block;
        margin-bottom: 16rpx;
        font-size: 24rpx;
        color: #64748b;
      }

      .product-features {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-bottom: 16rpx;

        .feature-tag {
          display: flex;
          align-items: center;
          gap: 6rpx;
          padding: 6rpx 12rpx;
          background: #f1f5f9;
          border-radius: 12rpx;

          .feature-icon {
            font-size: 20rpx;
            color: #64748b;
          }

          .feature-text {
            font-size: 20rpx;
            color: #64748b;
          }
        }
      }

      .price-info {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .current-price {
          font-size: 36rpx;
          font-weight: 700;
          color: #e11d48;
        }

        .original-price {
          font-size: 24rpx;
          color: #94a3b8;
          text-decoration: line-through;
        }

        .discount-badge {
          padding: 6rpx 12rpx;
          background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 50%, #f59e0b 100%);
          border-radius: 12rpx;

          .discount-text {
            font-size: 20rpx;
            font-weight: 600;
            color: #92400e;
          }
        }
      }
    }
  }
}

// 区域标题
.section-header {
  margin-bottom: 24rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1e293b;
  }
}

// 支付方式
.payment-section {
  margin-bottom: 32rpx;

  .payment-methods {
    background: white;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .payment-method {
      display: flex;
      align-items: center;
      padding: 32rpx;
      border-bottom: 2rpx solid #f1f5f9;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &.method-selected {
        background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
        border-color: #00c9a7;
      }

      &.method-disabled {
        opacity: 0.5;
        cursor: not-allowed;

        .method-info .method-status {
          color: #ef4444;
        }
      }

      &:not(.method-disabled):active {
        transform: scale(0.98);
      }

      .method-icon-wrapper {
        width: 72rpx;
        height: 72rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        background: #f8fafc;
        border-radius: 16rpx;

        .method-icon {
          width: 72rpx;
          height: 72rpx;
        }
      }

      .method-info {
        flex: 1;

        .method-name {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
        }

        .method-desc {
          display: block;
          font-size: 24rpx;
          color: #64748b;
        }

        .method-status {
          display: block;
          margin-top: 4rpx;
          font-size: 22rpx;
          color: #ef4444;
        }
      }

      .method-selector {
        .radio-btn {
          width: 36rpx;
          height: 36rpx;
          border: 4rpx solid #d1d5db;
          border-radius: 50%;
          position: relative;
          transition: all 0.3s ease;

          &.radio-checked {
            border-color: #00c9a7;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              width: 16rpx;
              height: 16rpx;
              background: #00c9a7;
              border-radius: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }
  }
}

// 订单金额
.amount-section {
  margin-bottom: 32rpx;

  .amount-details {
    padding: 32rpx;
    background: white;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .amount-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.discount-item {
        .discount-value {
          color: #16a34a;
        }
      }

      &.total-item {
        padding-top: 20rpx;

        .amount-label {
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
        }

        .total-value {
          font-size: 32rpx;
          font-weight: 700;
          color: #e11d48;
        }
      }

      .amount-label {
        font-size: 26rpx;
        color: #64748b;
      }

      .amount-value {
        font-size: 26rpx;
        font-weight: 500;
        color: #1e293b;
      }
    }

    .amount-divider {
      height: 2rpx;
      margin: 20rpx 0;
      background: #f1f5f9;
    }
  }
}

// 支付协议
.agreement-section {
  margin-bottom: 32rpx;

  .agreement-item {
    .agreement-text {
      font-size: 24rpx;
      line-height: 1.6;
      color: #64748b;

      .agreement-link {
        color: #00c9a7;
        text-decoration: underline;
      }
    }
  }
}

// 底部支付栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 2rpx solid #f1f5f9;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);

  .pay-info {
    flex: 1;
    margin-right: 24rpx;

    .pay-label {
      display: block;
      margin-bottom: 4rpx;
      font-size: 22rpx;
      color: #64748b;
    }

    .pay-amount {
      font-size: 32rpx;
      font-weight: 700;
      color: #e11d48;
    }
  }

  .pay-btn {
    min-width: 240rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    color: white;
    border: none;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
    transition: all 0.3s ease;

    &:not(:disabled):active {
      transform: scale(0.95);
    }

    &.pay-btn-loading {
      background: #94a3b8;
      box-shadow: none;
    }

    &.pay-btn-pulse {
      animation: pulse 2s infinite;
    }

    .loading-icon {
      .loading-spinner {
        font-size: 32rpx;
        animation: spin 1s linear infinite;
      }
    }

    .pay-btn-text {
      font-size: 28rpx;
      font-weight: 600;
    }
  }
}

// 支付进度遮罩
.payment-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;

  .payment-modal {
    background: white;
    padding: 60rpx 40rpx;
    border-radius: 20rpx;
    width: 80%;
    max-width: 400rpx;
    text-align: center;
    animation: slideUp 0.3s ease;

    .payment-icon-wrapper {
      position: relative;
      margin-bottom: 40rpx;

      .payment-icon-bg {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto;
        border-radius: 50%;
        background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;
      }

      .payment-icon {
        font-size: 48rpx;
        color: #00c9a7;
        animation: bounce 1s infinite;
      }
    }

    .payment-progress {
      position: relative;
      margin-bottom: 40rpx;

      .progress-bg {
        height: 20rpx;
        background: #f1f5f9;
        border-radius: 10rpx;
        overflow: hidden;
        position: relative;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
          border-radius: 10rpx;
          transition: width 0.3s ease;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressShine 1.5s infinite;
          }
        }
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24rpx;
        font-weight: 600;
        color: #1e293b;
        z-index: 1;
      }
    }

    .payment-step {
      margin-bottom: 40rpx;

      .step-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
        animation: textPulse 1s infinite;
      }
    }

    .payment-security {
      display: flex;
      justify-content: center;
      gap: 32rpx;

      .security-item {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .security-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }

        .security-text {
          font-size: 24rpx;
          color: #64748b;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes textPulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
