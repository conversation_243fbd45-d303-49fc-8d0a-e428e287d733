<script setup lang="ts">
// @ts-ignore
import { ref, reactive, computed, shallowRef, markRaw, nextTick } from 'vue'
import { onLoad, onHide, onShow, onUnload } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import BottomTabBar from '@/components/BottomTabBar.vue'
// @ts-ignore
import OnboardingGuide from '@/components/OnboardingGuide.vue'
// @ts-ignore
import ProgressVisualization from '@/components/ProgressVisualization.vue'


// 导入API接口
import {
  getDashboardSummary,
  getUserAbilities,
  getStudyStats,
  getSmartTasks,
  // @ts-ignore
} from '@/service/index/dashboard'
import type {
  UserAbilities,
  StudyStats,
  SmartTask,
  DashboardSummary,
  // @ts-ignore
} from '@/service/index/dashboard'
// @ts-ignore
import type { UserGrowthStage } from '@/types/onboarding'
// @ts-ignore
import Radar<PERSON>hart from '@/components/RadarChart.vue'
// @ts-ignore
import { useUserStore } from '@/store/user'
// @ts-ignore
import { dataManager } from '@/service/dataManager'
// @ts-ignore
import { errorHandler } from '@/utils/errorHandler'


// 用户状态
const userStore = useUserStore()

// 添加学习计划相关接口定义
// 学习计划类型
interface Plan {
  id: number
  title: string
  desc: string
  done: boolean
  category: string
  deadline?: string
  createTime: number
}

// 学习计划相关状态
const dailyPlans = ref<Plan[]>([])
const showAddPlan = ref(false)
const newPlanTitle = ref('')
const newPlanDesc = ref('')
const newPlanCategory = ref('其他')
const planCategories = [
  { value: '算法', color: '#6366f1' },
  { value: '前端', color: '#00C9A7' },
  { value: '后端', color: '#ff4d4f' },
  { value: '面试', color: '#faad14' },
  { value: '其他', color: '#9ca3af' },
]

// 计算今日待完成的计划数
const todayPendingPlans = computed(() => {
  return dailyPlans.value.filter((p) => !p.done).length
})

// 性能优化：使用shallowRef减少深度响应式开销
const currentUser = shallowRef<{ name: string; avatar?: string } | null>(null)
const welcomeMessage = ref('')
const aiMotivation = ref('')
const isLoading = ref(false)
const userInfo = ref({
  name: '',
  avatar: '',
})

// 新用户引导相关
const showOnboarding = ref(false)
const userGrowthStage = ref<UserGrowthStage>('new_user')
const hasCompletedAssessment = ref(false)

// 开发者测试功能
const showDeveloperPanel = ref(false)

// 性能优化：使用shallowRef避免深层响应式
const userGrowthProfile = shallowRef(null)

// 性能优化：缓存用户能力数据，避免频繁计算
const userAbilities = reactive<UserAbilities>({
  professionalKnowledge: 85,
  logicalThinking: 75,
  languageExpression: 70,
  stressResistance: 80,
  teamCollaboration: 90,
  innovation: 65,
})

// 学习统计数据
const studyStats = reactive<StudyStats>({
  totalInterviews: 15,
  averageScore: 78,
  improvementRate: 23,
  targetPosition: '前端工程师',
})

// AI推荐任务 - 使用shallowRef优化
const smartTasks = shallowRef<SmartTask[]>([
  {
    id: 1,
    title: '提升算法思维能力',
    description: '基于你最近的面试表现，建议加强算法题的练习',
    type: 'skill',
    priority: 'high',
    link: '/pages/resources/index?tag=algorithm',
  },
  {
    id: 2,
    title: '完善项目经验表达',
    description: '学习STAR法则，让你的项目经验更有说服力',
    type: 'expression',
    priority: 'medium',
    link: '/pages/resources/index?tag=star-method',
  },
])

// 通知状态
const notification = reactive({
  show: false,
  message: '',
  type: 'info',
  hideTimer: null as number | null,
})

// 性能优化：缓存状态标识
const pageStates = reactive({
  isDataLoaded: false,
  isNetworkAvailable: true,
  lastLoadTime: 0,
})

// 性能优化：防抖处理
let debounceTimer: number | null = null

/**
 * @description 防抖函数
 */
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  return ((...args: any[]) => {
    if (debounceTimer) clearTimeout(debounceTimer)
    debounceTimer = window.setTimeout(() => func(...args), delay)
  }) as T
}

/**
 * @description 检查新用户引导
 */
function checkNewUserOnboarding() {
  // 性能优化：批量读取本地存储
  const storage = uni.getStorageInfoSync()
  const keys = storage.keys

  hasCompletedAssessment.value = keys.includes('hasCompletedInitialAssessment')

  if (keys.includes('userGrowthProfile')) {
    try {
      const growthProfile = uni.getStorageSync('userGrowthProfile')
      const profile = JSON.parse(growthProfile)
      userGrowthStage.value = profile.currentStage || 'new_user'
      userGrowthProfile.value = markRaw(profile) // 使用markRaw避免深度响应式
    } catch (error) {
      console.warn('解析用户成长档案失败:', error)
    }
  }

  if (!hasCompletedAssessment.value && userGrowthStage.value === 'new_user') {
    const hasSeenOnboarding = keys.includes('hasSeenOnboarding')
    if (!hasSeenOnboarding) {
      showOnboarding.value = true
    }
  }
}

/**
 * @description 获取用户信息和动态欢迎语
 */
async function loadUserDashboard() {
  // 性能优化：避免重复加载
  const now = Date.now()
  if (pageStates.isDataLoaded && now - pageStates.lastLoadTime < 5 * 60 * 1000) {
    return
  }

  try {
    isLoading.value = true
    checkNewUserOnboarding()

    const isOnline = await errorHandler.checkNetworkStatus()
    pageStates.isNetworkAvailable = isOnline

    if (!isOnline) {
      loadOfflineData()
      return
    }

    // 性能优化：使用Promise.allSettled并行加载
    const [dashboardResult, abilitiesResult, statsResult, tasksResult] = await Promise.allSettled([
      errorHandler.callWithFallback(() => getDashboardSummary(), {
        enableFallback: true,
        fallbackData: {
          code: 200,
          data: {
            user: { name: '同学', avatar: '' },
            welcomeMessage: '欢迎回来！',
            aiMotivation: '今天也要加油学习哦！',
          },
        },
        fallbackMessage: '使用本地缓存数据',
        cacheTimeout: 30 * 60 * 1000,
      }),
      loadAbilityData(),
      loadStudyStats(),
      loadSmartTasks(),
    ])

    // 处理主要数据
    if (dashboardResult.status === 'fulfilled' && dashboardResult.value.code === 200) {
      const data = dashboardResult.value.data as DashboardSummary
      currentUser.value = markRaw({
        name: data.user.name,
        avatar: data.user.avatar,
      })
      welcomeMessage.value = data.welcomeMessage
      aiMotivation.value = data.aiMotivation
      dataManager.setData('currentUser', data.user)
    } else {
      loadOfflineData()
    }

    pageStates.isDataLoaded = true
    pageStates.lastLoadTime = now
  } catch (error) {
    loadOfflineData()
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 加载离线数据
 */
function loadOfflineData() {
  const userInfo = dataManager.getData('currentUser')
  if (userInfo) {
    currentUser.value = markRaw({ name: userInfo.name, avatar: userInfo.avatar })
  } else {
    currentUser.value = markRaw({ name: '同学' })
  }
  generateAIWelcomeMessage()
}

/**
 * @description 生成AI智能欢迎语
 */
function generateAIWelcomeMessage() {
  const now = new Date()
  const hour = now.getHours()
  let greeting = '早上好'

  if (hour >= 12 && hour < 18) {
    greeting = '下午好'
  } else if (hour >= 18 || hour < 5) {
    greeting = '晚上好'
  }

  const userName = currentUser.value ? currentUser.value.name : '同学'

  if (!hasCompletedAssessment.value) {
    welcomeMessage.value = `${greeting}，${userName}！欢迎来到智能面试平台`
    aiMotivation.value = '让我们先了解一下你的能力水平，为你量身定制学习计划吧！'
  } else {
    welcomeMessage.value = `${greeting}，${userName}！`
    generatePersonalizedMotivation()
  }
}

/**
 * @description 生成个性化激励语
 */
function generatePersonalizedMotivation() {
  const growthProfile = uni.getStorageSync('userGrowthProfile')
  if (growthProfile) {
    try {
      const profile = JSON.parse(growthProfile)
      const motivations = generateMotivationsByStage(profile.currentStage, profile)
      aiMotivation.value = motivations[Math.floor(Math.random() * motivations.length)]
    } catch (error) {
      console.warn('解析激励语失败:', error)
      setDefaultMotivation()
    }
  } else {
    setDefaultMotivation()
  }
}

/**
 * @description 设置默认激励语
 */
function setDefaultMotivation() {
  const defaultMotivations = [
    '今天也要加油哦！每一次练习都是成长的机会',
    '相信自己，你比想象中更优秀！',
    '持续学习是通往成功的唯一路径',
    '今天适合挑战一下新的面试题目呢！',
  ]
  aiMotivation.value = defaultMotivations[Math.floor(Math.random() * defaultMotivations.length)]
}

/**
 * @description 根据成长阶段生成激励语
 */
function generateMotivationsByStage(stage: string, profile: any): string[] {
  const stageMotivations = {
    new_user: [
      '刚开始的路总是充满希望，加油！',
      '每一个专家都曾是初学者，相信自己！',
      '你已经迈出了最重要的第一步！',
    ],
    beginner: [
      `你的${getWeakestAbility(profile)}提升了，继续努力！`,
      `已连续学习${profile.continuousLearningDays}天，坚持就是胜利！`,
      '基础越扎实，未来越稳固！',
    ],
    intermediate: [
      '你的进步让人印象深刻，再接再厉！',
      `距离${profile.targetPosition}的目标越来越近了！`,
      '现在可以挑战更有难度的面试题了！',
    ],
    advanced: [
      '你已经是面试高手了，可以帮助其他人了！',
      '考虑向更高阶的技能发起挑战吧！',
      '你的经验已经很丰富，是时候展现领导力了！',
    ],
    expert: [
      '作为专家级用户，你可以分享经验帮助他人！',
      '持续学习让你保持在行业前沿！',
      '你的成功经验值得被更多人学习！',
    ],
  }
  return stageMotivations[stage] || stageMotivations.new_user
}

/**
 * @description 获取最薄弱的能力
 */
function getWeakestAbility(profile: any): string {
  if (!profile.currentAssessment) return '综合能力'

  const abilities = {
    professionalKnowledge: '专业知识',
    logicalThinking: '逻辑思维',
    languageExpression: '语言表达',
    stressResistance: '抗压能力',
    teamCollaboration: '团队协作',
    innovation: '创新能力',
  }

  let weakestKey = 'professionalKnowledge'
  let lowestScore = profile.currentAssessment.professionalKnowledge

  Object.keys(abilities).forEach((key) => {
    if (profile.currentAssessment[key] < lowestScore) {
      lowestScore = profile.currentAssessment[key]
      weakestKey = key
    }
  })

  return abilities[weakestKey]
}

/**
 * @description 加载能力数据
 */
async function loadAbilityData() {
  try {
    const cachedAbilities = dataManager.getData('userAbilities')
    if (cachedAbilities) {
      Object.assign(userAbilities, cachedAbilities)
    }

    const res = await errorHandler.callWithFallback(() => getUserAbilities(), {
      enableFallback: true,
      fallbackData: { code: 200, data: cachedAbilities },
      fallbackMessage: '使用缓存的能力数据',
      cacheTimeout: 10 * 60 * 1000,
    })

    if (res.code === 200 && res.data) {
      Object.assign(userAbilities, res.data)
      dataManager.setData('userAbilities', res.data)
    }
  } catch (error) {
    console.warn('加载能力数据失败:', error)
  }
}

/**
 * @description 加载学习统计数据
 */
async function loadStudyStats() {
  try {
    const cachedStats = dataManager.getData('studyStats')
    if (cachedStats) {
      Object.assign(studyStats, cachedStats)
    }

    const res = await errorHandler.callWithFallback(() => getStudyStats(), {
      enableFallback: true,
      fallbackData: { code: 200, data: cachedStats || studyStats },
      fallbackMessage: '使用缓存的统计数据',
      cacheTimeout: 5 * 60 * 1000,
    })

    if (res.code === 200 && res.data) {
      Object.assign(studyStats, res.data)
      dataManager.setData('studyStats', res.data)
    }
  } catch (error) {
    console.warn('加载统计数据失败:', error)
  }
}

/**
 * @description 加载智能推荐任务
 */
async function loadSmartTasks() {
  try {
    const cachedTasks = dataManager.getData('smartTasks')
    if (cachedTasks) {
      smartTasks.value = markRaw(cachedTasks)
    }

    const res = await errorHandler.callWithFallback(() => getSmartTasks({ params: { limit: 3 } }), {
      enableFallback: true,
      fallbackData: { code: 200, data: cachedTasks || smartTasks.value },
      fallbackMessage: '使用缓存的推荐任务',
      cacheTimeout: 15 * 60 * 1000,
    })

    if (res.code === 200 && res.data) {
      smartTasks.value = markRaw(res.data)
      dataManager.setData('smartTasks', res.data)
    }
  } catch (error) {
    console.warn('加载推荐任务失败:', error)
  }
}

/**
 * @description 开始新的模拟面试
 */
const startNewInterview = debounce(() => {
  uni.navigateTo({
    url: '/pages/interview/select',
  })
}, 300)

/**
 * @description 跳转到摄像头演示页面
 */
const goToCameraDemo = debounce(() => {
  uni.navigateTo({
    url: '/pages/demo/camera-demo',
  })
}, 300)

/**
 * @description 跳转到H5摄像头演示页面
 */
const goToH5CameraDemo = debounce(() => {
  uni.navigateTo({
    url: '/pages/demo/h5-camera',
  })
}, 300)

/**
 * @description 执行智能任务
 * @param task 任务对象
 */
const executeSmartTask = debounce((task: any) => {
  if (task.link) {
    uni.navigateTo({
      url: task.link,
    })
  }
}, 300)

/**
 * @description 跳转到个人中心
 */
const goToProfile = debounce(() => {
  uni.navigateTo({
    url: '/pages/user/center',
  })
}, 300)

/**
 * @description 跳转到消息中心
 */
const goToMessage = debounce(() => {
  uni.navigateTo({
    url: '/pages/message/index',
  })
}, 300)

/**
 * @description 跳转到能力评估详情页
 */
const goToAbilityAssessment = debounce(() => {
  uni.setStorageSync('userAbilities', JSON.stringify(userAbilities))
  uni.navigateTo({
    url: '/pages/user/ability-assessment',
    fail: (err) => {
      console.error('跳转失败:', err)
    },
  })
}, 300)

/**
 * @description 关闭新用户引导
 */
function closeOnboarding() {
  showOnboarding.value = false
  uni.setStorageSync('hasSeenOnboarding', true)
}

/**
 * @description 开始初始评估
 */
function startInitialAssessment() {
  showOnboarding.value = false
  uni.setStorageSync('hasSeenOnboarding', true)
  uni.navigateTo({
    url: '/pages/assessment/initial',
  })
}

/**
 * @description 跳过引导
 */
function skipOnboarding() {
  showOnboarding.value = false
  uni.setStorageSync('hasSeenOnboarding', true)
  uni.setStorageSync('hasSkippedOnboarding', true)
}

/**
 * @description 显示/隐藏开发者面板
 */
function toggleDeveloperPanel() {
  showDeveloperPanel.value = !showDeveloperPanel.value
}

// 性能优化：缓存计算结果，避免频繁重新计算
let radarDataCache: any = null
let lastAbilitiesHash = ''

// 雷达图数据 - 基于用户能力数据动态计算（优化版）
const radarData = computed(() => {
  // 计算能力数据的哈希值，只有当数据真正变化时才重新计算
  const currentHash = JSON.stringify(userAbilities)
  if (radarDataCache && lastAbilitiesHash === currentHash) {
    return radarDataCache
  }

  lastAbilitiesHash = currentHash
  radarDataCache = [
    {
      name: '专业知识',
      current: userAbilities.professionalKnowledge,
      previous: userAbilities.professionalKnowledge - 5,
      max: 100,
      color: '#00c9a7',
    },
    {
      name: '逻辑思维',
      current: userAbilities.logicalThinking,
      previous: userAbilities.logicalThinking - 3,
      max: 100,
      color: '#4CAF50',
    },
    {
      name: '语言表达',
      current: userAbilities.languageExpression,
      previous: userAbilities.languageExpression - 2,
      max: 100,
      color: '#FFC107',
    },
    {
      name: '抗压能力',
      current: userAbilities.stressResistance,
      previous: userAbilities.stressResistance - 4,
      max: 100,
      color: '#FF9800',
    },
    {
      name: '团队协作',
      current: userAbilities.teamCollaboration,
      previous: userAbilities.teamCollaboration - 3,
      max: 100,
      color: '#9C27B0',
    },
    {
      name: '创新能力',
      current: userAbilities.innovation,
      previous: userAbilities.innovation + 2,
      max: 100,
      color: '#2196F3',
    },
  ]

  return radarDataCache
})

// 当前激活的tab
const activeTab = ref('home')

// 当前激活的section，根据用户状态决定默认显示哪个section
const activeSection = ref(hasCompletedAssessment.value ? 'progress' : 'ability')

// 组件懒加载状态
const componentStates = reactive({
  shouldLoadRadarChart: false,
  shouldLoadProgressVisualization: false,
})

/**
 * @description 懒加载组件
 */
function initLazyComponents() {
  nextTick(() => {
    // 延迟加载非关键组件
    setTimeout(() => {
      componentStates.shouldLoadRadarChart = true
    }, 100)

    setTimeout(() => {
      componentStates.shouldLoadProgressVisualization = true
    }, 200)
  })
}

// 开发者功能保持不变，但优化性能
function simulateUserStage(stage: string) {
  userGrowthStage.value = stage as any
  hasCompletedAssessment.value = stage !== 'new_user'

  const mockProfile = {
    userId: 'test_user_' + Date.now(),
    currentStage: stage,
    joinDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastActiveDate: new Date().toISOString(),
    continuousLearningDays: stage === 'new_user' ? 1 : Math.floor(Math.random() * 30) + 1,
    totalInterviews: stage === 'new_user' ? 0 : Math.floor(Math.random() * 20) + 1,
    targetPosition: '前端工程师',
    learningGoals: ['提升算法能力', '增强项目经验', '改善面试表达'],
    achievements: [],
    completedCourses: Math.floor(Math.random() * 10),
    improvementRate: Math.floor(Math.random() * 30) + 10,
    initialAssessment: {
      professionalKnowledge: Math.floor(Math.random() * 30) + 50,
      logicalThinking: Math.floor(Math.random() * 30) + 50,
      languageExpression: Math.floor(Math.random() * 30) + 50,
      stressResistance: Math.floor(Math.random() * 30) + 50,
      teamCollaboration: Math.floor(Math.random() * 30) + 50,
      innovation: Math.floor(Math.random() * 30) + 50,
      overallScore: 0,
    },
    currentAssessment: {
      professionalKnowledge: Math.floor(Math.random() * 40) + 60,
      logicalThinking: Math.floor(Math.random() * 40) + 60,
      languageExpression: Math.floor(Math.random() * 40) + 60,
      stressResistance: Math.floor(Math.random() * 40) + 60,
      teamCollaboration: Math.floor(Math.random() * 40) + 60,
      innovation: Math.floor(Math.random() * 40) + 60,
      overallScore: 0,
    },
  }

  mockProfile.initialAssessment.overallScore = Math.round(
    (mockProfile.initialAssessment.professionalKnowledge +
      mockProfile.initialAssessment.logicalThinking +
      mockProfile.initialAssessment.languageExpression +
      mockProfile.initialAssessment.stressResistance +
      mockProfile.initialAssessment.teamCollaboration +
      mockProfile.initialAssessment.innovation) /
    6,
  )

  mockProfile.currentAssessment.overallScore = Math.round(
    (mockProfile.currentAssessment.professionalKnowledge +
      mockProfile.currentAssessment.logicalThinking +
      mockProfile.currentAssessment.languageExpression +
      mockProfile.currentAssessment.stressResistance +
      mockProfile.currentAssessment.teamCollaboration +
      mockProfile.currentAssessment.innovation) /
    6,
  )

  if (stage !== 'new_user') {
    uni.setStorageSync('userGrowthProfile', JSON.stringify(mockProfile))
    uni.setStorageSync('hasCompletedInitialAssessment', true)
    userGrowthProfile.value = markRaw(mockProfile)
  }

  showDeveloperPanel.value = false
  generateAIWelcomeMessage()

  uni.showToast({
    title: `已切换到${stage}阶段`,
    icon: 'success',
  })
}

// 其他开发者功能保持不变...
function resetToNewUser() {
  uni.clearStorageSync()
  userGrowthStage.value = 'new_user'
  hasCompletedAssessment.value = false
  showOnboarding.value = true
  showDeveloperPanel.value = false
  pageStates.isDataLoaded = false
  generateAIWelcomeMessage()
  uni.showToast({ title: '已重置为新用户', icon: 'success' })
}

function simulateHighPerformer() {
  const mockProfile = {
    userId: 'high_performer_' + Date.now(),
    currentStage: 'advanced',
    joinDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    lastActiveDate: new Date().toISOString(),
    continuousLearningDays: 45,
    totalInterviews: 25,
    targetPosition: '前端工程师',
    learningGoals: ['成为技术专家', '提升架构能力', '培养团队领导力'],
    achievements: [
      {
        id: 'high_score',
        name: '高分达人',
        description: '面试得分超过90分',
        icon: 'i-mdi-trophy',
        rarity: 'epic',
        unlockedAt: new Date().toISOString(),
      },
    ],
    completedCourses: 15,
    improvementRate: 45,
    initialAssessment: {
      professionalKnowledge: 75,
      logicalThinking: 70,
      languageExpression: 65,
      stressResistance: 80,
      teamCollaboration: 85,
      innovation: 70,
      overallScore: 74,
    },
    currentAssessment: {
      professionalKnowledge: 95,
      logicalThinking: 92,
      languageExpression: 88,
      stressResistance: 90,
      teamCollaboration: 96,
      innovation: 89,
      overallScore: 92,
    },
  }

  userGrowthStage.value = 'advanced'
  hasCompletedAssessment.value = true
  userGrowthProfile.value = markRaw(mockProfile)
  uni.setStorageSync('userGrowthProfile', JSON.stringify(mockProfile))
  uni.setStorageSync('hasCompletedInitialAssessment', true)
  showDeveloperPanel.value = false
  generateAIWelcomeMessage()
  uni.showToast({ title: '已切换为高分用户', icon: 'success' })
}

function simulateLowPerformer() {
  const mockProfile = {
    userId: 'low_performer_' + Date.now(),
    currentStage: 'beginner',
    joinDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    lastActiveDate: new Date().toISOString(),
    continuousLearningDays: 3,
    totalInterviews: 2,
    targetPosition: '前端工程师',
    learningGoals: ['掌握基础知识', '提升表达能力', '增强自信心'],
    achievements: [],
    completedCourses: 1,
    improvementRate: 5,
    initialAssessment: {
      professionalKnowledge: 40,
      logicalThinking: 35,
      languageExpression: 30,
      stressResistance: 45,
      teamCollaboration: 50,
      innovation: 35,
      overallScore: 39,
    },
    currentAssessment: {
      professionalKnowledge: 45,
      logicalThinking: 42,
      languageExpression: 38,
      stressResistance: 48,
      teamCollaboration: 55,
      innovation: 40,
      overallScore: 45,
    },
  }

  userGrowthStage.value = 'beginner'
  hasCompletedAssessment.value = true
  userGrowthProfile.value = markRaw(mockProfile)
  uni.setStorageSync('userGrowthProfile', JSON.stringify(mockProfile))
  uni.setStorageSync('hasCompletedInitialAssessment', true)
  showDeveloperPanel.value = false
  generateAIWelcomeMessage()
  uni.showToast({ title: '已切换为低分用户', icon: 'success' })
}

function simulateActiveUser() {
  const mockProfile = {
    userId: 'active_user_' + Date.now(),
    currentStage: 'intermediate',
    joinDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastActiveDate: new Date().toISOString(),
    continuousLearningDays: 28,
    totalInterviews: 18,
    targetPosition: '前端工程师',
    learningGoals: ['系统化学习', '实战项目经验', '面试技巧提升'],
    achievements: [
      {
        id: 'continuous_learner',
        name: '坚持不懈',
        description: '连续学习7天',
        icon: 'i-mdi-calendar-check',
        rarity: 'rare',
        unlockedAt: new Date().toISOString(),
      },
    ],
    completedCourses: 8,
    improvementRate: 35,
    initialAssessment: {
      professionalKnowledge: 60,
      logicalThinking: 55,
      languageExpression: 50,
      stressResistance: 65,
      teamCollaboration: 70,
      innovation: 55,
      overallScore: 59,
    },
    currentAssessment: {
      professionalKnowledge: 78,
      logicalThinking: 75,
      languageExpression: 72,
      stressResistance: 80,
      teamCollaboration: 85,
      innovation: 70,
      overallScore: 77,
    },
  }

  userGrowthStage.value = 'intermediate'
  hasCompletedAssessment.value = true
  userGrowthProfile.value = markRaw(mockProfile)
  uni.setStorageSync('userGrowthProfile', JSON.stringify(mockProfile))
  uni.setStorageSync('hasCompletedInitialAssessment', true)
  showDeveloperPanel.value = false
  generateAIWelcomeMessage()
  uni.showToast({ title: '已切换为活跃用户', icon: 'success' })
}

function simulateInactiveUser() {
  const mockProfile = {
    userId: 'inactive_user_' + Date.now(),
    currentStage: 'beginner',
    joinDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    lastActiveDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    continuousLearningDays: 0,
    totalInterviews: 3,
    targetPosition: '前端工程师',
    learningGoals: ['重新开始学习', '找回学习动力'],
    achievements: [],
    completedCourses: 2,
    improvementRate: 8,
    initialAssessment: {
      professionalKnowledge: 55,
      logicalThinking: 50,
      languageExpression: 45,
      stressResistance: 60,
      teamCollaboration: 65,
      innovation: 50,
      overallScore: 54,
    },
    currentAssessment: {
      professionalKnowledge: 58,
      logicalThinking: 55,
      languageExpression: 52,
      stressResistance: 62,
      teamCollaboration: 68,
      innovation: 53,
      overallScore: 58,
    },
  }

  userGrowthStage.value = 'beginner'
  hasCompletedAssessment.value = true
  userGrowthProfile.value = markRaw(mockProfile)
  uni.setStorageSync('userGrowthProfile', JSON.stringify(mockProfile))
  uni.setStorageSync('hasCompletedInitialAssessment', true)
  showDeveloperPanel.value = false
  generateAIWelcomeMessage()
  uni.showToast({ title: '已切换为非活跃用户', icon: 'success' })
}

// 学习计划相关方法
/**
 * @description 加载学习计划数据
 */
function loadPlans() {
  const storedPlans = uni.getStorageSync('studyPlans')
  if (storedPlans) {
    try {
      const parsedData = JSON.parse(storedPlans)
      // 兼容旧数据
      const updatedData = parsedData.map((item: any) => ({
        ...item,
        category: item.category || '其他',
        createTime: item.createTime || Date.now(),
      }))
      dailyPlans.value = updatedData
    } catch (e) {
      console.error('解析学习计划数据失败', e)
      dailyPlans.value = []
    }
  }
}

/**
 * @description 保存学习计划到本地
 */
function savePlansToLocal() {
  uni.setStorageSync('studyPlans', JSON.stringify(dailyPlans.value))
}

/**
 * @description 添加新的学习计划
 */
function addNewPlan() {
  if (!newPlanTitle.value.trim()) {
    uni.showToast({ title: '请输入计划标题', icon: 'none' })
    return
  }

  const newPlan: Plan = {
    id: Date.now(),
    title: newPlanTitle.value.trim(),
    desc: newPlanDesc.value.trim(),
    done: false,
    category: newPlanCategory.value,
    createTime: Date.now(),
  }

  dailyPlans.value.unshift(newPlan) // 添加到列表前面
  savePlansToLocal()

  // 重置输入
  newPlanTitle.value = ''
  newPlanDesc.value = ''
  newPlanCategory.value = '其他'
  showAddPlan.value = false

  uni.showToast({ title: '计划添加成功', icon: 'success' })
}

/**
 * @description 切换计划完成状态
 */
function togglePlanStatus(plan: Plan) {
  plan.done = !plan.done
  savePlansToLocal()
  uni.vibrateShort() // 触感反馈
}

/**
 * @description 删除学习计划
 */
function deletePlan(id: number) {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个学习计划吗？',
    success: (res) => {
      if (res.confirm) {
        dailyPlans.value = dailyPlans.value.filter((p) => p.id !== id)
        savePlansToLocal()
        uni.showToast({ title: '删除成功', icon: 'success' })
      }
    },
  })
}

/**
 * @description 前往学习计划详情页
 */
function goToPlanPage() {
  uni.navigateTo({
    url: '/pages/learning/plan',
  })
}

/**
 * @description 获取分类对应的颜色
 */
function getCategoryColor(category: string) {
  const found = planCategories.find((c) => c.value === category)
  return found ? found.color : '#9ca3af'
}

function getPlanClass(plan: Plan) {
  return {
    'plan-done': plan.done,
    'plan-pending': !plan.done,
  }
}

function getCheckboxClass(plan: Plan) {
  return {
    checked: plan.done,
  }
}

function preLoadPage() {
  // 预加载页面
  uni.preloadPage({
    url: '/pages/learning/plan',
  })
  uni.preloadPage({
    url: '/pages/interview/index',
  })
  uni.preloadPage({
    url: '/pages/message/index',
  })
  uni.preloadPage({
    url: '/pages/user/ability-assessment',
  })
}
onBeforeMount(() => {
  uni.hideTabBar()
  // 查看是否登录 如果没有 跳转到登录页面
  if (!userStore.isLogined) {
    uni.reLaunch({
      url: '/pages/auth/login',
    })
  }
})

// 页面生命周期优化
onLoad(() => {

  // 性能优化：分阶段加载，避免阻塞主线程
  requestAnimationFrame(() => {
    loadUserDashboard()
    loadPlans() // 加载学习计划数据
  })

  // 延迟初始化非关键功能
  setTimeout(() => {
    // 懒加载组件
    initLazyComponents()
    // 预加载页面
    preLoadPage()

    errorHandler.watchNetworkStatus((isOnline) => {
      pageStates.isNetworkAvailable = isOnline
      if (isOnline && !pageStates.isDataLoaded) {
        loadUserDashboard()
      }
    })

    const integrityResult = dataManager.performIntegrityCheck()
    if (!integrityResult.isHealthy) {
      console.warn('数据完整性检查发现问题:', integrityResult.issues)
    }
  }, 0)
})

onShow(() => {
  // 页面显示时检查是否需要刷新数据
  if (pageStates.isNetworkAvailable && !pageStates.isDataLoaded) {
    loadUserDashboard()
  }

  // 刷新学习计划数据
  loadPlans()
})

onHide(() => {
  if (notification.hideTimer) clearTimeout(notification.hideTimer)
  if (debounceTimer) clearTimeout(debounceTimer)
})

onUnload(() => {
  // 清理资源
  if (notification.hideTimer) clearTimeout(notification.hideTimer)
  if (debounceTimer) clearTimeout(debounceTimer)
  radarDataCache = null
  lastAbilitiesHash = ''
})

</script>

<template>
  <view class="page-container">
    <!-- 顶部导航 - 固定定位 -->
    <view class="nav-wrapper">
      <HeadBar title="求职能力驾驶舱" :show-back="false" :show-right-button="true" right-icon="i-mdi-bell"
        @right-click="goToMessage" />
    </view>

    <!-- 新用户引导 -->
    <OnboardingGuide :visible="showOnboarding" :user-stage="userGrowthStage" @close="closeOnboarding"
      @start-assessment="startInitialAssessment" @skip="skipOnboarding" />

    <!-- 通知提示 -->
    <view v-if="notification.show" class="notification" :class="notification.type">
      <text class="mr-2" :class="{
        'i-mdi-check-circle': notification.type === 'success',
        'i-mdi-alert-circle': notification.type === 'error',
        'i-mdi-information': notification.type === 'info',
      }"></text>
      <text>{{ notification.message }}</text>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-wrapper">
      <scroll-view class="main-content" scroll-y>
        <!-- 加载骨架屏 -->
        <view v-if="isLoading" class="skeleton-container">
          <view class="skeleton-welcome"></view>
          <view class="skeleton-button"></view>
          <view class="skeleton-stats">
            <view class="skeleton-stat" v-for="i in 4" :key="i"></view>
          </view>
          <view class="skeleton-chart"></view>
        </view>

        <view v-else class="content-wrapper">
          <!-- 欢迎区域 -->
          <view class="welcome-section">
            <view class="welcome-header">
              <view>
                <text class="welcome-text">{{ welcomeMessage }}</text>
                <text class="welcome-subtitle">{{ aiMotivation }}</text>
              </view>
              <view class="avatar-container" @click="goToProfile">
                <text class="i-mdi-account"></text>
              </view>
            </view>

            <!-- 核心按钮 - 开始新的模拟面试 -->
            <button class="primary-interview-btn" @click="startNewInterview">
              <text class="btn-icon i-mdi-play-circle"></text>
              <text class="btn-text">开始新的模拟面试</text>
            </button>

            <!-- 学习数据卡片 -->
            <view class="stats-card">
              <view class="stat-item">
                <text class="stat-value">{{ studyStats.totalInterviews }}</text>
                <text class="stat-label">面试次数</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{ studyStats.averageScore }}</text>
                <text class="stat-label">平均得分</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">
                  {{ studyStats.improvementRate }}
                  <text class="stat-unit">%</text>
                </text>
                <text class="stat-label">提升率</text>
              </view>
              <view class="stat-item">
                <text class="stat-value-small">{{ studyStats.targetPosition }}</text>
                <text class="stat-label">目标岗位</text>
              </view>
            </view>
          </view>

          <!-- 切换section -->
          <view class="section-switcher">
            <view class="switcher-item" :class="{ active: activeSection === 'ability' }"
              @click="activeSection = 'ability'">
              <text class="switcher-icon i-mdi-chart-line"></text>
              <text class="switcher-text">能力评估</text>
            </view>
            <view class="switcher-item" :class="{ active: activeSection === 'progress' }"
              @click="activeSection = 'progress'" v-if="hasCompletedAssessment">
              <text class="switcher-icon i-mdi-trending-up"></text>
              <text class="switcher-text">学习进度</text>
            </view>
            <view class="switcher-item" :class="{ active: activeSection === 'recommend' }"
              @click="activeSection = 'recommend'">
              <text class="switcher-icon i-mdi-lightbulb"></text>
              <text class="switcher-text">智能推荐</text>
            </view>
            <view class="switcher-item" :class="{ active: activeSection === 'plan' }" @click="activeSection = 'plan'">
              <text class="switcher-icon i-mdi-clipboard-check"></text>
              <text class="switcher-text">学习计划</text>
            </view>
          </view>

          <!-- 能力雷达图 - 懒加载优化 -->
          <view v-if="activeSection === 'ability'" class="section content-section">
            <view class="section-header">
              <text class="section-title">能力评估</text>
              <text class="section-link" @click="goToAbilityAssessment">查看详情 ></text>
            </view>
            <view class="radar-card">
              <!-- 性能优化：条件渲染，避免首屏阻塞 -->
              <view v-if="!componentStates.shouldLoadRadarChart" class="radar-placeholder">
                <view class="placeholder-circle"></view>
                <text class="placeholder-text">加载中...</text>
              </view>
              <RadarChart v-else :ability-data="radarData" :width="600" :height="390" :show-comparison="false" />
            </view>
          </view>

          <!-- 学习进度可视化 - 懒加载优化 -->
          <view v-if="activeSection === 'progress' && hasCompletedAssessment" class="section content-section">
            <view class="section-header">
              <text class="section-title">学习进度</text>
              <text class="section-subtitle">你的成长轨迹</text>
            </view>
            <!-- 性能优化：条件渲染 -->
            <view v-if="!componentStates.shouldLoadProgressVisualization" class="progress-placeholder">
              <view class="placeholder-bar"></view>
              <view class="placeholder-bar"></view>
              <view class="placeholder-bar"></view>
            </view>
            <ProgressVisualization v-else-if="componentStates.shouldLoadProgressVisualization"
              :profile="userGrowthProfile" :compact="true" :show-animation="true" />
          </view>

          <!-- AI智能推荐任务 -->
          <view v-if="activeSection === 'recommend'" class="section content-section">
            <view class="section-header">
              <text class="section-title">智能推荐</text>
              <text class="section-subtitle">基于你的短板定制</text>
            </view>
            <view class="task-list">
              <view v-for="task in smartTasks" :key="task.id" class="task-card"
                :class="{ 'high-priority': task.priority === 'high' }" @click="executeSmartTask(task)">
                <view class="task-icon">
                  <text :class="{
                    'i-mdi-brain': task.type === 'skill',
                    'i-mdi-message-text': task.type === 'expression',
                  }"></text>
                </view>
                <view class="task-content">
                  <text class="task-title">{{ task.title }}</text>
                  <text class="task-desc">{{ task.description }}</text>
                </view>
                <text class="task-arrow i-mdi-chevron-right"></text>
              </view>
            </view>
          </view>

          <!-- 每日学习计划 -->
          <view v-if="activeSection === 'plan'" class="section content-section">
            <view class="section-header">
              <text class="section-title">学习计划</text>
              <text class="section-link" @click="goToPlanPage">更多 ></text>
            </view>
            <view class="plan-card">
              <view class="plan-header">
                <text class="plan-title">今日待完成</text>
                <text class="plan-count">{{ todayPendingPlans }}</text>
              </view>

              <!-- 计划列表 -->
              <view class="daily-plans">
                <view v-for="plan in dailyPlans.slice(0, 3)" :key="plan.id" class="plan-item"
                  :class="getPlanClass(plan)">
                  <view class="plan-checkbox" @click="togglePlanStatus(plan)">
                    <view class="checkbox" :class="getCheckboxClass(plan)">
                      <text v-if="plan.done" class="i-mdi-check check-icon"></text>
                    </view>
                  </view>
                  <view class="plan-info" @click="togglePlanStatus(plan)">
                    <text class="plan-name">{{ plan.title }}</text>
                    <view class="plan-meta">
                      <view class="plan-category" :style="{ backgroundColor: getCategoryColor(plan.category) }">
                        {{ plan.category }}
                      </view>
                      <text v-if="plan.desc" class="plan-desc">{{ plan.desc }}</text>
                    </view>
                  </view>
                  <view class="plan-actions">
                    <text class="plan-delete-btn i-mdi-delete" @click.stop="deletePlan(plan.id)"></text>
                  </view>
                </view>

                <!-- 空状态显示 -->
                <view v-if="dailyPlans.length === 0" class="empty-plans">
                  <text class="i-mdi-clipboard-outline empty-icon"></text>
                  <text>暂无学习计划，快来添加吧！</text>
                </view>
                <view class="add-plan-btn" @click="goToPlanPage()">
                  <text class="i-mdi-plus"></text>
                  <text>添加学习计划</text>
                </view>
              </view>
            </view>
          </view>
          <!-- 底部安全距离 -->
          <view class="bottom-safe-area"></view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部导航栏 - 固定定位 -->
    <view class="bottom-wrapper">
      <BottomTabBar :active-tab="activeTab" :show-badge="true" />
    </view>

  </view>
</template>


<style scoped lang="scss">
/* ==================== 顶部导航区域 ==================== */
.nav-wrapper {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1000;

  // 添加安全距离支持
  // #ifdef MP-WEIXIN
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.2);
  // #endif
}

.page-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

/* ==================== 底部导航区域 ==================== */
.bottom-wrapper {
  position: fixed !important;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;

  // 添加安全距离支持
  // #ifdef MP-WEIXIN
  padding-bottom: env(safe-area-inset-bottom);
  // #endif

  // #ifdef H5
  padding-bottom: 0;
  background: white;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  // #endif
}

.notification {
  position: fixed;
  top: 100rpx;
  left: 50%;
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 48rpx;
  transform: translateX(-50%);
  animation: slideDown 0.3s ease-out;

  &.success {
    background: rgba(0, 201, 167, 0.9);
  }

  &.error {
    background: rgba(239, 68, 68, 0.9);
  }
}

/* ==================== 主内容区域布局 ==================== */
.main-wrapper {
  position: relative;
  top: 120rpx;

  // #ifdef MP-WEIXIN
  top: calc(120rpx + var(--status-bar-height));
  right: 0;
  bottom: 120rpx;
  left: 0;
  // #endif
}

.main-content {
  width: 100%;
  height: 100%;
  /* 性能优化：滚动优化 */
  -webkit-overflow-scrolling: touch;
  contain: layout style paint;
  overscroll-behavior: contain;
}

.content-wrapper {
  padding: 32rpx;
  padding-bottom: 40rpx;
}

.welcome-section {
  margin-bottom: 48rpx;
}

.welcome-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.welcome-text {
  display: block;
  margin-bottom: 8rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #222;
}

.welcome-subtitle {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #00c9a7;
}

.avatar-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  font-size: 36rpx;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.primary-interview-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  margin-bottom: 32rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  border: none;
  border-radius: 48rpx;
  box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.35);
  transition: all 0.3s ease;
  /* 性能优化：强制GPU加速 */
  transform: translateZ(0);
  will-change: transform, box-shadow;
  backface-visibility: hidden;

  &:active {
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
    transform: translateY(2rpx) translateZ(0);
  }

  .btn-icon {
    margin-right: 12rpx;
    font-size: 40rpx;
  }
}

.page-container .stats-card {
  position: relative;
  display: grid;
  grid-template-columns: 0.8fr 0.8fr 0.8fr 1.4fr;
  gap: 16rpx;
  padding: 32rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.page-container .stats-card::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 4rpx;
  content: '';
  background: linear-gradient(90deg, #00c9a7, #4caf50, #ffc107, #ff9800);
}

.stat-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-item:nth-child(1) {
  animation-delay: 0.1s;
}

.stat-item:nth-child(2) {
  animation-delay: 0.2s;
}

.stat-item:nth-child(3) {
  animation-delay: 0.3s;
}

.stat-item:nth-child(4) {
  animation-delay: 0.4s;
}

.stat-item:not(:last-child):after {
  position: absolute;
  top: 20%;
  right: -12rpx;
  width: 1px;
  height: 60%;
  content: '';
  background-color: #f0f0f0;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-value {
  display: block;
  margin-bottom: 8rpx;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1.2;
  color: #00c9a7;
}

.stat-value-small {
  display: block;
  width: auto;
  max-width: none;
  padding: 6rpx 16rpx;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.3;
  color: #00c9a7;
  text-align: center;
  white-space: normal;
  background: rgba(0, 201, 167, 0.1);
  border-radius: 8rpx;
}

.target-position {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  line-height: 1;
  color: #666;
}

.section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}

.section-subtitle {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.section-link {
  font-size: 28rpx;
  color: #00c9a7;
}

.radar-card {
  padding: 28rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  /* 性能优化：GPU加速 */
  transform: translateZ(0);
  will-change: transform, box-shadow;
  backface-visibility: hidden;

  &.high-priority {
    border-left: 6rpx solid #ff6b6b;
  }

  &:active {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    transform: translateY(-2rpx) translateZ(0);
  }
}

.task-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  margin-right: 24rpx;
  font-size: 28rpx;
  color: #00c9a7;
  background: rgba(0, 201, 167, 0.1);
  border-radius: 50%;
}

.task-content {
  flex: 1;
}

.task-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #222;
}

.task-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.task-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 开发者面板样式 */
.developer-panel {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9998;
  padding: 32rpx;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx 32rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

.developer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.developer-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #00c9a7;
}

.developer-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  font-size: 48rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.developer-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
  max-height: 300rpx;
  margin-bottom: 24rpx;
  overflow-y: auto;
}

.dev-btn {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: #00c9a7;
  text-align: center;
  background: rgba(0, 201, 167, 0.2);
  border: 2rpx solid #00c9a7;
  border-radius: 16rpx;

  &:active {
    background: rgba(0, 201, 167, 0.3);
  }

  &.test-btn {
    font-weight: 600;
    color: #fff;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    border: 2rpx solid #ff6b6b;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);

    &:active {
      background: linear-gradient(135deg, #ff5252 0%, #ff7979 100%);
      transform: scale(0.98);
    }
  }
}

.developer-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.8;
}

.developer-trigger {
  position: fixed;
  right: 32rpx;
  bottom: 200rpx;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.3);
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;

  &:active {
    background: rgba(0, 0, 0, 0.2);
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }

  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 骨架屏样式 - 性能优化版 */
.skeleton-container {
  padding: 32rpx;
  animation: fadeIn 0.3s ease-out;
}

.skeleton-welcome,
.skeleton-button,
.skeleton-stat,
.skeleton-chart {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 16rpx;
  animation: loading 1.5s infinite;
  will-change: background-position;
  /* 优化动画性能 */
}

.skeleton-welcome {
  height: 120rpx;
  margin-bottom: 32rpx;
}

.skeleton-button {
  height: 96rpx;
  margin-bottom: 32rpx;
}

.skeleton-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  background: #fff;
  border-radius: 32rpx;
}

.skeleton-stat {
  height: 80rpx;
}

.skeleton-chart {
  height: 400rpx;
  margin-bottom: 48rpx;
  background: #fff;
  border-radius: 24rpx;
}

/* 懒加载占位符样式 */
.radar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.placeholder-circle {
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(45deg, #e9ecef 25%, transparent 25%),
    linear-gradient(-45deg, #e9ecef 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #e9ecef 75%),
    linear-gradient(-45deg, transparent 75%, #e9ecef 75%);
  background-position:
    0 0,
    0 10rpx,
    10rpx -10rpx,
    -10rpx 0rpx;
  background-size: 20rpx 20rpx;
  border-radius: 50%;
  animation: placeholderPulse 1.5s ease-in-out infinite;
}

.placeholder-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #6c757d;
}

.progress-placeholder {
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.placeholder-bar {
  height: 24rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 12rpx;
  animation: loading 1.5s infinite;
  will-change: background-position;
}

.placeholder-bar:nth-child(1) {
  width: 100%;
}

.placeholder-bar:nth-child(2) {
  width: 80%;
}

.placeholder-bar:nth-child(3) {
  width: 90%;
  margin-bottom: 0;
}

.bottom-spacer {
  height: 100rpx;
}

/* ==================== 底部安全距离 ==================== */
.bottom-safe-area {

  // #ifdef MP-WEIXIN
  height: calc(60rpx + env(safe-area-inset-bottom));
  // #endif

  // #ifdef H5
  height: 80rpx;
  background: transparent;
  // #endif
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes placeholderPulse {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/* ==================== 学习计划样式 ==================== */
.plan-card {
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.plan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 28rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.plan-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.plan-count {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  background: #00c9a7;
  border-radius: 50%;
}

.daily-plans {
  padding: 16rpx 28rpx;
}

.plan-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-checkbox {
  padding: 4rpx;
  margin-right: 16rpx;
}

.checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #00c9a7;
  border-radius: 50%;
}

.checkbox.checked {
  background: #00c9a7;
}

.check-icon {
  font-size: 24rpx;
  color: #fff;
}

.plan-info {
  flex: 1;
  padding-right: 16rpx;
}

.plan-name {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.plan-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

.plan-category {
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  color: #fff;
  background: #00b39a;
  border-radius: 16rpx;
}

.plan-desc {
  font-size: 24rpx;
  color: #666;
}

.plan-actions {
  padding-left: 16rpx;
}

.plan-delete-btn {
  padding: 8rpx;
  font-size: 32rpx;
  color: #ff4d4f;
  opacity: 0.6;
}

.plan-delete-btn:active {
  opacity: 1;
}

.empty-plans {
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

.empty-icon {
  display: inline-flex;
  margin-bottom: 16rpx;
  font-size: 64rpx;
}

.add-plan-btn {
  display: flex;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #00c9a7;
  background: rgba(0, 201, 167, 0.05);
  border-radius: 16rpx;
}

.add-plan-btn:active {
  background: rgba(0, 201, 167, 0.1);
}

.plan-done .plan-name {
  color: #999;
  text-decoration: line-through;
}

.section-switcher {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.switcher-item {
  position: relative;
  z-index: 1;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.switcher-item.active {
  color: #00c9a7;
}

.switcher-item.active::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 40rpx;
  height: 4rpx;
  content: '';
  background: #00c9a7;
  border-radius: 4rpx;
  transform: translateX(-50%);
}

.switcher-icon {
  margin-bottom: 8rpx;
  font-size: 36rpx;
  color: #666;
}

.switcher-item.active .switcher-icon {
  color: #00c9a7;
}

.switcher-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
}

.switcher-item.active .switcher-text {
  font-weight: bold;
  color: #00c9a7;
}

.content-section {
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  animation: fadeIn 0.3s ease;
}

.stat-unit {
  font-size: 24rpx;
  font-weight: normal;
  color: #00c9a7;
  opacity: 0.8;
}
</style>
