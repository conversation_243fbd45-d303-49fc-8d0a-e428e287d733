/**
 * Smart Cache Manager
 * Provides intelligent caching with LRU eviction, compression, and background refresh
 */

import { performanceMonitor } from './performanceMonitor'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
  compressed: boolean
  tags: string[]
  size: number
}

interface CacheOptions {
  ttl?: number
  tags?: string[]
  compress?: boolean
}

interface CacheStats {
  size: number
  maxSize: number
  hitRate: number
  totalHits: number
  totalMisses: number
  evictions: number
  memoryUsage: number
}

class SmartCache {
  private cache = new Map<string, CacheEntry<any>>()
  private readonly maxSize: number
  private readonly maxMemory: number // bytes
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    sets: 0,
    deletes: 0,
  }

  constructor(maxSize: number = 1000, maxMemory: number = 50 * 1024 * 1024) {
    // 50MB default
    this.maxSize = maxSize
    this.maxMemory = maxMemory
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    performanceMonitor.startTiming('smartCache.get')

    const entry = this.cache.get(key)

    if (!entry) {
      this.stats.misses++
      performanceMonitor.endTiming('smartCache.get')
      performanceMonitor.incrementCounter('cache.miss')
      return null
    }

    // Check if expired
    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      this.stats.misses++
      performanceMonitor.endTiming('smartCache.get')
      performanceMonitor.incrementCounter('cache.expired')
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = now

    this.stats.hits++
    performanceMonitor.endTiming('smartCache.get')
    performanceMonitor.incrementCounter('cache.hit')

    // Decompress if needed
    let data = entry.data
    if (entry.compressed && typeof data === 'string') {
      try {
        data = this.decompress(data)
      } catch (error) {
        console.warn('Failed to decompress cache entry:', error)
        this.cache.delete(key)
        return null
      }
    }

    return data
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    performanceMonitor.startTiming('smartCache.set')

    const now = Date.now()
    const ttl = options.ttl || 5 * 60 * 1000 // 5 minutes default
    const tags = options.tags || []
    const shouldCompress = options.compress !== false && this.shouldCompress(data)

    let processedData = data
    let compressed = false

    // Compress large data
    if (shouldCompress) {
      try {
        processedData = this.compress(data) as T
        compressed = true
      } catch (error) {
        console.warn('Failed to compress data:', error)
      }
    }

    const size = this.calculateSize(processedData)

    const entry: CacheEntry<T> = {
      data: processedData,
      timestamp: now,
      ttl,
      accessCount: 0,
      lastAccessed: now,
      compressed,
      tags,
      size,
    }

    // Check if we need to evict entries
    await this.ensureCapacity(size)

    this.cache.set(key, entry)
    this.stats.sets++

    performanceMonitor.endTiming('smartCache.set')
    performanceMonitor.incrementCounter('cache.set')
  }

  /**
   * Delete specific key
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.stats.deletes++
      performanceMonitor.incrementCounter('cache.delete')
    }
    return deleted
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    this.cache.clear()
    this.resetStats()
    performanceMonitor.incrementCounter('cache.clear')
  }

  /**
   * Invalidate entries by tags
   */
  async invalidateByTags(tags: string[]): Promise<number> {
    let invalidated = 0

    for (const [key, entry] of this.cache) {
      if (entry.tags.some((tag) => tags.includes(tag))) {
        this.cache.delete(key)
        invalidated++
      }
    }

    performanceMonitor.incrementCounter('cache.invalidate-by-tags')
    return invalidated
  }

  /**
   * Invalidate entries by pattern
   */
  async invalidateByPattern(pattern: RegExp): Promise<number> {
    let invalidated = 0

    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key)
        invalidated++
      }
    }

    performanceMonitor.incrementCounter('cache.invalidate-by-pattern')
    return invalidated
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0
    const memoryUsage = this.calculateTotalMemoryUsage()

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      evictions: this.stats.evictions,
      memoryUsage,
    }
  }

  /**
   * Warm cache with multiple entries
   */
  async warmCache(
    entries: Array<{ key: string; data: any; ttl?: number; tags?: string[] }>,
  ): Promise<void> {
    performanceMonitor.startTiming('smartCache.warmCache')

    const promises = entries.map((entry) =>
      this.set(entry.key, entry.data, {
        ttl: entry.ttl,
        tags: entry.tags,
      }),
    )

    await Promise.all(promises)

    performanceMonitor.endTiming('smartCache.warmCache')
    performanceMonitor.incrementCounter('cache.warm')
  }

  /**
   * Get keys by tag
   */
  getKeysByTag(tag: string): string[] {
    const keys: string[] = []

    for (const [key, entry] of this.cache) {
      if (entry.tags.includes(tag)) {
        keys.push(key)
      }
    }

    return keys
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Get all keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    let cleaned = 0
    const now = Date.now()

    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }

    if (cleaned > 0) {
      performanceMonitor.incrementCounter('cache.cleanup')
    }

    return cleaned
  }

  // Private methods

  private async ensureCapacity(newEntrySize: number): Promise<void> {
    // Check memory limit
    const currentMemory = this.calculateTotalMemoryUsage()
    if (currentMemory + newEntrySize > this.maxMemory) {
      await this.evictByMemory(newEntrySize)
    }

    // Check size limit
    if (this.cache.size >= this.maxSize) {
      await this.evictLRU(1)
    }
  }

  private async evictLRU(count: number): Promise<void> {
    const entries = Array.from(this.cache.entries())

    // Sort by last accessed time (oldest first)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)

    for (let i = 0; i < Math.min(count, entries.length); i++) {
      const [key] = entries[i]
      this.cache.delete(key)
      this.stats.evictions++
    }
  }

  private async evictByMemory(requiredSpace: number): Promise<void> {
    const entries = Array.from(this.cache.entries())

    // Sort by access frequency and recency (least valuable first)
    entries.sort(([, a], [, b]) => {
      const scoreA = a.accessCount / (Date.now() - a.lastAccessed + 1)
      const scoreB = b.accessCount / (Date.now() - b.lastAccessed + 1)
      return scoreA - scoreB
    })

    let freedSpace = 0
    let evicted = 0

    for (const [key, entry] of entries) {
      if (freedSpace >= requiredSpace) break

      this.cache.delete(key)
      freedSpace += entry.size
      evicted++
      this.stats.evictions++
    }

    console.log(`Evicted ${evicted} entries to free ${freedSpace} bytes`)
  }

  private shouldCompress(data: any): boolean {
    const size = this.calculateSize(data)
    return size > 1024 // Compress if larger than 1KB
  }

  private compress(data: any): string {
    try {
      const jsonString = JSON.stringify(data)
      // Simple compression using built-in methods
      // In a real implementation, you might use a proper compression library
      return btoa(jsonString)
    } catch (error) {
      throw new Error('Compression failed')
    }
  }

  private decompress(compressedData: string): any {
    try {
      const jsonString = atob(compressedData)
      return JSON.parse(jsonString)
    } catch (error) {
      throw new Error('Decompression failed')
    }
  }

  private calculateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size
    } catch (error) {
      // Fallback size estimation
      return JSON.stringify(data).length * 2 // Rough estimate
    }
  }

  private calculateTotalMemoryUsage(): number {
    let total = 0
    for (const entry of this.cache.values()) {
      total += entry.size
    }
    return total
  }

  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      sets: 0,
      deletes: 0,
    }
  }
}

// Export singleton instance
export const smartCache = new SmartCache()

// Export class for testing
export { SmartCache }

// Auto-cleanup expired entries every 5 minutes
setInterval(
  () => {
    const cleaned = smartCache.cleanup()
    if (cleaned > 0) {
      console.log(`Smart cache cleaned up ${cleaned} expired entries`)
    }
  },
  5 * 60 * 1000,
)
