/**
 * 面试房间接口服务
 * 提供面试进行中相关接口
 */

import { httpGet, httpPost } from '@/utils/http'
import { IResData } from '@/types/interview-select'

// API 端点常量
const INTERVIEW_ROOM_API_ENDPOINTS = {
  GET_SESSION_INFO: '/app/interview/session/info',
  GET_QUESTION: '/app/interview/session/question',
  SUBMIT_ANSWER: '/app/interview/session/submit-answer',
  END_INTERVIEW: '/app/interview/session/end',
  FEEDBACK: '/app/interview/session/feedback',
  CHECK_DEVICES: '/app/interview/session/check-devices',
  SESSION_STATUS: '/app/interview/session/status',
}

// 多模态分析API端点
const MULTIMODAL_API_ENDPOINTS = {
  COMPREHENSIVE_ANALYSIS: '/app/multimodal/analyze/comprehensive',
  AUDIO_ANALYSIS: '/app/multimodal/analyze/audio',
  VIDEO_ANALYSIS: '/app/multimodal/analyze/video',
  TEXT_ANALYSIS: '/app/multimodal/analyze/text',
  AUDIO_STREAM: '/app/multimodal/analyze/audio/stream',
  VIDEO_STREAM: '/app/multimodal/analyze/video/stream',
  ANALYSIS_HISTORY: '/app/multimodal/history',
  BATCH_ANALYSIS: '/app/multimodal/analyze/batch',
}

// 设备检查结果
export interface DeviceCheckResult {
  camera: boolean
  microphone: boolean
  network: {
    status: boolean
    speed: number
    latency: number
  }
  resolution: {
    width: number
    height: number
    supported: boolean
  }
}

// 面试会话信息
export interface SessionInfo {
  sessionId: string
  jobName: string
  company: string
  mode: string
  duration: number
  questionCount: number
  currentQuestionIndex: number
  timeRemaining: number
  status: 'waiting' | 'in-progress' | 'paused' | 'completed' | 'expired'
  startTime: string
  endTime?: string
}

// 面试问题
export interface InterviewQuestion {
  id: string
  questionId: string
  content: string
  type: 'technical' | 'behavioral' | 'case' | 'project'
  difficulty: number
  timeLimit: number
  hints?: string[]
  answerPoints?: string[]
  category: string
  subcategory?: string
  options?: Array<{
    id: string
    content: string
    isCorrect?: boolean
  }>
  metadata?: {
    skills: string[]
    topics: string[]
    experienceLevel: string
  }
}

// 提交答案请求
export interface SubmitAnswerRequest {
  sessionId: string
  questionId: string
  answer: {
    text: string
    audioUrl?: string
    videoUrl?: string
    duration?: number
    options?: string[]
  }
}

// 答案评估结果
export interface AnswerAssessment {
  score: number
  feedback: string
  strengths: string[]
  weaknesses: string[]
  keywordMatches: string[]
  improvementSuggestions: string[]
  audioAssessment?: {
    clarity: number
    fluency: number
    confidence: number
    pace: number
    feedback: string
  }
  videoAssessment?: {
    eyeContact: number
    posture: number
    gestures: number
    expressions: number
    feedback: string
  }
}

// 结束面试请求
export interface EndInterviewRequest {
  sessionId: string
  endReason?: 'completed' | 'time-up' | 'user-cancelled' | 'error'
}

// 结束面试响应
export interface EndInterviewResponse {
  sessionId: string
  totalQuestions: number
  answeredQuestions: number
  totalDuration: number
  preliminaryScore: number
  reportId: string
  reportUrl: string
}

// 面试反馈请求
export interface FeedbackRequest {
  sessionId: string
  rating: number
  difficulty: number
  comments?: string
  categories?: string[]
}

// 多模态分析结果
export interface MultimodalAnalysisResult {
  sessionId: string
  audioResult?: AudioAnalysisResult
  videoResult?: VideoAnalysisResult
  textResult?: TextAnalysisResult
  overallAssessment?: OverallAssessment
  analysisTime: number
  status: string
  metadata?: Record<string, any>
}

// 音频分析结果
export interface AudioAnalysisResult {
  clarity: number           // 清晰度 (0-100)
  fluency: number           // 流利度 (0-100)
  confidence: number        // 自信度 (0-100)
  pace: number              // 语速 (0-100)
  overall: number           // 总体评分 (0-100)
  transcript?: string       // 语音转文字结果
  emotionScores?: Record<string, number> // 情感分析结果
  keyInsights?: string[]    // 关键洞察
  technicalMetrics?: Record<string, any> // 技术指标
}

// 视频分析结果
export interface VideoAnalysisResult {
  eyeContact: number        // 眼神交流 (0-100)
  posture: number           // 姿态 (0-100)
  expressions: number       // 表情 (0-100)
  gestures: number          // 手势 (0-100)
  overall: number           // 总体评分 (0-100)
  detectedEmotions?: string[] // 检测到的情绪
  gestureCount?: Record<string, number> // 手势统计
  postureIssues?: string[]  // 姿态问题
  faceMetrics?: Record<string, any> // 面部指标
}

// 文本分析结果
export interface TextAnalysisResult {
  professionalKnowledge: number // 专业知识水平 (0-100)
  logicalThinking: number       // 逻辑思维能力 (0-100)
  innovation: number            // 创新能力 (0-100)
  skillMatching: number         // 技能匹配度 (0-100)
  starStructure: number         // STAR结构完整性 (0-100)
  keyWords?: string[]           // 关键词
  skillsIdentified?: string[]   // 识别的技能
  topicRelevance?: Record<string, number> // 话题相关性
  improvementSuggestions?: string[] // 改进建议
}

// 综合评估结果
export interface OverallAssessment {
  totalScore: number            // 总分 (0-100)
  level: string                 // 等级 (excellent/good/average/poor)
  percentile: number            // 百分位数
  topStrengths?: string[]       // 主要优势
  topWeaknesses?: string[]      // 主要劣势
  dimensionScores?: DimensionScore[] // 维度评分
  overallFeedback?: string      // 总体反馈
  comparisonData?: Record<string, any> // 对比数据
}

// 维度评分
export interface DimensionScore {
  dimension: string
  score: number
  description?: string
}

/**
 * @description 获取面试会话信息
 * @param sessionId 会话ID
 * @returns 会话信息
 */
export async function getSessionInfo(sessionId: string): Promise<IResData<SessionInfo>> {
  return httpGet<SessionInfo>(INTERVIEW_ROOM_API_ENDPOINTS.GET_SESSION_INFO, { sessionId })
}

/**
 * @description 获取面试问题
 * @param sessionId 会话ID
 * @param index 问题索引，不传则获取当前问题
 * @returns 面试问题
 */
export async function getQuestion(sessionId: string, index?: number): Promise<IResData<InterviewQuestion>> {
  return httpGet<InterviewQuestion>(INTERVIEW_ROOM_API_ENDPOINTS.GET_QUESTION, { 
    sessionId,
    index
  })
}

/**
 * @description 提交答案
 * @param data 答案数据
 * @returns 答案评估结果
 */
export async function submitAnswer(data: SubmitAnswerRequest): Promise<IResData<AnswerAssessment>> {
  return httpPost<AnswerAssessment>(INTERVIEW_ROOM_API_ENDPOINTS.SUBMIT_ANSWER, data)
}

/**
 * @description 结束面试
 * @param data 结束面试请求数据
 * @returns 面试结果概要
 */
export async function endInterview(data: EndInterviewRequest): Promise<IResData<EndInterviewResponse>> {
  return httpPost<EndInterviewResponse>(INTERVIEW_ROOM_API_ENDPOINTS.END_INTERVIEW, data)
}

/**
 * @description 提交面试反馈
 * @param data 反馈数据
 * @returns 反馈结果
 */
export async function submitFeedback(data: FeedbackRequest): Promise<IResData<{ success: boolean }>> {
  return httpPost<{ success: boolean }>(INTERVIEW_ROOM_API_ENDPOINTS.FEEDBACK, data)
}

/**
 * @description 检查设备状态
 * @returns 设备检查结果
 */
export async function checkDevices(): Promise<IResData<DeviceCheckResult>> {
  return httpGet<DeviceCheckResult>(INTERVIEW_ROOM_API_ENDPOINTS.CHECK_DEVICES)
}

/**
 * @description 获取会话状态
 * @param sessionId 会话ID
 * @returns 会话状态
 */
export async function getSessionStatus(sessionId: string): Promise<IResData<{
  status: 'waiting' | 'in-progress' | 'paused' | 'completed' | 'expired'
  timeRemaining: number
}>> {
  return httpGet<{
    status: 'waiting' | 'in-progress' | 'paused' | 'completed' | 'expired'
    timeRemaining: number
  }>(INTERVIEW_ROOM_API_ENDPOINTS.SESSION_STATUS, { sessionId })
}

/**
 * @description 综合多模态分析
 * @param sessionId 面试会话ID
 * @param audioFile 音频文件
 * @param videoFile 视频文件
 * @param textContent 文本内容
 * @param jobPosition 岗位信息
 * @returns 分析结果
 */
export async function comprehensiveAnalysis(
  sessionId: string,
  audioFile?: File,
  videoFile?: File,
  textContent?: string,
  jobPosition?: string
): Promise<IResData<MultimodalAnalysisResult>> {
  const formData = new FormData()
  formData.append('sessionId', sessionId || '1')

  if (audioFile) {
    formData.append('audioFile', audioFile)
  }
  if (videoFile) {
    formData.append('videoFile', videoFile)
  }
  if (textContent) {
    formData.append('textContent', textContent)
  }
  if (jobPosition) {
    formData.append('jobPosition', jobPosition)
  }

  return httpPost<MultimodalAnalysisResult>(MULTIMODAL_API_ENDPOINTS.COMPREHENSIVE_ANALYSIS, {
    sessionId,
    audioFile,
    videoFile,
    textContent,
    jobPosition,  
  })
}

/**
 * @description 音频分析
 * @param audioFile 音频文件
 * @param sessionId 会话ID
 * @returns 音频分析结果
 */
export async function analyzeAudio(
  audioFile: File,
  sessionId: string
): Promise<IResData<AudioAnalysisResult>> {
  const formData = new FormData()
  formData.append('file', audioFile)
  formData.append('sessionId', sessionId)

  return httpPost<AudioAnalysisResult>(MULTIMODAL_API_ENDPOINTS.AUDIO_ANALYSIS, formData)
}

/**
 * @description 视频分析
 * @param videoFile 视频文件
 * @param sessionId 会话ID
 * @returns 视频分析结果
 */
export async function analyzeVideo(
  videoFile: File,
  sessionId: string
): Promise<IResData<VideoAnalysisResult>> {
  const formData = new FormData()
  formData.append('file', videoFile)
  formData.append('sessionId', sessionId)

  return httpPost<VideoAnalysisResult>(MULTIMODAL_API_ENDPOINTS.VIDEO_ANALYSIS, formData)
}

/**
 * @description 文本分析
 * @param textContent 文本内容
 * @param sessionId 会话ID
 * @param jobPosition 岗位信息
 * @returns 文本分析结果
 */
export async function analyzeText(
  textContent: string,
  sessionId: string,
  jobPosition?: string
): Promise<IResData<TextAnalysisResult>> {
  const data = {
    textContent,
    sessionId,
    jobPosition
  }

  return httpPost<TextAnalysisResult>(MULTIMODAL_API_ENDPOINTS.TEXT_ANALYSIS, data)
}

/**
 * @description 获取分析历史
 * @param sessionId 会话ID
 * @returns 分析历史记录
 */
export async function getAnalysisHistory(sessionId: string): Promise<IResData<MultimodalAnalysisResult[]>> {
  return httpGet<MultimodalAnalysisResult[]>(`${MULTIMODAL_API_ENDPOINTS.ANALYSIS_HISTORY}/${sessionId}`)
}

/**
 * @description 创建实时音频分析SSE连接
 * @param sessionId 会话ID
 * @param satoken 认证token
 * @param onProgress 进度回调
 * @param onComplete 完成回调
 * @param onError 错误回调
 * @returns EventSource对象
 */
export function createAudioStreamAnalysis(
  sessionId: string,
  satoken: string,
  onProgress?: (progress: number, stage: string) => void,
  onComplete?: (result: any) => void,
  onError?: (error: string) => void
): EventSource {
  const url = `${MULTIMODAL_API_ENDPOINTS.AUDIO_STREAM}?sessionId=${sessionId || '1'}&satoken=${satoken}`
  const eventSource = new EventSource(url)

  eventSource.addEventListener('progress', (event) => {
    try {
      const data = JSON.parse(event.data)
      onProgress?.(data.progress, data.stage)
    } catch (e) {
      console.error('解析进度数据失败:', e)
    }
  })

  eventSource.addEventListener('complete', (event) => {
    try {
      const result = JSON.parse(event.data)
      onComplete?.(result)
      eventSource.close()
    } catch (e) {
      console.error('解析完成数据失败:', e)
    }
  })

  eventSource.addEventListener('error', (event) => {
    try {
      const errorData = JSON.parse((event as any).data)
      onError?.(errorData)
    } catch (e) {
      onError?.('实时音频分析连接错误')
    }
    eventSource.close()
  })

  return eventSource
}

/**
 * @description 创建实时视频分析SSE连接
 * @param sessionId 会话ID
 * @param satoken 认证token
 * @param onProgress 进度回调
 * @param onComplete 完成回调
 * @param onError 错误回调
 * @returns EventSource对象
 */
export function createVideoStreamAnalysis(
  sessionId: string,
  satoken: string,
  onProgress?: (progress: number, stage: string) => void,
  onComplete?: (result: any) => void,
  onError?: (error: string) => void
): EventSource {
  const url = `${MULTIMODAL_API_ENDPOINTS.VIDEO_STREAM}?sessionId=${sessionId}&satoken=${satoken}`
  const eventSource = new EventSource(url)

  eventSource.addEventListener('progress', (event) => {
    try {
      const data = JSON.parse(event.data)
      onProgress?.(data.progress, data.stage)
    } catch (e) {
      console.error('解析进度数据失败:', e)
    }
  })

  eventSource.addEventListener('complete', (event) => {
    try {
      const result = JSON.parse(event.data)
      onComplete?.(result)
      eventSource.close()
    } catch (e) {
      console.error('解析完成数据失败:', e)
    }
  })

  eventSource.addEventListener('error', (event) => {
    try {
      const errorData = JSON.parse((event as any).data)
      onError?.(errorData)
    } catch (e) {
      onError?.('实时视频分析连接错误')
    }
    eventSource.close()
  })

  return eventSource
}