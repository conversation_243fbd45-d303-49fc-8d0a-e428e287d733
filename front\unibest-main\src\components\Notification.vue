<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

/**
 * <AUTHOR>
 * @description 通知组件
 */

// Props 定义
interface Props {
  visible: boolean
  message: string
  type?: 'success' | 'error' | 'info' | 'warning'
  duration?: number
  closable?: boolean
  position?: 'top' | 'center' | 'bottom'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 3000,
  closable: true,
  position: 'top',
})

// Emits 定义
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 状态
const show = ref(false)
const animationClass = ref('')
let timer: any = null

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      showNotification()
    } else {
      hideNotification()
    }
  },
  { immediate: true },
)

/**
 * 显示通知
 */
function showNotification() {
  show.value = true
  animationClass.value = 'notification-enter'

  // 清除之前的定时器
  if (timer) {
    clearTimeout(timer)
  }

  // 设置自动关闭
  if (props.duration > 0) {
    timer = setTimeout(() => {
      hideNotification()
    }, props.duration)
  }
}

/**
 * 隐藏通知
 */
function hideNotification() {
  animationClass.value = 'notification-leave'

  setTimeout(() => {
    show.value = false
    emit('update:visible', false)
    emit('close')
  }, 300)
}

/**
 * 手动关闭
 */
function handleClose() {
  if (props.closable) {
    hideNotification()
  }
}

/**
 * 获取图标
 */
function getIcon() {
  switch (props.type) {
    case 'success':
      return 'i-fa-solid-check-circle'
    case 'error':
      return 'i-fa-solid-times-circle'
    case 'warning':
      return 'i-fa-solid-exclamation-triangle'
    case 'info':
    default:
      return 'i-fa-solid-info-circle'
  }
}

/**
 * 获取类型样式类
 */
function getTypeClass() {
  return `notification-${props.type}`
}

/**
 * 获取位置样式类
 */
function getPositionClass() {
  return `notification-${props.position}`
}

// 组件卸载时清理定时器
onMounted(() => {
  return () => {
    if (timer) {
      clearTimeout(timer)
    }
  }
})
</script>

<template>
  <view v-if="show" class="notification-container" :class="getPositionClass()">
    <view class="notification" :class="[getTypeClass(), animationClass]" @click="handleClose">
      <view class="notification-content">
        <text class="notification-icon" :class="getIcon()"></text>
        <text class="notification-message">{{ message }}</text>
        <text
          v-if="closable"
          class="notification-close i-fa-solid-times"
          @click.stop="handleClose"
        ></text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
/* 通知容器 */
.notification-container {
  position: fixed;
  left: 50%;
  z-index: 9999;
  transform: translateX(-50%);

  &.notification-top {
    top: 120rpx;
    /* #ifdef MP */
    top: calc(120rpx + var(--status-bar-height, 0));
    /* #endif */
  }

  &.notification-center {
    top: 50%;
    transform: translate(-50%, -50%);
  }

  &.notification-bottom {
    bottom: 120rpx;
    /* #ifdef MP */
    bottom: calc(120rpx + var(--safe-area-inset-bottom, 0));
    /* #endif */
  }
}

/* 通知主体 */
.notification {
  min-width: 480rpx;
  max-width: 640rpx;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

/* 通知内容 */
.notification-content {
  display: flex;
  align-items: center;
  position: relative;
}

.notification-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;
  word-break: break-all;
}

.notification-close {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 24rpx;
  padding: 8rpx;
  opacity: 0.6;
  transition: opacity 0.3s;

  &:active {
    opacity: 1;
  }
}

/* 不同类型的样式 */
.notification-success {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border: 2rpx solid #28a745;

  .notification-icon {
    color: #28a745;
  }

  .notification-message {
    color: #155724;
  }

  .notification-close {
    color: #28a745;
  }
}

.notification-error {
  background: linear-gradient(135deg, #fdf2f2 0%, #fed7d7 100%);
  border: 2rpx solid #dc3545;

  .notification-icon {
    color: #dc3545;
  }

  .notification-message {
    color: #721c24;
  }

  .notification-close {
    color: #dc3545;
  }
}

.notification-warning {
  background: linear-gradient(135deg, #fffbf0 0%, #ffeaa7 100%);
  border: 2rpx solid #ffc107;

  .notification-icon {
    color: #ffc107;
  }

  .notification-message {
    color: #856404;
  }

  .notification-close {
    color: #ffc107;
  }
}

.notification-info {
  background: linear-gradient(135deg, #f0f8ff 0%, #d1ecf1 100%);
  border: 2rpx solid #17a2b8;

  .notification-icon {
    color: #17a2b8;
  }

  .notification-message {
    color: #0c5460;
  }

  .notification-close {
    color: #17a2b8;
  }
}

/* 动画效果 */
.notification-enter {
  animation: notification-enter 0.3s ease-out;
}

.notification-leave {
  animation: notification-leave 0.3s ease-in;
}

@keyframes notification-enter {
  0% {
    opacity: 0;
    transform: translateY(-100rpx) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes notification-leave {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100rpx) scale(0.8);
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .notification {
    min-width: 400rpx;
    max-width: 600rpx;
    margin: 0 40rpx;
  }
}
</style>
