/**
 * @description 面试房间页面演示数据初始化器
 * 提供面试房间页面所需的所有演示数据初始化功能
 */

import { demoDataManager } from '../DemoDataManager'
import { demoDataOptimizer } from '../DemoDataOptimizer'
import { API_PATHS, registerInterviewDemoData } from './index'
import {
  createBaseEmotionData,
  createBaseMultiModalMetrics,
  generateFluctuatingEmotions,
  generateFluctuatingMultiModalMetrics,
} from './analysisGenerator'

/**
 * 初始化面试房间页面演示数据
 * 预加载所有可能需要的API数据，确保在接口请求失败时能够使用演示数据
 */
export const initInterviewRoomDemoData = (): void => {
  console.log('[InterviewRoomInitializer] 开始初始化面试房间演示数据')

  // 确保演示数据已注册
  registerInterviewDemoData()

  // 预加载面试房间相关的演示数据
  demoDataOptimizer.preloadInterviewRoomData()

  console.log('[InterviewRoomInitializer] 面试房间演示数据初始化完成')
}

/**
 * 创建WebSocket模拟器
 * 用于在WebSocket连接失败时提供模拟的实时数据
 * @param url WebSocket连接URL
 * @param options 配置选项
 * @returns 模拟的WebSocket对象
 */
export const createMockWebSocket = (url: string, options?: any): WebSocket => {
  // 检查是否是演示模式
  const isDemoMode = url.includes('demo') || !demoDataManager.isEnabled()

  // 如果不是演示模式，则创建真实的WebSocket连接
  if (!isDemoMode) {
    return new WebSocket(url, options)
  }

  console.log('[InterviewRoomInitializer] 创建模拟WebSocket连接')

  // 创建模拟的WebSocket对象
  const mockWs = {
    url,
    readyState: 0, // CONNECTING
    onopen: null as any,
    onclose: null as any,
    onmessage: null as any,
    onerror: null as any,
    send: (data: any) => {
      console.log('[MockWebSocket] 发送数据:', data)

      // 模拟接收消息
      setTimeout(
        () => {
          if (mockWs.onmessage) {
            // 解析发送的数据
            let parsedData
            try {
              parsedData = typeof data === 'string' ? JSON.parse(data) : data
            } catch (e) {
              parsedData = { type: 'unknown', data }
            }

            // 根据发送的数据类型生成不同的模拟响应
            let responseData

            if (parsedData.type === 'audio' || parsedData.action === 'analyzeAudio') {
              // 模拟音频分析响应
              const metrics = createBaseMultiModalMetrics()
              const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)

              responseData = {
                type: 'audioAnalysis',
                timestamp: Date.now(),
                metrics: fluctuatingMetrics.speech,
                transcript: '这是一段模拟的语音转文本内容...',
              }
            } else if (parsedData.type === 'video' || parsedData.action === 'analyzeVideo') {
              // 模拟视频分析响应
              const metrics = createBaseMultiModalMetrics()
              const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)
              const emotions = generateFluctuatingEmotions()

              responseData = {
                type: 'videoAnalysis',
                timestamp: Date.now(),
                metrics: fluctuatingMetrics.video,
                emotions,
                attentionLevel: Math.floor(Math.random() * 30) + 70,
              }
            } else if (parsedData.type === 'text' || parsedData.action === 'analyzeText') {
              // 模拟文本分析响应
              const metrics = createBaseMultiModalMetrics()
              const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)

              responseData = {
                type: 'textAnalysis',
                timestamp: Date.now(),
                metrics: fluctuatingMetrics.text,
                keywordMatches: ['技术', '经验', '框架', '优化'].filter(() => Math.random() > 0.5),
                sentiment: Math.floor(Math.random() * 100),
              }
            } else {
              // 默认响应
              responseData = {
                type: 'response',
                timestamp: Date.now(),
                message: '收到消息',
                originalData: parsedData,
              }
            }

            // 触发onmessage事件
            mockWs.onmessage({
              data: JSON.stringify(responseData),
              type: 'message',
              target: mockWs,
            })
          }
        },
        100 + Math.random() * 200,
      ) // 模拟100-300ms的延迟
    },
    close: () => {
      console.log('[MockWebSocket] 关闭连接')
      mockWs.readyState = 3 // CLOSED

      if (mockWs.onclose) {
        mockWs.onclose({
          code: 1000,
          reason: 'Normal closure',
          wasClean: true,
          type: 'close',
          target: mockWs,
        })
      }
    },
  } as unknown as WebSocket

  // 模拟连接成功
  setTimeout(
    () => {
      mockWs.readyState = 1 // OPEN

      if (mockWs.onopen) {
        mockWs.onopen({
          type: 'open',
          target: mockWs,
        })
      }

      // 模拟定期发送数据
      startMockDataStream(mockWs)
    },
    500 + Math.random() * 500,
  ) // 模拟500-1000ms的连接延迟

  return mockWs
}

/**
 * 开始模拟数据流
 * 定期向WebSocket连接发送模拟数据
 * @param ws WebSocket对象
 */
const startMockDataStream = (ws: any): void => {
  // 创建定时器，模拟每2秒发送一次数据
  const intervalId = setInterval(() => {
    // 如果连接已关闭，则停止发送数据
    if (ws.readyState !== 1) {
      clearInterval(intervalId)
      return
    }

    // 随机选择一种数据类型发送
    const dataTypes = ['emotionUpdate', 'suggestionUpdate', 'metricsUpdate']
    const selectedType = dataTypes[Math.floor(Math.random() * dataTypes.length)]

    let data

    if (selectedType === 'emotionUpdate') {
      // 发送情绪更新
      data = {
        type: 'emotionUpdate',
        timestamp: Date.now(),
        emotions: generateFluctuatingEmotions(),
      }
    } else if (selectedType === 'suggestionUpdate') {
      // 发送建议更新
      const suggestionTypes = ['speech', 'video', 'text']
      const suggestionLevels = ['info', 'warning', 'error']

      data = {
        type: 'suggestionUpdate',
        timestamp: Date.now(),
        suggestion: {
          id: `suggestion-${Date.now()}`,
          type: suggestionTypes[Math.floor(Math.random() * suggestionTypes.length)],
          level: suggestionLevels[Math.floor(Math.random() * suggestionLevels.length)],
          title: ['语速提示', '眼神接触', '关键词使用', '结构优化'][Math.floor(Math.random() * 4)],
          message: [
            '当前语速适中，保持这个节奏能让面试官更好地理解你的表达。',
            '建议增加与镜头的眼神接触，展示自信和专注。',
            '可以多使用行业专业术语，展示你的专业知识。',
            '回答结构可以更清晰，建议使用STAR法则组织语言。',
          ][Math.floor(Math.random() * 4)],
          action: ['保持当前状态', '看向摄像头', '使用关键词', '优化结构'][
            Math.floor(Math.random() * 4)
          ],
        },
      }
    } else {
      // 发送指标更新
      const metrics = createBaseMultiModalMetrics()
      const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)

      data = {
        type: 'metricsUpdate',
        timestamp: Date.now(),
        metrics: fluctuatingMetrics,
      }
    }

    // 触发onmessage事件
    if (ws.onmessage) {
      ws.onmessage({
        data: JSON.stringify(data),
        type: 'message',
        target: ws,
      })
    }
  }, 2000)

  // 将定时器ID保存到WebSocket对象中，以便在关闭连接时清除
  ws._mockIntervalId = intervalId
}

export default {
  initInterviewRoomDemoData,
  createMockWebSocket,
}
