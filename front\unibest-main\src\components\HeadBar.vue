<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'

/**
 * <AUTHOR>
 * @description 头部导航栏组件
 */

// 声明微信小程序全局对象类型
// #ifdef MP-WEIXIN
declare global {
  interface Window {
    wx: any
  }
  // @ts-ignore
  const wx: {
    getMenuButtonBoundingClientRect(): {
      top: number
      height: number
      width: number
      right: number
      bottom: number
      left: number
    }
  }
}
// #endif
// 定义组件属性
const props = defineProps({
  // 导航栏标题
  title: {
    type: String,
    default: '智能面试助手',
  },
  // 背景颜色，支持渐变
  background: {
    type: String,
    default: 'linear-gradient(135deg, #00C9A7 0%, #4FD1C7 100%)',
  },
  // 是否显示返回箭头
  showBack: {
    type: Boolean,
    default: true,
  },
  // 是否为TabBar模式（首页等不需要返回的页面）
  isTabBar: {
    type: Boolean,
    default: false,
  },
  // 是否显示右侧操作按钮
  showRightButton: {
    type: Boolean,
    default: false,
  },
  // 右侧按钮图标
  rightIcon: {
    type: String,
    default: 'i-fa-solid-bell',
  },
  // 右侧按钮文字
  rightText: {
    type: String,
    default: '',
  },
  // 右侧按钮的长度（rpx单位）
  rightTextWidth: {
    type: Number,
    default: 120,
  },
  // 右侧按钮的高度（rpx单位）
  rightButtonHeight: {
    type: Number,
    default: 64,
  },
  // 右侧按钮的圆角大小（rpx单位）
  rightButtonRadius: {
    type: Number,
    default: 32,
  },
  // 右侧按钮的内边距（rpx单位）
  rightButtonPadding: {
    type: Number,
    default: 16,
  },
  // 右侧按钮背景色
  rightButtonBg: {
    type: String,
    default: 'rgba(255, 255, 255, 0.2)',
  },
  // 右侧按钮边框色
  rightButtonBorder: {
    type: String,
    default: 'rgba(255, 255, 255, 0.3)',
  },
  // 右侧按钮文字颜色
  rightTextColor: {
    type: String,
    default: '#fff',
  },
  // 右侧按钮图标大小（rpx单位）
  rightIconSize: {
    type: Number,
    default: 32,
  },
  // 右侧按钮文字大小（rpx单位）
  rightTextSize: {
    type: Number,
    default: 28,
  },
  // 是否固定定位
  fixed: {
    type: Boolean,
    default: false,
  },
  // 自定义返回处理
  customBack: {
    type: Boolean,
    default: false,
  },
  // 导航栏透明度
  opacity: {
    type: Number,
    default: 1,
  },
  // 主题色
  themeColor: {
    type: String,
    default: '#00C9A7',
  },
  // 是否显示排序按钮
  showSortButton: {
    type: Boolean,
    default: false,
  },
  // 排序按钮图标
  sortIcon: {
    type: String,
    default: 'i-mdi-sort-variant',
  },
})

// 定义事件
const emit = defineEmits(['back', 'rightClick', 'sortClick'])

// 状态栏和胶囊信息
const statusBarHeight = ref(0)
const capsuleInfo = ref({
  top: 0,
  height: 44,
  width: 87,
  right: 0,
})
const navBarHeight = ref(88)

// 获取系统信息
const getSystemInfo = () => {
  try {
    // 优先从全局数据获取
    const app = getApp() as any
    if (app && app.globalData) {
      statusBarHeight.value = app.globalData.statusBarHeight
      navBarHeight.value = app.globalData.navBarHeight
      if (app.globalData.capsule) {
        capsuleInfo.value = {
          top: app.globalData.capsule.top,
          height: app.globalData.capsule.height,
          width: app.globalData.capsule.width,
          right: app.globalData.capsule.right,
        }
      }
      return
    }

    // 如果全局数据不存在，则重新获取
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0

    // #ifdef MP-WEIXIN
    // 微信小程序获取胶囊信息
    const menuButton = wx.getMenuButtonBoundingClientRect()
    capsuleInfo.value = {
      top: menuButton.top,
      height: menuButton.height,
      width: menuButton.width,
      right: menuButton.right,
    }
    // 计算导航栏高度：状态栏高度 + 胶囊按钮高度 + (胶囊距离顶部 - 状态栏高度) * 2
    navBarHeight.value =
      statusBarHeight.value +
      capsuleInfo.value.height +
      (capsuleInfo.value.top - statusBarHeight.value) * 2
    // #endif

    // #ifdef H5
    // H5端固定高度
    navBarHeight.value = statusBarHeight.value + 44
    capsuleInfo.value = {
      top: statusBarHeight.value + 4,
      height: 36,
      width: 72,
      right: 0,
    }
    // #endif

    // #ifdef APP-PLUS
    // APP端处理
    navBarHeight.value = statusBarHeight.value + 44
    capsuleInfo.value = {
      top: statusBarHeight.value + 4,
      height: 36,
      width: 72,
      right: 0,
    }
    // #endif
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 设置默认值
    statusBarHeight.value = 20
    navBarHeight.value = 64
    capsuleInfo.value = {
      top: 24,
      height: 32,
      width: 72,
      right: 0,
    }
  }
}

// 计算样式
const navBarStyle = computed(() => ({
  height: `${navBarHeight.value}px`,
  paddingTop: `${statusBarHeight.value}px`,
  background: props.background,
  opacity: props.opacity,
}))

const contentStyle = computed(() => ({
  height: `${capsuleInfo.value.height}px`,
  marginTop: `${capsuleInfo.value.top - statusBarHeight.value}px`,
}))

// 右侧按钮样式计算
const rightButtonStyle = computed(() => ({
  minWidth: `${props.rightTextWidth}rpx`,
  width: `${props.rightTextWidth}rpx`,
  height: `${props.rightButtonHeight}rpx`,
  borderRadius: `${props.rightButtonRadius}rpx`,
  padding: `0 ${props.rightButtonPadding}rpx`,
  background: props.rightButtonBg,
  borderColor: props.rightButtonBorder,
}))

// 右侧按钮图标样式计算
const rightIconStyle = computed(() => ({
  fontSize: `${props.rightIconSize}rpx`,
  color: props.rightTextColor,
}))

// 右侧按钮文字样式计算
const rightTextStyle = computed(() => ({
  fontSize: `${props.rightTextSize}rpx`,
  color: props.rightTextColor,
}))

// 处理返回事件
const handleBack = () => {
  if (props.customBack) {
    emit('back')
    return
  }

  try {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      uni.navigateBack({
        delta: 1,
      })
    } else {
      // 如果是第一个页面，跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
      })
    }
  } catch (error) {
    console.error('返回失败:', error)
    uni.showToast({
      title: '返回失败',
      icon: 'none',
    })
  }
}

// 处理右侧按钮点击
const handleRightClick = () => {
  emit('rightClick')
}

// 处理排序按钮点击
const handleSortClick = () => {
  emit('sortClick')
}

// 触发震动反馈
const triggerVibrate = () => {
  // #ifdef MP-WEIXIN || APP-PLUS
  uni.vibrateShort({
    type: 'light',
  })
  // #endif
}

onLoad(() => {
  getSystemInfo()
})

onShow(() => {
  getSystemInfo()
})

/**
 * @description 获取头部导航栏的类名
 * @returns 头部导航栏的类名
 */
const getHeadBarClass = () => {
  return {
    'head-bar--fixed': props.fixed,
    'head-bar--tabbar': props.isTabBar,
  }
}
</script>
<template>
  <view class="head-bar" :class="getHeadBarClass()" :style="navBarStyle">
    <!-- 背景装饰元素 -->
    <view class="head-bar__bg-deco deco1"></view>
    <view class="head-bar__bg-deco deco2"></view>

    <!-- 导航栏内容 -->
    <view class="head-bar__content" :style="contentStyle">
      <!-- 左侧返回按钮区域 -->
      <view class="head-bar__left">
        <view
          v-if="showBack && !isTabBar"
          class="head-bar__back-btn"
          :style="{ borderColor: themeColor }"
          @click="handleBack"
          @touchstart="triggerVibrate"
        >
          <text class="head-bar__back-icon i-fa-solid-chevron-left"></text>
        </view>
      </view>

      <!-- 中间标题区域 -->
      <view class="head-bar__center">
        <text class="head-bar__title" :class="{ 'head-bar__title--tabbar': isTabBar }">
          {{ title }}
        </text>
        <!-- TabBar模式下的装饰 -->
        <view
          v-if="isTabBar"
          class="head-bar__title-decoration"
          :style="{ backgroundColor: themeColor }"
        ></view>
      </view>

      <!-- 右侧按钮区域 -->
      <view class="head-bar__right">
        <!-- 排序按钮 -->
        <view
          v-if="showSortButton"
          class="head-bar__sort-btn"
          @click="handleSortClick"
          @touchstart="triggerVibrate"
        >
          <text :class="sortIcon" class="head-bar__sort-icon"></text>
        </view>

        <!-- 原有的右侧按钮 -->
        <view
          v-if="showRightButton"
          class="head-bar__right-btn"
          :style="rightButtonStyle"
          @click="handleRightClick"
          @touchstart="triggerVibrate"
        >
          <text
            v-if="rightIcon"
            :class="rightIcon"
            class="head-bar__right-icon"
            :style="rightIconStyle"
          ></text>
          <text v-if="rightText" class="head-bar__right-text" :style="rightTextStyle">
            {{ rightText }}
          </text>
        </view>
      </view>
    </view>

    <!-- TabBar模式下的底部装饰线 -->
    <view
      v-if="isTabBar"
      class="head-bar__bottom-line"
      :style="{ background: `linear-gradient(90deg, transparent, ${themeColor}, transparent)` }"
    ></view>

    <!-- 状态栏占位 -->
    <view class="head-bar__status-placeholder" :style="{ height: `${statusBarHeight}px` }"></view>
  </view>
</template>

<style lang="scss" scoped>
// 头部导航栏 - 毛玻璃效果优化
.head-bar {
  position: relative;
  top: 0;
  right: 0;
  left: 0;
  z-index: 998;
  width: 100%;
  overflow: hidden;
  // 毛玻璃背景效果
  background: rgba(0, 201, 167, 0.85);
  -webkit-backdrop-filter: blur(20rpx) saturate(180%);
  backdrop-filter: blur(20rpx) saturate(180%);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 2rpx 20rpx rgba(0, 201, 167, 0.2),
    0 8rpx 32rpx rgba(0, 201, 167, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);

  // 添加毛玻璃渐变叠加层
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
    content: '';
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.3) 0%,
      rgba(79, 209, 199, 0.2) 50%,
      rgba(124, 58, 237, 0.1) 100%
    );
  }

  // 固定定位
  &--fixed {
    position: fixed;
  }

  // TabBar模式样式
  &--tabbar {
    background: rgba(0, 201, 167, 0.9);
    -webkit-backdrop-filter: blur(25rpx) saturate(200%);
    backdrop-filter: blur(25rpx) saturate(200%);
    box-shadow:
      0 4rpx 25rpx rgba(0, 201, 167, 0.25),
      0 12rpx 40rpx rgba(0, 201, 167, 0.15),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.4);

    &::before {
      background: linear-gradient(
        135deg,
        rgba(0, 201, 167, 0.4) 0%,
        rgba(79, 209, 199, 0.3) 30%,
        rgba(124, 58, 237, 0.2) 100%
      );
    }

    .head-bar__content {
      align-items: flex-end;
      padding-bottom: 8rpx;
    }
  }

  // 背景装饰元素 - 毛玻璃效果
  &__bg-deco {
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(8rpx);
    backdrop-filter: blur(8rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    box-shadow:
      0 4rpx 12rpx rgba(255, 255, 255, 0.1),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
    animation: floating 6s ease-in-out infinite;

    &.deco1 {
      top: 10%;
      right: 15%;
      width: 60rpx;
      height: 60rpx;
    }

    &.deco2 {
      bottom: 20%;
      left: 10%;
      width: 40rpx;
      height: 40rpx;
      animation-delay: -3s;
    }
  }

  // 导航栏内容容器
  &__content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
  }

  // 左侧区域
  &__left {
    display: flex;
    flex: 0 0 120rpx;
    align-items: center;
    justify-content: flex-start;
  }

  // 返回按钮 - 增强毛玻璃效果
  &__back-btn {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    background: rgba(255, 255, 255, 0.25);
    -webkit-backdrop-filter: blur(15rpx) saturate(150%);
    backdrop-filter: blur(15rpx) saturate(150%);
    border: 1rpx solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    box-shadow:
      0 4rpx 16rpx rgba(0, 0, 0, 0.15),
      0 2rpx 8rpx rgba(255, 255, 255, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      background: rgba(255, 255, 255, 0.35);
      box-shadow:
        0 2rpx 8rpx rgba(0, 0, 0, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
      transform: scale(0.9);
    }

    // H5端悬停效果
    /* #ifdef H5 */
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      box-shadow:
        0 6rpx 20rpx rgba(0, 0, 0, 0.2),
        0 3rpx 10rpx rgba(255, 255, 255, 0.25),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
      transform: scale(1.05);
    }
    /* #endif */
  }

  // 返回图标
  &__back-icon {
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  }

  // 中间区域
  &__center {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx;
  }

  // 标题
  &__title {
    font-size: 36rpx;
    font-weight: 600;
    line-height: 1.2;
    color: #fff;
    text-align: center;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    letter-spacing: 1rpx;
    transition: all 0.3s ease;

    // TabBar模式下的标题样式
    &--tabbar {
      font-size: 40rpx;
      font-weight: 700;
      text-shadow: none;
      background: linear-gradient(45deg, #fff, #f0f8ff);
      -webkit-background-clip: text;
      background-clip: text;
      animation: titleGlow 3s ease-in-out infinite;
      -webkit-text-fill-color: transparent;
    }
  }

  // 标题装饰
  &__title-decoration {
    width: 60rpx;
    height: 4rpx;
    margin-top: 8rpx;
    background: #fff;
    border-radius: 2rpx;
    opacity: 0.8;
    animation: decorationPulse 2s ease-in-out infinite;
  }

  // 右侧区域
  &__right {
    display: flex;
    flex: 0 0 120rpx;
    align-items: center;
    justify-content: flex-end;
    gap: 16rpx;
  }

  // 排序按钮 - 增强毛玻璃效果
  &__sort-btn {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    background: rgba(255, 255, 255, 0.25);
    -webkit-backdrop-filter: blur(15rpx) saturate(150%);
    backdrop-filter: blur(15rpx) saturate(150%);
    border: 1rpx solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    box-shadow:
      0 4rpx 16rpx rgba(0, 0, 0, 0.15),
      0 2rpx 8rpx rgba(255, 255, 255, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      background: rgba(255, 255, 255, 0.35);
      box-shadow:
        0 2rpx 8rpx rgba(0, 0, 0, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
      transform: scale(0.9);
    }

    // H5端悬停效果
    /* #ifdef H5 */
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      box-shadow:
        0 6rpx 20rpx rgba(0, 0, 0, 0.2),
        0 3rpx 10rpx rgba(255, 255, 255, 0.25),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
      transform: scale(1.05);
    }
    /* #endif */
  }

  // 排序图标
  &__sort-icon {
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  }

  // 右侧按钮 - 增强毛玻璃效果
  &__right-btn {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.25);
    -webkit-backdrop-filter: blur(15rpx) saturate(150%);
    backdrop-filter: blur(15rpx) saturate(150%);
    border: 1rpx solid rgba(255, 255, 255, 0.4);
    box-shadow:
      0 4rpx 16rpx rgba(0, 0, 0, 0.15),
      0 2rpx 8rpx rgba(255, 255, 255, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      background: rgba(255, 255, 255, 0.35);
      box-shadow:
        0 2rpx 8rpx rgba(0, 0, 0, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
      transform: scale(0.9);
    }

    // H5端悬停效果
    /* #ifdef H5 */
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      box-shadow:
        0 6rpx 20rpx rgba(0, 0, 0, 0.2),
        0 3rpx 10rpx rgba(255, 255, 255, 0.25),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
      transform: scale(1.05);
    }
    /* #endif */
  }

  // 右侧图标
  &__right-icon {
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  }

  // 右侧文字
  &__right-text {
    margin-left: 8rpx;
    font-weight: 500;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
  }

  // 底部装饰线
  &__bottom-line {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 2rpx;
    opacity: 0.6;
  }

  // 状态栏占位（用于内容计算）
  &__status-placeholder {
    position: absolute;
    top: 0;
    left: -9999rpx;
    width: 1rpx;
    pointer-events: none;
    visibility: hidden;
  }
}

// 动画定义
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(5deg);
  }
}

@keyframes titleGlow {
  0%,
  100% {
    text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8);
  }
}

@keyframes decorationPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.2);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .head-bar {
    &__title {
      font-size: 32rpx;

      &--tabbar {
        font-size: 36rpx;
      }
    }

    &__back-btn,
    &__right-btn {
      width: 56rpx;
      height: 56rpx;
    }

    &__back-icon,
    &__right-icon {
      font-size: 28rpx;
    }
  }
}

// 暗黑模式支持
@media (prefers-color-scheme: dark) {
  .head-bar {
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);

    &--tabbar {
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.4);
    }

    &__back-btn,
    &__right-btn {
      background: rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 0.2);
    }
  }
}

// 小程序端特殊样式
/* #ifdef MP */
.head-bar {
  // 确保在小程序中正确显示
  &__content {
    // 微信小程序胶囊按钮适配
    padding-right: 200rpx; // 给胶囊按钮让出空间
  }

  &__right {
    // 在小程序中右侧按钮需要避开胶囊
    margin-right: -170rpx;
  }
}
/* #endif */

// H5端特殊样式 - 增强毛玻璃效果
/* #ifdef H5 */
.head-bar {
  // H5端可以使用更多高级特性
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  backdrop-filter: blur(25rpx) saturate(180%);

  // 增强边框效果
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);

  // 添加渐变遮罩
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    pointer-events: none;
    content: '';
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.05) 100%
    );
  }

  &__back-btn,
  &__right-btn {
    cursor: pointer;
    user-select: none;

    // H5端增强毛玻璃效果
    -webkit-backdrop-filter: blur(20rpx) saturate(180%);
    backdrop-filter: blur(20rpx) saturate(180%);

    &:hover {
      -webkit-backdrop-filter: blur(25rpx) saturate(200%);
      backdrop-filter: blur(25rpx) saturate(200%);
    }
  }
}
/* #endif */
</style>
