/**
 * 认证模块类型定义
 */

// 基础响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 登录方式枚举
export enum LoginMethod {
  SMS = 'sms',
  PASSWORD = 'password',
  SOCIAL = 'social',
}

// 通知类型枚举
export enum NotificationType {
  SUCCESS = 'success',
  ERROR = 'error',
  INFO = 'info',
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
}

// 短信验证码参数
export interface SendSmsCodeParams {
  phone: string
}

// 邮箱验证码参数
export interface SendEmailCodeParams {
  email: string
}

// 手机验证码登录参数
export interface LoginByPhoneParams {
  phone: string
  code: string
  clientId?: string
  grantType?: string
}

// 密码登录参数
export interface LoginByPasswordParams {
  phone: string
  password: string
  clientId?: string
  grantType?: string
}

// 用户注册参数
export interface RegisterParams {
  email: string
  code: string
  password: string
  realName: string
  studentId: string
  major: string
  grade: string
  clientId?: string
  grantType?: string
}

// 检查手机号存在参数
export interface CheckPhoneExistsParams {
  phone: string
}

// 检查邮箱存在参数
export interface CheckEmailExistsParams {
  email: string
}

// 重置密码参数
export interface ResetPasswordParams {
  phone: string
  code: string
  newPassword: string
}

// 修改密码参数
export interface ChangePasswordParams {
  password: string
  newPassword: string
}

// 第三方登录参数
export interface ThirdPartyLoginParams {
  source: string
  socialCode: string
  socialState?: string
  clientId?: string
  grantType?: string
}

// 用户基础信息
export interface UserInfo {
  id: string
  name: string
  phone?: string
  email?: string
  realName?: string
  studentId?: string
  major?: string
  grade?: string
  avatar?: string
  tokenName?: string
  tokenValue?: string
  status?: UserStatus
  openid?: string
}

// 扩展用户信息（兼容现有store）
export interface ExtendedUserInfo extends UserInfo {
  nickname: string
  token: string
}

// Token刷新响应
export interface RefreshTokenResponse {
  access_token: string
  expire_in: number
  device_type: string
}

// 在线状态响应
export interface OnlineStatusResponse {
  online: boolean
  userId?: string
  loginTime?: string
  expireTime?: number
}

// 检查存在性响应
export interface CheckExistsResponse {
  exists: boolean
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean
  pattern?: RegExp
  minLength?: number
  maxLength?: number
  message: string
  validator?: (value: string) => boolean
}

// 表单字段验证规则映射
export interface FormValidationRules {
  [fieldName: string]: ValidationRule[]
}

// 登录表单数据
export interface LoginFormData {
  phone: string
  code?: string
  password?: string
  loginMethod: LoginMethod
}

// 注册表单数据
export interface RegisterFormData {
  email: string
  studentId: string
  realName: string
  major: string
  grade: string
  password: string
  confirmPassword: string
  code: string
  agreement: boolean
}

// 忘记密码表单数据
export interface ForgetPasswordFormData {
  phone: string
  code: string
  newPassword: string
  confirmPassword: string
}

// 表单错误状态
export interface FormErrors {
  [fieldName: string]: string
}

// 密码强度等级
export enum PasswordStrength {
  WEAK = 'weak',
  MEDIUM = 'medium',
  STRONG = 'strong',
}

// 密码强度信息
export interface PasswordStrengthInfo {
  level: PasswordStrength
  text: string
  class: string
}

// 通知消息配置
export interface NotificationConfig {
  show: boolean
  message: string
  type: NotificationType
  icon: string
  duration?: number
}

// 验证码发送状态
export interface CodeSendStatus {
  loading: boolean
  countdown: number
  buttonText: string
  disabled: boolean
}

// 专业选项
export interface MajorOption {
  value: string
  text: string
}

// 年级选项
export type GradeOption = string

// 默认客户端配置
export interface ClientConfig {
  clientId: string
  grantType: string
}
