<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// 引入API服务
import {
  getVideoDetail,
  getRelatedVideos,
  getVideoComments,
  publishVideoComment,
  toggleVideoLike,
  toggleVideoCollect,
  toggleInstructorFollow,
  toggleCommentLike as toggleCommentLikeAPI,
  updateVideoProgress,
  getVideoPlayRecord,
  incrementVideoView,
  shareVideo,
  checkVideoPurchaseStatus,
  saveVideoPlayRecord,
  // @ts-ignore
} from '@/service/video'
// 引入类型定义
import type {
  VideoDetail,
  VideoComment,
  RelatedVideo,
  CommentQueryParams,
  // @ts-ignore
} from '@/types/video'

// 页面状态
const isLoading = ref(true)
const videoId = ref<number | null>(null)
const videoContext = ref<any>(null)
const isPlaying = ref(false)
const showPlayingIndicator = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const isVideoReady = ref(true)
const videoError = ref(false)

// 视频详情数据
const videoDetail = ref<VideoDetail | null>(null)

// 评论数据
const comments = ref<VideoComment[]>([])
const commentPagination = ref({
  page: 1,
  pageSize: 20,
  total: 0,
  hasMore: true,
})
const isLoadingComments = ref(false)

// 相关推荐视频
const relatedVideos = ref<RelatedVideo[]>([])

// 新评论内容
const newComment = ref('')
const isSubmittingComment = ref(false)

// 回复评论相关
const replyingTo = ref<VideoComment | null>(null)
const replyContent = ref('')

// 播放进度记录定时器
let progressTimer: number | null = null

// 开发模式开关 - 设置为true时强制使用演示数据
const isDemoMode = ref(false)

// 标签切换状态
const activeTab = ref('info') // 'info' | 'comments' | 'related'
const tabsLoaded = ref({
  info: false,
  comments: false,
  related: false,
})
const loadingTab = ref('')

// 演示数据定义
const demoVideoDetail: VideoDetail = {
  id: 1,
  title: 'Vue3 + TypeScript 实战开发教程',
  instructor: 'TechMaster',
  duration: '45:30',
  thumbnail: 'https://qiniu-web-assets.dcloud.net.cn/video/sample/2minute-demo.mp4',
  category: '前端开发',
  difficulty: '进阶',
  rating: 4.8,
  studentCount: 1250,
  price: 199,
  isFree: false,
  isCompleted: false,
  completionRate: 35,
  tags: ['Vue3', 'TypeScript', '组件开发', '状态管理'],
  description:
    '本课程深入讲解Vue3与TypeScript的结合使用，从基础语法到高级特性，通过实战项目帮助开发者掌握现代前端开发技术栈。课程内容包括：组合式API、响应式系统、组件设计模式、状态管理、性能优化等核心知识点。',
  viewCount: 28500,
  isBookmarked: false,
  createTime: '2024-01-15',
  updateTime: '2024-01-20',
  videoUrl: 'https://qiniu-web-assets.dcloud.net.cn/video/sample/2minute-demo.mp4',
  likeCount: 1580,
  collectCount: 892,
  shareCount: 234,
  isLiked: false,
  isCollected: false,
  publishTime: '2024-01-15',
  instructorAvatar: 'https://picsum.photos/80/80?random=100',
  instructorFollowers: 8520,
  isFollowed: false,
}

const demoRelatedVideos: RelatedVideo[] = [
  {
    id: 2,
    title: 'React Hooks 深度解析',
    instructor: 'ReactGuru',
    duration: '32:15',
    thumbnail: 'https://picsum.photos/300/200?random=2',
    viewCount: 15200,
    rating: 4.6,
    category: '前端开发',
    isFree: true,
    price: 0,
  },
  {
    id: 3,
    title: 'Node.js 全栈开发实战',
    instructor: 'BackendExpert',
    duration: '58:42',
    thumbnail: 'https://picsum.photos/300/200?random=3',
    viewCount: 22800,
    rating: 4.9,
    category: '后端开发',
    isFree: false,
    price: 299,
  },
  {
    id: 4,
    title: '微信小程序开发指南',
    instructor: 'MiniAppDev',
    duration: '41:18',
    thumbnail: 'https://picsum.photos/300/200?random=4',
    viewCount: 18600,
    rating: 4.7,
    category: '移动开发',
    isFree: false,
    price: 159,
  },
  {
    id: 5,
    title: 'CSS Grid 布局详解',
    instructor: 'CSSMaster',
    duration: '28:55',
    thumbnail: 'https://picsum.photos/300/200?random=5',
    viewCount: 9800,
    rating: 4.5,
    category: '前端开发',
    isFree: true,
    price: 0,
  },
  {
    id: 6,
    title: 'Docker 容器化部署',
    instructor: 'DevOpsGuru',
    duration: '52:30',
    thumbnail: 'https://picsum.photos/300/200?random=6',
    viewCount: 13400,
    rating: 4.8,
    category: '运维部署',
    isFree: false,
    price: 259,
  },
  {
    id: 7,
    title: 'JavaScript 性能优化',
    instructor: 'JSExpert',
    duration: '36:45',
    thumbnail: 'https://picsum.photos/300/200?random=7',
    viewCount: 20100,
    rating: 4.6,
    category: '前端开发',
    isFree: true,
    price: 0,
  },
]

const demoComments: VideoComment[] = [
  {
    id: 1,
    userId: 1001,
    userName: '前端小白',
    userAvatar: 'https://picsum.photos/68/68?random=201',
    content:
      '老师讲得很清楚，Vue3的组合式API确实比选项式API更灵活，特别是在逻辑复用方面。期待后续课程！',
    publishTime: '2小时前',
    likeCount: 12,
    isLiked: false,
    replies: [
      {
        id: 101,
        userId: 1002,
        userName: 'Vue爱好者',
        userAvatar: 'https://picsum.photos/52/52?random=202',
        content: '同感！组合式API的确让代码更清晰了',
        publishTime: '1小时前',
        likeCount: 3,
        isLiked: false,
      },
    ],
  },
  {
    id: 2,
    userId: 1003,
    userName: '全栈开发者',
    userAvatar: 'https://picsum.photos/68/68?random=203',
    content:
      'TypeScript的类型系统真的很强大，配合Vue3使用体验非常好。希望老师能多讲讲高级类型的使用技巧。',
    publishTime: '3小时前',
    likeCount: 8,
    isLiked: true,
    replies: [
      {
        id: 102,
        userId: 1004,
        userName: 'TS新手',
        userAvatar: 'https://picsum.photos/52/52?random=204',
        content: '确实，泛型那块还是有点难理解',
        publishTime: '2小时前',
        likeCount: 2,
        isLiked: false,
      },
      {
        id: 103,
        userId: 1005,
        userName: 'TypeScript专家',
        userAvatar: 'https://picsum.photos/52/52?random=205',
        content: '@TS新手 可以多看看官方文档，泛型其实就是参数化类型',
        publishTime: '1小时前',
        likeCount: 5,
        isLiked: false,
      },
    ],
  },
  {
    id: 3,
    userId: 1006,
    userName: '学习达人',
    userAvatar: 'https://picsum.photos/68/68?random=206',
    content:
      '课程内容很实用，跟着老师的步骤一步步做下来，收获很大。建议新手先把基础打牢再来学这个。',
    publishTime: '5小时前',
    likeCount: 15,
    isLiked: false,
  },
  {
    id: 4,
    userId: 1007,
    userName: '代码小匠',
    userAvatar: 'https://picsum.photos/68/68?random=207',
    content: '响应式系统的讲解很透彻，终于理解了ref和reactive的区别和使用场景。感谢老师！',
    publishTime: '6小时前',
    likeCount: 20,
    isLiked: true,
    replies: [
      {
        id: 104,
        userId: 1008,
        userName: '新手小白',
        userAvatar: 'https://picsum.photos/52/52?random=208',
        content: '能简单解释一下ref和reactive的区别吗？',
        publishTime: '4小时前',
        likeCount: 1,
        isLiked: false,
      },
    ],
  },
  {
    id: 5,
    userId: 1009,
    userName: '技术探索者',
    userAvatar: 'https://picsum.photos/68/68?random=209',
    content: '老师，能否出一期关于Vue3性能优化的课程？比如虚拟DOM的优化策略等。',
    publishTime: '8小时前',
    likeCount: 25,
    isLiked: false,
  },
]

// 计算属性：当前评论输入内容
const currentCommentInput = computed({
  get: () => (replyingTo.value ? replyContent.value : newComment.value),
  set: (value: string) => {
    if (replyingTo.value) {
      replyContent.value = value
    } else {
      newComment.value = value
    }
  },
})

/**
 * @description 获取视频ID参数
 */
const getVideoId = (): void => {
  // 从路由参数获取视频ID
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}
  videoId.value = options.id ? parseInt(options.id) : 1
}

/**
 * @description 加载视频详情
 */
const loadVideoDetail = async (): Promise<void> => {
  if (!videoId.value) return

  // 如果启用了演示模式，直接使用演示数据
  if (isDemoMode.value) {
    console.log('演示模式已启用，使用演示数据')
    videoDetail.value = {
      ...demoVideoDetail,
      id: videoId.value,
    }

    uni.showToast({
      title: '演示模式已启用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    const response = await getVideoDetail(videoId.value)
    if (response.code === 200) {
      videoDetail.value = response.data

      // 增加播放次数
      incrementVideoView(videoId.value).catch(console.error)

      // 检查购买状态
      if (!response.data.isFree && response.data.price > 0) {
        const purchaseStatus = await checkVideoPurchaseStatus(videoId.value)
        if (!purchaseStatus.data.isPurchased) {
          uni.showModal({
            title: '付费课程',
            content: `此课程需要付费购买，价格：¥${response.data.price}`,
            confirmText: '购买',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: `/pages/pay/pay?id=${videoId.value}&type=video`,
                })
              } else {
                uni.navigateBack()
              }
            },
          })
          return
        }
      }
    } else {
      throw new Error(response.message || '加载失败')
    }
  } catch (error) {
    console.error('加载视频详情失败:', error)
    console.log('使用演示数据')

    // 使用演示数据
    videoDetail.value = {
      ...demoVideoDetail,
      id: videoId.value,
    }

    uni.showToast({
      title: '网络连接失败，已加载演示数据',
      icon: 'none',
      duration: 2000,
    })
  }
}

/**
 * @description 加载相关推荐视频
 */
const loadRelatedVideos = async (): Promise<void> => {
  if (!videoId.value) return

  // 如果启用了演示模式，直接使用演示数据
  if (isDemoMode.value) {
    console.log('演示模式已启用，使用演示数据 - 相关视频')
    relatedVideos.value = demoRelatedVideos
    return
  }

  try {
    const response = await getRelatedVideos(videoId.value, { limit: 6 })
    if (response.code === 200) {
      relatedVideos.value = response.data
    }
  } catch (error) {
    console.error('加载相关视频失败:', error)
    console.log('使用演示数据 - 相关视频')

    // 使用演示数据
    relatedVideos.value = demoRelatedVideos
  }
}

/**
 * @description 加载评论列表
 * @param loadMore 是否加载更多
 */
const loadComments = async (loadMore = false): Promise<void> => {
  if (!videoId.value || isLoadingComments.value) return

  // 如果启用了演示模式，直接使用演示数据
  if (isDemoMode.value) {
    console.log('演示模式已启用，使用演示数据 - 评论')

    if (loadMore) {
      // 模拟加载更多时没有更多数据
      commentPagination.value.hasMore = false
    } else {
      // 初始加载使用演示数据
      comments.value = demoComments
      commentPagination.value.page = 1
      commentPagination.value.total = demoComments.length
      commentPagination.value.hasMore = false
    }
    return
  }

  try {
    isLoadingComments.value = true

    const params: CommentQueryParams = {
      videoId: videoId.value,
      page: loadMore ? commentPagination.value.page + 1 : 1,
      pageSize: commentPagination.value.pageSize,
      sortBy: 'latest',
    }

    const response = await getVideoComments(params)
    if (response.code === 200) {
      const { comments: newComments, total, hasMore } = response.data

      if (loadMore) {
        comments.value.push(...newComments)
        commentPagination.value.page++
      } else {
        comments.value = newComments
        commentPagination.value.page = 1
      }

      commentPagination.value.total = total
      commentPagination.value.hasMore = hasMore
    }
  } catch (error) {
    console.error('加载评论失败:', error)
    console.log('使用演示数据 - 评论')

    // 使用演示数据
    if (loadMore) {
      // 模拟加载更多时没有更多数据
      commentPagination.value.hasMore = false
    } else {
      // 初始加载使用演示数据
      comments.value = demoComments
      commentPagination.value.page = 1
      commentPagination.value.total = demoComments.length
      commentPagination.value.hasMore = false
    }
  } finally {
    isLoadingComments.value = false
  }
}

/**
 * @description 切换点赞状态
 */
const toggleLike = async (): Promise<void> => {
  if (!videoDetail.value || !videoId.value) return

  // 如果启用了演示模式，直接执行本地操作
  if (isDemoMode.value) {
    console.log('演示模式已启用，模拟点赞操作')

    videoDetail.value.isLiked = !videoDetail.value.isLiked
    if (videoDetail.value.isLiked) {
      videoDetail.value.likeCount++
      uni.showToast({
        title: '点赞成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      videoDetail.value.likeCount--
      uni.showToast({
        title: '已取消点赞',
        icon: 'none',
        duration: 1500,
      })
    }
    return
  }

  try {
    const response = await toggleVideoLike(videoId.value, !videoDetail.value.isLiked)
    if (response.code === 200) {
      videoDetail.value.isLiked = !videoDetail.value.isLiked
      if (videoDetail.value.isLiked) {
        videoDetail.value.likeCount++
        uni.showToast({
          title: '点赞成功',
          icon: 'success',
          duration: 1500,
        })
      } else {
        videoDetail.value.likeCount--
        uni.showToast({
          title: '已取消点赞',
          icon: 'none',
          duration: 1500,
        })
      }
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    console.log('使用演示数据模拟点赞操作')

    // 模拟点赞操作成功
    videoDetail.value.isLiked = !videoDetail.value.isLiked
    if (videoDetail.value.isLiked) {
      videoDetail.value.likeCount++
      uni.showToast({
        title: '点赞成功（演示模式）',
        icon: 'success',
        duration: 1500,
      })
    } else {
      videoDetail.value.likeCount--
      uni.showToast({
        title: '已取消点赞（演示模式）',
        icon: 'none',
        duration: 1500,
      })
    }
  }
}

/**
 * @description 切换收藏状态
 */
const toggleCollect = async (): Promise<void> => {
  if (!videoDetail.value || !videoId.value) return

  // 如果启用了演示模式，直接执行本地操作
  if (isDemoMode.value) {
    console.log('演示模式已启用，模拟收藏操作')

    videoDetail.value.isCollected = !videoDetail.value.isCollected
    if (videoDetail.value.isCollected) {
      videoDetail.value.collectCount++
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      videoDetail.value.collectCount--
      uni.showToast({
        title: '已取消收藏',
        icon: 'none',
        duration: 1500,
      })
    }
    return
  }

  try {
    const response = await toggleVideoCollect(videoId.value, !videoDetail.value.isCollected)
    if (response.code === 200) {
      videoDetail.value.isCollected = !videoDetail.value.isCollected
      if (videoDetail.value.isCollected) {
        videoDetail.value.collectCount++
        uni.showToast({
          title: '收藏成功',
          icon: 'success',
          duration: 1500,
        })
      } else {
        videoDetail.value.collectCount--
        uni.showToast({
          title: '已取消收藏',
          icon: 'none',
          duration: 1500,
        })
      }
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    console.log('使用演示数据模拟收藏操作')

    // 模拟收藏操作成功
    videoDetail.value.isCollected = !videoDetail.value.isCollected
    if (videoDetail.value.isCollected) {
      videoDetail.value.collectCount++
      uni.showToast({
        title: '收藏成功（演示模式）',
        icon: 'success',
        duration: 1500,
      })
    } else {
      videoDetail.value.collectCount--
      uni.showToast({
        title: '已取消收藏（演示模式）',
        icon: 'none',
        duration: 1500,
      })
    }
  }
}

/**
 * @description 分享视频
 */
const shareVideoHandler = async (): Promise<void> => {
  if (!videoId.value) return

  uni.showActionSheet({
    itemList: ['复制链接', '分享到微信', '分享到朋友圈'],
    success: async (res) => {
      const platforms = ['link', 'wechat', 'moments']
      const platform = platforms[res.tapIndex]
      const platformNames = ['复制链接', '分享到微信', '分享到朋友圈']

      try {
        await shareVideo(videoId.value!, platform)
        uni.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 1500,
        })
      } catch (error) {
        console.error('分享失败:', error)
        console.log('使用演示数据模拟分享操作')

        // 模拟分享成功
        if (videoDetail.value) {
          videoDetail.value.shareCount++
        }

        uni.showToast({
          title: `${platformNames[res.tapIndex]}成功（演示模式）`,
          icon: 'success',
          duration: 1500,
        })
      }
    },
  })
}

/**
 * @description 关注/取消关注讲师
 */
const toggleFollow = async (): Promise<void> => {
  if (!videoDetail.value) return

  // 如果启用了演示模式，直接执行本地操作
  if (isDemoMode.value) {
    console.log('演示模式已启用，模拟关注操作')

    videoDetail.value.isFollowed = !videoDetail.value.isFollowed
    if (videoDetail.value.isFollowed) {
      videoDetail.value.instructorFollowers++
      uni.showToast({
        title: '关注成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      videoDetail.value.instructorFollowers--
      uni.showToast({
        title: '已取消关注',
        icon: 'none',
        duration: 1500,
      })
    }
    return
  }

  // 假设讲师ID与视频ID相关，实际应该从videoDetail中获取
  const instructorId = videoDetail.value.id // 应该是 videoDetail.value.instructorId

  try {
    const response = await toggleInstructorFollow(instructorId, !videoDetail.value.isFollowed)
    if (response.code === 200) {
      videoDetail.value.isFollowed = !videoDetail.value.isFollowed
      if (videoDetail.value.isFollowed) {
        videoDetail.value.instructorFollowers++
        uni.showToast({
          title: '关注成功',
          icon: 'success',
          duration: 1500,
        })
      } else {
        videoDetail.value.instructorFollowers--
        uni.showToast({
          title: '已取消关注',
          icon: 'none',
          duration: 1500,
        })
      }
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    console.log('使用演示数据模拟关注操作')

    // 模拟关注操作成功
    videoDetail.value.isFollowed = !videoDetail.value.isFollowed
    if (videoDetail.value.isFollowed) {
      videoDetail.value.instructorFollowers++
      uni.showToast({
        title: '关注成功（演示模式）',
        icon: 'success',
        duration: 1500,
      })
    } else {
      videoDetail.value.instructorFollowers--
      uni.showToast({
        title: '已取消关注（演示模式）',
        icon: 'none',
        duration: 1500,
      })
    }
  }
}

/**
 * @description 切换评论点赞状态
 * @param comment 评论对象
 */
const toggleCommentLike = async (comment: VideoComment): Promise<void> => {
  // 如果启用了演示模式，直接执行本地操作
  if (isDemoMode.value) {
    console.log('演示模式已启用，模拟评论点赞操作')

    comment.isLiked = !comment.isLiked
    if (comment.isLiked) {
      comment.likeCount++
    } else {
      comment.likeCount--
    }
    return
  }

  try {
    const response = await toggleCommentLikeAPI(comment.id, !comment.isLiked)
    if (response.code === 200) {
      comment.isLiked = !comment.isLiked
      if (comment.isLiked) {
        comment.likeCount++
      } else {
        comment.likeCount--
      }
    }
  } catch (error) {
    console.error('评论点赞失败:', error)
    console.log('使用演示数据模拟评论点赞操作')

    // 模拟评论点赞操作成功
    comment.isLiked = !comment.isLiked
    if (comment.isLiked) {
      comment.likeCount++
    } else {
      comment.likeCount--
    }
  }
}

/**
 * @description 发布评论
 */
const publishComment = async (): Promise<void> => {
  const content = replyingTo.value ? replyContent.value.trim() : newComment.value.trim()

  if (!content) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none',
    })
    return
  }

  if (!videoId.value || isSubmittingComment.value) return

  // 如果启用了演示模式，直接执行本地操作
  if (isDemoMode.value) {
    console.log('演示模式已启用，模拟发布评论')

    isSubmittingComment.value = true

    // 模拟发布成功，创建一个新评论
    const mockComment: VideoComment = {
      id: Date.now(), // 使用时间戳作为临时ID
      userId: 9999,
      userName: '当前用户',
      userAvatar: 'https://picsum.photos/68/68?random=999',
      content: content,
      publishTime: '刚刚',
      likeCount: 0,
      isLiked: false,
      replies: [],
    }

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    if (replyingTo.value) {
      // 如果是回复，添加到对应评论的回复列表
      const parentComment = comments.value.find((c) => c.id === replyingTo.value!.id)
      if (parentComment) {
        if (!parentComment.replies) {
          parentComment.replies = []
        }
        parentComment.replies.push(mockComment)
      }
      replyContent.value = ''
      replyingTo.value = null
    } else {
      // 如果是新评论，添加到评论列表开头
      comments.value.unshift(mockComment)
      newComment.value = ''
    }

    uni.showToast({
      title: '评论发布成功',
      icon: 'success',
      duration: 1500,
    })

    isSubmittingComment.value = false
    return
  }

  try {
    isSubmittingComment.value = true

    const response = await publishVideoComment({
      videoId: videoId.value,
      content,
      parentId: replyingTo.value?.id,
    })

    if (response.code === 200) {
      // 添加新评论到列表
      if (replyingTo.value) {
        // 如果是回复，添加到对应评论的回复列表
        const parentComment = comments.value.find((c) => c.id === replyingTo.value!.id)
        if (parentComment) {
          if (!parentComment.replies) {
            parentComment.replies = []
          }
          parentComment.replies.push(response.data)
        }
        replyContent.value = ''
        replyingTo.value = null
      } else {
        // 如果是新评论，添加到评论列表开头
        comments.value.unshift(response.data)
        newComment.value = ''
      }

      uni.showToast({
        title: '评论发布成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.message || '发布失败')
    }
  } catch (error) {
    console.error('发布评论失败:', error)
    console.log('使用演示数据模拟发布评论')

    // 模拟发布成功，创建一个新评论
    const mockComment: VideoComment = {
      id: Date.now(), // 使用时间戳作为临时ID
      userId: 9999,
      userName: '当前用户',
      userAvatar: 'https://picsum.photos/68/68?random=999',
      content: content,
      publishTime: '刚刚',
      likeCount: 0,
      isLiked: false,
      replies: [],
    }

    if (replyingTo.value) {
      // 如果是回复，添加到对应评论的回复列表
      const parentComment = comments.value.find((c) => c.id === replyingTo.value!.id)
      if (parentComment) {
        if (!parentComment.replies) {
          parentComment.replies = []
        }
        parentComment.replies.push(mockComment)
      }
      replyContent.value = ''
      replyingTo.value = null
    } else {
      // 如果是新评论，添加到评论列表开头
      comments.value.unshift(mockComment)
      newComment.value = ''
    }

    uni.showToast({
      title: '评论发布成功（演示模式）',
      icon: 'success',
      duration: 1500,
    })
  } finally {
    isSubmittingComment.value = false
  }
}

/**
 * @description 回复评论
 * @param comment 要回复的评论
 */
const replyToComment = (comment: VideoComment): void => {
  replyingTo.value = comment
  replyContent.value = ''
}

/**
 * @description 取消回复
 */
const cancelReply = (): void => {
  replyingTo.value = null
  replyContent.value = ''
}

/**
 * @description 跳转到相关视频
 * @param video 视频对象
 */
const goToRelatedVideo = (video: RelatedVideo): void => {
  uni.redirectTo({
    url: `/pages/learning/video-player?id=${video.id}`,
  })
}

/**
 * @description 格式化数字显示
 * @param num 数字
 * @returns 格式化后的字符串
 */
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

/**
 * @description 获取难度对应的样式类名
 * @param difficulty 难度等级（入门/进阶/高级）
 * @returns 返回对应的样式类名字符串
 */
const getDifficultyStyle = (difficulty: string): string => {
  const styleMap: Record<string, string> = {
    入门: 'difficulty-beginner',
    进阶: 'difficulty-intermediate',
    高级: 'difficulty-advanced',
  }
  return styleMap[difficulty] || 'difficulty-beginner'
}

/**
 * @description 更新播放进度
 */
const updateProgress = async (): Promise<void> => {
  if (!videoId.value || !isPlaying.value) return

  try {
    await updateVideoProgress(videoId.value, currentTime.value, duration.value)
  } catch (error) {
    console.error('更新播放进度失败:', error)
    console.log('播放进度更新失败，但不影响播放')
    // 播放进度更新失败时，不需要特殊处理，只是无法保存进度到服务器
  }
}

/**
 * @description 加载播放记录
 */
const loadPlayRecord = async (): Promise<void> => {
  if (!videoId.value) return

  try {
    const response = await getVideoPlayRecord(videoId.value)
    if (response.code === 200 && response.data) {
      const { lastPlayTime } = response.data
      if (lastPlayTime > 0 && videoContext.value) {
        // 恢复上次播放位置
        videoContext.value.seek(lastPlayTime)
        uni.showToast({
          title: `已恢复到上次播放位置`,
          icon: 'none',
          duration: 2000,
        })
      }
    }
  } catch (error) {
    console.error('加载播放记录失败:', error)
    console.log('无法获取播放记录，从头开始播放')
    // 播放记录获取失败时，从头开始播放，不需要额外处理
  }
}

/**
 * @description 视频播放事件处理
 */
const onVideoPlay = (): void => {
  isPlaying.value = true
  showPlayingIndicator.value = true
  console.log('视频开始播放')

  // 1秒后隐藏播放指示器
  setTimeout(() => {
    showPlayingIndicator.value = false
  }, 1000)

  // 开始记录播放进度
  if (!progressTimer) {
    progressTimer = setInterval(() => {
      updateProgress()
    }, 10000) // 每10秒更新一次进度
  }
}

/**
 * @description 视频暂停事件处理
 */
const onVideoPause = (): void => {
  isPlaying.value = false
  showPlayingIndicator.value = false
  console.log('视频暂停播放')

  // 停止记录播放进度
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
    // 暂停时立即更新一次进度
    updateProgress()
  }
}

/**
 * @description 视频播放结束事件处理
 */
const onVideoEnded = (): void => {
  isPlaying.value = false
  if (videoDetail.value) {
    videoDetail.value.completionRate = 100
    videoDetail.value.isCompleted = true
  }

  // 停止记录播放进度
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }

  // 更新最终进度
  updateProgress()

  uni.showToast({
    title: '视频播放完成',
    icon: 'success',
    duration: 2000,
  })
}

/**
 * @description 视频时间更新事件处理
 * @param event 时间更新事件
 */
const onVideoTimeUpdate = (event: any): void => {
  const { currentTime: current, duration: total } = event.detail
  currentTime.value = current
  if (total > 0) {
    duration.value = total
    if (videoDetail.value) {
      videoDetail.value.completionRate = Math.round((current / total) * 100)
    }
  }
}

/**
 * @description 视频加载完成事件处理
 */
const onVideoLoadedData = (): void => {
  console.log('视频数据加载完成')
  isVideoReady.value = true
  videoError.value = false
  videoContext.value = uni.createVideoContext('videoPlayer')

  // 加载播放记录
  loadPlayRecord()

  uni.showToast({
    title: '视频加载完成',
    icon: 'success',
    duration: 1500,
  })
}

/**
 * @description 视频错误事件处理
 * @param event 错误事件
 */
const onVideoError = (event: any): void => {
  console.error('视频播放错误:', event.detail)
  videoError.value = true
  isVideoReady.value = false
  uni.showModal({
    title: '视频加载失败',
    content: '视频无法正常加载，请检查网络连接或稍后重试',
    showCancel: true,
    cancelText: '取消',
    confirmText: '重试',
    success: (res) => {
      if (res.confirm && videoContext.value) {
        // 重新加载视频
        videoError.value = false
        videoContext.value.play()
      }
    },
  })
}

/**
 * @description 视频全屏变化事件处理
 * @param event 全屏事件
 */
const onVideoFullscreenChange = (event: any): void => {
  const { fullScreen } = event.detail
  console.log('全屏状态变化:', fullScreen)

  if (fullScreen) {
    uni.showToast({
      title: '已进入全屏',
      icon: 'none',
      duration: 1000,
    })
  }
}

/**
 * @description 视频缓冲事件处理
 */
const onVideoWaiting = (): void => {
  console.log('视频缓冲中...')
}

/**
 * @description 视频可以播放事件处理
 */
const onVideoCanPlay = (): void => {
  console.log('视频可以开始播放')
  if (!isVideoReady.value) {
    isVideoReady.value = true
    videoError.value = false
  }
}

/**
 * @description 按需加载标签数据
 * @param tabName 标签名称
 */
const loadTabData = async (tabName: string): Promise<void> => {
  if (tabsLoaded.value[tabName as keyof typeof tabsLoaded.value]) {
    return // 已加载过，不重复加载
  }

  try {
    loadingTab.value = tabName

    switch (tabName) {
      case 'comments':
        await loadComments()
        break
      case 'related':
        await loadRelatedVideos()
        break
      default:
        break
    }

    // 标记为已加载
    tabsLoaded.value[tabName as keyof typeof tabsLoaded.value] = true
  } catch (error) {
    console.error(`加载${tabName}数据失败:`, error)
  } finally {
    // 清除加载状态
    loadingTab.value = ''
  }
}

/**
 * @description 切换标签
 * @param tabName 标签名称
 */
const switchTab = async (tabName: string): Promise<void> => {
  if (activeTab.value === tabName) return // 已经是当前标签，不重复切换

  activeTab.value = tabName

  // 加载对应标签的数据
  await loadTabData(tabName)
}

/**
 * @description 初始化页面数据
 */
const initPageData = async (): Promise<void> => {
  try {
    getVideoId()

    // 只加载视频详情，其他数据按需加载
    await loadVideoDetail()

    // 标记信息区域已加载
    tabsLoaded.value.info = true

    // 如果当前不是信息tab，加载对应的数据
    if (activeTab.value !== 'info') {
      await loadTabData(activeTab.value)
    }
  } catch (error) {
    console.error('页面数据初始化失败:', error)
    uni.showToast({
      title: '页面加载失败，请重试',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isLoading.value = false
  }
}

// 获取标签样式
const getAnimationStyle = (index: number): string => {
  const delay = index * 0.1
  return `animation: slideInUp 0.5s ease-out calc(0.9s + ${delay}s) both;`
}

// 保存历史记录
const saveHistory = async (): Promise<void> => {
  if (!videoId.value) return
  await saveVideoPlayRecord(videoId.value)
}

// 页面加载时执行初始化
onMounted(() => {
  initPageData()
  // 保存历史记录
  saveHistory()
})

// 页面卸载时清理定时器
onBeforeUnmount(() => {
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
})

/**
 * @description 切换演示模式
 */
const toggleDemoMode = (): void => {
  isDemoMode.value = !isDemoMode.value

  // 重置标签加载状态
  tabsLoaded.value = {
    info: false,
    comments: false,
    related: false,
  }

  // 重置到信息标签
  activeTab.value = 'info'

  if (isDemoMode.value) {
    uni.showToast({
      title: '已切换到演示模式',
      icon: 'success',
      duration: 1500,
    })
    // 如果切换到演示模式，重新加载数据
    initPageData()
  } else {
    uni.showToast({
      title: '已切换到正常模式',
      icon: 'success',
      duration: 1500,
    })
    // 如果切换到正常模式，重新加载数据
    initPageData()
  }
}
</script>

<template>
  <view class="video-player-container">
    <HeadBar title="视频详情" />

    <!-- 演示模式切换按钮 -->
    <view class="demo-mode-toggle">
      <button class="demo-toggle-btn" :class="isDemoMode ? 'active' : ''" @click="toggleDemoMode">
        {{ isDemoMode ? '演示模式' : '正常模式' }}
      </button>
    </view>

    <scroll-view v-if="!isLoading" class="main-content" scroll-y>
      <!-- 视频播放器区域 -->
      <view class="video-player-section">
        <view class="video-wrapper">
          <video
            v-if="videoDetail"
            id="videoPlayer"
            class="video-player"
            :src="videoDetail.videoUrl"
            :controls="true"
            :show-play-btn="true"
            :show-center-play-btn="true"
            :show-fullscreen-btn="true"
            :show-progress="true"
            :show-loading="true"
            :poster="videoDetail.thumbnail"
            :object-fit="'contain'"
            @play="onVideoPlay"
            @pause="onVideoPause"
            @ended="onVideoEnded"
            @timeupdate="onVideoTimeUpdate"
            @loadeddata="onVideoLoadedData"
            @error="onVideoError"
            @fullscreenchange="onVideoFullscreenChange"
            @waiting="onVideoWaiting"
            @canplay="onVideoCanPlay"
          ></video>

          <!-- 视频加载状态 -->
          <view v-if="!isVideoReady && !videoError" class="loading-indicator">
            <view class="loading-spinner"></view>
            <text class="loading-text">视频加载中...</text>
          </view>

          <!-- 视频错误状态 -->
          <view v-if="videoError" class="error-indicator">
            <view class="i-mdi-alert-circle error-icon"></view>
            <text class="error-text">视频加载失败</text>
          </view>

          <!-- 播放状态指示器 -->
          <view v-if="showPlayingIndicator" class="playing-indicator">
            <view class="playing-dot"></view>
            <text class="playing-text">正在播放</text>
          </view>
        </view>
      </view>

      <!-- 视频信息/ 评论区 / 相关推荐 区域 -->
      <view class="video-info-wrapper">
        <!-- 切换标签 -->
        <view class="switch-section">
          <view
            class="switch-item"
            :class="activeTab === 'info' ? 'active' : ''"
            @click="switchTab('info')"
          >
            <text class="switch-text">视频信息</text>
          </view>
          <view
            class="switch-item"
            :class="activeTab === 'comments' ? 'active' : ''"
            @click="switchTab('comments')"
          >
            <text class="switch-text">评论区</text>
            <text v-if="commentPagination.total > 0" class="switch-badge">
              {{ commentPagination.total }}
            </text>
          </view>
          <view
            class="switch-item"
            :class="activeTab === 'related' ? 'active' : ''"
            @click="switchTab('related')"
          >
            <text class="switch-text">相关推荐</text>
          </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-section">
          <!-- 视频信息内容 -->
          <view v-if="activeTab === 'info'" class="tab-content info-content">
            <view v-if="videoDetail" class="video-info-content">
              <!-- 视频基本信息 -->
              <view class="video-basic-info">
                <view class="video-title">{{ videoDetail.title }}</view>
                <view class="video-meta">
                  <text class="meta-item">{{ formatNumber(videoDetail.viewCount) }}播放</text>
                  <text class="meta-separator">·</text>
                  <text class="meta-item">{{ commentPagination.total }}评论</text>
                  <text class="meta-separator">·</text>
                  <text class="meta-item">{{ videoDetail.publishTime }}</text>
                  <text class="meta-separator">·</text>
                  <text class="meta-item">{{ videoDetail.duration }}</text>
                </view>
              </view>

              <!-- 互动按钮区域 -->
              <view class="interaction-buttons">
                <button
                  class="interaction-btn"
                  :class="videoDetail.isLiked ? 'active' : ''"
                  @click="toggleLike"
                >
                  <view
                    :class="videoDetail.isLiked ? 'i-mdi-thumb-up' : 'i-mdi-thumb-up-outline'"
                    class="btn-icon"
                  ></view>
                  <text class="btn-count">{{ formatNumber(videoDetail.likeCount) }}</text>
                </button>

                <button class="interaction-btn dislike-btn">
                  <view class="i-mdi-thumb-down-outline btn-icon"></view>
                  <text class="btn-text">不喜欢</text>
                </button>

                <button class="interaction-btn coin-btn">
                  <view class="i-mdi-circle-outline btn-icon coin-icon"></view>
                  <text class="btn-count">2651</text>
                </button>

                <button
                  class="interaction-btn"
                  :class="videoDetail.isCollected ? 'active' : ''"
                  @click="toggleCollect"
                >
                  <view
                    :class="videoDetail.isCollected ? 'i-mdi-star' : 'i-mdi-star-outline'"
                    class="btn-icon"
                  ></view>
                  <text class="btn-count">{{ formatNumber(videoDetail.collectCount) }}</text>
                </button>

                <button class="interaction-btn" @click="shareVideoHandler">
                  <view class="i-mdi-share btn-icon"></view>
                  <text class="btn-count">{{ formatNumber(videoDetail.shareCount) }}</text>
                </button>
              </view>

              <!-- 作者信息区域 -->
              <view class="author-section">
                <view class="author-info">
                  <image class="author-avatar" :src="videoDetail.instructorAvatar" />
                  <view class="author-details">
                    <view class="author-name">{{ videoDetail.instructor }}</view>
                    <view class="author-stats">
                      <text class="stats-text">
                        {{ formatNumber(videoDetail.instructorFollowers) }} 粉丝
                      </text>
                    </view>
                  </view>
                  <button
                    class="follow-button"
                    :class="videoDetail.isFollowed ? 'followed' : ''"
                    @click="toggleFollow"
                  >
                    {{ videoDetail.isFollowed ? '已关注' : '+ 关注' }}
                  </button>
                </view>
              </view>

              <!-- 课程介绍 -->
              <view class="course-description">
                <view class="description-header">
                  <text class="description-title">课程介绍</text>
                  <view
                    class="difficulty-badge"
                    :class="getDifficultyStyle(videoDetail.difficulty)"
                  >
                    {{ videoDetail.difficulty }}
                  </view>
                </view>
                <text class="description-content">{{ videoDetail.description }}</text>

                <!-- 课程标签 -->
                <view class="course-tags">
                  <view
                    v-for="(tag, index) in videoDetail.tags.slice(0, 3)"
                    :key="tag"
                    class="course-tag"
                    :style="getAnimationStyle(index)"
                  >
                    {{ tag }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 评论区内容 -->
          <view v-if="activeTab === 'comments'" class="tab-content comments-content">
            <view class="comments-wrapper">
              <!-- 加载状态 -->
              <view v-if="loadingTab === 'comments'" class="tab-loading">
                <view class="loading-spinner"></view>
                <text class="loading-text">加载评论中...</text>
              </view>

              <!-- 评论内容 -->
              <view v-else>
                <view class="comments-header">
                  <text class="comments-title">评论 {{ commentPagination.total }}</text>
                  <view class="comments-sort">
                    <text class="sort-text">最新</text>
                    <view class="i-mdi-chevron-down sort-icon"></view>
                  </view>
                </view>

                <!-- 回复提示 -->
                <view v-if="replyingTo" class="reply-indicator">
                  <text class="reply-text">回复 @{{ replyingTo.userName }}：</text>
                  <button class="cancel-reply-btn" @click="cancelReply">
                    <view class="i-mdi-close cancel-icon"></view>
                  </button>
                </view>

                <!-- 评论输入框 -->
                <view class="comment-input-area">
                  <image class="comment-user-avatar" src="../../static/images/avatar.png" />
                  <view class="input-wrapper">
                    <input
                      class="comment-input"
                      type="text"
                      :placeholder="
                        replyingTo
                          ? '回复 @' + replyingTo.userName + '...'
                          : '友善评论，共建和谐社区...'
                      "
                      v-model="currentCommentInput"
                      @confirm="publishComment"
                    />
                    <button
                      class="send-btn"
                      @click="publishComment"
                      :disabled="isSubmittingComment"
                    >
                      <view class="i-mdi-send send-icon"></view>
                    </button>
                  </view>
                </view>

                <!-- 评论列表 -->
                <view class="comments-list" v-if="comments.length > 0">
                  <view
                    v-for="(comment, index) in comments"
                    :key="comment.id"
                    class="comment-item"
                    :style="getAnimationStyle(index)"
                  >
                    <image class="commenter-avatar" :src="comment.userAvatar" />
                    <view class="comment-main">
                      <view class="commenter-info">
                        <text class="commenter-name">{{ comment.userName }}</text>
                        <text class="comment-time">{{ comment.publishTime }}</text>
                      </view>
                      <text class="comment-content">{{ comment.content }}</text>
                      <view class="comment-actions">
                        <button
                          class="comment-action-btn like-btn"
                          :class="comment.isLiked ? 'liked' : ''"
                          @click="toggleCommentLike(comment)"
                        >
                          <view
                            :class="comment.isLiked ? 'i-mdi-thumb-up' : 'i-mdi-thumb-up-outline'"
                            class="action-icon"
                          ></view>
                          <text class="action-count">{{ comment.likeCount || '' }}</text>
                        </button>
                        <button
                          class="comment-action-btn reply-btn"
                          @click="replyToComment(comment)"
                        >
                          <view class="i-mdi-reply action-icon"></view>
                          <text class="action-text">回复</text>
                        </button>
                      </view>

                      <!-- 回复列表 -->
                      <view
                        v-if="comment.replies && comment.replies.length > 0"
                        class="replies-section"
                      >
                        <view v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                          <image class="reply-avatar" :src="reply.userAvatar" />
                          <view class="reply-content">
                            <text class="reply-author">{{ reply.userName }}</text>
                            <text class="reply-text">{{ reply.content }}</text>
                            <view class="reply-actions">
                              <text class="reply-time">{{ reply.publishTime }}</text>
                              <button class="reply-like-btn" @click="toggleCommentLike(reply)">
                                <view class="i-mdi-thumb-up-outline reply-like-icon"></view>
                                <text class="reply-like-count">{{ reply.likeCount || '' }}</text>
                              </button>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 加载更多评论 -->
                  <view v-if="commentPagination.hasMore" class="load-more-comments">
                    <button
                      class="load-more-btn"
                      @click="loadComments(true)"
                      :disabled="isLoadingComments"
                    >
                      <view v-if="isLoadingComments" class="loading-spinner"></view>
                      <text class="load-more-text">
                        {{ isLoadingComments ? '加载中...' : '加载更多评论' }}
                      </text>
                    </button>
                  </view>
                </view>

                <!-- 空状态 -->
                <view class="comments-list" v-else>
                  <view class="empty-state">
                    <view class="i-mdi-comment-outline empty-icon"></view>
                    <text class="no-comments-text">暂无评论</text>
                    <text class="empty-tip">快来发表第一条评论吧～</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 相关推荐内容 -->
          <view v-if="activeTab === 'related'" class="tab-content related-content">
            <view class="recommended-section">
              <text class="section-title">相关推荐</text>

              <!-- 加载状态 -->
              <view v-if="loadingTab === 'related'" class="tab-loading">
                <view class="loading-spinner"></view>
                <text class="loading-text">加载推荐中...</text>
              </view>

              <!-- 推荐内容 -->
              <view v-else>
                <view class="recommended-grid" v-if="relatedVideos.length > 0">
                  <view
                    v-for="(video, index) in relatedVideos"
                    :key="video.id"
                    class="recommended-card"
                    :style="getAnimationStyle(index)"
                    @click="goToRelatedVideo(video)"
                  >
                    <image class="card-thumbnail" :src="video.thumbnail" mode="aspectFill" />
                    <view class="card-overlay">
                      <text class="card-duration">{{ video.duration }}</text>
                    </view>
                    <view class="card-info">
                      <text class="card-title">{{ video.title }}</text>
                      <text class="card-author">{{ video.instructor }}</text>
                      <view class="card-stats">
                        <text class="card-views">{{ formatNumber(video.viewCount) }}播放</text>
                        <view class="card-rating">
                          <view class="i-mdi-star rating-icon"></view>
                          <text class="rating-text">{{ video.rating }}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 空状态 -->
                <view class="recommended-grid" v-else>
                  <view class="empty-state empty-state-centered">
                    <view class="i-mdi-video-outline empty-icon"></view>
                    <text class="no-related-text">暂无相关推荐</text>
                    <text class="empty-tip">探索更多精彩内容～</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载状态 -->
    <LoadingCard :visible="isLoading" text="正在加载视频..." />
  </view>
</template>

<style scoped lang="scss">
.video-player-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
  animation: fadeIn 0.8s ease-out;
}

// 演示模式切换按钮
.demo-mode-toggle {
  position: fixed;
  top: 120rpx;
  right: 20rpx;
  z-index: 1000;

  .demo-toggle-btn {
    padding: 12rpx 24rpx;
    background: rgba(255, 255, 255, 0.9);
    border: 2rpx solid #e0e0e0;
    border-radius: 24rpx;
    font-size: 22rpx;
    color: #666;
    transition: all 0.3s ease;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.active {
      background: linear-gradient(135deg, #00c9a7 0%, #22d3ee 100%);
      color: white;
      border-color: #00c9a7;
      box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
    }

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
    }
  }
}

// 切换区域样式
.switch-section {
  display: flex;
  background: white;
  border-radius: 16rpx 16rpx 0 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 201, 167, 0.1);
  border-bottom: 1rpx solid #f0f8f6;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1rpx;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 201, 167, 0.2) 50%,
      transparent 100%
    );
  }

  .switch-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 16rpx;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(0, 201, 167, 0.05) 0%,
        rgba(34, 211, 238, 0.05) 100%
      );
      transform: translateY(100%);
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover::before {
      transform: translateY(0);
    }

    &.active {
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(34, 211, 238, 0.1) 100%);
      border-bottom: 3rpx solid #00c9a7;
      transform: translateY(-2rpx);

      .switch-text {
        color: #00c9a7;
        font-weight: 600;
        transform: scale(1.05);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -1rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 60%;
        height: 3rpx;
        background: linear-gradient(90deg, #00c9a7 0%, #22d3ee 100%);
        border-radius: 2rpx;
        animation: expandWidth 0.3s ease-out;
      }
    }

    .switch-text {
      font-size: 28rpx;
      color: #666;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
      position: relative;
    }

    .switch-badge {
      position: absolute;
      top: 12rpx;
      right: 16rpx;
      background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
      color: white;
      font-size: 18rpx;
      font-weight: 600;
      padding: 4rpx 8rpx;
      border-radius: 10rpx;
      min-width: 20rpx;
      text-align: center;
      box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
      animation: pulse 2s infinite;
      z-index: 2;
    }

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1rpx;
      height: 60%;
      background: linear-gradient(
        180deg,
        transparent 0%,
        rgba(0, 201, 167, 0.2) 50%,
        transparent 100%
      );
    }
  }
}

// 内容区域样式
.content-section {
  background: white;
  border-radius: 0 0 20rpx 20rpx;
  overflow: hidden;
  min-height: 400rpx;

  .tab-content {
    animation: fadeInUp 0.5s ease-out;

    &.info-content {
      .video-info-content {
        padding: 0;
      }
    }

    &.comments-content {
      .comments-wrapper {
        background: transparent;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
        border: none;
      }
    }

    &.related-content {
      .recommended-section {
        margin: 20rpx;

        .section-title {
          margin-bottom: 24rpx;
          padding-left: 12rpx;
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  background: transparent;
  padding: 0 20rpx;
  max-width: 750rpx;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

// 视频播放器区域
.video-player-section {
  background: #000;
  position: relative;
  margin: 20rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  .video-wrapper {
    position: relative;
    width: 100%;
    height: 450rpx;

    .video-player {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 16rpx;
    }

    .playing-indicator {
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background: rgba(0, 201, 167, 0.9);
      border-radius: 20rpx;
      backdrop-filter: blur(10rpx);
      animation: bounceIn 0.5s ease-out;

      .playing-dot {
        width: 12rpx;
        height: 12rpx;
        background: white;
        border-radius: 50%;
        margin-right: 8rpx;
        animation: pulse 1.5s infinite;
      }

      .playing-text {
        font-size: 22rpx;
        color: white;
        font-weight: 500;
      }
    }

    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32rpx;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 16rpx;
      backdrop-filter: blur(10rpx);
      animation: fadeIn 0.3s ease-out;

      .loading-spinner {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 16rpx;
        animation: spin 1s linear infinite;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        border-top: 4rpx solid #00c9a7;
        border-radius: 50%;
      }

      .loading-text {
        font-size: 24rpx;
        color: white;
        font-weight: 500;
      }
    }

    .error-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32rpx;
      background: rgba(255, 0, 0, 0.9);
      border-radius: 16rpx;
      animation: shake 0.5s ease-out;

      .error-icon {
        font-size: 48rpx;
        color: white;
        margin-bottom: 16rpx;
        animation: bounce 1s infinite;
      }

      .error-text {
        font-size: 24rpx;
        color: white;
        font-weight: 500;
      }
    }
  }
}

// 回复提示
.reply-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 30rpx;
  background: #f0fdf9;
  border-bottom: 1rpx solid #e0f2fe;

  .reply-text {
    font-size: 26rpx;
    color: #00c9a7;
  }

  .cancel-reply-btn {
    padding: 8rpx 16rpx;
    background: transparent;
    border: none;

    .cancel-icon {
      font-size: 24rpx;
      color: #64748b;
    }
  }
}

// 加载更多评论
.load-more-comments {
  display: flex;
  justify-content: center;
  padding: 32rpx;

  .load-more-btn {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx 32rpx;
    background: #f8fafc;
    border: 1rpx solid #e2e8f0;
    border-radius: 24rpx;
    font-size: 26rpx;
    color: #64748b;
    transition: all 0.3s ease;

    &:not(:disabled):hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
    }

    &:disabled {
      opacity: 0.6;
    }

    .loading-spinner {
      width: 24rpx;
      height: 24rpx;
      border: 2rpx solid #e2e8f0;
      border-top: 2rpx solid #00c9a7;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

// 视频信息包装器
.video-info-wrapper {
  background: white;
  margin: 20rpx 0;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.1);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8rpx 30rpx rgba(0, 201, 167, 0.15);
    transform: translateY(-2rpx);
  }
}

// 视频基本信息
.video-basic-info {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f8f6;
  animation: fadeInLeft 0.8s ease-out 0.2s both;

  .video-title {
    font-size: 34rpx;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.4;
    margin-bottom: 16rpx;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -8rpx;
      left: 0;
      width: 0;
      height: 3rpx;
      background: linear-gradient(90deg, #00c9a7, #22d3ee);
      border-radius: 2rpx;
      animation: expandWidth 1s ease-out 0.5s both;
    }
  }

  .video-meta {
    display: flex;
    align-items: center;
    gap: 12rpx;
    opacity: 0;
    animation: fadeIn 0.6s ease-out 0.4s both;

    .meta-item {
      font-size: 26rpx;
      color: #666;
      transition: color 0.3s ease;

      &:hover {
        color: #00c9a7;
      }
    }

    .meta-separator {
      font-size: 24rpx;
      color: rgba(0, 201, 167, 0.3);
    }
  }
}

// 去除uni-button的边框
uni-button:after {
  border: none !important;
}

// 互动按钮区域
.interaction-buttons {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f8f6;
  gap: 30rpx;
  justify-content: center;
  animation: fadeInUp 0.8s ease-out 0.3s both;

  .interaction-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10rpx;
    background: transparent;
    border: none !important;
    padding: 16rpx;
    border-radius: 16rpx;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 88rpx;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(0, 201, 167, 0.1);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:active {
      transform: scale(0.95);

      &::before {
        width: 200%;
        height: 200%;
      }
    }

    &.active {
      .btn-icon {
        color: #00c9a7;
        animation: heartBeat 0.6s ease-out;
      }
      .btn-count {
        color: #00c9a7;
        font-weight: 600;
      }
    }

    &.dislike-btn {
      .btn-icon {
        color: #999;
      }
      .btn-text {
        color: #999;
        font-size: 22rpx;
      }

      &:hover {
        .btn-icon {
          color: #ff6b6b;
        }
        .btn-text {
          color: #ff6b6b;
        }
      }
    }

    &.coin-btn {
      .coin-icon {
        color: #ffa500;
        font-size: 36rpx;
      }
      .btn-count {
        color: #ffa500;
        font-weight: 600;
      }

      &:hover {
        .coin-icon {
          animation: spin 0.5s ease-out;
        }
      }
    }

    .btn-icon {
      font-size: 38rpx;
      color: #888;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
    }

    .btn-count,
    .btn-text {
      font-size: 24rpx;
      color: #888;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 1;
      font-weight: 500;
    }
  }
}

// 作者信息区域
.author-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f8f6;
  animation: fadeInRight 0.8s ease-out 0.4s both;

  .author-info {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .author-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      object-fit: cover;
      border: 3rpx solid rgba(0, 201, 167, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      animation: rotateIn 0.6s ease-out 0.6s both;

      &:hover {
        border-color: #00c9a7;
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);
      }
    }

    .author-details {
      flex: 1;

      .author-name {
        font-size: 30rpx;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 6rpx;
        transition: color 0.3s ease;

        &:hover {
          color: #00c9a7;
        }
      }

      .author-stats {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .stats-text {
          font-size: 24rpx;
          color: #888;
          transition: color 0.3s ease;

          &:hover {
            color: #00c9a7;
          }
        }

        .stats-separator {
          font-size: 22rpx;
          color: rgba(0, 201, 167, 0.3);
        }
      }
    }

    .follow-button {
      padding: 14rpx 28rpx;
      background: linear-gradient(135deg, #00c9a7 0%, #22d3ee 100%);
      color: white;
      font-size: 26rpx;
      font-weight: 600;
      border: none;
      border-radius: 25rpx;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4rpx 15rpx rgba(0, 201, 167, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
      }

      &:active {
        transform: scale(0.95);

        &::before {
          left: 100%;
        }
      }

      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 8rpx 25rpx rgba(0, 201, 167, 0.4);
      }

      &.followed {
        background: #f8f9fa;
        color: #888;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        &:hover {
          background: #e9ecef;
        }
      }
    }
  }
}

// 课程介绍区域
.course-description {
  padding: 30rpx;
  animation: fadeInLeft 0.8s ease-out 0.5s both;

  .description-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .description-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #1a1a1a;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -4rpx;
        left: 0;
        width: 0;
        height: 2rpx;
        background: #00c9a7;
        border-radius: 1rpx;
        animation: expandWidth 0.8s ease-out 0.7s both;
      }
    }

    .difficulty-badge {
      padding: 8rpx 16rpx;
      font-size: 22rpx;
      font-weight: 500;
      border-radius: 16rpx;
      animation: bounceIn 0.6s ease-out 0.8s both;

      &.difficulty-beginner {
        background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
        color: #16a34a;
        box-shadow: 0 2rpx 8rpx rgba(22, 163, 74, 0.2);
      }

      &.difficulty-intermediate {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #d97706;
        box-shadow: 0 2rpx 8rpx rgba(217, 119, 6, 0.2);
      }

      &.difficulty-advanced {
        background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
        color: #dc2626;
        box-shadow: 0 2rpx 8rpx rgba(220, 38, 38, 0.2);
      }
    }
  }

  .description-content {
    font-size: 28rpx;
    line-height: 1.7;
    color: #555;
    margin-bottom: 24rpx;
    animation: typeWriter 2s ease-out 0.8s both;
  }

  .course-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .course-tag {
      padding: 10rpx 20rpx;
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(34, 211, 238, 0.1) 100%);
      color: #00c9a7;
      font-size: 24rpx;
      font-weight: 500;
      border-radius: 20rpx;
      border: 1rpx solid rgba(0, 201, 167, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      animation: slideInUp 0.5s ease-out calc(0.9s + var(--delay, 0) * 0.1s) both;

      &:hover {
        background: linear-gradient(
          135deg,
          rgba(0, 201, 167, 0.2) 0%,
          rgba(34, 211, 238, 0.2) 100%
        );
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.2);
      }

      &:nth-child(1) {
        --delay: 0;
      }
      &:nth-child(2) {
        --delay: 1;
      }
      &:nth-child(3) {
        --delay: 2;
      }
    }
  }
}

// 标签加载状态样式
.tab-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  animation: fadeIn 0.3s ease-out;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(0, 201, 167, 0.2);
    border-top: 4rpx solid #00c9a7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 24rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    animation: pulse 2s infinite;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  animation: fadeInUp 0.6s ease-out;

  // 在网格中居中对齐
  &.empty-state-centered {
    grid-column: 1 / -1; // 跨越所有列
  }

  .empty-icon {
    font-size: 120rpx;
    color: rgba(0, 201, 167, 0.3);
    margin-bottom: 24rpx;
    animation: float 3s ease-in-out infinite;
  }

  .no-comments-text,
  .no-related-text {
    font-size: 32rpx;
    color: #999;
    font-weight: 500;
    margin-bottom: 12rpx;
    opacity: 0;
    animation: fadeIn 0.8s ease-out 0.3s both;
  }

  .empty-tip {
    font-size: 26rpx;
    color: #bbb;
    line-height: 1.4;
    opacity: 0;
    animation: fadeIn 0.8s ease-out 0.6s both;
    position: relative;

    &::before {
      content: '✨';
      margin-right: 8rpx;
    }

    &::after {
      content: '✨';
      margin-left: 8rpx;
    }
  }

  // 悬停效果
  &:hover {
    .empty-icon {
      color: rgba(0, 201, 167, 0.5);
      transform: scale(1.1);
    }

    .no-comments-text,
    .no-related-text {
      color: #666;
    }

    .empty-tip {
      color: #999;
    }
  }
}

// 评论区域
.comments-wrapper {
  background: white;
  margin: 20rpx 0;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.1);
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  animation: slideInUp 0.8s ease-out 0.6s both;
}

.comments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f8f6;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.02) 0%, rgba(34, 211, 238, 0.02) 100%);

  .comments-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #1a1a1a;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -4rpx;
      left: 0;
      width: 0;
      height: 2rpx;
      background: #00c9a7;
      border-radius: 1rpx;
      animation: expandWidth 0.8s ease-out 0.8s both;
    }
  }

  .comments-sort {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 8rpx 16rpx;
    background: rgba(0, 201, 167, 0.1);
    border-radius: 20rpx;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 201, 167, 0.15);
      transform: scale(1.05);
    }

    .sort-text {
      font-size: 24rpx;
      color: #00c9a7;
      font-weight: 500;
    }

    .sort-icon {
      font-size: 20rpx;
      color: #00c9a7;
      transition: transform 0.3s ease;
    }

    &:hover .sort-icon {
      transform: rotate(180deg);
    }
  }
}

.comment-input-area {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f8f6;
  background: rgba(0, 201, 167, 0.02);
  animation: fadeIn 0.6s ease-out 0.7s both;

  .comment-user-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    border: 2rpx solid rgba(0, 201, 167, 0.3);
    animation: bounceIn 0.6s ease-out 0.8s both;
  }

  .input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16rpx;

    .comment-input {
      flex: 1;
      padding: 20rpx 24rpx;
      background: white;
      border: 2rpx solid rgba(0, 201, 167, 0.2);
      border-radius: 30rpx;
      font-size: 28rpx;
      color: #333;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.1);

      &::placeholder {
        color: #bbb;
      }

      &:focus {
        border-color: #00c9a7;
        background: white;
        box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.2);
        transform: translateY(-1rpx);
      }
    }

    .send-btn {
      width: 72rpx;
      height: 72rpx;
      background: linear-gradient(135deg, #00c9a7 0%, #22d3ee 100%);
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4rpx 15rpx rgba(0, 201, 167, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
      }

      &:active {
        transform: scale(0.9);

        &::before {
          width: 200%;
          height: 200%;
        }
      }

      &:hover {
        transform: translateY(-2rpx) scale(1.05);
        box-shadow: 0 8rpx 25rpx rgba(0, 201, 167, 0.4);
      }

      &:disabled {
        opacity: 0.6;
      }

      .send-icon {
        font-size: 32rpx;
        color: white;
        z-index: 1;
      }
    }
  }
}

.comments-list {
  .comment-item {
    display: flex;
    gap: 20rpx;
    padding: 25rpx 30rpx;
    border-bottom: 1rpx solid rgba(0, 201, 167, 0.05);
    transition: all 0.3s ease;
    animation: slideInLeft 0.6s ease-out calc(0.8s + var(--index, 0) * 0.1s) both;

    &:hover {
      background: rgba(0, 201, 167, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .commenter-avatar {
      width: 68rpx;
      height: 68rpx;
      border-radius: 50%;
      object-fit: cover;
      flex-shrink: 0;
      border: 2rpx solid rgba(0, 201, 167, 0.2);
      transition: all 0.3s ease;

      &:hover {
        border-color: #00c9a7;
        transform: scale(1.1);
      }
    }

    .comment-main {
      flex: 1;

      .commenter-info {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: 10rpx;

        .commenter-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #1a1a1a;
          transition: color 0.3s ease;

          &:hover {
            color: #00c9a7;
          }
        }

        .comment-time {
          font-size: 22rpx;
          color: #999;
        }
      }

      .comment-content {
        font-size: 28rpx;
        line-height: 1.6;
        color: #444;
        margin-bottom: 16rpx;
      }

      .comment-actions {
        display: flex;
        align-items: center;
        gap: 24rpx;

        .comment-action-btn {
          display: flex;
          align-items: center;
          gap: 8rpx;
          background: transparent;
          border: none;
          padding: 10rpx 16rpx;
          border-radius: 20rpx;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(0, 201, 167, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
          }

          &:active {
            transform: scale(0.95);

            &::before {
              width: 200%;
              height: 200%;
            }
          }

          &.like-btn.liked {
            .action-icon {
              color: #00c9a7;
              animation: heartBeat 0.5s ease-out;
            }
            .action-count {
              color: #00c9a7;
              font-weight: 600;
            }
          }

          .action-icon {
            font-size: 26rpx;
            color: #888;
            transition: all 0.3s ease;
            z-index: 1;
          }

          .action-count,
          .action-text {
            font-size: 24rpx;
            color: #888;
            transition: all 0.3s ease;
            z-index: 1;
          }

          &:hover {
            .action-icon,
            .action-count,
            .action-text {
              color: #00c9a7;
            }
          }
        }
      }

      .replies-section {
        margin-top: 20rpx;
        padding-left: 30rpx;
        border-left: 3rpx solid rgba(0, 201, 167, 0.2);
        animation: fadeInLeft 0.5s ease-out 0.3s both;

        .reply-item {
          display: flex;
          gap: 16rpx;
          padding: 16rpx 0;

          .reply-avatar {
            width: 52rpx;
            height: 52rpx;
            border-radius: 50%;
            object-fit: cover;
            flex-shrink: 0;
            border: 1rpx solid rgba(0, 201, 167, 0.2);
          }

          .reply-content {
            flex: 1;

            .reply-author {
              font-size: 24rpx;
              font-weight: 500;
              color: #1a1a1a;
              margin-bottom: 6rpx;
            }

            .reply-text {
              font-size: 26rpx;
              line-height: 1.5;
              color: #444;
              margin-bottom: 12rpx;
            }

            .reply-actions {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .reply-time {
                font-size: 20rpx;
                color: #999;
              }

              .reply-like-btn {
                display: flex;
                align-items: center;
                gap: 6rpx;
                background: transparent;
                border: none;
                padding: 6rpx 12rpx;
                border-radius: 16rpx;
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(0, 201, 167, 0.1);

                  .reply-like-icon,
                  .reply-like-count {
                    color: #00c9a7;
                  }
                }

                .reply-like-icon {
                  font-size: 20rpx;
                  color: #888;
                  transition: color 0.3s ease;
                }

                .reply-like-count {
                  font-size: 18rpx;
                  color: #888;
                  transition: color 0.3s ease;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 相关推荐区域
.recommended-section {
  margin: 20rpx 0 40rpx;
  animation: slideInUp 0.8s ease-out 0.7s both;

  .section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 24rpx;
    padding-left: 12rpx;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4rpx;
      height: 32rpx;
      background: linear-gradient(135deg, #00c9a7 0%, #22d3ee 100%);
      border-radius: 2rpx;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -8rpx;
      left: 12rpx;
      width: 0;
      height: 2rpx;
      background: #00c9a7;
      border-radius: 1rpx;
      animation: expandWidth 1s ease-out 0.9s both;
    }
  }

  .recommended-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .recommended-card {
      background: white;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1rpx solid rgba(0, 201, 167, 0.1);
      animation: scaleIn 0.6s ease-out calc(0.8s + var(--index, 0) * 0.2s) both;

      &:hover {
        transform: translateY(-8rpx) scale(1.02);
        box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.2);
        border-color: rgba(0, 201, 167, 0.3);
      }

      .card-thumbnail {
        width: 100%;
        height: 240rpx;
        object-fit: cover;
        position: relative;
        transition: transform 0.4s ease;
      }

      &:hover .card-thumbnail {
        transform: scale(1.05);
      }

      .card-overlay {
        position: absolute;
        bottom: 8rpx;
        right: 8rpx;
        background: rgba(0, 0, 0, 0.8);
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
        backdrop-filter: blur(10rpx);

        .card-duration {
          font-size: 20rpx;
          color: white;
          font-weight: 500;
        }
      }

      .card-info {
        padding: 20rpx;

        .card-title {
          font-size: 26rpx;
          font-weight: 600;
          color: #1a1a1a;
          line-height: 1.4;
          margin-bottom: 12rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          transition: color 0.3s ease;
        }

        .card-author {
          font-size: 22rpx;
          color: #888;
          margin-bottom: 16rpx;
          transition: color 0.3s ease;
        }

        .card-stats {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .card-views {
            font-size: 20rpx;
            color: #999;
          }

          .card-rating {
            display: flex;
            align-items: center;
            gap: 4rpx;

            .rating-icon {
              font-size: 16rpx;
              color: #ffa500;
            }

            .rating-text {
              font-size: 20rpx;
              color: #888;
            }
          }
        }
      }

      &:hover {
        .card-title {
          color: #00c9a7;
        }

        .card-author {
          color: #00c9a7;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.5);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes heartBeat {
  0%,
  100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(1.15);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5rpx);
  }
  75% {
    transform: translateX(5rpx);
  }
}

@keyframes expandWidth {
  from {
    width: 0;
  }
  to {
    width: 100rpx;
  }
}

@keyframes typeWriter {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 100%;
    opacity: 1;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

// 针对评论项添加延迟动画
.comments-list .comment-item:nth-child(1) {
  --index: 0;
}
.comments-list .comment-item:nth-child(2) {
  --index: 1;
}
.comments-list .comment-item:nth-child(3) {
  --index: 2;
}
.comments-list .comment-item:nth-child(4) {
  --index: 3;
}

// 针对推荐卡片添加延迟动画
.recommended-grid .recommended-card:nth-child(1) {
  --index: 0;
}
.recommended-grid .recommended-card:nth-child(2) {
  --index: 1;
}
.recommended-grid .recommended-card:nth-child(3) {
  --index: 2;
}
.recommended-grid .recommended-card:nth-child(4) {
  --index: 3;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .main-content {
    padding: 0 16rpx;
  }

  .recommended-grid {
    grid-template-columns: 1fr;
  }

  .interaction-buttons {
    gap: 20rpx;

    .interaction-btn {
      min-width: 70rpx;

      .btn-icon {
        font-size: 32rpx;
      }

      .btn-count,
      .btn-text {
        font-size: 20rpx;
      }
    }
  }

  .video-basic-info,
  .author-section,
  .course-description,
  .comments-header,
  .comment-input-area {
    padding: 24rpx;
  }

  .comments-list .comment-item {
    padding: 20rpx 24rpx;
  }

  // 加载状态响应式适配
  .tab-loading {
    padding: 60rpx 20rpx;

    .loading-spinner {
      width: 50rpx;
      height: 50rpx;
      border: 3rpx solid rgba(0, 201, 167, 0.2);
      border-top: 3rpx solid #00c9a7;
    }

    .loading-text {
      font-size: 26rpx;
    }
  }

  // 空状态响应式适配
  .empty-state {
    padding: 60rpx 20rpx;

    .empty-icon {
      font-size: 100rpx;
    }

    .no-comments-text,
    .no-related-text {
      font-size: 28rpx;
    }

    .empty-tip {
      font-size: 24rpx;
      padding: 0 20rpx;
    }
  }
}
</style>
