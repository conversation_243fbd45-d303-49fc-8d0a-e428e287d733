<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

/**
 * @description 智能建议组件
 * 在面试过程中提供实时的智能建议和反馈
 */
const props = defineProps({
  // 是否处于录制状态
  isRecording: {
    type: Boolean,
    default: false
  },
  // 是否自动生成建议
  autoGenerate: {
    type: Boolean,
    default: true
  },
  // 建议生成间隔(毫秒)
  interval: {
    type: Number,
    default: 10000
  }
})

// 组件事件
const emit = defineEmits(['dismiss', 'dismissAll', 'action'])

// 建议数据
const suggestions = ref<Array<{
  id: number | string,
  type: string,
  level: string,
  icon: string,
  title: string,
  message: string,
  action?: string,
  isRemoving?: boolean,
  progress?: number
}>>([])

// 定时器
let suggestionTimer: ReturnType<typeof setInterval> | null = null
// 存储每个建议的定时器
const suggestionTimers = new Map<number | string, ReturnType<typeof setTimeout>>()
// 存储每个建议的进度定时器
const progressTimers = new Map<number | string, ReturnType<typeof setInterval>>()

/**
 * @description 关闭单个建议
 * @param id 建议ID
 */
const dismissSuggestion = (id: number | string) => {
  const index = suggestions.value.findIndex((s: any) => s.id === id)
  if (index > -1) {
    // 清除对应的定时器
    const timer = suggestionTimers.get(id)
    if (timer) {
      clearTimeout(timer)
      suggestionTimers.delete(id)
    }

    // 清除进度定时器
    const progressTimer = progressTimers.get(id)
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimers.delete(id)
    }

    // 标记为正在移除，触发淡出动画
    suggestions.value[index].isRemoving = true

    // 等待动画完成后移除
    setTimeout(() => {
      const currentIndex = suggestions.value.findIndex((s: any) => s.id === id)
      if (currentIndex > -1) {
        suggestions.value.splice(currentIndex, 1)
        emit('dismiss', id)
      }
    }, 300) // 300ms 动画时间
  }
}

/**
 * @description 关闭所有建议
 */
const dismissAllSuggestions = () => {
  // 清除所有定时器
  suggestionTimers.forEach((timer) => clearTimeout(timer))
  suggestionTimers.clear()

  // 清除所有进度定时器
  progressTimers.forEach((timer) => clearInterval(timer))
  progressTimers.clear()

  // 标记所有建议为正在移除
  suggestions.value.forEach((suggestion: any) => {
    suggestion.isRemoving = true
  })

  // 等待动画完成后清空
  setTimeout(() => {
    suggestions.value = []
    emit('dismissAll')
  }, 300)
}

/**
 * @description 执行建议动作
 * @param suggestion 建议对象
 */
const handleAction = (suggestion: any) => {
  emit('action', suggestion)
}

/**
 * @description 生成智能建议(已移除模拟逻辑，现在通过WebSocket接收真实数据)
 */
const generateSuggestion = () => {
  // 此方法已废弃，建议现在通过WebSocket从服务端接收
  console.log('智能建议现在通过WebSocket接收，不再使用模拟数据')
}

/**
 * @description 添加建议
 * @param suggestion 建议对象
 */
const addSuggestion = (suggestion: any) => {
  if (!suggestion.id) {
    suggestion.id = Date.now() + Math.random()
  }

  // 初始化状态
  suggestion.isRemoving = false
  suggestion.progress = 100 // 初始进度为100%
  suggestions.value.push(suggestion)

  // 进度条倒计时
  const progressTimer = setInterval(() => {
    const index = suggestions.value.findIndex((s: any) => s.id === suggestion.id)
    if (index > -1 && !suggestions.value[index].isRemoving) {
      suggestions.value[index].progress -= 100 / 30 // 3秒内从100%到0%，每100ms减少约3.33%
      if (suggestions.value[index].progress <= 0) {
        suggestions.value[index].progress = 0
      }
    }
  }, 100)

  // 3秒后自动移除建议
  const timer = setTimeout(() => {
    dismissSuggestion(suggestion.id)
  }, 3000)

  // 保存定时器引用
  suggestionTimers.set(suggestion.id, timer)
  progressTimers.set(suggestion.id, progressTimer)
}

// 暴露方法给父组件
defineExpose({
  addSuggestion,
  dismissSuggestion,
  dismissAllSuggestions,
  generateSuggestion,
  suggestions
})

// 生命周期钩子
onMounted(() => {
  console.log('SmartSuggestion组件已挂载，等待WebSocket数据')
})

onUnmounted(() => {
  // 清理定时器
  if (suggestionTimer) {
    clearInterval(suggestionTimer)
    suggestionTimer = null
  }

  // 清理所有建议的定时器
  suggestionTimers.forEach((timer) => clearTimeout(timer))
  suggestionTimers.clear()

  // 清理所有进度定时器
  progressTimers.forEach((timer) => clearInterval(timer))
  progressTimers.clear()
})

// 监听录制状态变化
watch(() => props.isRecording, (newValue) => {
  console.log('录制状态变化:', newValue ? '开始录制' : '停止录制')
})
</script>

<template>
  <div class="smart-suggestions" v-if="suggestions.length > 0">
    <div class="suggestions-header">
      <i class="fa fa-lightbulb suggestions-icon"></i>
      <span class="suggestions-title">智能建议</span>
      <button class="close-all-btn" @click="dismissAllSuggestions">
        <i class="fa fa-times"></i>
      </button>
    </div>
    <div class="suggestions-list">
      <div class="suggestion-card" v-for="suggestion in suggestions" :key="suggestion.id" :class="[
        `level-${suggestion.level}`,
        `type-${suggestion.type}`,
        { 'removing': suggestion.isRemoving }
      ]">
        <div class="suggestion-content">
          <div class="suggestion-header">
            <i :class="suggestion.icon" class="suggestion-type-icon"></i>
            <span class="suggestion-title-text">{{ suggestion.title }}</span>
            <button class="dismiss-btn" @click="dismissSuggestion(suggestion.id)">
              <i class="fa fa-times"></i>
            </button>
          </div>
          <span class="suggestion-message">{{ suggestion.message }}</span>
          <div class="suggestion-action" v-if="suggestion.action" @click="handleAction(suggestion)">
            <i class="fa fa-arrow-right action-icon"></i>
            <span class="action-text">{{ suggestion.action }}</span>
          </div>
          <!-- 进度条 -->
          <div class="suggestion-progress" v-if="suggestion.progress !== undefined">
            <div class="progress-bar" :style="{ width: suggestion.progress + '%' }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.smart-suggestions {
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.suggestions-header {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 10px 12px;
  background: #f8fbfd;
  border-bottom: 1px solid #f0f0f0;
}

.suggestions-icon {
  font-size: 14px;
  color: #faad14;
}

.suggestions-title {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.close-all-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 10px;
  color: #999;
  cursor: pointer;
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-all-btn:hover {
  color: #666;
  background: #e6e6e6;
}

.suggestions-list {
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-card {
  padding: 10px 12px;
  border-bottom: 1px solid #f8f8f8;
  opacity: 1;
  transition: opacity 0.3s ease, transform 0.3s ease;
  transform: translateX(0);
}

.suggestion-card:last-child {
  border-bottom: none;
}

.suggestion-card.removing {
  opacity: 0;
  transform: translateX(100%);
}

.suggestion-card.level-high {
  border-left: 3px solid #ff4d4f;
}

.suggestion-card.level-medium {
  border-left: 3px solid #faad14;
}

.suggestion-card.level-low {
  border-left: 3px solid #52c41a;
}

.suggestion-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.suggestion-header {
  display: flex;
  gap: 4px;
  align-items: center;
}

.suggestion-type-icon {
  font-size: 12px;
  color: #666;
}

.suggestion-title-text {
  flex: 1;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.dismiss-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  font-size: 10px;
  color: #999;
  cursor: pointer;
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dismiss-btn:hover {
  color: #666;
  background: #e6e6e6;
}

.suggestion-message {
  font-size: 12px;
  line-height: 1.5;
  color: #666;
}

.suggestion-action {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #00c9a7;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-action:hover {
  color: #00a085;
}

.action-icon {
  font-size: 10px;
}

.action-text {
  font-weight: 500;
}

.suggestion-progress {
  width: 100%;
  height: 2px;
  margin-top: 6px;
  overflow: hidden;
  background-color: #f0f0f0;
  border-radius: 1px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #faad14 50%, #ff4d4f 100%);
  border-radius: 1px;
  transition: width 0.1s linear;
}
</style>