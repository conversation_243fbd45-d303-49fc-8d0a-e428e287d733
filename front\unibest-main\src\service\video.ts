/**
 * 视频服务模块
 * <AUTHOR>
 * @description 管理所有视频相关的API接口
 */
import { http, httpPost, httpGet, httpPut } from '@/utils/http'
import type {
  Video,
  VideoQueryParams,
  VideoLearningStats,
  VideoComment,
  CommentQueryParams,
  VideoPlayRecord,
  VideoPurchaseStatus,
  VideoListResponse,
  RelatedVideo,
  CommentListResponse,
} from '@/types/video'

/**
 * 获取视频列表
 * @param params 查询参数
 * @returns 视频列表结果
 */
export const getVideoList = (params: VideoQueryParams) => {
  return httpPost<VideoListResponse>('/app/video/list', {
    ...params,
  })
}

/**
 * 获取视频详情
 * @param videoId 视频ID
 * @returns 视频详情
 */
export const getVideoDetail = (videoId: number) => {
  return httpGet<Video>(`/app/video/${videoId}`)
}

/**
 * 获取学习统计数据
 * @returns 学习统计数据
 */
export const getVideoLearningStats = () => {
  return httpGet<VideoLearningStats>('/app/video/learning-stats')
}

/**
 * 获取收藏的视频
 * @param params 查询参数
 * @returns 收藏视频列表
 */
export const getBookmarkedVideos = (params: VideoQueryParams) => {
  return httpGet<VideoListResponse>('/app/video/bookmarked', { params })
}

/**
 * 获取已购买的视频
 * @param params 查询参数
 * @returns 已购买视频列表
 */
export const getPurchasedVideos = (params: VideoQueryParams) => {
  return httpGet<VideoListResponse>('/app/video/purchased', { params })
}

/**
 * 获取学习历史
 * @param params 查询参数
 * @returns 学习历史列表
 */
export const getLearningHistory = (params: VideoQueryParams) => {
  return httpGet<VideoListResponse>('/app/video/history', { params })
}

/**
 * 获取相关推荐视频
 * @param videoId 视频ID
 * @param limit 限制数量
 * @returns 相关推荐视频列表
 */
export const getRelatedVideos = (videoId: number, limit: number = 10) => {
  return httpGet<RelatedVideo[]>(`/app/video/related/${videoId}`, { params: { limit } })
}

/**
 * 切换视频点赞状态
 * @param videoId 视频ID
 * @param isLike 是否点赞
 * @returns 操作结果
 */
export const toggleVideoLike = (videoId: number, isLike: boolean) => {
  return httpPost<void>('/app/video/like', { videoId, isLike })
}

/**
 * 切换视频收藏状态
 * @param videoId 视频ID
 * @param isCollect 是否收藏
 * @returns 操作结果
 */
export const toggleVideoCollect = (videoId: number, isCollect: boolean) => {
  return httpPost<void>('/app/video/collect', { videoId, isCollect })
}

/**
 * 分享视频
 * @param videoId 视频ID
 * @param platform 分享平台
 * @returns 操作结果
 */
export const shareVideo = (videoId: number, platform: string) => {
  return httpPost<void>('/app/video/share', { videoId, platform })
}

/**
 * 增加视频播放次数
 * @param videoId 视频ID
 * @returns 操作结果
 */
export const incrementVideoView = (videoId: number) => {
  return httpPost<void>(`/app/video/${videoId}/view`)
}

/**
 * 更新视频播放进度
 * @param videoId 视频ID
 * @param currentTime 当前播放时间
 * @param duration 视频总时长
 * @returns 操作结果
 */
export const updateVideoProgress = (videoId: number, currentTime: number, duration: number) => {
  return httpPost<void>('/app/video/progress', { videoId, currentTime, duration })
}

/**
 * 获取视频播放记录
 * @param videoId 视频ID
 * @returns 播放记录
 */
export const getVideoPlayRecord = (videoId: number) => {
  return httpGet<VideoPlayRecord>(`/app/video/play-record/${videoId}`)
}

/**
 * 保存视频播放记录
 * @param videoId 视频ID
 * @returns 操作结果
 */
export const saveVideoPlayRecord = (videoId: number) => {
  return httpPost<void>(`/app/video/${videoId}/save-play-record`)
}
/**
 * 检查视频购买状态
 * @param videoId 视频ID
 * @returns 购买状态
 */
export const checkVideoPurchaseStatus = (videoId: number) => {
  return httpGet<VideoPurchaseStatus>(`/app/video/purchase-status/${videoId}`)
}

/**
 * 关注/取关讲师
 * @param instructorId 讲师ID
 * @param isFollow 是否关注
 * @returns 操作结果
 */
export const toggleInstructorFollow = (instructorId: number, isFollow: boolean) => {
  return httpPost<void>('/app/video/instructor-follow', { instructorId, isFollow })
}

/**
 * 获取视频评论列表
 * @param params 查询参数
 * @returns 评论列表
 */
export const getVideoComments = (params: CommentQueryParams) => {
  return httpGet<CommentListResponse>('/app/video/comments', { params })
}

/**
 * 发布视频评论
 * @param videoId 视频ID
 * @param content 评论内容
 * @param parentId 父评论ID（可选）
 * @returns 评论详情
 */
export const publishVideoComment = (videoId: number, content: string, parentId?: number) => {
  return httpPost<VideoComment>('/app/video/comment', { videoId, content, parentId })
}

/**
 * 切换评论点赞状态
 * @param commentId 评论ID
 * @param isLike 是否点赞
 * @returns 操作结果
 */
export const toggleCommentLike = (commentId: number, isLike: boolean) => {
  return httpPost<void>('/app/video/comment-like', { commentId, isLike })
}

/**
 * 保存视频播放记录
 * @param videoId 视频ID
 * @returns 操作结果
 */
export const saveVideoHistory = (videoId: number) => {
  return httpPost<void>(`/app/video/${videoId}/history`)
}
