{"name": "load-bmfont", "version": "1.4.2", "description": "loads a BMFont file in Node and the browser", "main": "index.js", "browser": "browser.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {"buffer-equal": "0.0.1", "mime": "^1.3.4", "parse-bmfont-ascii": "^1.0.3", "parse-bmfont-binary": "^1.0.5", "parse-bmfont-xml": "^1.1.4", "phin": "^3.7.1", "xhr": "^2.0.1", "xtend": "^4.0.0"}, "devDependencies": {"browserify": "^9.0.3", "tap-spec": "^2.2.2", "tape": "^3.5.0", "testling": "^1.7.1"}, "scripts": {"test-node": "(node test.js; node test-server.js) | tap-spec", "test-browser": "browserify test.js | testling | tap-spec", "test": "npm run test-node && npm run test-browser"}, "keywords": ["bmfont", "bitmap", "font", "angel", "code", "angelcode", "parse", "ascii", "xml", "text", "json"], "repository": {"type": "git", "url": "git://github.com/Jam3/load-bmfont.git"}, "homepage": "https://github.com/Jam3/load-bmfont", "bugs": {"url": "https://github.com/Jam3/load-bmfont/issues"}}