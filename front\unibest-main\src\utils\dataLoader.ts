/**
 * Enhanced Data Loader
 * Provides parallel API loading, request batching, and intelligent retry logic
 */

import { performanceMonitor } from './performanceMonitor'
import { errorHandler } from './errorHandler'
import { smartCache } from './smartCache'
import { requestDeduplicator } from './requestDeduplicator'

// Data loading interfaces
export interface LoadOptions {
  priority: 'high' | 'medium' | 'low'
  useCache: boolean
  timeout: number
  retries: number
  cacheTTL?: number
}

export interface CriticalData {
  user: {
    name: string
    avatar?: string
  }
  welcomeMessage: string
  aiMotivation: string
  stats: {
    totalInterviews: number
    averageScore: number
    improvementRate: number
    targetPosition: string
  }
}

export interface SecondaryData {
  abilities: any
  smartTasks: any[]
  recentInterviews: any[]
  progressData: any
}

export interface DashboardData {
  critical: CriticalData
  secondary: SecondaryData
}

// Default loading options
const DEFAULT_OPTIONS: LoadOptions = {
  priority: 'medium',
  useCache: true,
  timeout: 10000,
  retries: 3,
  cacheTTL: 5 * 60 * 1000, // 5 minutes
}

class DataLoader {
  private loadingStates = new Map<string, boolean>()
  private batchQueue = new Map<string, Array<{ resolve: Function; reject: Function }>>()
  private batchTimer: number | null = null
  private readonly BATCH_DELAY = 50 // ms

  /**
   * Load dashboard data with parallel execution and intelligent caching
   */
  async loadDashboardData(options: Partial<LoadOptions> = {}): Promise<DashboardData> {
    const opts = { ...DEFAULT_OPTIONS, ...options }

    performanceMonitor.startTiming('dataLoader.loadDashboardData')

    try {
      // Load critical and secondary data in parallel
      const [criticalResult, secondaryResult] = await Promise.allSettled([
        this.loadCriticalData(opts),
        this.loadSecondaryData(opts),
      ])

      const result: DashboardData = {
        critical:
          criticalResult.status === 'fulfilled'
            ? criticalResult.value
            : await this.getCachedCriticalData(),
        secondary:
          secondaryResult.status === 'fulfilled'
            ? secondaryResult.value
            : await this.getCachedSecondaryData(),
      }

      performanceMonitor.endTiming('dataLoader.loadDashboardData')
      performanceMonitor.incrementCounter('dataLoader.success')

      return result
    } catch (error) {
      performanceMonitor.endTiming('dataLoader.loadDashboardData')
      performanceMonitor.incrementCounter('dataLoader.error')
      throw error
    }
  }

  /**
   * Load critical data that should appear immediately
   */
  async loadCriticalData(options: Partial<LoadOptions> = {}): Promise<CriticalData> {
    const opts = { ...DEFAULT_OPTIONS, ...options, priority: 'high' as const }
    const cacheKey = 'dashboard.critical'

    // Check cache first for high-priority requests
    if (opts.useCache) {
      const cached = await smartCache.get<CriticalData>(cacheKey)
      if (cached) {
        performanceMonitor.incrementCounter('dataLoader.cache-hit')
        // Start background refresh for stale data
        this.backgroundRefreshCritical(opts)
        return cached
      }
    }

    performanceMonitor.startTiming('dataLoader.loadCriticalData')

    try {
      // Use request deduplication to prevent duplicate calls
      const result = await requestDeduplicator.dedupe('loadCriticalData', () =>
        this.fetchCriticalData(opts),
      )

      // Cache the result
      if (opts.useCache) {
        await smartCache.set(cacheKey, result, {
          ttl: opts.cacheTTL || 5 * 60 * 1000,
          tags: ['dashboard', 'critical'],
        })
      }

      performanceMonitor.endTiming('dataLoader.loadCriticalData')
      return result
    } catch (error) {
      performanceMonitor.endTiming('dataLoader.loadCriticalData')

      // Fallback to cached data on error
      const cached = await smartCache.get<CriticalData>(cacheKey)
      if (cached) {
        performanceMonitor.incrementCounter('dataLoader.fallback-cache')
        return cached
      }

      throw error
    }
  }

  /**
   * Load secondary data that can appear after critical content
   */
  async loadSecondaryData(options: Partial<LoadOptions> = {}): Promise<SecondaryData> {
    const opts = { ...DEFAULT_OPTIONS, ...options, priority: 'medium' as const }
    const cacheKey = 'dashboard.secondary'

    // Check cache first
    if (opts.useCache) {
      const cached = await smartCache.get<SecondaryData>(cacheKey)
      if (cached) {
        performanceMonitor.incrementCounter('dataLoader.cache-hit')
        // Start background refresh
        this.backgroundRefreshSecondary(opts)
        return cached
      }
    }

    performanceMonitor.startTiming('dataLoader.loadSecondaryData')

    try {
      const result = await requestDeduplicator.dedupe('loadSecondaryData', () =>
        this.fetchSecondaryData(opts),
      )

      // Cache the result
      if (opts.useCache) {
        await smartCache.set(cacheKey, result, {
          ttl: opts.cacheTTL || 10 * 60 * 1000,
          tags: ['dashboard', 'secondary'],
        })
      }

      performanceMonitor.endTiming('dataLoader.loadSecondaryData')
      return result
    } catch (error) {
      performanceMonitor.endTiming('dataLoader.loadSecondaryData')

      // Fallback to cached data
      const cached = await smartCache.get<SecondaryData>(cacheKey)
      if (cached) {
        performanceMonitor.incrementCounter('dataLoader.fallback-cache')
        return cached
      }

      throw error
    }
  }

  /**
   * Prefetch data for improved perceived performance
   */
  async prefetchData(keys: string[]): Promise<void> {
    performanceMonitor.startTiming('dataLoader.prefetch')

    const prefetchPromises = keys.map(async (key) => {
      try {
        switch (key) {
          case 'critical':
            return this.loadCriticalData({ priority: 'low' })
          case 'secondary':
            return this.loadSecondaryData({ priority: 'low' })
          default:
            console.warn(`Unknown prefetch key: ${key}`)
        }
      } catch (error) {
        // Ignore prefetch errors
        console.warn(`Prefetch failed for ${key}:`, error)
      }
    })

    await Promise.allSettled(prefetchPromises)
    performanceMonitor.endTiming('dataLoader.prefetch')
  }

  /**
   * Batch multiple requests together to reduce network overhead
   */
  async batchRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // Add to batch queue
      if (!this.batchQueue.has(key)) {
        this.batchQueue.set(key, [])
      }

      this.batchQueue.get(key)!.push({ resolve, reject })

      // Set timer to execute batch
      if (!this.batchTimer) {
        this.batchTimer = window.setTimeout(() => {
          this.executeBatch()
        }, this.BATCH_DELAY)
      }
    })
  }

  /**
   * Get loading state for a specific operation
   */
  isLoading(key: string): boolean {
    return this.loadingStates.get(key) || false
  }

  /**
   * Clear all caches
   */
  async clearCache(): Promise<void> {
    await smartCache.invalidateByTags(['dashboard'])
    performanceMonitor.incrementCounter('dataLoader.cache-cleared')
  }

  // Private methods

  private async fetchCriticalData(options: LoadOptions): Promise<CriticalData> {
    const { getDashboardSummary } = await import('@/service/index/dashboard')

    const result = await errorHandler.executeWithRetry(() => getDashboardSummary(), {
      maxRetries: options.retries,
      retryDelay: 1000,
      backoffFactor: 1.5,
      retryableErrors: ['NETWORK_ERROR', 'TIMEOUT_ERROR'],
    })

    if (result.code !== 200) {
      throw new Error(`API Error: ${result.message || 'Unknown error'}`)
    }

    return {
      user: {
        name: result.data.user.name,
        avatar: result.data.user.avatar,
      },
      welcomeMessage: result.data.welcomeMessage,
      aiMotivation: result.data.aiMotivation,
      stats: {
        totalInterviews: result.data.totalInterviews || 0,
        averageScore: result.data.averageScore || 0,
        improvementRate: result.data.improvementRate || 0,
        targetPosition: result.data.targetPosition || '前端工程师',
      },
    }
  }

  private async fetchSecondaryData(options: LoadOptions): Promise<SecondaryData> {
    const { getUserAbilities, getStudyStats, getSmartTasks } = await import(
      '@/service/index/dashboard'
    )

    // Execute secondary API calls in parallel
    const [abilitiesResult, statsResult, tasksResult] = await Promise.allSettled([
      errorHandler.executeWithRetry(() => getUserAbilities(), {
        maxRetries: options.retries,
        retryDelay: 1000,
        backoffFactor: 1.5,
      }),
      errorHandler.executeWithRetry(() => getStudyStats(), {
        maxRetries: options.retries,
        retryDelay: 1000,
        backoffFactor: 1.5,
      }),
      errorHandler.executeWithRetry(() => getSmartTasks({ params: { limit: 3 } }), {
        maxRetries: options.retries,
        retryDelay: 1000,
        backoffFactor: 1.5,
      }),
    ])

    return {
      abilities: abilitiesResult.status === 'fulfilled' ? abilitiesResult.value.data : null,
      smartTasks: tasksResult.status === 'fulfilled' ? tasksResult.value.data : [],
      recentInterviews: [], // Placeholder for future implementation
      progressData: statsResult.status === 'fulfilled' ? statsResult.value.data : null,
    }
  }

  private async getCachedCriticalData(): Promise<CriticalData> {
    const cached = await smartCache.get<CriticalData>('dashboard.critical')
    return (
      cached || {
        user: { name: '同学' },
        welcomeMessage: '欢迎回来！',
        aiMotivation: '今天也要加油学习哦！',
        stats: {
          totalInterviews: 0,
          averageScore: 0,
          improvementRate: 0,
          targetPosition: '前端工程师',
        },
      }
    )
  }

  private async getCachedSecondaryData(): Promise<SecondaryData> {
    const cached = await smartCache.get<SecondaryData>('dashboard.secondary')
    return (
      cached || {
        abilities: null,
        smartTasks: [],
        recentInterviews: [],
        progressData: null,
      }
    )
  }

  private async backgroundRefreshCritical(options: LoadOptions): Promise<void> {
    try {
      const fresh = await this.fetchCriticalData(options)
      await smartCache.set('dashboard.critical', fresh, {
        ttl: options.cacheTTL || 5 * 60 * 1000,
        tags: ['dashboard', 'critical'],
      })
      performanceMonitor.incrementCounter('dataLoader.background-refresh')
    } catch (error) {
      console.warn('Background refresh failed for critical data:', error)
    }
  }

  private async backgroundRefreshSecondary(options: LoadOptions): Promise<void> {
    try {
      const fresh = await this.fetchSecondaryData(options)
      await smartCache.set('dashboard.secondary', fresh, {
        ttl: options.cacheTTL || 10 * 60 * 1000,
        tags: ['dashboard', 'secondary'],
      })
      performanceMonitor.incrementCounter('dataLoader.background-refresh')
    } catch (error) {
      console.warn('Background refresh failed for secondary data:', error)
    }
  }

  private async executeBatch(): Promise<void> {
    const currentQueue = new Map(this.batchQueue)
    this.batchQueue.clear()
    this.batchTimer = null

    for (const [key, requests] of currentQueue) {
      try {
        // Execute the batched request once
        const result = await this.fetchBatchedData(key)

        // Resolve all waiting promises
        requests.forEach(({ resolve }) => resolve(result))
      } catch (error) {
        // Reject all waiting promises
        requests.forEach(({ reject }) => reject(error))
      }
    }
  }

  private async fetchBatchedData(key: string): Promise<any> {
    // Implementation for batched requests
    // This would be expanded based on specific batching needs
    switch (key) {
      case 'dashboard':
        return this.loadDashboardData()
      default:
        throw new Error(`Unknown batch key: ${key}`)
    }
  }
}

// Export singleton instance
export const dataLoader = new DataLoader()

// Export class for testing
export { DataLoader }
