/**
 * 面试模块类型定义
 */

// 基础响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 岗位难度等级
export type DifficultyLevel = 1 | 2 | 3 | 4 | 5

// 技能等级
export type SkillLevel = 'high' | 'medium' | 'low'

// 题目类型
export type QuestionType = 'technical' | 'project' | 'problem' | 'behavior' | 'product' | 'analysis' | 'design'

// 岗位基本信息
export interface JobBasicInfo {
  id: number
  categoryId: number
  name: string
  company: string
  logo?: string
  difficulty: DifficultyLevel
  duration: number
  questionCount: number
  interviewers: number
  passRate: string
  tags: string[]
}

// 面试流程步骤
export interface InterviewStep {
  step: number
  name: string
  duration: number
  description: string
}

// 技能考查点
export interface SkillPoint {
  name: string
  weight: number
  level: SkillLevel
}

// 岗位详情信息
export interface JobDetail extends JobBasicInfo {
  description: string
  requirements: string[]
  interviewProcess: InterviewStep[]
  skillPoints: SkillPoint[]
  benefits: string[]
}

// 面试题目
export interface InterviewQuestion {
  id: number
  type: QuestionType
  question: string
  difficulty: DifficultyLevel
  timeLimit: number
  tags?: string[]
}

// 获取岗位详情请求参数
export interface GetJobDetailParams {
  jobId: number
  categoryId?: number
}

// 获取岗位详情响应
export interface GetJobDetailResponse {
  jobDetail: JobDetail
  relatedJobs: JobBasicInfo[]
  sampleQuestions: InterviewQuestion[]
}

// 获取样题请求参数
export interface GetSampleQuestionsParams {
  jobId: number
  categoryId: number
  count?: number
}

// 获取样题响应
export interface GetSampleQuestionsResponse {
  questions: InterviewQuestion[]
}

// 获取相关岗位请求参数
export interface GetRelatedJobsParams {
  categoryId: number
  excludeJobId: number
  limit?: number
}

// 获取相关岗位响应
export interface GetRelatedJobsResponse {
  jobs: JobBasicInfo[]
}

// 收藏岗位请求参数
export interface FavoriteJobParams {
  jobId: number
  isFavorited: boolean
}

// 分享岗位请求参数
export interface ShareJobParams {
  jobId: number
  platform: string
} 