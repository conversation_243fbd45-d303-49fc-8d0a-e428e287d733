/**
 * @deprecated As of 1.0, use .material-icons instead of .mi
 */
.mi {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
}

/**
 * @deprecated As of 1.0, use .material-icons-outlined instead of .mi-outlined
 */
.mi-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
}

/**
 * @deprecated As of 1.0, use .material-icons-round instead of .mi-round
 */
.mi-round {
  font-family: "Material Icons Round";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
}

/**
 * @deprecated As of 1.0, use .material-icons-sharp instead of .mi-sharp
 */
.mi-sharp {
  font-family: "Material Icons Sharp";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
}

/**
 * @deprecated As of 1.0, use .material-icons-two-tone instead of .mi-two-tone
 */
.mi-two-tone {
  font-family: "Material Icons Two Tone";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga";
}

.mi-123::before {
  content: "\eb8d";
}

.mi-360::before {
  content: "\e577";
}

.mi-10k::before {
  content: "\e951";
}

.mi-10mp::before {
  content: "\e952";
}

.mi-11mp::before {
  content: "\e953";
}

.mi-12mp::before {
  content: "\e954";
}

.mi-13mp::before {
  content: "\e955";
}

.mi-14mp::before {
  content: "\e956";
}

.mi-15mp::before {
  content: "\e957";
}

.mi-16mp::before {
  content: "\e958";
}

.mi-17mp::before {
  content: "\e959";
}

.mi-18-up-rating::before {
  content: "\f8fd";
}

.mi-18mp::before {
  content: "\e95a";
}

.mi-19mp::before {
  content: "\e95b";
}

.mi-1k::before {
  content: "\e95c";
}

.mi-1k-plus::before {
  content: "\e95d";
}

.mi-1x-mobiledata::before {
  content: "\efcd";
}

.mi-20mp::before {
  content: "\e95e";
}

.mi-21mp::before {
  content: "\e95f";
}

.mi-22mp::before {
  content: "\e960";
}

.mi-23mp::before {
  content: "\e961";
}

.mi-24mp::before {
  content: "\e962";
}

.mi-2k::before {
  content: "\e963";
}

.mi-2k-plus::before {
  content: "\e964";
}

.mi-2mp::before {
  content: "\e965";
}

.mi-30fps::before {
  content: "\efce";
}

.mi-30fps-select::before {
  content: "\efcf";
}

.mi-3d-rotation::before {
  content: "\e84d";
}

.mi-3g-mobiledata::before {
  content: "\efd0";
}

.mi-3k::before {
  content: "\e966";
}

.mi-3k-plus::before {
  content: "\e967";
}

.mi-3mp::before {
  content: "\e968";
}

.mi-3p::before {
  content: "\efd1";
}

.mi-4g-mobiledata::before {
  content: "\efd2";
}

.mi-4g-plus-mobiledata::before {
  content: "\efd3";
}

.mi-4k::before {
  content: "\e072";
}

.mi-4k-plus::before {
  content: "\e969";
}

.mi-4mp::before {
  content: "\e96a";
}

.mi-5g::before {
  content: "\ef38";
}

.mi-5k::before {
  content: "\e96b";
}

.mi-5k-plus::before {
  content: "\e96c";
}

.mi-5mp::before {
  content: "\e96d";
}

.mi-60fps::before {
  content: "\efd4";
}

.mi-60fps-select::before {
  content: "\efd5";
}

.mi-6-ft-apart::before {
  content: "\f21e";
}

.mi-6k::before {
  content: "\e96e";
}

.mi-6k-plus::before {
  content: "\e96f";
}

.mi-6mp::before {
  content: "\e970";
}

.mi-7k::before {
  content: "\e971";
}

.mi-7k-plus::before {
  content: "\e972";
}

.mi-7mp::before {
  content: "\e973";
}

.mi-8k::before {
  content: "\e974";
}

.mi-8k-plus::before {
  content: "\e975";
}

.mi-8mp::before {
  content: "\e976";
}

.mi-9k::before {
  content: "\e977";
}

.mi-9k-plus::before {
  content: "\e978";
}

.mi-9mp::before {
  content: "\e979";
}

.mi-abc::before {
  content: "\eb94";
}

.mi-ac-unit::before {
  content: "\eb3b";
}

.mi-access-alarm::before {
  content: "\e190";
}

.mi-access-alarms::before {
  content: "\e191";
}

.mi-access-time::before {
  content: "\e192";
}

.mi-access-time-filled::before {
  content: "\efd6";
}

.mi-accessibility::before {
  content: "\e84e";
}

.mi-accessibility-new::before {
  content: "\e92c";
}

.mi-accessible::before {
  content: "\e914";
}

.mi-accessible-forward::before {
  content: "\e934";
}

.mi-account-balance::before {
  content: "\e84f";
}

.mi-account-balance-wallet::before {
  content: "\e850";
}

.mi-account-box::before {
  content: "\e851";
}

.mi-account-circle::before {
  content: "\e853";
}

.mi-account-tree::before {
  content: "\e97a";
}

.mi-ad-units::before {
  content: "\ef39";
}

.mi-adb::before {
  content: "\e60e";
}

.mi-add::before {
  content: "\e145";
}

.mi-add-a-photo::before {
  content: "\e439";
}

.mi-add-alarm::before {
  content: "\e193";
}

.mi-add-alert::before {
  content: "\e003";
}

.mi-add-box::before {
  content: "\e146";
}

.mi-add-business::before {
  content: "\e729";
}

.mi-add-call::before {
  content: "\e0e8";
}

.mi-add-card::before {
  content: "\eb86";
}

.mi-add-chart::before {
  content: "\e97b";
}

.mi-add-circle::before {
  content: "\e147";
}

.mi-add-circle-outline::before {
  content: "\e148";
}

.mi-add-comment::before {
  content: "\e266";
}

.mi-add-home::before {
  content: "\f8eb";
}

.mi-add-home-work::before {
  content: "\f8ed";
}

.mi-add-ic-call::before {
  content: "\e97c";
}

.mi-add-link::before {
  content: "\e178";
}

.mi-add-location::before {
  content: "\e567";
}

.mi-add-location-alt::before {
  content: "\ef3a";
}

.mi-add-moderator::before {
  content: "\e97d";
}

.mi-add-photo-alternate::before {
  content: "\e43e";
}

.mi-add-reaction::before {
  content: "\e1d3";
}

.mi-add-road::before {
  content: "\ef3b";
}

.mi-add-shopping-cart::before {
  content: "\e854";
}

.mi-add-task::before {
  content: "\f23a";
}

.mi-add-to-drive::before {
  content: "\e65c";
}

.mi-add-to-home-screen::before {
  content: "\e1fe";
}

.mi-add-to-photos::before {
  content: "\e39d";
}

.mi-add-to-queue::before {
  content: "\e05c";
}

.mi-addchart::before {
  content: "\ef3c";
}

.mi-adf-scanner::before {
  content: "\eada";
}

.mi-adjust::before {
  content: "\e39e";
}

.mi-admin-panel-settings::before {
  content: "\ef3d";
}

.mi-adobe::before {
  content: "\ea96";
}

.mi-ads-click::before {
  content: "\e762";
}

.mi-agriculture::before {
  content: "\ea79";
}

.mi-air::before {
  content: "\efd8";
}

.mi-airline-seat-flat::before {
  content: "\e630";
}

.mi-airline-seat-flat-angled::before {
  content: "\e631";
}

.mi-airline-seat-individual-suite::before {
  content: "\e632";
}

.mi-airline-seat-legroom-extra::before {
  content: "\e633";
}

.mi-airline-seat-legroom-normal::before {
  content: "\e634";
}

.mi-airline-seat-legroom-reduced::before {
  content: "\e635";
}

.mi-airline-seat-recline-extra::before {
  content: "\e636";
}

.mi-airline-seat-recline-normal::before {
  content: "\e637";
}

.mi-airline-stops::before {
  content: "\e7d0";
}

.mi-airlines::before {
  content: "\e7ca";
}

.mi-airplane-ticket::before {
  content: "\efd9";
}

.mi-airplanemode-active::before {
  content: "\e195";
}

.mi-airplanemode-inactive::before {
  content: "\e194";
}

.mi-airplanemode-off::before {
  content: "\e194";
}

.mi-airplanemode-on::before {
  content: "\e195";
}

.mi-airplay::before {
  content: "\e055";
}

.mi-airport-shuttle::before {
  content: "\eb3c";
}

.mi-alarm::before {
  content: "\e855";
}

.mi-alarm-add::before {
  content: "\e856";
}

.mi-alarm-off::before {
  content: "\e857";
}

.mi-alarm-on::before {
  content: "\e858";
}

.mi-album::before {
  content: "\e019";
}

.mi-align-horizontal-center::before {
  content: "\e00f";
}

.mi-align-horizontal-left::before {
  content: "\e00d";
}

.mi-align-horizontal-right::before {
  content: "\e010";
}

.mi-align-vertical-bottom::before {
  content: "\e015";
}

.mi-align-vertical-center::before {
  content: "\e011";
}

.mi-align-vertical-top::before {
  content: "\e00c";
}

.mi-all-inbox::before {
  content: "\e97f";
}

.mi-all-inclusive::before {
  content: "\eb3d";
}

.mi-all-out::before {
  content: "\e90b";
}

.mi-alt-route::before {
  content: "\f184";
}

.mi-alternate-email::before {
  content: "\e0e6";
}

.mi-amp-stories::before {
  content: "\ea13";
}

.mi-analytics::before {
  content: "\ef3e";
}

.mi-anchor::before {
  content: "\f1cd";
}

.mi-android::before {
  content: "\e859";
}

.mi-animation::before {
  content: "\e71c";
}

.mi-announcement::before {
  content: "\e85a";
}

.mi-aod::before {
  content: "\efda";
}

.mi-apartment::before {
  content: "\ea40";
}

.mi-api::before {
  content: "\f1b7";
}

.mi-app-blocking::before {
  content: "\ef3f";
}

.mi-app-registration::before {
  content: "\ef40";
}

.mi-app-settings-alt::before {
  content: "\ef41";
}

.mi-app-shortcut::before {
  content: "\eae4";
}

.mi-apple::before {
  content: "\ea80";
}

.mi-approval::before {
  content: "\e982";
}

.mi-apps::before {
  content: "\e5c3";
}

.mi-apps-outage::before {
  content: "\e7cc";
}

.mi-architecture::before {
  content: "\ea3b";
}

.mi-archive::before {
  content: "\e149";
}

.mi-area-chart::before {
  content: "\e770";
}

.mi-arrow-back::before {
  content: "\e5c4";
}

.mi-arrow-back-ios::before {
  content: "\e5e0";
}

.mi-arrow-back-ios-new::before {
  content: "\e2ea";
}

.mi-arrow-circle-down::before {
  content: "\f181";
}

.mi-arrow-circle-left::before {
  content: "\eaa7";
}

.mi-arrow-circle-right::before {
  content: "\eaaa";
}

.mi-arrow-circle-up::before {
  content: "\f182";
}

.mi-arrow-downward::before {
  content: "\e5db";
}

.mi-arrow-drop-down::before {
  content: "\e5c5";
}

.mi-arrow-drop-down-circle::before {
  content: "\e5c6";
}

.mi-arrow-drop-up::before {
  content: "\e5c7";
}

.mi-arrow-forward::before {
  content: "\e5c8";
}

.mi-arrow-forward-ios::before {
  content: "\e5e1";
}

.mi-arrow-left::before {
  content: "\e5de";
}

.mi-arrow-outward::before {
  content: "\f8ce";
}

.mi-arrow-right::before {
  content: "\e5df";
}

.mi-arrow-right-alt::before {
  content: "\e941";
}

.mi-arrow-upward::before {
  content: "\e5d8";
}

.mi-art-track::before {
  content: "\e060";
}

.mi-article::before {
  content: "\ef42";
}

.mi-aspect-ratio::before {
  content: "\e85b";
}

.mi-assessment::before {
  content: "\e85c";
}

.mi-assignment::before {
  content: "\e85d";
}

.mi-assignment-add::before {
  content: "\f848";
}

.mi-assignment-ind::before {
  content: "\e85e";
}

.mi-assignment-late::before {
  content: "\e85f";
}

.mi-assignment-return::before {
  content: "\e860";
}

.mi-assignment-returned::before {
  content: "\e861";
}

.mi-assignment-turned-in::before {
  content: "\e862";
}

.mi-assist-walker::before {
  content: "\f8d5";
}

.mi-assistant::before {
  content: "\e39f";
}

.mi-assistant-direction::before {
  content: "\e988";
}

.mi-assistant-navigation::before {
  content: "\e989";
}

.mi-assistant-photo::before {
  content: "\e3a0";
}

.mi-assured-workload::before {
  content: "\eb6f";
}

.mi-atm::before {
  content: "\e573";
}

.mi-attach-email::before {
  content: "\ea5e";
}

.mi-attach-file::before {
  content: "\e226";
}

.mi-attach-money::before {
  content: "\e227";
}

.mi-attachment::before {
  content: "\e2bc";
}

.mi-attractions::before {
  content: "\ea52";
}

.mi-attribution::before {
  content: "\efdb";
}

.mi-audio-file::before {
  content: "\eb82";
}

.mi-audiotrack::before {
  content: "\e3a1";
}

.mi-auto-awesome::before {
  content: "\e65f";
}

.mi-auto-awesome-mosaic::before {
  content: "\e660";
}

.mi-auto-awesome-motion::before {
  content: "\e661";
}

.mi-auto-delete::before {
  content: "\ea4c";
}

.mi-auto-fix-high::before {
  content: "\e663";
}

.mi-auto-fix-normal::before {
  content: "\e664";
}

.mi-auto-fix-off::before {
  content: "\e665";
}

.mi-auto-graph::before {
  content: "\e4fb";
}

.mi-auto-mode::before {
  content: "\ec20";
}

.mi-auto-stories::before {
  content: "\e666";
}

.mi-autofps-select::before {
  content: "\efdc";
}

.mi-autorenew::before {
  content: "\e863";
}

.mi-av-timer::before {
  content: "\e01b";
}

.mi-baby-changing-station::before {
  content: "\f19b";
}

.mi-back-hand::before {
  content: "\e764";
}

.mi-backpack::before {
  content: "\f19c";
}

.mi-backspace::before {
  content: "\e14a";
}

.mi-backup::before {
  content: "\e864";
}

.mi-backup-table::before {
  content: "\ef43";
}

.mi-badge::before {
  content: "\ea67";
}

.mi-bakery-dining::before {
  content: "\ea53";
}

.mi-balance::before {
  content: "\eaf6";
}

.mi-balcony::before {
  content: "\e58f";
}

.mi-ballot::before {
  content: "\e172";
}

.mi-bar-chart::before {
  content: "\e26b";
}

.mi-barcode-reader::before {
  content: "\f85c";
}

.mi-batch-prediction::before {
  content: "\f0f5";
}

.mi-bathroom::before {
  content: "\efdd";
}

.mi-bathtub::before {
  content: "\ea41";
}

.mi-battery-0-bar::before {
  content: "\ebdc";
}

.mi-battery-1-bar::before {
  content: "\ebd9";
}

.mi-battery-20::before {
  content: "\f09c";
}

.mi-battery-2-bar::before {
  content: "\ebe0";
}

.mi-battery-30::before {
  content: "\f09d";
}

.mi-battery-3-bar::before {
  content: "\ebdd";
}

.mi-battery-4-bar::before {
  content: "\ebe2";
}

.mi-battery-50::before {
  content: "\f09e";
}

.mi-battery-5-bar::before {
  content: "\ebd4";
}

.mi-battery-60::before {
  content: "\f09f";
}

.mi-battery-6-bar::before {
  content: "\ebd2";
}

.mi-battery-80::before {
  content: "\f0a0";
}

.mi-battery-90::before {
  content: "\f0a1";
}

.mi-battery-alert::before {
  content: "\e19c";
}

.mi-battery-charging-20::before {
  content: "\f0a2";
}

.mi-battery-charging-30::before {
  content: "\f0a3";
}

.mi-battery-charging-50::before {
  content: "\f0a4";
}

.mi-battery-charging-60::before {
  content: "\f0a5";
}

.mi-battery-charging-80::before {
  content: "\f0a6";
}

.mi-battery-charging-90::before {
  content: "\f0a7";
}

.mi-battery-charging-full::before {
  content: "\e1a3";
}

.mi-battery-full::before {
  content: "\e1a4";
}

.mi-battery-saver::before {
  content: "\efde";
}

.mi-battery-std::before {
  content: "\e1a5";
}

.mi-battery-unknown::before {
  content: "\e1a6";
}

.mi-beach-access::before {
  content: "\eb3e";
}

.mi-bed::before {
  content: "\efdf";
}

.mi-bedroom-baby::before {
  content: "\efe0";
}

.mi-bedroom-child::before {
  content: "\efe1";
}

.mi-bedroom-parent::before {
  content: "\efe2";
}

.mi-bedtime::before {
  content: "\ef44";
}

.mi-bedtime-off::before {
  content: "\eb76";
}

.mi-beenhere::before {
  content: "\e52d";
}

.mi-bento::before {
  content: "\f1f4";
}

.mi-bike-scooter::before {
  content: "\ef45";
}

.mi-biotech::before {
  content: "\ea3a";
}

.mi-blender::before {
  content: "\efe3";
}

.mi-blind::before {
  content: "\f8d6";
}

.mi-blinds::before {
  content: "\e286";
}

.mi-blinds-closed::before {
  content: "\ec1f";
}

.mi-block::before {
  content: "\e14b";
}

.mi-block-flipped::before {
  content: "\ef46";
}

.mi-bloodtype::before {
  content: "\efe4";
}

.mi-bluetooth::before {
  content: "\e1a7";
}

.mi-bluetooth-audio::before {
  content: "\e60f";
}

.mi-bluetooth-connected::before {
  content: "\e1a8";
}

.mi-bluetooth-disabled::before {
  content: "\e1a9";
}

.mi-bluetooth-drive::before {
  content: "\efe5";
}

.mi-bluetooth-searching::before {
  content: "\e1aa";
}

.mi-blur-circular::before {
  content: "\e3a2";
}

.mi-blur-linear::before {
  content: "\e3a3";
}

.mi-blur-off::before {
  content: "\e3a4";
}

.mi-blur-on::before {
  content: "\e3a5";
}

.mi-bolt::before {
  content: "\ea0b";
}

.mi-book::before {
  content: "\e865";
}

.mi-book-online::before {
  content: "\f217";
}

.mi-bookmark::before {
  content: "\e866";
}

.mi-bookmark-add::before {
  content: "\e598";
}

.mi-bookmark-added::before {
  content: "\e599";
}

.mi-bookmark-border::before {
  content: "\e867";
}

.mi-bookmark-outline::before {
  content: "\e867";
}

.mi-bookmark-remove::before {
  content: "\e59a";
}

.mi-bookmarks::before {
  content: "\e98b";
}

.mi-border-all::before {
  content: "\e228";
}

.mi-border-bottom::before {
  content: "\e229";
}

.mi-border-clear::before {
  content: "\e22a";
}

.mi-border-color::before {
  content: "\e22b";
}

.mi-border-horizontal::before {
  content: "\e22c";
}

.mi-border-inner::before {
  content: "\e22d";
}

.mi-border-left::before {
  content: "\e22e";
}

.mi-border-outer::before {
  content: "\e22f";
}

.mi-border-right::before {
  content: "\e230";
}

.mi-border-style::before {
  content: "\e231";
}

.mi-border-top::before {
  content: "\e232";
}

.mi-border-vertical::before {
  content: "\e233";
}

.mi-boy::before {
  content: "\eb67";
}

.mi-branding-watermark::before {
  content: "\e06b";
}

.mi-breakfast-dining::before {
  content: "\ea54";
}

.mi-brightness-1::before {
  content: "\e3a6";
}

.mi-brightness-2::before {
  content: "\e3a7";
}

.mi-brightness-3::before {
  content: "\e3a8";
}

.mi-brightness-4::before {
  content: "\e3a9";
}

.mi-brightness-5::before {
  content: "\e3aa";
}

.mi-brightness-6::before {
  content: "\e3ab";
}

.mi-brightness-7::before {
  content: "\e3ac";
}

.mi-brightness-auto::before {
  content: "\e1ab";
}

.mi-brightness-high::before {
  content: "\e1ac";
}

.mi-brightness-low::before {
  content: "\e1ad";
}

.mi-brightness-medium::before {
  content: "\e1ae";
}

.mi-broadcast-on-home::before {
  content: "\f8f8";
}

.mi-broadcast-on-personal::before {
  content: "\f8f9";
}

.mi-broken-image::before {
  content: "\e3ad";
}

.mi-browse-gallery::before {
  content: "\ebd1";
}

.mi-browser-not-supported::before {
  content: "\ef47";
}

.mi-browser-updated::before {
  content: "\e7cf";
}

.mi-brunch-dining::before {
  content: "\ea73";
}

.mi-brush::before {
  content: "\e3ae";
}

.mi-bubble-chart::before {
  content: "\e6dd";
}

.mi-bug-report::before {
  content: "\e868";
}

.mi-build::before {
  content: "\e869";
}

.mi-build-circle::before {
  content: "\ef48";
}

.mi-bungalow::before {
  content: "\e591";
}

.mi-burst-mode::before {
  content: "\e43c";
}

.mi-bus-alert::before {
  content: "\e98f";
}

.mi-business::before {
  content: "\e0af";
}

.mi-business-center::before {
  content: "\eb3f";
}

.mi-cabin::before {
  content: "\e589";
}

.mi-cable::before {
  content: "\efe6";
}

.mi-cached::before {
  content: "\e86a";
}

.mi-cake::before {
  content: "\e7e9";
}

.mi-calculate::before {
  content: "\ea5f";
}

.mi-calendar-month::before {
  content: "\ebcc";
}

.mi-calendar-today::before {
  content: "\e935";
}

.mi-calendar-view-day::before {
  content: "\e936";
}

.mi-calendar-view-month::before {
  content: "\efe7";
}

.mi-calendar-view-week::before {
  content: "\efe8";
}

.mi-call::before {
  content: "\e0b0";
}

.mi-call-end::before {
  content: "\e0b1";
}

.mi-call-made::before {
  content: "\e0b2";
}

.mi-call-merge::before {
  content: "\e0b3";
}

.mi-call-missed::before {
  content: "\e0b4";
}

.mi-call-missed-outgoing::before {
  content: "\e0e4";
}

.mi-call-received::before {
  content: "\e0b5";
}

.mi-call-split::before {
  content: "\e0b6";
}

.mi-call-to-action::before {
  content: "\e06c";
}

.mi-camera::before {
  content: "\e3af";
}

.mi-camera-alt::before {
  content: "\e3b0";
}

.mi-camera-enhance::before {
  content: "\e8fc";
}

.mi-camera-front::before {
  content: "\e3b1";
}

.mi-camera-indoor::before {
  content: "\efe9";
}

.mi-camera-outdoor::before {
  content: "\efea";
}

.mi-camera-rear::before {
  content: "\e3b2";
}

.mi-camera-roll::before {
  content: "\e3b3";
}

.mi-cameraswitch::before {
  content: "\efeb";
}

.mi-campaign::before {
  content: "\ef49";
}

.mi-cancel::before {
  content: "\e5c9";
}

.mi-cancel-presentation::before {
  content: "\e0e9";
}

.mi-cancel-schedule-send::before {
  content: "\ea39";
}

.mi-candlestick-chart::before {
  content: "\ead4";
}

.mi-car-crash::before {
  content: "\ebf2";
}

.mi-car-rental::before {
  content: "\ea55";
}

.mi-car-repair::before {
  content: "\ea56";
}

.mi-card-giftcard::before {
  content: "\e8f6";
}

.mi-card-membership::before {
  content: "\e8f7";
}

.mi-card-travel::before {
  content: "\e8f8";
}

.mi-carpenter::before {
  content: "\f1f8";
}

.mi-cases::before {
  content: "\e992";
}

.mi-casino::before {
  content: "\eb40";
}

.mi-cast::before {
  content: "\e307";
}

.mi-cast-connected::before {
  content: "\e308";
}

.mi-cast-for-education::before {
  content: "\efec";
}

.mi-castle::before {
  content: "\eab1";
}

.mi-catching-pokemon::before {
  content: "\e508";
}

.mi-category::before {
  content: "\e574";
}

.mi-celebration::before {
  content: "\ea65";
}

.mi-cell-tower::before {
  content: "\ebba";
}

.mi-cell-wifi::before {
  content: "\e0ec";
}

.mi-center-focus-strong::before {
  content: "\e3b4";
}

.mi-center-focus-weak::before {
  content: "\e3b5";
}

.mi-chair::before {
  content: "\efed";
}

.mi-chair-alt::before {
  content: "\efee";
}

.mi-chalet::before {
  content: "\e585";
}

.mi-change-circle::before {
  content: "\e2e7";
}

.mi-change-history::before {
  content: "\e86b";
}

.mi-charging-station::before {
  content: "\f19d";
}

.mi-chat::before {
  content: "\e0b7";
}

.mi-chat-bubble::before {
  content: "\e0ca";
}

.mi-chat-bubble-outline::before {
  content: "\e0cb";
}

.mi-check::before {
  content: "\e5ca";
}

.mi-check-box::before {
  content: "\e834";
}

.mi-check-box-outline-blank::before {
  content: "\e835";
}

.mi-check-circle::before {
  content: "\e86c";
}

.mi-check-circle-outline::before {
  content: "\e92d";
}

.mi-checklist::before {
  content: "\e6b1";
}

.mi-checklist-rtl::before {
  content: "\e6b3";
}

.mi-checkroom::before {
  content: "\f19e";
}

.mi-chevron-left::before {
  content: "\e5cb";
}

.mi-chevron-right::before {
  content: "\e5cc";
}

.mi-child-care::before {
  content: "\eb41";
}

.mi-child-friendly::before {
  content: "\eb42";
}

.mi-chrome-reader-mode::before {
  content: "\e86d";
}

.mi-church::before {
  content: "\eaae";
}

.mi-circle::before {
  content: "\ef4a";
}

.mi-circle-notifications::before {
  content: "\e994";
}

.mi-class::before {
  content: "\e86e";
}

.mi-clean-hands::before {
  content: "\f21f";
}

.mi-cleaning-services::before {
  content: "\f0ff";
}

.mi-clear::before {
  content: "\e14c";
}

.mi-clear-all::before {
  content: "\e0b8";
}

.mi-close::before {
  content: "\e5cd";
}

.mi-close-fullscreen::before {
  content: "\f1cf";
}

.mi-closed-caption::before {
  content: "\e01c";
}

.mi-closed-caption-disabled::before {
  content: "\f1dc";
}

.mi-closed-caption-off::before {
  content: "\e996";
}

.mi-cloud::before {
  content: "\e2bd";
}

.mi-cloud-circle::before {
  content: "\e2be";
}

.mi-cloud-done::before {
  content: "\e2bf";
}

.mi-cloud-download::before {
  content: "\e2c0";
}

.mi-cloud-off::before {
  content: "\e2c1";
}

.mi-cloud-queue::before {
  content: "\e2c2";
}

.mi-cloud-sync::before {
  content: "\eb5a";
}

.mi-cloud-upload::before {
  content: "\e2c3";
}

.mi-cloudy-snowing::before {
  content: "\e810";
}

.mi-co2::before {
  content: "\e7b0";
}

.mi-co-present::before {
  content: "\eaf0";
}

.mi-code::before {
  content: "\e86f";
}

.mi-code-off::before {
  content: "\e4f3";
}

.mi-coffee::before {
  content: "\efef";
}

.mi-coffee-maker::before {
  content: "\eff0";
}

.mi-collections::before {
  content: "\e3b6";
}

.mi-collections-bookmark::before {
  content: "\e431";
}

.mi-color-lens::before {
  content: "\e3b7";
}

.mi-colorize::before {
  content: "\e3b8";
}

.mi-comment::before {
  content: "\e0b9";
}

.mi-comment-bank::before {
  content: "\ea4e";
}

.mi-comments-disabled::before {
  content: "\e7a2";
}

.mi-commit::before {
  content: "\eaf5";
}

.mi-commute::before {
  content: "\e940";
}

.mi-compare::before {
  content: "\e3b9";
}

.mi-compare-arrows::before {
  content: "\e915";
}

.mi-compass-calibration::before {
  content: "\e57c";
}

.mi-compost::before {
  content: "\e761";
}

.mi-compress::before {
  content: "\e94d";
}

.mi-computer::before {
  content: "\e30a";
}

.mi-confirmation-num::before {
  content: "\e638";
}

.mi-confirmation-number::before {
  content: "\e638";
}

.mi-connect-without-contact::before {
  content: "\f223";
}

.mi-connected-tv::before {
  content: "\e998";
}

.mi-connecting-airports::before {
  content: "\e7c9";
}

.mi-construction::before {
  content: "\ea3c";
}

.mi-contact-emergency::before {
  content: "\f8d1";
}

.mi-contact-mail::before {
  content: "\e0d0";
}

.mi-contact-page::before {
  content: "\f22e";
}

.mi-contact-phone::before {
  content: "\e0cf";
}

.mi-contact-support::before {
  content: "\e94c";
}

.mi-contactless::before {
  content: "\ea71";
}

.mi-contacts::before {
  content: "\e0ba";
}

.mi-content-copy::before {
  content: "\e14d";
}

.mi-content-cut::before {
  content: "\e14e";
}

.mi-content-paste::before {
  content: "\e14f";
}

.mi-content-paste-go::before {
  content: "\ea8e";
}

.mi-content-paste-off::before {
  content: "\e4f8";
}

.mi-content-paste-search::before {
  content: "\ea9b";
}

.mi-contrast::before {
  content: "\eb37";
}

.mi-control-camera::before {
  content: "\e074";
}

.mi-control-point::before {
  content: "\e3ba";
}

.mi-control-point-duplicate::before {
  content: "\e3bb";
}

.mi-conveyor-belt::before {
  content: "\f867";
}

.mi-cookie::before {
  content: "\eaac";
}

.mi-copy::before {
  content: "\f08a";
}

.mi-copy-all::before {
  content: "\e2ec";
}

.mi-copyright::before {
  content: "\e90c";
}

.mi-coronavirus::before {
  content: "\f221";
}

.mi-corporate-fare::before {
  content: "\f1d0";
}

.mi-cottage::before {
  content: "\e587";
}

.mi-countertops::before {
  content: "\f1f7";
}

.mi-create::before {
  content: "\e150";
}

.mi-create-new-folder::before {
  content: "\e2cc";
}

.mi-credit-card::before {
  content: "\e870";
}

.mi-credit-card-off::before {
  content: "\e4f4";
}

.mi-credit-score::before {
  content: "\eff1";
}

.mi-crib::before {
  content: "\e588";
}

.mi-crisis-alert::before {
  content: "\ebe9";
}

.mi-crop::before {
  content: "\e3be";
}

.mi-crop-16-9::before {
  content: "\e3bc";
}

.mi-crop-3-2::before {
  content: "\e3bd";
}

.mi-crop-5-4::before {
  content: "\e3bf";
}

.mi-crop-7-5::before {
  content: "\e3c0";
}

.mi-crop-din::before {
  content: "\e3c1";
}

.mi-crop-free::before {
  content: "\e3c2";
}

.mi-crop-landscape::before {
  content: "\e3c3";
}

.mi-crop-original::before {
  content: "\e3c4";
}

.mi-crop-portrait::before {
  content: "\e3c5";
}

.mi-crop-rotate::before {
  content: "\e437";
}

.mi-crop-square::before {
  content: "\e3c6";
}

.mi-cruelty-free::before {
  content: "\e799";
}

.mi-css::before {
  content: "\eb93";
}

.mi-currency-bitcoin::before {
  content: "\ebc5";
}

.mi-currency-exchange::before {
  content: "\eb70";
}

.mi-currency-franc::before {
  content: "\eafa";
}

.mi-currency-lira::before {
  content: "\eaef";
}

.mi-currency-pound::before {
  content: "\eaf1";
}

.mi-currency-ruble::before {
  content: "\eaec";
}

.mi-currency-rupee::before {
  content: "\eaf7";
}

.mi-currency-yen::before {
  content: "\eafb";
}

.mi-currency-yuan::before {
  content: "\eaf9";
}

.mi-curtains::before {
  content: "\ec1e";
}

.mi-curtains-closed::before {
  content: "\ec1d";
}

.mi-cut::before {
  content: "\f08b";
}

.mi-cyclone::before {
  content: "\ebd5";
}

.mi-dangerous::before {
  content: "\e99a";
}

.mi-dark-mode::before {
  content: "\e51c";
}

.mi-dashboard::before {
  content: "\e871";
}

.mi-dashboard-customize::before {
  content: "\e99b";
}

.mi-data-array::before {
  content: "\ead1";
}

.mi-data-exploration::before {
  content: "\e76f";
}

.mi-data-object::before {
  content: "\ead3";
}

.mi-data-saver-off::before {
  content: "\eff2";
}

.mi-data-saver-on::before {
  content: "\eff3";
}

.mi-data-thresholding::before {
  content: "\eb9f";
}

.mi-data-usage::before {
  content: "\e1af";
}

.mi-dataset::before {
  content: "\f8ee";
}

.mi-dataset-linked::before {
  content: "\f8ef";
}

.mi-date-range::before {
  content: "\e916";
}

.mi-deblur::before {
  content: "\eb77";
}

.mi-deck::before {
  content: "\ea42";
}

.mi-dehaze::before {
  content: "\e3c7";
}

.mi-delete::before {
  content: "\e872";
}

.mi-delete-forever::before {
  content: "\e92b";
}

.mi-delete-outline::before {
  content: "\e92e";
}

.mi-delete-sweep::before {
  content: "\e16c";
}

.mi-delivery-dining::before {
  content: "\ea72";
}

.mi-density-large::before {
  content: "\eba9";
}

.mi-density-medium::before {
  content: "\eb9e";
}

.mi-density-small::before {
  content: "\eba8";
}

.mi-departure-board::before {
  content: "\e576";
}

.mi-description::before {
  content: "\e873";
}

.mi-deselect::before {
  content: "\ebb6";
}

.mi-design-services::before {
  content: "\f10a";
}

.mi-desk::before {
  content: "\f8f4";
}

.mi-desktop-access-disabled::before {
  content: "\e99d";
}

.mi-desktop-mac::before {
  content: "\e30b";
}

.mi-desktop-windows::before {
  content: "\e30c";
}

.mi-details::before {
  content: "\e3c8";
}

.mi-developer-board::before {
  content: "\e30d";
}

.mi-developer-board-off::before {
  content: "\e4ff";
}

.mi-developer-mode::before {
  content: "\e1b0";
}

.mi-device-hub::before {
  content: "\e335";
}

.mi-device-thermostat::before {
  content: "\e1ff";
}

.mi-device-unknown::before {
  content: "\e339";
}

.mi-devices::before {
  content: "\e1b1";
}

.mi-devices-fold::before {
  content: "\ebde";
}

.mi-devices-other::before {
  content: "\e337";
}

.mi-dew-point::before {
  content: "\f879";
}

.mi-dialer-sip::before {
  content: "\e0bb";
}

.mi-dialpad::before {
  content: "\e0bc";
}

.mi-diamond::before {
  content: "\ead5";
}

.mi-difference::before {
  content: "\eb7d";
}

.mi-dining::before {
  content: "\eff4";
}

.mi-dinner-dining::before {
  content: "\ea57";
}

.mi-directions::before {
  content: "\e52e";
}

.mi-directions-bike::before {
  content: "\e52f";
}

.mi-directions-boat::before {
  content: "\e532";
}

.mi-directions-boat-filled::before {
  content: "\eff5";
}

.mi-directions-bus::before {
  content: "\e530";
}

.mi-directions-bus-filled::before {
  content: "\eff6";
}

.mi-directions-car::before {
  content: "\e531";
}

.mi-directions-car-filled::before {
  content: "\eff7";
}

.mi-directions-ferry::before {
  content: "\e532";
}

.mi-directions-off::before {
  content: "\f10f";
}

.mi-directions-railway::before {
  content: "\e534";
}

.mi-directions-railway-filled::before {
  content: "\eff8";
}

.mi-directions-run::before {
  content: "\e566";
}

.mi-directions-subway::before {
  content: "\e533";
}

.mi-directions-subway-filled::before {
  content: "\eff9";
}

.mi-directions-train::before {
  content: "\e534";
}

.mi-directions-transit::before {
  content: "\e535";
}

.mi-directions-transit-filled::before {
  content: "\effa";
}

.mi-directions-walk::before {
  content: "\e536";
}

.mi-dirty-lens::before {
  content: "\ef4b";
}

.mi-disabled-by-default::before {
  content: "\f230";
}

.mi-disabled-visible::before {
  content: "\e76e";
}

.mi-disc-full::before {
  content: "\e610";
}

.mi-discord::before {
  content: "\ea6c";
}

.mi-discount::before {
  content: "\ebc9";
}

.mi-display-settings::before {
  content: "\eb97";
}

.mi-diversity-1::before {
  content: "\f8d7";
}

.mi-diversity-2::before {
  content: "\f8d8";
}

.mi-diversity-3::before {
  content: "\f8d9";
}

.mi-dnd-forwardslash::before {
  content: "\e611";
}

.mi-dns::before {
  content: "\e875";
}

.mi-do-disturb::before {
  content: "\f08c";
}

.mi-do-disturb-alt::before {
  content: "\f08d";
}

.mi-do-disturb-off::before {
  content: "\f08e";
}

.mi-do-disturb-on::before {
  content: "\f08f";
}

.mi-do-not-disturb::before {
  content: "\e612";
}

.mi-do-not-disturb-alt::before {
  content: "\e611";
}

.mi-do-not-disturb-off::before {
  content: "\e643";
}

.mi-do-not-disturb-on::before {
  content: "\e644";
}

.mi-do-not-disturb-on-total-silence::before {
  content: "\effb";
}

.mi-do-not-step::before {
  content: "\f19f";
}

.mi-do-not-touch::before {
  content: "\f1b0";
}

.mi-dock::before {
  content: "\e30e";
}

.mi-document-scanner::before {
  content: "\e5fa";
}

.mi-domain::before {
  content: "\e7ee";
}

.mi-domain-add::before {
  content: "\eb62";
}

.mi-domain-disabled::before {
  content: "\e0ef";
}

.mi-domain-verification::before {
  content: "\ef4c";
}

.mi-done::before {
  content: "\e876";
}

.mi-done-all::before {
  content: "\e877";
}

.mi-done-outline::before {
  content: "\e92f";
}

.mi-donut-large::before {
  content: "\e917";
}

.mi-donut-small::before {
  content: "\e918";
}

.mi-door-back::before {
  content: "\effc";
}

.mi-door-front::before {
  content: "\effd";
}

.mi-door-sliding::before {
  content: "\effe";
}

.mi-doorbell::before {
  content: "\efff";
}

.mi-double-arrow::before {
  content: "\ea50";
}

.mi-downhill-skiing::before {
  content: "\e509";
}

.mi-download::before {
  content: "\f090";
}

.mi-download-done::before {
  content: "\f091";
}

.mi-download-for-offline::before {
  content: "\f000";
}

.mi-downloading::before {
  content: "\f001";
}

.mi-drafts::before {
  content: "\e151";
}

.mi-drag-handle::before {
  content: "\e25d";
}

.mi-drag-indicator::before {
  content: "\e945";
}

.mi-draw::before {
  content: "\e746";
}

.mi-drive-eta::before {
  content: "\e613";
}

.mi-drive-file-move::before {
  content: "\e675";
}

.mi-drive-file-move-outline::before {
  content: "\e9a1";
}

.mi-drive-file-move-rtl::before {
  content: "\e76d";
}

.mi-drive-file-rename-outline::before {
  content: "\e9a2";
}

.mi-drive-folder-upload::before {
  content: "\e9a3";
}

.mi-dry::before {
  content: "\f1b3";
}

.mi-dry-cleaning::before {
  content: "\ea58";
}

.mi-duo::before {
  content: "\e9a5";
}

.mi-dvr::before {
  content: "\e1b2";
}

.mi-dynamic-feed::before {
  content: "\ea14";
}

.mi-dynamic-form::before {
  content: "\f1bf";
}

.mi-e-mobiledata::before {
  content: "\f002";
}

.mi-earbuds::before {
  content: "\f003";
}

.mi-earbuds-battery::before {
  content: "\f004";
}

.mi-east::before {
  content: "\f1df";
}

.mi-eco::before {
  content: "\ea35";
}

.mi-edgesensor-high::before {
  content: "\f005";
}

.mi-edgesensor-low::before {
  content: "\f006";
}

.mi-edit::before {
  content: "\e3c9";
}

.mi-edit-attributes::before {
  content: "\e578";
}

.mi-edit-calendar::before {
  content: "\e742";
}

.mi-edit-document::before {
  content: "\f88c";
}

.mi-edit-location::before {
  content: "\e568";
}

.mi-edit-location-alt::before {
  content: "\e1c5";
}

.mi-edit-note::before {
  content: "\e745";
}

.mi-edit-notifications::before {
  content: "\e525";
}

.mi-edit-off::before {
  content: "\e950";
}

.mi-edit-road::before {
  content: "\ef4d";
}

.mi-edit-square::before {
  content: "\f88d";
}

.mi-egg::before {
  content: "\eacc";
}

.mi-egg-alt::before {
  content: "\eac8";
}

.mi-eject::before {
  content: "\e8fb";
}

.mi-elderly::before {
  content: "\f21a";
}

.mi-elderly-woman::before {
  content: "\eb69";
}

.mi-electric-bike::before {
  content: "\eb1b";
}

.mi-electric-bolt::before {
  content: "\ec1c";
}

.mi-electric-car::before {
  content: "\eb1c";
}

.mi-electric-meter::before {
  content: "\ec1b";
}

.mi-electric-moped::before {
  content: "\eb1d";
}

.mi-electric-rickshaw::before {
  content: "\eb1e";
}

.mi-electric-scooter::before {
  content: "\eb1f";
}

.mi-electrical-services::before {
  content: "\f102";
}

.mi-elevator::before {
  content: "\f1a0";
}

.mi-email::before {
  content: "\e0be";
}

.mi-emergency::before {
  content: "\e1eb";
}

.mi-emergency-recording::before {
  content: "\ebf4";
}

.mi-emergency-share::before {
  content: "\ebf6";
}

.mi-emoji-emotions::before {
  content: "\ea22";
}

.mi-emoji-events::before {
  content: "\ea23";
}

.mi-emoji-flags::before {
  content: "\ea1a";
}

.mi-emoji-food-beverage::before {
  content: "\ea1b";
}

.mi-emoji-nature::before {
  content: "\ea1c";
}

.mi-emoji-objects::before {
  content: "\ea24";
}

.mi-emoji-people::before {
  content: "\ea1d";
}

.mi-emoji-symbols::before {
  content: "\ea1e";
}

.mi-emoji-transportation::before {
  content: "\ea1f";
}

.mi-energy-savings-leaf::before {
  content: "\ec1a";
}

.mi-engineering::before {
  content: "\ea3d";
}

.mi-enhance-photo-translate::before {
  content: "\e8fc";
}

.mi-enhanced-encryption::before {
  content: "\e63f";
}

.mi-equalizer::before {
  content: "\e01d";
}

.mi-error::before {
  content: "\e000";
}

.mi-error-outline::before {
  content: "\e001";
}

.mi-escalator::before {
  content: "\f1a1";
}

.mi-escalator-warning::before {
  content: "\f1ac";
}

.mi-euro::before {
  content: "\ea15";
}

.mi-euro-symbol::before {
  content: "\e926";
}

.mi-ev-station::before {
  content: "\e56d";
}

.mi-event::before {
  content: "\e878";
}

.mi-event-available::before {
  content: "\e614";
}

.mi-event-busy::before {
  content: "\e615";
}

.mi-event-note::before {
  content: "\e616";
}

.mi-event-repeat::before {
  content: "\eb7b";
}

.mi-event-seat::before {
  content: "\e903";
}

.mi-exit-to-app::before {
  content: "\e879";
}

.mi-expand::before {
  content: "\e94f";
}

.mi-expand-circle-down::before {
  content: "\e7cd";
}

.mi-expand-less::before {
  content: "\e5ce";
}

.mi-expand-more::before {
  content: "\e5cf";
}

.mi-explicit::before {
  content: "\e01e";
}

.mi-explore::before {
  content: "\e87a";
}

.mi-explore-off::before {
  content: "\e9a8";
}

.mi-exposure::before {
  content: "\e3ca";
}

.mi-exposure-minus-1::before {
  content: "\e3cb";
}

.mi-exposure-minus-2::before {
  content: "\e3cc";
}

.mi-exposure-neg-1::before {
  content: "\e3cb";
}

.mi-exposure-neg-2::before {
  content: "\e3cc";
}

.mi-exposure-plus-1::before {
  content: "\e3cd";
}

.mi-exposure-plus-2::before {
  content: "\e3ce";
}

.mi-exposure-zero::before {
  content: "\e3cf";
}

.mi-extension::before {
  content: "\e87b";
}

.mi-extension-off::before {
  content: "\e4f5";
}

.mi-face::before {
  content: "\e87c";
}

.mi-face-2::before {
  content: "\f8da";
}

.mi-face-3::before {
  content: "\f8db";
}

.mi-face-4::before {
  content: "\f8dc";
}

.mi-face-5::before {
  content: "\f8dd";
}

.mi-face-6::before {
  content: "\f8de";
}

.mi-face-retouching-natural::before {
  content: "\ef4e";
}

.mi-face-retouching-off::before {
  content: "\f007";
}

.mi-face-unlock::before {
  content: "\f008";
}

.mi-facebook::before {
  content: "\f234";
}

.mi-fact-check::before {
  content: "\f0c5";
}

.mi-factory::before {
  content: "\ebbc";
}

.mi-family-restroom::before {
  content: "\f1a2";
}

.mi-fast-forward::before {
  content: "\e01f";
}

.mi-fast-rewind::before {
  content: "\e020";
}

.mi-fastfood::before {
  content: "\e57a";
}

.mi-favorite::before {
  content: "\e87d";
}

.mi-favorite-border::before {
  content: "\e87e";
}

.mi-favorite-outline::before {
  content: "\e87e";
}

.mi-fax::before {
  content: "\ead8";
}

.mi-featured-play-list::before {
  content: "\e06d";
}

.mi-featured-video::before {
  content: "\e06e";
}

.mi-feed::before {
  content: "\f009";
}

.mi-feedback::before {
  content: "\e87f";
}

.mi-female::before {
  content: "\e590";
}

.mi-fence::before {
  content: "\f1f6";
}

.mi-festival::before {
  content: "\ea68";
}

.mi-fiber-dvr::before {
  content: "\e05d";
}

.mi-fiber-manual-record::before {
  content: "\e061";
}

.mi-fiber-new::before {
  content: "\e05e";
}

.mi-fiber-pin::before {
  content: "\e06a";
}

.mi-fiber-smart-record::before {
  content: "\e062";
}

.mi-file-copy::before {
  content: "\e173";
}

.mi-file-download::before {
  content: "\e2c4";
}

.mi-file-download-done::before {
  content: "\e9aa";
}

.mi-file-download-off::before {
  content: "\e4fe";
}

.mi-file-open::before {
  content: "\eaf3";
}

.mi-file-present::before {
  content: "\ea0e";
}

.mi-file-upload::before {
  content: "\e2c6";
}

.mi-file-upload-off::before {
  content: "\f886";
}

.mi-filter::before {
  content: "\e3d3";
}

.mi-filter-1::before {
  content: "\e3d0";
}

.mi-filter-2::before {
  content: "\e3d1";
}

.mi-filter-3::before {
  content: "\e3d2";
}

.mi-filter-4::before {
  content: "\e3d4";
}

.mi-filter-5::before {
  content: "\e3d5";
}

.mi-filter-6::before {
  content: "\e3d6";
}

.mi-filter-7::before {
  content: "\e3d7";
}

.mi-filter-8::before {
  content: "\e3d8";
}

.mi-filter-9::before {
  content: "\e3d9";
}

.mi-filter-9-plus::before {
  content: "\e3da";
}

.mi-filter-alt::before {
  content: "\ef4f";
}

.mi-filter-alt-off::before {
  content: "\eb32";
}

.mi-filter-b-and-w::before {
  content: "\e3db";
}

.mi-filter-center-focus::before {
  content: "\e3dc";
}

.mi-filter-drama::before {
  content: "\e3dd";
}

.mi-filter-frames::before {
  content: "\e3de";
}

.mi-filter-hdr::before {
  content: "\e3df";
}

.mi-filter-list::before {
  content: "\e152";
}

.mi-filter-list-alt::before {
  content: "\e94e";
}

.mi-filter-list-off::before {
  content: "\eb57";
}

.mi-filter-none::before {
  content: "\e3e0";
}

.mi-filter-tilt-shift::before {
  content: "\e3e2";
}

.mi-filter-vintage::before {
  content: "\e3e3";
}

.mi-find-in-page::before {
  content: "\e880";
}

.mi-find-replace::before {
  content: "\e881";
}

.mi-fingerprint::before {
  content: "\e90d";
}

.mi-fire-extinguisher::before {
  content: "\f1d8";
}

.mi-fire-hydrant::before {
  content: "\f1a3";
}

.mi-fire-hydrant-alt::before {
  content: "\f8f1";
}

.mi-fire-truck::before {
  content: "\f8f2";
}

.mi-fireplace::before {
  content: "\ea43";
}

.mi-first-page::before {
  content: "\e5dc";
}

.mi-fit-screen::before {
  content: "\ea10";
}

.mi-fitbit::before {
  content: "\e82b";
}

.mi-fitness-center::before {
  content: "\eb43";
}

.mi-flag::before {
  content: "\e153";
}

.mi-flag-circle::before {
  content: "\eaf8";
}

.mi-flaky::before {
  content: "\ef50";
}

.mi-flare::before {
  content: "\e3e4";
}

.mi-flash-auto::before {
  content: "\e3e5";
}

.mi-flash-off::before {
  content: "\e3e6";
}

.mi-flash-on::before {
  content: "\e3e7";
}

.mi-flashlight-off::before {
  content: "\f00a";
}

.mi-flashlight-on::before {
  content: "\f00b";
}

.mi-flatware::before {
  content: "\f00c";
}

.mi-flight::before {
  content: "\e539";
}

.mi-flight-class::before {
  content: "\e7cb";
}

.mi-flight-land::before {
  content: "\e904";
}

.mi-flight-takeoff::before {
  content: "\e905";
}

.mi-flip::before {
  content: "\e3e8";
}

.mi-flip-camera-android::before {
  content: "\ea37";
}

.mi-flip-camera-ios::before {
  content: "\ea38";
}

.mi-flip-to-back::before {
  content: "\e882";
}

.mi-flip-to-front::before {
  content: "\e883";
}

.mi-flood::before {
  content: "\ebe6";
}

.mi-flourescent::before {
  content: "\ec31";
}

.mi-fluorescent::before {
  content: "\ec31";
}

.mi-flutter-dash::before {
  content: "\e00b";
}

.mi-fmd-bad::before {
  content: "\f00e";
}

.mi-fmd-good::before {
  content: "\f00f";
}

.mi-foggy::before {
  content: "\e818";
}

.mi-folder::before {
  content: "\e2c7";
}

.mi-folder-copy::before {
  content: "\ebbd";
}

.mi-folder-delete::before {
  content: "\eb34";
}

.mi-folder-off::before {
  content: "\eb83";
}

.mi-folder-open::before {
  content: "\e2c8";
}

.mi-folder-shared::before {
  content: "\e2c9";
}

.mi-folder-special::before {
  content: "\e617";
}

.mi-folder-zip::before {
  content: "\eb2c";
}

.mi-follow-the-signs::before {
  content: "\f222";
}

.mi-font-download::before {
  content: "\e167";
}

.mi-font-download-off::before {
  content: "\e4f9";
}

.mi-food-bank::before {
  content: "\f1f2";
}

.mi-forest::before {
  content: "\ea99";
}

.mi-fork-left::before {
  content: "\eba0";
}

.mi-fork-right::before {
  content: "\ebac";
}

.mi-forklift::before {
  content: "\f868";
}

.mi-format-align-center::before {
  content: "\e234";
}

.mi-format-align-justify::before {
  content: "\e235";
}

.mi-format-align-left::before {
  content: "\e236";
}

.mi-format-align-right::before {
  content: "\e237";
}

.mi-format-bold::before {
  content: "\e238";
}

.mi-format-clear::before {
  content: "\e239";
}

.mi-format-color-fill::before {
  content: "\e23a";
}

.mi-format-color-reset::before {
  content: "\e23b";
}

.mi-format-color-text::before {
  content: "\e23c";
}

.mi-format-indent-decrease::before {
  content: "\e23d";
}

.mi-format-indent-increase::before {
  content: "\e23e";
}

.mi-format-italic::before {
  content: "\e23f";
}

.mi-format-line-spacing::before {
  content: "\e240";
}

.mi-format-list-bulleted::before {
  content: "\e241";
}

.mi-format-list-bulleted-add::before {
  content: "\f849";
}

.mi-format-list-numbered::before {
  content: "\e242";
}

.mi-format-list-numbered-rtl::before {
  content: "\e267";
}

.mi-format-overline::before {
  content: "\eb65";
}

.mi-format-paint::before {
  content: "\e243";
}

.mi-format-quote::before {
  content: "\e244";
}

.mi-format-shapes::before {
  content: "\e25e";
}

.mi-format-size::before {
  content: "\e245";
}

.mi-format-strikethrough::before {
  content: "\e246";
}

.mi-format-textdirection-l-to-r::before {
  content: "\e247";
}

.mi-format-textdirection-r-to-l::before {
  content: "\e248";
}

.mi-format-underline::before {
  content: "\e249";
}

.mi-format-underlined::before {
  content: "\e249";
}

.mi-fort::before {
  content: "\eaad";
}

.mi-forum::before {
  content: "\e0bf";
}

.mi-forward::before {
  content: "\e154";
}

.mi-forward-10::before {
  content: "\e056";
}

.mi-forward-30::before {
  content: "\e057";
}

.mi-forward-5::before {
  content: "\e058";
}

.mi-forward-to-inbox::before {
  content: "\f187";
}

.mi-foundation::before {
  content: "\f200";
}

.mi-free-breakfast::before {
  content: "\eb44";
}

.mi-free-cancellation::before {
  content: "\e748";
}

.mi-front-hand::before {
  content: "\e769";
}

.mi-front-loader::before {
  content: "\f869";
}

.mi-fullscreen::before {
  content: "\e5d0";
}

.mi-fullscreen-exit::before {
  content: "\e5d1";
}

.mi-functions::before {
  content: "\e24a";
}

.mi-g-mobiledata::before {
  content: "\f010";
}

.mi-g-translate::before {
  content: "\e927";
}

.mi-gamepad::before {
  content: "\e30f";
}

.mi-games::before {
  content: "\e021";
}

.mi-garage::before {
  content: "\f011";
}

.mi-gas-meter::before {
  content: "\ec19";
}

.mi-gavel::before {
  content: "\e90e";
}

.mi-generating-tokens::before {
  content: "\e749";
}

.mi-gesture::before {
  content: "\e155";
}

.mi-get-app::before {
  content: "\e884";
}

.mi-gif::before {
  content: "\e908";
}

.mi-gif-box::before {
  content: "\e7a3";
}

.mi-girl::before {
  content: "\eb68";
}

.mi-gite::before {
  content: "\e58b";
}

.mi-goat::before {
  content: "\10fffd";
}

.mi-golf-course::before {
  content: "\eb45";
}

.mi-gpp-bad::before {
  content: "\f012";
}

.mi-gpp-good::before {
  content: "\f013";
}

.mi-gpp-maybe::before {
  content: "\f014";
}

.mi-gps-fixed::before {
  content: "\e1b3";
}

.mi-gps-not-fixed::before {
  content: "\e1b4";
}

.mi-gps-off::before {
  content: "\e1b5";
}

.mi-grade::before {
  content: "\e885";
}

.mi-gradient::before {
  content: "\e3e9";
}

.mi-grading::before {
  content: "\ea4f";
}

.mi-grain::before {
  content: "\e3ea";
}

.mi-graphic-eq::before {
  content: "\e1b8";
}

.mi-grass::before {
  content: "\f205";
}

.mi-grid-3x3::before {
  content: "\f015";
}

.mi-grid-4x4::before {
  content: "\f016";
}

.mi-grid-goldenratio::before {
  content: "\f017";
}

.mi-grid-off::before {
  content: "\e3eb";
}

.mi-grid-on::before {
  content: "\e3ec";
}

.mi-grid-view::before {
  content: "\e9b0";
}

.mi-group::before {
  content: "\e7ef";
}

.mi-group-add::before {
  content: "\e7f0";
}

.mi-group-off::before {
  content: "\e747";
}

.mi-group-remove::before {
  content: "\e7ad";
}

.mi-group-work::before {
  content: "\e886";
}

.mi-groups::before {
  content: "\f233";
}

.mi-groups-2::before {
  content: "\f8df";
}

.mi-groups-3::before {
  content: "\f8e0";
}

.mi-h-mobiledata::before {
  content: "\f018";
}

.mi-h-plus-mobiledata::before {
  content: "\f019";
}

.mi-hail::before {
  content: "\e9b1";
}

.mi-handshake::before {
  content: "\ebcb";
}

.mi-handyman::before {
  content: "\f10b";
}

.mi-hardware::before {
  content: "\ea59";
}

.mi-hd::before {
  content: "\e052";
}

.mi-hdr-auto::before {
  content: "\f01a";
}

.mi-hdr-auto-select::before {
  content: "\f01b";
}

.mi-hdr-enhanced-select::before {
  content: "\ef51";
}

.mi-hdr-off::before {
  content: "\e3ed";
}

.mi-hdr-off-select::before {
  content: "\f01c";
}

.mi-hdr-on::before {
  content: "\e3ee";
}

.mi-hdr-on-select::before {
  content: "\f01d";
}

.mi-hdr-plus::before {
  content: "\f01e";
}

.mi-hdr-strong::before {
  content: "\e3f1";
}

.mi-hdr-weak::before {
  content: "\e3f2";
}

.mi-headphones::before {
  content: "\f01f";
}

.mi-headphones-battery::before {
  content: "\f020";
}

.mi-headset::before {
  content: "\e310";
}

.mi-headset-mic::before {
  content: "\e311";
}

.mi-headset-off::before {
  content: "\e33a";
}

.mi-healing::before {
  content: "\e3f3";
}

.mi-health-and-safety::before {
  content: "\e1d5";
}

.mi-hearing::before {
  content: "\e023";
}

.mi-hearing-disabled::before {
  content: "\f104";
}

.mi-heart-broken::before {
  content: "\eac2";
}

.mi-heat-pump::before {
  content: "\ec18";
}

.mi-height::before {
  content: "\ea16";
}

.mi-help::before {
  content: "\e887";
}

.mi-help-center::before {
  content: "\f1c0";
}

.mi-help-outline::before {
  content: "\e8fd";
}

.mi-hevc::before {
  content: "\f021";
}

.mi-hexagon::before {
  content: "\eb39";
}

.mi-hide-image::before {
  content: "\f022";
}

.mi-hide-source::before {
  content: "\f023";
}

.mi-high-quality::before {
  content: "\e024";
}

.mi-highlight::before {
  content: "\e25f";
}

.mi-highlight-alt::before {
  content: "\ef52";
}

.mi-highlight-off::before {
  content: "\e888";
}

.mi-highlight-remove::before {
  content: "\e888";
}

.mi-hiking::before {
  content: "\e50a";
}

.mi-history::before {
  content: "\e889";
}

.mi-history-edu::before {
  content: "\ea3e";
}

.mi-history-toggle-off::before {
  content: "\f17d";
}

.mi-hive::before {
  content: "\eaa6";
}

.mi-hls::before {
  content: "\eb8a";
}

.mi-hls-off::before {
  content: "\eb8c";
}

.mi-holiday-village::before {
  content: "\e58a";
}

.mi-home::before {
  content: "\e88a";
}

.mi-home-filled::before {
  content: "\e9b2";
}

.mi-home-max::before {
  content: "\f024";
}

.mi-home-mini::before {
  content: "\f025";
}

.mi-home-repair-service::before {
  content: "\f100";
}

.mi-home-work::before {
  content: "\ea09";
}

.mi-horizontal-distribute::before {
  content: "\e014";
}

.mi-horizontal-rule::before {
  content: "\f108";
}

.mi-horizontal-split::before {
  content: "\e947";
}

.mi-hot-tub::before {
  content: "\eb46";
}

.mi-hotel::before {
  content: "\e53a";
}

.mi-hotel-class::before {
  content: "\e743";
}

.mi-hourglass-bottom::before {
  content: "\ea5c";
}

.mi-hourglass-disabled::before {
  content: "\ef53";
}

.mi-hourglass-empty::before {
  content: "\e88b";
}

.mi-hourglass-full::before {
  content: "\e88c";
}

.mi-hourglass-top::before {
  content: "\ea5b";
}

.mi-house::before {
  content: "\ea44";
}

.mi-house-siding::before {
  content: "\f202";
}

.mi-houseboat::before {
  content: "\e584";
}

.mi-how-to-reg::before {
  content: "\e174";
}

.mi-how-to-vote::before {
  content: "\e175";
}

.mi-html::before {
  content: "\eb7e";
}

.mi-http::before {
  content: "\e902";
}

.mi-https::before {
  content: "\e88d";
}

.mi-hub::before {
  content: "\e9f4";
}

.mi-hvac::before {
  content: "\f10e";
}

.mi-ice-skating::before {
  content: "\e50b";
}

.mi-icecream::before {
  content: "\ea69";
}

.mi-image::before {
  content: "\e3f4";
}

.mi-image-aspect-ratio::before {
  content: "\e3f5";
}

.mi-image-not-supported::before {
  content: "\f116";
}

.mi-image-search::before {
  content: "\e43f";
}

.mi-imagesearch-roller::before {
  content: "\e9b4";
}

.mi-import-contacts::before {
  content: "\e0e0";
}

.mi-import-export::before {
  content: "\e0c3";
}

.mi-important-devices::before {
  content: "\e912";
}

.mi-inbox::before {
  content: "\e156";
}

.mi-incomplete-circle::before {
  content: "\e79b";
}

.mi-indeterminate-check-box::before {
  content: "\e909";
}

.mi-info::before {
  content: "\e88e";
}

.mi-info-outline::before {
  content: "\e88f";
}

.mi-input::before {
  content: "\e890";
}

.mi-insert-chart::before {
  content: "\e24b";
}

.mi-insert-chart-outlined::before {
  content: "\e26a";
}

.mi-insert-comment::before {
  content: "\e24c";
}

.mi-insert-drive-file::before {
  content: "\e24d";
}

.mi-insert-emoticon::before {
  content: "\e24e";
}

.mi-insert-invitation::before {
  content: "\e24f";
}

.mi-insert-link::before {
  content: "\e250";
}

.mi-insert-page-break::before {
  content: "\eaca";
}

.mi-insert-photo::before {
  content: "\e251";
}

.mi-insights::before {
  content: "\f092";
}

.mi-install-desktop::before {
  content: "\eb71";
}

.mi-install-mobile::before {
  content: "\eb72";
}

.mi-integration-instructions::before {
  content: "\ef54";
}

.mi-interests::before {
  content: "\e7c8";
}

.mi-interpreter-mode::before {
  content: "\e83b";
}

.mi-inventory::before {
  content: "\e179";
}

.mi-inventory-2::before {
  content: "\e1a1";
}

.mi-invert-colors::before {
  content: "\e891";
}

.mi-invert-colors-off::before {
  content: "\e0c4";
}

.mi-invert-colors-on::before {
  content: "\e891";
}

.mi-ios-share::before {
  content: "\e6b8";
}

.mi-iron::before {
  content: "\e583";
}

.mi-iso::before {
  content: "\e3f6";
}

.mi-javascript::before {
  content: "\eb7c";
}

.mi-join-full::before {
  content: "\eaeb";
}

.mi-join-inner::before {
  content: "\eaf4";
}

.mi-join-left::before {
  content: "\eaf2";
}

.mi-join-right::before {
  content: "\eaea";
}

.mi-kayaking::before {
  content: "\e50c";
}

.mi-kebab-dining::before {
  content: "\e842";
}

.mi-key::before {
  content: "\e73c";
}

.mi-key-off::before {
  content: "\eb84";
}

.mi-keyboard::before {
  content: "\e312";
}

.mi-keyboard-alt::before {
  content: "\f028";
}

.mi-keyboard-arrow-down::before {
  content: "\e313";
}

.mi-keyboard-arrow-left::before {
  content: "\e314";
}

.mi-keyboard-arrow-right::before {
  content: "\e315";
}

.mi-keyboard-arrow-up::before {
  content: "\e316";
}

.mi-keyboard-backspace::before {
  content: "\e317";
}

.mi-keyboard-capslock::before {
  content: "\e318";
}

.mi-keyboard-command::before {
  content: "\eae0";
}

.mi-keyboard-command-key::before {
  content: "\eae7";
}

.mi-keyboard-control::before {
  content: "\e5d3";
}

.mi-keyboard-control-key::before {
  content: "\eae6";
}

.mi-keyboard-double-arrow-down::before {
  content: "\ead0";
}

.mi-keyboard-double-arrow-left::before {
  content: "\eac3";
}

.mi-keyboard-double-arrow-right::before {
  content: "\eac9";
}

.mi-keyboard-double-arrow-up::before {
  content: "\eacf";
}

.mi-keyboard-hide::before {
  content: "\e31a";
}

.mi-keyboard-option::before {
  content: "\eadf";
}

.mi-keyboard-option-key::before {
  content: "\eae8";
}

.mi-keyboard-return::before {
  content: "\e31b";
}

.mi-keyboard-tab::before {
  content: "\e31c";
}

.mi-keyboard-voice::before {
  content: "\e31d";
}

.mi-king-bed::before {
  content: "\ea45";
}

.mi-kitchen::before {
  content: "\eb47";
}

.mi-kitesurfing::before {
  content: "\e50d";
}

.mi-label::before {
  content: "\e892";
}

.mi-label-important::before {
  content: "\e937";
}

.mi-label-important-outline::before {
  content: "\e948";
}

.mi-label-off::before {
  content: "\e9b6";
}

.mi-label-outline::before {
  content: "\e893";
}

.mi-lan::before {
  content: "\eb2f";
}

.mi-landscape::before {
  content: "\e3f7";
}

.mi-landslide::before {
  content: "\ebd7";
}

.mi-language::before {
  content: "\e894";
}

.mi-laptop::before {
  content: "\e31e";
}

.mi-laptop-chromebook::before {
  content: "\e31f";
}

.mi-laptop-mac::before {
  content: "\e320";
}

.mi-laptop-windows::before {
  content: "\e321";
}

.mi-last-page::before {
  content: "\e5dd";
}

.mi-launch::before {
  content: "\e895";
}

.mi-layers::before {
  content: "\e53b";
}

.mi-layers-clear::before {
  content: "\e53c";
}

.mi-leaderboard::before {
  content: "\f20c";
}

.mi-leak-add::before {
  content: "\e3f8";
}

.mi-leak-remove::before {
  content: "\e3f9";
}

.mi-leave-bags-at-home::before {
  content: "\f21b";
}

.mi-legend-toggle::before {
  content: "\f11b";
}

.mi-lens::before {
  content: "\e3fa";
}

.mi-lens-blur::before {
  content: "\f029";
}

.mi-library-add::before {
  content: "\e02e";
}

.mi-library-add-check::before {
  content: "\e9b7";
}

.mi-library-books::before {
  content: "\e02f";
}

.mi-library-music::before {
  content: "\e030";
}

.mi-light::before {
  content: "\f02a";
}

.mi-light-mode::before {
  content: "\e518";
}

.mi-lightbulb::before {
  content: "\e0f0";
}

.mi-lightbulb-circle::before {
  content: "\ebfe";
}

.mi-lightbulb-outline::before {
  content: "\e90f";
}

.mi-line-axis::before {
  content: "\ea9a";
}

.mi-line-style::before {
  content: "\e919";
}

.mi-line-weight::before {
  content: "\e91a";
}

.mi-linear-scale::before {
  content: "\e260";
}

.mi-link::before {
  content: "\e157";
}

.mi-link-off::before {
  content: "\e16f";
}

.mi-linked-camera::before {
  content: "\e438";
}

.mi-liquor::before {
  content: "\ea60";
}

.mi-list::before {
  content: "\e896";
}

.mi-list-alt::before {
  content: "\e0ee";
}

.mi-live-help::before {
  content: "\e0c6";
}

.mi-live-tv::before {
  content: "\e639";
}

.mi-living::before {
  content: "\f02b";
}

.mi-local-activity::before {
  content: "\e53f";
}

.mi-local-airport::before {
  content: "\e53d";
}

.mi-local-atm::before {
  content: "\e53e";
}

.mi-local-attraction::before {
  content: "\e53f";
}

.mi-local-bar::before {
  content: "\e540";
}

.mi-local-cafe::before {
  content: "\e541";
}

.mi-local-car-wash::before {
  content: "\e542";
}

.mi-local-convenience-store::before {
  content: "\e543";
}

.mi-local-dining::before {
  content: "\e556";
}

.mi-local-drink::before {
  content: "\e544";
}

.mi-local-fire-department::before {
  content: "\ef55";
}

.mi-local-florist::before {
  content: "\e545";
}

.mi-local-gas-station::before {
  content: "\e546";
}

.mi-local-grocery-store::before {
  content: "\e547";
}

.mi-local-hospital::before {
  content: "\e548";
}

.mi-local-hotel::before {
  content: "\e549";
}

.mi-local-laundry-service::before {
  content: "\e54a";
}

.mi-local-library::before {
  content: "\e54b";
}

.mi-local-mall::before {
  content: "\e54c";
}

.mi-local-movies::before {
  content: "\e54d";
}

.mi-local-offer::before {
  content: "\e54e";
}

.mi-local-parking::before {
  content: "\e54f";
}

.mi-local-pharmacy::before {
  content: "\e550";
}

.mi-local-phone::before {
  content: "\e551";
}

.mi-local-pizza::before {
  content: "\e552";
}

.mi-local-play::before {
  content: "\e553";
}

.mi-local-police::before {
  content: "\ef56";
}

.mi-local-post-office::before {
  content: "\e554";
}

.mi-local-print-shop::before {
  content: "\e555";
}

.mi-local-printshop::before {
  content: "\e555";
}

.mi-local-restaurant::before {
  content: "\e556";
}

.mi-local-see::before {
  content: "\e557";
}

.mi-local-shipping::before {
  content: "\e558";
}

.mi-local-taxi::before {
  content: "\e559";
}

.mi-location-city::before {
  content: "\e7f1";
}

.mi-location-disabled::before {
  content: "\e1b6";
}

.mi-location-history::before {
  content: "\e55a";
}

.mi-location-off::before {
  content: "\e0c7";
}

.mi-location-on::before {
  content: "\e0c8";
}

.mi-location-pin::before {
  content: "\f1db";
}

.mi-location-searching::before {
  content: "\e1b7";
}

.mi-lock::before {
  content: "\e897";
}

.mi-lock-clock::before {
  content: "\ef57";
}

.mi-lock-open::before {
  content: "\e898";
}

.mi-lock-outline::before {
  content: "\e899";
}

.mi-lock-person::before {
  content: "\f8f3";
}

.mi-lock-reset::before {
  content: "\eade";
}

.mi-login::before {
  content: "\ea77";
}

.mi-logo-dev::before {
  content: "\ead6";
}

.mi-logout::before {
  content: "\e9ba";
}

.mi-looks::before {
  content: "\e3fc";
}

.mi-looks-3::before {
  content: "\e3fb";
}

.mi-looks-4::before {
  content: "\e3fd";
}

.mi-looks-5::before {
  content: "\e3fe";
}

.mi-looks-6::before {
  content: "\e3ff";
}

.mi-looks-one::before {
  content: "\e400";
}

.mi-looks-two::before {
  content: "\e401";
}

.mi-loop::before {
  content: "\e028";
}

.mi-loupe::before {
  content: "\e402";
}

.mi-low-priority::before {
  content: "\e16d";
}

.mi-loyalty::before {
  content: "\e89a";
}

.mi-lte-mobiledata::before {
  content: "\f02c";
}

.mi-lte-plus-mobiledata::before {
  content: "\f02d";
}

.mi-luggage::before {
  content: "\f235";
}

.mi-lunch-dining::before {
  content: "\ea61";
}

.mi-lyrics::before {
  content: "\ec0b";
}

.mi-macro-off::before {
  content: "\f8d2";
}

.mi-mail::before {
  content: "\e158";
}

.mi-mail-lock::before {
  content: "\ec0a";
}

.mi-mail-outline::before {
  content: "\e0e1";
}

.mi-male::before {
  content: "\e58e";
}

.mi-man::before {
  content: "\e4eb";
}

.mi-man-2::before {
  content: "\f8e1";
}

.mi-man-3::before {
  content: "\f8e2";
}

.mi-man-4::before {
  content: "\f8e3";
}

.mi-manage-accounts::before {
  content: "\f02e";
}

.mi-manage-history::before {
  content: "\ebe7";
}

.mi-manage-search::before {
  content: "\f02f";
}

.mi-map::before {
  content: "\e55b";
}

.mi-maps-home-work::before {
  content: "\f030";
}

.mi-maps-ugc::before {
  content: "\ef58";
}

.mi-margin::before {
  content: "\e9bb";
}

.mi-mark-as-unread::before {
  content: "\e9bc";
}

.mi-mark-chat-read::before {
  content: "\f18b";
}

.mi-mark-chat-unread::before {
  content: "\f189";
}

.mi-mark-email-read::before {
  content: "\f18c";
}

.mi-mark-email-unread::before {
  content: "\f18a";
}

.mi-mark-unread-chat-alt::before {
  content: "\eb9d";
}

.mi-markunread::before {
  content: "\e159";
}

.mi-markunread-mailbox::before {
  content: "\e89b";
}

.mi-masks::before {
  content: "\f218";
}

.mi-maximize::before {
  content: "\e930";
}

.mi-media-bluetooth-off::before {
  content: "\f031";
}

.mi-media-bluetooth-on::before {
  content: "\f032";
}

.mi-mediation::before {
  content: "\efa7";
}

.mi-medical-information::before {
  content: "\ebed";
}

.mi-medical-services::before {
  content: "\f109";
}

.mi-medication::before {
  content: "\f033";
}

.mi-medication-liquid::before {
  content: "\ea87";
}

.mi-meeting-room::before {
  content: "\eb4f";
}

.mi-memory::before {
  content: "\e322";
}

.mi-menu::before {
  content: "\e5d2";
}

.mi-menu-book::before {
  content: "\ea19";
}

.mi-menu-open::before {
  content: "\e9bd";
}

.mi-merge::before {
  content: "\eb98";
}

.mi-merge-type::before {
  content: "\e252";
}

.mi-message::before {
  content: "\e0c9";
}

.mi-messenger::before {
  content: "\e0ca";
}

.mi-messenger-outline::before {
  content: "\e0cb";
}

.mi-mic::before {
  content: "\e029";
}

.mi-mic-external-off::before {
  content: "\ef59";
}

.mi-mic-external-on::before {
  content: "\ef5a";
}

.mi-mic-none::before {
  content: "\e02a";
}

.mi-mic-off::before {
  content: "\e02b";
}

.mi-microwave::before {
  content: "\f204";
}

.mi-military-tech::before {
  content: "\ea3f";
}

.mi-minimize::before {
  content: "\e931";
}

.mi-minor-crash::before {
  content: "\ebf1";
}

.mi-miscellaneous-services::before {
  content: "\f10c";
}

.mi-missed-video-call::before {
  content: "\e073";
}

.mi-mms::before {
  content: "\e618";
}

.mi-mobile-friendly::before {
  content: "\e200";
}

.mi-mobile-off::before {
  content: "\e201";
}

.mi-mobile-screen-share::before {
  content: "\e0e7";
}

.mi-mobiledata-off::before {
  content: "\f034";
}

.mi-mode::before {
  content: "\f097";
}

.mi-mode-comment::before {
  content: "\e253";
}

.mi-mode-edit::before {
  content: "\e254";
}

.mi-mode-edit-outline::before {
  content: "\f035";
}

.mi-mode-fan-off::before {
  content: "\ec17";
}

.mi-mode-night::before {
  content: "\f036";
}

.mi-mode-of-travel::before {
  content: "\e7ce";
}

.mi-mode-standby::before {
  content: "\f037";
}

.mi-model-training::before {
  content: "\f0cf";
}

.mi-monetization-on::before {
  content: "\e263";
}

.mi-money::before {
  content: "\e57d";
}

.mi-money-off::before {
  content: "\e25c";
}

.mi-money-off-csred::before {
  content: "\f038";
}

.mi-monitor::before {
  content: "\ef5b";
}

.mi-monitor-heart::before {
  content: "\eaa2";
}

.mi-monitor-weight::before {
  content: "\f039";
}

.mi-monochrome-photos::before {
  content: "\e403";
}

.mi-mood::before {
  content: "\e7f2";
}

.mi-mood-bad::before {
  content: "\e7f3";
}

.mi-moped::before {
  content: "\eb28";
}

.mi-more::before {
  content: "\e619";
}

.mi-more-horiz::before {
  content: "\e5d3";
}

.mi-more-time::before {
  content: "\ea5d";
}

.mi-more-vert::before {
  content: "\e5d4";
}

.mi-mosque::before {
  content: "\eab2";
}

.mi-motion-photos-auto::before {
  content: "\f03a";
}

.mi-motion-photos-off::before {
  content: "\e9c0";
}

.mi-motion-photos-on::before {
  content: "\e9c1";
}

.mi-motion-photos-pause::before {
  content: "\f227";
}

.mi-motion-photos-paused::before {
  content: "\e9c2";
}

.mi-motorcycle::before {
  content: "\e91b";
}

.mi-mouse::before {
  content: "\e323";
}

.mi-move-down::before {
  content: "\eb61";
}

.mi-move-to-inbox::before {
  content: "\e168";
}

.mi-move-up::before {
  content: "\eb64";
}

.mi-movie::before {
  content: "\e02c";
}

.mi-movie-creation::before {
  content: "\e404";
}

.mi-movie-edit::before {
  content: "\f840";
}

.mi-movie-filter::before {
  content: "\e43a";
}

.mi-moving::before {
  content: "\e501";
}

.mi-mp::before {
  content: "\e9c3";
}

.mi-multiline-chart::before {
  content: "\e6df";
}

.mi-multiple-stop::before {
  content: "\f1b9";
}

.mi-multitrack-audio::before {
  content: "\e1b8";
}

.mi-museum::before {
  content: "\ea36";
}

.mi-music-note::before {
  content: "\e405";
}

.mi-music-off::before {
  content: "\e440";
}

.mi-music-video::before {
  content: "\e063";
}

.mi-my-library-add::before {
  content: "\e02e";
}

.mi-my-library-books::before {
  content: "\e02f";
}

.mi-my-library-music::before {
  content: "\e030";
}

.mi-my-location::before {
  content: "\e55c";
}

.mi-nat::before {
  content: "\ef5c";
}

.mi-nature::before {
  content: "\e406";
}

.mi-nature-people::before {
  content: "\e407";
}

.mi-navigate-before::before {
  content: "\e408";
}

.mi-navigate-next::before {
  content: "\e409";
}

.mi-navigation::before {
  content: "\e55d";
}

.mi-near-me::before {
  content: "\e569";
}

.mi-near-me-disabled::before {
  content: "\f1ef";
}

.mi-nearby-error::before {
  content: "\f03b";
}

.mi-nearby-off::before {
  content: "\f03c";
}

.mi-nest-cam-wired-stand::before {
  content: "\ec16";
}

.mi-network-cell::before {
  content: "\e1b9";
}

.mi-network-check::before {
  content: "\e640";
}

.mi-network-locked::before {
  content: "\e61a";
}

.mi-network-ping::before {
  content: "\ebca";
}

.mi-network-wifi::before {
  content: "\e1ba";
}

.mi-network-wifi-1-bar::before {
  content: "\ebe4";
}

.mi-network-wifi-2-bar::before {
  content: "\ebd6";
}

.mi-network-wifi-3-bar::before {
  content: "\ebe1";
}

.mi-new-label::before {
  content: "\e609";
}

.mi-new-releases::before {
  content: "\e031";
}

.mi-newspaper::before {
  content: "\eb81";
}

.mi-next-plan::before {
  content: "\ef5d";
}

.mi-next-week::before {
  content: "\e16a";
}

.mi-nfc::before {
  content: "\e1bb";
}

.mi-night-shelter::before {
  content: "\f1f1";
}

.mi-nightlife::before {
  content: "\ea62";
}

.mi-nightlight::before {
  content: "\f03d";
}

.mi-nightlight-round::before {
  content: "\ef5e";
}

.mi-nights-stay::before {
  content: "\ea46";
}

.mi-no-accounts::before {
  content: "\f03e";
}

.mi-no-adult-content::before {
  content: "\f8fe";
}

.mi-no-backpack::before {
  content: "\f237";
}

.mi-no-cell::before {
  content: "\f1a4";
}

.mi-no-crash::before {
  content: "\ebf0";
}

.mi-no-drinks::before {
  content: "\f1a5";
}

.mi-no-encryption::before {
  content: "\e641";
}

.mi-no-encryption-gmailerrorred::before {
  content: "\f03f";
}

.mi-no-flash::before {
  content: "\f1a6";
}

.mi-no-food::before {
  content: "\f1a7";
}

.mi-no-luggage::before {
  content: "\f23b";
}

.mi-no-meals::before {
  content: "\f1d6";
}

.mi-no-meals-ouline::before {
  content: "\f229";
}

.mi-no-meeting-room::before {
  content: "\eb4e";
}

.mi-no-photography::before {
  content: "\f1a8";
}

.mi-no-sim::before {
  content: "\e0cc";
}

.mi-no-stroller::before {
  content: "\f1af";
}

.mi-no-transfer::before {
  content: "\f1d5";
}

.mi-noise-aware::before {
  content: "\ebec";
}

.mi-noise-control-off::before {
  content: "\ebf3";
}

.mi-nordic-walking::before {
  content: "\e50e";
}

.mi-north::before {
  content: "\f1e0";
}

.mi-north-east::before {
  content: "\f1e1";
}

.mi-north-west::before {
  content: "\f1e2";
}

.mi-not-accessible::before {
  content: "\f0fe";
}

.mi-not-interested::before {
  content: "\e033";
}

.mi-not-listed-location::before {
  content: "\e575";
}

.mi-not-started::before {
  content: "\f0d1";
}

.mi-note::before {
  content: "\e06f";
}

.mi-note-add::before {
  content: "\e89c";
}

.mi-note-alt::before {
  content: "\f040";
}

.mi-notes::before {
  content: "\e26c";
}

.mi-notification-add::before {
  content: "\e399";
}

.mi-notification-important::before {
  content: "\e004";
}

.mi-notifications::before {
  content: "\e7f4";
}

.mi-notifications-active::before {
  content: "\e7f7";
}

.mi-notifications-none::before {
  content: "\e7f5";
}

.mi-notifications-off::before {
  content: "\e7f6";
}

.mi-notifications-on::before {
  content: "\e7f7";
}

.mi-notifications-paused::before {
  content: "\e7f8";
}

.mi-now-wallpaper::before {
  content: "\e1bc";
}

.mi-now-widgets::before {
  content: "\e1bd";
}

.mi-numbers::before {
  content: "\eac7";
}

.mi-offline-bolt::before {
  content: "\e932";
}

.mi-offline-pin::before {
  content: "\e90a";
}

.mi-offline-share::before {
  content: "\e9c5";
}

.mi-oil-barrel::before {
  content: "\ec15";
}

.mi-on-device-training::before {
  content: "\ebfd";
}

.mi-ondemand-video::before {
  content: "\e63a";
}

.mi-online-prediction::before {
  content: "\f0eb";
}

.mi-opacity::before {
  content: "\e91c";
}

.mi-open-in-browser::before {
  content: "\e89d";
}

.mi-open-in-full::before {
  content: "\f1ce";
}

.mi-open-in-new::before {
  content: "\e89e";
}

.mi-open-in-new-off::before {
  content: "\e4f6";
}

.mi-open-with::before {
  content: "\e89f";
}

.mi-other-houses::before {
  content: "\e58c";
}

.mi-outbond::before {
  content: "\f228";
}

.mi-outbound::before {
  content: "\e1ca";
}

.mi-outbox::before {
  content: "\ef5f";
}

.mi-outdoor-grill::before {
  content: "\ea47";
}

.mi-outgoing-mail::before {
  content: "\f0d2";
}

.mi-outlet::before {
  content: "\f1d4";
}

.mi-outlined-flag::before {
  content: "\e16e";
}

.mi-output::before {
  content: "\ebbe";
}

.mi-padding::before {
  content: "\e9c8";
}

.mi-pages::before {
  content: "\e7f9";
}

.mi-pageview::before {
  content: "\e8a0";
}

.mi-paid::before {
  content: "\f041";
}

.mi-palette::before {
  content: "\e40a";
}

.mi-pallet::before {
  content: "\f86a";
}

.mi-pan-tool::before {
  content: "\e925";
}

.mi-pan-tool-alt::before {
  content: "\ebb9";
}

.mi-panorama::before {
  content: "\e40b";
}

.mi-panorama-fish-eye::before {
  content: "\e40c";
}

.mi-panorama-fisheye::before {
  content: "\e40c";
}

.mi-panorama-horizontal::before {
  content: "\e40d";
}

.mi-panorama-horizontal-select::before {
  content: "\ef60";
}

.mi-panorama-photosphere::before {
  content: "\e9c9";
}

.mi-panorama-photosphere-select::before {
  content: "\e9ca";
}

.mi-panorama-vertical::before {
  content: "\e40e";
}

.mi-panorama-vertical-select::before {
  content: "\ef61";
}

.mi-panorama-wide-angle::before {
  content: "\e40f";
}

.mi-panorama-wide-angle-select::before {
  content: "\ef62";
}

.mi-paragliding::before {
  content: "\e50f";
}

.mi-park::before {
  content: "\ea63";
}

.mi-party-mode::before {
  content: "\e7fa";
}

.mi-password::before {
  content: "\f042";
}

.mi-paste::before {
  content: "\f098";
}

.mi-pattern::before {
  content: "\f043";
}

.mi-pause::before {
  content: "\e034";
}

.mi-pause-circle::before {
  content: "\e1a2";
}

.mi-pause-circle-filled::before {
  content: "\e035";
}

.mi-pause-circle-outline::before {
  content: "\e036";
}

.mi-pause-presentation::before {
  content: "\e0ea";
}

.mi-payment::before {
  content: "\e8a1";
}

.mi-payments::before {
  content: "\ef63";
}

.mi-paypal::before {
  content: "\ea8d";
}

.mi-pedal-bike::before {
  content: "\eb29";
}

.mi-pending::before {
  content: "\ef64";
}

.mi-pending-actions::before {
  content: "\f1bb";
}

.mi-pentagon::before {
  content: "\eb50";
}

.mi-people::before {
  content: "\e7fb";
}

.mi-people-alt::before {
  content: "\ea21";
}

.mi-people-outline::before {
  content: "\e7fc";
}

.mi-percent::before {
  content: "\eb58";
}

.mi-perm-camera-mic::before {
  content: "\e8a2";
}

.mi-perm-contact-cal::before {
  content: "\e8a3";
}

.mi-perm-contact-calendar::before {
  content: "\e8a3";
}

.mi-perm-data-setting::before {
  content: "\e8a4";
}

.mi-perm-device-info::before {
  content: "\e8a5";
}

.mi-perm-device-information::before {
  content: "\e8a5";
}

.mi-perm-identity::before {
  content: "\e8a6";
}

.mi-perm-media::before {
  content: "\e8a7";
}

.mi-perm-phone-msg::before {
  content: "\e8a8";
}

.mi-perm-scan-wifi::before {
  content: "\e8a9";
}

.mi-person::before {
  content: "\e7fd";
}

.mi-person-2::before {
  content: "\f8e4";
}

.mi-person-3::before {
  content: "\f8e5";
}

.mi-person-4::before {
  content: "\f8e6";
}

.mi-person-add::before {
  content: "\e7fe";
}

.mi-person-add-alt::before {
  content: "\ea4d";
}

.mi-person-add-alt-1::before {
  content: "\ef65";
}

.mi-person-add-disabled::before {
  content: "\e9cb";
}

.mi-person-off::before {
  content: "\e510";
}

.mi-person-outline::before {
  content: "\e7ff";
}

.mi-person-pin::before {
  content: "\e55a";
}

.mi-person-pin-circle::before {
  content: "\e56a";
}

.mi-person-remove::before {
  content: "\ef66";
}

.mi-person-remove-alt-1::before {
  content: "\ef67";
}

.mi-person-search::before {
  content: "\f106";
}

.mi-personal-injury::before {
  content: "\e6da";
}

.mi-personal-video::before {
  content: "\e63b";
}

.mi-pest-control::before {
  content: "\f0fa";
}

.mi-pest-control-rodent::before {
  content: "\f0fd";
}

.mi-pets::before {
  content: "\e91d";
}

.mi-phishing::before {
  content: "\ead7";
}

.mi-phone::before {
  content: "\e0cd";
}

.mi-phone-android::before {
  content: "\e324";
}

.mi-phone-bluetooth-speaker::before {
  content: "\e61b";
}

.mi-phone-callback::before {
  content: "\e649";
}

.mi-phone-disabled::before {
  content: "\e9cc";
}

.mi-phone-enabled::before {
  content: "\e9cd";
}

.mi-phone-forwarded::before {
  content: "\e61c";
}

.mi-phone-in-talk::before {
  content: "\e61d";
}

.mi-phone-iphone::before {
  content: "\e325";
}

.mi-phone-locked::before {
  content: "\e61e";
}

.mi-phone-missed::before {
  content: "\e61f";
}

.mi-phone-paused::before {
  content: "\e620";
}

.mi-phonelink::before {
  content: "\e326";
}

.mi-phonelink-erase::before {
  content: "\e0db";
}

.mi-phonelink-lock::before {
  content: "\e0dc";
}

.mi-phonelink-off::before {
  content: "\e327";
}

.mi-phonelink-ring::before {
  content: "\e0dd";
}

.mi-phonelink-setup::before {
  content: "\e0de";
}

.mi-photo::before {
  content: "\e410";
}

.mi-photo-album::before {
  content: "\e411";
}

.mi-photo-camera::before {
  content: "\e412";
}

.mi-photo-camera-back::before {
  content: "\ef68";
}

.mi-photo-camera-front::before {
  content: "\ef69";
}

.mi-photo-filter::before {
  content: "\e43b";
}

.mi-photo-library::before {
  content: "\e413";
}

.mi-photo-size-select-actual::before {
  content: "\e432";
}

.mi-photo-size-select-large::before {
  content: "\e433";
}

.mi-photo-size-select-small::before {
  content: "\e434";
}

.mi-php::before {
  content: "\eb8f";
}

.mi-piano::before {
  content: "\e521";
}

.mi-piano-off::before {
  content: "\e520";
}

.mi-picture-as-pdf::before {
  content: "\e415";
}

.mi-picture-in-picture::before {
  content: "\e8aa";
}

.mi-picture-in-picture-alt::before {
  content: "\e911";
}

.mi-pie-chart::before {
  content: "\e6c4";
}

.mi-pie-chart-outline::before {
  content: "\f044";
}

.mi-pie-chart-outlined::before {
  content: "\e6c5";
}

.mi-pin::before {
  content: "\f045";
}

.mi-pin-drop::before {
  content: "\e55e";
}

.mi-pin-end::before {
  content: "\e767";
}

.mi-pin-invoke::before {
  content: "\e763";
}

.mi-pinch::before {
  content: "\eb38";
}

.mi-pivot-table-chart::before {
  content: "\e9ce";
}

.mi-pix::before {
  content: "\eaa3";
}

.mi-place::before {
  content: "\e55f";
}

.mi-plagiarism::before {
  content: "\ea5a";
}

.mi-play-arrow::before {
  content: "\e037";
}

.mi-play-circle::before {
  content: "\e1c4";
}

.mi-play-circle-fill::before {
  content: "\e038";
}

.mi-play-circle-filled::before {
  content: "\e038";
}

.mi-play-circle-outline::before {
  content: "\e039";
}

.mi-play-disabled::before {
  content: "\ef6a";
}

.mi-play-for-work::before {
  content: "\e906";
}

.mi-play-lesson::before {
  content: "\f047";
}

.mi-playlist-add::before {
  content: "\e03b";
}

.mi-playlist-add-check::before {
  content: "\e065";
}

.mi-playlist-add-check-circle::before {
  content: "\e7e6";
}

.mi-playlist-add-circle::before {
  content: "\e7e5";
}

.mi-playlist-play::before {
  content: "\e05f";
}

.mi-playlist-remove::before {
  content: "\eb80";
}

.mi-plumbing::before {
  content: "\f107";
}

.mi-plus-one::before {
  content: "\e800";
}

.mi-podcasts::before {
  content: "\f048";
}

.mi-point-of-sale::before {
  content: "\f17e";
}

.mi-policy::before {
  content: "\ea17";
}

.mi-poll::before {
  content: "\e801";
}

.mi-polyline::before {
  content: "\ebbb";
}

.mi-polymer::before {
  content: "\e8ab";
}

.mi-pool::before {
  content: "\eb48";
}

.mi-portable-wifi-off::before {
  content: "\e0ce";
}

.mi-portrait::before {
  content: "\e416";
}

.mi-post-add::before {
  content: "\ea20";
}

.mi-power::before {
  content: "\e63c";
}

.mi-power-input::before {
  content: "\e336";
}

.mi-power-off::before {
  content: "\e646";
}

.mi-power-settings-new::before {
  content: "\e8ac";
}

.mi-precision-manufacturing::before {
  content: "\f049";
}

.mi-pregnant-woman::before {
  content: "\e91e";
}

.mi-present-to-all::before {
  content: "\e0df";
}

.mi-preview::before {
  content: "\f1c5";
}

.mi-price-change::before {
  content: "\f04a";
}

.mi-price-check::before {
  content: "\f04b";
}

.mi-print::before {
  content: "\e8ad";
}

.mi-print-disabled::before {
  content: "\e9cf";
}

.mi-priority-high::before {
  content: "\e645";
}

.mi-privacy-tip::before {
  content: "\f0dc";
}

.mi-private-connectivity::before {
  content: "\e744";
}

.mi-production-quantity-limits::before {
  content: "\e1d1";
}

.mi-propane::before {
  content: "\ec14";
}

.mi-propane-tank::before {
  content: "\ec13";
}

.mi-psychology::before {
  content: "\ea4a";
}

.mi-psychology-alt::before {
  content: "\f8ea";
}

.mi-public::before {
  content: "\e80b";
}

.mi-public-off::before {
  content: "\f1ca";
}

.mi-publish::before {
  content: "\e255";
}

.mi-published-with-changes::before {
  content: "\f232";
}

.mi-punch-clock::before {
  content: "\eaa8";
}

.mi-push-pin::before {
  content: "\f10d";
}

.mi-qr-code::before {
  content: "\ef6b";
}

.mi-qr-code-2::before {
  content: "\e00a";
}

.mi-qr-code-scanner::before {
  content: "\f206";
}

.mi-query-builder::before {
  content: "\e8ae";
}

.mi-query-stats::before {
  content: "\e4fc";
}

.mi-question-answer::before {
  content: "\e8af";
}

.mi-question-mark::before {
  content: "\eb8b";
}

.mi-queue::before {
  content: "\e03c";
}

.mi-queue-music::before {
  content: "\e03d";
}

.mi-queue-play-next::before {
  content: "\e066";
}

.mi-quick-contacts-dialer::before {
  content: "\e0cf";
}

.mi-quick-contacts-mail::before {
  content: "\e0d0";
}

.mi-quickreply::before {
  content: "\ef6c";
}

.mi-quiz::before {
  content: "\f04c";
}

.mi-quora::before {
  content: "\ea98";
}

.mi-r-mobiledata::before {
  content: "\f04d";
}

.mi-radar::before {
  content: "\f04e";
}

.mi-radio::before {
  content: "\e03e";
}

.mi-radio-button-checked::before {
  content: "\e837";
}

.mi-radio-button-off::before {
  content: "\e836";
}

.mi-radio-button-on::before {
  content: "\e837";
}

.mi-radio-button-unchecked::before {
  content: "\e836";
}

.mi-railway-alert::before {
  content: "\e9d1";
}

.mi-ramen-dining::before {
  content: "\ea64";
}

.mi-ramp-left::before {
  content: "\eb9c";
}

.mi-ramp-right::before {
  content: "\eb96";
}

.mi-rate-review::before {
  content: "\e560";
}

.mi-raw-off::before {
  content: "\f04f";
}

.mi-raw-on::before {
  content: "\f050";
}

.mi-read-more::before {
  content: "\ef6d";
}

.mi-real-estate-agent::before {
  content: "\e73a";
}

.mi-rebase-edit::before {
  content: "\f846";
}

.mi-receipt::before {
  content: "\e8b0";
}

.mi-receipt-long::before {
  content: "\ef6e";
}

.mi-recent-actors::before {
  content: "\e03f";
}

.mi-recommend::before {
  content: "\e9d2";
}

.mi-record-voice-over::before {
  content: "\e91f";
}

.mi-rectangle::before {
  content: "\eb54";
}

.mi-recycling::before {
  content: "\e760";
}

.mi-reddit::before {
  content: "\eaa0";
}

.mi-redeem::before {
  content: "\e8b1";
}

.mi-redo::before {
  content: "\e15a";
}

.mi-reduce-capacity::before {
  content: "\f21c";
}

.mi-refresh::before {
  content: "\e5d5";
}

.mi-remember-me::before {
  content: "\f051";
}

.mi-remove::before {
  content: "\e15b";
}

.mi-remove-circle::before {
  content: "\e15c";
}

.mi-remove-circle-outline::before {
  content: "\e15d";
}

.mi-remove-done::before {
  content: "\e9d3";
}

.mi-remove-from-queue::before {
  content: "\e067";
}

.mi-remove-moderator::before {
  content: "\e9d4";
}

.mi-remove-red-eye::before {
  content: "\e417";
}

.mi-remove-road::before {
  content: "\ebfc";
}

.mi-remove-shopping-cart::before {
  content: "\e928";
}

.mi-reorder::before {
  content: "\e8fe";
}

.mi-repartition::before {
  content: "\f8e8";
}

.mi-repeat::before {
  content: "\e040";
}

.mi-repeat-on::before {
  content: "\e9d6";
}

.mi-repeat-one::before {
  content: "\e041";
}

.mi-repeat-one-on::before {
  content: "\e9d7";
}

.mi-replay::before {
  content: "\e042";
}

.mi-replay-10::before {
  content: "\e059";
}

.mi-replay-30::before {
  content: "\e05a";
}

.mi-replay-5::before {
  content: "\e05b";
}

.mi-replay-circle-filled::before {
  content: "\e9d8";
}

.mi-reply::before {
  content: "\e15e";
}

.mi-reply-all::before {
  content: "\e15f";
}

.mi-report::before {
  content: "\e160";
}

.mi-report-gmailerrorred::before {
  content: "\f052";
}

.mi-report-off::before {
  content: "\e170";
}

.mi-report-problem::before {
  content: "\e8b2";
}

.mi-request-page::before {
  content: "\f22c";
}

.mi-request-quote::before {
  content: "\f1b6";
}

.mi-reset-tv::before {
  content: "\e9d9";
}

.mi-restart-alt::before {
  content: "\f053";
}

.mi-restaurant::before {
  content: "\e56c";
}

.mi-restaurant-menu::before {
  content: "\e561";
}

.mi-restore::before {
  content: "\e8b3";
}

.mi-restore-from-trash::before {
  content: "\e938";
}

.mi-restore-page::before {
  content: "\e929";
}

.mi-reviews::before {
  content: "\f054";
}

.mi-rice-bowl::before {
  content: "\f1f5";
}

.mi-ring-volume::before {
  content: "\e0d1";
}

.mi-rocket::before {
  content: "\eba5";
}

.mi-rocket-launch::before {
  content: "\eb9b";
}

.mi-roller-shades::before {
  content: "\ec12";
}

.mi-roller-shades-closed::before {
  content: "\ec11";
}

.mi-roller-skating::before {
  content: "\ebcd";
}

.mi-roofing::before {
  content: "\f201";
}

.mi-room::before {
  content: "\e8b4";
}

.mi-room-preferences::before {
  content: "\f1b8";
}

.mi-room-service::before {
  content: "\eb49";
}

.mi-rotate-90-degrees-ccw::before {
  content: "\e418";
}

.mi-rotate-90-degrees-cw::before {
  content: "\eaab";
}

.mi-rotate-left::before {
  content: "\e419";
}

.mi-rotate-right::before {
  content: "\e41a";
}

.mi-roundabout-left::before {
  content: "\eb99";
}

.mi-roundabout-right::before {
  content: "\eba3";
}

.mi-rounded-corner::before {
  content: "\e920";
}

.mi-route::before {
  content: "\eacd";
}

.mi-router::before {
  content: "\e328";
}

.mi-rowing::before {
  content: "\e921";
}

.mi-rss-feed::before {
  content: "\e0e5";
}

.mi-rsvp::before {
  content: "\f055";
}

.mi-rtt::before {
  content: "\e9ad";
}

.mi-rule::before {
  content: "\f1c2";
}

.mi-rule-folder::before {
  content: "\f1c9";
}

.mi-run-circle::before {
  content: "\ef6f";
}

.mi-running-with-errors::before {
  content: "\e51d";
}

.mi-rv-hookup::before {
  content: "\e642";
}

.mi-safety-check::before {
  content: "\ebef";
}

.mi-safety-divider::before {
  content: "\e1cc";
}

.mi-sailing::before {
  content: "\e502";
}

.mi-sanitizer::before {
  content: "\f21d";
}

.mi-satellite::before {
  content: "\e562";
}

.mi-satellite-alt::before {
  content: "\eb3a";
}

.mi-save::before {
  content: "\e161";
}

.mi-save-alt::before {
  content: "\e171";
}

.mi-save-as::before {
  content: "\eb60";
}

.mi-saved-search::before {
  content: "\ea11";
}

.mi-savings::before {
  content: "\e2eb";
}

.mi-scale::before {
  content: "\eb5f";
}

.mi-scanner::before {
  content: "\e329";
}

.mi-scatter-plot::before {
  content: "\e268";
}

.mi-schedule::before {
  content: "\e8b5";
}

.mi-schedule-send::before {
  content: "\ea0a";
}

.mi-schema::before {
  content: "\e4fd";
}

.mi-school::before {
  content: "\e80c";
}

.mi-science::before {
  content: "\ea4b";
}

.mi-score::before {
  content: "\e269";
}

.mi-scoreboard::before {
  content: "\ebd0";
}

.mi-screen-lock-landscape::before {
  content: "\e1be";
}

.mi-screen-lock-portrait::before {
  content: "\e1bf";
}

.mi-screen-lock-rotation::before {
  content: "\e1c0";
}

.mi-screen-rotation::before {
  content: "\e1c1";
}

.mi-screen-rotation-alt::before {
  content: "\ebee";
}

.mi-screen-search-desktop::before {
  content: "\ef70";
}

.mi-screen-share::before {
  content: "\e0e2";
}

.mi-screenshot::before {
  content: "\f056";
}

.mi-screenshot-monitor::before {
  content: "\ec08";
}

.mi-scuba-diving::before {
  content: "\ebce";
}

.mi-sd::before {
  content: "\e9dd";
}

.mi-sd-card::before {
  content: "\e623";
}

.mi-sd-card-alert::before {
  content: "\f057";
}

.mi-sd-storage::before {
  content: "\e1c2";
}

.mi-search::before {
  content: "\e8b6";
}

.mi-search-off::before {
  content: "\ea76";
}

.mi-security::before {
  content: "\e32a";
}

.mi-security-update::before {
  content: "\f058";
}

.mi-security-update-good::before {
  content: "\f059";
}

.mi-security-update-warning::before {
  content: "\f05a";
}

.mi-segment::before {
  content: "\e94b";
}

.mi-select-all::before {
  content: "\e162";
}

.mi-self-improvement::before {
  content: "\ea78";
}

.mi-sell::before {
  content: "\f05b";
}

.mi-send::before {
  content: "\e163";
}

.mi-send-and-archive::before {
  content: "\ea0c";
}

.mi-send-time-extension::before {
  content: "\eadb";
}

.mi-send-to-mobile::before {
  content: "\f05c";
}

.mi-sensor-door::before {
  content: "\f1b5";
}

.mi-sensor-occupied::before {
  content: "\ec10";
}

.mi-sensor-window::before {
  content: "\f1b4";
}

.mi-sensors::before {
  content: "\e51e";
}

.mi-sensors-off::before {
  content: "\e51f";
}

.mi-sentiment-dissatisfied::before {
  content: "\e811";
}

.mi-sentiment-neutral::before {
  content: "\e812";
}

.mi-sentiment-satisfied::before {
  content: "\e813";
}

.mi-sentiment-satisfied-alt::before {
  content: "\e0ed";
}

.mi-sentiment-very-dissatisfied::before {
  content: "\e814";
}

.mi-sentiment-very-satisfied::before {
  content: "\e815";
}

.mi-set-meal::before {
  content: "\f1ea";
}

.mi-settings::before {
  content: "\e8b8";
}

.mi-settings-accessibility::before {
  content: "\f05d";
}

.mi-settings-applications::before {
  content: "\e8b9";
}

.mi-settings-backup-restore::before {
  content: "\e8ba";
}

.mi-settings-bluetooth::before {
  content: "\e8bb";
}

.mi-settings-brightness::before {
  content: "\e8bd";
}

.mi-settings-cell::before {
  content: "\e8bc";
}

.mi-settings-display::before {
  content: "\e8bd";
}

.mi-settings-ethernet::before {
  content: "\e8be";
}

.mi-settings-input-antenna::before {
  content: "\e8bf";
}

.mi-settings-input-component::before {
  content: "\e8c0";
}

.mi-settings-input-composite::before {
  content: "\e8c1";
}

.mi-settings-input-hdmi::before {
  content: "\e8c2";
}

.mi-settings-input-svideo::before {
  content: "\e8c3";
}

.mi-settings-overscan::before {
  content: "\e8c4";
}

.mi-settings-phone::before {
  content: "\e8c5";
}

.mi-settings-power::before {
  content: "\e8c6";
}

.mi-settings-remote::before {
  content: "\e8c7";
}

.mi-settings-suggest::before {
  content: "\f05e";
}

.mi-settings-system-daydream::before {
  content: "\e1c3";
}

.mi-settings-voice::before {
  content: "\e8c8";
}

.mi-severe-cold::before {
  content: "\ebd3";
}

.mi-shape-line::before {
  content: "\f8d3";
}

.mi-share::before {
  content: "\e80d";
}

.mi-share-arrival-time::before {
  content: "\e524";
}

.mi-share-location::before {
  content: "\f05f";
}

.mi-shelves::before {
  content: "\f86e";
}

.mi-shield::before {
  content: "\e9e0";
}

.mi-shield-moon::before {
  content: "\eaa9";
}

.mi-shop::before {
  content: "\e8c9";
}

.mi-shop-2::before {
  content: "\e19e";
}

.mi-shop-two::before {
  content: "\e8ca";
}

.mi-shopify::before {
  content: "\ea9d";
}

.mi-shopping-bag::before {
  content: "\f1cc";
}

.mi-shopping-basket::before {
  content: "\e8cb";
}

.mi-shopping-cart::before {
  content: "\e8cc";
}

.mi-shopping-cart-checkout::before {
  content: "\eb88";
}

.mi-short-text::before {
  content: "\e261";
}

.mi-shortcut::before {
  content: "\f060";
}

.mi-show-chart::before {
  content: "\e6e1";
}

.mi-shower::before {
  content: "\f061";
}

.mi-shuffle::before {
  content: "\e043";
}

.mi-shuffle-on::before {
  content: "\e9e1";
}

.mi-shutter-speed::before {
  content: "\e43d";
}

.mi-sick::before {
  content: "\f220";
}

.mi-sign-language::before {
  content: "\ebe5";
}

.mi-signal-cellular-0-bar::before {
  content: "\f0a8";
}

.mi-signal-cellular-1-bar::before {
  content: "\f0a9";
}

.mi-signal-cellular-2-bar::before {
  content: "\f0aa";
}

.mi-signal-cellular-3-bar::before {
  content: "\f0ab";
}

.mi-signal-cellular-4-bar::before {
  content: "\e1c8";
}

.mi-signal-cellular-alt::before {
  content: "\e202";
}

.mi-signal-cellular-alt-1-bar::before {
  content: "\ebdf";
}

.mi-signal-cellular-alt-2-bar::before {
  content: "\ebe3";
}

.mi-signal-cellular-connected-no-internet-0-bar::before {
  content: "\f0ac";
}

.mi-signal-cellular-connected-no-internet-1-bar::before {
  content: "\f0ad";
}

.mi-signal-cellular-connected-no-internet-2-bar::before {
  content: "\f0ae";
}

.mi-signal-cellular-connected-no-internet-3-bar::before {
  content: "\f0af";
}

.mi-signal-cellular-connected-no-internet-4-bar::before {
  content: "\e1cd";
}

.mi-signal-cellular-no-sim::before {
  content: "\e1ce";
}

.mi-signal-cellular-nodata::before {
  content: "\f062";
}

.mi-signal-cellular-null::before {
  content: "\e1cf";
}

.mi-signal-cellular-off::before {
  content: "\e1d0";
}

.mi-signal-wifi-0-bar::before {
  content: "\f0b0";
}

.mi-signal-wifi-1-bar::before {
  content: "\f0b1";
}

.mi-signal-wifi-1-bar-lock::before {
  content: "\f0b2";
}

.mi-signal-wifi-2-bar::before {
  content: "\f0b3";
}

.mi-signal-wifi-2-bar-lock::before {
  content: "\f0b4";
}

.mi-signal-wifi-3-bar::before {
  content: "\f0b5";
}

.mi-signal-wifi-3-bar-lock::before {
  content: "\f0b6";
}

.mi-signal-wifi-4-bar::before {
  content: "\e1d8";
}

.mi-signal-wifi-4-bar-lock::before {
  content: "\e1d9";
}

.mi-signal-wifi-bad::before {
  content: "\f063";
}

.mi-signal-wifi-connected-no-internet-0::before {
  content: "\f0f2";
}

.mi-signal-wifi-connected-no-internet-1::before {
  content: "\f0ee";
}

.mi-signal-wifi-connected-no-internet-2::before {
  content: "\f0f1";
}

.mi-signal-wifi-connected-no-internet-3::before {
  content: "\f0ed";
}

.mi-signal-wifi-connected-no-internet-4::before {
  content: "\f064";
}

.mi-signal-wifi-off::before {
  content: "\e1da";
}

.mi-signal-wifi-statusbar-1-bar::before {
  content: "\f0e6";
}

.mi-signal-wifi-statusbar-2-bar::before {
  content: "\f0f0";
}

.mi-signal-wifi-statusbar-3-bar::before {
  content: "\f0ea";
}

.mi-signal-wifi-statusbar-4-bar::before {
  content: "\f065";
}

.mi-signal-wifi-statusbar-connected-no-internet::before {
  content: "\f0f8";
}

.mi-signal-wifi-statusbar-connected-no-internet-1::before {
  content: "\f0e9";
}

.mi-signal-wifi-statusbar-connected-no-internet-2::before {
  content: "\f0f7";
}

.mi-signal-wifi-statusbar-connected-no-internet-3::before {
  content: "\f0e8";
}

.mi-signal-wifi-statusbar-connected-no-internet-4::before {
  content: "\f066";
}

.mi-signal-wifi-statusbar-not-connected::before {
  content: "\f0ef";
}

.mi-signal-wifi-statusbar-null::before {
  content: "\f067";
}

.mi-signpost::before {
  content: "\eb91";
}

.mi-sim-card::before {
  content: "\e32b";
}

.mi-sim-card-alert::before {
  content: "\e624";
}

.mi-sim-card-download::before {
  content: "\f068";
}

.mi-single-bed::before {
  content: "\ea48";
}

.mi-sip::before {
  content: "\f069";
}

.mi-skateboarding::before {
  content: "\e511";
}

.mi-skip-next::before {
  content: "\e044";
}

.mi-skip-previous::before {
  content: "\e045";
}

.mi-sledding::before {
  content: "\e512";
}

.mi-slideshow::before {
  content: "\e41b";
}

.mi-slow-motion-video::before {
  content: "\e068";
}

.mi-smart-button::before {
  content: "\f1c1";
}

.mi-smart-display::before {
  content: "\f06a";
}

.mi-smart-screen::before {
  content: "\f06b";
}

.mi-smart-toy::before {
  content: "\f06c";
}

.mi-smartphone::before {
  content: "\e32c";
}

.mi-smoke-free::before {
  content: "\eb4a";
}

.mi-smoking-rooms::before {
  content: "\eb4b";
}

.mi-sms::before {
  content: "\e625";
}

.mi-sms-failed::before {
  content: "\e626";
}

.mi-snapchat::before {
  content: "\ea6e";
}

.mi-snippet-folder::before {
  content: "\f1c7";
}

.mi-snooze::before {
  content: "\e046";
}

.mi-snowboarding::before {
  content: "\e513";
}

.mi-snowing::before {
  content: "\e80f";
}

.mi-snowmobile::before {
  content: "\e503";
}

.mi-snowshoeing::before {
  content: "\e514";
}

.mi-soap::before {
  content: "\f1b2";
}

.mi-social-distance::before {
  content: "\e1cb";
}

.mi-solar-power::before {
  content: "\ec0f";
}

.mi-sort::before {
  content: "\e164";
}

.mi-sort-by-alpha::before {
  content: "\e053";
}

.mi-sos::before {
  content: "\ebf7";
}

.mi-soup-kitchen::before {
  content: "\e7d3";
}

.mi-source::before {
  content: "\f1c4";
}

.mi-south::before {
  content: "\f1e3";
}

.mi-south-america::before {
  content: "\e7e4";
}

.mi-south-east::before {
  content: "\f1e4";
}

.mi-south-west::before {
  content: "\f1e5";
}

.mi-spa::before {
  content: "\eb4c";
}

.mi-space-bar::before {
  content: "\e256";
}

.mi-space-dashboard::before {
  content: "\e66b";
}

.mi-spatial-audio::before {
  content: "\ebeb";
}

.mi-spatial-audio-off::before {
  content: "\ebe8";
}

.mi-spatial-tracking::before {
  content: "\ebea";
}

.mi-speaker::before {
  content: "\e32d";
}

.mi-speaker-group::before {
  content: "\e32e";
}

.mi-speaker-notes::before {
  content: "\e8cd";
}

.mi-speaker-notes-off::before {
  content: "\e92a";
}

.mi-speaker-phone::before {
  content: "\e0d2";
}

.mi-speed::before {
  content: "\e9e4";
}

.mi-spellcheck::before {
  content: "\e8ce";
}

.mi-splitscreen::before {
  content: "\f06d";
}

.mi-spoke::before {
  content: "\e9a7";
}

.mi-sports::before {
  content: "\ea30";
}

.mi-sports-bar::before {
  content: "\f1f3";
}

.mi-sports-baseball::before {
  content: "\ea51";
}

.mi-sports-basketball::before {
  content: "\ea26";
}

.mi-sports-cricket::before {
  content: "\ea27";
}

.mi-sports-esports::before {
  content: "\ea28";
}

.mi-sports-football::before {
  content: "\ea29";
}

.mi-sports-golf::before {
  content: "\ea2a";
}

.mi-sports-gymnastics::before {
  content: "\ebc4";
}

.mi-sports-handball::before {
  content: "\ea33";
}

.mi-sports-hockey::before {
  content: "\ea2b";
}

.mi-sports-kabaddi::before {
  content: "\ea34";
}

.mi-sports-martial-arts::before {
  content: "\eae9";
}

.mi-sports-mma::before {
  content: "\ea2c";
}

.mi-sports-motorsports::before {
  content: "\ea2d";
}

.mi-sports-rugby::before {
  content: "\ea2e";
}

.mi-sports-score::before {
  content: "\f06e";
}

.mi-sports-soccer::before {
  content: "\ea2f";
}

.mi-sports-tennis::before {
  content: "\ea32";
}

.mi-sports-volleyball::before {
  content: "\ea31";
}

.mi-square::before {
  content: "\eb36";
}

.mi-square-foot::before {
  content: "\ea49";
}

.mi-ssid-chart::before {
  content: "\eb66";
}

.mi-stacked-bar-chart::before {
  content: "\e9e6";
}

.mi-stacked-line-chart::before {
  content: "\f22b";
}

.mi-stadium::before {
  content: "\eb90";
}

.mi-stairs::before {
  content: "\f1a9";
}

.mi-star::before {
  content: "\e838";
}

.mi-star-border::before {
  content: "\e83a";
}

.mi-star-border-purple500::before {
  content: "\f099";
}

.mi-star-half::before {
  content: "\e839";
}

.mi-star-outline::before {
  content: "\f06f";
}

.mi-star-purple500::before {
  content: "\f09a";
}

.mi-star-rate::before {
  content: "\f0ec";
}

.mi-stars::before {
  content: "\e8d0";
}

.mi-start::before {
  content: "\e089";
}

.mi-stay-current-landscape::before {
  content: "\e0d3";
}

.mi-stay-current-portrait::before {
  content: "\e0d4";
}

.mi-stay-primary-landscape::before {
  content: "\e0d5";
}

.mi-stay-primary-portrait::before {
  content: "\e0d6";
}

.mi-sticky-note-2::before {
  content: "\f1fc";
}

.mi-stop::before {
  content: "\e047";
}

.mi-stop-circle::before {
  content: "\ef71";
}

.mi-stop-screen-share::before {
  content: "\e0e3";
}

.mi-storage::before {
  content: "\e1db";
}

.mi-store::before {
  content: "\e8d1";
}

.mi-store-mall-directory::before {
  content: "\e563";
}

.mi-storefront::before {
  content: "\ea12";
}

.mi-storm::before {
  content: "\f070";
}

.mi-straight::before {
  content: "\eb95";
}

.mi-straighten::before {
  content: "\e41c";
}

.mi-stream::before {
  content: "\e9e9";
}

.mi-streetview::before {
  content: "\e56e";
}

.mi-strikethrough-s::before {
  content: "\e257";
}

.mi-stroller::before {
  content: "\f1ae";
}

.mi-style::before {
  content: "\e41d";
}

.mi-subdirectory-arrow-left::before {
  content: "\e5d9";
}

.mi-subdirectory-arrow-right::before {
  content: "\e5da";
}

.mi-subject::before {
  content: "\e8d2";
}

.mi-subscript::before {
  content: "\f111";
}

.mi-subscriptions::before {
  content: "\e064";
}

.mi-subtitles::before {
  content: "\e048";
}

.mi-subtitles-off::before {
  content: "\ef72";
}

.mi-subway::before {
  content: "\e56f";
}

.mi-summarize::before {
  content: "\f071";
}

.mi-sunny::before {
  content: "\e81a";
}

.mi-sunny-snowing::before {
  content: "\e819";
}

.mi-superscript::before {
  content: "\f112";
}

.mi-supervised-user-circle::before {
  content: "\e939";
}

.mi-supervisor-account::before {
  content: "\e8d3";
}

.mi-support::before {
  content: "\ef73";
}

.mi-support-agent::before {
  content: "\f0e2";
}

.mi-surfing::before {
  content: "\e515";
}

.mi-surround-sound::before {
  content: "\e049";
}

.mi-swap-calls::before {
  content: "\e0d7";
}

.mi-swap-horiz::before {
  content: "\e8d4";
}

.mi-swap-horizontal-circle::before {
  content: "\e933";
}

.mi-swap-vert::before {
  content: "\e8d5";
}

.mi-swap-vert-circle::before {
  content: "\e8d6";
}

.mi-swap-vertical-circle::before {
  content: "\e8d6";
}

.mi-swipe::before {
  content: "\e9ec";
}

.mi-swipe-down::before {
  content: "\eb53";
}

.mi-swipe-down-alt::before {
  content: "\eb30";
}

.mi-swipe-left::before {
  content: "\eb59";
}

.mi-swipe-left-alt::before {
  content: "\eb33";
}

.mi-swipe-right::before {
  content: "\eb52";
}

.mi-swipe-right-alt::before {
  content: "\eb56";
}

.mi-swipe-up::before {
  content: "\eb2e";
}

.mi-swipe-up-alt::before {
  content: "\eb35";
}

.mi-swipe-vertical::before {
  content: "\eb51";
}

.mi-switch-access-shortcut::before {
  content: "\e7e1";
}

.mi-switch-access-shortcut-add::before {
  content: "\e7e2";
}

.mi-switch-account::before {
  content: "\e9ed";
}

.mi-switch-camera::before {
  content: "\e41e";
}

.mi-switch-left::before {
  content: "\f1d1";
}

.mi-switch-right::before {
  content: "\f1d2";
}

.mi-switch-video::before {
  content: "\e41f";
}

.mi-synagogue::before {
  content: "\eab0";
}

.mi-sync::before {
  content: "\e627";
}

.mi-sync-alt::before {
  content: "\ea18";
}

.mi-sync-disabled::before {
  content: "\e628";
}

.mi-sync-lock::before {
  content: "\eaee";
}

.mi-sync-problem::before {
  content: "\e629";
}

.mi-system-security-update::before {
  content: "\f072";
}

.mi-system-security-update-good::before {
  content: "\f073";
}

.mi-system-security-update-warning::before {
  content: "\f074";
}

.mi-system-update::before {
  content: "\e62a";
}

.mi-system-update-alt::before {
  content: "\e8d7";
}

.mi-system-update-tv::before {
  content: "\e8d7";
}

.mi-tab::before {
  content: "\e8d8";
}

.mi-tab-unselected::before {
  content: "\e8d9";
}

.mi-table-bar::before {
  content: "\ead2";
}

.mi-table-chart::before {
  content: "\e265";
}

.mi-table-restaurant::before {
  content: "\eac6";
}

.mi-table-rows::before {
  content: "\f101";
}

.mi-table-view::before {
  content: "\f1be";
}

.mi-tablet::before {
  content: "\e32f";
}

.mi-tablet-android::before {
  content: "\e330";
}

.mi-tablet-mac::before {
  content: "\e331";
}

.mi-tag::before {
  content: "\e9ef";
}

.mi-tag-faces::before {
  content: "\e420";
}

.mi-takeout-dining::before {
  content: "\ea74";
}

.mi-tap-and-play::before {
  content: "\e62b";
}

.mi-tapas::before {
  content: "\f1e9";
}

.mi-task::before {
  content: "\f075";
}

.mi-task-alt::before {
  content: "\e2e6";
}

.mi-taxi-alert::before {
  content: "\ef74";
}

.mi-telegram::before {
  content: "\ea6b";
}

.mi-temple-buddhist::before {
  content: "\eab3";
}

.mi-temple-hindu::before {
  content: "\eaaf";
}

.mi-terminal::before {
  content: "\eb8e";
}

.mi-terrain::before {
  content: "\e564";
}

.mi-text-decrease::before {
  content: "\eadd";
}

.mi-text-fields::before {
  content: "\e262";
}

.mi-text-format::before {
  content: "\e165";
}

.mi-text-increase::before {
  content: "\eae2";
}

.mi-text-rotate-up::before {
  content: "\e93a";
}

.mi-text-rotate-vertical::before {
  content: "\e93b";
}

.mi-text-rotation-angledown::before {
  content: "\e93c";
}

.mi-text-rotation-angleup::before {
  content: "\e93d";
}

.mi-text-rotation-down::before {
  content: "\e93e";
}

.mi-text-rotation-none::before {
  content: "\e93f";
}

.mi-text-snippet::before {
  content: "\f1c6";
}

.mi-textsms::before {
  content: "\e0d8";
}

.mi-texture::before {
  content: "\e421";
}

.mi-theater-comedy::before {
  content: "\ea66";
}

.mi-theaters::before {
  content: "\e8da";
}

.mi-thermostat::before {
  content: "\f076";
}

.mi-thermostat-auto::before {
  content: "\f077";
}

.mi-thumb-down::before {
  content: "\e8db";
}

.mi-thumb-down-alt::before {
  content: "\e816";
}

.mi-thumb-down-off-alt::before {
  content: "\e9f2";
}

.mi-thumb-up::before {
  content: "\e8dc";
}

.mi-thumb-up-alt::before {
  content: "\e817";
}

.mi-thumb-up-off-alt::before {
  content: "\e9f3";
}

.mi-thumbs-up-down::before {
  content: "\e8dd";
}

.mi-thunderstorm::before {
  content: "\ebdb";
}

.mi-tiktok::before {
  content: "\ea7e";
}

.mi-time-to-leave::before {
  content: "\e62c";
}

.mi-timelapse::before {
  content: "\e422";
}

.mi-timeline::before {
  content: "\e922";
}

.mi-timer::before {
  content: "\e425";
}

.mi-timer-10::before {
  content: "\e423";
}

.mi-timer-10-select::before {
  content: "\f07a";
}

.mi-timer-3::before {
  content: "\e424";
}

.mi-timer-3-select::before {
  content: "\f07b";
}

.mi-timer-off::before {
  content: "\e426";
}

.mi-tips-and-updates::before {
  content: "\e79a";
}

.mi-tire-repair::before {
  content: "\ebc8";
}

.mi-title::before {
  content: "\e264";
}

.mi-toc::before {
  content: "\e8de";
}

.mi-today::before {
  content: "\e8df";
}

.mi-toggle-off::before {
  content: "\e9f5";
}

.mi-toggle-on::before {
  content: "\e9f6";
}

.mi-token::before {
  content: "\ea25";
}

.mi-toll::before {
  content: "\e8e0";
}

.mi-tonality::before {
  content: "\e427";
}

.mi-topic::before {
  content: "\f1c8";
}

.mi-tornado::before {
  content: "\e199";
}

.mi-touch-app::before {
  content: "\e913";
}

.mi-tour::before {
  content: "\ef75";
}

.mi-toys::before {
  content: "\e332";
}

.mi-track-changes::before {
  content: "\e8e1";
}

.mi-traffic::before {
  content: "\e565";
}

.mi-train::before {
  content: "\e570";
}

.mi-tram::before {
  content: "\e571";
}

.mi-transcribe::before {
  content: "\f8ec";
}

.mi-transfer-within-a-station::before {
  content: "\e572";
}

.mi-transform::before {
  content: "\e428";
}

.mi-transgender::before {
  content: "\e58d";
}

.mi-transit-enterexit::before {
  content: "\e579";
}

.mi-translate::before {
  content: "\e8e2";
}

.mi-travel-explore::before {
  content: "\e2db";
}

.mi-trending-down::before {
  content: "\e8e3";
}

.mi-trending-flat::before {
  content: "\e8e4";
}

.mi-trending-neutral::before {
  content: "\e8e4";
}

.mi-trending-up::before {
  content: "\e8e5";
}

.mi-trip-origin::before {
  content: "\e57b";
}

.mi-trolley::before {
  content: "\f86b";
}

.mi-troubleshoot::before {
  content: "\e1d2";
}

.mi-try::before {
  content: "\f07c";
}

.mi-tsunami::before {
  content: "\ebd8";
}

.mi-tty::before {
  content: "\f1aa";
}

.mi-tune::before {
  content: "\e429";
}

.mi-tungsten::before {
  content: "\f07d";
}

.mi-turn-left::before {
  content: "\eba6";
}

.mi-turn-right::before {
  content: "\ebab";
}

.mi-turn-sharp-left::before {
  content: "\eba7";
}

.mi-turn-sharp-right::before {
  content: "\ebaa";
}

.mi-turn-slight-left::before {
  content: "\eba4";
}

.mi-turn-slight-right::before {
  content: "\eb9a";
}

.mi-turned-in::before {
  content: "\e8e6";
}

.mi-turned-in-not::before {
  content: "\e8e7";
}

.mi-tv::before {
  content: "\e333";
}

.mi-tv-off::before {
  content: "\e647";
}

.mi-two-wheeler::before {
  content: "\e9f9";
}

.mi-type-specimen::before {
  content: "\f8f0";
}

.mi-u-turn-left::before {
  content: "\eba1";
}

.mi-u-turn-right::before {
  content: "\eba2";
}

.mi-umbrella::before {
  content: "\f1ad";
}

.mi-unarchive::before {
  content: "\e169";
}

.mi-undo::before {
  content: "\e166";
}

.mi-unfold-less::before {
  content: "\e5d6";
}

.mi-unfold-less-double::before {
  content: "\f8cf";
}

.mi-unfold-more::before {
  content: "\e5d7";
}

.mi-unfold-more-double::before {
  content: "\f8d0";
}

.mi-unpublished::before {
  content: "\f236";
}

.mi-unsubscribe::before {
  content: "\e0eb";
}

.mi-upcoming::before {
  content: "\f07e";
}

.mi-update::before {
  content: "\e923";
}

.mi-update-disabled::before {
  content: "\e075";
}

.mi-upgrade::before {
  content: "\f0fb";
}

.mi-upload::before {
  content: "\f09b";
}

.mi-upload-file::before {
  content: "\e9fc";
}

.mi-usb::before {
  content: "\e1e0";
}

.mi-usb-off::before {
  content: "\e4fa";
}

.mi-vaccines::before {
  content: "\e138";
}

.mi-vape-free::before {
  content: "\ebc6";
}

.mi-vaping-rooms::before {
  content: "\ebcf";
}

.mi-verified::before {
  content: "\ef76";
}

.mi-verified-user::before {
  content: "\e8e8";
}

.mi-vertical-align-bottom::before {
  content: "\e258";
}

.mi-vertical-align-center::before {
  content: "\e259";
}

.mi-vertical-align-top::before {
  content: "\e25a";
}

.mi-vertical-distribute::before {
  content: "\e076";
}

.mi-vertical-shades::before {
  content: "\ec0e";
}

.mi-vertical-shades-closed::before {
  content: "\ec0d";
}

.mi-vertical-split::before {
  content: "\e949";
}

.mi-vibration::before {
  content: "\e62d";
}

.mi-video-call::before {
  content: "\e070";
}

.mi-video-camera-back::before {
  content: "\f07f";
}

.mi-video-camera-front::before {
  content: "\f080";
}

.mi-video-chat::before {
  content: "\f8a0";
}

.mi-video-collection::before {
  content: "\e04a";
}

.mi-video-file::before {
  content: "\eb87";
}

.mi-video-label::before {
  content: "\e071";
}

.mi-video-library::before {
  content: "\e04a";
}

.mi-video-settings::before {
  content: "\ea75";
}

.mi-video-stable::before {
  content: "\f081";
}

.mi-videocam::before {
  content: "\e04b";
}

.mi-videocam-off::before {
  content: "\e04c";
}

.mi-videogame-asset::before {
  content: "\e338";
}

.mi-videogame-asset-off::before {
  content: "\e500";
}

.mi-view-agenda::before {
  content: "\e8e9";
}

.mi-view-array::before {
  content: "\e8ea";
}

.mi-view-carousel::before {
  content: "\e8eb";
}

.mi-view-column::before {
  content: "\e8ec";
}

.mi-view-comfortable::before {
  content: "\e42a";
}

.mi-view-comfy::before {
  content: "\e42a";
}

.mi-view-comfy-alt::before {
  content: "\eb73";
}

.mi-view-compact::before {
  content: "\e42b";
}

.mi-view-compact-alt::before {
  content: "\eb74";
}

.mi-view-cozy::before {
  content: "\eb75";
}

.mi-view-day::before {
  content: "\e8ed";
}

.mi-view-headline::before {
  content: "\e8ee";
}

.mi-view-in-ar::before {
  content: "\e9fe";
}

.mi-view-kanban::before {
  content: "\eb7f";
}

.mi-view-list::before {
  content: "\e8ef";
}

.mi-view-module::before {
  content: "\e8f0";
}

.mi-view-quilt::before {
  content: "\e8f1";
}

.mi-view-sidebar::before {
  content: "\f114";
}

.mi-view-stream::before {
  content: "\e8f2";
}

.mi-view-timeline::before {
  content: "\eb85";
}

.mi-view-week::before {
  content: "\e8f3";
}

.mi-vignette::before {
  content: "\e435";
}

.mi-villa::before {
  content: "\e586";
}

.mi-visibility::before {
  content: "\e8f4";
}

.mi-visibility-off::before {
  content: "\e8f5";
}

.mi-voice-chat::before {
  content: "\e62e";
}

.mi-voice-over-off::before {
  content: "\e94a";
}

.mi-voicemail::before {
  content: "\e0d9";
}

.mi-volcano::before {
  content: "\ebda";
}

.mi-volume-down::before {
  content: "\e04d";
}

.mi-volume-down-alt::before {
  content: "\e79c";
}

.mi-volume-mute::before {
  content: "\e04e";
}

.mi-volume-off::before {
  content: "\e04f";
}

.mi-volume-up::before {
  content: "\e050";
}

.mi-volunteer-activism::before {
  content: "\ea70";
}

.mi-vpn-key::before {
  content: "\e0da";
}

.mi-vpn-key-off::before {
  content: "\eb7a";
}

.mi-vpn-lock::before {
  content: "\e62f";
}

.mi-vrpano::before {
  content: "\f082";
}

.mi-wallet::before {
  content: "\f8ff";
}

.mi-wallet-giftcard::before {
  content: "\e8f6";
}

.mi-wallet-membership::before {
  content: "\e8f7";
}

.mi-wallet-travel::before {
  content: "\e8f8";
}

.mi-wallpaper::before {
  content: "\e1bc";
}

.mi-warehouse::before {
  content: "\ebb8";
}

.mi-warning::before {
  content: "\e002";
}

.mi-warning-amber::before {
  content: "\f083";
}

.mi-wash::before {
  content: "\f1b1";
}

.mi-watch::before {
  content: "\e334";
}

.mi-watch-later::before {
  content: "\e924";
}

.mi-watch-off::before {
  content: "\eae3";
}

.mi-water::before {
  content: "\f084";
}

.mi-water-damage::before {
  content: "\f203";
}

.mi-water-drop::before {
  content: "\e798";
}

.mi-waterfall-chart::before {
  content: "\ea00";
}

.mi-waves::before {
  content: "\e176";
}

.mi-waving-hand::before {
  content: "\e766";
}

.mi-wb-auto::before {
  content: "\e42c";
}

.mi-wb-cloudy::before {
  content: "\e42d";
}

.mi-wb-incandescent::before {
  content: "\e42e";
}

.mi-wb-iridescent::before {
  content: "\e436";
}

.mi-wb-shade::before {
  content: "\ea01";
}

.mi-wb-sunny::before {
  content: "\e430";
}

.mi-wb-twighlight::before {
  content: "\ea02";
}

.mi-wb-twilight::before {
  content: "\e1c6";
}

.mi-wc::before {
  content: "\e63d";
}

.mi-web::before {
  content: "\e051";
}

.mi-web-asset::before {
  content: "\e069";
}

.mi-web-asset-off::before {
  content: "\e4f7";
}

.mi-web-stories::before {
  content: "\e595";
}

.mi-webhook::before {
  content: "\eb92";
}

.mi-wechat::before {
  content: "\ea81";
}

.mi-weekend::before {
  content: "\e16b";
}

.mi-west::before {
  content: "\f1e6";
}

.mi-whatshot::before {
  content: "\e80e";
}

.mi-wheelchair-pickup::before {
  content: "\f1ab";
}

.mi-where-to-vote::before {
  content: "\e177";
}

.mi-widgets::before {
  content: "\e1bd";
}

.mi-width-full::before {
  content: "\f8f5";
}

.mi-width-normal::before {
  content: "\f8f6";
}

.mi-width-wide::before {
  content: "\f8f7";
}

.mi-wifi::before {
  content: "\e63e";
}

.mi-wifi-1-bar::before {
  content: "\e4ca";
}

.mi-wifi-2-bar::before {
  content: "\e4d9";
}

.mi-wifi-calling::before {
  content: "\ef77";
}

.mi-wifi-calling-1::before {
  content: "\f0e7";
}

.mi-wifi-calling-2::before {
  content: "\f0f6";
}

.mi-wifi-calling-3::before {
  content: "\f085";
}

.mi-wifi-channel::before {
  content: "\eb6a";
}

.mi-wifi-find::before {
  content: "\eb31";
}

.mi-wifi-lock::before {
  content: "\e1e1";
}

.mi-wifi-off::before {
  content: "\e648";
}

.mi-wifi-password::before {
  content: "\eb6b";
}

.mi-wifi-protected-setup::before {
  content: "\f0fc";
}

.mi-wifi-tethering::before {
  content: "\e1e2";
}

.mi-wifi-tethering-error::before {
  content: "\ead9";
}

.mi-wifi-tethering-error-rounded::before {
  content: "\f086";
}

.mi-wifi-tethering-off::before {
  content: "\f087";
}

.mi-wind-power::before {
  content: "\ec0c";
}

.mi-window::before {
  content: "\f088";
}

.mi-wine-bar::before {
  content: "\f1e8";
}

.mi-woman::before {
  content: "\e13e";
}

.mi-woman-2::before {
  content: "\f8e7";
}

.mi-woo-commerce::before {
  content: "\ea6d";
}

.mi-wordpress::before {
  content: "\ea9f";
}

.mi-work::before {
  content: "\e8f9";
}

.mi-work-history::before {
  content: "\ec09";
}

.mi-work-off::before {
  content: "\e942";
}

.mi-work-outline::before {
  content: "\e943";
}

.mi-workspace-premium::before {
  content: "\e7af";
}

.mi-workspaces::before {
  content: "\e1a0";
}

.mi-workspaces-filled::before {
  content: "\ea0d";
}

.mi-workspaces-outline::before {
  content: "\ea0f";
}

.mi-wrap-text::before {
  content: "\e25b";
}

.mi-wrong-location::before {
  content: "\ef78";
}

.mi-wysiwyg::before {
  content: "\f1c3";
}

.mi-yard::before {
  content: "\f089";
}

.mi-youtube-searched-for::before {
  content: "\e8fa";
}

.mi-zoom-in::before {
  content: "\e8ff";
}

.mi-zoom-in-map::before {
  content: "\eb2d";
}

.mi-zoom-out::before {
  content: "\e900";
}

.mi-zoom-out-map::before {
  content: "\e56b";
}
