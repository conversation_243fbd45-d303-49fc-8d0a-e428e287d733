/**
 * @description 面试房间演示数据集成
 * 将演示数据系统集成到面试房间页面中
 */

import { interviewRoomHelper } from './roomHelper'
import { createMockWebSocket } from './roomInitializer'
import * as InterviewRoomAPI from '@/service/interview-room'

/**
 * 集成演示数据到面试房间页面
 * 替换原有的API调用，确保在API请求失败时能够使用演示数据
 */
export const integrateInterviewRoomDemoData = () => {
  // 初始化面试房间演示数据
  const isDemoMode = interviewRoomHelper.init()
  console.log('[InterviewRoomIntegration] 演示数据集成初始化完成，演示模式:', isDemoMode)

  // 替换原有的API调用
  patchInterviewRoomAPI()

  return {
    isDemoMode,
    helper: interviewRoomHelper,
  }
}

/**
 * 替换原有的API调用
 * 使用演示数据系统提供的API调用，确保在API请求失败时能够使用演示数据
 */
const patchInterviewRoomAPI = () => {
  // 保存原始API函数
  const originalAPI = { ...InterviewRoomAPI }

  // 替换getSessionInfo函数
  InterviewRoomAPI.getSessionInfo = async (sessionId: string) => {
    try {
      // 尝试调用原始API
      return await originalAPI.getSessionInfo(sessionId)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] getSessionInfo API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.getSessionInfo({ sessionId })
      return demoData
    }
  }

  // 替换getQuestion函数
  InterviewRoomAPI.getQuestion = async (sessionId: string, index?: number) => {
    try {
      // 尝试调用原始API
      return await originalAPI.getQuestion(sessionId, index)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] getQuestion API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.getQuestion(index || 1)
      return demoData
    }
  }

  // 替换submitAnswer函数
  InterviewRoomAPI.submitAnswer = async (data: InterviewRoomAPI.SubmitAnswerRequest) => {
    try {
      // 尝试调用原始API
      return await originalAPI.submitAnswer(data)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] submitAnswer API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.submitAnswer(data)
      return demoData
    }
  }

  // 替换endInterview函数
  InterviewRoomAPI.endInterview = async (data: InterviewRoomAPI.EndInterviewRequest) => {
    try {
      // 尝试调用原始API
      return await originalAPI.endInterview(data)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] endInterview API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.endInterview(data)
      return demoData
    }
  }

  // 替换submitFeedback函数
  InterviewRoomAPI.submitFeedback = async (data: InterviewRoomAPI.FeedbackRequest) => {
    try {
      // 尝试调用原始API
      return await originalAPI.submitFeedback(data)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] submitFeedback API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.submitFeedback(data)
      return demoData
    }
  }

  // 替换checkDevices函数
  InterviewRoomAPI.checkDevices = async () => {
    try {
      // 尝试调用原始API
      return await originalAPI.checkDevices()
    } catch (error) {
      console.warn('[InterviewRoomIntegration] checkDevices API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.checkDevices()
      return demoData
    }
  }

  // 替换getSessionStatus函数
  InterviewRoomAPI.getSessionStatus = async (sessionId: string) => {
    try {
      // 尝试调用原始API
      return await originalAPI.getSessionStatus(sessionId)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] getSessionStatus API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.getSessionStatus({ sessionId })
      return demoData
    }
  }

  // 替换comprehensiveAnalysis函数
  InterviewRoomAPI.comprehensiveAnalysis = async (
    sessionId: string,
    audioFile?: File,
    videoFile?: File,
    textContent?: string,
    jobPosition?: string,
  ) => {
    try {
      // 尝试调用原始API
      return await originalAPI.comprehensiveAnalysis(
        sessionId,
        audioFile,
        videoFile,
        textContent,
        jobPosition,
      )
    } catch (error) {
      console.warn(
        '[InterviewRoomIntegration] comprehensiveAnalysis API请求失败，使用演示数据',
        error,
      )

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.comprehensiveAnalysis({
        sessionId,
        audio: audioFile ? 'audio-data' : undefined,
        video: videoFile ? 'video-data' : undefined,
        text: textContent,
        jobPosition,
      })
      return demoData
    }
  }

  // 替换analyzeAudio函数
  InterviewRoomAPI.analyzeAudio = async (audioFile: File, sessionId: string) => {
    try {
      // 尝试调用原始API
      return await originalAPI.analyzeAudio(audioFile, sessionId)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] analyzeAudio API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.analyzeAudio({
        sessionId,
        audio: 'audio-data',
      })
      return demoData
    }
  }

  // 替换analyzeVideo函数
  InterviewRoomAPI.analyzeVideo = async (videoFile: File, sessionId: string) => {
    try {
      // 尝试调用原始API
      return await originalAPI.analyzeVideo(videoFile, sessionId)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] analyzeVideo API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.analyzeVideo({
        sessionId,
        video: 'video-data',
      })
      return demoData
    }
  }

  // 替换analyzeText函数
  InterviewRoomAPI.analyzeText = async (
    textContent: string,
    sessionId: string,
    jobPosition?: string,
  ) => {
    try {
      // 尝试调用原始API
      return await originalAPI.analyzeText(textContent, sessionId, jobPosition)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] analyzeText API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.analyzeText({
        sessionId,
        text: textContent,
        jobPosition,
      })
      return demoData
    }
  }

  // 替换getAnalysisHistory函数
  InterviewRoomAPI.getAnalysisHistory = async (sessionId: string) => {
    try {
      // 尝试调用原始API
      return await originalAPI.getAnalysisHistory(sessionId)
    } catch (error) {
      console.warn('[InterviewRoomIntegration] getAnalysisHistory API请求失败，使用演示数据', error)

      // 如果API请求失败，则使用演示数据
      const demoData = await interviewRoomHelper.getAnalysisHistory({ sessionId })
      return demoData
    }
  }

  // 替换createAudioStreamAnalysis函数
  const originalCreateAudioStreamAnalysis = InterviewRoomAPI.createAudioStreamAnalysis
  InterviewRoomAPI.createAudioStreamAnalysis = (
    sessionId: string,
    satoken: string,
    onProgress?: (progress: number, stage: string) => void,
    onComplete?: (result: any) => void,
    onError?: (error: string) => void,
  ) => {
    try {
      // 尝试调用原始API
      const eventSource = originalCreateAudioStreamAnalysis(
        sessionId,
        satoken,
        onProgress,
        onComplete,
        onError,
      )

      // 添加错误处理
      eventSource.addEventListener('error', (event) => {
        console.warn('[InterviewRoomIntegration] 音频流分析连接错误，使用演示数据')

        // 关闭原始连接
        eventSource.close()

        // 创建模拟WebSocket连接
        const mockWs = createMockWebSocket('wss://demo-audio-analysis.example.com')

        // 模拟进度事件
        let progress = 0
        const progressInterval = setInterval(() => {
          progress += 10
          if (progress <= 100) {
            onProgress?.(progress, `处理中 ${progress}%`)
          } else {
            clearInterval(progressInterval)

            // 模拟完成事件
            interviewRoomHelper
              .analyzeAudio({ sessionId })
              .then((result) => {
                onComplete?.(result.data)
              })
              .catch((error) => {
                onError?.('演示数据生成失败')
              })
          }
        }, 500)

        // 返回一个模拟的EventSource对象
        return {
          close: () => {
            clearInterval(progressInterval)
            mockWs.close()
          },
        } as any
      })

      return eventSource
    } catch (error) {
      console.warn(
        '[InterviewRoomIntegration] createAudioStreamAnalysis API请求失败，使用演示数据',
        error,
      )

      // 如果API请求失败，则使用演示数据
      // 创建模拟WebSocket连接
      const mockWs = createMockWebSocket('wss://demo-audio-analysis.example.com')

      // 模拟进度事件
      let progress = 0
      const progressInterval = setInterval(() => {
        progress += 10
        if (progress <= 100) {
          onProgress?.(progress, `处理中 ${progress}%`)
        } else {
          clearInterval(progressInterval)

          // 模拟完成事件
          interviewRoomHelper
            .analyzeAudio({ sessionId })
            .then((result) => {
              onComplete?.(result.data)
            })
            .catch((error) => {
              onError?.('演示数据生成失败')
            })
        }
      }, 500)

      // 返回一个模拟的EventSource对象
      return {
        close: () => {
          clearInterval(progressInterval)
          mockWs.close()
        },
      } as any
    }
  }

  // 替换createVideoStreamAnalysis函数
  const originalCreateVideoStreamAnalysis = InterviewRoomAPI.createVideoStreamAnalysis
  InterviewRoomAPI.createVideoStreamAnalysis = (
    sessionId: string,
    satoken: string,
    onProgress?: (progress: number, stage: string) => void,
    onComplete?: (result: any) => void,
    onError?: (error: string) => void,
  ) => {
    try {
      // 尝试调用原始API
      const eventSource = originalCreateVideoStreamAnalysis(
        sessionId,
        satoken,
        onProgress,
        onComplete,
        onError,
      )

      // 添加错误处理
      eventSource.addEventListener('error', (event) => {
        console.warn('[InterviewRoomIntegration] 视频流分析连接错误，使用演示数据')

        // 关闭原始连接
        eventSource.close()

        // 创建模拟WebSocket连接
        const mockWs = createMockWebSocket('wss://demo-video-analysis.example.com')

        // 模拟进度事件
        let progress = 0
        const progressInterval = setInterval(() => {
          progress += 10
          if (progress <= 100) {
            onProgress?.(progress, `处理中 ${progress}%`)
          } else {
            clearInterval(progressInterval)

            // 模拟完成事件
            interviewRoomHelper
              .analyzeVideo({ sessionId })
              .then((result) => {
                onComplete?.(result.data)
              })
              .catch((error) => {
                onError?.('演示数据生成失败')
              })
          }
        }, 500)

        // 返回一个模拟的EventSource对象
        return {
          close: () => {
            clearInterval(progressInterval)
            mockWs.close()
          },
        } as any
      })

      return eventSource
    } catch (error) {
      console.warn(
        '[InterviewRoomIntegration] createVideoStreamAnalysis API请求失败，使用演示数据',
        error,
      )

      // 如果API请求失败，则使用演示数据
      // 创建模拟WebSocket连接
      const mockWs = createMockWebSocket('wss://demo-video-analysis.example.com')

      // 模拟进度事件
      let progress = 0
      const progressInterval = setInterval(() => {
        progress += 10
        if (progress <= 100) {
          onProgress?.(progress, `处理中 ${progress}%`)
        } else {
          clearInterval(progressInterval)

          // 模拟完成事件
          interviewRoomHelper
            .analyzeVideo({ sessionId })
            .then((result) => {
              onComplete?.(result.data)
            })
            .catch((error) => {
              onError?.('演示数据生成失败')
            })
        }
      }, 500)

      // 返回一个模拟的EventSource对象
      return {
        close: () => {
          clearInterval(progressInterval)
          mockWs.close()
        },
      } as any
    }
  }

  console.log('[InterviewRoomIntegration] API函数替换完成')
}

/**
 * 清理演示数据集成
 * 在页面卸载时调用，清理资源
 */
export const cleanupInterviewRoomDemoData = () => {
  // 关闭所有WebSocket连接
  interviewRoomHelper.closeAllWebSockets()

  console.log('[InterviewRoomIntegration] 演示数据集成清理完成')
}

export default {
  integrateInterviewRoomDemoData,
  cleanupInterviewRoomDemoData,
}
