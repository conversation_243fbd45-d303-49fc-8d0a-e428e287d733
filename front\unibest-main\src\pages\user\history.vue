<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import CategoryFilter from '@/components/CategoryFilter.vue'
// @ts-ignore
import { getHistoryRecords, getStatistics, getMoreHistoryRecords } from '@/service/history'
// @ts-ignore
import type { InterviewRecord, Statistics, PaginationParams } from '@/service/history'

// 响应式数据
const loading = ref(false)
const historyList = ref<InterviewRecord[]>([])
const statistics = ref<Statistics>({
  totalInterviews: 0,
  averageScore: 0,
  monthlyImprovement: 0,
  improvementPercent: 0,
  currentLevel: '初级',
  nextLevelProgress: 0,
})

// 分页参数
const pagination = ref<PaginationParams>({
  page: 1,
  pageSize: 20,
})

// 排序字段和顺序
const sortField = ref<'date' | 'totalScore'>('date')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 排序选择器状态
const showSortModal = ref(false)

// 筛选选项 - 适配CategoryFilter组件格式
const categoryOptions = ref([
  { key: 'all', name: '全部', icon: 'i-mdi-view-list' },
  { key: 'ai', name: 'AI算法', icon: 'i-mdi-robot' },
  { key: 'data', name: '大数据', icon: 'i-mdi-chart-line' },
  { key: 'iot', name: '物联网', icon: 'i-mdi-wifi' },
  { key: 'system', name: '系统架构', icon: 'i-mdi-cog' },
])

// 状态筛选选项
const statusOptions = ref([
  { id: 'all', name: '全部状态' },
  { id: 'excellent', name: '优秀' },
  { id: 'good', name: '良好' },
  { id: 'fair', name: '一般' },
  { id: 'poor', name: '待提升' },
])

const activeFilter = ref('all')
const activeStatusFilter = ref('all')

// 模拟数据 - 后续从接口获取
const mockData: InterviewRecord[] = [
  {
    id: 1,
    jobName: 'AI算法工程师面试',
    company: '腾讯科技',
    icon: 'i-mdi-robot',
    difficulty: '中级难度',
    questionCount: 5,
    date: '2023-06-15',
    time: '14:30',
    duration: '30分钟',
    totalScore: 85,
    status: 'excellent',
    timeAgo: '2小时前',
    dimensions: {
      professional: 85,
      communication: 82,
      logic: 75,
      innovation: 78,
    },
    category: 'ai',
  },
  {
    id: 2,
    jobName: '数据分析师面试',
    company: '阿里巴巴',
    icon: 'i-mdi-chart-line',
    difficulty: '中级难度',
    questionCount: 8,
    date: '2023-06-12',
    time: '10:15',
    duration: '45分钟',
    totalScore: 72,
    status: 'good',
    timeAgo: '1天前',
    dimensions: {
      professional: 78,
      communication: 75,
      logic: 68,
      innovation: 70,
    },
    category: 'data',
  },
  {
    id: 3,
    jobName: '物联网工程师面试',
    company: '字节跳动',
    icon: 'i-mdi-wifi',
    difficulty: '基础难度',
    questionCount: 5,
    date: '2023-06-08',
    time: '16:45',
    duration: '25分钟',
    totalScore: 88,
    status: 'excellent',
    timeAgo: '3天前',
    dimensions: {
      professional: 90,
      communication: 88,
      logic: 85,
      innovation: 82,
    },
    category: 'iot',
  },
  {
    id: 4,
    jobName: '系统架构师面试',
    company: '美团',
    icon: 'i-mdi-cog',
    difficulty: '高级难度',
    questionCount: 6,
    date: '2023-06-05',
    time: '11:20',
    duration: '50分钟',
    totalScore: 65,
    status: 'fair',
    timeAgo: '1周前',
    dimensions: {
      professional: 70,
      communication: 68,
      logic: 58,
      innovation: 62,
    },
    category: 'system',
  },
]

/**
 * @description 获取状态标签样式和文本
 * @param status 状态
 * @returns 状态对象
 */
const getStatusInfo = (status: string) => {
  const statusMap = {
    excellent: { text: '优秀', class: 'status-excellent' },
    good: { text: '良好', class: 'status-good' },
    fair: { text: '一般', class: 'status-fair' },
    poor: { text: '待提升', class: 'status-poor' },
    completed: { text: '已完成', class: 'status-completed' },
    'in-progress': { text: '进行中', class: 'status-progress' },
    cancelled: { text: '已取消', class: 'status-cancelled' },
  }
  return statusMap[status] || { text: status, class: 'status-default' }
}

/**
 * @description 获取排序图标
 * @param field 排序字段
 * @returns 图标类名
 */
const getSortIcon = (field: 'date' | 'totalScore') => {
  if (sortField.value !== field) return 'i-mdi-sort'
  return sortOrder.value === 'asc' ? 'i-mdi-sort-ascending' : 'i-mdi-sort-descending'
}

/**
 * @description 处理排序
 * @param field 排序字段
 */
const handleSort = (field: 'date' | 'totalScore') => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'desc'
  }
}

/**
 * @description 显示排序选择器
 */
const showSortSelector = () => {
  showSortModal.value = true
}

/**
 * @description 关闭排序选择器
 */
const closeSortModal = () => {
  showSortModal.value = false
}

/**
 * @description 在弹窗中选择排序方式
 * @param field 排序字段
 */
const selectSortInModal = (field: 'date' | 'totalScore') => {
  handleSort(field)
  closeSortModal()
}

// 计算属性 - 根据筛选条件过滤并排序数据
const filteredHistory = computed(() => {
  let filtered = historyList.value

  // 按类别筛选
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter((item) => item.category === activeFilter.value)
  }

  // 按状态筛选
  if (activeStatusFilter.value !== 'all') {
    filtered = filtered.filter((item) => item.status === activeStatusFilter.value)
  }

  // 排序
  filtered.sort((a, b) => {
    let compareValue = 0
    if (sortField.value === 'date') {
      compareValue = new Date(a.date).getTime() - new Date(b.date).getTime()
    } else if (sortField.value === 'totalScore') {
      compareValue = a.totalScore - b.totalScore
    }
    return sortOrder.value === 'asc' ? compareValue : -compareValue
  })

  return filtered
})

/**
 * @description 获取评分等级样式
 * @param score 分数
 * @returns 样式类名
 */
const getScoreStyle = (score: number) => {
  if (score >= 85) return 'score-excellent'
  if (score >= 70) return 'score-good'
  return 'score-fair'
}

/**
 * @description 获取难度样式
 * @param difficulty 难度
 * @returns 样式类名
 */
const getDifficultyStyle = (difficulty: string) => {
  const styles = {
    基础难度: 'bg-green-100 text-green-700',
    中级难度: 'bg-yellow-100 text-yellow-700',
    高级难度: 'bg-red-100 text-red-700',
  }
  return styles[difficulty] || 'bg-gray-100 text-gray-700'
}

/**
 * @description 处理分类筛选变化
 * @param category 选中的分类
 */
const handleCategoryChange = (category: string) => {
  activeFilter.value = category
  pagination.value.page = 1
  fetchHistoryRecords()
}

/**
 * @description 处理分类筛选重置
 */
const handleCategoryReset = () => {
  activeFilter.value = 'all'
  pagination.value.page = 1
  fetchHistoryRecords()
}

/**
 * @description 切换状态筛选条件
 * @param statusId 状态筛选条件ID
 */
const changeStatusFilter = (statusId: string) => {
  activeStatusFilter.value = statusId
  pagination.value.page = 1
  fetchHistoryRecords()
}

/**
 * @description 查看详细报告
 * @param recordId 记录ID
 */
const viewDetail = (recordId: number) => {
  uni.navigateTo({
    url: `/pages/interview/detail?id=${recordId}`,
  })
}

/**
 * @description 加载更多记录
 */
const loadMore = async () => {
  if (loading.value) return

  loading.value = true

  try {
    const lastRecord = historyList.value[historyList.value.length - 1]
    const response = await getMoreHistoryRecords({
      params: {
        ...pagination.value,
        page: pagination.value.page + 1,
        lastId: lastRecord.id,
        category: activeFilter.value !== 'all' ? activeFilter.value : undefined,
        status: activeStatusFilter.value !== 'all' ? activeStatusFilter.value : undefined,
      },
    })

    if (response.data.records && response.data.records.length > 0) {
      historyList.value.push(...response.data.records)
      pagination.value.page++
    } else {
      uni.showToast({
        title: '没有更多数据了',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('加载更多失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 开始新面试
 */
const startInterview = () => {
  uni.navigateTo({
    url: '/pages/interview/select',
  })
}

/**
 * @description 获取面试历史记录
 */
const fetchHistoryRecords = async () => {
  loading.value = true

  try {
    const response = await getHistoryRecords({
      params: {
        ...pagination.value,
        category: activeFilter.value !== 'all' ? activeFilter.value : undefined,
        status: activeStatusFilter.value !== 'all' ? activeStatusFilter.value : undefined,
      },
    })

    if (response.data) {
      historyList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取历史记录失败:', error)
    // 失败时使用模拟数据
    historyList.value = mockData.filter(
      (item) => activeFilter.value === 'all' || item.category === activeFilter.value,
    )
    uni.showToast({
      title: '获取数据失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 获取统计数据
 */
const fetchStatistics = async () => {
  try {
    const response = await getStatistics()
    if (response.data) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用默认数据
    statistics.value = {
      totalInterviews: 15,
      averageScore: 82,
      monthlyImprovement: 12,
      improvementPercent: 15,
      currentLevel: '中级',
      nextLevelProgress: 75,
    }
  }
}

/**
 * @description 获取状态筛选器样式类名
 * @param statusId 状态ID
 * @returns 样式类名
 */
const getStatusFilterClass = (statusId: string) => {
  return activeStatusFilter.value === statusId ? 'status-filter-active' : ''
}

/**
 * @description 页面加载时获取数据
 */
onLoad(() => {
  fetchHistoryRecords()
  fetchStatistics()
})
</script>

<template>
  <view class="history-container">
    <HeadBar
      title="面试记录"
      :show-back="true"
      :show-right-button="false"
      :show-sort-button="true"
      :fixed="true"
      @sort-click="showSortSelector"
    />

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y enable-flex>
      <view class="content-wrapper">
        <!-- 成长统计卡片 -->
        <view class="stats-section">
          <view class="stats-content">
            <text class="stats-title">成长统计</text>
            <text class="stats-subtitle">记录你的每一次进步</text>
          </view>
          <view class="stats-decoration">
            <view class="i-mdi-chart-line decoration-icon"></view>
          </view>
        </view>

        <!-- 统计数据网格 -->
        <view class="stats-grid">
          <view class="stat-card">
            <view class="i-mdi-clipboard-check stat-icon"></view>
            <text class="stat-value">{{ statistics.totalInterviews }}</text>
            <text class="stat-label">总面试次数</text>
          </view>
          <view class="stat-card">
            <view class="i-mdi-trending-up stat-icon"></view>
            <text class="stat-value">{{ statistics.averageScore }}</text>
            <text class="stat-label">平均分数</text>
          </view>
          <view class="stat-card">
            <view class="i-mdi-rocket-launch stat-icon"></view>
            <text class="stat-value">+{{ statistics.monthlyImprovement }}%</text>
            <text class="stat-label">本月进步</text>
          </view>
          <view class="stat-card">
            <view class="i-mdi-medal stat-icon"></view>
            <text class="stat-value">{{ statistics.currentLevel }}</text>
            <text class="stat-label">当前等级</text>
          </view>
        </view>

        <!-- 进度条区域 -->
        <view class="progress-card">
          <view class="progress-header">
            <text class="progress-title">距离下一等级</text>
            <text class="progress-percent">{{ statistics.nextLevelProgress }}%</text>
          </view>
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: statistics.nextLevelProgress + '%' }"
            ></view>
          </view>
          <text class="progress-desc">
            继续加油！你已经超过了 {{ statistics.improvementPercent }}% 的用户
          </text>
        </view>

        <!-- 筛选区域 - 使用 CategoryFilter 组件 -->
        <view class="filter-section">
          <!-- 分类筛选 -->
          <CategoryFilter
            title="分类筛选"
            :category-options="categoryOptions"
            :selected-category="activeFilter"
            @change="handleCategoryChange"
            @reset="handleCategoryReset"
          />

          <!-- 状态筛选 -->
          <view class="status-section">
            <view class="status-header">
              <view class="status-title-wrapper">
                <view class="i-mdi-flag status-header-icon"></view>
                <text class="status-title">状态筛选</text>
              </view>
            </view>

            <scroll-view class="status-filter-scroll" scroll-x enable-flex>
              <view class="status-filter-list">
                <view
                  v-for="item in statusOptions"
                  :key="item.id"
                  class="status-filter-item"
                  :class="getStatusFilterClass(item.id)"
                  @click="changeStatusFilter(item.id)"
                >
                  <text class="status-filter-text">{{ item.name }}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- 历史记录列表 -->
        <view class="history-section">
          <view class="section-header">
            <text class="section-title">面试历史</text>
            <text class="section-subtitle">共 {{ filteredHistory.length }} 条记录</text>
          </view>

          <!-- 加载状态 -->
          <view v-if="loading" class="loading-wrapper">
            <view class="i-mdi-loading loading-icon"></view>
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 记录列表 -->
          <view v-else-if="filteredHistory.length > 0" class="history-list">
            <view
              v-for="record in filteredHistory"
              :key="record.id"
              class="history-card"
              @click="viewDetail(record.id)"
            >
              <view class="card-header">
                <view class="job-info">
                  <view class="job-icon">
                    <view :class="record.icon" class="icon-element"></view>
                  </view>
                  <view class="job-details">
                    <text class="job-name">{{ record.jobName }}</text>
                    <view class="job-meta">
                      <view class="i-mdi-office-building company-icon"></view>
                      <text class="company-name">{{ record.company }}</text>
                      <view class="difficulty-tag" :class="getDifficultyStyle(record.difficulty)">
                        <text class="difficulty-text">{{ record.difficulty }}</text>
                      </view>
                    </view>
                  </view>
                </view>

                <view class="score-section">
                  <view class="score-badge" :class="getScoreStyle(record.totalScore)">
                    <text class="score-value">{{ record.totalScore }}</text>
                    <text class="score-label">分</text>
                  </view>
                  <view class="status-badge" :class="getStatusInfo(record.status).class">
                    <text class="status-text">{{ getStatusInfo(record.status).text }}</text>
                  </view>
                </view>
              </view>

              <!-- 维度评分 -->
              <view class="dimensions-grid">
                <view class="dimension-item">
                  <view class="dimension-bar">
                    <view
                      class="dimension-fill professional"
                      :style="{ width: record.dimensions.professional + '%' }"
                    ></view>
                  </view>
                  <text class="dimension-label">专业知识 {{ record.dimensions.professional }}</text>
                </view>
                <view class="dimension-item">
                  <view class="dimension-bar">
                    <view
                      class="dimension-fill logic"
                      :style="{ width: record.dimensions.logic + '%' }"
                    ></view>
                  </view>
                  <text class="dimension-label">逻辑思维 {{ record.dimensions.logic }}</text>
                </view>
                <view class="dimension-item">
                  <view class="dimension-bar">
                    <view
                      class="dimension-fill communication"
                      :style="{ width: record.dimensions.communication + '%' }"
                    ></view>
                  </view>
                  <text class="dimension-label">
                    表达能力 {{ record.dimensions.communication }}
                  </text>
                </view>
                <view class="dimension-item">
                  <view class="dimension-bar">
                    <view
                      class="dimension-fill innovation"
                      :style="{ width: record.dimensions.innovation + '%' }"
                    ></view>
                  </view>
                  <text class="dimension-label">创新能力 {{ record.dimensions.innovation }}</text>
                </view>
              </view>

              <!-- 卡片底部 -->
              <view class="card-footer">
                <view class="footer-info">
                  <view class="i-mdi-calendar-clock info-icon"></view>
                  <text class="info-text">{{ record.date }} {{ record.time }}</text>
                  <text class="separator">·</text>
                  <view class="i-mdi-timer-outline info-icon"></view>
                  <text class="info-text">{{ record.duration }}</text>
                  <text class="separator">·</text>
                  <view class="i-mdi-help-circle-outline info-icon"></view>
                  <text class="info-text">{{ record.questionCount }}题</text>
                </view>
                <text class="time-ago">{{ record.timeAgo }}</text>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <view class="i-mdi-clipboard-text-off empty-icon"></view>
            <text class="empty-title">暂无面试记录</text>
            <text class="empty-desc">开始你的第一次模拟面试吧</text>
            <button class="empty-action-btn" @click="startInterview">
              <view class="i-mdi-play"></view>
              <text>开始面试</text>
            </button>
          </view>

          <!-- 加载更多 -->
          <view v-if="!loading && filteredHistory.length > 0" class="load-more-section">
            <button class="load-more-btn" @click="loadMore" :disabled="loading">
              <view class="i-mdi-refresh load-more-icon" :class="{ rotating: loading }"></view>
              <text class="load-more-text">{{ loading ? '加载中...' : '加载更多' }}</text>
            </button>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 排序选择器弹窗 -->
    <view v-if="showSortModal" class="sort-modal-overlay" @click="closeSortModal">
      <view class="sort-modal-content" @click.stop>
        <view class="sort-modal-header">
          <text class="sort-modal-title">选择排序方式</text>
          <button class="sort-modal-close-btn" @click="closeSortModal">
            <view class="i-mdi-close sort-modal-close-icon"></view>
          </button>
        </view>

        <view class="sort-modal-body">
          <view class="sort-modal-options">
            <view
              class="sort-modal-option"
              :class="{ 'sort-modal-active': sortField === 'date' }"
              @click="selectSortInModal('date')"
            >
              <view class="sort-modal-option-content">
                <view class="i-mdi-calendar-clock sort-modal-option-icon"></view>
                <text class="sort-modal-option-text">按时间排序</text>
              </view>
              <view
                v-if="sortField === 'date'"
                :class="getSortIcon('date')"
                class="sort-modal-check-icon"
              ></view>
            </view>
            <view
              class="sort-modal-option"
              :class="{ 'sort-modal-active': sortField === 'totalScore' }"
              @click="selectSortInModal('totalScore')"
            >
              <view class="sort-modal-option-content">
                <view class="i-mdi-trophy sort-modal-option-icon"></view>
                <text class="sort-modal-option-text">按分数排序</text>
              </view>
              <view
                v-if="sortField === 'totalScore'"
                :class="getSortIcon('totalScore')"
                class="sort-modal-check-icon"
              ></view>
            </view>
          </view>
        </view>

        <view class="sort-modal-footer">
          <button class="sort-modal-confirm-btn" @click="closeSortModal">
            <text class="sort-modal-btn-text">确定</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.history-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  overflow: hidden;
}

// 主要内容区域 - 修复滚动问题
.main-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  padding-top: 100rpx; // 为固定头部预留空间（适当增加以确保内容不被遮挡）
  box-sizing: border-box;
  background: transparent;

  // 确保在不同平台上都能正常滚动
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

// 内容包装器
.content-wrapper {
  min-height: calc(100vh - 100px); // 确保内容高度足够滚动
  padding: 20rpx 20rpx 160rpx 20rpx; // 底部留出安全距离
}

// 统计区域
.stats-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  margin: 0 0 24rpx 0;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);

  .stats-content {
    flex: 1;

    .stats-title {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: white;
    }

    .stats-subtitle {
      font-size: 24rpx;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .stats-decoration {
    .decoration-icon {
      font-size: 64rpx;
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 统计数据网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;

  .stat-card {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    align-items: center;
    padding: 24rpx;
    background: white;
    border: 2rpx solid #e2e8f0;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
      transform: scale(0.98);
    }

    .stat-icon {
      font-size: 32rpx;
      color: #00c9a7;
    }

    .stat-value {
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .stat-label {
      font-size: 22rpx;
      color: #64748b;
    }
  }
}

// 进度卡片
.progress-card {
  padding: 24rpx;
  margin-bottom: 32rpx;
  background: white;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .progress-title {
      font-size: 26rpx;
      font-weight: 500;
      color: #1e293b;
    }

    .progress-percent {
      font-size: 26rpx;
      font-weight: 600;
      color: #00c9a7;
    }
  }

  .progress-bar {
    height: 16rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    background: #e2e8f0;
    border-radius: 8rpx;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
      border-radius: 8rpx;
      transition: width 1s ease-out;
    }
  }

  .progress-desc {
    font-size: 22rpx;
    line-height: 1.4;
    color: #64748b;
  }
}

// 筛选区域
.filter-section {
  margin-bottom: 32rpx;

  // 状态筛选区域
  .status-section {
    margin: 24rpx 0;

    .status-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .status-title-wrapper {
        display: flex;
        gap: 12rpx;
        align-items: center;

        .status-header-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }

        .status-title {
          font-size: 26rpx;
          font-weight: 600;
          color: #1e293b;
        }
      }
    }

    .status-filter-scroll {
      white-space: nowrap;

      .status-filter-list {
        display: inline-flex;
        gap: 12rpx;
        padding: 0 8rpx;

        .status-filter-item {
          padding: 10rpx 20rpx;
          font-size: 22rpx;
          font-weight: 500;
          color: #64748b;
          white-space: nowrap;
          background: white;
          border: 2rpx solid #e2e8f0;
          border-radius: 20rpx;
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.98);
          }

          &.status-filter-active {
            color: #00c9a7;
            background: #f0fdf9;
            border-color: #00c9a7;
            box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.2);
          }
        }
      }
    }
  }
}

// 历史记录区域
.history-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #64748b;
    }
  }

  // 加载状态
  .loading-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0;

    .loading-icon {
      font-size: 48rpx;
      color: #00c9a7;
      animation: rotate 1s linear infinite;
    }

    .loading-text {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #64748b;
    }
  }

  // 历史记录列表
  .history-list {
    .history-card {
      margin-bottom: 20rpx;
      overflow: hidden;
      background: white;
      border: 2rpx solid #f1f5f9;
      border-radius: 20rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:active {
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
        transform: translateY(-4rpx);
      }

      .card-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 24rpx;
        padding-bottom: 16rpx;

        .job-info {
          display: flex;
          flex: 1;
          gap: 16rpx;
          align-items: center;

          .job-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 72rpx;
            height: 72rpx;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16rpx;

            .icon-element {
              font-size: 32rpx;
              color: #00c9a7;
            }
          }

          .job-details {
            flex: 1;

            .job-name {
              display: block;
              margin-bottom: 8rpx;
              font-size: 28rpx;
              font-weight: 600;
              line-height: 1.3;
              color: #1e293b;
            }

            .job-meta {
              display: flex;
              gap: 12rpx;
              align-items: center;

              .company-icon {
                font-size: 20rpx;
                color: #64748b;
              }

              .company-name {
                font-size: 24rpx;
                color: #64748b;
              }

              .difficulty-tag {
                padding: 4rpx 12rpx;
                font-size: 20rpx;
                font-weight: 500;
                border-radius: 10rpx;

                &.bg-green-100 {
                  color: #16a34a;
                  background: #dcfce7;
                }

                &.bg-yellow-100 {
                  color: #d97706;
                  background: #fef3c7;
                }

                &.bg-red-100 {
                  color: #dc2626;
                  background: #fee2e2;
                }
              }
            }
          }
        }

        .score-section {
          display: flex;
          flex-direction: column;
          gap: 8rpx;
          align-items: flex-end;

          .score-badge {
            display: flex;
            gap: 4rpx;
            align-items: baseline;
            padding: 8rpx 16rpx;
            border-radius: 12rpx;

            .score-value {
              font-size: 32rpx;
              font-weight: 700;
            }

            .score-label {
              font-size: 20rpx;
              color: inherit;
            }

            &.score-excellent {
              background: #dcfce7;

              .score-value {
                color: #16a34a;
              }
            }

            &.score-good {
              background: #fef3c7;

              .score-value {
                color: #d97706;
              }
            }

            &.score-fair {
              background: #fee2e2;

              .score-value {
                color: #dc2626;
              }
            }
          }

          .status-badge {
            padding: 4rpx 12rpx;
            font-size: 20rpx;
            font-weight: 500;
            border-radius: 10rpx;

            &.status-excellent {
              color: #16a34a;
              background: #dcfce7;
            }

            &.status-good {
              color: #d97706;
              background: #fef3c7;
            }

            &.status-fair {
              color: #eab308;
              background: #fef9c3;
            }

            &.status-poor {
              color: #dc2626;
              background: #fee2e2;
            }

            &.status-completed {
              color: #0ea5e9;
              background: #e0f2fe;
            }

            &.status-progress {
              color: #8b5cf6;
              background: #ede9fe;
            }

            &.status-cancelled {
              color: #6b7280;
              background: #f3f4f6;
            }
          }
        }
      }

      // 维度评分
      .dimensions-grid {
        padding: 0 24rpx 20rpx;

        .dimension-item {
          margin-bottom: 16rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .dimension-bar {
            height: 8rpx;
            margin-bottom: 8rpx;
            overflow: hidden;
            background: #e2e8f0;
            border-radius: 4rpx;

            .dimension-fill {
              height: 100%;
              border-radius: 4rpx;
              transition: width 1s ease-out;

              &.professional {
                background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
              }

              &.logic {
                background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
              }

              &.communication {
                background: linear-gradient(90deg, #8b5cf6 0%, #a78bfa 100%);
              }

              &.innovation {
                background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
              }
            }
          }

          .dimension-label {
            font-size: 22rpx;
            color: #64748b;
          }
        }
      }

      // 卡片底部
      .card-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16rpx 24rpx;
        background: #f8fafc;
        border-top: 1rpx solid #e2e8f0;

        .footer-info {
          display: flex;
          gap: 8rpx;
          align-items: center;

          .info-icon {
            font-size: 20rpx;
            color: #94a3b8;
          }

          .info-text {
            font-size: 22rpx;
            color: #64748b;
          }

          .separator {
            font-size: 20rpx;
            color: #cbd5e1;
          }
        }

        .time-ago {
          font-size: 22rpx;
          color: #94a3b8;
        }
      }
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      margin-bottom: 32rpx;
      font-size: 120rpx;
      color: #cbd5e1;
    }

    .empty-title {
      margin-bottom: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #475569;
    }

    .empty-desc {
      margin-bottom: 40rpx;
      font-size: 24rpx;
      line-height: 1.5;
      color: #64748b;
      text-align: center;
    }

    .empty-action-btn {
      display: flex;
      gap: 12rpx;
      align-items: center;
      padding: 20rpx 32rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: white;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border: none;
      border-radius: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
      }
    }
  }

  // 加载更多
  .load-more-section {
    margin: 40rpx 0;
    text-align: center;

    .load-more-btn {
      display: inline-flex;
      gap: 12rpx;
      align-items: center;
      padding: 20rpx 40rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: #64748b;
      background: white;
      border: 2rpx solid #e2e8f0;
      border-radius: 32rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:disabled {
        opacity: 0.6;
      }

      &:not(:disabled):active {
        background: #f8f9fa;
        transform: scale(0.98);
      }

      .load-more-icon {
        font-size: 20rpx;

        &.rotating {
          animation: rotate 1s linear infinite;
        }
      }
    }
  }
}

// 排序弹窗样式
.sort-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  z-index: 1000;
  animation: sortModalFadeIn 0.3s ease-out;

  .sort-modal-content {
    width: 320rpx;
    height: 100vh;
    background: white;
    display: flex;
    flex-direction: column;
    animation: sortModalSlideIn 0.3s ease-out;

    .sort-modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 24rpx 24rpx;
      border-bottom: 1rpx solid #e2e8f0;

      .sort-modal-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
      }

      .sort-modal-close-btn {
        padding: 8rpx;
        background: transparent;
        border: none;

        .sort-modal-close-icon {
          font-size: 28rpx;
          color: #64748b;
        }
      }
    }

    .sort-modal-body {
      flex: 1;
      padding: 24rpx;

      .sort-modal-options {
        .sort-modal-option {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20rpx 0;
          border-bottom: 1rpx solid #f1f5f9;
          transition: all 0.2s ease;

          &:last-child {
            border-bottom: none;
          }

          &:active {
            background: #f8fafc;
          }

          &.sort-modal-active {
            .sort-modal-option-content {
              .sort-modal-option-icon {
                color: #00c9a7;
              }

              .sort-modal-option-text {
                color: #00c9a7;
                font-weight: 600;
              }
            }

            .sort-modal-check-icon {
              color: #00c9a7;
            }
          }

          .sort-modal-option-content {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .sort-modal-option-icon {
              font-size: 28rpx;
              color: #64748b;
            }

            .sort-modal-option-text {
              font-size: 26rpx;
              color: #1e293b;
            }
          }

          .sort-modal-check-icon {
            font-size: 24rpx;
            color: #00c9a7;
          }
        }
      }
    }

    .sort-modal-footer {
      padding: 24rpx;
      border-top: 1rpx solid #e2e8f0;

      .sort-modal-confirm-btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        border: none;
        border-radius: 16rpx;
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.98);
        }

        .sort-modal-btn-text {
          font-size: 26rpx;
          font-weight: 500;
          color: white;
        }
      }
    }
  }
}

// 动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes sortModalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes sortModalSlideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes sortModalSlideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

// H5端优化
/* #ifdef H5 */
.history-container {
  // H5端特殊处理 - 确保正确的视口高度
  height: 100vh;
  max-height: 100vh;
}

.main-content {
  // H5端优化滚动性能
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;

  // 添加滚动条样式优化
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 201, 167, 0.3);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 201, 167, 0.5);
  }
}

.history-section .history-list .history-card,
.filter-section .sort-selector .sort-option,
.filter-section .filter-scroll .filter-list .filter-item,
.filter-section .status-filter-scroll .status-filter-list .status-filter-item,
.stats-grid .stat-card,
.history-section .load-more-section .load-more-btn,
.history-section .empty-state .empty-action-btn {
  cursor: pointer;
  user-select: none;
}
/* #endif */

// APP端优化
/* #ifdef APP-PLUS */
.main-content {
  // APP端优化滚动性能
  padding-top: 100px; // 为状态栏和导航栏预留空间
}
/* #endif */

// 小程序端优化
/* #ifdef MP */
.main-content {
  // 小程序端特殊处理
  padding-top: 100px;
}
/* #endif */

// 响应式设计
@media screen and (max-width: 750rpx) {
  .content-wrapper {
    padding: 16rpx 16rpx 160rpx 16rpx;
  }

  .stats-grid {
    gap: 12rpx;
  }

  .history-card {
    .card-header {
      padding: 20rpx;
    }

    .dimensions-grid {
      padding: 0 20rpx 16rpx;
    }

    .card-footer {
      padding: 12rpx 20rpx;
    }
  }
}

// 解决iOS橡皮筋效果
/* #ifdef H5 */
@supports (-webkit-touch-callout: none) {
  .main-content {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}
/* #endif */
</style>
