<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
// @ts-ignore
import { sendSmsCode, loginByPhone, loginByPassword, thirdPartyLogin } from '@/service/auth'
// @ts-ignore
import { useUserStore } from '@/store/user'
// @ts-ignore
import Notification from '@/components/Notification.vue'

// 使用用户状态管理
const userStore = useUserStore()

// Reactive state
const currentTime = ref('9:41')
const phone = ref('13333333333')
const code = ref('123456')
const password = ref('123456')

// 登录方式：'sms' 或 'password'
const loginMethod = ref<'sms' | 'password'>('sms')

// 通知组件相关状态
const notificationVisible = ref(false)
const notificationMessage = ref('')
const notificationType = ref<'success' | 'error' | 'info'>('info')

const phoneError = ref('')
const codeError = ref('')
const passwordError = ref('')

const sendCodeBtnText = ref('获取验证码')
const sendCodeBtnDisabled = ref(false)
let countdownInterval: any = null

const isLoading = ref(false)
const loginBtnText = ref('立即登录')
const loginIconVisible = ref(true)

// 密码可见性控制
const passwordVisible = ref(false)

// Functions
/**
 * 更新时间
 */
function updateTime() {
  const now = new Date()
  currentTime.value = `${now.getHours().toString().padStart(2, '0')}:${now
    .getMinutes()
    .toString()
    .padStart(2, '0')}`
}

/**
 * 验证手机号
 * @param p 手机号
 * @returns 是否有效
 */
function validatePhone(p: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(p)
}

/**
 * 验证验证码
 * @param c 验证码
 * @returns 是否有效
 */
function validateCode(c: string): boolean {
  // 验证码长度为6位，且为数字
  return c.length === 6 && /^\d{6}$/.test(c)
}

/**
 * 验证密码
 * @param p 密码
 * @returns 是否有效
 */
function validatePassword(p: string): boolean {
  // 密码长度为6-20位
  return p.length >= 6 && p.length <= 20
}

/**
 * 显示通知
 * @param message 消息
 * @param type 类型
 * @param duration 持续时间
 */
function showNotification(
  message: string,
  type: 'success' | 'error' | 'info' = 'info',
  duration = 3000,
) {
  notificationMessage.value = message
  notificationType.value = type
  notificationVisible.value = true
}

/**
 * 通知关闭回调
 */
function onNotificationClose() {
  notificationVisible.value = false
}

/**
 * 显示输入错误
 * @param field 字段
 * @param message 消息
 */
function showInputError(field: 'phone' | 'code' | 'password', message: string) {
  if (field === 'phone') {
    phoneError.value = message
  } else if (field === 'code') {
    codeError.value = message
  } else if (field === 'password') {
    passwordError.value = message
  }
  // Auto clear error after 3s
  setTimeout(() => {
    clearInputError(field)
  }, 3000)
}

/**
 * 清除输入错误
 * @param field 字段
 */
function clearInputError(field: 'phone' | 'code' | 'password') {
  if (field === 'phone') {
    phoneError.value = ''
  } else if (field === 'code') {
    codeError.value = ''
  } else if (field === 'password') {
    passwordError.value = ''
  }
}

/**
 * 切换密码可见性
 */
function togglePasswordVisibility() {
  passwordVisible.value = !passwordVisible.value
}

/**
 * 切换登录方式
 * @param method 登录方式
 */
function switchLoginMethod(method: 'sms' | 'password') {
  loginMethod.value = method
  // 清空错误信息
  clearInputError('phone')
  clearInputError('password')
  clearInputError('code')

  // 清空输入框
  if (method === 'sms') {
    password.value = ''
  } else {
    code.value = ''
  }

  // 更新登录按钮文字
  loginBtnText.value = method === 'sms' ? '验证码登录' : '密码登录'
}

/**
 * 发送验证码
 */
async function handleSendCode() {
  if (!phone.value) {
    showInputError('phone', '请输入手机号码')
    return
  }
  if (!validatePhone(phone.value)) {
    showInputError('phone', '请输入正确的手机号码格式')
    return
  }

  sendCodeBtnDisabled.value = true
  let countdown = 60
  sendCodeBtnText.value = `${countdown}s后重发`

  countdownInterval = setInterval(() => {
    countdown--
    if (countdown <= 0) {
      clearInterval(countdownInterval)
      sendCodeBtnText.value = '获取验证码'
      sendCodeBtnDisabled.value = false
    } else {
      sendCodeBtnText.value = `${countdown}s后重发`
    }
  }, 1000)

  try {
    // 调用后端发送验证码接口
    const res = await sendSmsCode({
      params: { phone: phone.value },
    })

    if (res.code === 200) {
      showNotification('验证码已发送到您的手机', 'success')

      // 开发环境下自动填入验证码（生产环境应移除）
      // #ifdef DEV
      setTimeout(() => {
        code.value = '123456'
        showNotification('开发模式：验证码为 123456', 'info')
      }, 500)
      // #endif
    } else {
      showNotification(res.message || '验证码发送失败', 'error')
      clearInterval(countdownInterval)
      sendCodeBtnText.value = '获取验证码'
      sendCodeBtnDisabled.value = false
    }
  } catch (error) {
    console.error('验证码发送失败:', error)
    showNotification('验证码发送失败，请稍后重试', 'error')
    clearInterval(countdownInterval)
    sendCodeBtnText.value = '获取验证码'
    sendCodeBtnDisabled.value = false
  }
}

/**
 * 登录
 */
async function handleLogin() {
  if (isLoading.value) return

  clearInputError('phone')
  clearInputError('code')
  clearInputError('password')

  let hasError = false
  if (!phone.value) {
    showInputError('phone', '请输入手机号码')
    hasError = true
  } else if (!validatePhone(phone.value)) {
    showInputError('phone', '请输入正确的手机号码格式')
    hasError = true
  }

  if (loginMethod.value === 'sms') {
    // 验证码登录验证
    if (!code.value) {
      showInputError('code', '请输入验证码')
      hasError = true
    } else if (!validateCode(code.value)) {
      showInputError('code', '请输入6位数字验证码')
      hasError = true
    }
  } else {
    // 密码登录验证
    if (!password.value) {
      showInputError('password', '请输入密码')
      hasError = true
    } else if (!validatePassword(password.value)) {
      showInputError('password', '请输入6-20位密码')
      hasError = true
    }
  }

  if (hasError) return

  setLoadingState(true)

  try {
    if (loginMethod.value === 'sms') {
      // 验证码登录
      const res = await loginByPhone({
        params: {
          phone: phone.value,
          code: code.value,
        },
      })

      if (res.code === 200 && res.data) {
        // 保存用户信息到状态管理
        userStore.setUserInfo(res.data)
        userStore.setToken(res.data.tokenValue)

        // 保存到本地存储（统一存储键名）
        uni.setStorageSync('userInfo', JSON.stringify(res.data))
        uni.setStorageSync('tokenName', res.data.tokenName)
        uni.setStorageSync('token', res.data.tokenValue)
        uni.setStorageSync('loginTime', new Date().toISOString())
        uni.setStorageSync('isLoggedIn', true)

        console.log('验证码登录成功，用户信息已保存:', res.data)
        showNotification('登录成功！正在跳转...', 'success')
        setTimeout(() => {
          uni.switchTab({ url: '/pages/index/index' })
        }, 1500)
      } else {
        showNotification(res.message || '登录失败，请检查验证码', 'error')
      }
    } else {
      // 密码登录
      const res = await loginByPassword({
        params: {
          phone: phone.value,
          password: password.value,
        },
      })
      console.log('密码登录响应:', res)

      if (res.code === 200 && res.data) {
        // 保存用户信息到状态管理
        userStore.setUserInfo(res.data)
        userStore.setToken(res.data.tokenValue)

        // 保存到本地存储（统一存储键名）
        uni.setStorageSync('userInfo', JSON.stringify(res.data))
        uni.setStorageSync('tokenName', res.data.tokenName)
        uni.setStorageSync('token', res.data.tokenValue)
        uni.setStorageSync('loginTime', new Date().toISOString())
        uni.setStorageSync('isLoggedIn', true)

        console.log('密码登录成功，用户信息已保存:', res.data)
        showNotification('登录成功！正在跳转...', 'success')
        setTimeout(() => {
          uni.switchTab({ url: '/pages/index/index' })
        }, 1500)
      } else {
        showNotification(res.message || '登录失败，请检查账号密码', 'error')
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    showNotification('登录失败，请稍后重试', 'error')
  } finally {
    setLoadingState(false)
  }
}

/**
 * 设置登录状态
 * @param loading 是否加载
 */
function setLoadingState(loading: boolean) {
  isLoading.value = loading
  if (loading) {
    loginIconVisible.value = false
    loginBtnText.value = '登录中...'
  } else {
    loginIconVisible.value = true
    loginBtnText.value = loginMethod.value === 'sms' ? '验证码登录' : '密码登录'
  }
}

/**
 * 注册
 */
function goToRegister() {
  uni.navigateTo({ url: '/pages/auth/register' }) // Assuming this is the path
}

/**
 * 忘记密码
 */
function goToForgotPassword() {
  // Placeholder for navigation
  uni.navigateTo({ url: '/pages/auth/forgetpassword' })
}

/**
 * 第三方登录
 * @param platform 平台
 */
async function handleThirdPartyLogin(platform: string) {
  const platformMap: Record<string, 'wechat' | 'qq' | 'weibo'> = {
    微信: 'wechat',
    QQ: 'qq',
    微博: 'weibo',
  }

  const platformType = platformMap[platform]
  if (!platformType) {
    showNotification('不支持的登录平台', 'error')
    return
  }

  try {
    // 根据不同平台调用相应的授权接口
    // #ifdef MP-WEIXIN
    if (platformType === 'wechat') {
      uni.login({
        provider: 'weixin',
        success: async (loginRes) => {
          if (loginRes.code) {
            try {
              // 调用后端第三方登录接口
              const res = await thirdPartyLogin({
                params: {
                  source: platformType,
                  socialCode: loginRes.code,
                },
              })

              if (res.code === 200 && res.data) {
                // 保存用户信息到状态管理
                userStore.setUserInfo(res.data)
                userStore.setToken(res.data.token)

                // 保存到本地存储（统一存储键名）
                uni.setStorageSync('userInfo', JSON.stringify(res.data))
                uni.setStorageSync('token', res.data.token)
                uni.setStorageSync('loginTime', new Date().toISOString())
                uni.setStorageSync('isLoggedIn', true)

                console.log('第三方登录成功，用户信息已保存:', res.data)
                showNotification('登录成功！正在跳转...', 'success')
                setTimeout(() => {
                  uni.switchTab({ url: '/pages/index/index' })
                }, 1500)
              } else {
                showNotification(res.message || '第三方登录失败', 'error')
              }
            } catch (error) {
              console.error('第三方登录失败:', error)
              showNotification('第三方登录失败，请稍后重试', 'error')
            }
          }
        },
        fail: () => {
          showNotification('获取授权失败', 'error')
        },
      })
      return
    }
    // #endif

    // 其他平台暂未实现
    showNotification(`${platform}登录功能正在开发中`, 'info')
  } catch (error) {
    console.error('第三方登录失败:', error)
    showNotification('第三方登录失败，请稍后重试', 'error')
  }
}

/**
 * 页面加载时
 */
onMounted(() => {
  updateTime()
  const timerId = setInterval(updateTime, 1000) // Update time every second

  // 初始化登录按钮文字
  loginBtnText.value = loginMethod.value === 'sms' ? '验证码登录' : '密码登录'

  // 检查是否已登录
  const isLoggedIn = userStore.checkLoginStatus()
  if (isLoggedIn && !userStore.isTokenExpired()) {
    showNotification('您已登录，正在跳转...', 'success')
    setTimeout(() => {
      uni.switchTab({ url: '/pages/index/index' })
    }, 1000)
  }

  // 监听键盘弹起事件
  onBeforeUnmount(() => {
    if (countdownInterval) clearInterval(countdownInterval)
    clearInterval(timerId)
  })
})
</script>

<template>
  <view class="login-container">
    <!-- 通知组件 -->
    <Notification
      :visible="notificationVisible"
      :message="notificationMessage"
      :type="notificationType"
      :duration="3000"
      :closable="true"
      @update:visible="(value) => (notificationVisible = value)"
      @close="onNotificationClose"
    />

    <!-- 主要内容区域 -->
    <view class="content-area bg-gray-50 relative">
      <!-- 背景装饰元素 -->
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>
      <view class="decoration-circle decoration-circle-3"></view>

      <view class="page-content">
        <!-- Logo 区域 -->
        <view class="logo-area">
          <view class="logo">
            <text class="i-fa-solid-briefcase text-white"></text>
          </view>
          <text class="title">智能面试助手</text>
        </view>

        <!-- 登录表单 -->
        <view class="form-container">
          <!-- 登录方式切换 -->
          <view class="login-method-switch">
            <view class="switch-tabs">
              <view
                class="switch-tab"
                :class="{ active: loginMethod === 'sms' }"
                @click="switchLoginMethod('sms')"
              >
                <text class="i-fa-solid-mobile-screen-button mr-2"></text>
                验证码登录
              </view>
              <view
                class="switch-tab"
                :class="{ active: loginMethod === 'password' }"
                @click="switchLoginMethod('password')"
              >
                <text class="i-fa-solid-lock mr-2"></text>
                密码登录
              </view>
            </view>
          </view>

          <!-- 手机号输入 -->
          <view class="form-item" style="--item-index: 0">
            <text class="form-label">手机号</text>
            <view class="input-container">
              <input
                type="number"
                v-model="phone"
                placeholder="请输入手机号码"
                class="form-input"
                :class="{ 'input-error': phoneError }"
                maxlength="11"
              />
              <text class="input-icon i-fa-solid-mobile-screen-button"></text>
            </view>
            <text v-if="phoneError" class="error-text">{{ phoneError }}</text>
          </view>

          <!-- 验证码输入 (验证码登录时显示) -->
          <view v-if="loginMethod === 'sms'" class="form-item" style="--item-index: 1">
            <text class="form-label">验证码</text>
            <view class="code-container">
              <view class="input-container flex-1">
                <input
                  type="number"
                  v-model="code"
                  placeholder="请输入验证码"
                  class="form-input"
                  :class="{ 'input-error': codeError }"
                  maxlength="6"
                />
                <text class="input-icon i-fa-solid-shield-halved"></text>
              </view>
              <button
                type="button"
                @click="handleSendCode"
                :disabled="sendCodeBtnDisabled"
                class="code-btn"
                :class="{ 'btn-disabled': sendCodeBtnDisabled }"
              >
                {{ sendCodeBtnText }}
              </button>
            </view>
            <text v-if="codeError" class="error-text">{{ codeError }}</text>
          </view>

          <!-- 密码输入 (密码登录时显示) -->
          <view v-if="loginMethod === 'password'" class="form-item" style="--item-index: 1">
            <text class="form-label">密码</text>
            <view class="input-container">
              <input
                :type="passwordVisible ? 'text' : 'password'"
                v-model="password"
                placeholder="请输入密码"
                class="form-input"
                :class="{ 'input-error': passwordError }"
                maxlength="20"
              />
              <text class="input-icon-toggle" @click="togglePasswordVisibility">
                <text v-if="passwordVisible" class="i-fa-solid-eye-slash"></text>
                <text v-else class="i-fa-solid-eye"></text>
              </text>
            </view>
            <text v-if="passwordError" class="error-text">{{ passwordError }}</text>
          </view>

          <!-- 登录按钮 -->
          <button
            type="button"
            @click="handleLogin"
            :disabled="isLoading"
            class="login-btn"
            :class="{ 'btn-loading': isLoading }"
          >
            <view class="btn-content">
              <view v-if="isLoading" class="loading-spinner"></view>
              <text v-if="loginIconVisible" class="i-fa-solid-arrow-right-to-bracket mr-2"></text>
              <text>{{ loginBtnText }}</text>
            </view>
          </button>
          <!-- 其他操作 -->
          <view class="other-actions">
            <button type="button" @click="goToRegister" class="register-btn">
              <text class="i-fa-solid-user-plus mr-2"></text>
              注册新账号
            </button>
            <view class="forgot-password">
              <text @click="goToForgotPassword" class="link-text">
                <text class="i-fa-solid-key mr-1"></text>
                忘记密码？
              </text>
            </view>
          </view>
        </view>

        <!-- 第三方登录 -->
        <view class="third-party-login">
          <view class="social-buttons">
            <view @click="handleThirdPartyLogin('微信')" class="social-btn">
              <text class="i-fa-brands-weixin social-icon wx-icon"></text>
            </view>
            <view @click="handleThirdPartyLogin('QQ')" class="social-btn">
              <text class="i-fa-brands-qq social-icon qq-icon"></text>
            </view>
            <view @click="handleThirdPartyLogin('微博')" class="social-btn">
              <text class="i-fa-brands-weibo social-icon wb-icon"></text>
            </view>
          </view>
          <text class="social-text">第三方账号登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 导入统一的样式变量
@import '@/style/auth-variables.scss';
/* 基础样式 */
.login-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fdfc 0%, #e8f5f2 100%);
  overflow: hidden;
}
/* 状态栏样式 */
.status-bar {
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  height: calc(88rpx + var(--status-bar-height, 0));
  padding: 0 30rpx;
  /* #ifdef MP */
  padding-top: var(--status-bar-height, 0);
  /* #endif */
}

.status-time {
  font-size: 28rpx;
  font-weight: 500;
}

.battery-container {
  position: relative;
  width: 44rpx;
  height: 24rpx;
  padding: 2rpx;
  border: 2rpx solid #ffffff;
  border-radius: 4rpx;
}

.battery-level {
  width: 70%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 2rpx;
}
/* 主内容区域 */
.content-area {
  box-sizing: border-box;
  width: 100%;
  min-height: calc(100vh - 88rpx);
  /* #ifdef MP */
  min-height: calc(100vh - 88rpx - var(--status-bar-height, 0));
  /* #endif */
  padding: 40rpx 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
/* 装饰圆圈 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  backdrop-filter: blur(20rpx);
}

.decoration-circle-1 {
  top: 160rpx;
  right: 60rpx;
  width: 240rpx;
  height: 240rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.2) 0%, rgba(0, 201, 167, 0.4) 100%);
  animation: floating 3s ease-in-out infinite;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);
}

.decoration-circle-2 {
  bottom: 300rpx;
  left: 60rpx;
  width: 180rpx;
  height: 180rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.3) 100%);
  animation: floating 4s ease-in-out infinite;
  animation-direction: reverse;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.1);
}

.decoration-circle-3 {
  top: 50%;
  right: -80rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.2) 100%);
  animation: floating 5s ease-in-out infinite;
}
/* 页面内容 */
.page-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 750rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}
/* Logo 区域 */
.logo-area {
  width: 100%;
  margin-bottom: 50rpx;
  text-align: center;
  animation: slideInDown 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
  }
}

.logo text {
  font-size: 60rpx;
  position: relative;
  z-index: 1;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  background: linear-gradient(135deg, #333 0%, #00B39A 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
/* 表单容器 */
.form-container {
  width: 100%;
  margin-bottom: 50rpx;
  animation: slideInUp 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.2s both;
}

/* 登录方式切换 */
.login-method-switch {
  margin-bottom: 30rpx;
}

.switch-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.switch-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 72rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  background: transparent;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 12rpx;
    background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &.active {
    color: #fff;
    font-weight: 600;
    transform: translateY(-2rpx);

    &::before {
      opacity: 1;
    }

    text {
      position: relative;
      z-index: 1;
    }
  }
}

.form-item {
  margin-bottom: 30rpx;
  animation: slideInLeft 0.6s cubic-bezier(0.19, 1, 0.22, 1) calc(var(--item-index, 0) * 0.1s) both;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.input-container {
  position: relative;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 96rpx;
  padding: 0 96rpx 0 30rpx;
  font-size: 30rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:focus {
    border-color: #00C9A7;
    box-shadow: 0 0 0 2rpx rgba(0, 201, 167, 0.2);
    background: #fff;
    transform: translateY(-2rpx);
  }

  &::placeholder {
    color: #A3A3A3;
  }

  &:focus::placeholder {
    color: transparent;
  }
}

.input-error {
  border-color: #F56565 !important;
  box-shadow: 0 0 0 2rpx rgba(245, 101, 101, 0.2) !important;
  animation: shake 0.5s ease-in-out;
}

.input-icon {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 36rpx;
  color: #A3A3A3;
  transform: translateY(-50%);
}

.input-icon-toggle {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 36rpx;
  color: #A3A3A3;
  transform: translateY(-50%);
  cursor: pointer;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;

  &:active {
    color: #00C9A7;
    background: rgba(0, 201, 167, 0.1);
    transform: translateY(-50%) scale(0.95);
  }
}

.error-text {
  display: block;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #F56565;
  animation: slideInDown 0.3s ease-out;
}
/* 验证码输入 */
.code-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.code-btn {
  height: 96rpx;
  min-width: 180rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #00C9A7;
  background: #FFFFFF;
  border: 2rpx solid #00C9A7;
  border-radius: 16rpx;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
}

.btn-disabled {
  opacity: 0.5;
}
/* 登录按钮 */
.login-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  margin: 40rpx 0 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
  border: none;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
  animation: slideInUp 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.4s both;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 4rpx 8rpx rgba(0, 201, 167, 0.2);

    &::before {
      opacity: 1;
    }
  }
}

.btn-loading {
  opacity: 0.8;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
/* 其他操作 */
.other-actions {
  width: 100%;
  margin-bottom: 30rpx;
  animation: slideInUp 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.5s both;
}

.register-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #00C9A7;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid #00C9A7;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.forgot-password {
  text-align: center;
}

.link-text {
  display: inline-flex;
  align-items: center;
  font-size: 28rpx;
  color: #777;
  padding: 10rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;

  &:active {
    color: #00C9A7;
    background: rgba(0, 201, 167, 0.1);
    transform: scale(0.95);
  }
}
/* 第三方登录 */
.third-party-login {
  width: 100%;
  margin-top: 40rpx;
  text-align: center;
  animation: slideInUp 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.6s both;
}

.social-buttons {
  display: flex;
  gap: 30rpx;
  justify-content: center;
  margin-bottom: 16rpx;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.social-icon {
  font-size: 48rpx;
}

.wx-icon {
  color: #07c160;
}

.qq-icon {
  color: #12b7f5;
}

.wb-icon {
  color: #e6162d;
}

.social-text {
  font-size: 24rpx;
  color: #999;
}

/* 动画 */
@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5rpx);
  }
}
</style>
