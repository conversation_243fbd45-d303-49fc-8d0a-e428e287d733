{"name": "js-tokens", "version": "4.0.0", "author": "<PERSON>", "license": "MIT", "description": "A regex that tokenizes JavaScript.", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "files": ["index.js"], "repository": "lydell/js-tokens", "scripts": {"test": "mocha --ui tdd", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js", "build": "node generate-index.js", "dev": "npm run build && npm test"}, "devDependencies": {"coffeescript": "2.1.1", "esprima": "4.0.0", "everything.js": "1.0.3", "mocha": "5.0.0"}}