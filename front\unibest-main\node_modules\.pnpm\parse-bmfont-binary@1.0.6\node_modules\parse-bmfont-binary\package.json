{"name": "parse-bmfont-binary", "version": "1.0.6", "description": "reads a BMFont binary in a Buffer into a JSON object", "main": "index.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {}, "devDependencies": {"tape": "^3.5.0"}, "scripts": {"test": "node test/test.js"}, "keywords": ["bmfont", "bitmap", "font", "angel", "code", "angelcode", "fonts", "text", "layout", "glyph", "glyph<PERSON><PERSON><PERSON>", "canvas", "webgl"], "repository": {"type": "git", "url": "git://github.com/Jam3/parse-bmfont-binary.git"}, "homepage": "https://github.com/Jam3/parse-bmfont-binary", "bugs": {"url": "https://github.com/Jam3/parse-bmfont-binary/issues"}}