/**
 * @description AI回复生成器
 * 根据用户输入和AI助手类型生成回复
 */

import { getDemoAgentById } from './agentData'
import { createDemoChatMessage, ChatMessage } from './sessionData'

// 回复生成选项
export interface ReplyGeneratorOptions {
  streaming?: boolean
  delay?: number
  includeThinking?: boolean
}

// 回复生成器
export class ReplyGenerator {
  private static instance: ReplyGenerator

  // 私有构造函数，确保单例模式
  private constructor() {}

  // 获取单例实例
  public static getInstance(): ReplyGenerator {
    if (!ReplyGenerator.instance) {
      ReplyGenerator.instance = new ReplyGenerator()
    }
    return ReplyGenerator.instance
  }

  /**
   * 生成AI回复
   * @param userMessage 用户消息
   * @param agentId AI助手ID
   * @param options 生成选项
   * @returns 生成的AI回复消息
   */
  public generateReply(
    userMessage: string,
    agentId: string,
    options: ReplyGeneratorOptions = {},
  ): ChatMessage {
    // 获取AI助手信息
    const agent = getDemoAgentById(agentId)

    // 根据用户消息和AI助手类型生成回复
    const replyContent = this.getReplyContent(userMessage, agentId)

    // 生成思考过程
    const thinking = options.includeThinking
      ? this.generateThinking(userMessage, agentId)
      : undefined

    // 创建回复消息
    return createDemoChatMessage('assistant', replyContent, {
      think: thinking,
      showThink: false,
      isStreaming: options.streaming || false,
    })
  }

  /**
   * 生成流式回复
   * @param userMessage 用户消息
   * @param agentId AI助手ID
   * @param callback 回调函数，用于接收流式内容
   * @param options 生成选项
   * @returns 取消函数
   */
  public generateStreamingReply(
    userMessage: string,
    agentId: string,
    callback: (content: string, isComplete: boolean) => void,
    options: ReplyGeneratorOptions = {},
  ): () => void {
    // 获取完整回复内容
    const fullReply = this.getReplyContent(userMessage, agentId)

    // 将回复内容分成单词
    const words = fullReply.split(' ')

    // 当前已发送的内容
    let currentContent = ''

    // 当前单词索引
    let currentIndex = 0

    // 设置流式发送的间隔时间
    const delay = options.delay || 100

    // 设置定时器
    const timer = setInterval(() => {
      if (currentIndex < words.length) {
        // 添加下一个单词
        currentContent += (currentIndex > 0 ? ' ' : '') + words[currentIndex]
        currentIndex++

        // 回调通知
        callback(currentContent, false)
      } else {
        // 发送完成
        clearInterval(timer)
        callback(currentContent, true)
      }
    }, delay)

    // 返回取消函数
    return () => {
      clearInterval(timer)
    }
  }

  /**
   * 根据用户消息和AI助手类型获取回复内容
   * @param userMessage 用户消息
   * @param agentId AI助手ID
   * @returns 回复内容
   */
  private getReplyContent(userMessage: string, agentId: string): string {
    // 通用回复模板
    const generalReplies = [
      '感谢您的提问。{response}',
      '很高兴能帮到您。{response}',
      '{response}',
      '这是一个很好的问题。{response}',
      '我理解您的问题。{response}',
    ]

    // 根据不同的助手类型和用户消息生成回复
    let response = ''

    // 检查用户消息中的关键词
    const lowerMessage = userMessage.toLowerCase()

    if (
      lowerMessage.includes('你好') ||
      lowerMessage.includes('嗨') ||
      lowerMessage.includes('hi') ||
      lowerMessage.includes('hello')
    ) {
      response = this.getGreetingResponse(agentId)
    } else if (lowerMessage.includes('谢谢') || lowerMessage.includes('感谢')) {
      response = this.getThankYouResponse()
    } else if (
      lowerMessage.includes('再见') ||
      lowerMessage.includes('拜拜') ||
      lowerMessage.includes('bye')
    ) {
      response = this.getGoodbyeResponse()
    } else if (lowerMessage.includes('帮助') || lowerMessage.includes('help')) {
      response = this.getHelpResponse(agentId)
    } else {
      // 根据助手类型生成特定回复
      response = this.getAgentSpecificResponse(userMessage, agentId)
    }

    // 从通用模板中随机选择一个
    const template = generalReplies[Math.floor(Math.random() * generalReplies.length)]

    // 将回复插入模板
    return template.replace('{response}', response)
  }

  /**
   * 生成问候回复
   * @param agentId AI助手ID
   * @returns 问候回复
   */
  private getGreetingResponse(agentId: string): string {
    const agent = getDemoAgentById(agentId)
    const agentName = agent ? agent.name : 'AI助手'

    const greetings = [
      `你好！我是${agentName}，很高兴为你服务。有什么我可以帮助你的吗？`,
      `你好！欢迎使用${agentName}。我能为你做些什么？`,
      `嗨！我是${agentName}。今天有什么问题需要我帮忙解答吗？`,
      `你好！很高兴见到你。我是${agentName}，随时准备帮助你。`,
    ]

    return greetings[Math.floor(Math.random() * greetings.length)]
  }

  /**
   * 生成感谢回复
   * @returns 感谢回复
   */
  private getThankYouResponse(): string {
    const responses = [
      '不客气！如果还有其他问题，随时可以问我。',
      '很高兴能帮到你！有任何问题都可以随时咨询。',
      '不用谢，这是我的工作。希望我的回答对你有所帮助。',
      '客气了！能帮助你我很开心。',
    ]

    return responses[Math.floor(Math.random() * responses.length)]
  }

  /**
   * 生成再见回复
   * @returns 再见回复
   */
  private getGoodbyeResponse(): string {
    const responses = [
      '再见！祝你有愉快的一天。',
      '下次再见！如果有问题随时回来。',
      '再见！很高兴能帮到你。',
      '期待下次与你交流！再见。',
    ]

    return responses[Math.floor(Math.random() * responses.length)]
  }

  /**
   * 生成帮助回复
   * @param agentId AI助手ID
   * @returns 帮助回复
   */
  private getHelpResponse(agentId: string): string {
    const agent = getDemoAgentById(agentId)

    if (agent && agent.capabilities) {
      return (
        `我是${agent.name}，可以帮助你：\n\n` +
        agent.capabilities.map((cap) => `- ${cap}`).join('\n') +
        '\n\n你可以尝试问我一些相关问题，例如：\n\n' +
        (agent.examples ? agent.examples.map((ex) => `- ${ex}`).join('\n') : '')
      )
    }

    return '我是AI助手，可以回答你的问题、提供信息和帮助解决问题。请告诉我你需要什么帮助？'
  }

  /**
   * 根据助手类型生成特定回复
   * @param userMessage 用户消息
   * @param agentId AI助手ID
   * @returns 特定回复
   */
  private getAgentSpecificResponse(userMessage: string, agentId: string): string {
    // 根据不同的助手类型返回不同的回复
    const responses: Record<string, string[]> = {
      general: [
        '这是一个很好的问题。作为通用助手，我建议你考虑以下几点：首先，明确你的目标；其次，收集相关信息；最后，制定具体的行动计划。',
        '从我的理解来看，这个问题涉及多个方面。一方面，需要考虑实际情况；另一方面，也要兼顾长远发展。建议你综合考虑这些因素后再做决定。',
        '这个问题很常见。通常的解决方法是：1. 分析问题根源；2. 寻找可行的解决方案；3. 评估各方案的优缺点；4. 选择最适合的方案并执行。',
        '对于这个问题，没有一成不变的答案。不同的情况可能需要不同的处理方式。建议根据具体情况灵活应对。',
      ],
      code: [
        '从编程角度看，这个问题可以通过以下步骤解决：\n\n1. 分析需求\n2. 设计数据结构\n3. 编写算法\n4. 测试和优化\n\n你需要我详细解释哪一步吗？',
        '这是一个典型的编程挑战。解决它的关键是选择合适的数据结构和算法。考虑时间复杂度和空间复杂度，可能需要在两者之间做出权衡。',
        '在软件开发中，这种情况通常需要考虑代码的可维护性、可扩展性和性能。建议遵循SOLID原则，编写清晰、模块化的代码。',
        '这个问题在实际开发中很常见。可以考虑使用设计模式来解决，例如工厂模式、观察者模式或策略模式，具体取决于你的具体需求。',
      ],
      creative: [
        '从创意角度看，这个问题可以有多种解决方案。不妨尝试跳出常规思维，从不同的角度思考。有时候，最意想不到的想法可能带来最好的结果。',
        '创意过程中，灵感来源很重要。可以从自然、艺术、科技等各个领域寻找灵感。尝试将不同领域的元素结合，可能会产生独特的创意。',
        '在创意工作中，平衡原创性和实用性很重要。一个好的创意不仅要有新意，还要能解决实际问题或满足特定需求。',
        '创意不是一蹴而就的。它需要时间、耐心和持续的尝试。不要害怕失败，每次尝试都是学习的机会。',
      ],
      business: [
        '从商业角度分析，这个问题涉及市场定位、竞争策略和资源分配。建议先进行SWOT分析，明确自身优势和市场机会，然后制定相应的策略。',
        '在商业决策中，数据分析非常重要。建议收集相关数据，进行量化分析，以支持你的决策过程。同时，也要考虑定性因素，如品牌价值和客户体验。',
        '这个商业问题需要考虑短期利益和长期发展的平衡。有时候，为了长期成功，可能需要做出短期牺牲。关键是明确你的商业目标和价值观。',
        '在当前的商业环境中，创新和适应能力至关重要。建议保持对市场趋势的敏感，灵活调整策略，同时保持核心竞争力。',
      ],
      education: [
        '从教育角度看，这个问题涉及学习方法、知识结构和实践应用。建议采用主动学习的方式，结合理论学习和实践应用，加深理解和记忆。',
        '有效学习需要明确目标、合理规划和持续努力。建议制定详细的学习计划，分解大目标为小任务，逐步完成。同时，定期复习和反思也很重要。',
        '在学习过程中，理解概念比记忆事实更重要。尝试将新知识与已有知识联系起来，形成知识网络。这样不仅容易记忆，也便于应用。',
        '教育不仅是知识的传授，也是能力的培养。除了学习具体知识，也要注重培养批判性思维、问题解决能力和创新能力。',
      ],
    }

    const agentResponses = responses[agentId] || responses.general
    return agentResponses[Math.floor(Math.random() * agentResponses.length)]
  }

  /**
   * 生成思考过程
   * @param userMessage 用户消息
   * @param agentId AI助手ID
   * @returns 思考过程
   */
  private generateThinking(userMessage: string, agentId: string): string {
    // 根据不同的助手类型生成不同的思考过程
    const thinking: Record<string, string[]> = {
      general: [
        '分析用户问题：用户询问的是一般性问题，需要提供全面而客观的回答。\n\n考虑回答要点：\n1. 提供基本信息\n2. 考虑不同角度\n3. 给出实用建议\n\n组织回答结构：\n- 开头确认理解问题\n- 中间部分提供详细信息\n- 结尾给出具体建议',
        '理解用户需求：用户似乎需要关于日常问题的建议。\n\n思考回答策略：\n1. 提供实用信息\n2. 考虑用户可能的情境\n3. 给出可行的建议\n\n准备回答框架：\n- 首先确认问题\n- 然后提供多角度分析\n- 最后总结关键点',
      ],
      code: [
        '分析编程问题：这是一个关于软件开发的问题。\n\n考虑技术方面：\n1. 算法复杂度\n2. 代码可维护性\n3. 最佳实践\n\n准备回答：\n- 解释核心概念\n- 提供代码示例\n- 讨论优化方向',
        '理解技术需求：用户询问的是编程相关问题。\n\n思考解决方案：\n1. 考虑常用设计模式\n2. 评估不同实现方法\n3. 权衡性能和可读性\n\n组织回答：\n- 首先解释基本原理\n- 然后提供实现思路\n- 最后讨论潜在问题',
      ],
      creative: [
        '分析创意需求：用户寻求创意相关的建议。\n\n思考创意方向：\n1. 考虑原创性\n2. 评估实用性\n3. 思考创新点\n\n准备回答：\n- 提供灵感来源\n- 讨论创意发展过程\n- 建议实现方法',
        '理解创意挑战：这是一个关于创意和创作的问题。\n\n考虑创意元素：\n1. 独特性\n2. 情感共鸣\n3. 表达方式\n\n组织回答：\n- 首先肯定创意价值\n- 然后提供具体建议\n- 最后鼓励创新尝试',
      ],
      business: [
        '分析商业问题：用户询问的是商业策略相关问题。\n\n考虑商业因素：\n1. 市场分析\n2. 竞争策略\n3. 资源分配\n\n准备回答：\n- 提供市场洞察\n- 讨论策略选择\n- 建议实施步骤',
        '理解商业需求：这是一个关于商业决策的问题。\n\n思考商业角度：\n1. 短期收益\n2. 长期发展\n3. 风险管理\n\n组织回答：\n- 首先分析市场情况\n- 然后讨论可行策略\n- 最后提供具体建议',
      ],
      education: [
        '分析学习问题：用户询问的是学习方法相关问题。\n\n考虑教育因素：\n1. 学习风格\n2. 知识结构\n3. 实践应用\n\n准备回答：\n- 解释学习原理\n- 提供学习方法\n- 建议实践活动',
        '理解教育需求：这是一个关于学习和教育的问题。\n\n思考教育角度：\n1. 概念理解\n2. 技能培养\n3. 知识应用\n\n组织回答：\n- 首先解释基本概念\n- 然后提供学习策略\n- 最后建议实践方法',
      ],
    }

    const agentThinking = thinking[agentId] || thinking.general
    return agentThinking[Math.floor(Math.random() * agentThinking.length)]
  }
}

// 导出单例实例
export const replyGenerator = ReplyGenerator.getInstance()

// 导出默认实例
export default replyGenerator
