<template>
  <scroll-view 
    class="virtual-scroll"
    :style="{ height: containerHeight + 'px' }"
    scroll-y
    :scroll-top="scrollTop"
    @scroll="handleScroll"
    :enhanced="true"
  >
    <!-- 上方占位 -->
    <view :style="{ height: topPlaceholderHeight + 'px' }"></view>
    
    <!-- 可见项目 -->
    <view 
      v-for="(item, index) in visibleItems" 
      :key="getItemKey(item, startIndex + index)"
      :style="{ height: itemHeight + 'px' }"
      class="virtual-item"
    >
      <slot :item="item" :index="startIndex + index"></slot>
    </view>
    
    <!-- 下方占位 -->
    <view :style="{ height: bottomPlaceholderHeight + 'px' }"></view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  containerHeight: number
  overscan?: number
  getItemKey?: (item: any, index: number) => string | number
}

const props = withDefaults(defineProps<Props>(), {
  overscan: 5,
  getItemKey: (item: any, index: number) => index
})

const scrollTop = ref(0)
const isScrolling = ref(false)
let scrollTimer: number | null = null

// 计算可见范围
const visibleRange = computed(() => {
  const start = Math.floor(scrollTop.value / props.itemHeight)
  const visibleCount = Math.ceil(props.containerHeight / props.itemHeight)
  
  // 添加overscan缓冲区
  const startIndex = Math.max(0, start - props.overscan)
  const endIndex = Math.min(
    props.items.length - 1,
    start + visibleCount + props.overscan
  )
  
  return { startIndex, endIndex }
})

const startIndex = computed(() => visibleRange.value.startIndex)
const endIndex = computed(() => visibleRange.value.endIndex)

// 可见项目
const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

// 占位高度
const topPlaceholderHeight = computed(() => {
  return startIndex.value * props.itemHeight
})

const bottomPlaceholderHeight = computed(() => {
  const remainingItems = props.items.length - endIndex.value - 1
  return Math.max(0, remainingItems * props.itemHeight)
})

// 总高度
const totalHeight = computed(() => {
  return props.items.length * props.itemHeight
})

// 滚动处理
const handleScroll = (e: any) => {
  scrollTop.value = e.detail.scrollTop
  
  // 标记正在滚动
  isScrolling.value = true
  
  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
  
  // 设置滚动结束检测
  scrollTimer = setTimeout(() => {
    isScrolling.value = false
    scrollTimer = null
  }, 150)
}

// 滚动到指定项目
const scrollToItem = (index: number, alignment: 'start' | 'center' | 'end' = 'start') => {
  let targetScrollTop = 0
  
  switch (alignment) {
    case 'start':
      targetScrollTop = index * props.itemHeight
      break
    case 'center':
      targetScrollTop = index * props.itemHeight - props.containerHeight / 2 + props.itemHeight / 2
      break
    case 'end':
      targetScrollTop = index * props.itemHeight - props.containerHeight + props.itemHeight
      break
  }
  
  // 确保滚动位置在有效范围内
  targetScrollTop = Math.max(0, Math.min(targetScrollTop, totalHeight.value - props.containerHeight))
  
  scrollTop.value = targetScrollTop
}

// 获取当前可见的第一个项目索引
const getFirstVisibleIndex = () => {
  return Math.floor(scrollTop.value / props.itemHeight)
}

// 获取当前可见的最后一个项目索引
const getLastVisibleIndex = () => {
  const firstVisible = getFirstVisibleIndex()
  const visibleCount = Math.ceil(props.containerHeight / props.itemHeight)
  return Math.min(props.items.length - 1, firstVisible + visibleCount - 1)
}

// 检查项目是否可见
const isItemVisible = (index: number) => {
  return index >= startIndex.value && index <= endIndex.value
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  getFirstVisibleIndex,
  getLastVisibleIndex,
  isItemVisible,
  scrollTop: () => scrollTop.value,
  isScrolling: () => isScrolling.value
})

// 监听items变化，自动调整滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength > oldLength) {
    // 新增项目时，如果用户在底部，自动滚动到底部
    const isAtBottom = scrollTop.value >= totalHeight.value - props.containerHeight - 100
    if (isAtBottom) {
      setTimeout(() => {
        scrollTop.value = totalHeight.value - props.containerHeight
      }, 50)
    }
  }
})

onUnmounted(() => {
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})
</script>

<style lang="scss" scoped>
.virtual-scroll {
  position: relative;
  overflow: hidden;
  
  // 优化滚动性能
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
  contain: layout style paint;
}

.virtual-item {
  position: relative;
  
  // 防止内容溢出
  overflow: hidden;
  
  // 优化渲染性能
  contain: layout style paint;
  will-change: transform;
}

// 滚动条样式优化
.virtual-scroll::-webkit-scrollbar {
  width: 6px;
}

.virtual-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}
</style>
