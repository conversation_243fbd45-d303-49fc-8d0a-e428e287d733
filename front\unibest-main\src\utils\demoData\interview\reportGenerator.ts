/**
 * @description 面试报告演示数据生成器
 * 提供面试结束后的报告数据生成功能
 */

import { demoScoringStandards, demoReportTemplate } from '@/pages/interview/demo-data'
import { getDemoQuestion } from './sessionData'
import {
  createBaseMultiModalMetrics,
  generateFluctuatingMultiModalMetrics,
} from './analysisGenerator'

// 面试报告接口
export interface InterviewReport {
  sessionId: string
  candidateName: string
  jobTitle: string
  company: string
  date: string
  duration: number
  overallScore: number
  recommendation: string
  level: string
  highlights: string[]
  improvements: string[]
  questionScores: Array<{
    id: number
    question: string
    score: number
    feedback: string
    keywords: {
      matched: string[]
      missed: string[]
    }
  }>
  skillAnalysis: {
    technicalSkills: {
      score: number
      strengths: string[]
      weaknesses: string[]
    }
    softSkills: {
      score: number
      strengths: string[]
      weaknesses: string[]
    }
  }
  performanceMetrics: {
    speech: {
      clarity: number
      fluency: number
      emotion: number
      pace: number
      logic: number
      volume: number
      pitch: number
      pauseFrequency: number
    }
    video: {
      eyeContact: number
      expression: number
      gesture: number
      posture: number
      confidence: number
      microExpressions: number
      headMovement: number
      handGestures: number
    }
    text: {
      structure: number
      relevance: number
      depth: number
      keywords: number
      grammar: number
      coherence: number
      completeness: number
      starMethod: number
    }
  }
  emotionAnalysis: {
    dominant: string
    trend: Array<{
      time: number
      emotions: Record<string, number>
    }>
  }
  suggestions: string[]
}

/**
 * 生成面试报告
 * @param sessionId 会话ID
 * @param params 其他参数
 * @returns 面试报告
 */
export const generateInterviewReport = (sessionId: string, params?: any): InterviewReport => {
  // 基础信息
  const candidateName = params?.candidateName || '候选人'
  const jobTitle = params?.jobTitle || '前端开发工程师'
  const company = params?.company || '创新科技有限公司'
  const date = params?.date || new Date().toISOString().split('T')[0]
  const duration = params?.duration || Math.floor(Math.random() * 600) + 1200 // 20-30分钟

  // 生成总体评分 (70-95)
  const overallScore = Math.floor(Math.random() * 25) + 70

  // 确定评级
  let level = 'poor'
  let recommendation = '建议不录用'

  if (overallScore >= demoScoringStandards.excellent.min) {
    level = 'excellent'
    recommendation = '强烈建议录用'
  } else if (overallScore >= demoScoringStandards.good.min) {
    level = 'good'
    recommendation = '建议录用'
  } else if (overallScore >= demoScoringStandards.average.min) {
    level = 'average'
    recommendation = '可以考虑'
  }

  // 生成问题评分
  const questionIds = params?.questionIds || [1, 2, 3, 4, 5]
  const questionScores = questionIds
    .map((id: number) => {
      const question = getDemoQuestion(id)
      if (!question) return null

      // 生成问题得分 (60-100)
      const score = Math.floor(Math.random() * 40) + 60

      // 生成关键词匹配
      const allKeywords = [...(question.expectedKeywords || [])]
      const matchedCount = Math.floor(Math.random() * (allKeywords.length + 1))
      const matched = allKeywords.slice(0, matchedCount)
      const missed = allKeywords.slice(matchedCount)

      // 生成反馈
      let feedback = ''
      if (score >= 90) {
        feedback = '回答非常出色，展示了深厚的专业知识和丰富的实践经验。'
      } else if (score >= 80) {
        feedback = '回答良好，展示了扎实的专业知识和一定的实践经验。'
      } else if (score >= 70) {
        feedback = '回答基本合格，但在某些方面还需要加强。'
      } else {
        feedback = '回答不够理想，需要在专业知识和实践经验方面进一步提升。'
      }

      return {
        id,
        question: question.question,
        score,
        feedback,
        keywords: {
          matched,
          missed,
        },
      }
    })
    .filter(Boolean)

  // 生成技能分析
  const technicalScore = Math.floor(Math.random() * 30) + 70
  const softScore = Math.floor(Math.random() * 30) + 70

  const technicalStrengths = [
    '前端框架掌握扎实',
    '代码质量高',
    '性能优化意识强',
    '工程化经验丰富',
    '技术视野开阔',
  ]
    .sort(() => Math.random() - 0.5)
    .slice(0, 2 + Math.floor(Math.random() * 2))

  const technicalWeaknesses = [
    '架构设计能力有待提升',
    '对新技术了解不够深入',
    '测试覆盖率不足',
    '安全意识有待加强',
    '代码复用性可以提高',
  ]
    .sort(() => Math.random() - 0.5)
    .slice(0, 1 + Math.floor(Math.random() * 2))

  const softStrengths = [
    '沟通表达能力强',
    '逻辑思维清晰',
    '学习能力突出',
    '团队协作意识强',
    '解决问题能力强',
  ]
    .sort(() => Math.random() - 0.5)
    .slice(0, 2 + Math.floor(Math.random() * 2))

  const softWeaknesses = [
    '领导力有待提升',
    '创新思维可以加强',
    '抗压能力需要提高',
    '时间管理需要改进',
    '项目管理经验不足',
  ]
    .sort(() => Math.random() - 0.5)
    .slice(0, 1 + Math.floor(Math.random() * 2))

  // 生成亮点和改进点
  const highlights = [...technicalStrengths, ...softStrengths]
    .sort(() => Math.random() - 0.5)
    .slice(0, 3)

  const improvements = [...technicalWeaknesses, ...softWeaknesses]
    .sort(() => Math.random() - 0.5)
    .slice(0, 2)

  // 生成表现指标
  const baseMetrics = createBaseMultiModalMetrics()
  const performanceMetrics = generateFluctuatingMultiModalMetrics(baseMetrics, 10)

  // 生成情绪分析
  const emotions = ['happy', 'neutral', 'sad', 'angry', 'surprised']
  const dominantEmotion = emotions[Math.floor(Math.random() * 2)] // 主要是happy或neutral

  const emotionTrend = []
  const startTime = Date.now() - duration * 1000
  const steps = 10

  for (let i = 0; i < steps; i++) {
    const time = startTime + ((duration * 1000) / steps) * i
    const emotionData: Record<string, number> = {}

    // 生成各情绪占比，确保总和为100
    let remaining = 100
    for (let j = 0; j < emotions.length - 1; j++) {
      const emotion = emotions[j]
      const value =
        j === 0 && emotion === dominantEmotion
          ? Math.floor(Math.random() * 30) + 50 // 主要情绪占50-80%
          : Math.floor(Math.random() * Math.min(20, remaining))

      emotionData[emotion] = value
      remaining -= value
    }

    // 最后一个情绪占剩余比例
    emotionData[emotions[emotions.length - 1]] = remaining

    emotionTrend.push({
      time,
      emotions: emotionData,
    })
  }

  // 生成建议
  const suggestions = [
    '建议加强前端架构设计能力的学习',
    '可以参加更多技术交流活动，拓展技术视野',
    '建议系统学习性能优化相关知识',
    '可以尝试参与开源项目，提升代码质量和团队协作能力',
    '建议加强软技能培养，特别是沟通表达和项目管理能力',
    '可以关注前端新技术发展趋势，保持技术敏感度',
  ]
    .sort(() => Math.random() - 0.5)
    .slice(0, 3)

  // 组装报告
  return {
    sessionId,
    candidateName,
    jobTitle,
    company,
    date,
    duration,
    overallScore,
    recommendation,
    level,
    highlights,
    improvements,
    questionScores,
    skillAnalysis: {
      technicalSkills: {
        score: technicalScore,
        strengths: technicalStrengths,
        weaknesses: technicalWeaknesses,
      },
      softSkills: {
        score: softScore,
        strengths: softStrengths,
        weaknesses: softWeaknesses,
      },
    },
    performanceMetrics,
    emotionAnalysis: {
      dominant: dominantEmotion,
      trend: emotionTrend,
    },
    suggestions,
  }
}

/**
 * 生成面试报告摘要
 * @param report 完整面试报告
 * @returns 面试报告摘要
 */
export const generateReportSummary = (report: InterviewReport) => {
  return {
    sessionId: report.sessionId,
    candidateName: report.candidateName,
    jobTitle: report.jobTitle,
    company: report.company,
    date: report.date,
    overallScore: report.overallScore,
    recommendation: report.recommendation,
    level: report.level,
    highlights: report.highlights.slice(0, 2),
    improvements: report.improvements.slice(0, 1),
  }
}

export default {
  generateInterviewReport,
  generateReportSummary,
}
