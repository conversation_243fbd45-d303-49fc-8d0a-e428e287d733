<!--
/**
 * @component LoadingCard
 * @description 通用加载状态组件 - 现代化毛玻璃风格设计
 * <AUTHOR>
 * @version 1.0.0
 * 
 * @example 基础用法
 * <LoadingCard :visible="isLoading" />
 * 
 * @example 自定义文本
 * <LoadingCard :visible="isLoading" text="正在加载数据..." />
 * 
 * @example 非全屏模式（相对定位）
 * <LoadingCard :visible="isLoading" :fullscreen="false" />
 * 
 * @example 完整配置
 * <LoadingCard 
 *   :visible="isLoading" 
 *   text="请稍候..." 
 *   :fullscreen="true"
 *   :background-opacity="0.9"
 * />
 * 
-->

<script setup lang="ts">
interface Props {
  /** 是否显示加载状态 */
  visible?: boolean
  /** 加载提示文本 */
  text?: string
  /** 背景透明度 (0-1) */
  backgroundOpacity?: number
  /** 是否全屏遮罩 */
  fullscreen?: boolean
}

/**
 * @description 定义组件属性
 */
const props = withDefaults(defineProps<Props>(), {
  visible: false,
  text: '正在加载...',
  backgroundOpacity: 0.95,
  fullscreen: true,
})
</script>

<template>
  <!-- 加载状态遮罩 -->
  <view v-if="props.visible" class="loading-container" :class="{ fullscreen: props.fullscreen }">
    <view class="loading-content">
      <view class="loading-spinner">
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
      </view>
      <text class="loading-text">{{ props.text }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 加载状态容器 - 现代化毛玻璃设计
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    rgba(245, 247, 255, 0.95) 0%,
    rgba(232, 244, 248, 0.95) 50%,
    rgba(240, 248, 255, 0.95) 100%
  );
  backdrop-filter: blur(20rpx) saturate(150%);
  -webkit-backdrop-filter: blur(20rpx) saturate(150%);
  z-index: 999;
  transition: all 0.3s ease-in-out;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

// 加载内容卡片
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  padding: 80rpx 60rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(25rpx) saturate(180%);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 32rpx;
  box-shadow:
    0 24rpx 60rpx rgba(0, 201, 167, 0.15),
    0 12rpx 40rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
  animation: fadeInScale 0.3s ease-out;
  position: relative;
  overflow: hidden;

  // 装饰性背景元素
  &::before {
    content: '';
    position: absolute;
    top: -20rpx;
    right: -20rpx;
    width: 80rpx;
    height: 80rpx;
    background: radial-gradient(circle, rgba(0, 201, 167, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 4s ease-in-out infinite;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -15rpx;
    left: -15rpx;
    width: 60rpx;
    height: 60rpx;
    background: radial-gradient(circle, rgba(79, 209, 199, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 3s ease-in-out infinite reverse;
    pointer-events: none;
  }
}

// 加载动画容器
.loading-spinner {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
}

// 加载点动画
.spinner-dot {
  width: 16rpx;
  height: 16rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 50%;
  animation: spinnerBounce 1.4s ease-in-out infinite both;
  box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }

  &:nth-child(3) {
    animation-delay: 0s;
  }
}

// 加载文本
.loading-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  text-align: center;
  letter-spacing: 1rpx;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

// 动画定义
@keyframes spinnerBounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(5deg);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .loading-content {
    padding: 60rpx 40rpx;
    border-radius: 24rpx;
    gap: 24rpx;
  }

  .loading-text {
    font-size: 26rpx;
  }

  .spinner-dot {
    width: 14rpx;
    height: 14rpx;
  }
}

// 小程序端优化
.loading-container {
  backdrop-filter: blur(15rpx) saturate(130%);
  -webkit-backdrop-filter: blur(15rpx) saturate(130%);
}

.loading-content {
  backdrop-filter: blur(20rpx) saturate(160%);
  -webkit-backdrop-filter: blur(20rpx) saturate(160%);
}

// H5端优化
// #ifdef H5
.loading-container {
  backdrop-filter: blur(25rpx) saturate(180%);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
}

.loading-content {
  backdrop-filter: blur(30rpx) saturate(200%);
  -webkit-backdrop-filter: blur(30rpx) saturate(200%);
}
// #endif

// 暗黑模式支持
@media (prefers-color-scheme: dark) {
  .loading-container {
    background: linear-gradient(
      135deg,
      rgba(26, 26, 46, 0.95) 0%,
      rgba(22, 33, 62, 0.95) 50%,
      rgba(15, 52, 96, 0.95) 100%
    );
  }

  .loading-content {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(248, 250, 252, 0.05) 100%
    );
    border: 1rpx solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 24rpx 60rpx rgba(0, 0, 0, 0.4),
      0 12rpx 40rpx rgba(0, 201, 167, 0.1),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  }

  .loading-text {
    color: rgba(255, 255, 255, 0.9);
  }
}

// 减少动画以提升性能（可选）
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
