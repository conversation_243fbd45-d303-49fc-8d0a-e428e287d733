/**
 * 学习模块类型定义
 */

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

/**
 * 专业信息
 */
export interface Major {
  id: string
  name: string
  icon?: string
  color?: string
  count?: number
}

/**
 * 题库信息
 */
export interface QuestionBank {
  id: string
  code: string
  title: string
  description: string
  icon?: string
  cover?: string
  questionCount: number
  practiceCount: number
  difficulty: string
  categories: string[]
  majorId: string
  majorName: string
  isBookmarked: boolean
  progress: number
  status: string
  createTime: string
  updateTime: string
}

/**
 * 题库VO
 */
export interface QuestionBankVO {
  id: string
  title: string
  description: string
  icon?: string
  color?: string
  difficulty: string
  totalQuestions: number
  practiceCount: number
  progress: number
  categories: string[]
  isBookmarked: boolean
  majorId: string
  majorName: string
  createdAt: string
  updatedAt: string
}

/**
 * 题目信息
 */
export interface Question {
  id: string
  title: string
  content?: string
  description?: string
  difficulty: string
  type: string
  practiceCount: number
  correctRate: number
  commentCount: number
  category: string
  tags?: string[]
  acceptanceRate?: number
  isCompleted?: boolean
  isBookmarked?: boolean
  createTime: string
  estimatedTime?: number
  bankId?: string
  bankTitle?: string
}

/**
 * 题目详情
 */
export interface QuestionDetail extends Question {
  options?: QuestionOption[]
  answer?: string
  explanation?: string
  references?: string[]
  relatedQuestions?: Question[]
}

/**
 * 题目选项
 */
export interface QuestionOption {
  key: string
  value: string
  isCorrect?: boolean
}

/**
 * 题目评论
 */
export interface QuestionComment {
  id: string
  questionId: string
  userId: string
  userName: string
  userAvatar?: string
  content: string
  parentId?: string
  children?: QuestionComment[]
  likeCount: number
  isLiked: boolean
  createTime: string
}

/**
 * 题库列表查询参数
 */
export interface QuestionBankListParams {
  page?: number
  pageSize?: number
  keyword?: string
  majorId?: string
  category?: string
  difficulty?: string
  filter?: string
  status?: string
  orderBy?: string
  orderDirection?: string
}

export interface QuestionQueryParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  difficulty?: string
  category?: string
  completed?: boolean
  orderBy?: string
  orderDirection?: string
}

/**
 * 题库列表响应
 */
export interface QuestionBankListResponse {
  list: QuestionBankVO[]
  total: number
  page: number
  pageSize: number
}

export interface QuestionListResponse {
  list: Question[]
  total: number
  page: number
  pageSize: number
}

/**
 * 统计信息
 */
export interface QuestionStatistics {
  totalQuestions: number
  easyCount: number
  mediumCount: number
  hardCount: number
  averageCorrectRate: number
}

export interface QuestionBankStatistics {
  totalBanks: number
  totalQuestions: number
  completedQuestions: number
  bookmarkedBanks: number
  recentPractice: number
}

/**
 * 切换收藏参数
 */
export interface ToggleBookmarkParams {
  bankId?: string
  questionId?: string
  isBookmarked: boolean
}

/**
 * 书籍信息
 */
export interface Book {
  id: number
  title: string
  author: string
  cover: string
  category: string
  rating: number
  readCount: number
  chapters: number
  pages: number
  updateTime: string
  isCompleted: boolean
  tags: string[]
  description: string
  difficulty: string
  price: number
  isFree: boolean
}

/**
 * 章节信息
 */
export interface BookChapter {
  id: number
  bookId: number
  title: string
  content: string
  order: number
  wordCount: number
  readTime: number
  isUnlocked: boolean
  isCompleted: boolean
}

/**
 * 阅读记录
 */
export interface ReadingRecord {
  id: number
  bookId: number
  chapterId: number
  progress: number
  readTime: number
  lastReadTime: string
  bookmarkPosition?: number
}

/**
 * 阅读设置
 */
export interface ReadingSettings {
  fontSize: number
  fontFamily: string
  backgroundColor: string
  textColor: string
  lineHeight: number
  theme: 'light' | 'dark' | 'sepia'
}

/**
 * 书籍列表查询参数
 */
export interface BookListParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  difficulty?: string
  isFree?: boolean
  orderBy?: string
  orderDirection?: string
}

/**
 * 书籍列表响应
 */
export interface BookListResponse {
  list: Book[]
  total: number
  page: number
  pageSize: number
}

/**
 * 章节列表响应
 */
export interface ChapterListResponse {
  list: BookChapter[]
  total: number
}

/**
 * 书籍详情
 */
export interface BookDetail extends Book {
  chapterList?: BookChapter[]
  readingRecord?: ReadingRecord
  relatedBooks?: Book[]
}

/**
 * 阅读统计
 */
export interface ReadingStats {
  totalBooks: number
  completedBooks: number
  totalPages: number
  readToday: number
  readingStreak: number
  totalReadTime: number
}

/**
 * 分类选项
 */
export interface CategoryOption {
  key: string
  name: string
  icon: string
}

export interface BookmarkResponse {
  isBookmarked: boolean
  message: string
}

/**
 * 练习相关参数
 */
export interface PracticeParams {
  questionId: string
  bankId: string
  majorId?: string
}

export interface PracticeResponse {
  practiceId: string
  question: QuestionDetail
}

/**
 * 提交答案参数
 */
export interface SubmitAnswerParams {
  practiceId: string
  questionId: string
  answer: string
  timeSpent: number
}

export interface SubmitAnswerResponse {
  isCorrect: boolean
  correctAnswer: string
  explanation: string
  score: number
  totalScore: number
}

/**
 * 学习进度
 */
export interface LearningProgress {
  bankId: string
  totalQuestions: number
  completedQuestions: number
  correctAnswers: number
  progress: number
  lastStudyTime: string
}

/**
 * 用户学习统计
 */
export interface UserLearningStats {
  totalStudyTime: number
  completedQuestions: number
  correctRate: number
  continuousDays: number
  totalBanks: number
  completedBanks: number
  bookmarkedQuestions: number
  recentActivity: RecentActivity[]
}

/**
 * 学习数据中心 - 详细统计数据
 */
export interface LearningDataCenterStats {
  // 基础统计
  totalStudyTime: number // 总学习时长（分钟）
  todayStudyTime: number // 今日学习时长（分钟）
  weeklyStudyTime: number // 本周学习时长（分钟）
  monthlyStudyTime: number // 本月学习时长（分钟）

  // 题目统计
  totalQuestions: number // 总做题数
  completedQuestions: number // 已完成题目数
  correctAnswers: number // 正确答题数
  averageCorrectRate: number // 平均正确率

  // 连续学习
  currentStreak: number // 当前连续学习天数
  longestStreak: number // 最长连续学习天数

  // 学习进度
  totalBooks: number // 总书籍数
  completedBooks: number // 已完成书籍数
  totalCourses: number // 总课程数
  completedCourses: number // 已完成课程数

  // 收藏与书签
  bookmarkedQuestions: number // 收藏的题目数
  bookmarkedBooks: number // 收藏的书籍数
  bookmarkedCourses: number // 收藏的课程数

  // 学习排名
  globalRank: number // 全球排名
  weeklyRank: number // 周排名

  // 更新时间
  lastUpdated: string
}

/**
 * 学习记录项
 */
export interface LearningRecord {
  id: string
  type: 'question' | 'book' | 'video' | 'course' // 学习类型
  title: string // 标题
  description?: string // 描述
  duration: number // 学习时长（分钟）
  progress: number // 进度百分比
  status: 'completed' | 'in_progress' | 'paused' // 状态
  score?: number // 得分（题目类型）
  correctRate?: number // 正确率（题目类型）
  difficulty?: string // 难度
  category: string // 分类
  tags?: string[] // 标签
  createdAt: string // 开始时间
  updatedAt: string // 最后更新时间
  completedAt?: string // 完成时间
}

/**
 * 学习记录查询参数
 */
export interface LearningRecordQueryParams {
  page?: number
  pageSize?: number
  type?: 'question' | 'book' | 'video' | 'course'
  status?: 'completed' | 'in_progress' | 'paused'
  startDate?: string
  endDate?: string
  keyword?: string
  category?: string
  orderBy?: 'createdAt' | 'updatedAt' | 'duration' | 'score'
  orderDirection?: 'asc' | 'desc'
}

/**
 * 学习记录列表响应
 */
export interface LearningRecordListResponse {
  list: LearningRecord[]
  total: number
  page: number
  pageSize: number
  summary: {
    totalDuration: number
    avgScore: number
    avgCorrectRate: number
    completedCount: number
    inProgressCount: number
  }
}

/**
 * 学习分析数据
 */
export interface LearningAnalysis {
  // 时间分析
  timeDistribution: {
    hourly: Array<{ hour: number; duration: number }> // 每小时学习时长分布
    daily: Array<{ date: string; duration: number }> // 每日学习时长
    weekly: Array<{ week: string; duration: number }> // 每周学习时长
    monthly: Array<{ month: string; duration: number }> // 每月学习时长
  }

  // 学科分析
  subjectDistribution: Array<{
    subject: string
    questionCount: number
    correctRate: number
    avgDuration: number
    color: string
  }>

  // 难度分析
  difficultyDistribution: Array<{
    difficulty: string
    questionCount: number
    correctRate: number
    avgDuration: number
  }>

  // 学习趋势
  learningTrend: {
    studyTimeProgress: Array<{ date: string; time: number; target: number }>
    correctRateProgress: Array<{ date: string; rate: number; target: number }>
    streakProgress: Array<{ date: string; streak: number }>
  }

  // 薄弱环节
  weakAreas: Array<{
    category: string
    correctRate: number
    questionCount: number
    recommendations: string[]
  }>
}

/**
 * AI学习建议
 */
export interface AiLearningAdvice {
  id: string
  type: 'study_plan' | 'weakness_improvement' | 'motivation' | 'time_management'
  title: string
  content: string
  priority: 'high' | 'medium' | 'low'
  actionItems: Array<{
    action: string
    description: string
    estimatedTime: number // 预估所需时间（分钟）
    difficulty: string
  }>
  createdAt: string
  isRead: boolean
  isImplemented?: boolean
}

/**
 * 学习目标
 */
export interface LearningGoal {
  id: string
  title: string
  description: string
  type: 'daily' | 'weekly' | 'monthly' | 'custom'
  target: number // 目标值
  current: number // 当前进度
  unit: 'minutes' | 'questions' | 'books' | 'courses' // 单位
  deadline?: string // 截止日期
  status: 'active' | 'completed' | 'paused' | 'failed'
  createdAt: string
  completedAt?: string
}

/**
 * 学习成就
 */
export interface LearningAchievement {
  id: string
  title: string
  description: string
  icon: string
  type: 'time' | 'question' | 'streak' | 'accuracy' | 'completion'
  requirement: number // 达成要求
  progress: number // 当前进度
  isUnlocked: boolean
  unlockedAt?: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  reward?: {
    type: 'points' | 'badge' | 'feature'
    value: number | string
  }
}

/**
 * AI对话历史
 */
export interface AiChatHistory {
  id: string
  sessionId: string
  messages: Array<{
    role: 'user' | 'assistant'
    content: string
    timestamp: string
    metadata?: {
      type?: 'analysis' | 'suggestion' | 'explanation'
      relatedData?: any
    }
  }>
  topic: string
  createdAt: string
  updatedAt: string
}

export interface RecentActivity {
  id: string
  type: 'practice' | 'complete' | 'bookmark'
  title: string
  description: string
  time: string
}

/**
 * 推荐相关参数
 */
export interface RecommendationParams {
  userId?: string
  majorId?: string
  difficulty?: string
  limit?: number
}

/**
 * 错误处理
 */
export interface LearningError {
  code: string
  message: string
  details?: any
}

/**
 * 筛选选项
 */
export interface FilterOption {
  key: string
  label: string
  count?: number
}

export interface SortOption {
  key: string
  label: string
}

/**
 * 分页信息
 */
export interface PaginationInfo {
  current: number
  pageSize: number
  total: number
  hasMore: boolean
}

/**
 * 搜索历史
 */
export interface SearchHistory {
  id: string
  keyword: string
  count: number
  lastSearchTime: string
}

/**
 * 导出所有类型的联合类型
 */
export type LearningModuleTypes =
  | Major
  | QuestionBank
  | Question
  | QuestionDetail
  | QuestionComment
  | QuestionBankListParams
  | QuestionQueryParams
  | QuestionBankListResponse
  | QuestionListResponse
  | QuestionStatistics
  | QuestionBankStatistics
  | ToggleBookmarkParams
  | BookmarkResponse
  | PracticeParams
  | PracticeResponse
  | SubmitAnswerParams
  | SubmitAnswerResponse
  | LearningProgress
  | UserLearningStats
  | RecentActivity
  | RecommendationParams
  | LearningError
  | FilterOption
  | SortOption
  | PaginationInfo
  | SearchHistory
  | Book
  | BookChapter
  | ReadingRecord
  | ReadingSettings
  | BookListParams
  | BookListResponse
  | ChapterListResponse
  | BookDetail
  | ReadingStats
  | CategoryOption
