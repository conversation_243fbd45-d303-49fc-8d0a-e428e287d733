/**
 * @description 面试实时分析演示数据生成器
 * 提供面试过程中的实时分析数据，包括情绪分析、多模态分析等
 */

// 情绪数据接口
export interface EmotionData {
  happy: number
  neutral: number
  sad: number
  angry: number
  surprised: number
  [key: string]: number
}

// 多模态分析数据接口
export interface MultiModalMetrics {
  speech: {
    clarity: number
    fluency: number
    emotion: number
    pace: number
    logic: number
    volume: number
    pitch: number
    pauseFrequency: number
  }
  video: {
    eyeContact: number
    expression: number
    gesture: number
    posture: number
    confidence: number
    microExpressions: number
    headMovement: number
    handGestures: number
  }
  text: {
    structure: number
    relevance: number
    depth: number
    keywords: number
    grammar: number
    coherence: number
    completeness: number
    starMethod: number
  }
}

// 实时分析结果接口
export interface RealTimeAnalysis {
  keywordMatches: string[]
  emotionScore: number
  microExpressions: string[]
  attentionLevel: number
  wordCount: number
  sentiment: number
  currentTranscript: string
}

// 创建基础情绪数据
export const createBaseEmotionData = (): EmotionData => {
  return {
    happy: 78,
    neutral: 12,
    sad: 5,
    angry: 2,
    surprised: 3,
  }
}

// 创建基础多模态分析数据
export const createBaseMultiModalMetrics = (): MultiModalMetrics => {
  return {
    speech: {
      clarity: 85,
      fluency: 78,
      emotion: 82,
      pace: 75,
      logic: 80,
      volume: 75,
      pitch: 80,
      pauseFrequency: 70,
    },
    video: {
      eyeContact: 78,
      expression: 90,
      gesture: 73,
      posture: 85,
      confidence: 82,
      microExpressions: 75,
      headMovement: 80,
      handGestures: 70,
    },
    text: {
      structure: 70,
      relevance: 85,
      depth: 75,
      keywords: 80,
      grammar: 92,
      coherence: 78,
      completeness: 82,
      starMethod: 65,
    },
  }
}

// 创建基础实时分析结果
export const createBaseRealTimeAnalysis = (): RealTimeAnalysis => {
  return {
    keywordMatches: [],
    emotionScore: 0,
    microExpressions: [],
    attentionLevel: 0,
    wordCount: 0,
    sentiment: 0,
    currentTranscript: '',
  }
}

/**
 * 生成波动的数值
 * @param baseValue 基础值
 * @param amplitude 波动幅度
 * @param min 最小值
 * @param max 最大值
 * @returns 波动后的数值
 */
export const generateFluctuatingValue = (
  baseValue: number,
  amplitude: number = 10,
  min: number = 0,
  max: number = 100,
): number => {
  const fluctuation = (Math.random() - 0.5) * 2 * amplitude
  return Math.max(min, Math.min(max, baseValue + fluctuation))
}

/**
 * 生成波动的情绪数据
 * @param baseEmotions 基础情绪数据
 * @param amplitude 波动幅度
 * @returns 波动后的情绪数据
 */
export const generateFluctuatingEmotions = (
  baseEmotions: EmotionData = createBaseEmotionData(),
  amplitude: number = 10,
): EmotionData => {
  const emotions: EmotionData = { ...baseEmotions }

  // 为每种情绪生成波动值
  Object.keys(emotions).forEach((emotion) => {
    emotions[emotion] = generateFluctuatingValue(emotions[emotion], amplitude)
  })

  // 确保所有情绪值的总和为100
  const total = Object.values(emotions).reduce((sum, value) => sum + value, 0)

  // 归一化
  if (total > 0) {
    Object.keys(emotions).forEach((emotion) => {
      emotions[emotion] = Math.round((emotions[emotion] / total) * 100)
    })
  }

  return emotions
}

/**
 * 生成波动的多模态分析数据
 * @param baseMetrics 基础多模态分析数据
 * @param amplitude 波动幅度
 * @returns 波动后的多模态分析数据
 */
export const generateFluctuatingMultiModalMetrics = (
  baseMetrics: MultiModalMetrics = createBaseMultiModalMetrics(),
  amplitude: number = 10,
): MultiModalMetrics => {
  const metrics: MultiModalMetrics = {
    speech: { ...baseMetrics.speech },
    video: { ...baseMetrics.video },
    text: { ...baseMetrics.text },
  }

  // 为语音分析数据生成波动值
  Object.keys(metrics.speech).forEach((key) => {
    metrics.speech[key as keyof typeof metrics.speech] = generateFluctuatingValue(
      baseMetrics.speech[key as keyof typeof baseMetrics.speech],
      amplitude,
    )
  })

  // 为视频分析数据生成波动值
  Object.keys(metrics.video).forEach((key) => {
    metrics.video[key as keyof typeof metrics.video] = generateFluctuatingValue(
      baseMetrics.video[key as keyof typeof baseMetrics.video],
      amplitude,
    )
  })

  // 为文本分析数据生成波动值
  Object.keys(metrics.text).forEach((key) => {
    metrics.text[key as keyof typeof metrics.text] = generateFluctuatingValue(
      baseMetrics.text[key as keyof typeof baseMetrics.text],
      amplitude,
    )
  })

  return metrics
}

/**
 * 生成实时分析结果
 * @param question 当前问题
 * @param userAnswer 用户回答
 * @returns 实时分析结果
 */
export const generateRealTimeAnalysis = (question: any, userAnswer: string): RealTimeAnalysis => {
  const analysis: RealTimeAnalysis = createBaseRealTimeAnalysis()

  // 如果有问题和用户回答，则生成关键词匹配
  if (question && question.expectedKeywords && userAnswer) {
    const keywords = question.expectedKeywords

    // 检查用户回答中包含的关键词
    analysis.keywordMatches = keywords.filter((keyword) =>
      userAnswer.toLowerCase().includes(keyword.toLowerCase()),
    )

    // 计算关键词匹配率
    const matchRate = keywords.length > 0 ? analysis.keywordMatches.length / keywords.length : 0

    // 根据关键词匹配率设置情感得分
    analysis.emotionScore = 50 + Math.round(matchRate * 50)

    // 设置注意力水平
    analysis.attentionLevel = 70 + Math.round(Math.random() * 30)

    // 设置字数统计
    analysis.wordCount = userAnswer.split(/\s+/).length

    // 设置情感倾向（-1到1之间的值，转换为0到100）
    const baseSentiment = matchRate * 2 - 1 // 根据匹配率生成基础情感倾向
    const randomFactor = (Math.random() - 0.5) * 0.4 // 添加随机因素
    analysis.sentiment = Math.round((baseSentiment + randomFactor + 1) * 50)

    // 设置当前转录文本（模拟实时语音转文本）
    analysis.currentTranscript =
      userAnswer.substring(0, Math.min(userAnswer.length, 100)) +
      (userAnswer.length > 100 ? '...' : '')

    // 设置微表情分析
    const possibleExpressions = ['专注', '思考', '自信', '犹豫', '紧张']
    const expressionCount = Math.floor(Math.random() * 3) + 1 // 1到3个表情

    for (let i = 0; i < expressionCount; i++) {
      const randomIndex = Math.floor(Math.random() * possibleExpressions.length)
      const expression = possibleExpressions[randomIndex]

      if (!analysis.microExpressions.includes(expression)) {
        analysis.microExpressions.push(expression)
      }
    }
  }

  return analysis
}

// 导出演示数据生成器
export default {
  createBaseEmotionData,
  createBaseMultiModalMetrics,
  createBaseRealTimeAnalysis,
  generateFluctuatingValue,
  generateFluctuatingEmotions,
  generateFluctuatingMultiModalMetrics,
  generateRealTimeAnalysis,
}
