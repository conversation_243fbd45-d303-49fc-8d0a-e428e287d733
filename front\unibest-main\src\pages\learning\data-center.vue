<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import BottomTabBar from '@/components/BottomTabBar.vue'
// @ts-ignore
import NoData from '@/components/NoData.vue'
// 导入类型定义
import type { Ref } from 'vue'
import type {
  LearningDataCenterStats,
  LearningRecord,
  LearningRecordQueryParams,
  LearningRecordListResponse,
  LearningAnalysis,
  AiLearningAdvice,
  LearningGoal,
  LearningAchievement,
} from '@/types/learning'
// 导入API接口
import {
  getLearningDataCenterStats,
  getLearningRecords,
  getLearningAnalysis,
  getAiLearningAdvice,
  getLearningGoals,
  getLearningAchievements,
  deleteLearningRecord,
  markAiAdviceAsRead,
  generateAiLearningAdvice,
} from '@/service/learning'

// Tab切换相关
const activeTab: Ref<string> = ref('overview')
const tabList = ref([
  { key: 'overview', name: '数据概览', icon: 'i-fa-solid-chart-line' },
  { key: 'records', name: '学习记录', icon: 'i-fa-solid-history' },
  { key: 'analysis', name: '学习分析', icon: 'i-fa-solid-chart-pie' },
  { key: 'ai', name: 'AI建议', icon: 'i-fa-solid-robot' },
])

// 数据状态
const isLoading: Ref<boolean> = ref(false)
const isInitialized: Ref<boolean> = ref(false)

// 统计数据
const dataCenterStats: Ref<LearningDataCenterStats | null> = ref(null)

// 学习记录
const learningRecords: Ref<LearningRecord[]> = ref([])
const recordsTotal: Ref<number> = ref(0)
const recordsPage: Ref<number> = ref(1)
const recordsPageSize: Ref<number> = ref(10)

// 学习分析
const learningAnalysis: Ref<LearningAnalysis | null> = ref(null)

// AI建议
const aiAdviceList: Ref<AiLearningAdvice[]> = ref([])
const aiAdviceTotal: Ref<number> = ref(0)

// 学习目标
const learningGoals: Ref<LearningGoal[]> = ref([])

// 学习成就
const learningAchievements: Ref<LearningAchievement[]> = ref([])

// 学习记录筛选
const selectedRecordType: Ref<string> = ref('all')
const selectedRecordStatus: Ref<string> = ref('all')
const isLoadingMore: Ref<boolean> = ref(false)

// 筛选选项
const recordTypeFilters = ref([
  { key: 'all', name: '全部', icon: 'i-fa-solid-list' },
  { key: 'question', name: '题目', icon: 'i-fa-solid-question-circle' },
  { key: 'book', name: '书籍', icon: 'i-fa-solid-book' },
  { key: 'video', name: '视频', icon: 'i-fa-solid-video' },
  { key: 'course', name: '课程', icon: 'i-fa-solid-graduation-cap' },
])

const recordStatusFilters = ref([
  { key: 'all', name: '全部' },
  { key: 'completed', name: '已完成' },
  { key: 'in_progress', name: '进行中' },
  { key: 'paused', name: '已暂停' },
])

// 计算属性：是否还有更多记录
const hasMoreRecords = computed(() => {
  return learningRecords.value.length < recordsTotal.value
})

/**
 * @description 切换Tab
 * @param tabKey Tab键值
 */
const switchTab = (tabKey: string): void => {
  if (activeTab.value === tabKey) return

  activeTab.value = tabKey

  // 根据切换的Tab加载对应数据
  loadTabData(tabKey)
}

/**
 * @description 根据Tab加载对应数据
 * @param tabKey Tab键值
 */
const loadTabData = async (tabKey: string): Promise<void> => {
  switch (tabKey) {
    case 'overview':
      await loadDataCenterStats()
      break
    case 'records':
      await loadLearningRecords()
      break
    case 'analysis':
      await loadLearningAnalysis()
      break
    case 'ai':
      await loadAiAdvice()
      break
  }
}

/**
 * @description 加载数据中心统计信息
 */
const loadDataCenterStats = async (): Promise<void> => {
  try {
    const res = await getLearningDataCenterStats()
    if (res.code === 200 && res.data) {
      dataCenterStats.value = res.data
    }
  } catch (error) {
    console.error('加载数据中心统计失败:', error)
    // 演示数据
    dataCenterStats.value = {
      totalStudyTime: 1200,
      todayStudyTime: 45,
      weeklyStudyTime: 320,
      monthlyStudyTime: 1200,
      totalQuestions: 856,
      completedQuestions: 624,
      correctAnswers: 487,
      averageCorrectRate: 78.1,
      currentStreak: 7,
      longestStreak: 15,
      totalBooks: 12,
      completedBooks: 5,
      totalCourses: 8,
      completedCourses: 3,
      bookmarkedQuestions: 45,
      bookmarkedBooks: 8,
      bookmarkedCourses: 5,
      globalRank: 1247,
      weeklyRank: 89,
      lastUpdated: new Date().toISOString(),
    }
  }
}

/**
 * @description 加载学习记录
 */
const loadLearningRecords = async (page: number = 1): Promise<void> => {
  try {
    const params: LearningRecordQueryParams = {
      page,
      pageSize: recordsPageSize.value,
      orderBy: 'createdAt',
      orderDirection: 'desc',
    }

    // 添加筛选条件
    if (selectedRecordType.value !== 'all') {
      params.type = selectedRecordType.value as any
    }
    if (selectedRecordStatus.value !== 'all') {
      params.status = selectedRecordStatus.value as any
    }

    const res = await getLearningRecords(params)
    if (res.code === 200 && res.data) {
      if (page === 1) {
        learningRecords.value = res.data.list
      } else {
        learningRecords.value.push(...res.data.list)
      }
      recordsTotal.value = res.data.total
      recordsPage.value = res.data.page
    }
  } catch (error) {
    console.error('加载学习记录失败:', error)
    // 演示数据
    if (page === 1) {
      learningRecords.value = [
        {
          id: '1',
          type: 'question',
          title: 'Java面试题练习',
          duration: 25,
          progress: 100,
          status: 'completed',
          score: 85,
          correctRate: 85,
          difficulty: '中等',
          category: 'Java',
          tags: ['面试', '基础'],
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:55:00Z',
          completedAt: '2024-01-15T10:55:00Z',
        },
        {
          id: '2',
          type: 'book',
          title: 'Spring Boot实战指南',
          duration: 120,
          progress: 65,
          status: 'in_progress',
          category: 'Java',
          tags: ['Spring Boot', '实战'],
          createdAt: '2024-01-14T14:20:00Z',
          updatedAt: '2024-01-15T09:30:00Z',
        },
        {
          id: '3',
          type: 'video',
          title: 'Vue3 全栈开发',
          duration: 95,
          progress: 30,
          status: 'in_progress',
          category: '前端',
          tags: ['Vue3', '前端'],
          createdAt: '2024-01-13T16:45:00Z',
          updatedAt: '2024-01-15T08:20:00Z',
        },
        {
          id: '4',
          type: 'course',
          title: '数据结构与算法',
          duration: 180,
          progress: 100,
          status: 'completed',
          score: 92,
          correctRate: 92,
          difficulty: '困难',
          category: '算法',
          tags: ['数据结构', '算法'],
          createdAt: '2024-01-12T11:15:00Z',
          updatedAt: '2024-01-13T15:30:00Z',
          completedAt: '2024-01-13T15:30:00Z',
        },
      ]
      recordsTotal.value = 4
    }
  }
}

/**
 * @description 加载学习分析数据
 */
const loadLearningAnalysis = async (): Promise<void> => {
  try {
    const res = await getLearningAnalysis({ timeRange: 'month' })
    if (res.code === 200 && res.data) {
      learningAnalysis.value = res.data
    }
  } catch (error) {
    console.error('加载学习分析失败:', error)
  }
}

/**
 * @description 加载AI建议
 */
const loadAiAdvice = async (): Promise<void> => {
  try {
    const res = await getAiLearningAdvice({ page: 1, pageSize: 10 })
    if (res.code === 200 && res.data) {
      aiAdviceList.value = res.data.list
      aiAdviceTotal.value = res.data.total
    }
  } catch (error) {
    console.error('加载AI建议失败:', error)
  }
}

/**
 * @description 格式化学习时长
 * @param minutes 分钟数
 * @returns 格式化后的时长字符串
 */
const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
}

/**
 * @description 格式化时间显示
 * @param dateString 时间字符串
 * @returns 格式化后的时间显示
 */
const formatTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

/**
 * @description 获取学习记录类型对应的图标
 * @param type 记录类型
 * @returns 图标类名
 */
const getRecordIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    question: 'i-fa-solid-question-circle',
    book: 'i-fa-solid-book',
    video: 'i-fa-solid-video',
    course: 'i-fa-solid-graduation-cap',
  }
  return iconMap[type] || 'i-fa-solid-circle'
}

/**
 * @description 获取学习状态对应的颜色
 * @param status 状态
 * @returns 颜色类名
 */
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    completed: 'text-green-600 bg-green-100',
    in_progress: 'text-blue-600 bg-blue-100',
    paused: 'text-yellow-600 bg-yellow-100',
  }
  return colorMap[status] || 'text-gray-600 bg-gray-100'
}

/**
 * @description 获取学习状态名称
 * @param status 状态
 * @returns 状态名称
 */
const getStatusName = (status: string): string => {
  const nameMap: Record<string, string> = {
    completed: '已完成',
    in_progress: '进行中',
    paused: '已暂停',
  }
  return nameMap[status] || '未知'
}

/**
 * @description 获取记录类型对应的图标样式类
 * @param type 记录类型
 * @returns 图标样式类名
 */
const getRecordIconClass = (type: string): string => {
  const classMap: Record<string, string> = {
    question: 'record-icon-question',
    book: 'record-icon-book',
    video: 'record-icon-video',
    course: 'record-icon-course',
  }
  return classMap[type] || 'record-icon-default'
}

/**
 * @description 选择记录类型筛选
 * @param type 类型键值
 */
const selectRecordType = (type: string): void => {
  if (selectedRecordType.value === type) return

  selectedRecordType.value = type
  recordsPage.value = 1
  loadLearningRecords(1)
}

/**
 * @description 选择记录状态筛选
 * @param status 状态键值
 */
const selectRecordStatus = (status: string): void => {
  if (selectedRecordStatus.value === status) return

  selectedRecordStatus.value = status
  recordsPage.value = 1
  loadLearningRecords(1)
}

/**
 * @description 重置记录筛选条件
 */
const resetRecordFilters = (): void => {
  selectedRecordType.value = 'all'
  selectedRecordStatus.value = 'all'
  recordsPage.value = 1
  loadLearningRecords(1)
}

/**
 * @description 加载更多学习记录
 */
const loadMoreRecords = async (): Promise<void> => {
  if (isLoadingMore.value || !hasMoreRecords.value) return

  isLoadingMore.value = true

  try {
    await loadLearningRecords(recordsPage.value + 1)
  } catch (error) {
    console.error('加载更多记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error',
      duration: 1500,
    })
  } finally {
    isLoadingMore.value = false
  }
}

/**
 * @description 获取今日学习记录数量
 * @returns 今日记录数量
 */
const getTodayRecordsCount = (): number => {
  const today = new Date().toDateString()
  return learningRecords.value.filter((record) => {
    const recordDate = new Date(record.createdAt).toDateString()
    return recordDate === today
  }).length
}

/**
 * @description 获取本周学习记录数量
 * @returns 本周记录数量
 */
const getWeekRecordsCount = (): number => {
  const now = new Date()
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

  return learningRecords.value.filter((record) => {
    const recordDate = new Date(record.createdAt)
    return recordDate >= weekAgo
  }).length
}

/**
 * @description 查看记录详情
 * @param record 学习记录对象
 */
const viewRecordDetail = (record: LearningRecord): void => {
  // 根据记录类型跳转到不同的详情页面
  let url = ''

  switch (record.type) {
    case 'question':
      url = `/pages/learning/question-detail?id=${record.id}`
      break
    case 'book':
      url = `/pages/learning/book-detail?id=${record.id}`
      break
    case 'video':
      url = `/pages/learning/video-detail?id=${record.id}`
      break
    case 'course':
      url = `/pages/learning/course-detail?id=${record.id}`
      break
    default:
      uni.showToast({
        title: '详情页面暂未开放',
        icon: 'none',
        duration: 1500,
      })
      return
  }

  uni.navigateTo({ url })
}

/**
 * @description 显示记录操作菜单
 * @param record 学习记录对象
 */
const showRecordActions = (record: LearningRecord): void => {
  const actions = ['查看详情', '删除记录']

  // 如果是进行中的记录，添加"继续学习"选项
  if (record.status === 'in_progress') {
    actions.unshift('继续学习')
  }

  uni.showActionSheet({
    itemList: actions,
    success: (res) => {
      const actionIndex = res.tapIndex
      const action = actions[actionIndex]

      switch (action) {
        case '继续学习':
          viewRecordDetail(record)
          break
        case '查看详情':
          viewRecordDetail(record)
          break
        case '删除记录':
          deleteRecord(record)
          break
      }
    },
  })
}

/**
 * @description 删除学习记录
 * @param record 学习记录对象
 */
const deleteRecord = (record: LearningRecord): void => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除"${record.title}"的学习记录吗？`,
    confirmText: '删除',
    confirmColor: '#ef4444',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deleteLearningRecord(record.id)
          if (result.code === 200) {
            // 从列表中移除记录
            const index = learningRecords.value.findIndex((r) => r.id === record.id)
            if (index > -1) {
              learningRecords.value.splice(index, 1)
              recordsTotal.value--
            }

            uni.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 1500,
            })
          }
        } catch (error) {
          console.error('删除记录失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'error',
            duration: 1500,
          })
        }
      }
    },
  })
}

/**
 * @description 返回上级页面
 */
const goBack = (): void => {
  uni.navigateBack()
}

/**
 * @description 初始化页面数据
 */
const initPageData = async (): Promise<void> => {
  if (isInitialized.value) return

  isLoading.value = true

  try {
    // 默认加载概览数据
    await loadDataCenterStats()
    isInitialized.value = true
  } catch (error) {
    console.error('初始化页面数据失败:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 页面显示时刷新数据
 */
onShow(() => {
  if (isInitialized.value) {
    loadTabData(activeTab.value)
  }
})

/**
 * @description 页面加载完成时初始化
 */
onMounted(() => {
  initPageData()
})
</script>

<template>
  <view class="data-center-container">
    <!-- 顶部导航 -->
    <view class="nav-wrapper">
      <HeadBar title="学习数据中心" :show-back="true" @back="goBack" />
    </view>

    <!-- 主要内容区域 -->
    <view class="main-wrapper">
      <scroll-view class="main-content" scroll-y enhanced :show-scrollbar="false">
        <!-- Tab导航 -->
        <view class="tab-nav-wrapper">
          <scroll-view class="tab-nav-scroll" scroll-x>
            <view class="tab-nav-list">
              <view
                v-for="tab in tabList"
                :key="tab.key"
                class="tab-nav-item"
                :class="{ 'tab-nav-item--active': activeTab === tab.key }"
                @click="switchTab(tab.key)"
              >
                <view :class="tab.icon" class="tab-nav-icon"></view>
                <text class="tab-nav-text">{{ tab.name }}</text>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 加载状态 -->
        <view v-if="isLoading && !isInitialized" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">数据加载中...</text>
        </view>

        <!-- 数据概览 -->
        <view v-else-if="activeTab === 'overview'" class="overview-content">
          <!-- 学习时长统计 -->
          <view class="stats-section">
            <text class="section-title">学习时长统计</text>
            <view class="time-stats-grid">
              <view class="time-stat-card gradient-blue">
                <view class="time-stat-icon">
                  <view class="i-fa-solid-clock"></view>
                </view>
                <view class="time-stat-info">
                  <text class="time-stat-value">
                    {{ formatDuration(dataCenterStats?.totalStudyTime || 0) }}
                  </text>
                  <text class="time-stat-label">总学习时长</text>
                </view>
              </view>
              <view class="time-stat-card gradient-green">
                <view class="time-stat-icon">
                  <view class="i-fa-solid-calendar-day"></view>
                </view>
                <view class="time-stat-info">
                  <text class="time-stat-value">
                    {{ formatDuration(dataCenterStats?.todayStudyTime || 0) }}
                  </text>
                  <text class="time-stat-label">今日学习</text>
                </view>
              </view>
              <view class="time-stat-card gradient-purple">
                <view class="time-stat-icon">
                  <view class="i-fa-solid-calendar-week"></view>
                </view>
                <view class="time-stat-info">
                  <text class="time-stat-value">
                    {{ formatDuration(dataCenterStats?.weeklyStudyTime || 0) }}
                  </text>
                  <text class="time-stat-label">本周学习</text>
                </view>
              </view>
              <view class="time-stat-card gradient-orange">
                <view class="time-stat-icon">
                  <view class="i-fa-solid-calendar-alt"></view>
                </view>
                <view class="time-stat-info">
                  <text class="time-stat-value">
                    {{ formatDuration(dataCenterStats?.monthlyStudyTime || 0) }}
                  </text>
                  <text class="time-stat-label">本月学习</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 题目练习统计 -->
          <view class="stats-section">
            <text class="section-title">题目练习统计</text>
            <view class="question-stats-card">
              <view class="question-stats-header">
                <view class="overall-stats">
                  <view class="overall-stat-item">
                    <text class="overall-stat-number">
                      {{ dataCenterStats?.totalQuestions || 0 }}
                    </text>
                    <text class="overall-stat-label">总题数</text>
                  </view>
                  <view class="overall-stat-item">
                    <text class="overall-stat-number">
                      {{ dataCenterStats?.completedQuestions || 0 }}
                    </text>
                    <text class="overall-stat-label">已完成</text>
                  </view>
                  <view class="overall-stat-item">
                    <text class="overall-stat-number text-green-600">
                      {{ dataCenterStats?.averageCorrectRate?.toFixed(1) || 0 }}%
                    </text>
                    <text class="overall-stat-label">正确率</text>
                  </view>
                </view>
              </view>
              <view class="question-progress-bar">
                <view class="progress-bg">
                  <view
                    class="progress-fill bg-gradient-to-r from-green-500 to-green-600"
                    :style="{
                      width: dataCenterStats
                        ? (
                            (dataCenterStats.completedQuestions / dataCenterStats.totalQuestions) *
                            100
                          ).toFixed(1) + '%'
                        : '0%',
                    }"
                  ></view>
                </view>
                <text class="progress-text">
                  完成进度
                  {{
                    dataCenterStats
                      ? (
                          (dataCenterStats.completedQuestions / dataCenterStats.totalQuestions) *
                          100
                        ).toFixed(1)
                      : 0
                  }}%
                </text>
              </view>
            </view>
          </view>

          <!-- 学习成就 -->
          <view class="stats-section">
            <text class="section-title">学习成就</text>
            <view class="achievement-stats-grid">
              <view class="achievement-stat-card">
                <view class="achievement-icon streak-icon">
                  <view class="i-fa-solid-fire"></view>
                </view>
                <view class="achievement-info">
                  <text class="achievement-number">{{ dataCenterStats?.currentStreak || 0 }}</text>
                  <text class="achievement-label">连续学习天数</text>
                  <text class="achievement-sub">
                    最长记录: {{ dataCenterStats?.longestStreak || 0 }}天
                  </text>
                </view>
              </view>
              <view class="achievement-stat-card">
                <view class="achievement-icon book-icon">
                  <view class="i-fa-solid-book"></view>
                </view>
                <view class="achievement-info">
                  <text class="achievement-number">{{ dataCenterStats?.completedBooks || 0 }}</text>
                  <text class="achievement-label">完成书籍</text>
                  <text class="achievement-sub">
                    总计: {{ dataCenterStats?.totalBooks || 0 }}本
                  </text>
                </view>
              </view>
              <view class="achievement-stat-card">
                <view class="achievement-icon course-icon">
                  <view class="i-fa-solid-graduation-cap"></view>
                </view>
                <view class="achievement-info">
                  <text class="achievement-number">
                    {{ dataCenterStats?.completedCourses || 0 }}
                  </text>
                  <text class="achievement-label">完成课程</text>
                  <text class="achievement-sub">
                    总计: {{ dataCenterStats?.totalCourses || 0 }}门
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 学习排名 -->
          <view class="stats-section">
            <text class="section-title">学习排名</text>
            <view class="ranking-stats-grid">
              <view class="ranking-card global-rank">
                <view class="ranking-icon">
                  <view class="i-fa-solid-globe"></view>
                </view>
                <view class="ranking-info">
                  <text class="ranking-label">全球排名</text>
                  <text class="ranking-number">#{{ dataCenterStats?.globalRank || 0 }}</text>
                  <text class="ranking-desc">在所有用户中的排名</text>
                </view>
              </view>
              <view class="ranking-card weekly-rank">
                <view class="ranking-icon">
                  <view class="i-fa-solid-trophy"></view>
                </view>
                <view class="ranking-info">
                  <text class="ranking-label">本周排名</text>
                  <text class="ranking-number">#{{ dataCenterStats?.weeklyRank || 0 }}</text>
                  <text class="ranking-desc">本周学习时长排名</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 收藏统计 -->
          <view class="stats-section">
            <text class="section-title">收藏统计</text>
            <view class="bookmark-stats-grid">
              <view class="bookmark-stat-item">
                <view class="bookmark-icon question-bookmark">
                  <view class="i-fa-solid-star"></view>
                </view>
                <view class="bookmark-content">
                  <text class="bookmark-number">
                    {{ dataCenterStats?.bookmarkedQuestions || 0 }}
                  </text>
                  <text class="bookmark-label">收藏题目</text>
                </view>
              </view>
              <view class="bookmark-stat-item">
                <view class="bookmark-icon book-bookmark">
                  <view class="i-fa-solid-bookmark"></view>
                </view>
                <view class="bookmark-content">
                  <text class="bookmark-number">{{ dataCenterStats?.bookmarkedBooks || 0 }}</text>
                  <text class="bookmark-label">收藏书籍</text>
                </view>
              </view>
              <view class="bookmark-stat-item">
                <view class="bookmark-icon course-bookmark">
                  <view class="i-fa-solid-heart"></view>
                </view>
                <view class="bookmark-content">
                  <text class="bookmark-number">{{ dataCenterStats?.bookmarkedCourses || 0 }}</text>
                  <text class="bookmark-label">收藏课程</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 数据更新时间 -->
          <view class="update-time-section">
            <text class="update-time-text">
              数据更新时间: {{ dataCenterStats ? formatTime(dataCenterStats.lastUpdated) : '未知' }}
            </text>
          </view>
        </view>

        <!-- 学习记录 -->
        <view v-else-if="activeTab === 'records'" class="records-content">
          <!-- 筛选条件 -->
          <view class="filter-section">
            <view class="filter-header">
              <text class="filter-title">筛选条件</text>
              <button class="filter-reset-btn" @click="resetRecordFilters">
                <view class="i-fa-solid-refresh"></view>
                <text>重置</text>
              </button>
            </view>
            <view class="filter-tabs">
              <view class="filter-group">
                <text class="filter-group-title">学习类型</text>
                <scroll-view class="filter-scroll" scroll-x>
                  <view class="filter-list">
                    <view
                      v-for="type in recordTypeFilters"
                      :key="type.key"
                      class="filter-tag"
                      :class="{ 'filter-tag--active': selectedRecordType === type.key }"
                      @click="selectRecordType(type.key)"
                    >
                      <view :class="type.icon" class="filter-tag-icon"></view>
                      <text class="filter-tag-text">{{ type.name }}</text>
                    </view>
                  </view>
                </scroll-view>
              </view>
              <view class="filter-group">
                <text class="filter-group-title">学习状态</text>
                <scroll-view class="filter-scroll" scroll-x>
                  <view class="filter-list">
                    <view
                      v-for="status in recordStatusFilters"
                      :key="status.key"
                      class="filter-tag"
                      :class="{ 'filter-tag--active': selectedRecordStatus === status.key }"
                      @click="selectRecordStatus(status.key)"
                    >
                      <text class="filter-tag-text">{{ status.name }}</text>
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>

          <!-- 记录统计摘要 -->
          <view class="records-summary" v-if="learningRecords.length > 0">
            <view class="summary-item">
              <text class="summary-label">总记录数</text>
              <text class="summary-value">{{ recordsTotal }}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">今日学习</text>
              <text class="summary-value">{{ getTodayRecordsCount() }}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">本周学习</text>
              <text class="summary-value">{{ getWeekRecordsCount() }}</text>
            </view>
          </view>

          <!-- 学习记录列表 -->
          <view class="records-list-section">
            <view v-if="learningRecords.length > 0" class="records-list">
              <view
                v-for="record in learningRecords"
                :key="record.id"
                class="record-item"
                @click="viewRecordDetail(record)"
              >
                <view class="record-left">
                  <view class="record-icon" :class="getRecordIconClass(record.type)">
                    <view :class="getRecordIcon(record.type)"></view>
                  </view>
                  <view class="record-info">
                    <text class="record-title">{{ record.title }}</text>
                    <text class="record-category">{{ record.category }}</text>
                    <view class="record-meta">
                      <text class="record-duration">{{ formatDuration(record.duration) }}</text>
                      <text class="record-time">{{ formatTime(record.createdAt) }}</text>
                    </view>
                  </view>
                </view>
                <view class="record-right">
                  <view class="record-status" :class="getStatusColor(record.status)">
                    <text>{{ getStatusName(record.status) }}</text>
                  </view>
                  <view class="record-progress">
                    <text class="progress-text">{{ record.progress }}%</text>
                    <view class="progress-bar-mini">
                      <view
                        class="progress-fill-mini"
                        :style="{ width: record.progress + '%' }"
                      ></view>
                    </view>
                  </view>
                  <view v-if="record.score" class="record-score">
                    <text class="score-label">得分:</text>
                    <text class="score-value">{{ record.score }}</text>
                  </view>
                  <button class="record-action-btn" @click.stop="showRecordActions(record)">
                    <view class="i-fa-solid-ellipsis-v"></view>
                  </button>
                </view>
              </view>
            </view>

            <!-- 无数据状态 -->
            <NoData
              v-else
              icon="i-fa-solid-history"
              text="暂无学习记录"
              description="开始学习后，这里将显示您的学习历史记录"
              custom-class="no-data--compact"
            />

            <!-- 加载更多 -->
            <view v-if="hasMoreRecords" class="load-more-section">
              <button class="load-more-btn" @click="loadMoreRecords" :disabled="isLoadingMore">
                <view v-if="isLoadingMore" class="loading-spinner-small"></view>
                <text>{{ isLoadingMore ? '加载中...' : '加载更多' }}</text>
              </button>
            </view>
          </view>
        </view>

        <!-- 学习分析 -->
        <view v-else-if="activeTab === 'analysis'" class="analysis-content">
          <!-- 这里将在后续实现分析内容 -->
          <text class="section-placeholder">学习分析内容（后续实现）</text>
        </view>

        <!-- AI建议 -->
        <view v-else-if="activeTab === 'ai'" class="ai-content">
          <!-- 这里将在后续实现AI建议内容 -->
          <text class="section-placeholder">AI建议内容（后续实现）</text>
        </view>

        <!-- 底部安全距离 -->
        <view class="bottom-safe-area"></view>
      </scroll-view>
    </view>

    <!-- 底部导航栏 -->
    <view class="bottom-wrapper">
      <BottomTabBar active-tab="learning" />
    </view>
  </view>
</template>

<style scoped lang="scss">
/* ==================== 页面整体布局 ==================== */
.data-center-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

/* ==================== 顶部导航区域 ==================== */
.nav-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.25);
  backdrop-filter: blur(10rpx);

  // 添加渐变边框效果
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%
    );
  }
}

/* ==================== Tab导航区域 ==================== */
.tab-nav-wrapper {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20rpx);
  margin: 0 32rpx 0;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.tab-nav-scroll {
  white-space: nowrap;
  padding: 24rpx 0 32rpx;
}

.tab-nav-list {
  display: inline-flex;
  padding: 0 32rpx;
  gap: 16rpx;
  align-items: center;
}

.tab-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 28rpx;
  border-radius: 20rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 120rpx;
  cursor: pointer;
  overflow: hidden;

  // 添加背景光晕效果
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(0, 201, 167, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: scale(0.95);
  }

  // #ifdef H5
  &:hover:not(&--active) {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.05) 0%, rgba(0, 178, 148, 0.05) 100%);
    transform: translateY(-2rpx);

    &::before {
      opacity: 1;
    }

    .tab-nav-icon {
      transform: scale(1.1);
      color: #00c9a7;
    }
  }
  // #endif

  &--active {
    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
    border: 2rpx solid #00c9a7;
    box-shadow: 0 12rpx 30rpx rgba(0, 201, 167, 0.3);
    transform: translateY(-2rpx);

    &::before {
      opacity: 1;
    }

    .tab-nav-icon {
      color: #00c9a7;
      transform: scale(1.15);
      filter: drop-shadow(0 4rpx 8rpx rgba(0, 201, 167, 0.3));
    }

    .tab-nav-text {
      color: #00c9a7;
      font-weight: 700;
    }

    // 添加激活状态的动画光效
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 70%
      );
      animation: shimmer 2s ease-in-out infinite;
    }
  }
}

.tab-nav-icon {
  font-size: 36rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-nav-text {
  font-size: 24rpx;
  color: #64748b;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* ==================== 主内容区域布局 ==================== */
.main-wrapper {
  position: absolute;
  top: 120rpx;
  left: 0;
  right: 0;
  bottom: 120rpx;

  // #ifdef MP-WEIXIN
  top: calc(120rpx + var(--status-bar-height));
  // #endif
}

.main-content {
  width: 100%;
  height: 100%;
  background: transparent;

  // #ifdef H5
  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  // #endif
}

/* ==================== 底部导航区域 ==================== */
.bottom-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  // #ifdef MP-WEIXIN
  padding-bottom: env(safe-area-inset-bottom);
  // #endif
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  gap: 24rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #00c9a7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #64748b;
}

/* ==================== 占位内容 ==================== */
/* ==================== 分析和AI内容样式 ==================== */
.analysis-content,
.ai-content {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 0 0 24rpx 24rpx;
  margin: 0 32rpx 32rpx;
  padding: 0 32rpx 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-top: none;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.section-placeholder {
  display: block;
  text-align: center;
  padding: 120rpx 32rpx;
  font-size: 28rpx;
  color: #94a3b8;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20rpx;
  border: 2rpx dashed #e2e8f0;
  margin-top: 40rpx;
}

/* ==================== 底部安全距离 ==================== */
.bottom-safe-area {
  height: 60rpx;
  background: transparent;

  // #ifdef MP-WEIXIN
  height: calc(60rpx + env(safe-area-inset-bottom));
  // #endif

  // #ifdef H5
  height: 80rpx;
  // #endif
}

/* ==================== 概览内容样式 ==================== */
.overview-content {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 0 0 24rpx 24rpx;
  margin: 0 32rpx 32rpx;
  padding: 0 32rpx 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-top: none;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* 统计区域 */
.stats-section {
  margin-bottom: 56rpx;
  position: relative;

  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 32rpx;
    position: relative;
    padding-left: 24rpx;

    // 添加装饰性左边框
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 36rpx;
      background: linear-gradient(180deg, #00c9a7 0%, #00b294 100%);
      border-radius: 4rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
    }

    // 添加微光效果
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 24rpx;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0, 201, 167, 0.03) 50%,
        transparent 100%
      );
      border-radius: 8rpx;
      z-index: -1;
    }
  }
}

/* 学习时长统计网格 */
.time-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 16rpx;

  // 移动端单列布局
  @media screen and (max-width: 750rpx) {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
}

.time-stat-card {
  position: relative;
  display: flex;
  align-items: center;
  padding: 36rpx;
  border-radius: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.8s ease-out both;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.5);

  // 添加卡片光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%
    );
    transition: left 0.6s ease;
  }

  // #ifdef H5
  &:hover {
    transform: translateY(-8rpx) scale(1.02);
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }

    .time-stat-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.2);
    }

    .time-stat-value {
      transform: scale(1.05);
    }
  }
  // #endif

  // 渐变背景优化
  &.gradient-blue {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
    box-shadow: 0 12rpx 40rpx rgba(59, 130, 246, 0.15);

    &:hover {
      box-shadow: 0 20rpx 60rpx rgba(59, 130, 246, 0.25);
    }
  }

  &.gradient-green {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 50%, #6ee7b7 100%);
    box-shadow: 0 12rpx 40rpx rgba(16, 185, 129, 0.15);

    &:hover {
      box-shadow: 0 20rpx 60rpx rgba(16, 185, 129, 0.25);
    }
  }

  &.gradient-purple {
    background: linear-gradient(135deg, #e9d5ff 0%, #d8b4fe 50%, #c084fc 100%);
    box-shadow: 0 12rpx 40rpx rgba(147, 51, 234, 0.15);

    &:hover {
      box-shadow: 0 20rpx 60rpx rgba(147, 51, 234, 0.25);
    }
  }

  &.gradient-orange {
    background: linear-gradient(135deg, #fed7aa 0%, #fdba74 50%, #fb923c 100%);
    box-shadow: 0 12rpx 40rpx rgba(251, 146, 60, 0.15);

    &:hover {
      box-shadow: 0 20rpx 60rpx rgba(251, 146, 60, 0.25);
    }
  }

  .time-stat-icon {
    width: 88rpx;
    height: 88rpx;
    border-radius: 22rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 28rpx;
    background: rgba(255, 255, 255, 0.9);
    font-size: 40rpx;
    color: #374151;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10rpx);
  }

  .time-stat-info {
    flex: 1;

    .time-stat-value {
      display: block;
      font-size: 36rpx;
      font-weight: 800;
      color: #1e293b;
      margin-bottom: 6rpx;
      transition: transform 0.3s ease;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    .time-stat-label {
      display: block;
      font-size: 26rpx;
      color: #64748b;
      font-weight: 500;
      letter-spacing: 0.5rpx;
    }
  }

  // 为每个卡片添加动画延迟
  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.3s;
  }
  &:nth-child(4) {
    animation-delay: 0.4s;
  }
}

/* 题目统计卡片 */
.question-stats-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  overflow: hidden;

  // 添加装饰性背景图案
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
  }

  .question-stats-header {
    margin-bottom: 40rpx;
    position: relative;
    z-index: 1;
  }

  .overall-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
    position: relative;
    z-index: 1;

    .overall-stat-item {
      position: relative;
      padding: 20rpx;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      // #ifdef H5
      &:hover {
        background: rgba(16, 185, 129, 0.05);
        transform: translateY(-4rpx);

        .overall-stat-number {
          transform: scale(1.1);
        }
      }
      // #endif

      .overall-stat-number {
        display: block;
        font-size: 44rpx;
        font-weight: 800;
        color: #1e293b;
        margin-bottom: 12rpx;
        transition: all 0.3s ease;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        &.text-green-600 {
          color: #059669;
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          filter: drop-shadow(0 2rpx 4rpx rgba(16, 185, 129, 0.3));
        }
      }

      .overall-stat-label {
        display: block;
        font-size: 26rpx;
        color: #64748b;
        font-weight: 500;
        letter-spacing: 0.5rpx;
      }
    }
  }

  .question-progress-bar {
    position: relative;
    z-index: 1;

    .progress-bg {
      height: 20rpx;
      background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 100%);
      border-radius: 10rpx;
      overflow: hidden;
      margin-bottom: 20rpx;
      box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      position: relative;

      // 添加进度条光泽效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.6) 50%,
          transparent 100%
        );
        animation: progressShine 2s ease-in-out infinite;
      }

      .progress-fill {
        height: 100%;
        border-radius: 10rpx;
        transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &.bg-gradient-to-r {
          background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
          box-shadow:
            0 4rpx 12rpx rgba(16, 185, 129, 0.4),
            inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);

          // 添加进度条内部光效
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
            border-radius: 10rpx 10rpx 0 0;
          }
        }
      }
    }

    .progress-text {
      display: block;
      text-align: center;
      font-size: 26rpx;
      color: #64748b;
      font-weight: 500;
    }
  }
}

/* 学习成就网格 */
.achievement-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.achievement-stat-card {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  // #ifdef H5
  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }
  // #endif

  .achievement-icon {
    width: 96rpx;
    height: 96rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 32rpx;
    font-size: 48rpx;
    color: white;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);

    &.streak-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    &.book-icon {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }

    &.course-icon {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
  }

  .achievement-info {
    flex: 1;

    .achievement-number {
      display: block;
      font-size: 40rpx;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 8rpx;
    }

    .achievement-label {
      display: block;
      font-size: 28rpx;
      color: #374151;
      margin-bottom: 4rpx;
      font-weight: 500;
    }

    .achievement-sub {
      display: block;
      font-size: 22rpx;
      color: #64748b;
    }
  }
}

/* 学习排名网格 */
.ranking-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.ranking-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  // #ifdef H5
  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }
  // #endif

  .ranking-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24rpx;
    font-size: 40rpx;
    color: white;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  }

  &.global-rank .ranking-icon {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  }

  &.weekly-rank .ranking-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }

  .ranking-info {
    .ranking-label {
      display: block;
      font-size: 24rpx;
      color: #64748b;
      margin-bottom: 8rpx;
    }

    .ranking-number {
      display: block;
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 8rpx;
    }

    .ranking-desc {
      display: block;
      font-size: 20rpx;
      color: #94a3b8;
    }
  }
}

/* 收藏统计网格 */
.bookmark-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.bookmark-stat-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  // #ifdef H5
  &:hover {
    transform: translateX(8rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }
  // #endif

  .bookmark-icon {
    width: 64rpx;
    height: 64rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    font-size: 28rpx;
    color: white;

    &.question-bookmark {
      background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    }

    &.book-bookmark {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    &.course-bookmark {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
  }

  .bookmark-content {
    .bookmark-number {
      display: block;
      font-size: 32rpx;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 4rpx;
    }

    .bookmark-label {
      display: block;
      font-size: 24rpx;
      color: #64748b;
    }
  }
}

/* 数据更新时间 */
.update-time-section {
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  text-align: center;

  .update-time-text {
    font-size: 22rpx;
    color: #94a3b8;
  }
}

/* 辅助样式 */
.text-green-600 {
  color: #059669;
}

/* ==================== 学习记录内容样式 ==================== */
.records-content {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 0 0 24rpx 24rpx;
  margin: 0 32rpx 32rpx;
  padding: 0 32rpx 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-top: none;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* 筛选区域 */
.filter-section {
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 32rpx 0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10rpx);
  overflow: hidden;

  // 添加装饰性边框光效
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2rpx;
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.3) 0%,
      transparent 50%,
      rgba(0, 201, 167, 0.3) 100%
    );
    border-radius: 24rpx;
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: xor;
    animation: borderRotate 3s ease-in-out infinite;
  }
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;

  .filter-title {
    font-size: 36rpx;
    font-weight: 800;
    color: #1e293b;
    position: relative;

    // 添加标题装饰
    &::after {
      content: '';
      position: absolute;
      bottom: -8rpx;
      left: 0;
      width: 60rpx;
      height: 4rpx;
      background: linear-gradient(90deg, #00c9a7 0%, #00b294 100%);
      border-radius: 2rpx;
    }
  }

  .filter-reset-btn {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx 28rpx;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: none;
    border-radius: 20rpx;
    font-size: 26rpx;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    // #ifdef H5
    &:hover {
      background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
      transform: translateY(-2rpx);
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
      color: #00c9a7;
    }
    // #endif

    &:active {
      transform: scale(0.95);
    }
  }
}

.filter-tabs {
  position: relative;
  z-index: 1;

  .filter-group {
    margin-bottom: 28rpx;
    padding: 24rpx;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 16rpx;
    border: 1rpx solid rgba(226, 232, 240, 0.5);

    &:last-child {
      margin-bottom: 0;
    }

    .filter-group-title {
      display: block;
      font-size: 28rpx;
      font-weight: 700;
      color: #374151;
      margin-bottom: 20rpx;
      position: relative;
      padding-left: 16rpx;

      // 添加分组标题装饰
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background: linear-gradient(180deg, #00c9a7 0%, #00b294 100%);
        border-radius: 3rpx;
      }
    }
  }
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: inline-flex;
  gap: 12rpx;
  padding-bottom: 8rpx;
}

.filter-tag {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 18rpx 28rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 28rpx;
  font-size: 26rpx;
  color: #64748b;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;

  // 添加悬停光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 201, 167, 0.1) 50%,
      transparent 100%
    );
    transition: left 0.5s ease;
  }

  &:active {
    transform: scale(0.95);
  }

  // #ifdef H5
  &:hover:not(&--active) {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.05) 0%, rgba(0, 178, 148, 0.05) 100%);
    border-color: rgba(0, 201, 167, 0.3);
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.1);

    &::before {
      left: 100%;
    }

    .filter-tag-icon {
      color: #00c9a7;
      transform: scale(1.1);
    }
  }
  // #endif

  &--active {
    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
    border-color: #00c9a7;
    color: #00c9a7;
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 25rpx rgba(0, 201, 167, 0.2);

    &::before {
      left: 100%;
    }

    .filter-tag-icon {
      color: #00c9a7;
      transform: scale(1.2);
      filter: drop-shadow(0 2rpx 4rpx rgba(0, 201, 167, 0.3));
    }

    .filter-tag-text {
      font-weight: 700;
    }
  }

  .filter-tag-icon {
    font-size: 22rpx;
    color: #94a3b8;
    transition: all 0.3s ease;
  }

  .filter-tag-text {
    font-weight: 600;
    letter-spacing: 0.5rpx;
  }
}

/* 记录统计摘要 */
.records-summary {
  display: flex;
  justify-content: space-around;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 32rpx 0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;

  // 添加动态背景光效
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
      from 0deg,
      transparent 0%,
      rgba(0, 201, 167, 0.03) 25%,
      transparent 50%,
      rgba(0, 201, 167, 0.03) 75%,
      transparent 100%
    );
    animation: rotate 15s linear infinite;
  }

  .summary-item {
    text-align: center;
    position: relative;
    z-index: 1;
    padding: 16rpx;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    // #ifdef H5
    &:hover {
      background: rgba(0, 201, 167, 0.05);
      transform: translateY(-4rpx);

      .summary-value {
        transform: scale(1.1);
        color: #00c9a7;
      }
    }
    // #endif

    .summary-label {
      display: block;
      font-size: 26rpx;
      color: #64748b;
      margin-bottom: 12rpx;
      font-weight: 500;
      letter-spacing: 0.5rpx;
    }

    .summary-value {
      display: block;
      font-size: 40rpx;
      font-weight: 800;
      color: #1e293b;
      transition: all 0.3s ease;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }
  }
}

/* 记录列表区域 */
.records-list-section {
  .records-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
}

/* 记录项 */
.record-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;

  // 添加悬停光晕效果
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.03) 0%,
      transparent 50%,
      rgba(0, 201, 167, 0.03) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // #ifdef H5
  &:hover {
    transform: translateY(-6rpx) scale(1.02);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }
  // #endif

  &:active {
    transform: scale(0.98);
  }

  .record-left {
    display: flex;
    align-items: center;
    flex: 1;
    margin-right: 24rpx;

    .record-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      font-size: 36rpx;
      color: white;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);

      &.record-icon-question {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
      }

      &.record-icon-book {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      }

      &.record-icon-video {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }

      &.record-icon-course {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      &.record-icon-default {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
      }
    }

    .record-info {
      flex: 1;

      .record-title {
        display: block;
        font-size: 30rpx;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 8rpx;
        line-height: 1.3;
      }

      .record-category {
        display: block;
        font-size: 24rpx;
        color: #00c9a7;
        margin-bottom: 12rpx;
        font-weight: 500;
      }

      .record-meta {
        display: flex;
        gap: 24rpx;
        font-size: 22rpx;
        color: #64748b;

        .record-duration,
        .record-time {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .record-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 12rpx;
    min-width: 200rpx;

    .record-status {
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 22rpx;
      font-weight: 500;

      &.text-green-600 {
        color: #059669;
        background: #dcfce7;
      }

      &.text-blue-600 {
        color: #2563eb;
        background: #dbeafe;
      }

      &.text-yellow-600 {
        color: #d97706;
        background: #fef3c7;
      }
    }

    .record-progress {
      display: flex;
      align-items: center;
      gap: 12rpx;
      width: 100%;

      .progress-text {
        font-size: 22rpx;
        color: #64748b;
        font-weight: 500;
        min-width: 60rpx;
        text-align: right;
      }

      .progress-bar-mini {
        flex: 1;
        height: 8rpx;
        background: #e2e8f0;
        border-radius: 4rpx;
        overflow: hidden;

        .progress-fill-mini {
          height: 100%;
          background: linear-gradient(90deg, #00c9a7 0%, #00b294 100%);
          border-radius: 4rpx;
          transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
    }

    .record-score {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .score-label {
        font-size: 22rpx;
        color: #64748b;
      }

      .score-value {
        font-size: 24rpx;
        font-weight: 700;
        color: #00c9a7;
      }
    }

    .record-action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: #f1f5f9;
      border: none;
      border-radius: 50%;
      color: #64748b;
      font-size: 24rpx;
      transition: all 0.2s ease;

      // #ifdef H5
      &:hover {
        background: #e2e8f0;
        color: #374151;
      }
      // #endif

      &:active {
        transform: scale(0.9);
      }
    }
  }
}

/* 加载更多区域 */
.load-more-section {
  margin-top: 32rpx;
  text-align: center;

  .load-more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    width: 100%;
    padding: 28rpx;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2rpx solid #e2e8f0;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #64748b;
    transition: all 0.3s ease;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &:not(:disabled) {
      // #ifdef H5
      &:hover {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        border-color: #cbd5e1;
      }
      // #endif

      &:active {
        transform: scale(0.98);
      }
    }

    .loading-spinner-small {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid #e2e8f0;
      border-top: 3rpx solid #00c9a7;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

/* ==================== 动画定义 ==================== */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes progressShine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes borderRotate {
  0% {
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.3) 0%,
      transparent 50%,
      rgba(0, 201, 167, 0.3) 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      transparent 0%,
      rgba(0, 201, 167, 0.5) 50%,
      transparent 100%
    );
  }
  100% {
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.3) 0%,
      transparent 50%,
      rgba(0, 201, 167, 0.3) 100%
    );
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 20rpx rgba(0, 201, 167, 0.2);
  }
  50% {
    box-shadow: 0 0 40rpx rgba(0, 201, 167, 0.4);
  }
  100% {
    box-shadow: 0 0 20rpx rgba(0, 201, 167, 0.2);
  }
}
</style>
