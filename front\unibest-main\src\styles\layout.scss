// ==================== 项目固定尺寸布局样式 ====================
// 项目规定尺寸：430 * 932

// ==================== 全局容器限制 ====================
html, body {
  max-width: 430px;
  margin: 0 auto;
  overflow-x: hidden;
  position: relative;
}

// ==================== 应用根容器 ====================
#app {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

// ==================== 页面容器 ====================
.page-container {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
  position: relative;
}

// ==================== 内容区域 ====================
.content-wrapper {
  width: 100%;
  max-width: 430px;
  margin: 0 auto;
  padding: 0 16px;
  box-sizing: border-box;
}

// ==================== 响应式处理 ====================
// 当屏幕宽度小于430px时，使用100%宽度
@media (max-width: 430px) {
  html, body, #app, .page-container {
    max-width: 100vw;
    width: 100%;
  }
  
  .content-wrapper {
    max-width: 100%;
    padding: 0 12px;
  }
}

// ==================== 当屏幕宽度大于430px时的居中处理 ====================
@media (min-width: 431px) {
  body {
    background: #f5f5f5;
    padding: 20px 0;
  }
  
  #app {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

// ==================== 高度处理 ====================
// 确保内容不超过932px高度
.full-height {
  height: 100vh;
  max-height: 932px;
  overflow-y: auto;
}

// ==================== 滚动优化 ====================
.scroll-container {
  max-height: 932px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// ==================== 固定定位元素的尺寸限制 ====================
.fixed-element {
  max-width: 430px;
  left: 50%;
  transform: translateX(-50%);
}

// ==================== 调试模式 ====================
.debug-layout {
  #app {
    border: 2px solid #ff0000;
    
    &::before {
      content: '430 × 932';
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(255, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      z-index: 9999;
    }
  }
}
