<script setup lang="ts">
import { ref, onBeforeUnmount, computed, onMounted } from 'vue'
// @ts-ignore
import { sendEmailCode, register, checkEmailExists } from '@/service/auth'
// @ts-ignore
import { useUserStore } from '@/store/user'

// 使用用户状态管理
const userStore = useUserStore()

// 表单数据
const form = ref({
  email: '',
  studentId: '',
  realName: '',
  major: '',
  grade: '',
  password: '',
  confirmPassword: '',
  code: '',
  agreement: false,
})

// 专业列表
const majorList = ref([
  { value: '', text: '请选择专业' },
  { value: '计算机科学与技术', text: '计算机科学与技术' },
  { value: '软件工程', text: '软件工程' },
  { value: '人工智能', text: '人工智能' },
  { value: '数据科学与大数据技术', text: '数据科学与大数据技术' },
  { value: '物联网工程', text: '物联网工程' },
  { value: '信息系统', text: '信息系统' },
  { value: '网络工程', text: '网络工程' },
  { value: '信息安全', text: '信息安全' },
  { value: '其他', text: '其他' },
])
const majorIndex = ref(0)

const onMajorChange = (e: any) => {
  majorIndex.value = e.detail.value
  form.value.major = majorList.value[majorIndex.value].value || ''
  clearInputError('major')
}

// 年级按钮
const grades = ['大一', '大二', '大三', '大四']

// 加载状态
const isLoading = ref(false)
const sendCodeLoading = ref(false)

// 验证码倒计时
const countdown = ref(0)
let countdownTimer: any = null

// 密码相关
const passwordVisible = ref(false)
const confirmPasswordVisible = ref(false)
const passwordStrengthInfo = ref({
  text: '弱',
  class: 'strength-weak',
})

// 通知相关
const notification = ref({
  show: false,
  message: '',
  type: 'info', // success, error, info
  icon: '',
})
let notificationTimer: any = null

// 错误信息
const errors = ref<Record<string, string>>({})

// 当前时间 (用于顶部模拟状态栏)
const currentTime = ref('8:30')

// 更新时间
function updateTime() {
  const now = new Date()
  currentTime.value = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
}

// 处理年级选择
const selectGrade = (grade: string) => {
  form.value.grade = grade
  clearInputError('grade')
}

// 切换密码可见性
const togglePasswordVisibility = (field: 'password' | 'confirmPassword') => {
  if (field === 'password') {
    passwordVisible.value = !passwordVisible.value
  } else {
    confirmPasswordVisible.value = !confirmPasswordVisible.value
  }
}

// 验证密码强度
const validatePasswordStrength = () => {
  const password = form.value.password
  let strength = 0
  if (password.length >= 6) strength++
  if (password.length >= 8) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^A-Za-z0-9]/.test(password)) strength++

  if (strength >= 4) {
    passwordStrengthInfo.value = { text: '强', class: 'strength-strong' }
  } else if (strength >= 2) {
    passwordStrengthInfo.value = { text: '中', class: 'strength-medium' }
  } else {
    passwordStrengthInfo.value = { text: '弱', class: 'strength-weak' }
  }
}

// 验证密码匹配
const validatePasswordMatch = () => {
  if (form.value.confirmPassword && form.value.password !== form.value.confirmPassword) {
    showInputError('confirmPassword', '两次输入的密码不一致')
  } else {
    clearInputError('confirmPassword')
  }
}

// 显示通知
const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  notification.value.message = message
  notification.value.type = type
  if (type === 'success') notification.value.icon = 'uni-icons icon-checkbox-filled'
  else if (type === 'error') notification.value.icon = 'uni-icons icon-info-filled'
  else notification.value.icon = 'uni-icons icon-info-filled'
  notification.value.show = true

  if (notificationTimer) clearTimeout(notificationTimer)
  notificationTimer = setTimeout(() => {
    notification.value.show = false
  }, 3000)
}

// 显示输入错误
const showInputError = (field: string, message: string) => {
  errors.value[field] = message
}

// 清除输入错误
const clearInputError = (field: string) => {
  if (errors.value[field]) {
    delete errors.value[field]
  }
}
const clearAllErrors = () => {
  errors.value = {}
}

// 发送验证码
const handleSendCode = async () => {
  clearInputError('email')
  if (!form.value.email) {
    showInputError('email', '请先输入邮箱地址')
    return
  }

  sendCodeLoading.value = true

  try {
    // 先检查邮箱是否已注册
    const checkRes = await checkEmailExists({
      params: { email: form.value.email },
    })

    if (checkRes.code === 200 && checkRes.data?.exists) {
      showInputError('email', '该邮箱已被注册')
      sendCodeLoading.value = false
      return
    }

    // 发送验证码
    const res = await sendEmailCode({
      params: { email: form.value.email },
    })

    if (res.code === 200) {
      countdown.value = 60
      showNotification('验证码已发送到您的邮箱', 'success')

      countdownTimer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      }, 1000)
    } else {
      showNotification(res.message || '验证码发送失败', 'error')
    }
  } catch (error) {
    showNotification('验证码发送失败，请稍后重试', 'error')
  } finally {
    sendCodeLoading.value = false
  }
}

// 表单验证
const validateForm = (): boolean => {
  clearAllErrors()
  let hasError = false

  if (!form.value.email) {
    showInputError('email', '请输入邮箱地址')
    hasError = true
  }

  if (!form.value.studentId) {
    showInputError('studentId', '请输入学号')
    hasError = true
  }

  if (!form.value.realName) {
    showInputError('realName', '请输入真实姓名')
    hasError = true
  } else if (form.value.realName.length < 2) {
    showInputError('realName', '姓名至少需要2个字符')
    hasError = true
  }

  if (!form.value.major) {
    showInputError('major', '请选择专业')
    hasError = true
  }

  if (!form.value.grade) {
    showInputError('grade', '请选择年级')
    hasError = true
  }

  if (!form.value.password) {
    showInputError('password', '请设置密码')
    hasError = true
  } else if (form.value.password.length < 6 || form.value.password.length > 20) {
    showInputError('password', '密码长度应为6-20位')
    hasError = true
  }

  if (!form.value.confirmPassword) {
    showInputError('confirmPassword', '请确认密码')
    hasError = true
  } else if (form.value.password !== form.value.confirmPassword) {
    showInputError('confirmPassword', '两次输入的密码不一致')
    hasError = true
  }

  if (!form.value.code) {
    showInputError('code', '请输入验证码')
    hasError = true
  }

  if (!form.value.agreement) {
    showInputError('agreement', '请阅读并同意用户协议和隐私政策')
    hasError = true
  }

  return !hasError
}

// 注册处理
const handleRegister = async () => {
  if (isLoading.value) return
  if (!validateForm()) return

  isLoading.value = true

  try {
    // 调用后端注册接口
    const res = await register({
      params: {
        email: form.value.email,
        studentId: form.value.studentId,
        realName: form.value.realName,
        major: form.value.major,
        grade: form.value.grade,
        password: form.value.password,
        code: form.value.code,
      },
    })

    if (res.code === 200 && res.data) {
      // 保存用户信息到状态管理
      userStore.setUserInfo(res.data)
      userStore.setToken(res.data.token)

      // 保存到本地存储
      uni.setStorageSync('currentUser', JSON.stringify(res.data))
      uni.setStorageSync('token', res.data.token)
      uni.setStorageSync('loginTime', new Date().toISOString())

      showNotification('注册成功！正在跳转...', 'success')
      setTimeout(() => {
        uni.switchTab({ url: '/pages/home/<USER>' })
      }, 2000)
    } else {
      showNotification(res.message || '注册失败，请重试', 'error')
    }
  } catch (error) {
    showNotification('注册失败，请稍后重试', 'error')
  } finally {
    isLoading.value = false
  }
}

// 返回登录
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/auth/login',
  })
}

const computedMajorText = computed(() => {
  return majorList.value[majorIndex.value].text || '请选择专业'
})

// 挂载
onMounted(() => {
  // 挂载
  updateTime()
  setInterval(updateTime, 60000)
})

// 卸载
onBeforeUnmount(() => {
  // 卸载
  if (countdownTimer) clearInterval(countdownTimer)
  if (notificationTimer) clearTimeout(notificationTimer)
})
</script>

<template>
  <view class="register-container">
    <!-- 通知区域 -->
    <view v-if="notification.show" class="notification" :class="notification.type">
      <text class="notification-icon" :class="notification.icon"></text>
      <text class="notification-message">{{ notification.message }}</text>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-area bg-gray-50 relative">
      <!-- 背景装饰元素 -->
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>
      <view class="decoration-circle decoration-circle-3"></view>

      <view class="page-content">
        <!-- 页面标题 -->
        <view class="page-header">
          <view class="logo">
            <text class="i-fa-solid-user-plus text-white"></text>
          </view>
          <text class="title">注册新账号</text>
          <text class="subtitle">创建您的智能面试助手账号</text>
        </view>

        <!-- 注册表单 -->
        <view class="form-container">
          <!-- 电子邮箱 -->
          <view class="form-item" style="--item-index: 0">
            <text class="form-label">电子邮箱 <text class="required">*</text></text>
            <view class="input-container">
              <input
                type="text"
                v-model="form.email"
                placeholder="请输入您的学校邮箱"
                class="form-input"
                :class="{ 'input-error': errors.email }"
              />
              <text class="input-icon i-fa-solid-envelope"></text>
            </view>
            <text v-if="errors.email" class="error-text">{{ errors.email }}</text>
          </view>

          <!-- 学号 -->
          <view class="form-item" style="--item-index: 1">
            <text class="form-label">学号 <text class="required">*</text></text>
            <view class="input-container">
              <input
                type="text"
                v-model="form.studentId"
                placeholder="请输入您的学号"
                class="form-input"
                :class="{ 'input-error': errors.studentId }"
              />
              <text class="input-icon i-fa-solid-id-card"></text>
            </view>
            <text v-if="errors.studentId" class="error-text">{{ errors.studentId }}</text>
          </view>

          <!-- 姓名 -->
          <view class="form-item" style="--item-index: 2">
            <text class="form-label">姓名 <text class="required">*</text></text>
            <view class="input-container">
              <input
                type="text"
                v-model="form.realName"
                placeholder="请输入您的真实姓名"
                class="form-input"
                :class="{ 'input-error': errors.realName }"
              />
              <text class="input-icon i-fa-solid-user"></text>
            </view>
            <text v-if="errors.realName" class="error-text">{{ errors.realName }}</text>
          </view>

          <!-- 专业 -->
          <view class="form-item" style="--item-index: 3">
            <text class="form-label">专业 <text class="required">*</text></text>
            <view class="select-container">
              <picker
                mode="selector"
                :range="majorList"
                range-key="text"
                :value="majorIndex"
                @change="onMajorChange"
                class="form-picker"
                :class="{ 'input-error': errors.major }"
              >
                <view class="picker-value">
                  <text>{{ form.major || '请选择您的专业' }}</text>
                  <text class="i-fa-solid-chevron-down picker-arrow"></text>
                </view>
              </picker>
            </view>
            <text v-if="errors.major" class="error-text">{{ errors.major }}</text>
          </view>

          <!-- 年级选择 -->
          <view class="form-item" style="--item-index: 4">
            <text class="form-label">年级 <text class="required">*</text></text>
            <view class="grade-buttons">
              <button
                type="button"
                v-for="grade in grades"
                :key="grade"
                :class="['grade-btn', form.grade === grade ? 'active' : '']"
                @click="selectGrade(grade)"
              >
                {{ grade }}
              </button>
            </view>
            <text v-if="errors.grade" class="error-text">{{ errors.grade }}</text>
          </view>

          <!-- 密码 -->
          <view class="form-item" style="--item-index: 5">
            <text class="form-label">密码 <text class="required">*</text></text>
            <view class="input-container">
              <input
                :type="passwordVisible ? 'text' : 'password'"
                v-model="form.password"
                placeholder="请设置登录密码"
                class="form-input"
                :class="{ 'input-error': errors.password }"
                maxlength="20"
                @input="validatePasswordStrength"
              />
              <text class="input-icon-toggle" @click="togglePasswordVisibility('password')">
                <text v-if="passwordVisible" class="i-fa-solid-eye-slash"></text>
                <text v-else class="i-fa-solid-eye"></text>
              </text>
            </view>
            <view v-if="form.password" class="password-strength">
              <text>密码强度：</text>
              <text :class="passwordStrengthInfo.class">{{ passwordStrengthInfo.text }}</text>
            </view>
            <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
          </view>

          <!-- 确认密码 -->
          <view class="form-item" style="--item-index: 6">
            <text class="form-label">确认密码 <text class="required">*</text></text>
            <view class="input-container">
              <input
                :type="confirmPasswordVisible ? 'text' : 'password'"
                v-model="form.confirmPassword"
                placeholder="请再次输入密码"
                class="form-input"
                :class="{ 'input-error': errors.confirmPassword }"
                maxlength="20"
                @input="validatePasswordMatch"
              />
              <text class="input-icon-toggle" @click="togglePasswordVisibility('confirmPassword')">
                <text v-if="confirmPasswordVisible" class="i-fa-solid-eye-slash"></text>
                <text v-else class="i-fa-solid-eye"></text>
              </text>
            </view>
            <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
          </view>

          <!-- 验证码 -->
          <view class="form-item" style="--item-index: 7">
            <text class="form-label">验证码 <text class="required">*</text></text>
            <view class="code-container">
              <view class="input-container flex-1">
                <input
                  type="text"
                  v-model="form.code"
                  placeholder="请输入邮箱验证码"
                  class="form-input"
                  :class="{ 'input-error': errors.code }"
                  maxlength="6"
                />
                <text class="input-icon i-fa-solid-shield-halved"></text>
              </view>
              <button
                type="button"
                @click="handleSendCode"
                :disabled="countdown > 0 || sendCodeLoading"
                class="code-btn"
                :class="{ 'btn-disabled': countdown > 0 || sendCodeLoading }"
              >
                <text v-if="sendCodeLoading" class="loading-spinner"></text>
                <text>{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}</text>
              </button>
            </view>
            <text v-if="errors.code" class="error-text">{{ errors.code }}</text>
          </view>

          <!-- 用户协议 -->
          <view class="form-item agreement-item" style="--item-index: 8">
            <view class="agreement-container" @click="form.agreement = !form.agreement">
              <text
                class="checkbox-icon"
                :class="form.agreement ? 'i-fa-solid-square-check checked' : 'i-fa-regular-square'"
              ></text>
              <view class="agreement-text">
                <text class="agreement-label">我已阅读并同意</text>
                <text class="agreement-link">《用户协议》</text>
                <text class="agreement-label">和</text>
                <text class="agreement-link">《隐私政策》</text>
              </view>
            </view>
            <text v-if="errors.agreement" class="error-text">{{ errors.agreement }}</text>
          </view>

          <!-- 注册按钮 -->
          <button
            type="button"
            @click="handleRegister"
            :disabled="isLoading"
            class="register-btn"
            :class="{ 'btn-loading': isLoading }"
          >
            <view class="btn-content">
              <view v-if="isLoading" class="loading-spinner"></view>
              <text v-else class="i-fa-solid-user-plus mr-2"></text>
              <text>{{ isLoading ? '注册中...' : '立即注册' }}</text>
            </view>
          </button>

          <!-- 已有账号 -->
          <view class="login-link">
            <text>已有账号？</text>
            <text @click="goToLogin" class="link-text">
              <text class="i-fa-solid-arrow-right-to-bracket mr-1"></text>
              立即登录
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
/* 基础样式 */
.register-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fdfc 0%, #e8f5f2 100%);
  overflow-x: hidden;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease;
  
  &.success {
    border-left: 8rpx solid #00C9A7;
  }
  
  &.error {
    border-left: 8rpx solid #F56565;
  }
  
  &.info {
    border-left: 8rpx solid #3498db;
  }
}

.notification-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
  
  .success & {
    color: #00C9A7;
  }
  
  .error & {
    color: #F56565;
  }
  
  .info & {
    color: #3498db;
  }
}

.notification-message {
  font-size: 28rpx;
  color: #333;
}

/* 主内容区域 */
.content-area {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding: 40rpx 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 装饰圆圈 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  backdrop-filter: blur(20rpx);
}

.decoration-circle-1 {
  top: 160rpx;
  right: 60rpx;
  width: 240rpx;
  height: 240rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.2) 0%, rgba(0, 201, 167, 0.4) 100%);
  animation: floating 3s ease-in-out infinite;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);
}

.decoration-circle-2 {
  bottom: 300rpx;
  left: 60rpx;
  width: 180rpx;
  height: 180rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.3) 100%);
  animation: floating 4s ease-in-out infinite;
  animation-direction: reverse;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.1);
}

.decoration-circle-3 {
  top: 50%;
  right: -80rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.2) 100%);
  animation: floating 5s ease-in-out infinite;
}

/* 页面内容 */
.page-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 750rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  width: 100%;
  margin-bottom: 40rpx;
  text-align: center;
  animation: slideInDown 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 20rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
  }

  text {
    font-size: 60rpx;
    position: relative;
    z-index: 1;
  }
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 10rpx;
  background: linear-gradient(135deg, #333 0%, #00B39A 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #777;
}

/* 表单容器 */
.form-container {
  width: 100%;
  animation: slideInUp 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0.2s both;
}

.form-item {
  margin-bottom: 24rpx;
  animation: slideInLeft 0.6s cubic-bezier(0.19, 1, 0.22, 1) calc(var(--item-index, 0) * 0.1s + 0.3s) both;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.required {
  color: #F56565;
  margin-left: 4rpx;
}

.input-container {
  position: relative;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 96rpx;
  padding: 0 96rpx 0 30rpx;
  font-size: 30rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:focus {
    border-color: #00C9A7;
    box-shadow: 0 0 0 2rpx rgba(0, 201, 167, 0.2);
    background: #fff;
    transform: translateY(-2rpx);
  }

  &::placeholder {
    color: #A3A3A3;
  }

  &:focus::placeholder {
    color: transparent;
  }
}

.input-error {
  border-color: #F56565 !important;
  box-shadow: 0 0 0 2rpx rgba(245, 101, 101, 0.2) !important;
  animation: shake 0.5s ease-in-out;
}

.input-icon {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 36rpx;
  color: #A3A3A3;
  transform: translateY(-50%);
}

.input-icon-toggle {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 36rpx;
  color: #A3A3A3;
  transform: translateY(-50%);
  cursor: pointer;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;

  &:active {
    color: #00C9A7;
    background: rgba(0, 201, 167, 0.1);
    transform: translateY(-50%) scale(0.95);
  }
}

.error-text {
  display: block;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #F56565;
  animation: slideInDown 0.3s ease-out;
}

/* 专业选择器 */
.select-container {
  position: relative;
  width: 100%;
}

.form-picker {
  width: 100%;
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 96rpx;
  padding: 0 30rpx;
  font-size: 30rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.picker-arrow {
  font-size: 28rpx;
  color: #A3A3A3;
}

/* 年级选择 */
.grade-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.grade-btn {
  flex: 1;
  height: 80rpx;
  min-width: 120rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  padding: 0;
  
  &.active {
    color: #fff;
    background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
    border-color: transparent;
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
    transform: translateY(-2rpx);
  }
}

/* 密码强度 */
.password-strength {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #777;
}

.strength-weak {
  color: #F56565;
}

.strength-medium {
  color: #ED8936;
}

.strength-strong {
  color: #00C9A7;
}

/* 验证码输入 */
.code-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.code-btn {
  height: 96rpx;
  min-width: 180rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #00C9A7;
  background: #FFFFFF;
  border: 2rpx solid #00C9A7;
  border-radius: 16rpx;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
}

.btn-disabled {
  opacity: 0.5;
}

/* 协议同意 */
.agreement-item {
  margin: 30rpx 0;
}

.agreement-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-icon {
  font-size: 36rpx;
  color: #A3A3A3;
  margin-right: 12rpx;
  
  &.checked {
    color: #00C9A7;
  }
}

.agreement-text {
  display: flex;
  flex-wrap: wrap;
  font-size: 28rpx;
}

.agreement-label {
  color: #777;
}

.agreement-link {
  color: #00C9A7;
  margin: 0 4rpx;
}

/* 注册按钮 */
.register-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  margin: 40rpx 0 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
  border: none;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
  animation: slideInUp 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.6s both;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 4rpx 8rpx rgba(0, 201, 167, 0.2);

    &::before {
      opacity: 1;
    }
  }
}

.btn-loading {
  opacity: 0.8;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 登录链接 */
.login-link {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20rpx 0 40rpx;
  font-size: 28rpx;
  color: #777;
}

.link-text {
  display: inline-flex;
  align-items: center;
  color: #00C9A7;
  margin-left: 8rpx;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  
  &:active {
    background: rgba(0, 201, 167, 0.1);
    transform: scale(0.95);
  }
}

/* 动画 */
@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

@keyframes slideDown {
  from {
    transform: translate(-50%, -20rpx);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5rpx);
  }
}
</style>
