/**
 * @description 能力评估相关API
 */
import {http} from '@/utils/http'
import type { 
  AssessmentQuestion, 
  AssessmentResult, 
  InitialAbilityAssessment,
  UserGrowthProfile,
  DetailedAbilityReport 
} from '@/types/onboarding'

// 演示数据：评估问题
const mockQuestions: AssessmentQuestion[] = [
  {
    id: 'q1',
    type: 'single',
    category: 'professionalKnowledge',
    question: '当面试官问到你不熟悉的技术概念时，你通常会怎么回应？',
    options: [
      { id: 'a', text: '直接承认不了解，但表达学习意愿', score: 4 },
      { id: 'b', text: '尝试从相关知识推导解释', score: 5 },
      { id: 'c', text: '避开话题，转向熟悉的领域', score: 2 },
      { id: 'd', text: '模糊回应，希望蒙混过关', score: 1 },
    ],
  },
  {
    id: 'q2',
    type: 'scale',
    category: 'logicalThinking',
    question: '请评估你解决复杂逻辑问题的能力（如算法题、系统设计等）',
    minValue: 1,
    maxValue: 5,
  },
  {
    id: 'q3',
    type: 'single',
    category: 'languageExpression',
    question: '描述项目经验时，你认为最重要的是什么？',
    options: [
      { id: 'a', text: '详细说明技术实现细节', score: 3 },
      { id: 'b', text: '突出项目的业务价值和成果', score: 5 },
      { id: 'c', text: '强调个人在项目中的贡献', score: 4 },
      { id: 'd', text: '简要概述项目功能', score: 2 },
    ],
  },
  {
    id: 'q4',
    type: 'scale',
    category: 'stressResistance',
    question: '面对时间紧迫或高压面试环境，你的应对能力如何？',
    minValue: 1,
    maxValue: 5,
  },
  {
    id: 'q5',
    type: 'single',
    category: 'teamCollaboration',
    question: '如果你和团队成员在技术方案上有分歧，你会？',
    options: [
      { id: 'a', text: '坚持自己的观点，据理力争', score: 2 },
      { id: 'b', text: '主动寻求共识，考虑多方意见', score: 5 },
      { id: 'c', text: '服从多数决定', score: 3 },
      { id: 'd', text: '请求上级或专家裁决', score: 4 },
    ],
  },
  {
    id: 'q6',
    type: 'scale',
    category: 'innovation',
    question: '你在工作或学习中提出创新想法和解决方案的频率如何？',
    minValue: 1,
    maxValue: 5,
  },
  {
    id: 'q7',
    type: 'single',
    category: 'professionalKnowledge',
    question: '面试前你通常如何准备技术问题？',
    options: [
      { id: 'a', text: '系统复习基础知识和核心概念', score: 5 },
      { id: 'b', text: '针对岗位要求重点准备', score: 4 },
      { id: 'c', text: '简单浏览常见面试题', score: 2 },
      { id: 'd', text: '依靠平时积累，不特别准备', score: 3 },
    ],
  },
  {
    id: 'q8',
    type: 'single',
    category: 'languageExpression',
    question: '当面试官提出开放性问题时，你的回答策略是？',
    options: [
      { id: 'a', text: '用STAR法则结构化回答', score: 5 },
      { id: 'b', text: '举具体例子说明', score: 4 },
      { id: 'c', text: '简洁直接回答要点', score: 3 },
      { id: 'd', text: '随机应变，想到什么说什么', score: 2 },
    ],
  },
]

// 演示数据：评估结果
const mockAssessmentResult: InitialAbilityAssessment = {
  professionalKnowledge: 78,
  logicalThinking: 82,
  languageExpression: 75,
  stressResistance: 68,
  teamCollaboration: 85,
  innovation: 72,
  overallScore: 76
}

// 演示数据：用户成长档案
const mockGrowthProfile: UserGrowthProfile = {
  userId: 'user_demo',
  currentStage: 'intermediate',
  joinDate: new Date().toISOString(),
  lastActiveDate: new Date().toISOString(),
  totalInterviews: 3,
  initialAssessment: mockAssessmentResult,
  currentAssessment: mockAssessmentResult,
  improvementRate: 15,
  targetPosition: '前端工程师',
  learningGoals: ['掌握React高级特性', '提升算法能力', '优化表达能力'],
  achievements: ['完成初次评估', '完成3次模拟面试'],
  continuousLearningDays: 5,
  completedCourses: 2
}

// 演示数据：详细能力报告
const mockDetailedReport: DetailedAbilityReport = {
  strengths: [
    '团队协作能力出色，善于与他人合作',
    '逻辑思维能力较强，能够解决复杂问题',
    '专业知识掌握扎实，对技术有较深理解'
  ],
  weaknesses: [
    '抗压能力有待提高，在高压环境下可能表现受影响',
    '创新思维可以进一步加强',
    '语言表达能力需要持续练习'
  ],
  recommendations: {
    resources: [
      {
        title: 'React高级模式与最佳实践',
        type: '在线课程',
        url: 'https://example.com/course/react-advanced',
        description: '深入学习React高级特性和性能优化'
      },
      {
        title: '算法与数据结构精讲',
        type: '电子书',
        url: 'https://example.com/book/algorithm',
        description: '从基础到高级的算法学习指南'
      },
      {
        title: '技术面试表达技巧',
        type: '视频课程',
        url: 'https://example.com/course/interview-expression',
        description: '提升面试沟通和表达能力'
      }
    ],
    practices: [
      {
        title: '模拟高压面试环境',
        description: '每周安排2-3次限时模拟面试，提高抗压能力',
        difficulty: '中等'
      },
      {
        title: '项目实战训练',
        description: '参与开源项目，提升实际问题解决能力',
        difficulty: '高级'
      },
      {
        title: '演讲练习',
        description: '每天练习技术主题演讲，录制并分析',
        difficulty: '初级'
      }
    ]
  }
}

/**
 * @description 获取评估问题
 * @returns Promise 评估问题列表
 */
export async function getAssessmentQuestions() {
  try {
    const res = await http.get('/assessment/questions', {
      method: 'GET'
    })
    return res
  } catch (error) {
    console.error('获取评估问题失败', error)
    // 返回模拟数据
    return {
      code: 200,
      msg: 'success',
      data: mockQuestions
    }
  }
}

/**
 * @description 提交评估结果
 * @param results 评估结果
 * @returns Promise 评估分析
 */
export async function submitAssessmentResults(results: AssessmentResult[]) {
  try {
    const res = await http.post('/assessment/submit', {
      method: 'POST',
      data: results
    })
    return res
  } catch (error) {
    console.error('提交评估结果失败', error)
    // 返回模拟数据
    return {
      code: 200,
      msg: 'success',
      data: mockAssessmentResult
    }
  }
}

/**
 * @description 获取详细能力报告
 * @returns Promise 详细能力报告
 */
export async function getDetailedAbilityReport() {
  try {
    const res = await http.get('/assessment/report', {
      method: 'GET'
    })
    return res
  } catch (error) {
    console.error('获取详细能力报告失败', error)
    // 返回模拟数据
    return {
      code: 200,
      msg: 'success',
      data: mockDetailedReport
    }
  }
}

/**
 * @description 获取用户成长档案
 * @returns Promise 用户成长档案
 */
export async function getUserGrowthProfile() {
  try {
    const res = await http.get('/assessment/profile', {
      method: 'GET'
    })
    return res
  } catch (error) {
    console.error('获取用户成长档案失败', error)
    // 返回模拟数据
    return {
      code: 200,
      msg: 'success',
      data: mockGrowthProfile
    }
  }
} 