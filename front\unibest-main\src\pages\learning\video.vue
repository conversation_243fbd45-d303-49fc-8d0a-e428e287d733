<script setup lang="ts">
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import HeadBar from '@/components/HeadBar.vue'
import LoadingCard from '@/components/LoadingCard.vue'
import Loading from '@/components/Loading.vue'
import CategoryFilter from '@/components/CategoryFilter.vue'
import FilterModal from '@/components/FilterModal.vue'
import Notification from '@/components/Notification.vue'
import SearchBox from '@/components/SearchBox.vue'
// 引入API服务
import {
  getVideoList,
  getVideoLearningStats,
  toggleVideoCollect,
  getBookmarkedVideos,
  getPurchasedVideos,
  getLearningHistory,
} from '@/service/video'
// 引入类型定义
import type { Ref } from 'vue'
import type {
  Video,
  VideoLearningStats,
  VideoQueryParams,
  VideoCategoryOption,
} from '@/types/video'

// 定义分页参数类型
interface PaginationParams {
  page: number
  pageSize: number
  total: number
  hasMore: boolean
}

// 页面加载状态
const isPageLoaded: Ref<boolean> = ref(false)
const isLoading: Ref<boolean> = ref(false)
const isInitialLoading: Ref<boolean> = ref(true)
const isRefreshing: Ref<boolean> = ref(false)
const isLoadingMore: Ref<boolean> = ref(false)

// 加载类型状态
const loadingType: Ref<'normal' | 'category' | 'search' | 'filter'> = ref('normal')

// 分页状态
const pagination: Ref<PaginationParams> = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  hasMore: true,
})

// 分类选项
const categoryOptions: Ref<VideoCategoryOption[]> = ref([
  { key: 'all', name: '全部', icon: 'i-mdi-view-grid' },
  { key: '面试技巧', name: '面试技巧', icon: 'i-mdi-briefcase' },
  { key: '技术专业', name: '技术专业', icon: 'i-mdi-code-braces' },
  { key: '算法讲解', name: '算法讲解', icon: 'i-mdi-function-variant' },
  { key: '项目经验', name: '项目经验', icon: 'i-mdi-folder-open' },
  { key: '沟通表达', name: '沟通表达', icon: 'i-mdi-account-voice' },
])

// 当前选中的分类
const selectedCategory: Ref<string> = ref('all')

// 搜索关键词
const searchQuery: Ref<string> = ref('')

// 排序方式
const sortBy: Ref<'latest' | 'popular' | 'rating' | 'price'> = ref('latest')

// 筛选条件
const showFreeOnly: Ref<boolean> = ref(false)
const selectedDifficulty: Ref<string> = ref('all')

// 弹框状态
const showFilterModal: Ref<boolean> = ref(false)

// 筛选配置
const filterSections = ref([
  {
    type: 'select' as const,
    key: 'difficulty',
    title: '难度等级',
    defaultValue: 'all',
    options: [
      { key: 'all', label: '全部' },
      { key: '入门', label: '入门' },
      { key: '进阶', label: '进阶' },
      { key: '高级', label: '高级' },
    ],
  },
  {
    type: 'toggle' as const,
    key: 'showFreeOnly',
    title: '价格类型',
    toggleText: '仅显示免费课程',
    defaultValue: false,
  },
  {
    type: 'select' as const,
    key: 'sortBy',
    title: '排序方式',
    defaultValue: 'latest',
    options: [
      { key: 'latest', label: '最新发布' },
      { key: 'popular', label: '最受欢迎' },
      { key: 'rating', label: '评分最高' },
      { key: 'price', label: '价格排序' },
    ],
  },
])

// 当前筛选值
const currentFilters = ref({
  difficulty: selectedDifficulty.value,
  showFreeOnly: showFreeOnly.value,
  sortBy: sortBy.value,
})

// 通知组件状态
const notificationVisible: Ref<boolean> = ref(false)
const notificationMessage: Ref<string> = ref('')
const notificationType: Ref<'success' | 'error' | 'info' | 'warning'> = ref('info')
const notificationDuration: Ref<number> = ref(3000)

// 视频课程数据
const videoList: Ref<Video[]> = ref([])

// 学习统计数据
const learningStats: Ref<VideoLearningStats> = ref({
  totalVideos: 0,
  completedVideos: 0,
  totalHours: 0,
  studiedToday: 0,
  weeklyGoal: 10,
  weeklyProgress: 0,
  categoryStats: [],
})

// 当前显示的列表类型
const currentListType: Ref<'all' | 'bookmarked' | 'purchased' | 'history'> = ref('all')

// 筛选后的视频列表
const filteredVideos = computed(() => {
  const videos = videoList.value
  console.log(videos)
  return videos
})

/**
 * @description 获取加载文本
 * @returns 返回当前加载类型对应的提示文本
 */
const getLoadingText = computed(() => {
  const textMap = {
    normal: '加载中...',
    category: '正在筛选课程...',
    search: '正在搜索课程...',
    filter: '正在应用筛选条件...',
  }
  return textMap[loadingType.value]
})

/**
 * @description 加载视频数据
 * @param isRefresh 是否为刷新操作
 * @param isLoadMore 是否为加载更多操作
 */
const loadVideoData = async (isRefresh = false, isLoadMore = false): Promise<void> => {
  try {
    if (isRefresh) {
      isRefreshing.value = true
      pagination.value.page = 1
    } else if (isLoadMore) {
      isLoadingMore.value = true
      pagination.value.page += 1
    } else {
      isLoading.value = true
    }

    const params: VideoQueryParams = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
      keyword: searchQuery.value.trim(),
      category: selectedCategory.value === 'all' ? undefined : selectedCategory.value,
      difficulty: selectedDifficulty.value === 'all' ? undefined : selectedDifficulty.value,
      free: showFreeOnly.value ? true : undefined,
      sortBy: sortBy.value,
      sortOrder: 'desc',
    }

    let response
    switch (currentListType.value) {
      case 'bookmarked':
        response = await getBookmarkedVideos(params)
        break
      case 'purchased':
        response = await getPurchasedVideos(params)
        break
      case 'history':
        response = await getLearningHistory(params)
        break
      default:
        response = await getVideoList(params)
    }
    console.log(response)

    if (response.code === 200) {
      const { videos, total, hasMore } = response.data
      if (isRefresh || (!isLoadMore && pagination.value.page === 1)) {
        videoList.value = videos
      } else if (isLoadMore) {
        videoList.value.push(...videos)
      }

      pagination.value.total = total
      pagination.value.hasMore = hasMore

      if (isRefresh) {
        showNotification('刷新成功', 'success', 1500)
      }
    } else {
      throw new Error(response.message || '加载失败')
    }
  } catch (error) {
    console.error('加载视频数据失败:', error)

    if (isLoadMore) {
      pagination.value.page -= 1 // 回滚页码
    }

    showNotification('加载失败，请重试', 'error', 2000)
  } finally {
    isRefreshing.value = false
    isLoadingMore.value = false
    isLoading.value = false
  }
}

/**
 * @description 加载学习统计数据
 */
const loadLearningStats = async (): Promise<void> => {
  try {
    const response = await getVideoLearningStats()
    if (response.code === 200) {
      learningStats.value = response.data
    }
  } catch (error) {
    console.error('加载学习统计失败:', error)
  }
}

/**
 * @description 获取难度对应的样式类名
 * @param difficulty 难度等级（入门/进阶/高级）
 * @returns 返回对应的样式类名字符串
 */
const getDifficultyStyle = (difficulty: string): string => {
  const styleMap: Record<string, string> = {
    入门: 'bg-green-100 text-green-700',
    进阶: 'bg-yellow-100 text-yellow-700',
    高级: 'bg-red-100 text-red-700',
  }
  return styleMap[difficulty] || 'bg-gray-100 text-gray-700'
}

/**
 * @description 处理分类筛选变化
 * @param category 选中的分类
 */
const handleCategoryChange = async (category: string): Promise<void> => {
  selectedCategory.value = category
  pagination.value.page = 1
  loadingType.value = 'category' // 设置加载类型为分类筛选
  await loadVideoData()
}

/**
 * @description 处理分类筛选重置
 */
const handleCategoryReset = async (): Promise<void> => {
  selectedCategory.value = 'all'
  selectedDifficulty.value = 'all'
  showFreeOnly.value = false
  pagination.value.page = 1
  loadingType.value = 'category' // 设置加载类型为分类筛选
  await loadVideoData()
}

/**
 * @description 处理搜索事件
 * @param searchValue 搜索关键词
 */
const handleSearch = async (searchValue: string): Promise<void> => {
  searchQuery.value = searchValue
  pagination.value.page = 1
  loadingType.value = 'search' // 设置加载类型为搜索
  await loadVideoData()
}

/**
 * @description 清除搜索内容
 */
const clearSearch = async (): Promise<void> => {
  // SearchBox组件会自动清除输入框内容
  // 我们只需要重置查询参数并重新加载数据
  pagination.value.page = 1
  loadingType.value = 'normal' // 设置加载类型为普通加载
  await loadVideoData()
}

/**
 * @description 处理下拉刷新
 */
const handleRefresh = async (): Promise<void> => {
  console.log('handleRefresh')
  await Promise.all([loadVideoData(true, false), loadLearningStats()])
}

/**
 * @description 处理上拉加载更多
 */
const handleLoadMore = async (): Promise<void> => {
  if (!pagination.value.hasMore || isLoadingMore.value || isLoading.value) {
    return
  }

  await loadVideoData(false, true)
}

/**
 * @description 播放视频课程
 * @param video 视频对象
 */
const playVideo = (video: Video): void => {
  if (!video.free && video.price > 0) {
    uni.showModal({
      title: '付费课程',
      content: `《${video.title}》是付费课程，价格：¥${video.price}，是否购买？`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到支付页面
          uni.navigateTo({
            url: `/pages/pay/pay?id=${video.id}&type=video`,
          })
        }
      },
    })
    return
  }

  uni.navigateTo({
    url: `/pages/learning/video-player?id=${video.id}`,
  })
}

/**
 * @description 切换视频收藏状态
 * @param video 视频对象
 */
const toggleBookmark = async (video: Video): Promise<void> => {
  try {
    const response = await toggleVideoCollect(video.id, !video.isBookmarked)
    if (response.code === 200) {
      video.isBookmarked = !video.isBookmarked
      showNotification(video.isBookmarked ? '已收藏' : '已取消收藏', 'success', 1500)
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    showNotification('操作失败，请重试', 'error', 2000)
  }
}

/**
 * @description 切换列表类型
 * @param type 列表类型
 */
const switchListType = async (
  type: 'all' | 'bookmarked' | 'purchased' | 'history',
): Promise<void> => {
  currentListType.value = type
  pagination.value.page = 1
  loadingType.value = 'normal' // 设置加载类型为普通加载
  await loadVideoData()
}

/**
 * @description 显示筛选弹框
 */
const showFilterPopup = (): void => {
  showFilterModal.value = true
}

/**
 * @description 处理头部右侧按钮点击
 */
const handleRightBtnClick = (): void => {
  showFilterPopup()
}

/**
 * @description 关闭筛选弹框
 */
const hideFilterPopup = (): void => {
  showFilterModal.value = false
}

/**
 * @description 处理筛选确认
 * @param filters 筛选值
 */
const handleFilterConfirm = async (filters: Record<string, any>): Promise<void> => {
  selectedDifficulty.value = filters.difficulty
  showFreeOnly.value = filters.showFreeOnly
  sortBy.value = filters.sortBy

  pagination.value.page = 1
  loadingType.value = 'filter' // 设置加载类型为筛选
  await loadVideoData()
}

/**
 * @description 处理筛选重置
 */
const handleFilterReset = async (): Promise<void> => {
  selectedDifficulty.value = 'all'
  showFreeOnly.value = false
  sortBy.value = 'latest'

  currentFilters.value = {
    difficulty: 'all',
    showFreeOnly: false,
    sortBy: 'latest',
  }

  pagination.value.page = 1
  loadingType.value = 'filter' // 设置加载类型为筛选
  await loadVideoData()
}

/**
 * @description 显示通知
 * @param message 通知内容
 * @param type 通知类型（success/error/info/warning）
 * @param duration 显示时长（毫秒）
 */
const showNotification = (
  message: string,
  type: 'success' | 'error' | 'info' | 'warning' = 'info',
  duration: number = 3000,
): void => {
  notificationMessage.value = message
  notificationType.value = type
  notificationDuration.value = duration
  notificationVisible.value = true
}

/**
 * @description 处理通知关闭
 */
const handleNotificationClose = (): void => {
  notificationVisible.value = false
}

/**
 * @description 获取当前主要筛选项
 */
const getMainFilterItems = () => {
  const items = []

  // 难度筛选
  if (selectedDifficulty.value !== 'all') {
    items.push({
      type: 'difficulty',
      label: selectedDifficulty.value,
      value: selectedDifficulty.value,
    })
  }

  // 免费筛选
  if (showFreeOnly.value) {
    items.push({
      type: 'free',
      label: '免费',
      value: 'free',
    })
  }

  // 排序
  const sortLabels = {
    latest: '最新',
    popular: '最热',
    rating: '评分',
    price: '价格',
  }
  items.push({
    type: 'sort',
    label: sortLabels[sortBy.value],
    value: sortBy.value,
  })

  return items
}

/**
 * @description 移除筛选项
 * @param item 要移除的筛选项
 */
const removeFilter = async (item: any): Promise<void> => {
  if (item.type === 'difficulty') {
    selectedDifficulty.value = 'all'
  } else if (item.type === 'free') {
    showFreeOnly.value = false
  }

  pagination.value.page = 1
  loadingType.value = 'filter' // 设置加载类型为筛选
  await loadVideoData()
}

/**
 * @description 格式化视频时长显示
 * @param duration 时长字符串
 * @returns 返回格式化后的时长字符串
 */
const formatDuration = (duration: string): string => {
  return duration
}

/**
 * @description 初始化页面动画效果
 */
const initPage = async (): Promise<void> => {
  await nextTick()
  setTimeout(() => {
    isPageLoaded.value = true
  }, 100)
}

/**
 * @description 初始化页面数据
 */
const initializePageData = async (): Promise<void> => {
  try {
    // 并行加载数据
    await Promise.all([loadVideoData()])
  } catch (error) {
    console.error('页面数据初始化失败:', error)
    showNotification('页面加载失败，请重试', 'error', 2000)
  } finally {
    isInitialLoading.value = false
  }
}

/**
 * @description 获取随机颜色
 * @returns 返回随机颜色
 */
const getRandomColor = (): string => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16)
}

/**
 * @description 获取随机渐变背景和文字颜色
 * @returns 返回包含背景渐变和文字颜色的样式对象
 */
const getRandomTagStyle = (): { background: string; color: string; animationDelay: string } => {
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #ff8a80 0%, #ffab40 100%)',
    'linear-gradient(135deg, #81c784 0%, #aed581 100%)',
    'linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%)',
    'linear-gradient(135deg, #ba68c8 0%, #9575cd 100%)',
    'linear-gradient(135deg, #4db6ac 0%, #26a69a 100%)',
  ]

  const colors = ['#ffffff', '#f8f9fa', '#fff']
  const randomGradient = gradients[Math.floor(Math.random() * gradients.length)]
  const randomColor = colors[Math.floor(Math.random() * colors.length)]
  const randomDelay = (Math.random() * 2).toFixed(1) + 's'

  return {
    background: randomGradient,
    color: randomColor,
    animationDelay: randomDelay,
  }
}

// 移除搜索关键词监听，现在使用点击按钮搜索

// 监听筛选条件变化，同步到currentFilters
watch([selectedDifficulty, showFreeOnly, sortBy], () => {
  currentFilters.value = {
    difficulty: selectedDifficulty.value,
    showFreeOnly: showFreeOnly.value,
    sortBy: sortBy.value,
  }
})

// 页面加载时执行初始化
onMounted(async () => {
  initPage()
  await initializePageData()
})
</script>

<template>
  <view class="video-container">
    <!-- 固定头部栏 -->
    <HeadBar
      title="视频课程"
      :show-right-button="true"
      right-icon="i-mdi-tune"
      @rightClick="handleRightBtnClick"
    />

    <!-- 主要内容区域 -->
    <scroll-view
      class="main-content"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
      @scrolltolower="handleLoadMore"
      lower-threshold="100"
    >
      <view v-if="!isInitialLoading" class="content-wrapper fade-in">
        <!-- 头部欢迎区域 -->
        <view class="welcome-section">
          <view class="welcome-content">
            <text class="welcome-title">视频课程</text>
            <text class="welcome-subtitle">精品视频课程，系统学习面试技巧</text>
          </view>
          <view class="welcome-decoration">
            <view class="i-mdi-play-circle-outline decoration-icon"></view>
          </view>
        </view>

        <!-- 学习统计 -->
        <!-- <view class="stats-section">
          <view class="stats-grid">
            <view class="stat-item">
              <view class="stat-icon-wrapper">
                <view class="i-mdi-check-circle stat-icon"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ learningStats.completedVideos }}</text>
                <text class="stat-label">已完成</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon-wrapper">
                <view class="i-mdi-clock-time-eight stat-icon"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ learningStats.totalHours }}h</text>
                <text class="stat-label">学习时长</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon-wrapper">
                <view class="i-mdi-calendar-today stat-icon"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ learningStats.studiedToday }}h</text>
                <text class="stat-label">今日学习</text>
              </view>
            </view>
          </view>

          <view class="weekly-progress">
            <view class="progress-header">
              <text class="progress-title">本周学习进度</text>
              <text class="progress-text">
                {{ learningStats.weeklyProgress }}/{{ learningStats.weeklyGoal }}h
              </text>
            </view>
            <view class="progress-bar">
              <view
                class="progress-fill"
                :style="{
                  width: (learningStats.weeklyProgress / learningStats.weeklyGoal) * 100 + '%',
                }"
              ></view>
            </view>
          </view>
        </view> -->

        <!-- 快捷切换 -->
        <view class="quick-tabs">
          <button
            class="tab-button"
            :class="currentListType === 'all' ? 'active' : ''"
            @click="switchListType('all')"
          >
            <view class="i-mdi-view-grid tab-icon"></view>
            <text class="tab-text">全部</text>
          </button>
          <button
            class="tab-button"
            :class="currentListType === 'bookmarked' ? 'active' : ''"
            @click="switchListType('bookmarked')"
          >
            <view class="i-mdi-star tab-icon"></view>
            <text class="tab-text">收藏</text>
          </button>
          <button
            class="tab-button"
            :class="currentListType === 'purchased' ? 'active' : ''"
            @click="switchListType('purchased')"
          >
            <view class="i-mdi-shopping tab-icon"></view>
            <text class="tab-text">已购</text>
          </button>
          <button
            class="tab-button"
            :class="currentListType === 'history' ? 'active' : ''"
            @click="switchListType('history')"
          >
            <view class="i-mdi-history tab-icon"></view>
            <text class="tab-text">历史</text>
          </button>
        </view>

        <!-- 搜索框 -->
        <view class="search-section">
          <SearchBox
            v-model="searchQuery"
            placeholder="搜索课程、讲师或内容..."
            search-button-text="搜索"
            :loading="isLoading"
            @search="handleSearch"
            @clear="clearSearch"
          />
        </view>

        <!-- 分类筛选 -->
        <CategoryFilter
          class="category-filter"
          title="课程分类"
          :category-options="categoryOptions"
          :selected-category="selectedCategory"
          max-width="100%"
          :show-icon="true"
          @change="handleCategoryChange"
          @reset="handleCategoryReset"
        />
        <!-- 筛选弹框 -->
        <FilterModal
          :visible="showFilterModal"
          :model-value="currentFilters"
          title="筛选条件"
          :filter-sections="filterSections"
          @update:visible="showFilterModal = $event"
          @update:model-value="currentFilters = $event"
          @confirm="handleFilterConfirm"
          @reset="handleFilterReset"
        />

        <!-- 视频列表 -->
        <view class="videos-section">
          <view class="section-header">
            <view class="section-title-wrapper">
              <text class="section-title">
                {{
                  currentListType === 'all'
                    ? '精选视频课程'
                    : currentListType === 'bookmarked'
                      ? '我的收藏'
                      : currentListType === 'purchased'
                        ? '已购课程'
                        : '学习历史'
                }}
              </text>
              <text class="section-subtitle">
                {{
                  currentListType === 'all'
                    ? '资深讲师录制，助你轻松通过面试'
                    : currentListType === 'bookmarked'
                      ? '您收藏的视频课程'
                      : currentListType === 'purchased'
                        ? '您已购买的视频课程'
                        : '您的学习记录'
                }}
              </text>
            </view>
            <text class="result-count">共 {{ pagination.total }} 个结果</text>
          </view>

          <Loading
            v-if="isLoading"
            :visible="isLoading"
            :text="getLoadingText"
            size="medium"
            type="spinner"
          />

          <!-- 视频卡片列表 -->
          <view v-else-if="filteredVideos.length > 0" class="videos-list">
            <view
              v-for="video in filteredVideos"
              :key="video.id"
              class="video-card"
              style="animation-delay: 0.2s"
              @click="playVideo(video)"
            >
              <!-- 视频缩略图 -->
              <view class="video-thumbnail">
                <image class="thumbnail-image" :src="video.thumbnail" mode="aspectFill" />
                <view class="play-overlay">
                  <view class="play-button">
                    <view class="i-mdi-play play-icon"></view>
                  </view>
                </view>
                <view class="duration-badge">
                  <view class="i-mdi-clock-outline duration-icon"></view>
                  <text class="duration-text">{{ formatDuration(video.duration) }}</text>
                </view>
                <view v-if="video.free" class="free-badge">免费</view>
                <view v-if="video.completionRate > 0" class="progress-bar-overlay">
                  <view
                    class="progress-fill-overlay"
                    :style="{ width: video.completionRate + '%' }"
                  ></view>
                </view>
              </view>

              <!-- 视频信息 -->
              <view class="video-info">
                <view class="video-header">
                  <text class="video-title">{{ video.title }}</text>
                </view>

                <text class="video-description">{{ video.description }}</text>

                <view class="video-meta">
                  <view class="instructor-info">
                    <view class="i-mdi-account-circle instructor-icon"></view>
                    <text class="instructor-name">{{ video.instructor }}</text>
                  </view>
                  <view class="difficulty-tag" :class="getDifficultyStyle(video.difficulty)">
                    <text class="difficulty-text">{{ video.difficulty }}</text>
                  </view>
                </view>

                <view class="video-stats">
                  <view class="stats-left">
                    <view class="stat-item-small">
                      <view class="i-mdi-star stat-icon"></view>
                      <text class="stat-text">{{ video.rating }}</text>
                    </view>
                    <view class="stat-item-small">
                      <view class="i-mdi-account-group stat-icon"></view>
                      <text class="stat-text">{{ video.studentCount }}人</text>
                    </view>
                    <view class="stat-item-small">
                      <view class="i-mdi-eye stat-icon"></view>
                      <text class="stat-text">{{ (video.viewCount / 1000).toFixed(1) }}k</text>
                    </view>
                  </view>
                  <text class="video-price" :class="video.free ? 'price-free' : 'price-paid'">
                    {{ video.free ? '免费' : `¥${video.price}` }}
                  </text>
                </view>

                <!-- 标签 -->
                <view class="video-tags">
                  <view
                    v-for="tag in video.tags.slice(0, 3)"
                    :key="tag"
                    class="tag animated-tag"
                    :style="getRandomTagStyle()"
                  >
                    <text class="tag-text">{{ tag }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 加载更多提示 -->
          <view v-if="isLoadingMore" class="load-more-indicator">
            <view class="loading-spinner"></view>
            <text class="loading-text">正在加载更多...</text>
          </view>

          <!-- 没有更多数据提示 -->
          <view v-else-if="!pagination.hasMore && filteredVideos.length > 0" class="no-more-data">
            <text class="no-more-text">已加载全部内容</text>
          </view>

          <!-- 空状态 -->
          <view v-else-if="filteredVideos.length === 0 && !isLoading" class="empty-state">
            <view class="i-mdi-video-off empty-icon"></view>
            <text class="empty-title">暂无相关课程</text>
            <text class="empty-desc">尝试调整筛选条件或搜索其他内容</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载状态 -->
    <LoadingCard :visible="isInitialLoading" :text="'正在加载页面...'" />

    <!-- 通知组件 -->
    <Notification
      :visible="notificationVisible"
      :message="notificationMessage"
      :type="notificationType"
      :duration="notificationDuration"
      @update:visible="notificationVisible = $event"
      @close="handleNotificationClose"
    />
  </view>
</template>

<style scoped lang="scss">
.video-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

// 主要内容区域
.main-content {
  flex: 1;
  background: transparent;
  margin-top: 120rpx; // 为固定的HeadBar预留空间
  height: calc(100vh - 120rpx); // 调整高度以适应固定头部
  
  // #ifdef MP-WEIXIN
  margin-top: calc(120rpx + var(--status-bar-height, 44px));
  height: calc(100vh - 120rpx - var(--status-bar-height, 44px));
  // #endif
}

.content-wrapper {
  padding: 0 20rpx 160rpx;

  &.fade-in {
    animation: fadeIn 0.6s ease-out;
  }
}

// 欢迎区域
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  margin: 20rpx 0 32rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);

  .welcome-content {
    flex: 1;

    .welcome-title {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: white;
    }

    .welcome-subtitle {
      font-size: 24rpx;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .welcome-decoration {
    .decoration-icon {
      font-size: 64rpx;
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 学习统计
.stats-section {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .stats-grid {
    display: flex;
    justify-content: space-around;
    margin-bottom: 24rpx;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .stat-icon-wrapper {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
        border-radius: 16rpx;

        .stat-icon {
          font-size: 32rpx;
          color: #00c9a7;
        }
      }

      .stat-info {
        display: flex;
        flex-direction: column;

        .stat-value {
          font-size: 28rpx;
          font-weight: 700;
          color: #1e293b;
        }

        .stat-label {
          font-size: 22rpx;
          color: #64748b;
        }
      }
    }
  }

  .weekly-progress {
    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .progress-title {
        font-size: 24rpx;
        color: #64748b;
      }

      .progress-text {
        font-size: 24rpx;
        font-weight: 600;
        color: #00c9a7;
      }
    }

    .progress-bar {
      height: 12rpx;
      background: #e2e8f0;
      border-radius: 6rpx;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
        border-radius: 6rpx;
        transition: width 0.6s ease-out;
      }
    }
  }
}

// 快捷切换
.quick-tabs {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .tab-button {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx;
    background: transparent;
    border: none;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &.active {
      background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);

      .tab-icon {
        color: #00c9a7;
      }

      .tab-text {
        color: #00c9a7;
        font-weight: 600;
      }
    }

    &:hover:not(.active) {
      background: #f8fafc;
    }

    .tab-icon {
      font-size: 36rpx;
      color: #64748b;
      transition: color 0.3s ease;
    }

    .tab-text {
      font-size: 24rpx;
      color: #64748b;
      transition: all 0.3s ease;
    }
  }
}

// 搜索框
.search-section {
  margin-bottom: 32rpx;
}

// 分类筛选
.category-filter {
  margin-bottom: 28rpx;
}

// 筛选和排序
.filter-sort-section {
  margin-bottom: 32rpx;

  .filter-chips {
    display: flex;
    align-items: center;
    gap: 16rpx;
    flex-wrap: wrap;

    .filter-chip {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      background: #f1f5f9;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #475569;
      border: 2rpx solid transparent;
      transition: all 0.3s ease;

      &.active {
        background: #00c9a7;
        color: white;
      }

      .chip-text {
        font-size: 24rpx;
        font-weight: 500;
      }

      .chip-close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28rpx;
        height: 28rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        margin-left: 8rpx;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .close-icon {
          font-size: 20rpx;
          color: white;
        }
      }
    }

    .more-filter-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      background: white;
      border: 2rpx solid #e2e8f0;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #475569;
      transition: all 0.3s ease;

      &:hover {
        border-color: #00c9a7;
        color: #00c9a7;
      }

      .filter-icon {
        font-size: 24rpx;
      }

      .btn-text {
        font-size: 24rpx;
        font-weight: 500;
      }
    }
  }
}

// 视频区域
.videos-section {
  .section-header {
    margin-bottom: 32rpx;

    .section-title-wrapper {
      .section-title {
        display: block;
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 700;
        color: #1e293b;
      }

      .section-subtitle {
        font-size: 24rpx;
        line-height: 1.4;
        color: #64748b;
      }
    }
  }

  .videos-list {
    .video-card {
      margin-bottom: 24rpx;
      background: white;
      border-radius: 20rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      opacity: 0;
      transition: all 0.3s ease;
      transform: translateY(30rpx);
      animation: slideInUp 0.6s ease-out forwards;

      &:active {
        transform: scale(0.98);
      }

      .video-thumbnail {
        position: relative;
        width: 100%;
        height: 400rpx;
        overflow: hidden;

        .thumbnail-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          inset: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;

          .play-button {
            width: 100rpx;
            height: 100rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            transition: all 0.3s ease;

            .play-icon {
              font-size: 48rpx;
              color: #00c9a7;
              margin-left: 8rpx;
            }
          }
        }

        .duration-badge {
          position: absolute;
          bottom: 16rpx;
          right: 16rpx;
          display: flex;
          align-items: center;
          gap: 6rpx;
          padding: 8rpx 16rpx;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          font-size: 22rpx;
          border-radius: 16rpx;

          .duration-icon {
            font-size: 20rpx;
          }
        }

        .free-badge {
          position: absolute;
          top: 16rpx;
          left: 16rpx;
          padding: 8rpx 16rpx;
          background: #16a34a;
          color: white;
          font-size: 22rpx;
          font-weight: 500;
          border-radius: 20rpx;
        }

        .progress-bar-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 6rpx;
          background: rgba(0, 0, 0, 0.3);

          .progress-fill-overlay {
            height: 100%;
            background: #00c9a7;
            transition: width 0.3s ease;
          }
        }
      }

      .video-info {
        padding: 24rpx;

        .video-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12rpx;

          .video-title {
            flex: 1;
            font-size: 30rpx;
            font-weight: 600;
            color: #1e293b;
            line-height: 1.4;
            margin-right: 16rpx;
          }
        }

        .video-description {
          display: block;
          margin-bottom: 16rpx;
          font-size: 26rpx;
          color: #64748b;
          line-height: 1.5;
        }

        .video-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;

          .instructor-info {
            display: flex;
            align-items: center;
            gap: 8rpx;

            .instructor-icon {
              font-size: 28rpx;
              color: #64748b;
            }

            .instructor-name {
              font-size: 24rpx;
              color: #64748b;
            }
          }

          .difficulty-tag {
            padding: 6rpx 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            border-radius: 12rpx;
          }
        }

        .video-stats {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;

          .stats-left {
            display: flex;
            gap: 24rpx;

            .stat-item-small {
              display: flex;
              align-items: center;
              gap: 6rpx;

              .stat-icon {
                font-size: 22rpx;
                color: #64748b;
              }

              .stat-text {
                font-size: 22rpx;
                color: #64748b;
              }

              &:first-child .stat-icon {
                color: #f59e0b;
              }
            }
          }

          .video-price {
            font-size: 32rpx;
            font-weight: 600;

            &.price-free {
              color: #16a34a;
            }

            &.price-paid {
              color: #00c9a7;
            }
          }
        }

        .video-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;

          .tag {
            padding: 8rpx 16rpx;
            background: #f1f5f9;
            border-radius: 16rpx;
            font-size: 22rpx;
            color: #475569;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;

            &.animated-tag {
              background-size: 200% 200%;
              animation: gradientShift 3s ease-in-out infinite;
              box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
              border: 1rpx solid rgba(255, 255, 255, 0.2);
              transform: scale(1);
              transition: transform 0.2s ease;

              &:hover {
                transform: scale(1.05);
                box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
              }

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                  90deg,
                  transparent,
                  rgba(255, 255, 255, 0.3),
                  transparent
                );
                animation: shimmer 2s infinite;
                animation-delay: inherit;
              }

              .tag-text {
                position: relative;
                z-index: 1;
                font-weight: 500;
                text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
              }
            }
          }
        }
      }
    }
  }

  // 加载更多指示器
  .load-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    padding: 40rpx 20rpx;

    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid #e2e8f0;
      border-top: 3rpx solid #00c9a7;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 24rpx;
      color: #64748b;
    }
  }

  // 没有更多数据提示
  .no-more-data {
    display: flex;
    justify-content: center;
    padding: 40rpx 20rpx;

    .no-more-text {
      font-size: 24rpx;
      color: #94a3b8;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      margin-bottom: 32rpx;
      font-size: 120rpx;
      color: #cbd5e1;
    }

    .empty-title {
      margin-bottom: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #475569;
    }

    .empty-desc {
      font-size: 24rpx;
      line-height: 1.5;
      color: #64748b;
      text-align: center;
    }
  }
}

// 确保 HeadBar 显示在最顶层
:deep(.head-bar) {
  z-index: 1000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

// H5端适配
/* #ifdef H5 */
.main-content {
  margin-top: 100rpx !important;
  height: calc(100vh - 100rpx) !important;
}
/* #endif */

// APP端适配  
/* #ifdef APP-PLUS */
.main-content {
  margin-top: 140rpx !important;
  height: calc(100vh - 140rpx) !important;
}
/* #endif */

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
