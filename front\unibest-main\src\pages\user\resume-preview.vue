<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import { getResumeDetail, getResumePreviewContent, getResumePreviewImage } from '@/service/resume'
// @ts-ignore
import type { ResumeDetail, ResumePreviewContent } from '@/types/resume'

/**
 * 简历在线预览页面
 *
 * 功能说明：
 * 1. 展示结构化的简历内容，包括个人信息、教育经历、工作经验等
 * 2. 支持标签页切换查看不同类型的简历信息
 * 3. 支持简历预览图片查看
 * 4. 具备完善的错误处理和加载状态管理
 *
 * <AUTHOR>
 * @version 1.0.0
 */

interface ResumePreviewParams {
  resumeId: string
}

// 常量定义
const CONSTANTS = {
  // 预览状态
  PREVIEW_STATUS: {
    LOADING: 'loading' as const,
    SUCCESS: 'success' as const,
    ERROR: 'error' as const,
  },
  // 默认标签页
  DEFAULT_TAB: 'personal',
  // 错误消息
  ERROR_MESSAGES: {
    MISSING_RESUME_ID: '缺少简历ID参数',
    RESUME_NOT_FOUND: '简历信息不存在',
    CONTENT_NOT_FOUND: '简历内容不存在',
    PREVIEW_IMAGE_NOT_FOUND: '预览图片不存在',
  },
  // Toast持续时间
  TOAST_DURATION: {
    SHORT: 2000,
    LONG: 3000,
  },
} as const

// 简历基本信息
const resumeInfo = ref<ResumeDetail | null>(null)

// 简历预览内容
const resumeContent = ref<ResumePreviewContent | null>(null)

// 预览参数
const previewParams = ref<ResumePreviewParams | null>(null)

// 是否正在加载
const loading = ref(true)

// 预览状态
const previewStatus = ref<'loading' | 'success' | 'error'>(CONSTANTS.PREVIEW_STATUS.LOADING)

// 错误信息
const errorMessage = ref('')

// 当前激活的标签页
const activeTab = ref<string>(CONSTANTS.DEFAULT_TAB)

// 简历预览图片数据
const previewImage = ref<{ imageUrl: string; thumbnailUrl?: string } | null>(null)

// 是否正在加载预览图片
const loadingPreviewImage = ref(false)

// 是否显示预览图片模态框
const showImageModal = ref(false)

// 标签页列表
const tabList = [
  { key: 'personal', label: '个人信息', icon: 'i-mdi-account' },
  { key: 'education', label: '教育经历', icon: 'i-mdi-school' },
  { key: 'work', label: '工作经验', icon: 'i-mdi-briefcase' },
  { key: 'skills', label: '技能特长', icon: 'i-mdi-star' },
  { key: 'projects', label: '项目经历', icon: 'i-mdi-folder-multiple' },
  { key: 'others', label: '其他信息', icon: 'i-mdi-information' },
]

// 计算属性：是否有项目经历
const hasProjects = computed(() => {
  return resumeContent.value?.projectList && resumeContent.value.projectList.length > 0
})

// 计算属性：是否有其他信息
const hasOthers = computed(() => {
  const content = resumeContent.value
  return (
    content &&
    ((content.awardList && content.awardList.length > 0) ||
      (content.certificateList && content.certificateList.length > 0) ||
      (content.languageList && content.languageList.length > 0) ||
      (content.hobbies && content.hobbies.length > 0) ||
      content.selfEvaluation)
  )
})

// 计算属性：过滤后的标签页列表
const filteredTabList = computed(() => {
  return tabList.filter((tab) => {
    if (tab.key === 'projects') return hasProjects.value
    if (tab.key === 'others') return hasOthers.value
    return true
  })
})

/**
 * @description 获取页面参数
 */
const getPageParams = (): void => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options

  if (options.resumeId) {
    previewParams.value = {
      resumeId: options.resumeId,
    }
  }
}

/**
 * @description 加载简历信息和预览内容
 */
const loadResumeContent = async (): Promise<void> => {
  if (!previewParams.value?.resumeId) {
    previewStatus.value = CONSTANTS.PREVIEW_STATUS.ERROR
    errorMessage.value = CONSTANTS.ERROR_MESSAGES.MISSING_RESUME_ID
    return
  }

  try {
    loading.value = true
    previewStatus.value = CONSTANTS.PREVIEW_STATUS.LOADING
    errorMessage.value = ''

    // 1. 获取简历基本信息
    console.log('获取简历详情：', previewParams.value.resumeId)
    const detailResponse = await getResumeDetail({
      params: { resumeId: previewParams.value.resumeId },
    })

    if (detailResponse.code !== 200) {
      throw new Error(detailResponse.message || '获取简历信息失败')
    }

    if (!detailResponse.data) {
      throw new Error(CONSTANTS.ERROR_MESSAGES.RESUME_NOT_FOUND)
    }

    resumeInfo.value = detailResponse.data

    // 2. 获取简历预览内容
    console.log('获取简历预览内容：', previewParams.value.resumeId)
    const contentResponse = await getResumePreviewContent({
      params: { resumeId: previewParams.value.resumeId },
    })

    if (contentResponse.code !== 200) {
      throw new Error(contentResponse.message || '获取简历内容失败')
    }

    if (!contentResponse.data) {
      throw new Error(CONSTANTS.ERROR_MESSAGES.CONTENT_NOT_FOUND)
    }

    // 验证并转换数据结构
    const validatedData = validateAndTransformResumeData(contentResponse.data)
    resumeContent.value = validatedData
    previewStatus.value = CONSTANTS.PREVIEW_STATUS.SUCCESS

    // 确保默认选中第一个可用的标签页
    if (filteredTabList.value.length > 0) {
      activeTab.value = filteredTabList.value[0].key
    }

  } catch (error: any) {
    console.error('加载简历内容失败：', error)
    previewStatus.value = CONSTANTS.PREVIEW_STATUS.ERROR

    // 根据错误类型设置不同的错误信息
    errorMessage.value = getErrorMessage(error)

    // 只在非参数错误时显示toast
    if (!error.message?.includes('缺少')) {
      uni.showToast({
        title: errorMessage.value,
        icon: 'none',
        duration: CONSTANTS.TOAST_DURATION.LONG,
      })
    }
  } finally {
    loading.value = false
  }
}

/**
 * @description 切换标签页
 */
const switchTab = (tabKey: string): void => {
  activeTab.value = tabKey
}

/**
 * @description 格式化时间
 */
const formatDate = (dateStr: string): string => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * @description 格式化时间范围
 */
const formatDateRange = (startTime: string, endTime: string, isOngoing?: boolean): string => {
  const start = formatDate(startTime)
  if (isOngoing) {
    return `${start} - 至今`
  }
  const end = formatDate(endTime)
  return `${start} - ${end}`
}

/**
 * @description 获取技能等级描述
 */
const getSkillLevelText = (level: number): string => {
  const levelMap: Record<number, string> = {
    1: '入门',
    2: '初级',
    3: '中级',
    4: '高级',
    5: '专家',
  }
  return levelMap[level] || '未知'
}

/**
 * @description 获取简历预览图片
 */
const loadResumePreviewImage = async (): Promise<void> => {
  if (!previewParams.value?.resumeId) return

  try {
    loadingPreviewImage.value = true

    console.log('获取简历预览图片：', previewParams.value.resumeId)
    const imageResponse = await getResumePreviewImage({
      params: { resumeId: previewParams.value.resumeId },
    })

    if (imageResponse.code !== 200) {
      throw new Error(imageResponse.message || '获取预览图片失败')
    }

    if (!imageResponse.data) {
      throw new Error(CONSTANTS.ERROR_MESSAGES.PREVIEW_IMAGE_NOT_FOUND)
    }

    previewImage.value = imageResponse.data

  } catch (error: any) {
    console.error('获取预览图片失败：', error)

    // 根据错误类型显示不同的提示信息
    const errorMsg = getImageErrorMessage(error)

    uni.showToast({
      title: errorMsg,
      icon: 'none',
      duration: CONSTANTS.TOAST_DURATION.SHORT,
    })
  } finally {
    loadingPreviewImage.value = false
  }
}

/**
 * @description 显示预览图片
 */
const showPreviewImage = (): void => {
  if (previewImage.value?.imageUrl) {
    showImageModal.value = true
  } else {
    loadResumePreviewImage()
  }
}

/**
 * @description 关闭预览图片模态框
 */
const closeImageModal = (): void => {
  showImageModal.value = false
}

/**
 * @description 预览图片（使用uni.previewImage）
 */
const previewImageWithUni = (): void => {
  if (!previewImage.value?.imageUrl) return

  uni.previewImage({
    urls: [previewImage.value.imageUrl],
    current: 0,
    success: () => {
      console.log('图片预览成功')
    },
    fail: (err) => {
      console.error('图片预览失败：', err)
      uni.showToast({
        title: '图片预览失败',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 获取错误信息
 */
const getErrorMessage = (error: any): string => {
  // 网络错误
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('网络')) {
    return '网络连接失败，请检查网络后重试'
  }

  // 超时错误
  if (error.code === 'TIMEOUT' || error.message?.includes('超时')) {
    return '请求超时，请重试'
  }

  // 服务器错误
  if (error.code >= 500) {
    return '服务器繁忙，请稍后重试'
  }

  // 权限错误
  if (error.code === 401 || error.code === 403) {
    return '登录已过期，请重新登录'
  }

  // 资源不存在
  if (error.code === 404 || error.message?.includes('不存在')) {
    return '简历不存在或已被删除'
  }

  // 数据格式错误
  if (error.message?.includes('数据格式') || error.message?.includes('解析')) {
    return '简历数据格式异常，请联系管理员'
  }

  // 参数错误
  if (error.message?.includes('缺少') || error.message?.includes('参数')) {
    return error.message
  }

  // 默认错误信息
  return error.message || '加载失败，请重试'
}

/**
 * @description 获取图片错误信息
 */
const getImageErrorMessage = (error: any): string => {
  // 网络错误
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('网络')) {
    return '网络连接失败，无法加载预览图'
  }

  // 服务器错误
  if (error.code >= 500) {
    return '服务器繁忙，预览图生成失败'
  }

  // 资源不存在
  if (error.code === 404) {
    return '预览图不存在，可能正在生成中'
  }

  // 文件格式不支持
  if (error.message?.includes('格式') || error.message?.includes('类型')) {
    return '简历格式不支持生成预览图'
  }

  // 默认错误信息
  return error.message || '获取预览图片失败'
}

/**
 * @description 验证并转换简历数据结构
 */
const validateAndTransformResumeData = (data: any): ResumePreviewContent => {
  try {
    // 如果数据为空或不是对象，返回默认结构
    if (!data || typeof data !== 'object') {
      console.warn('简历数据为空或格式不正确，使用默认数据结构')
      return createDefaultResumeData()
    }

    // 确保基本信息存在
    const basicInfo = data.basicInfo || {}
    const transformedBasicInfo = {
      resumeId: Number(basicInfo.resumeId) || 0,
      resumeName: String(basicInfo.resumeName || '未命名简历').trim(),
      createTime: basicInfo.createTime || new Date().toISOString(),
      updateTime: basicInfo.updateTime || new Date().toISOString(),
      isDefault: Boolean(basicInfo.isDefault),
    }

  // 确保个人信息存在
  const personalInfo = data.personalInfo || {}
  const transformedPersonalInfo = {
    name: personalInfo.name || '',
    gender: personalInfo.gender || '',
    age: personalInfo.age || null,
    phone: personalInfo.phone || '',
    email: personalInfo.email || '',
    address: personalInfo.address || '',
    summary: personalInfo.summary || '',
    jobIntention: personalInfo.jobIntention || {
      position: '',
      industry: '',
      city: '',
      salary: '',
      jobType: '',
    },
  }

    // 确保数组字段存在且为数组
    const ensureArray = (field: any): any[] => {
      return Array.isArray(field) ? field : []
    }

    return {
      basicInfo: transformedBasicInfo,
      personalInfo: transformedPersonalInfo,
      educationList: ensureArray(data.educationList),
      workExperienceList: ensureArray(data.workExperienceList),
      skillList: ensureArray(data.skillList),
      projectList: ensureArray(data.projectList),
      awardList: ensureArray(data.awardList),
      certificateList: ensureArray(data.certificateList),
      languageList: ensureArray(data.languageList),
      hobbies: ensureArray(data.hobbies),
      selfEvaluation: String(data.selfEvaluation || ''),
    }
  } catch (error) {
    console.error('数据转换失败，使用默认数据结构：', error)
    return createDefaultResumeData()
  }
}

/**
 * @description 创建默认简历数据结构
 */
const createDefaultResumeData = (): ResumePreviewContent => {
  return {
    basicInfo: {
      resumeId: 0,
      resumeName: '未命名简历',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      isDefault: false,
    },
    personalInfo: {
      name: '',
      gender: '',
      age: null,
      phone: '',
      email: '',
      address: '',
      summary: '',
      jobIntention: {
        position: '',
        industry: '',
        city: '',
        salary: '',
        jobType: '',
      },
    },
    educationList: [],
    workExperienceList: [],
    skillList: [],
    projectList: [],
    awardList: [],
    certificateList: [],
    languageList: [],
    hobbies: [],
    selfEvaluation: '',
  }
}

/**
 * @description 重新加载
 */
const reload = (): void => {
  previewStatus.value = CONSTANTS.PREVIEW_STATUS.LOADING
  errorMessage.value = ''
  loadResumeContent()
}

// 页面加载时获取简历信息
onMounted(() => {
  getPageParams()
  loadResumeContent()
})
</script>

<template>
  <view class="resume-preview-page">
    <HeadBar :title="'简历预览'" :show-back="true" />

    <view class="content">
      <!-- 加载中 -->
      <LoadingCard :visible="loading" text="正在加载简历内容..." />

      <!-- 加载成功 -->
      <template v-if="!loading && previewStatus === 'success' && resumeContent">
        <!-- 简历标题信息 -->
        <view class="resume-header">
          <view class="resume-title">{{ resumeContent.basicInfo.resumeName }}</view>
          <view class="resume-meta">
            <text class="i-mdi-clock-outline" />
            <text>更新于 {{ formatDate(resumeContent.basicInfo.updateTime) }}</text>
            <view class="default-tag" v-if="resumeContent.basicInfo.isDefault">默认简历</view>
          </view>

          <!-- 预览图片按钮 -->
          <view class="preview-actions">
            <view
              class="preview-image-btn"
              @click="showPreviewImage"
              :class="{ loading: loadingPreviewImage }"
            >
              <text class="i-mdi-image-outline" />
              <text>{{ loadingPreviewImage ? '加载中...' : '查看预览图' }}</text>
            </view>
          </view>
        </view>

        <!-- 标签页导航 -->
        <view class="tab-nav">
          <view
            v-for="tab in filteredTabList"
            :key="tab.key"
            class="tab-item"
            :class="{ active: activeTab === tab.key }"
            @click="switchTab(tab.key)"
          >
            <text :class="tab.icon" />
            <text>{{ tab.label }}</text>
          </view>
        </view>

        <!-- 标签页内容 -->
        <view class="tab-content">
          <!-- 个人信息 -->
          <view v-if="activeTab === 'personal'" class="tab-panel">
            <view class="section-card">
              <view class="section-header">
                <text class="i-mdi-account section-icon" />
                <text class="section-title">个人信息</text>
              </view>
              <view class="personal-info">
                <view class="info-row">
                  <text class="info-label">姓名</text>
                  <text class="info-value">{{ resumeContent.personalInfo.name }}</text>
                </view>
                <view class="info-row" v-if="resumeContent.personalInfo.gender">
                  <text class="info-label">性别</text>
                  <text class="info-value">{{ resumeContent.personalInfo.gender }}</text>
                </view>
                <view class="info-row" v-if="resumeContent.personalInfo.age">
                  <text class="info-label">年龄</text>
                  <text class="info-value">{{ resumeContent.personalInfo.age }}岁</text>
                </view>
                <view class="info-row" v-if="resumeContent.personalInfo.phone">
                  <text class="info-label">手机</text>
                  <text class="info-value">{{ resumeContent.personalInfo.phone }}</text>
                </view>
                <view class="info-row" v-if="resumeContent.personalInfo.email">
                  <text class="info-label">邮箱</text>
                  <text class="info-value">{{ resumeContent.personalInfo.email }}</text>
                </view>
                <view class="info-row" v-if="resumeContent.personalInfo.address">
                  <text class="info-label">地址</text>
                  <text class="info-value">{{ resumeContent.personalInfo.address }}</text>
                </view>
              </view>

              <!-- 个人简介 -->
              <view v-if="resumeContent.personalInfo.summary" class="summary-section">
                <view class="subsection-title">个人简介</view>
                <view class="summary-text">{{ resumeContent.personalInfo.summary }}</view>
              </view>

              <!-- 求职意向 -->
              <view v-if="resumeContent.personalInfo.jobIntention" class="intention-section">
                <view class="subsection-title">求职意向</view>
                <view class="intention-grid">
                  <view
                    class="intention-item"
                    v-if="resumeContent.personalInfo.jobIntention.position"
                  >
                    <text class="intention-label">期望职位</text>
                    <text class="intention-value">
                      {{ resumeContent.personalInfo.jobIntention.position }}
                    </text>
                  </view>
                  <view
                    class="intention-item"
                    v-if="resumeContent.personalInfo.jobIntention.industry"
                  >
                    <text class="intention-label">期望行业</text>
                    <text class="intention-value">
                      {{ resumeContent.personalInfo.jobIntention.industry }}
                    </text>
                  </view>
                  <view class="intention-item" v-if="resumeContent.personalInfo.jobIntention.city">
                    <text class="intention-label">期望城市</text>
                    <text class="intention-value">
                      {{ resumeContent.personalInfo.jobIntention.city }}
                    </text>
                  </view>
                  <view
                    class="intention-item"
                    v-if="resumeContent.personalInfo.jobIntention.salary"
                  >
                    <text class="intention-label">期望薪资</text>
                    <text class="intention-value">
                      {{ resumeContent.personalInfo.jobIntention.salary }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 教育经历 -->
          <view v-if="activeTab === 'education'" class="tab-panel">
            <view class="section-card">
              <view class="section-header">
                <text class="i-mdi-school section-icon" />
                <text class="section-title">教育经历</text>
              </view>
              <view class="timeline">
                <view
                  v-for="(edu, index) in resumeContent.educationList"
                  :key="edu.id || index"
                  class="timeline-item"
                >
                  <view class="timeline-dot"></view>
                  <view class="timeline-content">
                    <view class="timeline-header">
                      <view class="timeline-title">{{ edu.school }}</view>
                      <view class="timeline-time">
                        {{ formatDateRange(edu.startTime, edu.endTime, edu.isStudying) }}
                      </view>
                    </view>
                    <view class="timeline-subtitle">{{ edu.major }} · {{ edu.degree }}</view>
                    <view v-if="edu.gpa" class="timeline-meta">GPA: {{ edu.gpa }}</view>
                    <view v-if="edu.description" class="timeline-desc">{{ edu.description }}</view>
                    <view v-if="edu.mainCourses && edu.mainCourses.length" class="course-tags">
                      <text class="course-label">主要课程：</text>
                      <view class="tag-list">
                        <text v-for="course in edu.mainCourses" :key="course" class="course-tag">
                          {{ course }}
                        </text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 工作经验 -->
          <view v-if="activeTab === 'work'" class="tab-panel">
            <view class="section-card">
              <view class="section-header">
                <text class="i-mdi-briefcase section-icon" />
                <text class="section-title">工作经验</text>
              </view>
              <view class="timeline">
                <view
                  v-for="(work, index) in resumeContent.workExperienceList"
                  :key="work.id || index"
                  class="timeline-item"
                >
                  <view class="timeline-dot"></view>
                  <view class="timeline-content">
                    <view class="timeline-header">
                      <view class="timeline-title">{{ work.company }}</view>
                      <view class="timeline-time">
                        {{ formatDateRange(work.startTime, work.endTime, work.isWorking) }}
                      </view>
                    </view>
                    <view class="timeline-subtitle">
                      {{ work.position }}
                      <span v-if="work.department">· {{ work.department }}</span>
                    </view>
                    <view v-if="work.description" class="timeline-desc">
                      {{ work.description }}
                    </view>
                    <view v-if="work.achievements && work.achievements.length" class="achievements">
                      <text class="achievements-label">主要成就：</text>
                      <view class="achievement-list">
                        <view
                          v-for="achievement in work.achievements"
                          :key="achievement"
                          class="achievement-item"
                        >
                          <text class="i-mdi-check-circle achievement-icon" />
                          <text class="achievement-text">{{ achievement }}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 技能特长 -->
          <view v-if="activeTab === 'skills'" class="tab-panel">
            <view class="section-card">
              <view class="section-header">
                <text class="i-mdi-star section-icon" />
                <text class="section-title">技能特长</text>
              </view>
              <view class="skills-grid">
                <view
                  v-for="skill in resumeContent.skillList"
                  :key="skill.id || skill.name"
                  class="skill-item"
                >
                  <view class="skill-header">
                    <text class="skill-name">{{ skill.name }}</text>
                    <text v-if="skill.level" class="skill-level">
                      {{ getSkillLevelText(skill.level) }}
                    </text>
                  </view>
                  <view v-if="skill.level" class="skill-progress">
                    <view class="progress-bar">
                      <view class="progress-fill" :style="`width: ${skill.level * 20}%`"></view>
                    </view>
                  </view>
                  <view v-if="skill.type" class="skill-type">{{ skill.type }}</view>
                  <view v-if="skill.description" class="skill-desc">{{ skill.description }}</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 项目经历 -->
          <view v-if="activeTab === 'projects' && hasProjects" class="tab-panel">
            <view class="section-card">
              <view class="section-header">
                <text class="i-mdi-folder-multiple section-icon" />
                <text class="section-title">项目经历</text>
              </view>
              <view class="timeline">
                <view
                  v-for="(project, index) in resumeContent.projectList"
                  :key="project.id || index"
                  class="timeline-item"
                >
                  <view class="timeline-dot"></view>
                  <view class="timeline-content">
                    <view class="timeline-header">
                      <view class="timeline-title">{{ project.name }}</view>
                      <view class="timeline-time">
                        {{ formatDateRange(project.startTime, project.endTime) }}
                      </view>
                    </view>
                    <view class="timeline-subtitle">{{ project.role }}</view>
                    <view v-if="project.description" class="timeline-desc">
                      {{ project.description }}
                    </view>
                    <view
                      v-if="project.technologies && project.technologies.length"
                      class="tech-tags"
                    >
                      <text class="tech-label">技术栈：</text>
                      <view class="tag-list">
                        <text v-for="tech in project.technologies" :key="tech" class="tech-tag">
                          {{ tech }}
                        </text>
                      </view>
                    </view>
                    <view
                      v-if="project.achievements && project.achievements.length"
                      class="achievements"
                    >
                      <text class="achievements-label">项目成果：</text>
                      <view class="achievement-list">
                        <view
                          v-for="achievement in project.achievements"
                          :key="achievement"
                          class="achievement-item"
                        >
                          <text class="i-mdi-check-circle achievement-icon" />
                          <text class="achievement-text">{{ achievement }}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 其他信息 -->
          <view v-if="activeTab === 'others' && hasOthers" class="tab-panel">
            <!-- 获奖情况 -->
            <view
              v-if="resumeContent.awardList && resumeContent.awardList.length"
              class="section-card"
            >
              <view class="section-header">
                <text class="i-mdi-trophy section-icon" />
                <text class="section-title">获奖情况</text>
              </view>
              <view class="award-list">
                <view v-for="award in resumeContent.awardList" :key="award.id" class="award-item">
                  <view class="award-header">
                    <text class="award-name">{{ award.name }}</text>
                    <text class="award-time">{{ formatDate(award.time) }}</text>
                  </view>
                  <view class="award-issuer">{{ award.issuer }}</view>
                  <view v-if="award.level" class="award-level">{{ award.level }}</view>
                  <view v-if="award.description" class="award-desc">{{ award.description }}</view>
                </view>
              </view>
            </view>

            <!-- 证书资质 -->
            <view
              v-if="resumeContent.certificateList && resumeContent.certificateList.length"
              class="section-card"
            >
              <view class="section-header">
                <text class="i-mdi-certificate section-icon" />
                <text class="section-title">证书资质</text>
              </view>
              <view class="certificate-list">
                <view
                  v-for="cert in resumeContent.certificateList"
                  :key="cert.id"
                  class="certificate-item"
                >
                  <view class="cert-header">
                    <text class="cert-name">{{ cert.name }}</text>
                    <text class="cert-time">{{ formatDate(cert.time) }}</text>
                  </view>
                  <view class="cert-issuer">{{ cert.issuer }}</view>
                  <view v-if="cert.number" class="cert-number">证书编号：{{ cert.number }}</view>
                  <view v-if="cert.validUntil" class="cert-valid">
                    有效期至：{{ formatDate(cert.validUntil) }}
                  </view>
                </view>
              </view>
            </view>

            <!-- 语言能力 -->
            <view
              v-if="resumeContent.languageList && resumeContent.languageList.length"
              class="section-card"
            >
              <view class="section-header">
                <text class="i-mdi-translate section-icon" />
                <text class="section-title">语言能力</text>
              </view>
              <view class="language-list">
                <view
                  v-for="lang in resumeContent.languageList"
                  :key="lang.id"
                  class="language-item"
                >
                  <text class="lang-name">{{ lang.name }}</text>
                  <text class="lang-level">{{ lang.level }}</text>
                  <text v-if="lang.certificate" class="lang-cert">{{ lang.certificate }}</text>
                </view>
              </view>
            </view>

            <!-- 兴趣爱好 -->
            <view v-if="resumeContent.hobbies && resumeContent.hobbies.length" class="section-card">
              <view class="section-header">
                <text class="i-mdi-heart section-icon" />
                <text class="section-title">兴趣爱好</text>
              </view>
              <view class="hobby-tags">
                <text v-for="hobby in resumeContent.hobbies" :key="hobby" class="hobby-tag">
                  {{ hobby }}
                </text>
              </view>
            </view>

            <!-- 自我评价 -->
            <view v-if="resumeContent.selfEvaluation" class="section-card">
              <view class="section-header">
                <text class="i-mdi-account-edit section-icon" />
                <text class="section-title">自我评价</text>
              </view>
              <view class="evaluation-text">{{ resumeContent.selfEvaluation }}</view>
            </view>
          </view>
        </view>
      </template>

      <!-- 加载失败 -->
      <template v-else-if="!loading && previewStatus === 'error'">
        <view class="error-container">
          <view class="error-icon">
            <text class="i-mdi-alert-circle" />
          </view>
          <view class="error-text">{{ errorMessage }}</view>
          <view class="retry-btn" @click="reload">
            <text class="i-mdi-refresh btn-icon" />
            <text>重新加载</text>
          </view>
        </view>
      </template>

      <!-- 预览图片模态框 -->
      <view class="image-modal" v-if="showImageModal" @click="closeImageModal">
        <view class="modal-content" @click.stop>
          <view class="modal-header">
            <text class="modal-title">简历预览图</text>
            <text class="modal-close i-mdi-close" @click="closeImageModal"></text>
          </view>
          <view class="modal-body">
            <image
              v-if="previewImage?.imageUrl"
              :src="previewImage.imageUrl"
              mode="widthFix"
              class="preview-image"
              @click="previewImageWithUni"
            />
            <view v-else class="image-placeholder">
              <text class="i-mdi-image-off placeholder-icon"></text>
              <text>暂无预览图</text>
            </view>
          </view>
          <view class="modal-footer">
            <text class="image-tip">点击图片可全屏查看</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.resume-preview-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 100rpx 20rpx 40rpx;
}

// 简历标题信息
.resume-header {
  padding: 32rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.resume-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.resume-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  position: relative;

  .i-mdi-clock-outline {
    margin-right: 8rpx;
    font-size: 28rpx;
  }
}

.default-tag {
  position: absolute;
  right: 0;
  top: 0;
  padding: 6rpx 16rpx;
  font-size: 22rpx;
  color: #00b39a;
  background-color: #e6f7f5;
  border-radius: 20rpx;
}

// 预览操作区域
.preview-actions {
  margin-top: 24rpx;
  display: flex;
  justify-content: flex-end;
}

.preview-image-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #00b39a;
  color: #fff;
  border-radius: 24rpx;
  font-size: 26rpx;
  transition: all 0.3s;
  cursor: pointer;

  &:active:not(.loading) {
    transform: scale(0.96);
    background-color: #009688;
  }

  &.loading {
    opacity: 0.7;
    cursor: not-allowed;
  }

  text:first-child {
    margin-right: 8rpx;
    font-size: 28rpx;
  }
}

// 标签页导航
.tab-nav {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  overflow-x: auto;
  white-space: nowrap;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s;
  cursor: pointer;

  &.active {
    background-color: #00b39a;
    color: #fff;
  }

  text:first-child {
    font-size: 32rpx;
    margin-bottom: 8rpx;
  }
}

// 标签页内容
.tab-content {
  min-height: 400rpx;
}

.tab-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用卡片样式
.section-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-icon {
  font-size: 36rpx;
  color: #00b39a;
  margin-right: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

// 个人信息样式
.personal-info {
  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-label {
    width: 120rpx;
    font-size: 28rpx;
    color: #666;
    margin-right: 24rpx;
  }

  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: #333;
  }
}

.subsection-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin: 32rpx 0 20rpx;
}

.summary-section {
  margin-top: 32rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.intention-section {
  margin-top: 32rpx;
}

.intention-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.intention-item {
  .intention-label {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
  }

  .intention-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
}

// 时间线样式
.timeline {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 20rpx;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background-color: #e6f7f5;
  }
}

.timeline-item {
  position: relative;
  padding-left: 80rpx;
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-dot {
  position: absolute;
  left: 8rpx;
  top: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #00b39a;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 0 0 4rpx #e6f7f5;
}

.timeline-content {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.timeline-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.timeline-time {
  font-size: 24rpx;
  color: #00b39a;
  background-color: #e6f7f5;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  margin-left: 16rpx;
}

.timeline-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.timeline-meta {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

// 课程标签和成就列表
.course-tags,
.achievements,
.tech-tags {
  margin-top: 16rpx;
}

.course-label,
.achievements-label,
.tech-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.course-tag,
.tech-tag {
  padding: 6rpx 12rpx;
  background-color: #e6f7f5;
  color: #00b39a;
  font-size: 22rpx;
  border-radius: 16rpx;
}

.achievement-list {
  .achievement-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .achievement-icon {
    color: #52c41a;
    font-size: 24rpx;
    margin-right: 12rpx;
    margin-top: 2rpx;
    flex-shrink: 0;
  }

  .achievement-text {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

// 技能样式
.skills-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.skill-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.skill-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.skill-level {
  font-size: 22rpx;
  color: #00b39a;
  background-color: #e6f7f5;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
}

.skill-progress {
  margin-bottom: 12rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #00b39a;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.skill-type {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.skill-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

// 其他信息样式
.award-list,
.certificate-list {
  .award-item,
  .certificate-item {
    background-color: #f8f9fa;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .award-header,
  .cert-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12rpx;
  }

  .award-name,
  .cert-name {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  .award-time,
  .cert-time {
    font-size: 22rpx;
    color: #00b39a;
    white-space: nowrap;
    margin-left: 16rpx;
  }

  .award-issuer,
  .cert-issuer {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 8rpx;
  }

  .award-level,
  .cert-number,
  .cert-valid {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
  }

  .award-desc {
    font-size: 24rpx;
    color: #666;
    line-height: 1.4;
  }
}

.language-list {
  .language-item {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .lang-name {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-right: 16rpx;
  }

  .lang-level {
    font-size: 24rpx;
    color: #00b39a;
    background-color: #e6f7f5;
    padding: 4rpx 8rpx;
    border-radius: 12rpx;
    margin-right: 16rpx;
  }

  .lang-cert {
    font-size: 22rpx;
    color: #999;
  }
}

.hobby-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.hobby-tag {
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  font-size: 24rpx;
  border-radius: 20rpx;
}

.evaluation-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.error-icon {
  font-size: 96rpx;
  color: #ff4d4f;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 40rpx;
}

.retry-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background-color: #00b39a;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;

  &:active {
    transform: scale(0.96);
  }
}

// 预览图片模态框样式
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 36rpx;
  color: #999;
  cursor: pointer;
  padding: 8rpx;

  &:active {
    color: #666;
  }
}

.modal-body {
  flex: 1;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s;

  &:active {
    transform: scale(0.98);
  }
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  min-height: 200rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.modal-footer {
  padding: 16rpx 32rpx 24rpx;
  text-align: center;
  border-top: 2rpx solid #f0f0f0;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .intention-grid {
    grid-template-columns: 1fr;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .tab-nav {
    padding: 4rpx;
  }

  .tab-item {
    min-width: 100rpx;
    padding: 16rpx 12rpx;

    text:first-child {
      font-size: 28rpx;
    }
  }

  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .timeline-time {
    margin-left: 0;
    margin-top: 8rpx;
  }

  .image-modal {
    padding: 20rpx;
  }

  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .preview-image {
    max-height: 50vh;
  }
}
</style>
