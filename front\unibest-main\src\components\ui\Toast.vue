<template>
  <transition name="toast-fade">
    <view v-if="visible" :class="toastClasses">
      <!-- 图标 -->
      <text v-if="showIcon" :class="iconClasses"></text>

      <!-- 消息内容 -->
      <text class="ui-toast__message">{{ message }}</text>

      <!-- 关闭按钮 -->
      <text v-if="closable" class="ui-toast__close i-mdi-close" @click="handleClose"></text>
    </view>
  </transition>
</template>

<script setup lang="ts">
import { computed, watch, onMounted } from 'vue'

interface Props {
  // 是否显示
  visible?: boolean
  // 消息内容
  message?: string
  // 提示类型
  type?: 'success' | 'error' | 'warning' | 'info'
  // 显示位置
  position?: 'top' | 'center' | 'bottom'
  // 自动关闭时间(ms)，0表示不自动关闭
  duration?: number
  // 是否显示图标
  showIcon?: boolean
  // 是否可手动关闭
  closable?: boolean
  // 自定义类名
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  message: '',
  type: 'info',
  position: 'top',
  duration: 3000,
  showIcon: true,
  closable: false,
  customClass: '',
})

const emit = defineEmits<{
  close: []
  'update:visible': [value: boolean]
}>()

// 计算提示框类名
const toastClasses = computed(() => {
  const classes = [
    'ui-toast',
    `ui-toast--${props.type}`,
    `ui-toast--${props.position}`,
    props.customClass,
  ]

  return classes.join(' ')
})

// 计算图标类名
const iconClasses = computed(() => {
  const iconMap = {
    success: 'i-mdi-check-circle',
    error: 'i-mdi-alert-circle',
    warning: 'i-mdi-alert',
    info: 'i-mdi-information',
  }

  return ['ui-toast__icon', iconMap[props.type]]
})

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 自动关闭
let timer: number | null = null

watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.duration > 0) {
      timer = window.setTimeout(() => {
        handleClose()
      }, props.duration)
    } else if (timer) {
      clearTimeout(timer)
      timer = null
    }
  },
)

onMounted(() => {
  if (props.visible && props.duration > 0) {
    timer = window.setTimeout(() => {
      handleClose()
    }, props.duration)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables';
@import '@/styles/mixins';

.ui-toast {
  position: fixed;
  left: 50%;
  z-index: $z-index-tooltip;
  display: flex;
  gap: $spacing-sm;
  align-items: center;
  max-width: 80%;
  padding: $spacing-base $spacing-lg;
  font-size: $font-size-base;
  color: $text-inverse;
  word-break: break-all;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: $radius-xl;
  box-shadow: $shadow-lg;
  transform: translateX(-50%);

  // 位置样式
  &--top {
    top: 120rpx;
  }

  &--center {
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
  }

  &--bottom {
    bottom: 200rpx;
  }

  // 类型样式
  &--success {
    background: rgba($success-color, 0.9);
  }

  &--error {
    background: rgba($error-color, 0.9);
  }

  &--warning {
    background: rgba($warning-color, 0.9);
  }

  &--info {
    background: rgba($info-color, 0.9);
  }
}

// 图标
.ui-toast__icon {
  flex-shrink: 0;
  font-size: $font-size-lg;
}

// 消息内容
.ui-toast__message {
  flex: 1;
  line-height: $line-height-normal;
}

// 关闭按钮
.ui-toast__close {
  font-size: $font-size-base;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity $transition-fast;

  &:hover {
    opacity: 1;
  }
}

// 动画
.toast-fade-enter-active,
.toast-fade-leave-active {
  transition: all 0.3s ease;
}

.toast-fade-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(-20rpx);
}

.toast-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-20rpx);
}

// 响应式适配
@include mobile {
  .ui-toast {
    max-width: 90%;
    padding: $spacing-sm $spacing-base;
    font-size: $font-size-sm;

    &__icon {
      font-size: $font-size-base;
    }
  }
}
</style>
