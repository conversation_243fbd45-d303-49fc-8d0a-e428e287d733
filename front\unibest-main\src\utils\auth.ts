/**
 * 认证模块工具函数
 * 统一管理表单验证、密码强度检测等工具函数
 * <AUTHOR>
 */

import type {
  ValidationRule,
  FormValidationRules,
  PasswordStrengthInfo,
  FormErrors,
} from '@/types/auth'
import {
  VALIDATION_PATTERNS,
  VALIDATION_MESSAGES,
  PASSWORD_STRENGTH_CONFIG,
} from '@/types/auth-constants'
import { PasswordStrength } from '@/types/auth'


/**
 * 验证手机号格式
 * @param phone 手机号
 * @returns 是否有效
 */
export function validatePhone(phone: string): boolean {
  return VALIDATION_PATTERNS.PHONE.test(phone)
}

/**
 * 验证邮箱格式
 * @param email 邮箱
 * @returns 是否有效
 */
export function validateEmail(email: string): boolean {
  return VALIDATION_PATTERNS.EMAIL.test(email)
}

/**
 * 验证短信验证码格式
 * @param code 验证码
 * @returns 是否有效
 */
export function validateSmsCode(code: string): boolean {
  return VALIDATION_PATTERNS.SMS_CODE.test(code)
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 是否符合强密码要求
 */
export function validatePasswordStrength(password: string): boolean {
  return VALIDATION_PATTERNS.PASSWORD_STRONG.test(password)
}

/**
 * 验证基础密码格式（6-20位）
 * @param password 密码
 * @returns 是否有效
 */
export function validatePassword(password: string): boolean {
  return password.length >= 6 && password.length <= 20
}

/**
 * 验证学号格式
 * @param studentId 学号
 * @returns 是否有效
 */
export function validateStudentId(studentId: string): boolean {
  return VALIDATION_PATTERNS.STUDENT_ID.test(studentId)
}

/**
 * 验证真实姓名格式
 * @param realName 真实姓名
 * @returns 是否有效
 */
export function validateRealName(realName: string): boolean {
  // 2-20个字符，支持中文、英文、·
  const nameRegex = /^[\u4e00-\u9fa5a-zA-Z·]{2,20}$/
  return nameRegex.test(realName)
}

/**
 * 检测密码强度等级
 * @param password 密码
 * @returns 密码强度信息
 */
export function getPasswordStrengthInfo(password: string): PasswordStrengthInfo {
  let strength = 0

  // 长度检查
  if (password.length >= 6) strength++
  if (password.length >= 8) strength++

  // 字符类型检查
  if (/[A-Z]/.test(password)) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^A-Za-z0-9]/.test(password)) strength++

  let level: PasswordStrength
  if (strength >= 4) {
    level = PasswordStrength.STRONG
  } else if (strength >= 2) {
    level = PasswordStrength.MEDIUM
  } else {
    level = PasswordStrength.WEAK
  }

  const config =
    PASSWORD_STRENGTH_CONFIG[level.toUpperCase() as keyof typeof PASSWORD_STRENGTH_CONFIG]

  return {
    level,
    text: config.text,
    class: config.class,
  }
}

/**
 * 验证密码一致性
 * @param password 密码
 * @param confirmPassword 确认密码
 * @returns 是否一致
 */
export function validatePasswordMatch(password: string, confirmPassword: string): boolean {
  return password === confirmPassword
}

/**
 * 获取用户token
 * @returns 用户token
 */
export function getToken(): string {
  return localStorage.getItem('token') || ''
}

/**
 * 获取表单验证规则
 * @returns 验证规则配置
 */
export function getFormValidationRules(): FormValidationRules {
  return {
    phone: [
      {
        required: true,
        message: VALIDATION_MESSAGES.PHONE.REQUIRED,
      },
      {
        pattern: VALIDATION_PATTERNS.PHONE,
        message: VALIDATION_MESSAGES.PHONE.INVALID,
      },
    ],
    email: [
      {
        required: true,
        message: VALIDATION_MESSAGES.EMAIL.REQUIRED,
      },
      {
        pattern: VALIDATION_PATTERNS.EMAIL,
        message: VALIDATION_MESSAGES.EMAIL.INVALID,
      },
    ],
    code: [
      {
        required: true,
        message: VALIDATION_MESSAGES.CODE.REQUIRED,
      },
      {
        pattern: VALIDATION_PATTERNS.SMS_CODE,
        message: VALIDATION_MESSAGES.CODE.INVALID,
      },
    ],
    password: [
      {
        required: true,
        message: VALIDATION_MESSAGES.PASSWORD.REQUIRED,
      },
      {
        minLength: 6,
        message: VALIDATION_MESSAGES.PASSWORD.TOO_SHORT,
      },
      {
        maxLength: 20,
        message: VALIDATION_MESSAGES.PASSWORD.TOO_LONG,
      },
    ],
    strongPassword: [
      {
        required: true,
        message: VALIDATION_MESSAGES.PASSWORD.REQUIRED,
      },
      {
        pattern: VALIDATION_PATTERNS.PASSWORD_STRONG,
        message: VALIDATION_MESSAGES.PASSWORD.WEAK,
      },
    ],
    realName: [
      {
        required: true,
        message: VALIDATION_MESSAGES.REAL_NAME.REQUIRED,
      },
      {
        validator: validateRealName,
        message: VALIDATION_MESSAGES.REAL_NAME.INVALID,
      },
    ],
    studentId: [
      {
        required: true,
        message: VALIDATION_MESSAGES.STUDENT_ID.REQUIRED,
      },
      {
        pattern: VALIDATION_PATTERNS.STUDENT_ID,
        message: VALIDATION_MESSAGES.STUDENT_ID.INVALID,
      },
    ],
    major: [
      {
        required: true,
        message: VALIDATION_MESSAGES.MAJOR.REQUIRED,
      },
    ],
    grade: [
      {
        required: true,
        message: VALIDATION_MESSAGES.GRADE.REQUIRED,
      },
    ],
  }
}

/**
 * 验证单个字段
 * @param fieldName 字段名
 * @param value 字段值
 * @param rules 验证规则
 * @returns 验证错误信息，无错误返回空字符串
 */
export function validateField(fieldName: string, value: string, rules: ValidationRule[]): string {
  for (const rule of rules) {
    // 必填验证
    if (rule.required && (!value || value.trim() === '')) {
      return rule.message
    }

    // 跳过空值的其他验证
    if (!value || value.trim() === '') {
      continue
    }

    // 正则验证
    if (rule.pattern && !rule.pattern.test(value)) {
      return rule.message
    }

    // 长度验证
    if (rule.minLength && value.length < rule.minLength) {
      return rule.message
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return rule.message
    }

    // 自定义验证器
    if (rule.validator && !rule.validator(value)) {
      return rule.message
    }
  }

  return ''
}

/**
 * 验证整个表单
 * @param formData 表单数据
 * @param validationRules 验证规则
 * @returns 验证错误信息对象
 */
export function validateForm(
  formData: Record<string, any>,
  validationRules: FormValidationRules,
): FormErrors {
  const errors: FormErrors = {}

  for (const [fieldName, value] of Object.entries(formData)) {
    const rules = validationRules[fieldName]
    if (rules) {
      const error = validateField(fieldName, String(value || ''), rules)
      if (error) {
        errors[fieldName] = error
      }
    }
  }

  return errors
}

/**
 * 检查表单是否有效
 * @param errors 错误信息对象
 * @returns 是否有效
 */
export function isFormValid(errors: FormErrors): boolean {
  return Object.keys(errors).length === 0
}

/**
 * 清除表单错误
 * @param errors 错误信息对象
 * @param fieldName 要清除的字段名，不传则清除所有
 * @returns 新的错误信息对象
 */
export function clearFormErrors(errors: FormErrors, fieldName?: string): FormErrors {
  if (fieldName) {
    const newErrors = { ...errors }
    delete newErrors[fieldName]
    return newErrors
  }
  return {}
}

/**
 * 格式化手机号显示
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export function formatPhoneDisplay(phone: string): string {
  if (!phone || phone.length !== 11) return phone
  return `${phone.slice(0, 3)}****${phone.slice(7)}`
}

/**
 * 格式化邮箱显示
 * @param email 邮箱
 * @returns 格式化后的邮箱
 */
export function formatEmailDisplay(email: string): string {
  if (!email) return email
  const [username, domain] = email.split('@')
  if (!username || !domain) return email

  const maskedUsername =
    username.length > 2 ? `${username.slice(0, 2)}****${username.slice(-1)}` : username

  return `${maskedUsername}@${domain}`
}

/**
 * 生成随机字符串（用于状态参数等）
 * @param length 长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
