# Design Document: 演示数据实现方案

## Overview

本设计文档详细描述了为前端项目添加演示数据的实现方案。我们将创建一个统一的演示数据管理机制，并为面试房间、用户反馈列表和AI聊天页面实现具体的演示数据。方案设计遵循以下原则：

1. **无缝替代** - 演示数据应在API请求失败时自动替代真实数据
2. **结构一致** - 演示数据结构应与真实API返回数据结构保持一致
3. **真实体验** - 演示数据应模拟真实场景，提供良好的用户体验
4. **可配置性** - 演示数据应易于配置和更新
5. **开发友好** - 实现方案应对开发者友好，便于维护

## Architecture

我们将采用以下架构来实现演示数据管理：

```mermaid
graph TD
    A[API请求] --> B{API请求成功?}
    B -->|是| C[使用真实数据]
    B -->|否| D[演示数据拦截器]
    D --> E{是否有匹配的演示数据?}
    E -->|是| F[返回演示数据]
    E -->|否| G[返回错误]
    F --> H[页面渲染]
    C --> H
```

### 核心组件

1. **演示数据拦截器** - 拦截API请求，在请求失败时提供演示数据
2. **演示数据存储** - 存储各个页面和功能的演示数据
3. **演示数据生成器** - 根据需要动态生成演示数据
4. **演示数据配置** - 配置演示数据的行为和内容

## Components and Interfaces

### 1. 演示数据管理器 (DemoDataManager)

演示数据管理器是整个系统的核心，负责管理和提供演示数据。

```typescript
// 演示数据管理器接口
interface DemoDataManager {
  // 获取演示数据
  getDemoData<T>(apiPath: string, params?: any): T

  // 注册演示数据
  registerDemoData(apiPath: string, data: any): void

  // 注册演示数据生成器
  registerDemoDataGenerator(apiPath: string, generator: Function): void

  // 是否启用演示数据
  isEnabled(): boolean

  // 设置是否启用演示数据
  setEnabled(enabled: boolean): void
}
```

### 2. API拦截器 (ApiInterceptor)

API拦截器负责拦截API请求，在请求失败时提供演示数据。

```typescript
// API拦截器接口
interface ApiInterceptor {
  // 请求拦截器
  requestInterceptor(config: any): any

  // 响应拦截器
  responseInterceptor(response: any): any

  // 错误拦截器
  errorInterceptor(error: any): any
}
```

### 3. 演示数据存储 (DemoDataStore)

演示数据存储负责存储各个页面和功能的演示数据。

```typescript
// 演示数据存储接口
interface DemoDataStore {
  // 获取演示数据
  get<T>(key: string): T | null

  // 设置演示数据
  set(key: string, data: any): void

  // 检查是否存在演示数据
  has(key: string): boolean

  // 删除演示数据
  remove(key: string): void

  // 清空所有演示数据
  clear(): void
}
```

### 4. 演示数据生成器 (DemoDataGenerator)

演示数据生成器负责根据需要动态生成演示数据。

```typescript
// 演示数据生成器接口
interface DemoDataGenerator {
  // 生成演示数据
  generate<T>(params?: any): T
}
```

## Data Models

### 1. 面试房间演示数据模型

面试房间页面需要以下演示数据模型：

```typescript
// 面试会话信息
interface SessionInfo {
  sessionId: string
  jobId: number
  mode: string
  isDemo: boolean
  jobName: string
  company: string
}

// 面试问题
interface InterviewQuestion {
  id: number
  type: string
  question: string
  tips?: string
  timeLimit: number
  difficulty: number
  expectedKeywords: string[]
  evaluationPoints: string[]
}

// 情绪数据
interface EmotionData {
  happy: number
  neutral: number
  sad: number
  angry: number
  surprised: number
  [key: string]: number
}

// 多模态分析数据
interface MultiModalMetrics {
  speech: {
    clarity: number
    fluency: number
    emotion: number
    pace: number
    logic: number
    volume: number
    pitch: number
    pauseFrequency: number
  }
  video: {
    eyeContact: number
    expression: number
    gesture: number
    posture: number
    confidence: number
    microExpressions: number
    headMovement: number
    handGestures: number
  }
  text: {
    structure: number
    relevance: number
    depth: number
    keywords: number
    grammar: number
    coherence: number
    completeness: number
    starMethod: number
  }
}
```

### 2. 用户反馈列表演示数据模型

用户反馈列表页面需要以下演示数据模型：

```typescript
// 反馈统计数据
interface FeedbackStats {
  totalCount: number
  pendingCount: number
  processingCount: number
  resolvedCount: number
  typeStats: Record<string, number>
}

// 反馈状态类型
type FeedbackStatus = 'PENDING' | 'PROCESSING' | 'RESOLVED' | 'CLOSED'

// 反馈详情
interface FeedbackDetail {
  id: string
  type: string
  status: FeedbackStatus
  content: string
  createTime: number
  updateTime: number
  contactInfo?: string
  reply?: string
  handler?: string
  deviceInfo?: string
}

// 反馈列表查询参数
interface FeedbackListParams {
  pageNum: number
  pageSize: number
  type?: string
  status?: FeedbackStatus
}
```

### 3. AI聊天演示数据模型

AI聊天页面需要以下演示数据模型：

```typescript
// AI助手定义
interface AIAgent {
  id: string
  name: string
  description: string
  icon: string
  color: string
}

// 聊天消息
interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  think?: string
  showThink?: boolean
  isStreaming?: boolean
  isError?: boolean
  attachments?: Array<{
    type: 'image' | 'file'
    url: string
    name: string
  }>
}

// 聊天会话
interface ChatSession {
  id: string
  title: string
  agentId: string
  messages: ChatMessage[]
  createTime: number
  updateTime: number
}
```

## Error Handling

演示数据系统需要处理以下错误情况：

1. **API请求失败** - 当API请求失败时，系统应该自动使用演示数据
2. **演示数据不存在** - 当请求的演示数据不存在时，系统应该返回适当的错误信息
3. **演示数据格式错误** - 当演示数据格式与预期不符时，系统应该进行适当的转换或返回错误信息
4. **演示数据生成失败** - 当演示数据生成失败时，系统应该返回默认的演示数据或错误信息

错误处理流程如下：

```mermaid
flowchart TD
    A[API请求] --> B{API请求成功?}
    B -->|是| C[使用真实数据]
    B -->|否| D{有匹配的演示数据?}
    D -->|是| E[使用演示数据]
    D -->|否| F{可以生成演示数据?}
    F -->|是| G[生成演示数据]
    F -->|否| H[返回错误信息]
    G --> I{生成成功?}
    I -->|是| E
    I -->|否| H
    E --> J[返回数据]
    C --> J
```

## Testing Strategy

为了确保演示数据系统的可靠性，我们将采用以下测试策略：

1. **单元测试** - 测试各个组件的功能

   - 测试演示数据管理器的各个方法
   - 测试API拦截器的拦截功能
   - 测试演示数据存储的存取功能
   - 测试演示数据生成器的生成功能

2. **集成测试** - 测试组件之间的交互

   - 测试API拦截器与演示数据管理器的交互
   - 测试演示数据管理器与演示数据存储的交互
   - 测试演示数据管理器与演示数据生成器的交互

3. **端到端测试** - 测试整个系统的功能
   - 测试在API请求失败时是否正确提供演示数据
   - 测试演示数据是否与真实数据结构一致
   - 测试演示数据是否能够正确渲染在页面上

## Implementation Plan

实现演示数据系统的计划如下：

1. **创建核心组件**

   - 实现演示数据管理器
   - 实现API拦截器
   - 实现演示数据存储
   - 实现演示数据生成器

2. **创建演示数据**

   - 为面试房间页面创建演示数据
   - 为用户反馈列表页面创建演示数据
   - 为AI聊天页面创建演示数据

3. **集成到现有系统**

   - 将API拦截器集成到现有的API请求系统
   - 将演示数据管理器集成到现有的数据管理系统
   - 将演示数据存储集成到现有的存储系统

4. **测试和优化**
   - 进行单元测试、集成测试和端到端测试
   - 根据测试结果优化系统
   - 确保系统的可靠性和性能
