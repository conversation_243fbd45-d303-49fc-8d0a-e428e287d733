/**
 * @description 面试会话演示数据
 * 提供面试会话相关的演示数据
 */

import { demoInterviewer, demoCompany, demoJob } from '@/pages/interview/demo-data'

// 面试会话信息
export interface SessionInfo {
  sessionId: string
  jobId: number
  mode: string
  isDemo: boolean
  jobName: string
  company: string
}

// 面试问题
export interface InterviewQuestion {
  id: number
  type: string
  question: string
  tips?: string
  timeLimit: number
  difficulty: number
  expectedKeywords: string[]
  evaluationPoints: string[]
}

// 创建演示会话信息
export const createDemoSessionInfo = (params?: any): SessionInfo => {
  return {
    sessionId: 'demo-session-' + Date.now(),
    jobId: params?.jobId || 1001,
    mode: params?.mode || 'standard',
    isDemo: true,
    jobName: params?.jobName || demoJob.title,
    company: params?.company || demoCompany.name,
  }
}

// 演示面试问题列表
export const demoQuestions: InterviewQuestion[] = [
  {
    id: 1,
    type: 'technical',
    question: '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
    tips: '建议使用STAR法则来回答：Situation(情况)、Task(任务)、Action(行动)、Result(结果)',
    timeLimit: 180,
    difficulty: 3,
    expectedKeywords: ['Vue', 'React', 'Angular', '组件化', '响应式', '虚拟DOM'],
    evaluationPoints: ['技术深度', '实际应用', '场景分析'],
  },
  {
    id: 2,
    type: 'experience',
    question: '描述一个你在项目中遇到的技术难题，以及你是如何解决的？',
    tips: '重点描述问题的复杂性、解决思路和最终效果，体现你的问题解决能力',
    timeLimit: 240,
    difficulty: 4,
    expectedKeywords: ['问题定位', '解决方案', '优化', '团队协作', '技术调研'],
    evaluationPoints: ['问题分析能力', '解决思路', '实施效果'],
  },
  {
    id: 3,
    type: 'technical',
    question: '你对前端性能优化有什么理解？请举例说明。',
    tips: '可以从加载速度、运行性能、用户体验等角度回答，结合具体的优化手段',
    timeLimit: 180,
    difficulty: 3,
    expectedKeywords: ['缓存', '压缩', '懒加载', 'CDN', '首屏优化', 'Tree Shaking', '代码分割'],
    evaluationPoints: ['理论知识', '实践经验', '优化效果'],
  },
  {
    id: 4,
    type: 'soft_skill',
    question: '请谈谈你对团队协作和沟通的理解，以及在工作中是如何实践的？',
    tips: '可以从代码规范、版本控制、需求沟通、技术分享等方面来回答',
    timeLimit: 150,
    difficulty: 2,
    expectedKeywords: ['团队协作', '沟通', '代码规范', 'Git', '敏捷开发', '技术分享'],
    evaluationPoints: ['沟通能力', '团队意识', '协作经验'],
  },
  {
    id: 5,
    type: 'architecture',
    question: '如何设计一个高可用的前端架构？请从技术选型、部署策略等方面阐述。',
    tips: '考虑技术栈选择、容错机制、监控告警、灰度发布等方面',
    timeLimit: 300,
    difficulty: 5,
    expectedKeywords: ['微前端', '容错', '监控', '灰度发布', '负载均衡', 'CI/CD'],
    evaluationPoints: ['架构设计', '技术视野', '实践能力'],
  },
  {
    id: 6,
    type: 'career',
    question: '请介绍一下你的职业规划，以及为什么选择我们公司？',
    tips: '结合个人发展目标和公司发展方向，展现你的职业思考和求职动机',
    timeLimit: 120,
    difficulty: 2,
    expectedKeywords: ['职业规划', '技术成长', '团队贡献', '学习能力', '价值观'],
    evaluationPoints: ['职业规划', '求职动机', '价值匹配'],
  },
  {
    id: 7,
    type: 'engineering',
    question: '请描述一下现代前端工程化的理解和实践经验。',
    tips: '可以从构建工具、开发流程、质量保证、部署发布等方面来阐述',
    timeLimit: 200,
    difficulty: 4,
    expectedKeywords: ['Webpack', 'Vite', '自动化测试', 'ESLint', 'CI/CD', '模块化'],
    evaluationPoints: ['工程化思维', '工具使用', '流程优化'],
  },
  {
    id: 8,
    type: 'decision',
    question: '如果让你负责一个新项目的技术选型，你会如何考虑和决策？',
    tips: '从业务需求、团队能力、技术趋势、维护成本等多个维度来分析',
    timeLimit: 180,
    difficulty: 4,
    expectedKeywords: ['技术选型', '需求分析', '团队能力', '技术债务', '可维护性'],
    evaluationPoints: ['决策能力', '全局思维', '风险评估'],
  },
  {
    id: 9,
    type: 'ux',
    question: '请谈谈你对用户体验设计的理解，以及在开发中如何保证用户体验？',
    tips: '可以从交互设计、性能体验、无障碍访问、响应式设计等方面来回答',
    timeLimit: 160,
    difficulty: 3,
    expectedKeywords: ['用户体验', '交互设计', '响应式', '无障碍', '性能优化', 'A/B测试'],
    evaluationPoints: ['用户思维', '设计理解', '实践能力'],
  },
  {
    id: 10,
    type: 'question',
    question: '最后，你还有什么问题想要了解的吗？',
    tips: '这是一个展现你对公司和职位关注度的机会，可以询问技术栈、团队文化、发展机会等',
    timeLimit: 60,
    difficulty: 1,
    expectedKeywords: ['公司文化', '技术栈', '团队规模', '发展机会', '工作挑战'],
    evaluationPoints: ['求职态度', '关注重点', '沟通能力'],
  },
]

// 获取演示问题
export const getDemoQuestion = (id: number): InterviewQuestion | undefined => {
  return demoQuestions.find((q) => q.id === id)
}

// 获取演示问题列表
export const getDemoQuestions = (params?: any): InterviewQuestion[] => {
  // 如果指定了问题ID列表，则返回对应的问题
  if (params?.questionIds && Array.isArray(params.questionIds)) {
    return params.questionIds
      .map((id: number) => getDemoQuestion(id))
      .filter((q: InterviewQuestion | undefined) => q !== undefined) as InterviewQuestion[]
  }

  // 如果指定了问题数量，则返回指定数量的问题
  if (params?.count && typeof params.count === 'number') {
    const count = Math.min(params.count, demoQuestions.length)
    return demoQuestions.slice(0, count)
  }

  // 默认返回所有问题
  return [...demoQuestions]
}

// 导出演示数据
export default {
  createDemoSessionInfo,
  demoQuestions,
  getDemoQuestion,
  getDemoQuestions,
}
