/**
 * Performance Monitor
 * Tracks timing, metrics, and performance data for optimization
 */

interface PerformanceTiming {
  name: string
  startTime: number
  endTime?: number
  duration?: number
}

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  tags?: Record<string, string>
}

interface PerformanceReport {
  timings: PerformanceTiming[]
  metrics: PerformanceMetric[]
  counters: Record<string, number>
  memoryUsage?: MemoryInfo
  timestamp: number
}

class PerformanceMonitor {
  private timings = new Map<string, PerformanceTiming>()
  private metrics: PerformanceMetric[] = []
  private counters = new Map<string, number>()
  private readonly MAX_METRICS = 1000
  private readonly MAX_TIMINGS = 100

  /**
   * Start timing an operation
   */
  startTiming(name: string): void {
    const timing: PerformanceTiming = {
      name,
      startTime: performance.now(),
    }

    this.timings.set(name, timing)

    // Also use Performance API marks if available
    if (typeof performance.mark === 'function') {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * End timing an operation and return duration
   */
  endTiming(name: string): number {
    const timing = this.timings.get(name)
    if (!timing) {
      console.warn(`No timing found for: ${name}`)
      return 0
    }

    const endTime = performance.now()
    const duration = endTime - timing.startTime

    timing.endTime = endTime
    timing.duration = duration

    // Use Performance API marks if available
    if (typeof performance.mark === 'function' && typeof performance.measure === 'function') {
      performance.mark(`${name}-end`)
      try {
        performance.measure(name, `${name}-start`, `${name}-end`)
      } catch (error) {
        // Ignore measurement errors
      }
    }

    // Clean up old timings
    if (this.timings.size > this.MAX_TIMINGS) {
      const oldestKey = this.timings.keys().next().value
      this.timings.delete(oldestKey)
    }

    return duration
  }

  /**
   * Record a custom metric
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags,
    }

    this.metrics.push(metric)

    // Clean up old metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics.shift()
    }
  }

  /**
   * Increment a counter
   */
  incrementCounter(name: string, increment: number = 1): void {
    const current = this.counters.get(name) || 0
    this.counters.set(name, current + increment)
  }

  /**
   * Get counter value
   */
  getCounter(name: string): number {
    return this.counters.get(name) || 0
  }

  /**
   * Reset a counter
   */
  resetCounter(name: string): void {
    this.counters.delete(name)
  }

  /**
   * Get all timings
   */
  getTimings(): PerformanceTiming[] {
    return Array.from(this.timings.values())
  }

  /**
   * Get specific timing
   */
  getTiming(name: string): PerformanceTiming | undefined {
    return this.timings.get(name)
  }

  /**
   * Get all metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  /**
   * Get metrics by name
   */
  getMetricsByName(name: string): PerformanceMetric[] {
    return this.metrics.filter((metric) => metric.name === name)
  }

  /**
   * Get performance report
   */
  getReport(): PerformanceReport {
    const report: PerformanceReport = {
      timings: this.getTimings(),
      metrics: this.getMetrics(),
      counters: Object.fromEntries(this.counters),
      timestamp: Date.now(),
    }

    // Add memory usage if available
    if (typeof performance.memory !== 'undefined') {
      report.memoryUsage = {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
      }
    }

    return report
  }

  /**
   * Clear all performance data
   */
  clear(): void {
    this.timings.clear()
    this.metrics.length = 0
    this.counters.clear()
  }

  /**
   * Monitor Core Web Vitals
   */
  monitorWebVitals(): void {
    // First Contentful Paint
    this.observePerformanceEntry('paint', (entry) => {
      if (entry.name === 'first-contentful-paint') {
        this.recordMetric('web-vitals.fcp', entry.startTime)
      }
    })

    // Largest Contentful Paint
    this.observePerformanceEntry('largest-contentful-paint', (entry) => {
      this.recordMetric('web-vitals.lcp', entry.startTime)
    })

    // First Input Delay
    this.observePerformanceEntry('first-input', (entry) => {
      this.recordMetric('web-vitals.fid', entry.processingStart - entry.startTime)
    })

    // Cumulative Layout Shift
    this.observePerformanceEntry('layout-shift', (entry) => {
      if (!entry.hadRecentInput) {
        this.recordMetric('web-vitals.cls', entry.value)
      }
    })
  }

  /**
   * Monitor memory usage
   */
  monitorMemoryUsage(): void {
    if (typeof performance.memory !== 'undefined') {
      const interval = setInterval(() => {
        const memory = performance.memory
        this.recordMetric('memory.used', memory.usedJSHeapSize)
        this.recordMetric('memory.total', memory.totalJSHeapSize)
        this.recordMetric('memory.limit', memory.jsHeapSizeLimit)
      }, 5000) // Every 5 seconds

      // Clean up interval after 5 minutes
      setTimeout(
        () => {
          clearInterval(interval)
        },
        5 * 60 * 1000,
      )
    }
  }

  /**
   * Log performance summary
   */
  logSummary(): void {
    const report = this.getReport()

    console.group('🚀 Performance Summary')

    // Log timing summary
    if (report.timings.length > 0) {
      console.group('⏱️ Timings')
      report.timings
        .filter((t) => t.duration !== undefined)
        .sort((a, b) => (b.duration || 0) - (a.duration || 0))
        .slice(0, 10)
        .forEach((timing) => {
          console.log(`${timing.name}: ${timing.duration?.toFixed(2)}ms`)
        })
      console.groupEnd()
    }

    // Log counter summary
    if (report.counters && Object.keys(report.counters).length > 0) {
      console.group('📊 Counters')
      Object.entries(report.counters)
        .sort(([, a], [, b]) => b - a)
        .forEach(([name, count]) => {
          console.log(`${name}: ${count}`)
        })
      console.groupEnd()
    }

    // Log memory usage
    if (report.memoryUsage) {
      console.group('💾 Memory Usage')
      console.log(`Used: ${(report.memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
      console.log(`Total: ${(report.memoryUsage.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`)
      console.log(`Limit: ${(report.memoryUsage.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`)
      console.groupEnd()
    }

    console.groupEnd()
  }

  private observePerformanceEntry(
    entryType: string,
    callback: (entry: PerformanceEntry) => void,
  ): void {
    if (typeof PerformanceObserver !== 'undefined') {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach(callback)
        })
        observer.observe({ entryTypes: [entryType] })
      } catch (error) {
        console.warn(`Failed to observe ${entryType}:`, error)
      }
    }
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Export class for testing
export { PerformanceMonitor }

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.monitorWebVitals()
  performanceMonitor.monitorMemoryUsage()
}
