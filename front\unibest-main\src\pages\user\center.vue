<script setup lang="ts">
import { onUpdated, ref } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { getPinnedBadges } from '../../service/achievement'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import BottomTabBar from '@/components/BottomTabBar.vue'
// @ts-ignore
import ProfileCard from '../../components/ProfileCard.vue'
// @ts-ignore
import GrowthChart from '../../components/GrowthChart.vue'
// @ts-ignore
import AchievementWall from '../../components/AchievementWall.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import { useUserStore } from '@/store/user'

// 使用用户状态管理
const userStore = useUserStore()

/**
 * @description 用户信息
 */
const userInfo = ref({
  name: '张同学',
  major: '计算机科学与技术 · 大四',
  studyDays: 7,
  totalScore: 82,
  interviewCount: 15,
  avgScore: 82,
  badgeCount: 3,
  avatar: 'https://cdn.jsdelivr.net/gh/chuzhixin/image/vue-admin-beautiful/logo.png',
})

/**
 * @description 成长数据（模拟数据）
 */
const growthData = ref([
  {
    date: '2024-01-01',
    专业知识: 45,
    逻辑思维: 40,
    语言表达: 50,
    心理素质: 35,
    团队协作: 55,
  },
  {
    date: '2024-01-15',
    专业知识: 52,
    逻辑思维: 48,
    语言表达: 55,
    心理素质: 42,
    团队协作: 60,
  },
  {
    date: '2024-02-01',
    专业知识: 60,
    逻辑思维: 55,
    语言表达: 62,
    心理素质: 50,
    团队协作: 65,
  },
  {
    date: '2024-02-15',
    专业知识: 68,
    逻辑思维: 65,
    语言表达: 70,
    心理素质: 58,
    团队协作: 72,
  },
  {
    date: '2024-03-01',
    专业知识: 75,
    逻辑思维: 73,
    语言表达: 78,
    心理素质: 68,
    团队协作: 80,
  },
  {
    date: '2024-03-15',
    专业知识: 82,
    逻辑思维: 80,
    语言表达: 85,
    心理素质: 75,
    团队协作: 85,
  },
])

/**
 * @description 成就徽章数据（从徽章服务获取置顶徽章）
 */
const badges = ref<any[]>([])

/**
 * @description 加载用户数据
 */
const loadUserData = async () => {
  try {
    // 检查用户登录状态
    const isLoggedIn = userStore.checkLoginStatus()

    if (!isLoggedIn) {
      uni.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          uni.reLaunch({
            url: '/pages/auth/login',
          })
        },
      })
      return
    }

    // 从 userStore 获取用户信息
    const storeUserInfo = userStore.userInfo
    const userName = userStore.userName
    const userAvatar = userStore.userAvatar
    // 更新用户信息，合并从 userStore 获取的真实数据
    const newUserInfo = {
      // 使用真实的用户数据，优先级：realName > name > nickname > phone
      name:
        storeUserInfo.realName ||
        storeUserInfo.name ||
        storeUserInfo.nickname ||
        storeUserInfo.phone ||
        userName ||
        '用户',
      // 专业信息组合
      major: storeUserInfo.major
        ? `${storeUserInfo.major}${storeUserInfo.grade ? ' · ' + storeUserInfo.grade : ''}`
        : '专业信息待完善',
      // 头像处理
      avatar:
        storeUserInfo.avatar ||
        userAvatar ||
        'https://cdn.jsdelivr.net/gh/chuzhixin/image/vue-admin-beautiful/logo.png',
      studyDays: 7,
      totalScore: 82,
      interviewCount: 15,
      avgScore: 82,
      badgeCount: 3,
    }

    userInfo.value = newUserInfo

    // 加载置顶徽章数据
    badges.value = getPinnedBadges()
  } catch (error) {
    console.error('加载用户数据失败:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'none',
    })
  }
}

/**
 * @description 头像点击事件
 */
const onAvatarClick = () => {
  // 跳转到个人信息编辑页
  uni.navigateTo({
    url: '/pages/user/profile',
    fail: () => {
      uni.showToast({
        title: '个人信息页暂未开放',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 徽章点击事件
 */
const onBadgeClick = (badge: any) => {
  // 显示徽章详情
  uni.showModal({
    title: badge.title,
    content: `${badge.desc}\n${badge.unlocked ? `解锁时间: ${badge.unlockedAt}` : '未解锁'}`,
    showCancel: false,
    confirmText: '我知道了',
  })
}

/**
 * @description 跳转到徽章墙页面
 */
const goToAchievementWall = () => {
  uni.navigateTo({
    url: '/pages/achievement/wall',
    fail: () => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'error',
      })
    },
  })
}

/**
 * @description 跳转到成长曲线详细页面
 */
const goToGrowthChart = () => {
  uni.navigateTo({
    url: '/pages/user/growth-detail',
    fail: () => {
      uni.showToast({
        title: '成长分析页面开发中',
        icon: 'none',
        duration: 2000,
      })
    },
  })
}

// 当前激活的tab
const activeTab = ref('profile')

// 页面加载状态
const isLoading = ref(true)

/**
 * @description 跳转到更多设置页面
 */
const goToMoreSettings = () => {
  uni.navigateTo({
    url: '/pages/user/more-setting',
  })
}

onUpdated(() => {
  // 更新置顶徽章数据
  badges.value = getPinnedBadges()
})

// 页面加载时获取数据

onLoad(() => {
  // 初始化用户状态
  userStore.initUserState()
  loadUserData()
  isLoading.value = false
})

onLoad(() => {
  // 预加载
  uni.preloadPage({
    url: '/pages/user/profile',
  })
  uni.preloadPage({
    url: '/pages/user/more-setting',
  })
  uni.preloadPage({
    url: '/pages/user/growth-detail',
  })
  uni.preloadPage({
    url: '/pages/achievement/wall',
  })
  uni.preloadPage({
    url: '/pages/user/history',
  })
  uni.preloadPage({
    url: '/pages/user/ability-assessment',
  })
  uni.preloadPage({
    url: '/pages/user/feedback-list',
  })
  uni.preloadPage({
    url: '/pages/user/feedback',
  })
})
</script>

<template>
  <view class="center-page">
    <!-- 顶部状态栏 -->
    <HeadBar
      title="个人中心"
      :show-back="false"
      :show-right-button="true"
      right-icon="i-fa-solid-cog"
      right-button-bg="rgba(255, 255, 255, 0.25)"
      right-button-border="rgba(255, 255, 255, 0.3)"
      @right-click="goToMoreSettings"
      :fixed="true"
    />

    <!-- 加载状态 -->
    <LoadingCard :visible="isLoading" text="正在加载个人信息..." />

    <!-- 内容区域 -->
    <view v-show="!isLoading" class="content-area">
      <!-- 个人信息卡片 -->
      <view class="profile-section">
        <ProfileCard 
          :user-info="userInfo" 
          :badges="badges"
          @avatar-click="onAvatarClick"
          @badge-click="onBadgeClick"
          @badges-area-click="goToAchievementWall"
        />
      </view>
      <!-- 能力成长曲线 -->
      <view class="section growth-section">
        <view class="section-header">
          <view class="section-title-row">
            <view class="section-title-wrapper">
              <text class="section-title">
                <text class="i-fa-solid-chart-line section-icon"></text>
                能力成长轨迹
              </text>
              <text class="section-subtitle">追踪你的面试技能提升历程</text>
            </view>
            <!-- 查看更多按钮 -->
            <view class="view-all-btn growth-view-all" @click="goToGrowthChart">
              <text class="view-all-text">详细分析</text>
              <text class="i-fa-solid-chart-bar view-all-icon"></text>
            </view>
          </view>
        </view>
        <GrowthChart :growth-data="growthData" />
      </view>

      <!-- 底部空白区域，避免被TabBar遮挡 -->
      <view class="bottom-spacing"></view>
    </view>

    <!-- 底部导航栏 -->
    <BottomTabBar :active-tab="activeTab" :show-badge="true" />
  </view>
</template>

<style lang="scss" scoped>
.center-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #f7f9fc;
  overflow-x: hidden;

  // 添加装饰背景元素
  &::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 30% 20%, rgba(0, 201, 167, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 80%, rgba(79, 209, 199, 0.08) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
    animation: backgroundShift 20s ease-in-out infinite;
  }

  // 装饰圆圈
  &::after {
    content: '';
    position: fixed;
    top: 20%;
    right: -10%;
    width: 300rpx;
    height: 300rpx;
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.1), rgba(79, 209, 199, 0.05));
    border-radius: 50%;
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    animation: floating 8s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }
}

// 内容区域 - 增强毛玻璃效果
.content-area {
  margin-top: 100rpx;
  position: relative;
  width: 100%;
  z-index: 1;

  // 添加内容区域的装饰背景
  &::before {
    content: '';
    position: absolute;
    top: 100rpx;
    left: -20%;
    width: 400rpx;
    height: 400rpx;
    background: radial-gradient(circle, rgba(0, 201, 167, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 6s ease-in-out infinite reverse;
    pointer-events: none;
    z-index: -1;
  }
}

// 通用section样式 - 毛玻璃卡片设计
.section {
  position: relative;
  margin: 20rpx 0 32rpx 0;
  padding: 48rpx 32rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(25rpx) saturate(180%);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.12);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 24rpx;
    right: 48rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(0, 201, 167, 0.08);
    border-radius: 50%;
    animation: floating 5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 24rpx;
    left: 96rpx;
    width: 48rpx;
    height: 48rpx;
    background: rgba(79, 209, 199, 0.06);
    border-radius: 50%;
    animation: floating 4s ease-in-out infinite reverse;
    pointer-events: none;
    z-index: 1;
  }

  @media (hover: hover) {
    &:hover {
      transform: translateY(-8rpx);
      background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
      backdrop-filter: blur(30rpx) saturate(200%);
      -webkit-backdrop-filter: blur(30rpx) saturate(200%);
      box-shadow: 0 24rpx 60rpx rgba(0, 201, 167, 0.18);
    }
  }

  > * {
    position: relative;
    z-index: 2;
  }
}

// 底部间距，防止内容被TabBar遮挡
.bottom-spacing {
  width: 100%;
  height: 160rpx;
  background: transparent;
}

// 动画定义
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(5deg);
  }
}

@keyframes backgroundShift {
  0%,
  100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-2%) translateY(-1%);
  }
  50% {
    transform: translateX(1%) translateY(-2%);
  }
  75% {
    transform: translateX(-1%) translateY(1%);
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 滚动条美化（H5端）
/* #ifdef H5 */
::-webkit-scrollbar {
  width: 8rpx;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.6), rgba(79, 209, 199, 0.4));
  border-radius: 4rpx;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);

  &:hover {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.8), rgba(79, 209, 199, 0.6));
  }
}
/* #endif */

// H5端特殊样式优化
/* #ifdef H5 */
.center-page {
  .section {
    backdrop-filter: blur(35rpx) saturate(200%);
    -webkit-backdrop-filter: blur(35rpx) saturate(200%);

    &:hover {
      backdrop-filter: blur(40rpx) saturate(220%);
      -webkit-backdrop-filter: blur(40rpx) saturate(220%);
    }
  }

  .logout-btn {
    cursor: pointer;
    user-select: none;

    &:hover {
      cursor: pointer;
    }
  }
}
/* #endif */

// 小程序端优化
/* #ifdef MP-WEIXIN */
.center-page {
  .section {
    backdrop-filter: blur(20rpx) saturate(150%);
    -webkit-backdrop-filter: blur(20rpx) saturate(150%);

    &:active {
      transform: scale(0.98);
      background: rgba(255, 255, 255, 0.9);
    }
  }

  .logout-btn {
    &:active {
      transform: scale(0.95);
    }
  }
}
/* #endif */

// 响应式适配
@media screen and (max-width: 750rpx) {
  .center-page {
    .section {
      margin: 16rpx 0 24rpx 0;
      padding: 32rpx 24rpx;
      border-radius: 24rpx;
    }

    .logout-btn {
      width: calc(100% - 48rpx);
      margin: 32rpx 24rpx 40rpx 24rpx;
      height: 88rpx;
      font-size: 30rpx;
      border-radius: 44rpx;
    }
  }

  .profile-section {
    margin: 16rpx 0 24rpx 0;
  }

  .section-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .section-title-wrapper {
    width: 100%;
  }

  .section-subtitle {
    margin-left: 40rpx;
    font-size: 22rpx;
  }

  .growth-view-all {
    align-self: flex-end;
    padding: 12rpx 20rpx;
    border-radius: 24rpx;
  }

  .view-all-text {
    font-size: 22rpx;
  }

  .view-all-icon {
    font-size: 18rpx;
  }
}

// 暗黑模式支持
@media (prefers-color-scheme: dark) {
  .center-page {
    background-color: #1a1a23;

    &::before {
      background: radial-gradient(circle at 30% 20%, rgba(0, 201, 167, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(79, 209, 199, 0.1) 0%, transparent 50%);
    }
  }

  .section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(25rpx) saturate(150%);
    -webkit-backdrop-filter: blur(25rpx) saturate(150%);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 16rpx 40rpx rgba(0, 0, 0, 0.4),
      0 8rpx 24rpx rgba(0, 201, 167, 0.1),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(30rpx) saturate(180%);
      -webkit-backdrop-filter: blur(30rpx) saturate(180%);
    }
  }

  .logout-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
    border: 2rpx solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 16rpx 40rpx rgba(255, 107, 107, 0.2),
      0 8rpx 24rpx rgba(0, 0, 0, 0.3),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  }
}

// 减少动画以提升性能（可选）
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 个人信息区域
.profile-section {
  margin: 20rpx 0 32rpx 0;
}

// Section 标题样式
.section-header {
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.1);
}

.section-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16rpx;
}

.section-title-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 700;
  color: #222;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.section-icon {
  font-size: 28rpx;
  color: #00c9a7;
  text-shadow: 0 2rpx 4rpx rgba(0, 201, 167, 0.2);
}

.section-subtitle {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  opacity: 0.9;
  margin-left: 40rpx;
}

// 查看全部按钮
.view-all-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1), rgba(79, 209, 199, 0.05));
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.15), rgba(79, 209, 199, 0.08));

    &::before {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    &:hover {
      transform: translateY(-2rpx);
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.15), rgba(79, 209, 199, 0.08));
      box-shadow: 0 8rpx 16rpx rgba(0, 201, 167, 0.2);

      &::before {
        opacity: 1;
      }
    }
  }
}

// 成长曲线专用的查看更多按钮
.growth-view-all {
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.12), rgba(79, 209, 199, 0.08));
  border: 1rpx solid rgba(0, 201, 167, 0.25);
  padding: 14rpx 24rpx;
  border-radius: 28rpx;

  &:active {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.2), rgba(79, 209, 199, 0.12));
  }

  @media (hover: hover) {
    &:hover {
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.18), rgba(79, 209, 199, 0.1));
      box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.25);
    }
  }
}

.view-all-text {
  font-size: 24rpx;
  color: #00c9a7;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.view-all-icon {
  font-size: 20rpx;
  color: #00c9a7;
  transition: transform 0.3s;
}

.growth-view-all:active .view-all-icon,
.growth-view-all:hover .view-all-icon {
  transform: scale(1.1);
}

// 无徽章提示
.no-badges-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx;
  text-align: center;
}

.no-badges-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.no-badges-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 12rpx;
}

.no-badges-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.goto-wall-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 32rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
  }
}

.goto-wall-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

// 快捷操作网格
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 16rpx;
}

// 快捷操作卡片
.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(15rpx) saturate(150%);
  -webkit-backdrop-filter: blur(15rpx) saturate(150%);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 12rpx;
    right: 16rpx;
    width: 32rpx;
    height: 32rpx;
    background: rgba(0, 201, 167, 0.06);
    border-radius: 50%;
    animation: floating 3s ease-in-out infinite;
    pointer-events: none;
  }

  @media (hover: hover) {
    &:hover {
      transform: translateY(-8rpx) scale(1.02);
      background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
      backdrop-filter: blur(20rpx) saturate(180%);
      -webkit-backdrop-filter: blur(20rpx) saturate(180%);
      box-shadow: 0 20rpx 48rpx rgba(0, 201, 167, 0.15);

      .action-icon-wrapper {
        transform: scale(1.1);
      }
    }
  }

  &:active {
    transform: translateY(-4rpx) scale(0.98);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
}

// 操作图标容器
.action-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);

  &.primary {
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  }

  &.success {
    background: linear-gradient(135deg, #22c55e, #16a34a);
  }

  &.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
  }

  &.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
  }
}

.action-icon {
  font-size: 36rpx;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 8rpx;
  text-align: center;
}

.action-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
  opacity: 0.9;
}

// 响应式适配 - 快捷操作
@media screen and (max-width: 750rpx) {
  .quick-actions-grid {
    gap: 16rpx;
  }

  .action-card {
    padding: 24rpx 16rpx;
  }

  .action-icon-wrapper {
    width: 64rpx;
    height: 64rpx;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
  }

  .action-icon {
    font-size: 28rpx;
  }

  .action-title {
    font-size: 26rpx;
  }

  .action-desc {
    font-size: 20rpx;
  }
}

// 小程序端快捷操作优化
/* #ifdef MP */
.action-card {
  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.8);
  }
}
/* #endif */

// H5端快捷操作优化
/* #ifdef H5 */
.action-card {
  cursor: pointer;
  user-select: none;

  &:hover {
    cursor: pointer;
  }
}
/* #endif */


</style>
