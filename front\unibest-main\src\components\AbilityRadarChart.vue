<template>
  <view class="radar-chart-container">
    <!-- Canvas 版本（可能在某些平台不工作） -->
    <canvas
      v-if="useCanvas"
      ref="radarChartRef"
      canvas-id="radarChart"
      class="radar-chart"
      @touchstart="handleTouch"
    ></canvas>

    <!-- CSS 版本（降级方案） -->
    <view v-else class="radar-chart-css">
      <view class="radar-title">能力雷达图</view>
      <view class="ability-list">
        <view v-for="(item, index) in data" :key="index" class="ability-item">
          <view class="ability-info">
            <text class="ability-name">{{ item.name }}</text>
            <text class="ability-value">{{ item.value }}分</text>
          </view>
          <view class="ability-bar">
            <view
              class="ability-progress"
              :style="{
                width: `${(item.value / maxValue) * 100}%`,
                background: colorScheme,
              }"
            ></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'

// 组件接收的属性
const props = withDefaults(
  defineProps<{
    data: Array<{ name: string; value: number }>
    maxValue?: number
    colorScheme?: string
    showLegend?: boolean
    showAnimation?: boolean
  }>(),
  {
    maxValue: 100,
    colorScheme: '#00c9a7',
    showLegend: true,
    showAnimation: false,
  },
)

// 雷达图DOM引用
const radarChartRef = ref<any>(null)
const chartContext = ref<any>(null)
const useCanvas = ref(true) // 是否使用 Canvas 版本

// 处理触摸事件（uni-app 需要）
const handleTouch = () => {
  // 空函数，用于激活 canvas
}

// 监听数据变化重绘图表
watch(
  () => props.data,
  () => {
    if (useCanvas.value && chartContext.value && props.data.length > 0) {
      drawRadarChart()
    }
  },
  { deep: true },
)

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 初始化图表
const initChart = () => {
  // 在 H5 环境中，直接使用 CSS 版本，避免 Canvas 兼容性问题
  console.log('使用 CSS 版本的雷达图')
  useCanvas.value = false

  // 如果需要尝试 Canvas 版本，可以取消注释以下代码
  /*
  if (typeof uni !== 'undefined' && uni.createCanvasContext) {
    try {
      chartContext.value = uni.createCanvasContext('radarChart')
      if (props.data.length > 0) {
        drawRadarChart()
      }
    } catch (error) {
      console.warn('Canvas 初始化失败，使用 CSS 版本:', error)
      useCanvas.value = false
    }
  } else {
    console.log('不支持 Canvas，使用 CSS 版本')
    useCanvas.value = false
  }
  */
}

/**
 * @description 绘制雷达图
 */
function drawRadarChart() {
  if (!chartContext.value || !props.data.length) return

  const ctx = chartContext.value
  // 在 uni-app 中，canvas 尺寸需要手动设置
  const width = 300
  const height = 300
  const centerX = width / 2
  const centerY = height / 2
  const radius = Math.min(centerX, centerY) * 0.8

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  const dataPoints = props.data.length
  const angleStep = (Math.PI * 2) / dataPoints

  // 绘制背景网格
  drawGrid(ctx, centerX, centerY, radius, dataPoints, angleStep)

  // 绘制数据区域
  drawDataArea(ctx, centerX, centerY, radius, dataPoints, angleStep)

  // 绘制轴标签
  drawAxisLabels(ctx, centerX, centerY, radius, dataPoints, angleStep)

  // 绘制图例
  if (props.showLegend) {
    drawLegend(ctx, width, height)
  }

  // 在 uni-app 中需要调用 draw 方法
  ctx.draw()
}

/**
 * @description 绘制背景网格
 */
function drawGrid(
  ctx: any,
  centerX: number,
  centerY: number,
  radius: number,
  dataPoints: number,
  angleStep: number,
) {
  // 绘制同心圆
  const gridLevels = 5
  ctx.strokeStyle = '#e0e0e0'
  ctx.lineWidth = 1

  for (let i = 1; i <= gridLevels; i++) {
    const levelRadius = (radius * i) / gridLevels
    ctx.beginPath()
    ctx.arc(centerX, centerY, levelRadius, 0, Math.PI * 2)
    ctx.stroke()
  }

  // 绘制轴线
  ctx.beginPath()
  for (let i = 0; i < dataPoints; i++) {
    const angle = i * angleStep - Math.PI / 2
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    ctx.moveTo(centerX, centerY)
    ctx.lineTo(x, y)
  }
  ctx.stroke()
}

/**
 * @description 绘制数据区域
 */
function drawDataArea(
  ctx: any,
  centerX: number,
  centerY: number,
  radius: number,
  dataPoints: number,
  angleStep: number,
) {
  ctx.beginPath()
  for (let i = 0; i < dataPoints; i++) {
    const value = props.data[i].value / props.maxValue
    const angle = i * angleStep - Math.PI / 2
    const x = centerX + radius * value * Math.cos(angle)
    const y = centerY + radius * value * Math.sin(angle)

    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  }
  ctx.closePath()

  // 填充区域
  ctx.fillStyle = `${props.colorScheme}33` // 20% 透明度
  ctx.fill()

  // 绘制边框
  ctx.strokeStyle = props.colorScheme
  ctx.lineWidth = 2
  ctx.stroke()

  // 绘制数据点
  for (let i = 0; i < dataPoints; i++) {
    const value = props.data[i].value / props.maxValue
    const angle = i * angleStep - Math.PI / 2
    const x = centerX + radius * value * Math.cos(angle)
    const y = centerY + radius * value * Math.sin(angle)

    ctx.beginPath()
    ctx.arc(x, y, 4, 0, Math.PI * 2)
    ctx.fillStyle = props.colorScheme
    ctx.fill()
  }
}

/**
 * @description 绘制轴标签
 */
function drawAxisLabels(
  ctx: any,
  centerX: number,
  centerY: number,
  radius: number,
  dataPoints: number,
  angleStep: number,
) {
  ctx.font = '12px Arial'
  ctx.fillStyle = '#666'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  for (let i = 0; i < dataPoints; i++) {
    const angle = i * angleStep - Math.PI / 2
    const labelRadius = radius * 1.1
    const x = centerX + labelRadius * Math.cos(angle)
    const y = centerY + labelRadius * Math.sin(angle)

    ctx.fillText(props.data[i].name, x, y)
  }
}

/**
 * @description 绘制图例
 */
function drawLegend(ctx: any, width: number, height: number) {
  const legendX = width * 0.05
  const legendY = height - 30
  const legendWidth = width * 0.9
  const itemWidth = legendWidth / props.data.length

  for (let i = 0; i < props.data.length; i++) {
    const x = legendX + i * itemWidth
    const y = legendY

    // 绘制图例点
    ctx.beginPath()
    ctx.arc(x + 5, y, 4, 0, Math.PI * 2)
    ctx.fillStyle = props.colorScheme
    ctx.fill()

    // 绘制图例文本
    ctx.font = '10px Arial'
    ctx.fillStyle = '#666'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'
    ctx.fillText(`${props.data[i].name}: ${props.data[i].value}`, x + 15, y)
  }
}
</script>

<style lang="scss" scoped>
.radar-chart-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 16rpx 0;
}

.radar-chart {
  width: 600rpx;
  height: 600rpx;
}

.radar-chart-css {
  width: 100%;
  max-width: 600rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  .radar-title {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 40rpx;
  }

  .ability-list {
    .ability-item {
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .ability-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;

        .ability-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .ability-value {
          font-size: 24rpx;
          color: #666;
          font-weight: bold;
        }
      }

      .ability-bar {
        width: 100%;
        height: 12rpx;
        background: #f0f0f0;
        border-radius: 6rpx;
        overflow: hidden;

        .ability-progress {
          height: 100%;
          border-radius: 6rpx;
          transition: width 0.6s ease;
          background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
        }
      }
    }
  }
}
</style>
