/**
 * @description 面试房间演示数据模块
 * 提供面试房间相关的演示数据和生成器
 */

import { demoDataManager } from '../DemoDataManager'
import sessionData, {
  createDemoSessionInfo,
  demoQuestions,
  getDemoQuestion,
  getDemoQuestions,
} from './sessionData'
import analysisGenerator, {
  createBaseEmotionData,
  createBaseMultiModalMetrics,
  createBaseRealTimeAnalysis,
  generateFluctuatingEmotions,
  generateFluctuatingMultiModalMetrics,
  generateRealTimeAnalysis,
} from './analysisGenerator'
import reportGenerator, { generateInterviewReport, generateReportSummary } from './reportGenerator'
import roomInitializer, { initInterviewRoomDemoData, createMockWebSocket } from './roomInitializer'
import roomHelper, { interviewRoomHelper } from './roomHelper'

// API路径常量
const API_PATHS = {
  GET_SESSION_INFO: 'interview-room/getSessionInfo',
  GET_QUESTION: 'interview-room/getQuestion',
  GET_QUESTIONS: 'interview-room/getQuestions',
  SUBMIT_ANSWER: 'interview-room/submitAnswer',
  END_INTERVIEW: 'interview-room/endInterview',
  SUBMIT_FEEDBACK: 'interview-room/submitFeedback',
  CHECK_DEVICES: 'interview-room/checkDevices',
  GET_SESSION_STATUS: 'interview-room/getSessionStatus',
  ANALYZE_AUDIO: 'interview-room/analyzeAudio',
  ANALYZE_VIDEO: 'interview-room/analyzeVideo',
  ANALYZE_TEXT: 'interview-room/analyzeText',
  COMPREHENSIVE_ANALYSIS: 'interview-room/comprehensiveAnalysis',
  CREATE_AUDIO_STREAM_ANALYSIS: 'interview-room/createAudioStreamAnalysis',
  CREATE_VIDEO_STREAM_ANALYSIS: 'interview-room/createVideoStreamAnalysis',
  GET_ANALYSIS_HISTORY: 'interview-room/getAnalysisHistory',
  GET_INTERVIEW_REPORT: 'interview-room/getInterviewReport',
  GET_REPORT_SUMMARY: 'interview-room/getReportSummary',
}

// 注册演示数据和生成器
export const registerInterviewDemoData = () => {
  // 注册会话信息演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_SESSION_INFO, (params: any) => {
    return {
      code: 200,
      message: 'success',
      data: createDemoSessionInfo(params),
    }
  })

  // 注册获取问题演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_QUESTION, (params: any) => {
    const question = getDemoQuestion(params?.id || 1)
    return {
      code: 200,
      message: 'success',
      data: question,
    }
  })

  // 注册获取问题列表演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_QUESTIONS, (params: any) => {
    const questions = getDemoQuestions(params)
    return {
      code: 200,
      message: 'success',
      data: questions,
    }
  })

  // 注册获取面试报告演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_INTERVIEW_REPORT, (params: any) => {
    const report = generateInterviewReport(params?.sessionId || 'demo-session', params)
    return {
      code: 200,
      message: 'success',
      data: report,
    }
  })

  // 注册获取报告摘要演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_REPORT_SUMMARY, (params: any) => {
    // 先获取完整报告
    const report = generateInterviewReport(params?.sessionId || 'demo-session', params)
    // 然后生成摘要
    const summary = generateReportSummary(report)
    return {
      code: 200,
      message: 'success',
      data: summary,
    }
  })

  // 注册提交答案演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.SUBMIT_ANSWER, (params: any) => {
    return {
      code: 200,
      message: 'success',
      data: {
        id: params?.id || 1,
        status: 'COMPLETED',
        score: Math.floor(Math.random() * 30) + 70, // 70-100的随机分数
        feedback: '回答很好，展示了扎实的技术功底和实践经验。',
      },
    }
  })

  // 注册结束面试演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.END_INTERVIEW, (params: any) => {
    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        status: 'COMPLETED',
        totalScore: Math.floor(Math.random() * 20) + 80, // 80-100的随机总分
        reportUrl: '/interview/detail?id=' + (params?.sessionId || 'demo-session'),
      },
    }
  })

  // 注册提交反馈演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.SUBMIT_FEEDBACK, (params: any) => {
    return {
      code: 200,
      message: 'success',
      data: {
        id: `feedback-${Date.now()}`,
        status: 'SUBMITTED',
      },
    }
  })

  // 注册检查设备演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.CHECK_DEVICES, (params: any) => {
    return {
      code: 200,
      message: 'success',
      data: {
        camera: true,
        microphone: true,
        network: true,
        environment: Math.random() > 0.2, // 80%概率通过
        lighting: Math.random() > 0.3, // 70%概率通过
      },
    }
  })

  // 注册获取会话状态演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_SESSION_STATUS, (params: any) => {
    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        status: 'IN_PROGRESS',
        currentQuestionId: params?.currentQuestionId || 1,
        timeRemaining: params?.timeRemaining || 1800,
        totalQuestions: demoQuestions.length,
      },
    }
  })

  // 注册音频分析演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.ANALYZE_AUDIO, (params: any) => {
    const metrics = createBaseMultiModalMetrics()
    const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)

    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        questionId: params?.questionId || 1,
        timestamp: Date.now(),
        metrics: fluctuatingMetrics.speech,
        transcript: params?.text || '这是一段演示的语音转文本内容...',
      },
    }
  })

  // 注册视频分析演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.ANALYZE_VIDEO, (params: any) => {
    const metrics = createBaseMultiModalMetrics()
    const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)
    const emotions = generateFluctuatingEmotions()

    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        questionId: params?.questionId || 1,
        timestamp: Date.now(),
        metrics: fluctuatingMetrics.video,
        emotions,
        attentionLevel: Math.floor(Math.random() * 30) + 70, // 70-100的随机注意力水平
      },
    }
  })

  // 注册文本分析演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.ANALYZE_TEXT, (params: any) => {
    const metrics = createBaseMultiModalMetrics()
    const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)
    const question = getDemoQuestion(params?.questionId || 1)

    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        questionId: params?.questionId || 1,
        timestamp: Date.now(),
        metrics: fluctuatingMetrics.text,
        keywordMatches: question?.expectedKeywords?.filter(() => Math.random() > 0.5) || [],
        sentiment: Math.floor(Math.random() * 100), // 0-100的随机情感倾向
      },
    }
  })

  // 注册综合分析演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.COMPREHENSIVE_ANALYSIS, (params: any) => {
    const metrics = createBaseMultiModalMetrics()
    const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)
    const emotions = generateFluctuatingEmotions()
    const question = getDemoQuestion(params?.questionId || 1)

    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        questionId: params?.questionId || 1,
        timestamp: Date.now(),
        metrics: fluctuatingMetrics,
        emotions,
        keywordMatches: question?.expectedKeywords?.filter(() => Math.random() > 0.5) || [],
        attentionLevel: Math.floor(Math.random() * 30) + 70, // 70-100的随机注意力水平
        sentiment: Math.floor(Math.random() * 100), // 0-100的随机情感倾向
        transcript: params?.text || '这是一段演示的语音转文本内容...',
        suggestions: [
          {
            type: 'speech',
            level: 'info',
            title: '语速提示',
            message: '当前语速适中，保持这个节奏能让面试官更好地理解你的表达。',
            action: '保持当前语速',
          },
          {
            type: 'video',
            level: 'warning',
            title: '眼神接触',
            message: '建议增加与镜头的眼神接触，展示自信和专注。',
            action: '看向摄像头',
          },
        ],
      },
    }
  })

  // 注册创建音频流分析演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.CREATE_AUDIO_STREAM_ANALYSIS,
    (params: any) => {
      return {
        code: 200,
        message: 'success',
        data: {
          analysisId: `audio-analysis-${Date.now()}`,
          sessionId: params?.sessionId || 'demo-session',
          questionId: params?.questionId || 1,
          status: 'CREATED',
          wsUrl: 'wss://demo-audio-analysis.example.com',
        },
      }
    },
  )

  // 注册创建视频流分析演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.CREATE_VIDEO_STREAM_ANALYSIS,
    (params: any) => {
      return {
        code: 200,
        message: 'success',
        data: {
          analysisId: `video-analysis-${Date.now()}`,
          sessionId: params?.sessionId || 'demo-session',
          questionId: params?.questionId || 1,
          status: 'CREATED',
          wsUrl: 'wss://demo-video-analysis.example.com',
        },
      }
    },
  )

  // 注册获取分析历史演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_ANALYSIS_HISTORY, (params: any) => {
    const metrics = createBaseMultiModalMetrics()
    const fluctuatingMetrics = generateFluctuatingMultiModalMetrics(metrics, 15)
    const emotions = generateFluctuatingEmotions()
    const question = getDemoQuestion(params?.questionId || 1)

    // 创建历史分析数据点
    const historyPoints = []
    const startTime = Date.now() - 300000 // 5分钟前
    const count = params?.count || 10

    for (let i = 0; i < count; i++) {
      const timestamp = startTime + (i * 300000) / count
      const metrics = generateFluctuatingMultiModalMetrics(createBaseMultiModalMetrics(), 20)
      const emotions = generateFluctuatingEmotions()

      historyPoints.push({
        timestamp,
        metrics,
        emotions,
        keywordMatches: question?.expectedKeywords?.filter(() => Math.random() > 0.5) || [],
        attentionLevel: Math.floor(Math.random() * 30) + 70,
        sentiment: Math.floor(Math.random() * 100),
        transcript: `这是第${i + 1}段演示的历史分析内容...`,
      })
    }

    return {
      code: 200,
      message: 'success',
      data: {
        sessionId: params?.sessionId || 'demo-session',
        questionId: params?.questionId || 1,
        history: historyPoints,
      },
    }
  })

  console.log('[InterviewDemoData] 注册面试房间演示数据完成')
}

// 导出所有模块
export {
  sessionData,
  analysisGenerator,
  reportGenerator,
  roomInitializer,
  roomHelper,
  interviewRoomHelper,
  createDemoSessionInfo,
  demoQuestions,
  getDemoQuestion,
  getDemoQuestions,
  createBaseEmotionData,
  createBaseMultiModalMetrics,
  createBaseRealTimeAnalysis,
  generateFluctuatingEmotions,
  generateFluctuatingMultiModalMetrics,
  generateRealTimeAnalysis,
  generateInterviewReport,
  generateReportSummary,
  initInterviewRoomDemoData,
  createMockWebSocket,
  API_PATHS,
}

// 默认导出
export default {
  registerInterviewDemoData,
  initInterviewRoomDemoData,
  createMockWebSocket,
  interviewRoomHelper,
  API_PATHS,
}
