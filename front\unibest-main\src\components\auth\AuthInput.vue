<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { validateField, ValidationRules } from '@/utils/auth-helpers'

/**
 * 认证输入框组件
 * @description 统一的认证页面输入框组件，支持验证、图标、错误提示等功能
 */

interface Props {
  modelValue: string
  type?: 'text' | 'password' | 'number' | 'email' | 'phone'
  placeholder?: string
  label?: string
  icon?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  maxlength?: number
  validateType?: 'phone' | 'email' | 'password' | 'studentId' | 'realName' | 'verifyCode'
  showPassword?: boolean
  clearable?: boolean
  errorMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  placeholder: '',
  label: '',
  icon: '',
  required: false,
  disabled: false,
  readonly: false,
  maxlength: 100,
  showPassword: false,
  clearable: true,
  errorMessage: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  focus: [event: Event]
  blur: [event: Event]
  input: [event: Event]
  clear: []
  validate: [result: { isValid: boolean; message: string }]
}>()

// 状态
const isFocused = ref(false)
const isPasswordVisible = ref(false)
const internalError = ref('')

// 计算属性
const inputType = computed(() => {
  if (props.type === 'password' && props.showPassword) {
    return isPasswordVisible.value ? 'text' : 'password'
  }
  return props.type
})

const hasError = computed(() => {
  return !!(props.errorMessage || internalError.value)
})

const inputClass = computed(() => {
  return [
    'auth-input',
    {
      'auth-input--focused': isFocused.value,
      'auth-input--error': hasError.value,
      'auth-input--disabled': props.disabled,
      'auth-input--readonly': props.readonly,
    },
  ]
})

const errorText = computed(() => {
  return props.errorMessage || internalError.value
})

// 方法
function handleInput(event: any) {
  const value = event.detail.value
  emit('update:modelValue', value)
  emit('input', event)

  // 实时验证
  if (props.validateType && value) {
    validateInput(value)
  } else {
    internalError.value = ''
  }
}

function handleFocus(event: Event) {
  isFocused.value = true
  emit('focus', event)
}

function handleBlur(event: Event) {
  isFocused.value = false
  emit('blur', event)

  // 失焦时验证
  if (props.validateType && props.modelValue) {
    validateInput(props.modelValue)
  }
}

function handleClear() {
  emit('update:modelValue', '')
  emit('clear')
  internalError.value = ''
}

function togglePasswordVisibility() {
  isPasswordVisible.value = !isPasswordVisible.value
}

function validateInput(value: string) {
  if (!props.validateType) return

  const rule = ValidationRules[props.validateType]
  if (rule) {
    const result = validateField(value, rule)
    internalError.value = result.message
    emit('validate', result)
  }
}

// 监听外部验证错误
watch(
  () => props.errorMessage,
  (newError) => {
    if (newError) {
      internalError.value = ''
    }
  },
)
</script>

<template>
  <view class="auth-input-container">
    <!-- 标签 -->
    <text v-if="label" class="auth-input-label">
      {{ label }}
      <text v-if="required" class="auth-input-required">*</text>
    </text>

    <!-- 输入框容器 -->
    <view class="auth-input-wrapper" :class="inputClass">
      <!-- 左侧图标 -->
      <text v-if="icon" class="auth-input-icon auth-input-icon--left" :class="icon"></text>

      <!-- 输入框 -->
      <input
        class="auth-input-field"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />

      <!-- 右侧操作区 -->
      <view class="auth-input-actions">
        <!-- 清除按钮 -->
        <text
          v-if="clearable && modelValue && !disabled && !readonly"
          class="auth-input-icon auth-input-icon--clear i-fa-solid-times-circle"
          @click="handleClear"
        ></text>

        <!-- 密码可见性切换 -->
        <text
          v-if="type === 'password' && showPassword"
          class="auth-input-icon auth-input-icon--password"
          :class="isPasswordVisible ? 'i-fa-solid-eye' : 'i-fa-solid-eye-slash'"
          @click="togglePasswordVisibility"
        ></text>
      </view>
    </view>

    <!-- 错误提示 -->
    <text v-if="hasError" class="auth-input-error">{{ errorText }}</text>
  </view>
</template>

<style scoped lang="scss">
@import '@/style/auth-variables.scss';

.auth-input-container {
  width: 100%;
  margin-bottom: $spacing-lg;
}

.auth-input-label {
  display: block;
  margin-bottom: $spacing-sm;
  font-size: $font-md;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  transition: color $transition-normal ease;
}

.auth-input-required {
  color: $error-color;
  margin-left: 4rpx;
}

.auth-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid $border-secondary;
  border-radius: $radius-lg;
  transition: all $transition-normal $ease-out-quart;

  &.auth-input--focused {
    @include input-focus;
    background: $bg-primary;
    transform: translateY(-2rpx);
  }

  &.auth-input--error {
    @include input-error;
  }

  &.auth-input--disabled {
    background: $bg-disabled;
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.auth-input--readonly {
    background: $bg-secondary;
  }
}

.auth-input-icon {
  font-size: $font-lg;
  color: $text-tertiary;
  transition: color $transition-normal ease;

  &--left {
    margin-left: $input-padding-x;
    margin-right: $spacing-sm;
  }

  &--clear,
  &--password {
    padding: $spacing-sm;
    border-radius: $radius-sm;
    cursor: pointer;

    &:active {
      color: $primary-color;
      background: rgba(0, 201, 167, 0.1);
      transform: scale(0.95);
    }
  }
}

.auth-input-field {
  flex: 1;
  height: $input-height;
  padding: 0 $spacing-sm;
  font-size: $font-lg;
  color: $text-primary;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: $text-tertiary;
    transition: color $transition-normal ease;
  }

  &:focus::placeholder {
    color: transparent;
  }
}

.auth-input-actions {
  display: flex;
  align-items: center;
  margin-right: $spacing-sm;
  gap: $spacing-xs;
}

.auth-input-error {
  display: block;
  margin-top: $spacing-sm;
  font-size: $font-sm;
  color: $error-color;
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
