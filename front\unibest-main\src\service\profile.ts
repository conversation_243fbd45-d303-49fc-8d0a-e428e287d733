import { httpGet, httpPost, httpPut } from '@/utils/http'

/**
 * 用户个人信息数据结构
 */
export interface UserProfile {
  avatar: string
  name: string
  userName: string
  gender: string
  school: string
  major: string
  grade: string
  introduction: string
}

/**
 * 用户个人信息DTO
 */
export interface UserProfileDto {
  avatar?: string
  name?: string
  userName?: string
  gender?: string
  school?: string
  major?: string
  grade?: string
  introduction?: string
}

/**
 * 获取个人信息
 * @returns 用户个人信息
 */
export function getUserProfile() {
  return httpGet<UserProfile>('/app/user/profile/profile')
}

/**
 * 修改个人信息
 * @param params 个人信息参数
 */
export function updateUserProfile(params: { params: UserProfileDto }) {
  return httpPut<void>('/app/user/profile/profile', params.params)
}

/**
 * 重置个人信息
 */
export function resetUserProfile() {
  return httpPost<void>('/app/user/profile/profile/reset')
}

/**
 * 头像上传
 * @param filePath 文件路径
 */
export function uploadAvatar(filePath: string): Promise<{ url: string }> {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: '/app/user/profile/avatar', // 拦截器会自动处理基础URL
      filePath,
      name: 'avatarfile',
      // 拦截器会自动添加Authorization头
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 200) {
              resolve(data.data)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (e) {
            reject(new Error('响应数据解析失败'))
          }
        } else {
          reject(new Error('上传失败'))
        }
      },
      fail: (error) => {
        reject(error)
      },
    })
  })
}
