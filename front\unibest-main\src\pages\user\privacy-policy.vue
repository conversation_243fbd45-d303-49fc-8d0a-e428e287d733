<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

/**
 * @description 隐私政策页面
 * 包含数据收集、使用、保护等隐私相关政策条款
 */

// 政策更新时间
const lastUpdateTime = ref('2024年1月15日')

// 隐私政策章节数据
const privacySections = ref([
  {
    id: 'information-collection',
    title: '信息收集说明',
    content: [
      '我们仅在提供服务所必需的情况下收集您的个人信息。',
      '收集的信息包括但不限于：基本信息（姓名、性别、年龄）、联系方式（邮箱、手机号）、账户信息（用户名、密码）。',
      '使用数据包括：面试记录、学习进度、功能使用情况等。',
      '设备信息包括：设备类型、操作系统、唯一设备标识符等。',
    ],
  },
  {
    id: 'information-usage',
    title: '信息使用目的',
    content: [
      '提供个性化的面试辅导和学习服务。',
      '改进和优化平台功能和用户体验。',
      '发送重要的服务通知和产品更新。',
      '进行数据分析以提供更好的服务建议。',
    ],
  },
  {
    id: 'data-security',
    title: '数据安全保护',
    content: [
      '我们采用行业标准的加密技术保护您的数据传输安全。',
      '所有个人数据都存储在安全的服务器环境中。',
      '实施严格的数据访问控制，仅授权人员可访问必要数据。',
      '定期进行安全审计和漏洞检测，确保系统安全。',
    ],
  },
  {
    id: 'information-sharing',
    title: '信息共享原则',
    content: [
      '我们不会向第三方出售、交易或转让您的个人信息。',
      '只有在法律要求或您明确授权的情况下才会共享信息。',
      '与可信赖的服务提供商合作时，会要求其遵守相同的隐私保护标准。',
      '在企业合并、收购或资产转让时，会事先通知用户并保护用户权益。',
    ],
  },
  {
    id: 'cookies-technology',
    title: 'Cookies和跟踪技术',
    content: [
      '我们使用Cookies来改善您的使用体验和网站性能。',
      'Cookies帮助我们记住您的偏好设置和登录状态。',
      '您可以通过浏览器设置控制或删除Cookies。',
      '禁用Cookies可能会影响部分网站功能的正常使用。',
    ],
  },
  {
    id: 'user-rights',
    title: '用户权利保障',
    content: [
      '您有权访问、更正或删除我们持有的关于您的个人信息。',
      '您可以随时撤回对数据处理的同意。',
      '您有权要求我们限制或停止处理您的个人信息。',
      '您可以要求获取您的个人数据副本进行转移。',
    ],
  },
  {
    id: 'minors-protection',
    title: '未成年人保护',
    content: [
      '我们特别重视未成年人的隐私保护。',
      '不会故意收集13岁以下儿童的个人信息。',
      '如发现误收集未成年人信息，将立即采取措施删除。',
      '建议家长指导和监督未成年人的网络活动。',
    ],
  },
  {
    id: 'data-transfer',
    title: '跨境数据传输',
    content: [
      '在某些情况下，您的数据可能需要跨境传输。',
      '我们确保数据传输符合相关法律法规要求。',
      '采用适当的安全措施保护跨境传输的数据。',
      '在必要时会获得您的明确同意。',
    ],
  },
  {
    id: 'policy-updates',
    title: '政策更新通知',
    content: [
      '我们可能会不时更新本隐私政策。',
      '重大变更将通过平台公告或邮件方式通知您。',
      '更新后的政策将在网站上发布并注明生效日期。',
      '继续使用我们的服务即表示您接受更新后的政策。',
    ],
  },
  {
    id: 'contact-us',
    title: '联系我们',
    content: [
      '如您对本隐私政策有任何疑问或建议，请随时联系我们。',
      '您可以通过平台内的客服系统联系我们。',
      '我们承诺在收到您的询问后及时回复。',
      '我们重视每一位用户的隐私关切并会认真处理。',
    ],
  },
])

/**
 * @description 滚动到指定章节
 * @param sectionId 章节ID
 */
const scrollToSection = (sectionId: string): void => {
  const query = uni.createSelectorQuery()
  query
    .select(`#${sectionId}`)
    .boundingClientRect((data: any) => {
      if (data) {
        uni.pageScrollTo({
          scrollTop: data.top - 100,
          duration: 300,
        })
      }
    })
    .exec()
}

/**
 * @description 返回上一页
 */
const goBack = (): void => {
  uni.navigateBack()
}
</script>

<template>
  <view class="privacy-policy-page">
    <HeadBar title="隐私政策" :show-back="true" :show-right-button="false" />
    <view class="privacy-content">
      <!-- 政策导航 -->
      <view class="policy-nav">
        <view class="nav-title">政策目录</view>
        <scroll-view class="nav-scroll" scroll-x>
          <view class="nav-items">
            <view
              v-for="section in privacySections"
              :key="section.id"
              class="nav-item"
              @click="scrollToSection(section.id)"
            >
              {{ section.title }}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 政策内容 -->
      <view class="policy-content">
        <view class="content-header">
          <text class="content-title">隐私保护政策</text>
          <text class="update-time">最后更新：{{ lastUpdateTime }}</text>
          <view class="intro-section">
            <text class="intro-text">
              我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们承诺严格遵守相关法律法规，采用相应的安全保护措施，保护您的个人信息。
            </text>
          </view>
        </view>

        <!-- 政策章节 -->
        <view
          v-for="(section, index) in privacySections"
          :key="section.id"
          :id="section.id"
          class="policy-section"
        >
          <view class="section-header">
            <text class="section-number">{{ index + 1 }}.</text>
            <text class="section-title">{{ section.title }}</text>
          </view>

          <view class="section-content">
            <view
              v-for="(paragraph, pIndex) in section.content"
              :key="pIndex"
              class="content-paragraph"
            >
              <text class="paragraph-number">{{ index + 1 }}.{{ pIndex + 1 }}</text>
              <text class="paragraph-text">{{ paragraph }}</text>
            </view>
          </view>
        </view>

        <!-- 重要提示 -->
        <view class="important-notice">
          <view class="notice-header">
            <view class="notice-icon">
              <text class="i-fa-exclamation-triangle"></text>
            </view>
            <text class="notice-title">重要提示</text>
          </view>
          <text class="notice-content">
            如果您不同意本隐私政策的任何内容，请停止使用我们的服务。我们建议您定期查看本政策，以了解我们如何保护您的信息。
          </text>
        </view>

        <!-- 政策底部 -->
        <view class="policy-footer">
          <view class="footer-divider"></view>
          <view class="footer-info">
            <text class="footer-text">
              我们致力于保护您的隐私权益。如有任何隐私问题或需要行使您的权利，请及时联系我们。
            </text>
            <text class="contact-info">隐私邮箱：<EMAIL></text>
            <text class="contact-info">客服热线：400-123-4567</text>
            <text class="response-info">我们将在30个工作日内回复您的隐私相关咨询</text>
          </view>

          <view class="policy-actions">
            <button class="action-btn primary-btn" @click="goBack">
              <text class="i-fa-shield-alt" style="margin-right: 8rpx; font-size: 24rpx"></text>
              <text>我已了解隐私政策</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.privacy-policy-page {
  min-height: 100vh;
  background: #f8fafc;
}

.privacy-content {
  padding: 40rpx;
}
// 政策导航
.policy-nav {
  padding: 0 32rpx;
  margin-bottom: 32rpx;

  .nav-title {
    margin-bottom: 20rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .nav-scroll {
    white-space: nowrap;

    .nav-items {
      display: flex;
      gap: 16rpx;
      padding-bottom: 16rpx;

      .nav-item {
        flex-shrink: 0;
        padding: 12rpx 24rpx;
        background: white;
        border: 2rpx solid #e2e8f0;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #64748b;
        transition: all 0.3s ease;

        &:active {
          background: #3b82f6;
          border-color: #3b82f6;
          color: white;
          transform: scale(0.95);
        }
      }
    }
  }
}

// 政策内容
.policy-content {
  padding: 0 32rpx 40rpx;

  .content-header {
    padding: 40rpx 32rpx;
    margin-bottom: 32rpx;
    background: white;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    text-align: center;

    .content-title {
      display: block;
      margin-bottom: 16rpx;
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .update-time {
      display: block;
      margin-bottom: 24rpx;
      font-size: 24rpx;
      color: #64748b;
    }

    .intro-section {
      padding-top: 24rpx;
      border-top: 2rpx solid #f1f5f9;

      .intro-text {
        font-size: 26rpx;
        color: #475569;
        line-height: 1.6;
      }
    }
  }
}

// 政策章节
.policy-section {
  margin-bottom: 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .section-header {
    display: flex;
    align-items: center;
    padding: 32rpx;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-bottom: 2rpx solid #e0f2fe;

    .section-number {
      margin-right: 16rpx;
      font-size: 28rpx;
      font-weight: 700;
      color: #3b82f6;
    }

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }

  .section-content {
    padding: 32rpx;

    .content-paragraph {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .paragraph-number {
        flex-shrink: 0;
        width: 80rpx;
        margin-right: 16rpx;
        font-size: 22rpx;
        font-weight: 600;
        color: #3b82f6;
        line-height: 1.6;
      }

      .paragraph-text {
        flex: 1;
        font-size: 26rpx;
        color: #475569;
        line-height: 1.6;
      }
    }
  }
}

// 重要提示
.important-notice {
  margin-bottom: 32rpx;
  padding: 32rpx;
  background: linear-gradient(135deg, #fefbf3 0%, #fef3c7 100%);
  border: 2rpx solid #fbbf24;
  border-radius: 20rpx;

  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .notice-icon {
      margin-right: 12rpx;
      color: #f59e0b;
      font-size: 32rpx;
    }

    .notice-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #92400e;
    }
  }

  .notice-content {
    font-size: 26rpx;
    color: #78350f;
    line-height: 1.6;
  }
}

// 政策底部
.policy-footer {
  margin-top: 40rpx;
  padding: 40rpx 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .footer-divider {
    height: 2rpx;
    margin-bottom: 32rpx;
    background: linear-gradient(to right, transparent, #e2e8f0, transparent);
  }

  .footer-info {
    margin-bottom: 40rpx;
    text-align: center;

    .footer-text {
      display: block;
      margin-bottom: 24rpx;
      font-size: 26rpx;
      color: #475569;
      line-height: 1.6;
    }

    .contact-info {
      display: block;
      margin-bottom: 8rpx;
      font-size: 24rpx;
      color: #64748b;
    }

    .response-info {
      display: block;
      margin-top: 16rpx;
      font-size: 22rpx;
      color: #94a3b8;
      font-style: italic;
    }
  }

  .policy-actions {
    display: flex;
    justify-content: center;

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 300rpx;
      height: 88rpx;
      font-size: 28rpx;
      font-weight: 600;
      border-radius: 20rpx;
      border: none;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &.primary-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
      }
    }
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .policy-content {
    padding: 0 20rpx 40rpx;
  }

  .policy-section {
    .section-content {
      padding: 24rpx;

      .content-paragraph {
        .paragraph-number {
          width: 60rpx;
        }

        .paragraph-text {
          font-size: 24rpx;
        }
      }
    }
  }

  .important-notice {
    padding: 24rpx;

    .notice-header {
      .notice-icon {
        font-size: 28rpx;
      }

      .notice-title {
        font-size: 26rpx;
      }
    }

    .notice-content {
      font-size: 24rpx;
    }
  }
}
</style>
