/**
 * 用户状态管理
 * 统一管理用户信息、登录状态等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ExtendedUserInfo } from '@/types/auth'
import { STORAGE_KEYS, ROUTE_PATHS, TIME_CONSTANTS, DEFAULT_AVATAR } from '@/types/auth-constants'

// 初始用户状态
const initState: ExtendedUserInfo = {
  id: '',
  name: '',
  nickname: '',
  avatar: '',
  token: '',
  openid: '',
  phone: '',
  email: '',
  realName: '',
  studentId: '',
  major: '',
  grade: '',
  status: undefined,
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const userInfo = ref<ExtendedUserInfo>({ ...initState })
    const isLoggedIn = ref(false)

    // 计算属性
    const isLogined = computed(() => {
      return !!userInfo.value.token && isLoggedIn.value
    })

    const userName = computed(() => {
      return (
        userInfo.value.realName ||
        userInfo.value.name ||
        userInfo.value.nickname ||
        userInfo.value.phone ||
        '未知用户'
      )
    })

    const userAvatar = computed(() => {
      return userInfo.value.avatar || DEFAULT_AVATAR
    })

    const userDisplayPhone = computed(() => {
      const phone = userInfo.value.phone
      if (!phone || phone.length !== 11) return phone
      return `${phone.slice(0, 3)}****${phone.slice(7)}`
    })

    const userDisplayEmail = computed(() => {
      const email = userInfo.value.email
      if (!email) return email
      const [username, domain] = email.split('@')
      if (!username || !domain) return email

      const maskedUsername =
        username.length > 2 ? `${username.slice(0, 2)}****${username.slice(-1)}` : username

      return `${maskedUsername}@${domain}`
    })

    /**
     * 设置用户信息
     * @param info 用户信息
     */
    const setUserInfo = (info: Partial<ExtendedUserInfo>) => {
      userInfo.value = { ...userInfo.value, ...info }
      isLoggedIn.value = true

      // 记录登录时间
      const loginTime = new Date().toISOString()

      // 同步到本地存储
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
      uni.setStorageSync(STORAGE_KEYS.IS_LOGGED_IN, true)
      uni.setStorageSync(STORAGE_KEYS.LOGIN_TIME, loginTime)

      if (info.token) {
        uni.setStorageSync(STORAGE_KEYS.TOKEN, info.token)
      }
    }

    /**
     * 设置Token
     * @param token Token值
     */
    const setToken = (token: string) => {
      userInfo.value.token = token
      uni.setStorageSync(STORAGE_KEYS.TOKEN, token)

      // 更新登录时间
      const loginTime = new Date().toISOString()
      uni.setStorageSync(STORAGE_KEYS.LOGIN_TIME, loginTime)
    }

    /**
     * 获取Token
     * @returns Token值
     */
    const getToken = (): string => {
      return userInfo.value.token || uni.getStorageSync(STORAGE_KEYS.TOKEN) || ''
    }

    /**
     * 更新用户信息
     * @param updates 要更新的字段
     */
    const updateUserInfo = (updates: Partial<ExtendedUserInfo>) => {
      userInfo.value = { ...userInfo.value, ...updates }
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
    }

    /**
     * 清除用户信息
     */
    const clearUserInfo = () => {
      userInfo.value = { ...initState }
      isLoggedIn.value = false

      // 清除本地存储
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(STORAGE_KEYS.TOKEN)
      uni.removeStorageSync(STORAGE_KEYS.IS_LOGGED_IN)
      uni.removeStorageSync(STORAGE_KEYS.LOGIN_TIME)
    }

    /**
     * 检查登录状态
     * @returns 是否已登录
     */
    const checkLoginStatus = (): boolean => {
      try {
        const token = uni.getStorageSync(STORAGE_KEYS.TOKEN)
        const storedUserInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO)
        const storedLoginStatus = uni.getStorageSync(STORAGE_KEYS.IS_LOGGED_IN)

        if (token && storedUserInfo && storedLoginStatus) {
          userInfo.value = { ...userInfo.value, ...JSON.parse(storedUserInfo) }
          userInfo.value.token = token
          isLoggedIn.value = true
          return true
        }
        return false
      } catch (error) {
        console.error('检查登录状态失败:', error)
        return false
      }
    }

    /**
     * 初始化用户状态（从本地存储恢复）
     */
    const initUserState = () => {
      checkLoginStatus()
    }

    /**
     * 登出
     */
    const logout = async () => {
      try {
        // 可以在这里调用登出API
        // await logoutApi()

        clearUserInfo()

        // 跳转到登录页
        uni.reLaunch({
          url: ROUTE_PATHS.LOGIN,
        })
      } catch (error) {
        console.error('登出失败:', error)
        // 即使API调用失败，也要清除本地状态
        clearUserInfo()
        uni.reLaunch({
          url: ROUTE_PATHS.LOGIN,
        })
      }
    }

    /**
     * 检查Token是否过期
     * @returns 是否过期
     */
    const isTokenExpired = (): boolean => {
      try {
        const loginTime = uni.getStorageSync(STORAGE_KEYS.LOGIN_TIME)
        if (!loginTime) return true

        const loginDate = new Date(loginTime)
        const now = new Date()
        const diffInHours = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60)

        // 使用常量配置的Token有效期
        return diffInHours > TIME_CONSTANTS.TOKEN_EXPIRE_HOURS
      } catch (error) {
        console.error('检查Token过期失败:', error)
        return true
      }
    }

    /**
     * 自动刷新Token
     */
    const autoRefreshToken = async () => {
      try {
        if (isTokenExpired() && isLogined.value) {
          // 这里可以调用刷新Token的API
          // const response = await refreshTokenApi()
          // setToken(response.data.access_token)

          console.log('Token已过期，需要重新登录')
          logout()
        }
      } catch (error) {
        console.error('自动刷新Token失败:', error)
        logout()
      }
    }

    /**
     * 检查是否需要重新登录
     * @returns 是否需要重新登录
     */
    const needReLogin = (): boolean => {
      return !isLogined.value || isTokenExpired()
    }

    /**
     * 安全跳转（需要登录的页面）
     * @param url 目标页面路径
     * @param params 跳转参数
     */
    const safePushPage = (url: string, params?: Record<string, any>) => {
      if (needReLogin()) {
        uni.reLaunch({
          url: ROUTE_PATHS.LOGIN,
        })
        return
      }

      let finalUrl = url
      if (params) {
        const query = Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
          .join('&')
        finalUrl = `${url}?${query}`
      }

      uni.navigateTo({
        url: finalUrl,
      })
    }

    /**
     * 获取用户基本信息摘要
     * @returns 用户信息摘要
     */
    const getUserSummary = () => {
      return {
        id: userInfo.value.id,
        name: userName.value,
        avatar: userAvatar.value,
        phone: userDisplayPhone.value,
        email: userDisplayEmail.value,
        major: userInfo.value.major,
        grade: userInfo.value.grade,
        isLogined: isLogined.value,
      }
    }

    /**
     * 重置用户状态
     */
    const reset = () => {
      userInfo.value = { ...initState }
      isLoggedIn.value = false
    }

    return {
      // 状态
      userInfo,
      isLoggedIn,

      // 计算属性
      isLogined,
      userName,
      userAvatar,
      userDisplayPhone,
      userDisplayEmail,

      // 方法
      setUserInfo,
      setToken,
      getToken,
      updateUserInfo,
      clearUserInfo,
      checkLoginStatus,
      initUserState,
      logout,
      isTokenExpired,
      autoRefreshToken,
      needReLogin,
      safePushPage,
      getUserSummary,
      reset,
    }
  },
  {
    persist: true,
  },
)
