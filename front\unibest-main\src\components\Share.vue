<script setup lang="ts">
/**
 * 通用分享组件
 * 支持多平台分享，包括微信、QQ、微博、复制链接等
 * 兼容H5端和微信小程序端，符合主流APP分享流程
 * <AUTHOR>
 */

import { ref, defineProps, defineEmits, computed, nextTick, onMounted, onUnmounted } from 'vue'
import {
  debounce,
  throttle,
  getDeviceCapabilities,
  validateShareData,
  shareAnalytics,
  ShareError,
  retryShare,
  measurePerformance
} from '@/utils/shareUtils'

// 分享平台类型
type SharePlatform = 'wechat' | 'moments' | 'qq' | 'weibo' | 'link' | 'screenshot'

// 分享平台配置
interface SharePlatformConfig {
  /** 平台标识 */
  key: SharePlatform
  /** 平台名称 */
  name: string
  /** 平台图标 */
  icon: string
  /** 平台颜色 */
  color: string
  /** 是否可用 */
  available: boolean
}

// 分享数据接口
interface ShareData {
  /** 分享标题 */
  title: string
  /** 分享内容/描述 */
  content: string
  /** 分享链接 */
  url?: string
  /** 分享图片 */
  imageUrl?: string
  /** 额外数据 */
  extra?: Record<string, any>
}

// 组件属性
const props = defineProps({
  /** 是否显示分享弹窗 */
  visible: {
    type: Boolean,
    required: true,
  },
  /** 分享数据 */
  shareData: {
    type: Object as () => ShareData,
    required: true,
  },
  /** 自定义分享平台列表 */
  platforms: {
    type: Array as () => SharePlatform[],
    default: () => ['wechat', 'moments', 'qq', 'weibo', 'link', 'screenshot'],
  },
  /** 是否显示取消按钮 */
  showCancel: {
    type: Boolean,
    default: true,
  },
  /** 自定义样式类名 */
  customClass: {
    type: String,
    default: '',
  },
  /** 是否点击遮罩关闭 */
  maskClosable: {
    type: Boolean,
    default: true,
  },
})

// 组件事件
const emit = defineEmits<{
  /** 关闭分享弹窗 */
  'update:visible': [visible: boolean]
  /** 分享成功 */
  'share-success': [platform: SharePlatform, result?: any]
  /** 分享失败 */
  'share-error': [platform: SharePlatform, error: Error]
  /** 取消分享 */
  'cancel': []
}>()

// 响应式数据
const isAnimating = ref(false)
const isSharing = ref(false)
const currentPlatform = ref<SharePlatform | null>(null)
const deviceCapabilities = ref(getDeviceCapabilities())

// 前置声明分享处理函数
let debouncedShare: (platform: SharePlatform) => void

// 组件挂载时的初始化
onMounted(() => {
  // 预加载分享图标（如果需要）
  // preloadImages([...])
})

// 组件卸载时的清理
onUnmounted(() => {
  // 清理定时器等资源
})

// 分享平台配置
const platformConfigs: SharePlatformConfig[] = [
  {
    key: 'wechat',
    name: '微信好友',
    icon: 'i-fa-brands-weixin',
    color: '#07C160',
    available: true,
  },
  {
    key: 'moments',
    name: '朋友圈',
    icon: 'i-fa-solid-circle-nodes',
    color: '#07C160',
    available: true,
  },
  {
    key: 'qq',
    name: 'QQ好友',
    icon: 'i-fa-brands-qq',
    color: '#12B7F5',
    available: true,
  },
  {
    key: 'weibo',
    name: '新浪微博',
    icon: 'i-fa-brands-weibo',
    color: '#E6162D',
    available: true,
  },
  {
    key: 'link',
    name: '复制链接',
    icon: 'i-fa-solid-link',
    color: '#666666',
    available: true,
  },
  {
    key: 'screenshot',
    name: '保存图片',
    icon: 'i-fa-solid-camera',
    color: '#FF6B35',
    available: true,
  },
]

// 计算可用的分享平台
const availablePlatforms = computed(() => {
  return platformConfigs.filter(config =>
    props.platforms.includes(config.key) && config.available
  )
})

/**
 * @description 关闭分享弹窗
 */
const closeShare = (): void => {
  if (isSharing.value) return

  isAnimating.value = true
  setTimeout(() => {
    emit('update:visible', false)
    isAnimating.value = false
  }, 300)
}

/**
 * @description 处理遮罩点击
 */
const handleMaskClick = (): void => {
  if (props.maskClosable) {
    closeShare()
  }
}

/**
 * @description 处理取消按钮点击
 */
const handleCancel = (): void => {
  emit('cancel')
  closeShare()
}

/**
 * @description 处理平台分享（对外接口）
 * @param platform 分享平台
 */
const handleShare = (platform: SharePlatform): void => {
  debouncedShare(platform)
}

/**
 * @description 内部分享处理逻辑
 * @param platform 分享平台
 */
const handleShareInternal = async (platform: SharePlatform): Promise<void> => {
  if (isSharing.value) return

  // 数据验证
  const validation = validateShareData(props.shareData)
  if (!validation.isValid) {
    console.error('分享数据验证失败:', validation.errors)
    uni.showToast({
      title: validation.errors[0] || '分享数据不完整',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  const startTime = performance.now()

  try {
    isSharing.value = true
    currentPlatform.value = platform

    // 触觉反馈
    if (deviceCapabilities.value.hasVibration) {
      uni.vibrateShort({
        type: 'light',
      })
    }

    // 使用重试机制执行分享
    const result = await retryShare(async () => {
      return await executeShare(platform)
    }, 2, 500)

    // 记录成功统计
    shareAnalytics.record({
      platform,
      timestamp: Date.now(),
      success: true,
      duration: performance.now() - startTime
    })

    emit('share-success', platform, result)

    // 显示成功提示
    uni.showToast({
      title: getSuccessMessage(platform),
      icon: 'success',
      duration: 1500,
    })

    // 延迟关闭弹窗
    setTimeout(() => {
      closeShare()
    }, 1000)

  } catch (error) {
    const shareError = error instanceof ShareError ? error : new ShareError(
      `分享到${platform}失败`,
      platform,
      'SHARE_FAILED',
      error as Error
    )

    // 记录失败统计
    shareAnalytics.record({
      platform,
      timestamp: Date.now(),
      success: false,
      error: shareError.message,
      duration: performance.now() - startTime
    })

    console.error('分享失败:', shareError)
    emit('share-error', platform, shareError)

    // 显示错误提示
    uni.showToast({
      title: getErrorMessage(platform),
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isSharing.value = false
    currentPlatform.value = null
  }
}

/**
 * @description 执行具体的分享操作
 * @param platform 分享平台
 */
const executeShare = async (platform: SharePlatform): Promise<any> => {
  switch (platform) {
    case 'wechat':
      return await shareToWechat()
    case 'moments':
      return await shareToMoments()
    case 'qq':
      return await shareToQQ()
    case 'weibo':
      return await shareToWeibo()
    case 'link':
      return await copyLink()
    case 'screenshot':
      return await saveScreenshot()
    default:
      throw new ShareError(`不支持的分享平台: ${platform}`, platform, 'UNSUPPORTED_PLATFORM')
  }
}

/**
 * @description 分享到微信好友
 */
const shareToWechat = async (): Promise<any> => {
  // #ifdef MP-WEIXIN
  return new Promise((resolve, reject) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession',
      type: 0,
      title: props.shareData.title,
      summary: props.shareData.content,
      href: props.shareData.url || '',
      imageUrl: props.shareData.imageUrl || '',
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '分享失败'))
      }
    })
  })
  // #endif

  // #ifdef H5
  // H5端使用Web Share API或降级到复制链接
  if (navigator.share) {
    return navigator.share({
      title: props.shareData.title,
      text: props.shareData.content,
      url: props.shareData.url || window.location.href,
    })
  } else {
    return copyLink()
  }
  // #endif

  // #ifndef MP-WEIXIN || H5
  throw new Error('当前平台不支持微信分享')
  // #endif
}

/**
 * @description 分享到朋友圈
 */
const shareToMoments = async (): Promise<any> => {
  // #ifdef MP-WEIXIN
  return new Promise((resolve, reject) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneTimeline',
      type: 0,
      title: props.shareData.title,
      summary: props.shareData.content,
      href: props.shareData.url || '',
      imageUrl: props.shareData.imageUrl || '',
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '分享失败'))
      }
    })
  })
  // #endif

  // #ifdef H5
  // H5端降级到复制链接
  return copyLink()
  // #endif

  // #ifndef MP-WEIXIN || H5
  throw new Error('当前平台不支持朋友圈分享')
  // #endif
}

/**
 * @description 分享到QQ
 */
const shareToQQ = async (): Promise<any> => {
  // #ifdef MP-QQ
  return new Promise((resolve, reject) => {
    uni.share({
      provider: 'qq',
      type: 0,
      title: props.shareData.title,
      summary: props.shareData.content,
      href: props.shareData.url || '',
      imageUrl: props.shareData.imageUrl || '',
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '分享失败'))
      }
    })
  })
  // #endif

  // #ifdef H5
  // H5端降级到复制链接
  return copyLink()
  // #endif

  // #ifndef MP-QQ || H5
  throw new Error('当前平台不支持QQ分享')
  // #endif
}

/**
 * @description 分享到微博
 */
const shareToWeibo = async (): Promise<any> => {
  // #ifdef H5
  const shareUrl = props.shareData.url || window.location.href
  const shareText = `${props.shareData.title} ${props.shareData.content}`
  const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`

  window.open(weiboUrl, '_blank')
  return { success: true }
  // #endif

  // #ifndef H5
  // 非H5端降级到复制链接
  return copyLink()
  // #endif
}

/**
 * @description 复制链接
 */
const copyLink = async (): Promise<any> => {
  const shareUrl = props.shareData.url ||
    // #ifdef H5
    window.location.href
    // #endif
    // #ifndef H5
    'https://example.com'
    // #endif

  // #ifdef H5
  if (navigator.clipboard) {
    await navigator.clipboard.writeText(shareUrl)
    return { success: true, url: shareUrl }
  }
  // #endif

  // 使用uni-app的剪贴板API
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data: shareUrl,
      success: () => {
        resolve({ success: true, url: shareUrl })
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '复制失败'))
      }
    })
  })
}

/**
 * @description 保存截图
 */
const saveScreenshot = async (): Promise<any> => {
  // 这里可以实现截图功能，或者调用外部截图API
  // 目前先返回成功状态
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true })
    }, 1000)
  })
}

/**
 * @description 获取成功提示消息
 * @param platform 分享平台
 */
const getSuccessMessage = (platform: SharePlatform): string => {
  const messages: Record<SharePlatform, string> = {
    wechat: '已分享到微信',
    moments: '已分享到朋友圈',
    qq: '已分享到QQ',
    weibo: '已分享到微博',
    link: '链接已复制',
    screenshot: '图片已保存',
  }
  return messages[platform] || '分享成功'
}

/**
 * @description 获取错误提示消息
 * @param platform 分享平台
 */
const getErrorMessage = (platform: SharePlatform): string => {
  const messages: Record<SharePlatform, string> = {
    wechat: '微信分享失败',
    moments: '朋友圈分享失败',
    qq: 'QQ分享失败',
    weibo: '微博分享失败',
    link: '复制链接失败',
    screenshot: '保存图片失败',
  }
  return messages[platform] || '分享失败'
}

/**
 * @description 检查平台是否正在分享
 * @param platform 分享平台
 */
const isPlatformSharing = (platform: SharePlatform): boolean => {
  return isSharing.value && currentPlatform.value === platform
}

// 初始化防抖函数
debouncedShare = debounce(handleShareInternal, 300)
</script>

<template>
  <!-- 分享弹窗遮罩 -->
  <view
    v-if="visible"
    class="share-overlay"
    :class="[
      customClass,
      { 'share-overlay--animating': isAnimating }
    ]"
    @click="handleMaskClick"
  >
    <!-- 分享面板 -->
    <view
      class="share-panel"
      :class="{ 'share-panel--animating': isAnimating }"
      @click.stop
    >
      <!-- 面板头部 -->
      <view class="share-header">
        <view class="share-header__content">
          <view class="share-icon">
            <text class="i-fa-solid-share-nodes"></text>
          </view>
          <view class="share-title">分享到</view>
        </view>
        <view class="share-header__extra">
          <text class="share-subtitle">选择分享方式</text>
        </view>
      </view>

      <!-- 分享平台网格 -->
      <view class="share-platforms">
        <view
          v-for="platform in availablePlatforms"
          :key="platform.key"
          class="platform-item"
          :class="{
            'platform-item--loading': isPlatformSharing(platform.key),
            'platform-item--disabled': isSharing && !isPlatformSharing(platform.key)
          }"
          @click="handleShare(platform.key)"
        >
          <!-- 平台图标 -->
          <view
            class="platform-icon"
            :style="{
              backgroundColor: platform.color,
              boxShadow: `0 4rpx 12rpx ${platform.color}40`
            }"
          >
            <!-- 加载状态 -->
            <view v-if="isPlatformSharing(platform.key)" class="loading-spinner">
              <text class="i-fa-solid-spinner loading-icon"></text>
            </view>
            <!-- 平台图标 -->
            <text v-else :class="platform.icon" class="platform-icon-text"></text>
          </view>

          <!-- 平台名称 -->
          <text class="platform-name">{{ platform.name }}</text>
        </view>
      </view>

      <!-- 分享内容预览 -->
      <view class="share-preview">
        <view class="preview-content">
          <view class="preview-title">{{ shareData.title }}</view>
          <view class="preview-desc">{{ shareData.content }}</view>
        </view>
        <view v-if="shareData.imageUrl" class="preview-image">
          <image :src="shareData.imageUrl" mode="aspectFill" />
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="share-footer">
        <button
          v-if="showCancel"
          class="cancel-btn"
          :disabled="isSharing"
          @click="handleCancel"
        >
          取消
        </button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 分享弹窗遮罩
.share-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  animation: fadeIn 0.3s ease-out;

  &--animating {
    pointer-events: none;
  }
}

// 分享面板
.share-panel {
  width: 100%;
  max-width: 750rpx;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  position: relative;
  overflow: hidden;

  // 面板背景装饰
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200rpx;
    content: '';
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.05) 0%,
      rgba(79, 209, 199, 0.05) 50%,
      transparent 100%
    );
    pointer-events: none;
    border-radius: 32rpx 32rpx 0 0;
  }

  &--animating {
    animation: slideDown 0.3s ease-out;
  }
}

// 面板头部
.share-header {
  position: relative;
  z-index: 2;
  padding: 40rpx 32rpx 32rpx;
  text-align: center;

  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    margin-bottom: 8rpx;

    .share-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      font-size: 24rpx;
      color: #ffffff;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border-radius: 50%;
      box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
    }

    .share-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;
    }
  }

  &__extra {
    .share-subtitle {
      font-size: 24rpx;
      color: #999999;
      line-height: 1.4;
    }
  }
}

// 分享平台网格
.share-platforms {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  padding: 0 32rpx 32rpx;

  .platform-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
    padding: 20rpx;
    border-radius: 20rpx;
    transition: all 0.3s ease;
    cursor: pointer;

    // #ifdef H5
    &:hover:not(.platform-item--disabled) {
      background: rgba(0, 201, 167, 0.05);
      transform: translateY(-4rpx);
    }
    // #endif

    &:active:not(.platform-item--disabled) {
      transform: scale(0.95);
    }

    &--loading {
      pointer-events: none;
      opacity: 0.8;
    }

    &--disabled {
      opacity: 0.5;
      pointer-events: none;
    }

    .platform-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      transition: all 0.3s ease;
      position: relative;

      .loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        .loading-icon {
          font-size: 32rpx;
          color: #ffffff;
          animation: spin 1s linear infinite;
        }
      }

      .platform-icon-text {
        font-size: 36rpx;
        color: #ffffff;
      }
    }

    .platform-name {
      font-size: 24rpx;
      color: #666666;
      text-align: center;
      line-height: 1.4;
      font-weight: 500;
    }
  }
}

// 分享内容预览
.share-preview {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin: 0 32rpx 32rpx;
  padding: 24rpx;
  background: rgba(248, 249, 250, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(240, 240, 240, 0.8);

  .preview-content {
    flex: 1;
    min-width: 0;

    .preview-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;
      margin-bottom: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .preview-desc {
      font-size: 24rpx;
      color: #666666;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .preview-image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 12rpx;
    overflow: hidden;
    flex-shrink: 0;

    image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 底部按钮
.share-footer {
  position: relative;
  z-index: 2;
  padding: 0 32rpx 40rpx;

  .cancel-btn {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #666666;
    border: 1rpx solid #f0f0f0;
    border-radius: 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;

    // #ifdef H5
    &:hover:not(:disabled) {
      background: #f1f5f9;
      color: #333333;
      transform: translateY(-2rpx);
    }
    // #endif

    &:active:not(:disabled) {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(100%);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
// #ifdef H5
@media (max-width: 750px) {
  .share-panel {
    border-radius: 24rpx 24rpx 0 0;

    .share-header {
      padding: 32rpx 24rpx 24rpx;

      &__content .share-title {
        font-size: 28rpx;
      }

      &__extra .share-subtitle {
        font-size: 22rpx;
      }
    }

    .share-platforms {
      gap: 24rpx;
      padding: 0 24rpx 24rpx;

      .platform-item {
        padding: 16rpx;
        gap: 12rpx;

        .platform-icon {
          width: 72rpx;
          height: 72rpx;

          .platform-icon-text {
            font-size: 28rpx;
          }
        }

        .platform-name {
          font-size: 22rpx;
        }
      }
    }

    .share-preview {
      margin: 0 24rpx 24rpx;
      padding: 20rpx;

      .preview-content {
        .preview-title {
          font-size: 26rpx;
        }

        .preview-desc {
          font-size: 22rpx;
        }
      }

      .preview-image {
        width: 64rpx;
        height: 64rpx;
      }
    }

    .share-footer {
      padding: 0 24rpx 32rpx;

      .cancel-btn {
        height: 72rpx;
        font-size: 26rpx;
      }
    }
  }
}
// #endif

// 暗色主题支持
// #ifdef H5
@media (prefers-color-scheme: dark) {
  .share-panel {
    background: #1a1a1a;
    color: #ffffff;

    .share-header {
      &__content .share-title {
        color: #ffffff;
      }

      &__extra .share-subtitle {
        color: #cccccc;
      }
    }

    .share-platforms .platform-item .platform-name {
      color: #cccccc;
    }

    .share-preview {
      background: rgba(40, 40, 40, 0.8);
      border-color: rgba(80, 80, 80, 0.8);

      .preview-content {
        .preview-title {
          color: #ffffff;
        }

        .preview-desc {
          color: #cccccc;
        }
      }
    }

    .share-footer .cancel-btn {
      background: #333333;
      color: #cccccc;
      border-color: #444444;

      // #ifdef H5
      &:hover:not(:disabled) {
        background: #404040;
        color: #ffffff;
      }
      // #endif
    }
  }
}
// #endif
</style>