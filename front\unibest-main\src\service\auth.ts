/**
 * 认证服务模块
 * 统一管理所有认证相关的API接口
 */

import { httpGet, httpPost } from '@/utils/http'
import type {
  ApiResponse,
  SendSmsCodeParams,
  SendEmailCodeParams,
  LoginByPhoneParams,
  LoginByPasswordParams,
  RegisterParams,
  CheckPhoneExistsParams,
  CheckEmailExistsParams,
  ResetPasswordParams,
  ChangePasswordParams,
  ThirdPartyLoginParams,
  UserInfo,
  RefreshTokenResponse,
  OnlineStatusResponse,
  CheckExistsResponse,
} from '@/types/auth'
import { DEFAULT_CLIENT_CONFIG, AUTH_API_ENDPOINTS } from '@/types/auth-constants'

/**
 * 发送短信验证码
 * @param params 手机号参数
 * @returns API响应
 */
export function sendSmsCode(params: { params: SendSmsCodeParams }): Promise<ApiResponse<void>> {
  return httpPost<void>(AUTH_API_ENDPOINTS.SEND_SMS_CODE, {
    phone: params.params.phone,
  })
}

/**
 * 发送邮箱验证码
 * @param params 邮箱参数
 * @returns API响应
 */
export function sendEmailCode(params: { params: SendEmailCodeParams }): Promise<ApiResponse<void>> {
  return httpPost<void>(AUTH_API_ENDPOINTS.SEND_EMAIL_CODE, {
    email: params.params.email,
  })
}

/**
 * 手机验证码登录
 * @param params 登录参数
 * @returns 用户信息响应
 */
export function loginByPhone(params: {
  params: LoginByPhoneParams
}): Promise<ApiResponse<UserInfo>> {
  const requestData = {
    ...params.params,
    ...DEFAULT_CLIENT_CONFIG,
    grantType: 'phone',
  }

  return httpPost<UserInfo>(AUTH_API_ENDPOINTS.LOGIN_BY_PHONE, requestData)
}

/**
 * 密码登录
 * @param params 登录参数
 * @returns 用户信息响应
 */
export function loginByPassword(params: {
  params: LoginByPasswordParams
}): Promise<ApiResponse<UserInfo>> {
  const requestData = {
    ...params.params,
    ...DEFAULT_CLIENT_CONFIG,
    grantType: 'password',
  }

  return httpPost<UserInfo>(AUTH_API_ENDPOINTS.LOGIN_BY_PASSWORD, requestData)
}

/**
 * 用户注册
 * @param params 注册参数
 * @returns 用户信息响应
 */
export function register(params: { params: RegisterParams }): Promise<ApiResponse<UserInfo>> {
  const requestData = {
    ...params.params,
    ...DEFAULT_CLIENT_CONFIG,
    grantType: 'register',
  }

  return httpPost<UserInfo>(AUTH_API_ENDPOINTS.REGISTER, requestData)
}

/**
 * 检查手机号是否存在
 * @param params 手机号参数
 * @returns 存在性检查响应
 */
export function checkPhoneExists(params: {
  params: CheckPhoneExistsParams
}): Promise<ApiResponse<CheckExistsResponse>> {
  return httpPost<CheckExistsResponse>(AUTH_API_ENDPOINTS.CHECK_PHONE_EXISTS, {
    phone: params.params.phone,
  })
}

/**
 * 检查邮箱是否存在
 * @param params 邮箱参数
 * @returns 存在性检查响应
 */
export function checkEmailExists(params: {
  params: CheckEmailExistsParams
}): Promise<ApiResponse<CheckExistsResponse>> {
  return httpPost<CheckExistsResponse>(AUTH_API_ENDPOINTS.CHECK_EMAIL_EXISTS, {
    email: params.params.email,
  })
}

/**
 * 重置密码
 * @param params 重置密码参数
 * @returns API响应
 */
export function resetPassword(params: { params: ResetPasswordParams }): Promise<ApiResponse<void>> {
  return httpPost<void>(AUTH_API_ENDPOINTS.RESET_PASSWORD, params.params)
}

/**
 * 修改密码
 * @param params 修改密码参数
 * @returns API响应
 */
export function changePassword(params: {
  params: ChangePasswordParams
}): Promise<ApiResponse<void>> {
  return httpPost<void>(AUTH_API_ENDPOINTS.CHANGE_PASSWORD, params.params)
}

/**
 * 第三方登录
 * @param params 第三方登录参数
 * @returns 用户信息响应
 */
export function thirdPartyLogin(params: {
  params: ThirdPartyLoginParams
}): Promise<ApiResponse<UserInfo>> {
  const requestData = {
    ...params.params,
    ...DEFAULT_CLIENT_CONFIG,
    grantType: 'social',
  }

  return httpPost<UserInfo>(AUTH_API_ENDPOINTS.THIRD_PARTY_LOGIN, requestData)
}

/**
 * 刷新Token
 * @returns Token刷新响应
 */
export function refreshToken(): Promise<ApiResponse<RefreshTokenResponse>> {
  return httpPost<RefreshTokenResponse>(AUTH_API_ENDPOINTS.REFRESH_TOKEN, DEFAULT_CLIENT_CONFIG)
}

/**
 * 退出登录
 * @returns API响应
 */
export function logout(): Promise<ApiResponse<void>> {
  return httpPost<void>(AUTH_API_ENDPOINTS.LOGOUT)
}

/**
 * 获取当前用户信息
 * @returns 用户信息响应
 */
export function getCurrentUser(): Promise<ApiResponse<UserInfo>> {
  return httpGet<UserInfo>(AUTH_API_ENDPOINTS.CURRENT_USER)
}

/**
 * 获取在线状态
 * @returns 在线状态响应
 */
export function getOnlineStatus(): Promise<ApiResponse<OnlineStatusResponse>> {
  return httpGet<OnlineStatusResponse>(AUTH_API_ENDPOINTS.ONLINE_STATUS)
}
