/**
 * 视频模块类型定义
 * <AUTHOR>
 * @description 定义视频课程相关的所有类型接口
 */

/**
 * 视频基础信息
 */
export interface Video {
  id: number
  title: string
  description: string
  instructor: string
  instructorAvatar?: string
  instructorId?: number
  duration: string
  thumbnail: string
  category: string
  difficulty: string
  rating: number
  studentCount: number
  price: number
  isFree: boolean
  videoUrl?: string
  viewCount: number
  likeCount: number
  collectCount: number
  shareCount: number
  tags: string[]
  publishTime: string
  createTime: string
  updateTime: string
  isLiked?: boolean
  isCollected?: boolean
  isBookmarked?: boolean
  isPurchased?: boolean
  isCompleted?: boolean
  completionRate?: number
  isFollowed?: boolean
  instructorFollowers?: number
}

/**
 * 视频详细信息
 */
export interface VideoDetail extends Video {
  videoUrl: string
  likeCount: number
  collectCount: number
  shareCount: number
  isLiked: boolean
  isCollected: boolean
  publishTime: string
  instructorAvatar: string
  instructorFollowers: number
  isFollowed: boolean
  chapters?: VideoChapter[]
  resources?: VideoResource[]
}

/**
 * 视频章节信息
 */
export interface VideoChapter {
  id: string
  title: string
  duration: string
  startTime: number
  endTime: number
  isCompleted: boolean
}

/**
 * 视频资源信息
 */
export interface VideoResource {
  id: string
  name: string
  type: 'pdf' | 'doc' | 'ppt' | 'zip' | 'other'
  size: string
  downloadUrl: string
}

/**
 * 视频评论信息
 */
export interface VideoComment {
  id: number
  videoId: number
  userId: number
  userName: string
  userAvatar: string
  parentId?: number
  content: string
  likeCount: number
  isLiked: boolean
  replies?: VideoComment[]
  createTime: string
  publishTime: string
}

/**
 * 相关推荐视频
 */
export interface RelatedVideo {
  id: number
  title: string
  instructor: string
  duration: string
  thumbnail: string
  viewCount: number
  rating: number
  category: string
  isFree: boolean
  price: number
}

/**
 * 视频播放记录
 */
export interface VideoPlayRecord {
  videoId: number
  userId: number
  lastPlayTime: number
  duration: number
  completionRate: number
  isCompleted: boolean
}

/**
 * 视频学习统计
 */
export interface VideoLearningStats {
  totalVideos: number
  completedVideos: number
  totalHours: number
  studiedToday: number
  weeklyGoal: number
  weeklyProgress: number
  categoryStats: CategoryStats[]
}

/**
 * 分类统计
 */
export interface CategoryStats {
  category: string
  categoryName: string
  count: number
  completed: number
  completionRate: number
}

/**
 * 视频列表查询参数
 */
export interface VideoQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  difficulty?: string
  isFree?: boolean
  sortBy?: 'latest' | 'popular' | 'rating' | 'price'
  sortOrder?: 'asc' | 'desc'
}

/**
 * 视频列表响应
 */
export interface VideoListResponse {
  videos: Video[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

/**
 * 评论查询参数
 */
export interface CommentQueryParams {
  videoId: number
  page?: number
  pageSize?: number
  sortBy?: 'latest' | 'popular'
  sortOrder?: 'asc' | 'desc'
}

/**
 * 评论列表响应
 */
export interface CommentListResponse {
  comments: VideoComment[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

/**
 * 发布评论参数
 */
export interface PublishCommentParams {
  videoId: number
  content: string
  parentId?: number
}

/**
 * 视频操作响应
 */
export interface VideoActionResponse {
  success: boolean
  message: string
  data?: any
}

/**
 * 视频分类选项
 */
export interface VideoCategoryOption {
  key: string
  name: string
  icon: string
}

/**
 * 视频笔记
 */
export interface VideoNote {
  id: string
  videoId: number
  userId: number
  content: string
  timestamp: number
  createTime: string
  updateTime: string
}

/**
 * 学习计划
 */
export interface VideoLearningPlan {
  id: string
  title: string
  description: string
  videos: Video[]
  totalDuration: string
  completedCount: number
  totalCount: number
  progress: number
  createTime: string
  updateTime: string
}

/**
 * 支付相关参数
 */
export interface VideoPaymentParams {
  videoId: number
  paymentMethod: 'wechat' | 'alipay'
  amount: number
}

/**
 * 视频购买状态
 */
export interface VideoPurchaseStatus {
  videoId: number
  userId: number
  isPurchased: boolean
  purchaseTime?: string
  orderNo?: string
}

/**
 * 导出所有视频模块类型
 */
export type VideoModuleTypes =
  | Video
  | VideoDetail
  | VideoChapter
  | VideoResource
  | VideoComment
  | RelatedVideo
  | VideoPlayRecord
  | VideoLearningStats
  | CategoryStats
  | VideoQueryParams
  | VideoListResponse
  | CommentQueryParams
  | CommentListResponse
  | PublishCommentParams
  | VideoActionResponse
  | VideoCategoryOption
  | VideoNote
  | VideoLearningPlan
  | VideoPaymentParams
  | VideoPurchaseStatus
