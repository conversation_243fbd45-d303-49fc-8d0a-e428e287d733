<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import RadarChart from '@/components/RadarChart.vue'

/**
 * @description 能力评估详情页面
 * 展示用户各项能力的详细评估结果和提升建议
 */

// 用户能力数据
const userAbilities = reactive({
  professionalKnowledge: 85, // 专业知识
  logicalThinking: 75, // 逻辑思维
  languageExpression: 70, // 语言表达
  stressResistance: 80, // 抗压能力
  teamCollaboration: 90, // 团队协作
  innovation: 65, // 创新能力
})

// 能力详情配置
const abilityDetails = ref([
  {
    key: 'professionalKnowledge',
    name: '专业知识',
    icon: 'i-mdi-school',
    score: 85,
    trend: 'up', // up, down, stable
    trendValue: '+5%',
    description: '对相关领域的理论知识和实践经验的掌握程度',
    suggestions: [
      '建议深入学习框架原理，不只停留在使用层面',
      '多阅读技术文档和源码，提升技术深度',
      '参与开源项目，积累实战经验',
    ],
    relatedResources: [
      { name: '深入理解JavaScript', type: 'book', link: '/pages/resources/index?tag=javascript' },
      { name: 'Vue3源码解析', type: 'video', link: '/pages/resources/index?tag=vue3' },
    ],
  },
  {
    key: 'logicalThinking',
    name: '逻辑思维',
    icon: 'i-mdi-brain',
    score: 75,
    trend: 'up',
    trendValue: '+3%',
    description: '分析问题、解决问题的逻辑能力和思维清晰度',
    suggestions: [
      '多练习算法题，提升逻辑思维能力',
      '学习使用思维导图工具，培养结构化思维',
      '在回答问题时注意条理性，使用STAR法则',
    ],
    relatedResources: [
      { name: '算法思维训练营', type: 'course', link: '/pages/resources/index?tag=algorithm' },
      { name: 'STAR法则详解', type: 'article', link: '/pages/resources/index?tag=star-method' },
    ],
  },
  {
    key: 'languageExpression',
    name: '语言表达',
    icon: 'i-mdi-message-text',
    score: 70,
    trend: 'stable',
    trendValue: '0%',
    description: '清晰、准确、有条理地表达想法和观点的能力',
    suggestions: [
      '练习将技术概念用通俗易懂的语言解释',
      '多进行模拟面试，提升表达流畅度',
      '注意语速和停顿，让表达更有节奏感',
    ],
    relatedResources: [
      { name: '技术表达训练', type: 'video', link: '/pages/resources/index?tag=expression' },
      {
        name: '面试表达技巧',
        type: 'article',
        link: '/pages/resources/index?tag=interview-skills',
      },
    ],
  },
  {
    key: 'stressResistance',
    name: '抗压能力',
    icon: 'i-mdi-shield',
    score: 80,
    trend: 'up',
    trendValue: '+2%',
    description: '在压力环境下保持冷静、正常发挥的能力',
    suggestions: [
      '通过深呼吸等方法缓解紧张情绪',
      '充分准备，增强自信心',
      '将压力转化为动力，积极面对挑战',
    ],
    relatedResources: [
      { name: '压力管理技巧', type: 'video', link: '/pages/resources/index?tag=stress-management' },
      {
        name: '面试心理调节',
        type: 'article',
        link: '/pages/resources/index?tag=interview-psychology',
      },
    ],
  },
  {
    key: 'teamCollaboration',
    name: '团队协作',
    icon: 'i-mdi-account-group',
    score: 90,
    trend: 'up',
    trendValue: '+4%',
    description: '与他人有效合作、共同完成任务的能力',
    suggestions: [
      '继续保持良好的团队合作精神',
      '主动分享知识，帮助团队成员成长',
      '学习更多项目管理和沟通技巧',
    ],
    relatedResources: [
      { name: '敏捷开发实践', type: 'course', link: '/pages/resources/index?tag=agile' },
      {
        name: '高效团队沟通',
        type: 'article',
        link: '/pages/resources/index?tag=team-communication',
      },
    ],
  },
  {
    key: 'innovation',
    name: '创新能力',
    icon: 'i-mdi-lightbulb',
    score: 65,
    trend: 'down',
    trendValue: '-2%',
    description: '提出新想法、新方案，创造性解决问题的能力',
    suggestions: [
      '多关注行业前沿技术和趋势',
      '尝试用不同的方法解决同一个问题',
      '参与创新项目，培养创新思维',
    ],
    relatedResources: [
      { name: '创新思维训练', type: 'course', link: '/pages/resources/index?tag=innovation' },
      { name: '技术创新案例', type: 'video', link: '/pages/resources/index?tag=tech-innovation' },
    ],
  },
])

// 页面加载状态
const isLoading = ref(false)

// 计算雷达图数据
const radarChartData = computed(() => {
  return abilityDetails.value.map((item) => ({
    name: item.name,
    current: item.score,
    previous: item.score - 5, // 假设的历史数据，可以根据实际情况调整
    max: 100,
    color: getAbilityLevel(item.score).color,
  }))
})

/**
 * @description 获取能力等级描述
 */
function getAbilityLevel(score: number): { level: string; color: string } {
  if (score >= 90) return { level: '优秀', color: '#00c9a7' }
  if (score >= 80) return { level: '良好', color: '#4CAF50' }
  if (score >= 70) return { level: '中等', color: '#FFC107' }
  if (score >= 60) return { level: '及格', color: '#FF9800' }
  return { level: '待提升', color: '#F44336' }
}

/**
 * @description 跳转到学习资源
 */
function goToResource(link: string) {
  uni.navigateTo({ url: link })
}

/**
 * @description 显示加载状态
 */
function showLoading() {
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
}

onLoad(() => {
  showLoading()
  // 从存储中获取用户能力数据
  try {
    const abilities = uni.getStorageSync('userAbilities')
    if (abilities) {
      Object.assign(userAbilities, JSON.parse(abilities))
      // 更新详情数据
      abilityDetails.value.forEach((item) => {
        item.score = userAbilities[item.key as keyof typeof userAbilities]
      })
    }
  } catch (e) {
    console.error('获取能力数据失败:', e)
  }
})
</script>

<template>
  <view class="ability-assessment-page">
    <HeadBar title="能力评估详情" :show-back="true" :fixed="false" />

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载能力数据...</text>
    </view>

    <!-- 主要内容 -->
    <scroll-view v-else class="main-content" scroll-y>
      <!-- 总览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <text class="overview-title">综合能力评分</text>
          <view class="overall-score">
            <text class="score-value">
              {{ Math.round(Object.values(userAbilities).reduce((a, b) => a + b) / 6) }}
            </text>
            <text class="score-label">分</text>
          </view>
        </view>

        <!-- 雷达图 -->
        <view class="radar-container">
          <RadarChart :ability-data="radarChartData" :width="600" :height="400" />
        </view>
      </view>

      <!-- 能力详情列表 -->
      <view class="abilities-section">
        <text class="section-title">能力详细分析</text>

        <view v-for="ability in abilityDetails" :key="ability.key" class="ability-card">
          <!-- 能力头部 -->
          <view class="ability-header">
            <view class="ability-info">
              <view class="ability-icon">
                <text :class="[ability.icon]"></text>
              </view>
              <view class="ability-meta">
                <text class="ability-name">{{ ability.name }}</text>
                <text class="ability-desc">{{ ability.description }}</text>
              </view>
            </view>
            <view class="ability-score-info">
              <text class="score-number">{{ ability.score }}</text>
              <view class="score-level" :style="{ color: getAbilityLevel(ability.score).color }">
                {{ getAbilityLevel(ability.score).level }}
              </view>
              <view
                class="score-trend"
                :class="[
                  {
                    'trend-up': ability.trend === 'up',
                    'trend-down': ability.trend === 'down',
                    'trend-stable': ability.trend === 'stable',
                  },
                ]"
              >
                <text
                  class="trend-icon"
                  :class="[
                    {
                      'i-mdi-trending-up': ability.trend === 'up',
                      'i-mdi-trending-down': ability.trend === 'down',
                      'i-mdi-minus': ability.trend === 'stable',
                    },
                  ]"
                ></text>
                <text class="trend-value">{{ ability.trendValue }}</text>
              </view>
            </view>
          </view>

          <!-- 进度条 -->
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{
                width: ability.score + '%',
                backgroundColor: getAbilityLevel(ability.score).color,
              }"
            ></view>
          </view>

          <!-- 提升建议 -->
          <view class="suggestions">
            <text class="suggestions-title">提升建议</text>
            <view class="suggestion-list">
              <view
                v-for="(suggestion, index) in ability.suggestions"
                :key="index"
                class="suggestion-item"
              >
                <text class="suggestion-icon i-mdi-lightbulb"></text>
                <text class="suggestion-text">{{ suggestion }}</text>
              </view>
            </view>
          </view>

          <!-- 相关资源 -->
          <view class="resources">
            <text class="resources-title">推荐学习资源</text>
            <view class="resource-list">
              <view
                v-for="(resource, index) in ability.relatedResources"
                :key="index"
                class="resource-item"
                @click="goToResource(resource.link)"
              >
                <text
                  class="resource-icon"
                  :class="{
                    'i-mdi-book': resource.type === 'book',
                    'i-mdi-video': resource.type === 'video',
                    'i-mdi-school': resource.type === 'course',
                    'i-mdi-file-document': resource.type === 'article',
                  }"
                ></text>
                <text class="resource-name">{{ resource.name }}</text>
                <text class="resource-arrow i-mdi-chevron-right"></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.ability-assessment-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f7f9fc 0%, #eef2f7 100%);
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-height: 50vh;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 24rpx;
  border: 4rpx solid #f0f0f0;
  border-left-color: #00c9a7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.main-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
}

.overview-card {
  margin: 24rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.1),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 201, 167, 0.05);

  // 毛玻璃效果
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.overview-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.overall-score {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(79, 209, 199, 0.05) 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
}

.score-value {
  font-size: 56rpx;
  font-weight: 800;
  color: #00c9a7;
  line-height: 1;
}

.score-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.radar-container {
  width: 100%;
  min-height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fafbfc 0%, #f0f4f8 100%);
  border-radius: 16rpx;
  border: 1rpx solid #e8eef5;
}

.abilities-section {
  padding: 0 24rpx 32rpx 24rpx;
}

.section-title {
  display: block;
  margin-bottom: 24rpx;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.ability-card {
  padding: 32rpx;
  margin-bottom: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow:
    0 4rpx 24rpx rgba(0, 0, 0, 0.06),
    0 2rpx 6rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow:
      0 8rpx 32rpx rgba(0, 0, 0, 0.08),
      0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  }
}

.ability-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  gap: 16rpx;
}

.ability-info {
  display: flex;
  gap: 16rpx;
  flex: 1;
  min-width: 0;
}

.ability-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  font-size: 36rpx;
  color: #00c9a7;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.15) 0%, rgba(79, 209, 199, 0.1) 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  flex-shrink: 0;
}

.ability-meta {
  flex: 1;
  min-width: 0;
}

.ability-name {
  display: block;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.ability-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}

.ability-score-info {
  text-align: right;
  flex-shrink: 0;
  min-width: 120rpx;
}

.score-number {
  display: block;
  font-size: 48rpx;
  font-weight: 800;
  color: #00c9a7;
  line-height: 1;
}

.score-level {
  display: block;
  margin-top: 6rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.score-trend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4rpx;
  margin-top: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;

  &.trend-up {
    color: #4caf50;
    background: rgba(76, 175, 80, 0.1);
  }

  &.trend-down {
    color: #f44336;
    background: rgba(244, 67, 54, 0.1);
  }

  &.trend-stable {
    color: #999;
    background: rgba(153, 153, 153, 0.1);
  }
}

.trend-icon {
  font-size: 24rpx;
}

.trend-value {
  font-size: 20rpx;
  font-weight: 600;
}

.progress-bar {
  height: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 0%, #e8e8e8 100%);
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00c9a7 0%, #00b39a 100%);
  border-radius: 8rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.3);
}

.suggestions {
  margin-bottom: 32rpx;
}

.suggestions-title,
.resources-title {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 24rpx;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 24rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
    border-radius: 3rpx;
  }
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #fffbf0 0%, #fef7e8 100%);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 193, 7, 0.1);
}

.suggestion-icon {
  flex-shrink: 0;
  font-size: 28rpx;
  color: #ffc107;
  margin-top: 2rpx;
}

.suggestion-text {
  flex: 1;
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f8fffe 0%, #f0fdfc 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  transition: all 0.3s ease;
  gap: 16rpx;

  &:active {
    background: linear-gradient(135deg, #e8f5f3 0%, #d4f4f1 100%);
    transform: scale(0.98);
  }

  /* #ifdef H5 */
  &:hover {
    background: linear-gradient(135deg, #e8f5f3 0%, #d4f4f1 100%);
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.2);
  }
  /* #endif */
}

.resource-icon {
  flex-shrink: 0;
  font-size: 32rpx;
  color: #00c9a7;
}

.resource-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resource-arrow {
  flex-shrink: 0;
  font-size: 32rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.resource-item:active .resource-arrow {
  transform: translateX(4rpx);
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  padding-bottom: 32rpx;
}

// 响应式适配
/* #ifdef H5 */
@media screen and (max-width: 768px) {
  .ability-card {
    margin-bottom: 16rpx;
    padding: 24rpx;
  }

  .ability-header {
    flex-direction: column;
    gap: 16rpx;
  }

  .ability-score-info {
    text-align: left;
    width: 100%;
  }
}
/* #endif */

// 小程序端适配
/* #ifdef MP */
.ability-card {
  // 微信小程序兼容性处理
  display: block;
}

.ability-header {
  // 确保在小程序中正确显示
  width: 100%;
  box-sizing: border-box;
}
/* #endif */

// 暗黑模式支持
/* #ifdef H5 */
@media (prefers-color-scheme: dark) {
  .ability-assessment-page {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .overview-card,
  .ability-card {
    background: #2d2d2d;
    border-color: #404040;
    color: #e0e0e0;
  }

  .overview-title,
  .section-title,
  .ability-name {
    color: #e0e0e0;
  }

  .ability-desc,
  .suggestion-text {
    color: #b0b0b0;
  }

  .progress-bar {
    background: #404040;
  }

  .suggestion-item {
    background: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
  }

  .resource-item {
    background: rgba(0, 201, 167, 0.1);
    border-color: rgba(0, 201, 167, 0.2);
  }

  .resource-name {
    color: #e0e0e0;
  }
}
/* #endif */
</style>
