import { http } from '@/utils/http'

// 消息类型定义
export interface Message {
  id: string
  type: 'system' | 'interview' | 'study' | 'achievement'
  title: string
  content: string
  time: string
  isRead: boolean
  avatar?: string
  actionUrl?: string
}

// 演示数据
const demoMessages: Message[] = [
  {
    id: '1',
    type: 'system',
    title: '系统更新通知',
    content: '智能面试助手已更新至最新版本，新增AI语音评测功能，快来体验吧！',
    time: '2025-01-15 10:30',
    isRead: false,
  },
  {
    id: '2',
    type: 'interview',
    title: '面试结果已出',
    content: '您的前端工程师模拟面试已完成，总分85分，快来查看详细报告吧！',
    time: '2025-01-15 09:15',
    isRead: false,
    actionUrl: '/pages/interview/result?id=123',
  },
  {
    id: '3',
    type: 'study',
    title: '学习计划提醒',
    content: '今日学习任务：JavaScript高级特性练习，还有2个题目未完成。',
    time: '2025-01-15 08:00',
    isRead: true,
    actionUrl: '/pages/learning/practice',
  },
  {
    id: '4',
    type: 'achievement',
    title: '恭喜获得新成就',
    content: '连续学习7天！您已获得"坚持不懈"徽章，继续保持哦！',
    time: '2025-01-14 20:00',
    isRead: true,
  },
  {
    id: '5',
    type: 'system',
    title: '维护通知',
    content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用。',
    time: '2025-01-14 16:30',
    isRead: true,
  },
]

// 消息列表缓存
let messageCache: Message[] = []

/**
 * 获取消息列表
 * @param params 查询参数
 */
export const getMessages = async (params?: { type?: string }) => {
  try {
    const res = await http.get('/api/app/messages', { params })
    messageCache = res.data as Message[]
    return res.data
  } catch (error) {
    console.error('获取消息列表失败', error)
    // 调用失败时返回演示数据
    messageCache = [...demoMessages]
    return messageCache
  }
}

/**
 * 标记消息已读
 * @param messageId 消息ID
 */
export const markMessageAsRead = async (messageId: string) => {
  try {
    await http.put(`/api/app/messages/${messageId}/read`)
    return true
  } catch (error) {
    console.error('标记消息已读失败', error)
    // 调用失败时直接在本地更新缓存
    const message = messageCache.find(msg => msg.id === messageId)
    if (message) {
      message.isRead = true
    }
    return true
  }
}

/**
 * 标记消息未读
 * @param messageId 消息ID
 */
export const markMessageAsUnread = async (messageId: string) => {
  try {
    await http.put(`/api/app/messages/${messageId}/unread`)
    return true
  } catch (error) {
    console.error('标记消息未读失败', error)
    // 调用失败时直接在本地更新缓存
    const message = messageCache.find(msg => msg.id === messageId)
    if (message) {
      message.isRead = false
    }
    return true
  }
}

/**
 * 标记全部消息已读
 */
export const markAllMessagesAsRead = async () => {
  try {
    await http.put('/api/app/messages/read/all')
    return true
  } catch (error) {
    console.error('标记全部已读失败', error)
    // 调用失败时直接在本地更新缓存
    messageCache.forEach(msg => {
      msg.isRead = true
    })
    return true
  }
}

/**
 * 删除消息
 * @param messageId 消息ID
 */
export const deleteMessage = async (messageId: string) => {
  try {
    await http.delete(`/api/app/messages/${messageId}`)
    return true
  } catch (error) {
    console.error('删除消息失败', error)
    // 调用失败时直接在本地更新缓存
    const index = messageCache.findIndex(msg => msg.id === messageId)
    if (index > -1) {
      messageCache.splice(index, 1)
    }
    return true
  }
}

/**
 * 搜索消息
 * @param keyword 搜索关键词
 */
export const searchMessages = async (keyword: string) => {
  try {
    const res = await http.get('/api/app/messages/search', { params: { keyword } })
    return res.data as Message[]
  } catch (error) {
    console.error('搜索消息失败', error)
    // 调用失败时在本地过滤
    if (!keyword.trim()) return messageCache

    const lowercaseKeyword = keyword.toLowerCase().trim()
    return messageCache.filter(
      msg => 
        msg.title.toLowerCase().includes(lowercaseKeyword) || 
        msg.content.toLowerCase().includes(lowercaseKeyword)
    )
  }
} 