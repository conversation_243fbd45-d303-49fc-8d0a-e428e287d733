<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// 用户协议页面
// 展示用户服务协议的详细内容

// 协议更新日期
const lastUpdated = ref('2024-01-15')

// 协议内容
const agreementSections = ref([
  {
    title: '1. 服务条款的接受',
    content: `欢迎使用AI面试助手！本协议是您与我们之间关于使用AI面试助手服务的法律协议。当您使用我们的服务时，即表示您同意受本协议的约束。`,
  },
  {
    title: '2. 服务描述',
    content: `AI面试助手是一款基于人工智能技术的模拟面试评测平台，为用户提供：
• 多模态面试模拟（视频、语音、文本）
• 智能面试评测和反馈
• 个性化学习资源推荐
• 面试历史记录管理`,
  },
  {
    title: '3. 用户账户',
    content: `3.1 您需要创建账户才能使用我们的服务
3.2 您有责任保护账户信息的安全性
3.3 您对使用您账户进行的所有活动负责
3.4 我们有权在必要时暂停或终止您的账户`,
  },
  {
    title: '4. 用户行为规范',
    content: `在使用我们的服务时，您同意：
• 不进行任何违法或有害的活动
• 不上传或传播恶意内容
• 不干扰或破坏服务的正常运行
• 尊重其他用户的权利和隐私`,
  },
  {
    title: '5. 知识产权',
    content: `5.1 我们拥有服务中所有软件、技术和内容的知识产权
5.2 您保留对自己上传内容的权利
5.3 通过使用服务，您授予我们处理您内容的必要权限`,
  },
  {
    title: '6. 隐私保护',
    content: `我们非常重视您的隐私保护：
• 仅收集提供服务所必需的信息
• 不会将您的个人信息出售给第三方
• 采用行业标准的安全措施保护数据
• 详细信息请参阅我们的隐私政策`,
  },
  {
    title: '7. 服务变更和终止',
    content: `7.1 我们可能会更新或修改服务功能
7.2 我们会提前通知重大变更
7.3 您可以随时停止使用服务
7.4 我们保留在必要时终止服务的权利`,
  },
  {
    title: '8. 免责声明',
    content: `8.1 服务按"现状"提供，不提供任何形式的保证
8.2 我们不对因使用服务而产生的任何损失负责
8.3 我们不保证服务的连续性和可用性`,
  },
  {
    title: '9. 争议解决',
    content: `如发生争议，我们鼓励通过友好协商解决。如协商不成，应提交至有管辖权的人民法院解决。`,
  },
  {
    title: '10. 协议更新',
    content: `我们可能会不时更新本协议。更新后的协议将在平台上公布，继续使用服务即表示您接受更新后的协议。`,
  },
])
</script>

<template>
  <view class="agreement-page">
    <HeadBar title="用户协议" :show-back="true" :show-right-button="false" />

    <view class="content-area" style="margin-top: 80rpx">
      <!-- 协议标题 -->
      <view class="agreement-header">
        <text class="agreement-title">AI面试助手用户服务协议</text>
        <text class="last-updated">最后更新：{{ lastUpdated }}</text>
      </view>

      <!-- 协议内容 -->
      <view class="agreement-content">
        <view class="section" v-for="(section, index) in agreementSections" :key="index">
          <text class="section-title">{{ section.title }}</text>
          <text class="section-content">{{ section.content }}</text>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="contact-footer">
        <text class="contact-title">如有疑问，请联系我们：</text>
        <text class="contact-email">客服邮箱：<EMAIL></text>
        <text class="contact-phone">客服电话：************</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.agreement-page {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background: #f7f9fc;
}

// 头部导航
.nav-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 24rpx;
  color: #333;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.2s;

  &:active {
    background: #e0e0e0;
    transform: scale(0.9);
  }
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}

.placeholder {
  width: 60rpx;
}

// 内容区域
.content-area {
  padding: 40rpx 32rpx;
}

// 协议标题
.agreement-header {
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.agreement-title {
  display: block;
  margin-bottom: 16rpx;
  font-size: 38rpx;
  font-weight: bold;
  color: #222;
}

.last-updated {
  display: block;
  font-size: 26rpx;
  color: #00c9a7;
}

// 协议内容
.agreement-content {
  padding: 32rpx;
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.section {
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: block;
  padding-bottom: 12rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-content {
  display: block;
  font-size: 28rpx;
  line-height: 1.8;
  color: #555;
  white-space: pre-line;
}

// 联系信息
.contact-footer {
  padding: 32rpx;
  text-align: center;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
  border-radius: 24rpx;
}

.contact-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}

.contact-email {
  display: block;
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: #00c9a7;
}

.contact-phone {
  display: block;
  font-size: 26rpx;
  color: #00c9a7;
}
</style>
