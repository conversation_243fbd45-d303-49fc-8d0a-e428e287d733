/**
 * 认证页面工具函数
 * @description 为登录、注册、忘记密码页面提供通用的工具函数
 * <AUTHOR>
 */

/**
 * 表单验证规则
 */
export const ValidationRules = {
  // 手机号验证
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  
  // 邮箱验证
  email: {
    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    message: '请输入正确的邮箱地址'
  },
  
  // 密码验证
  password: {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    message: '密码至少8位，包含大小写字母和数字'
  },
  
  // 学号验证
  studentId: {
    pattern: /^\d{8,12}$/,
    message: '请输入正确的学号'
  },
  
  // 真实姓名验证
  realName: {
    pattern: /^[\u4e00-\u9fa5]{2,10}$/,
    message: '请输入2-10位中文姓名'
  },
  
  // 验证码验证
  verifyCode: {
    pattern: /^\d{4,6}$/,
    message: '请输入4-6位验证码'
  }
}

/**
 * 验证表单字段
 * @param value 要验证的值
 * @param rule 验证规则
 * @returns 验证结果
 */
export function validateField(value: string, rule: { pattern: RegExp; message: string }): {
  isValid: boolean
  message: string
} {
  if (!value || !value.trim()) {
    return {
      isValid: false,
      message: '此字段不能为空'
    }
  }
  
  const isValid = rule.pattern.test(value.trim())
  return {
    isValid,
    message: isValid ? '' : rule.message
  }
}

/**
 * 密码强度检测
 * @param password 密码
 * @returns 强度等级和描述
 */
export function checkPasswordStrength(password: string): {
  level: 'weak' | 'medium' | 'strong'
  score: number
  description: string
} {
  if (!password) {
    return { level: 'weak', score: 0, description: '请输入密码' }
  }
  
  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 25
  if (password.length >= 12) score += 25
  
  // 字符类型检查
  if (/[a-z]/.test(password)) score += 10
  if (/[A-Z]/.test(password)) score += 10
  if (/\d/.test(password)) score += 10
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 20
  
  let level: 'weak' | 'medium' | 'strong'
  let description: string
  
  if (score < 50) {
    level = 'weak'
    description = '密码强度较弱'
  } else if (score < 80) {
    level = 'medium'
    description = '密码强度中等'
  } else {
    level = 'strong'
    description = '密码强度较强'
  }
  
  return { level, score, description }
}

/**
 * 格式化倒计时显示
 * @param seconds 剩余秒数
 * @returns 格式化的时间字符串
 */
export function formatCountdown(seconds: number): string {
  if (seconds <= 0) return '获取验证码'
  return `${seconds}s后重新获取`
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func.apply(this, args)
    }
  }
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 存储管理
 */
export const Storage = {
  /**
   * 设置本地存储
   * @param key 键名
   * @param value 值
   * @param expire 过期时间（毫秒）
   */
  set(key: string, value: any, expire?: number): void {
    const data = {
      value,
      expire: expire ? Date.now() + expire : null
    }
    uni.setStorageSync(key, JSON.stringify(data))
  },
  
  /**
   * 获取本地存储
   * @param key 键名
   * @returns 存储的值
   */
  get(key: string): any {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return null
      
      const parsed = JSON.parse(data)
      
      // 检查是否过期
      if (parsed.expire && Date.now() > parsed.expire) {
        uni.removeStorageSync(key)
        return null
      }
      
      return parsed.value
    } catch (error) {
      console.error('Storage get error:', error)
      return null
    }
  },
  
  /**
   * 删除本地存储
   * @param key 键名
   */
  remove(key: string): void {
    uni.removeStorageSync(key)
  },
  
  /**
   * 清空本地存储
   */
  clear(): void {
    uni.clearStorageSync()
  }
}

/**
 * 动画工具
 */
export const AnimationUtils = {
  /**
   * 创建淡入动画
   * @param duration 动画时长
   * @returns 动画实例
   */
  createFadeIn(duration: number = 300) {
    return uni.createAnimation({
      duration,
      timingFunction: 'ease-out'
    }).opacity(1).step()
  },
  
  /**
   * 创建淡出动画
   * @param duration 动画时长
   * @returns 动画实例
   */
  createFadeOut(duration: number = 300) {
    return uni.createAnimation({
      duration,
      timingFunction: 'ease-in'
    }).opacity(0).step()
  },
  
  /**
   * 创建滑入动画
   * @param direction 滑入方向
   * @param duration 动画时长
   * @returns 动画实例
   */
  createSlideIn(direction: 'left' | 'right' | 'up' | 'down' = 'up', duration: number = 300) {
    const animation = uni.createAnimation({
      duration,
      timingFunction: 'ease-out'
    })
    
    switch (direction) {
      case 'left':
        return animation.translateX(0).step()
      case 'right':
        return animation.translateX(0).step()
      case 'up':
        return animation.translateY(0).step()
      case 'down':
        return animation.translateY(0).step()
      default:
        return animation.translateY(0).step()
    }
  }
}

/**
 * 设备信息工具
 */
export const DeviceUtils = {
  /**
   * 获取系统信息
   */
  getSystemInfo(): Promise<UniApp.GetSystemInfoResult> {
    return new Promise((resolve, reject) => {
      uni.getSystemInfo({
        success: resolve,
        fail: reject
      })
    })
  },
  
  /**
   * 判断是否为移动设备
   */
  isMobile(): boolean {
    const systemInfo = uni.getSystemInfoSync()
    return systemInfo.platform === 'android' || systemInfo.platform === 'ios'
  },
  
  /**
   * 获取状态栏高度
   */
  getStatusBarHeight(): number {
    const systemInfo = uni.getSystemInfoSync()
    return systemInfo.statusBarHeight || 0
  }
}
