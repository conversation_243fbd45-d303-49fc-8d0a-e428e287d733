<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120">
  <defs>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45a049;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e7d32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#81C784;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66BB6A;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="60" cy="60" r="55" fill="url(#primaryGradient)" filter="url(#shadow)"/>
  
  <!-- 主要图标组合 -->
  <g transform="translate(30, 25)">
    <!-- 书本基座 -->
    <rect x="10" y="35" width="40" height="25" rx="3" ry="3" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
    
    <!-- 书本页面 -->
    <rect x="10" y="35" width="40" height="4" fill="#f5f5f5"/>
    <line x1="15" y1="43" x2="45" y2="43" stroke="url(#accentGradient)" stroke-width="2" stroke-linecap="round"/>
    <line x1="15" y1="48" x2="40" y2="48" stroke="url(#accentGradient)" stroke-width="2" stroke-linecap="round"/>
    <line x1="15" y1="53" x2="42" y2="53" stroke="url(#accentGradient)" stroke-width="2" stroke-linecap="round"/>
    
    <!-- AI大脑图标 -->
    <ellipse cx="30" cy="20" rx="18" ry="12" fill="#ffffff" stroke="url(#primaryGradient)" stroke-width="2"/>
    
    <!-- 大脑纹理 -->
    <path d="M 20 15 Q 25 12 30 15 Q 35 12 40 15" stroke="url(#accentGradient)" stroke-width="1.5" fill="none"/>
    <path d="M 18 20 Q 23 17 30 20 Q 37 17 42 20" stroke="url(#accentGradient)" stroke-width="1.5" fill="none"/>
    <path d="M 20 25 Q 25 22 30 25 Q 35 22 40 25" stroke="url(#accentGradient)" stroke-width="1.5" fill="none"/>
    
    <!-- 问答对话框 -->
    <circle cx="45" cy="15" r="8" fill="url(#accentGradient)" stroke="#ffffff" stroke-width="2"/>
    <text x="45" y="19" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">?</text>
    
    <!-- 对话框尾巴 -->
    <path d="M 40 20 L 35 25 L 42 22 Z" fill="url(#accentGradient)"/>
  </g>
  
  <!-- 装饰元素 -->
  <g opacity="0.3">
    <circle cx="20" cy="25" r="3" fill="#ffffff"/>
    <circle cx="100" cy="30" r="2" fill="#ffffff"/>
    <circle cx="15" cy="90" r="2.5" fill="#ffffff"/>
    <circle cx="95" cy="85" r="2" fill="#ffffff"/>
  </g>
  
  <!-- 底部文字标识 -->
  <text x="60" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="600" fill="#ffffff" opacity="0.9">AI学习助手</text>
</svg>