<template>
  <view class="about-container">
    <!-- 状态栏 H5 -->
    <!-- #ifdef H5 -->
    <view class="status-bar gradient-bg flex justify-between items-center px-30 text-white">
      <text class="status-title">关于</text>
      <view class="flex items-center">
        <text class="i-fa-solid-info-circle mr-10"></text>
      </view>
    </view>
    <!-- #endif -->

    <view class="content-area bg-gray-50 relative">
      <!-- 背景装饰元素 -->
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>

      <view class="page-content">
        <!-- Logo/头像 -->
        <view class="logo-area">
          <view class="logo gradient-bg">
            <text class="i-fa-solid-user-graduate text-white"></text>
          </view>
          <text class="title">智能面试助手</text>
          <text class="subtitle">高校学生多模态模拟面试评测智能体</text>
        </view>

        <!-- 关于内容 -->
        <view class="about-content">
          <view class="about-block">
            <text class="block-title">项目简介</text>
            <text class="block-text">
              本项目致力于为高校学生提供多模态、智能化的模拟面试体验，支持语音、视频、表情等多维度评测，助力求职能力提升。
            </text>
          </view>
          <view class="about-block">
            <text class="block-title">开发团队</text>
            <view class="team-list">
              <view
                class="team-card animated-card"
                v-for="(member, idx) in 4"
                :key="idx"
                :style="{ animationDelay: 0.1 + idx * 0.08 + 's' }"
              >
                <view :class="['card-icon', cardIconBg[idx]]">
                  <text :class="cardIcon[idx]"></text>
                </view>
                <view class="card-info">
                  <text class="member-name">{{ teamData[idx].name }}</text>
                  <text class="member-role">{{ teamData[idx].role }}</text>
                  <text class="member-contact">邮箱：{{ teamData[idx].email }}</text>
                </view>
              </view>
            </view>
          </view>
          <view class="about-block">
            <text class="block-title">联系方式</text>
            <text class="block-text">
              邮箱：<EMAIL>
              <br />
              GitHub：https://github.com/SevenJL
            </text>
          </view>
          <view class="about-block">
            <text class="block-title">版本信息</text>
            <text class="block-text">当前版本：v1.0.0</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const teamData = [
  { name: '张三', role: '前端开发', email: '<EMAIL>' },
  { name: '李四', role: '后端开发', email: '<EMAIL>' },
  { name: '王五', role: '算法与AI', email: '<EMAIL>' },
  { name: '赵六', role: '产品与测试', email: '<EMAIL>' },
]
const cardIcon = [
  'i-fa-solid-laptop-code',
  'i-fa-solid-database',
  'i-fa-solid-robot',
  'i-fa-solid-user-check',
]
const cardIconBg = ['bg-fe', 'bg-be', 'bg-ai', 'bg-pm']
</script>

<style lang="scss" scoped>
// 复用 login.vue 的变量
$primary-color: #00c9a7;
$primary-dark: #00b39a;
$text-dark: #333333;
$text-light: #666666;
$text-lighter: #999999;
$border-color: #e0e0e0;
$bg-color: #f5f5f5;

.about-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}

.status-bar {
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  height: calc(88rpx + var(--status-bar-height, 0));
  padding: 0 30rpx;
  /* #ifdef MP */
  padding-top: var(--status-bar-height, 0);
  /* #endif */
}
.status-title {
  font-size: 32rpx;
  font-weight: 600;
}

.content-area {
  box-sizing: border-box;
  width: 100%;
  min-height: calc(100vh - 88rpx);
  /* #ifdef MP */
  min-height: calc(100vh - 88rpx - var(--status-bar-height, 0));
  /* #endif */
  padding: 40rpx 0;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.4;
}
.decoration-circle-1 {
  top: 160rpx;
  right: 60rpx;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, #e0f7f2 0%, #b5ece2 100%);
  animation: floating 3s ease-in-out infinite;
}
.decoration-circle-2 {
  bottom: 300rpx;
  left: 60rpx;
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #d1f5ee 0%, #a5e0d5 100%);
  animation: floating 4s ease-in-out infinite reverse;
}

.page-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100%;
  padding: 0 60rpx;
}

.logo-area {
  width: 100%;
  margin-bottom: 40rpx;
  text-align: center;
}
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);
}
.logo text {
  font-size: 60rpx;
}
.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: $text-dark;
}
.subtitle {
  display: block;
  font-size: 26rpx;
  line-height: 1.5;
  color: $text-light;
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  width: 100%;
  max-width: 600rpx;
  padding: 40rpx 30rpx;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(8rpx);
  border: 2rpx solid;
  border-radius: 24rpx;
  border-image: linear-gradient(135deg, #00c9a7 0%, #b5ece2 100%) 1;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.08);
  animation: fadeInUp 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}

.about-block {
  margin-bottom: 0;
}
.block-title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: $primary-dark;
  letter-spacing: 1rpx;
  transition: color 0.3s;
  animation: fadeIn 0.7s 0.1s backwards;
}
.block-text {
  display: block;
  font-size: 26rpx;
  line-height: 1.7;
  color: $text-light;
  word-break: break-all;
}
/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
}
/* 动画 */
@keyframes floating {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

.team-list {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
  align-items: center;
}
.team-card {
  display: flex;
  gap: 24rpx;
  align-items: center;
  width: 100%;
  max-width: 480rpx;
  padding: 24rpx 28rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.1);
  opacity: 1;
  transition:
    box-shadow 0.3s,
    transform 0.3s;
}
.animated-card {
  animation: fadeInUp 0.7s cubic-bezier(0.23, 1, 0.32, 1) both;
}
.team-card:hover {
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.18);
  transform: translateY(-6rpx) scale(1.03);
}
.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  font-size: 36rpx;
  color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s;
}
.bg-fe {
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
}
.bg-be {
  background: linear-gradient(135deg, #4e73df 0%, #375ab7 100%);
}
.bg-ai {
  background: linear-gradient(135deg, #f6c23e 0%, #e0a800 100%);
}
.bg-pm {
  background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
}
.card-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.member-name {
  font-size: 30rpx;
  font-weight: 600;
  color: $primary-dark;
}
.member-role {
  font-size: 26rpx;
  color: $primary-color;
}
.member-contact {
  font-size: 24rpx;
  color: $text-lighter;
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
