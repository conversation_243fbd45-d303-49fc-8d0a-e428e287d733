import '@/style/index.scss'
import 'virtual:uno.css'
// import 'font-awesome/css/font-awesome.min.css' // 注释掉，使用 UnoCSS 的 FontAwesome 图标
import { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import trackingPlugin, { trackingDirective } from './plugins/tracking'
import trackingUtils from './utils/trackingUtils'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(VueQueryPlugin)
  app.use(trackingPlugin)
  
  // 注册埋点指令
  app.directive('track', trackingDirective)

  // 全局错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('Vue Error:', err, info)
    
    // 发送错误埋点
    trackingUtils.track('ERROR', {
      error: err.message,
      stack: err.stack,
      info,
      url: window.location.href,
      userAgent: navigator.userAgent
    }, true) // 立即发送
  }
  
  // 配置埋点参数
  trackingUtils.setEnabled(import.meta.env.PROD) // 生产环境启用埋点
  trackingUtils.setBatchSize(5) // 设置批量大小
  trackingUtils.setFlushInterval(3000) // 3秒刷新一次

  return {
    app,
  }
}

