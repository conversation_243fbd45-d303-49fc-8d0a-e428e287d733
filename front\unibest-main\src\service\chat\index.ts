import { http } from '@/utils/http'

// AI聊天相关接口
export const chatApi = {
  // 发送消息给AI
  sendMessage: (data: {
    content: string
    attachments?: Array<{
      type: 'image' | 'file'
      url: string
      name: string
    }>
    sessionId?: string
  }) => {
    return http.post('/api/chat/send', data)
  },

  // 获取AI回复（流式响应）
  getAIResponse: (data: {
    messages: Array<{
      role: 'user' | 'assistant'
      content: string
    }>
    stream?: boolean
  }) => {
    return http.post('/api/chat/completion', data)
  },

  // 上传文件
  uploadFile: (filePath: string) => {
    // 使用配置中的baseURL，如果没有则使用默认值
    const baseURL = 'http://localhost:3000' // 根据实际情况修改
    return uni.uploadFile({
      url: baseURL + '/api/upload/file',
      filePath,
      name: 'file',
      header: {
        Authorization: uni.getStorageSync('token') || '',
      },
    })
  },

  // 语音转文字
  speechToText: (audioPath: string) => {
    // 使用配置中的baseURL，如果没有则使用默认值
    const baseURL = 'http://localhost:3000' // 根据实际情况修改
    return uni.uploadFile({
      url: baseURL + '/api/speech/to-text',
      filePath: audioPath,
      name: 'audio',
      header: {
        Authorization: uni.getStorageSync('token') || '',
      },
    })
  },

  // 获取聊天历史
  getChatHistory: (sessionId: string) => {
    return http.get('/api/chat/history', { sessionId })
  },

  // 删除聊天记录（使用POST方法模拟DELETE）
  deleteChatSession: (sessionId: string) => {
    return http.post(`/api/chat/session/${sessionId}/delete`)
  },
}
