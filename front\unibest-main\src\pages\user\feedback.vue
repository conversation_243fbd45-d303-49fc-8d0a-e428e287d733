<script setup lang="ts">
import { ref } from 'vue'
import HeadBar from '@/components/HeadBar.vue'
import { submitFeedback } from '@/service/feedback'

/**
 * @description 意见反馈页面
 * 用户可以提交各种类型的反馈意见
 */

// 反馈类型选项
const feedbackTypes = ['功能建议', '内容问题', '使用问题', '其他']

// 当前选择的反馈类型
const selectedType = ref(feedbackTypes[0])

// 反馈内容
const feedbackContent = ref('')

// 联系方式
const contactInfo = ref('')

// 是否提交中
const isSubmitting = ref(false)

// 错误信息
const errors = ref<Record<string, string>>({})

/**
 * @description 显示输入错误
 * @param field 字段名
 * @param message 错误消息
 */
const showInputError = (field: string, message: string) => {
  errors.value[field] = message
  // 3秒后自动清除错误
  setTimeout(() => {
    clearInputError(field)
  }, 3000)
}

/**
 * @description 清除输入错误
 * @param field 字段名
 */
const clearInputError = (field: string) => {
  if (errors.value[field]) {
    delete errors.value[field]
  }
}

/**
 * @description 验证表单
 * @returns 验证是否通过
 */
const validateForm = (): boolean => {
  // 清除之前的错误
  errors.value = {}

  let isValid = true

  // 验证反馈内容
  if (!feedbackContent.value.trim()) {
    showInputError('content', '请输入反馈内容')
    isValid = false
  } else if (feedbackContent.value.trim().length < 10) {
    showInputError('content', '反馈内容至少需要10个字符')
    isValid = false
  }

  // 验证联系方式格式（如果填写了的话）
  if (contactInfo.value.trim()) {
    const phoneRegex = /^1[3-9]\d{9}$/
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    if (!phoneRegex.test(contactInfo.value) && !emailRegex.test(contactInfo.value)) {
      showInputError('contact', '请输入正确的手机号或邮箱格式')
      isValid = false
    }
  }

  return isValid
}

/**
 * @description 提交反馈
 */
const submitFeedbackForm = async () => {
  // 表单验证
  if (!validateForm()) {
    return
  }

  // 设置提交状态
  isSubmitting.value = true

  try {
    // 调用API提交反馈
    const res = await submitFeedback({
      params: {
        type: selectedType.value,
        content: feedbackContent.value.trim(),
        contactInfo: contactInfo.value.trim() || undefined,
      },
    })

    if (res.code === 200) {
      uni.showToast({
        title: '提交成功，感谢您的反馈！',
        icon: 'success',
        duration: 2000,
      })

      // 重置表单
      feedbackContent.value = ''
      contactInfo.value = ''
      selectedType.value = feedbackTypes[0]
      errors.value = {}

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      // 显示后端返回的错误信息
      uni.showToast({
        title: res.message || '提交失败，请重试',
        icon: 'none',
        duration: 3000,
      })
    }
  } catch (error: any) {
    console.error('提交反馈失败:', error)

    // 处理网络错误或其他异常
    let errorMessage = '提交失败，请检查网络连接'
    if (error.data && error.data.message) {
      errorMessage = error.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000,
    })
  } finally {
    isSubmitting.value = false
  }
}

/**
 * @description 处理反馈类型选择
 * @param e 选择事件
 */
const onTypeChange = (e: any) => {
  selectedType.value = feedbackTypes[e.detail.value]
  clearInputError('type')
}

/**
 * @description 处理内容输入
 */
const onContentInput = () => {
  clearInputError('content')
}

/**
 * @description 处理联系方式输入
 */
const onContactInput = () => {
  clearInputError('contact')
}

/**
 * @description 查看反馈列表
 */
const viewFeedbackList = () => {
  uni.navigateTo({
    url: '/pages/user/feedback-list',
  })
}
</script>

<template>
  <view class="feedback-page">
    <HeadBar
      title="意见反馈"
      :show-back="true"
      :show-right-button="true"
      :right-text="'历史'"
      :right-icon="'i-fa-solid-history'"
      @right-click="viewFeedbackList"
    />

    <view class="content-area" style="margin-top: 40rpx">
      <view class="feedback-card">
        <!-- 反馈类型选择 -->
        <view class="form-item">
          <text class="form-label">反馈类型</text>
          <picker :range="feedbackTypes" @change="onTypeChange">
            <view class="picker-view">
              <text>{{ selectedType }}</text>
              <text class="i-fa-solid-chevron-down picker-icon"></text>
            </view>
          </picker>
          <text v-if="errors.type" class="error-text">{{ errors.type }}</text>
        </view>

        <!-- 反馈内容 -->
        <view class="form-item">
          <text class="form-label">
            反馈内容
            <text class="required">*</text>
          </text>
          <textarea
            v-model="feedbackContent"
            class="feedback-textarea"
            :class="{ error: errors.content }"
            placeholder="请详细描述您遇到的问题或建议，以便我们更好地改进产品&#10;&#10;例如：&#10;• 功能建议：希望增加XX功能&#10;• 内容问题：某个题目答案有误&#10;• 使用问题：在使用XX功能时遇到问题"
            maxlength="500"
            @input="onContentInput"
          />
          <view class="textarea-footer">
            <text v-if="errors.content" class="error-text">{{ errors.content }}</text>
            <text class="word-count">{{ feedbackContent.length }}/500</text>
          </view>
        </view>

        <!-- 联系方式 -->
        <view class="form-item">
          <text class="form-label">联系方式（选填）</text>
          <input
            v-model="contactInfo"
            class="form-input"
            :class="{ error: errors.contact }"
            placeholder="您的邮箱或手机号，方便我们联系您"
            @input="onContactInput"
          />
          <text v-if="errors.contact" class="error-text">{{ errors.contact }}</text>
        </view>

        <!-- 提交按钮 -->
        <button
          class="submit-btn"
          :class="{ loading: isSubmitting, disabled: isSubmitting }"
          @click="submitFeedbackForm"
          :disabled="isSubmitting"
        >
          <text v-if="isSubmitting" class="loading-icon i-fa-solid-spinner"></text>
          <text>{{ isSubmitting ? '提交中...' : '提交反馈' }}</text>
        </button>

        <!-- 隐私提示 -->
        <view class="privacy-tip">
          <text class="i-fa-solid-shield-alt privacy-icon"></text>
          <text>您的反馈将有助于我们改进产品，我们会保护您的隐私信息。感谢您的支持！</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.feedback-page {
  min-height: 100vh;
  background: #f7f9fc;
  padding-bottom: 40rpx;
}

// 内容区域
.content-area {
  padding: 40rpx 32rpx;
}

.feedback-card {
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 40rpx 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #222;
  margin-bottom: 16rpx;
  display: block;

  .required {
    color: #ff4757;
    margin-left: 4rpx;
  }
}

.picker-view {
  background: #f5f5f5;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;

  &:active {
    background: #e8e8e8;
  }

  .picker-icon {
    font-size: 22rpx;
    color: #999;
    font-weight: bold;
  }
}

.feedback-textarea {
  width: 100%;
  height: 240rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.6;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;

  &:focus {
    background: #fff;
    border-color: #00c9a7;
    box-shadow: 0 0 0 4rpx rgba(0, 201, 167, 0.1);
  }

  &.error {
    border-color: #ff4757;
    background: #fff5f5;
  }
}

.textarea-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

.word-count {
  font-size: 22rpx;
  color: #999;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;

  &:focus {
    background: #fff;
    border-color: #00c9a7;
    box-shadow: 0 0 0 4rpx rgba(0, 201, 167, 0.1);
  }

  &.error {
    border-color: #ff4757;
    background: #fff5f5;
  }
}

.error-text {
  font-size: 22rpx;
  color: #ff4757;
  margin-top: 8rpx;
  display: block;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  border-radius: 44rpx;
  margin-top: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:not(.disabled):hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.4);
  }

  &:not(.disabled):active {
    transform: translateY(0);
    box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.3);
  }

  &.loading {
    .loading-icon {
      animation: spin 1s linear infinite;
    }
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  // 按钮波纹效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active::before {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.privacy-tip {
  margin-top: 32rpx;
  font-size: 22rpx;
  color: #666;
  text-align: left;
  padding: 24rpx;
  background: rgba(0, 201, 167, 0.05);
  border-radius: 16rpx;
  border-left: 4rpx solid #00c9a7;
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  line-height: 1.6;

  .privacy-icon {
    color: #00c9a7;
    font-size: 28rpx;
    margin-top: 2rpx;
    flex-shrink: 0;
  }
}

.history-btn {
  margin-top: 24rpx;
  padding: 24rpx;
  background: rgba(108, 92, 231, 0.05);
  border-radius: 16rpx;
  border-left: 4rpx solid #6c5ce7;
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: rgba(108, 92, 231, 0.1);
    transform: translateY(1rpx);
  }

  .history-icon {
    color: #6c5ce7;
    font-size: 28rpx;
  }

  text {
    font-size: 24rpx;
    color: #666;
    flex: 1;
  }

  .arrow-icon {
    font-size: 16rpx;
    color: #ccc;
    margin-left: auto;
  }
}
</style>
