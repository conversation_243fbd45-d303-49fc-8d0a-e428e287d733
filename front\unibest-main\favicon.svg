<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45a049;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#2e7d32" stroke-width="2"/>
  
  <!-- 书本图标 -->
  <g transform="translate(16, 18)">
    <!-- 书本主体 -->
    <rect x="4" y="4" width="24" height="18" rx="2" ry="2" fill="url(#bookGradient)" stroke="#e0e0e0" stroke-width="1"/>
    
    <!-- 书本页面线条 -->
    <line x1="8" y1="8" x2="24" y2="8" stroke="#4CAF50" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="8" y1="12" x2="20" y2="12" stroke="#4CAF50" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="8" y1="16" x2="22" y2="16" stroke="#4CAF50" stroke-width="1.5" stroke-linecap="round"/>
    
    <!-- 问号图标 -->
    <circle cx="20" cy="28" r="6" fill="#4CAF50" stroke="#ffffff" stroke-width="2"/>
    <text x="20" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="white">?</text>
  </g>
  
  <!-- 装饰性元素 - 小星星 -->
  <circle cx="48" cy="16" r="2" fill="#ffffff" opacity="0.8"/>
  <circle cx="16" cy="48" r="1.5" fill="#ffffff" opacity="0.6"/>
  <circle cx="50" cy="50" r="1" fill="#ffffff" opacity="0.7"/>
</svg>