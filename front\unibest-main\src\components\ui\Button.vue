<template>
  <button :class="buttonClasses" :disabled="disabled || loading" @click="handleClick">
    <!-- 加载图标 -->
    <text v-if="loading" class="btn-loading-icon i-mdi-loading"></text>

    <!-- 左侧图标 -->
    <text v-if="leftIcon && !loading" :class="['btn-icon', 'btn-icon-left', leftIcon]"></text>

    <!-- 按钮文本 -->
    <text v-if="$slots.default" class="btn-text">
      <slot></slot>
    </text>

    <!-- 右侧图标 -->
    <text v-if="rightIcon && !loading" :class="['btn-icon', 'btn-icon-right', rightIcon]"></text>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 按钮类型
  type?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'text'
  // 按钮尺寸
  size?: 'small' | 'medium' | 'large'
  // 是否禁用
  disabled?: boolean
  // 是否加载中
  loading?: boolean
  // 是否块级按钮
  block?: boolean
  // 是否圆形按钮
  round?: boolean
  // 左侧图标
  leftIcon?: string
  // 右侧图标
  rightIcon?: string
  // 自定义类名
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  leftIcon: '',
  rightIcon: '',
  customClass: '',
})

const emit = defineEmits<{
  click: [event: Event]
}>()

// 计算按钮类名
const buttonClasses = computed(() => {
  const classes = [
    'ui-button',
    `ui-button--${props.type}`,
    `ui-button--${props.size}`,
    props.customClass,
  ]

  if (props.block) classes.push('ui-button--block')
  if (props.round) classes.push('ui-button--round')
  if (props.loading) classes.push('ui-button--loading')
  if (props.disabled) classes.push('ui-button--disabled')

  return classes.join(' ')
})

// 处理点击事件
const handleClick = (event: Event) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables';
@import '@/styles/mixins';

.ui-button {
  @include btn-base;
  position: relative;
  overflow: hidden;

  // 按钮类型样式
  &--primary {
    @include btn-primary;
  }

  &--secondary {
    @include btn-secondary;
  }

  &--danger {
    @include btn-danger;
  }

  &--ghost {
    color: $primary-color;
    background: transparent;
    border: 2rpx solid $primary-color;

    &:active {
      background: rgba($primary-color, 0.05);
    }
  }

  &--text {
    color: $primary-color;
    background: transparent;
    box-shadow: none;

    &:active {
      background: rgba($primary-color, 0.05);
    }
  }

  // 按钮尺寸样式
  &--small {
    height: $btn-height-sm;
    padding: $btn-padding-sm;
    font-size: $font-size-sm;
    border-radius: $radius-lg;
  }

  &--medium {
    height: $btn-height-base;
    padding: $btn-padding-base;
    font-size: $font-size-base;
  }

  &--large {
    height: $btn-height-lg;
    padding: $btn-padding-lg;
    font-size: $font-size-lg;
  }

  // 块级按钮
  &--block {
    width: 100%;
  }

  // 圆形按钮
  &--round {
    border-radius: $radius-full;
  }

  // 加载状态
  &--loading {
    pointer-events: none;

    .btn-loading-icon {
      animation: spin 1s linear infinite;
    }
  }

  // 禁用状态
  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:active {
      transform: none !important;
    }
  }
}

// 按钮图标
.btn-icon {
  font-size: 1.2em;

  &-left {
    margin-right: $spacing-xs;
  }

  &-right {
    margin-left: $spacing-xs;
  }
}

// 加载图标
.btn-loading-icon {
  margin-right: $spacing-xs;
  font-size: 1.2em;
}

// 按钮文本
.btn-text {
  flex: 1;
  text-align: center;
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 按钮波纹效果
.ui-button::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  content: '';
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: width 0.3s, height 0.3s;
  transform: 
   translate(-5
   0%, -50%);
}

.ui-button:active::after {
  width: 200%;
  height: 200%;
}

// 主题色按钮的特殊效果
.ui-button--primary {
  position: relative;

  &::before {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:active::before {
    left: 100%;
  }
}
</style>
