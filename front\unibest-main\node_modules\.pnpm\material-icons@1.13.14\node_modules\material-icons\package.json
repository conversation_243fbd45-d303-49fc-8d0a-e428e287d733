{"name": "material-icons", "version": "1.13.14", "description": "Latest icon fonts and CSS for self-hosting material design icons.", "browser": "iconfont/material-icons.css", "sass": "iconfont/material-icons.scss", "types": "index.d.ts", "files": ["index.d.ts", "_data/versions.json", "css/*.{css,scss}", "iconfont/*.{css,scss,woff,woff2}"], "scripts": {"check": "npm run download:metadata -- --status --dry-run", "update": "npm run download && npm run generate && npm run build", "download": "npm run download:font && npm run download:metadata", "download:font": "npx @material-design-icons/scripts download font --to iconfont", "download:metadata": "npx @material-design-icons/scripts download metadata", "generate": "npm run generate:types", "generate:types": "npx @material-design-icons/scripts generate types --in .", "build": "npm run build:codepoints && npm run build:css && npm run build:css:min", "build:codepoints": "node scripts/codepoints.js", "build:css": "sass --no-source-map --no-error-css css iconfont", "build:css:min": "sass --style compressed --no-source-map --no-error-css css/material-icons.scss:css/material-icons.min.css"}, "devDependencies": {"@material-design-icons/scripts": "0.5.1", "opentype.js": "1.3.4", "sass": "1.56.1"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/marella/material-icons.git"}, "bugs": {"url": "https://github.com/marella/material-icons/issues"}, "homepage": "https://marella.github.io/material-icons/demo/", "keywords": ["material-icons", "material-design-icons", "material-design", "material", "icons", "iconfont", "font", "css", "sass"]}