<script setup lang="ts">
import { ref, onMounted, watch, computed, defineProps, onUnmounted } from 'vue'
import uCharts from '@qiun/ucharts'

/**
 * @description 历史趋势分析组件
 * @prop trendData 趋势数据数组
 * @prop selectedAbilities 选中的能力维度
 * @prop width 宽度
 * @prop height 高度
 * <AUTHOR>
 */
interface TrendDataItem {
  date: string
  专业知识: number
  逻辑思维: number
  语言表达: number
  心理素质: number
  团队协作: number
}

interface Props {
  trendData: TrendDataItem[]
  selectedAbilities?: string[]
  width?: number | string
  height?: number | string
}

const props = defineProps<Props>()

// 生成唯一的canvas ID
const canvasId = ref(`trend-chart-${Date.now()}-${Math.floor(Math.random() * 1000)}`)

// uCharts 实例
let trendChart = null

// 计算实际的宽高（统一使用px单位）
const actualWidth = computed(() => {
  const w = props.width || 700
  if (typeof w === 'number') {
    return uni.upx2px(w)
  }
  const value = parseInt(w.toString())
  if (w.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || uni.upx2px(700)
})

const actualHeight = computed(() => {
  const h = props.height || 400
  if (typeof h === 'number') {
    return uni.upx2px(h)
  }
  const value = parseInt(h.toString())
  if (h.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || uni.upx2px(400)
})

// 能力维度配置
const abilities = [
  { key: '专业知识', color: '#00c9a7', name: '专业知识' },
  { key: '逻辑思维', color: '#4fd1c7', name: '逻辑思维' },
  { key: '语言表达', color: '#f59e0b', name: '语言表达' },
  { key: '心理素质', color: '#ef4444', name: '心理素质' },
  { key: '团队协作', color: '#8b5cf6', name: '团队协作' },
]

/**
 * @description 计算过滤后的能力数据
 */
const filteredAbilities = computed(() => {
  const selected = props.selectedAbilities || [
    '专业知识',
    '逻辑思维',
    '语言表达',
    '心理素质',
    '团队协作',
  ]
  return abilities.filter((ability) => selected.includes(ability.key))
})

/**
 * @description 获取当前组件实例
 */
function getCurrentInstance() {
  // #ifdef MP-WEIXIN
  return getCurrentPages()[getCurrentPages().length - 1]
  // #endif
}

/**
 * @description 绘制趋势图
 */
const drawTrendChart = () => {
  if (!props.trendData || props.trendData.length === 0) {
    console.warn('趋势数据为空，无法绘制')
    return
  }

  // 获取canvas绘图上下文
  const context = uni.createCanvasContext(canvasId.value, getCurrentInstance())
  if (!context) {
    console.error('无法创建Canvas上下文')
    return
  }

  // 准备x轴数据（日期）
  const categories = props.trendData.map((item) => {
    // 简化日期显示，只显示月-日
    return item.date.length > 8 ? item.date.substring(5) : item.date
  })

  // 准备系列数据
  const seriesData = filteredAbilities.value.map((ability) => {
    const data = props.trendData.map((item) => {
      const value = item[ability.key as keyof TrendDataItem] as number
      return typeof value === 'number' ? value : null
    })

    return {
      name: ability.name,
      data: data,
      color: ability.color,
      show: true,
      type: 'line',
      style: 'curve',
      pointShape: 'circle',
      pointSize: 5,
      lineWidth: 3,
    }
  })

  // 配置图表选项
  const chartOptions = {
    type: 'line',
    context: context,
    canvasId: canvasId.value,
    width: actualWidth.value,
    height: actualHeight.value,
    // #ifdef MP-WEIXIN
    $this: getCurrentInstance(),
    // #endif
    animation: true,
    fontSize: uni.upx2px(22),
    fontColor: '#334155',
    background: 'transparent',
    pixelRatio: 1,
    dataLabel: false,
    legend: {
      show: true,
      position: 'bottom',
      fontSize: uni.upx2px(20),
      fontColor: '#475569',
      itemGap: uni.upx2px(15),
      margin: uni.upx2px(10),
    },
    categories: categories,
    series: seriesData,
    xAxis: {
      type: 'grid',
      gridColor: 'rgba(71, 85, 105, 0.1)',
      gridType: 'dash',
      dashLength: 4,
      fontSize: uni.upx2px(20),
      fontColor: '#64748b',
      lineColor: 'rgba(71, 85, 105, 0.2)',
      calibration: true,
    },
    yAxis: {
      gridColor: 'rgba(71, 85, 105, 0.1)',
      gridType: 'dash',
      dashLength: 4,
      fontSize: uni.upx2px(20),
      fontColor: '#64748b',
      lineColor: 'rgba(71, 85, 105, 0.2)',
      min: 0,
      max: 100,
      splitNumber: 5,
      calibration: true,
      unit: '分',
    },
    extra: {
      line: {
        type: 'curve',
        width: 3,
        activeType: 'hollow',
        linearType: 'none',
        onShadow: true,
        shadowColor: 'rgba(0, 201, 167, 0.15)',
        shadowOffsetX: 0,
        shadowOffsetY: 3,
        shadowBlur: 8,
        dataLabel: false,
      },
      tooltip: {
        showBox: true,
        bgColor: 'rgba(51, 65, 85, 0.95)',
        bgOpacity: 0.95,
        fontColor: '#ffffff',
        fontSize: uni.upx2px(22),
        borderRadius: 8,
        borderColor: 'rgba(0, 201, 167, 0.3)',
        borderWidth: 1,
        padding: [8, 12],
      },
    },
  }

  // 如果已存在图表实例，先销毁
  if (trendChart) {
    trendChart = null
  }

  // 创建新的图表实例
  try {
    trendChart = new uCharts(chartOptions)
    console.log('uCharts趋势图创建成功')
  } catch (error) {
    console.error('创建uCharts趋势图失败:', error)
  }
}

/**
 * @description 计算趋势统计信息
 */
const trendStats = computed(() => {
  if (!props.trendData || props.trendData.length < 2) {
    return null
  }

  const latest = props.trendData[props.trendData.length - 1]
  const previous = props.trendData[props.trendData.length - 2]

  const stats = filteredAbilities.value.map((ability) => {
    const currentValue = latest[ability.key as keyof TrendDataItem] as number
    const previousValue = previous[ability.key as keyof TrendDataItem] as number
    const change = currentValue - previousValue

    return {
      name: ability.name,
      current: currentValue,
      previous: previousValue,
      change: change,
      changePercent: previousValue > 0 ? Math.round((change / previousValue) * 100) : 0,
      color: ability.color,
    }
  })

  return stats
})

/**
 * @description 计算整体趋势
 */
const overallTrend = computed(() => {
  if (!trendStats.value) {
    return { direction: 'stable', value: 0 }
  }

  const totalChange = trendStats.value.reduce((sum, stat) => sum + stat.change, 0)
  const avgChange = totalChange / trendStats.value.length

  if (avgChange > 2) {
    return { direction: 'up', value: Math.round(avgChange * 10) / 10 }
  } else if (avgChange < -2) {
    return { direction: 'down', value: Math.round(avgChange * 10) / 10 }
  } else {
    return { direction: 'stable', value: Math.round(avgChange * 10) / 10 }
  }
})

// 暴露更新方法
const updateChart = () => {
  if (props.trendData && props.trendData.length > 0) {
    drawTrendChart()
  }
}

// 监听数据变化
watch(
  () => [props.trendData, props.selectedAbilities],
  () => {
    setTimeout(() => {
      drawTrendChart()
    }, 100)
  },
  { deep: true },
)

// 组件挂载后初始化
onMounted(() => {
  console.log('TrendChart组件已挂载')
  setTimeout(() => {
    if (props.trendData && props.trendData.length > 0) {
      drawTrendChart()
    }
  }, 300)
})

// 组件卸载时清理
onUnmounted(() => {
  if (trendChart) {
    trendChart = null
  }
})

// 导出方法供父组件调用
defineExpose({
  updateChart,
})
</script>

<template>
  <view class="trend-chart">
    <view v-if="trendData.length > 0" class="chart-container">
      <!-- 整体趋势指示器 -->
      <view class="trend-indicator" v-if="overallTrend">
        <view class="indicator-card" :class="overallTrend.direction">
          <view class="indicator-icon">
            <text
              :class="{
                'i-fa-solid-arrow-up': overallTrend.direction === 'up',
                'i-fa-solid-arrow-down': overallTrend.direction === 'down',
                'i-fa-solid-minus': overallTrend.direction === 'stable',
              }"
              class="icon-text"
            ></text>
          </view>
          <view class="indicator-content">
            <text class="indicator-title">整体趋势</text>
            <text class="indicator-value">
              {{
                overallTrend.direction === 'up'
                  ? '上升'
                  : overallTrend.direction === 'down'
                    ? '下降'
                    : '稳定'
              }}
              <text class="value-number" v-if="overallTrend.value !== 0">
                {{ overallTrend.value > 0 ? '+' : '' }}{{ overallTrend.value }}分
              </text>
            </text>
          </view>
        </view>
      </view>

      <!-- 图表画布 -->
      <view class="canvas-wrapper">
        <canvas
          :canvas-id="canvasId"
          :id="canvasId"
          :style="{
            width: actualWidth + 'px',
            height: actualHeight + 'px',
          }"
          class="trend-chart-canvas"
          disable-scroll="true"
        />
      </view>

      <!-- 详细趋势统计 -->
      <view class="trend-statistics" v-if="trendStats">
        <view class="stats-header">
          <text class="stats-title">能力变化详情</text>
          <text class="stats-subtitle">与上次对比</text>
        </view>

        <view class="stats-grid">
          <view class="stat-item" v-for="stat in trendStats" :key="stat.name">
            <view class="stat-header">
              <view class="stat-color" :style="{ backgroundColor: stat.color }"></view>
              <text class="stat-name">{{ stat.name }}</text>
            </view>

            <view class="stat-values">
              <view class="current-value">
                <text class="value-number">{{ stat.current }}</text>
                <text class="value-unit">分</text>
              </view>

              <view
                class="change-indicator"
                :class="{
                  positive: stat.change > 0,
                  negative: stat.change < 0,
                  neutral: stat.change === 0,
                }"
              >
                <text
                  :class="{
                    'i-fa-solid-arrow-up': stat.change > 0,
                    'i-fa-solid-arrow-down': stat.change < 0,
                    'i-fa-solid-minus': stat.change === 0,
                  }"
                  class="change-icon"
                ></text>
                <text class="change-value">{{ stat.change > 0 ? '+' : '' }}{{ stat.change }}</text>
                <text class="change-percent" v-if="stat.changePercent !== 0">
                  ({{ stat.changePercent > 0 ? '+' : '' }}{{ stat.changePercent }}%)
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <view class="empty-icon-wrapper">
        <text class="empty-icon i-fa-solid-chart-line"></text>
        <view class="empty-decoration"></view>
      </view>
      <text class="empty-title">暂无趋势数据</text>
      <text class="empty-desc">需要至少2次面试记录才能分析趋势</text>
      <view class="empty-action">
        <view class="start-interview-btn">
          <text class="start-text">开始面试</text>
          <text class="i-fa-solid-arrow-right start-icon"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.trend-chart {
  width: 100%;
  min-height: 400rpx;
  position: relative;
}

// 图表容器
.chart-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

// 整体趋势指示器
.trend-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 8rpx;
}

.indicator-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  border-radius: 24rpx;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  transition: all 0.3s;

  &.up {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.15), rgba(79, 209, 199, 0.1));
    border: 1rpx solid rgba(0, 201, 167, 0.3);

    .indicator-icon {
      background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    }

    .indicator-value {
      color: #00c9a7;
    }
  }

  &.down {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.1));
    border: 1rpx solid rgba(239, 68, 68, 0.3);

    .indicator-icon {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .indicator-value {
      color: #ef4444;
    }
  }

  &.stable {
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.15), rgba(100, 116, 139, 0.1));
    border: 1rpx solid rgba(148, 163, 184, 0.3);

    .indicator-icon {
      background: linear-gradient(135deg, #94a3b8, #64748b);
    }

    .indicator-value {
      color: #64748b;
    }
  }
}

.indicator-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 20rpx;
  color: #ffffff;
}

.indicator-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.indicator-title {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.indicator-value {
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1;
}

.value-number {
  font-size: 24rpx;
  margin-left: 8rpx;
}

// 画布包装器
.canvas-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.trend-chart-canvas {
  display: block;
  border-radius: 16rpx;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 趋势统计
.trend-statistics {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(248, 250, 252, 0.7) 100%);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.08);
}

.stats-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.1);
}

.stats-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #334155;
  margin-bottom: 4rpx;
  display: block;
}

.stats-subtitle {
  font-size: 22rpx;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 16rpx;
}

.stat-item {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.95);
  }
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.stat-color {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.stat-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #334155;
}

.stat-values {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.current-value {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.value-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #00c9a7;
  line-height: 1;
}

.value-unit {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.change-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(148, 163, 184, 0.1);

  &.positive {
    background: rgba(0, 201, 167, 0.1);

    .change-icon,
    .change-value,
    .change-percent {
      color: #00c9a7;
    }
  }

  &.negative {
    background: rgba(239, 68, 68, 0.1);

    .change-icon,
    .change-value,
    .change-percent {
      color: #ef4444;
    }
  }

  &.neutral {
    .change-icon,
    .change-value {
      color: #64748b;
    }
  }
}

.change-icon {
  font-size: 16rpx;
}

.change-value {
  font-size: 20rpx;
  font-weight: 600;
}

.change-percent {
  font-size: 18rpx;
  opacity: 0.8;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(248, 250, 252, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 80rpx;
    height: 80rpx;
    background: radial-gradient(circle, rgba(0, 201, 167, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 4s ease-in-out infinite;
  }
}

.empty-icon-wrapper {
  position: relative;
  margin-bottom: 32rpx;
}

.empty-icon {
  font-size: 64rpx;
  color: #cbd5e1;
  display: block;
}

.empty-decoration {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #475569;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.empty-desc {
  font-size: 24rpx;
  line-height: 1.6;
  color: #64748b;
  margin-bottom: 32rpx;
  max-width: 400rpx;
}

.empty-action {
  margin-top: 16rpx;
}

.start-interview-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 28rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
  }
}

.start-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.start-icon {
  font-size: 20rpx;
  color: #ffffff;
}

// 动画定义
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-12rpx) rotate(3deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .stat-item {
    padding: 16rpx;
  }

  .stat-values {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .indicator-card {
    padding: 16rpx 24rpx;
    gap: 12rpx;
  }

  .indicator-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .icon-text {
    font-size: 18rpx;
  }

  .indicator-value {
    font-size: 24rpx;
  }

  .value-number {
    font-size: 20rpx;
  }

  .empty-state {
    min-height: 320rpx;
    padding: 32rpx 24rpx;
  }

  .empty-icon {
    font-size: 56rpx;
  }

  .empty-title {
    font-size: 28rpx;
  }

  .empty-desc {
    font-size: 22rpx;
  }

  .start-text {
    font-size: 24rpx;
  }
}

// H5端优化
/* #ifdef H5 */
.stat-item {
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.1);
  }
}

.start-interview-btn {
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.3);
  }
}
/* #endif */

// 小程序端优化
/* #ifdef MP */
.canvas-wrapper {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}

.trend-statistics {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}

.empty-state {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}
/* #endif */

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .canvas-wrapper {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
    border-color: rgba(0, 201, 167, 0.2);
  }

  .trend-statistics {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(15, 23, 42, 0.7) 100%);
    border-color: rgba(0, 201, 167, 0.15);
  }

  .stat-item {
    background: rgba(30, 41, 59, 0.8);
  }

  .stats-title,
  .stat-name {
    color: #e2e8f0;
  }

  .stats-subtitle,
  .value-unit {
    color: #94a3b8;
  }

  .indicator-title {
    color: #94a3b8;
  }

  .empty-state {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5) 0%, rgba(15, 23, 42, 0.6) 100%);
    border-color: rgba(0, 201, 167, 0.15);
  }

  .empty-title {
    color: #e2e8f0;
  }

  .empty-desc {
    color: #94a3b8;
  }

  .empty-icon {
    color: #475569;
  }
}
</style>
