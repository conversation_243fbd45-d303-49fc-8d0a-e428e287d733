<script setup lang="ts">
import { ref, onMounted, shallowRef, computed } from 'vue'
import AbilityRadarChart from '@/components/AbilityRadarChart.vue'
import type { InitialAbilityAssessment, UserGrowthProfile } from '@/types/onboarding'
import { getUserGrowthProfile, getDetailedAbilityReport } from '@/service/assessment'

// 页面状态
const isLoading = ref(true)
const loadingFailed = ref(false)

// 使用shallowRef提高性能
const assessmentResult = shallowRef<InitialAbilityAssessment | null>(null)
const userProfile = shallowRef<UserGrowthProfile | null>(null)

// 默认的评估结果数据
const defaultAssessmentResult: InitialAbilityAssessment = {
  professionalKnowledge: 78,
  logicalThinking: 82,
  languageExpression: 75,
  stressResistance: 68,
  teamCollaboration: 85,
  innovation: 72,
  overallScore: 76
}

// 默认的用户成长档案
const defaultUserProfile: UserGrowthProfile = {
  userId: 'user_demo',
  currentStage: 'intermediate',
  joinDate: new Date().toISOString(),
  lastActiveDate: new Date().toISOString(),
  totalInterviews: 3,
  initialAssessment: defaultAssessmentResult,
  currentAssessment: defaultAssessmentResult,
  improvementRate: 15,
  targetPosition: '前端工程师',
  learningGoals: ['掌握React高级特性', '提升算法能力', '优化表达能力'],
  achievements: ['完成初次评估', '完成3次模拟面试'],
  continuousLearningDays: 5,
  completedCourses: 2
}

// 雷达图数据
const radarData = computed(() => {
  if (!assessmentResult.value) return []
  
  return [
    { name: '专业知识', value: assessmentResult.value.professionalKnowledge },
    { name: '逻辑思维', value: assessmentResult.value.logicalThinking },
    { name: '语言表达', value: assessmentResult.value.languageExpression },
    { name: '抗压能力', value: assessmentResult.value.stressResistance },
    { name: '团队协作', value: assessmentResult.value.teamCollaboration },
    { name: '创新能力', value: assessmentResult.value.innovation },
  ]
})

// 阶段描述
const stageDescriptions = {
  new_user: '你刚刚开始职场之旅，有很大的成长空间。',
  beginner: '你已初步掌握了面试基础，但需要更多实践经验。',
  intermediate: '你具备一定的面试能力，可以应对常规面试情景。',
  advanced: '你在大多数面试情境中表现出色，是一名优秀的面试者。',
  expert: '你是面试的专家，几乎能应对任何面试挑战。',
}

// 能力描述
const abilityDescriptions = {
  professionalKnowledge: {
    low: '建议系统学习相关技术知识，夯实专业基础。',
    medium: '你已掌握基本专业知识，可以进一步深入学习高级概念。',
    high: '你的专业知识储备丰富，可以自信地应对技术面试。',
  },
  logicalThinking: {
    low: '建议通过算法题和系统设计练习提升逻辑思维能力。',
    medium: '你的逻辑思维基础良好，可以尝试更复杂的问题求解。',
    high: '你的逻辑思维能力出色，可以应对大多数复杂问题。',
  },
  languageExpression: {
    low: '建议练习表达技巧，学习结构化回答问题的方法。',
    medium: '你的表达基本清晰，可以通过模拟面试进一步提升。',
    high: '你的语言表达能力出色，可以清晰、有说服力地传达想法。',
  },
  stressResistance: {
    low: '建议通过模拟高压面试场景来提升应对能力。',
    medium: '你在压力下能保持基本表现，可以通过更多实战经验提升。',
    high: '你在高压环境下依然能保持出色表现，这是很宝贵的能力。',
  },
  teamCollaboration: {
    low: '建议关注团队协作相关的案例和问题，提升协作意识。',
    medium: '你具备基本的团队协作意识，可以进一步提升冲突处理能力。',
    high: '你展现出优秀的团队协作精神，是团队中的桥梁角色。',
  },
  innovation: {
    low: '建议培养创新思维，尝试从不同角度思考问题。',
    medium: '你有一定的创新意识，可以进一步培养创造性解决问题的能力。',
    high: '你的创新能力出色，能够提出独特的解决方案。',
  },
}

// 页面加载
onMounted(async () => {
  await loadAssessmentResult()
})

/**
 * @description 加载评估结果
 */
async function loadAssessmentResult() {
  isLoading.value = true
  loadingFailed.value = false

  try {
    // 尝试从API获取用户成长档案
    const res = await getUserGrowthProfile()
    
    if (res && res.code === 200 && res.data) {
      // 使用API返回的数据
      userProfile.value = res.data
      assessmentResult.value = res.data.initialAssessment
      console.log('成功从API获取评估结果')
      
      // 存储到本地
      uni.setStorageSync('initialAssessmentResult', JSON.stringify(res.data.initialAssessment))
      uni.setStorageSync('userGrowthProfile', JSON.stringify(res.data))
    } else {
      console.log('API返回无效数据，尝试从本地存储加载')
      // 如果API返回成功但没有数据，从本地存储加载
      loadFromLocalStorage()
    }
  } catch (error) {
    console.error('加载评估结果失败:', error)
    loadingFailed.value = true
    // 尝试从本地存储加载
    loadFromLocalStorage()
  } finally {
    // 添加300ms延迟，提升用户体验
    setTimeout(() => {
      isLoading.value = false
    }, 300)
  }
}

/**
 * @description 从本地存储加载数据
 */
function loadFromLocalStorage() {
  try {
    const resultStr = uni.getStorageSync('initialAssessmentResult')
    const profileStr = uni.getStorageSync('userGrowthProfile')
    
    if (resultStr) {
      assessmentResult.value = JSON.parse(resultStr)
      console.log('从本地存储加载评估结果成功')
    } else {
      console.log('本地存储无评估结果，使用默认数据')
      assessmentResult.value = defaultAssessmentResult
    }
    
    if (profileStr) {
      userProfile.value = JSON.parse(profileStr)
      console.log('从本地存储加载用户档案成功')
    } else {
      console.log('本地存储无用户档案，使用默认数据')
      userProfile.value = defaultUserProfile
    }
    
    // 重置加载失败状态，因为我们有默认数据可以显示
    loadingFailed.value = false
  } catch (e) {
    console.error('解析本地存储数据失败:', e)
    console.log('使用默认演示数据')
    // 使用默认数据
    assessmentResult.value = defaultAssessmentResult
    userProfile.value = defaultUserProfile
    loadingFailed.value = false
  }
}

/**
 * @description 获取能力评级
 * @param value 能力值
 * @returns 评级描述
 */
function getAbilityLevel(value: number): string {
  if (value >= 80) return 'high'
  if (value >= 60) return 'medium'
  return 'low'
}

/**
 * @description 获取能力描述
 * @param category 能力类别
 * @returns 能力描述
 */
function getAbilityDescription(category: keyof typeof abilityDescriptions): string {
  if (!assessmentResult.value) return ''
  const value = assessmentResult.value[category]
  const level = getAbilityLevel(value)
  return abilityDescriptions[category][level]
}

/**
 * @description 重试加载
 */
function retryLoading() {
  loadAssessmentResult()
}

/**
 * @description 返回主页
 */
function goToHome() {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

/**
 * @description 查看推荐学习资源
 */
function viewRecommendations() {
  uni.navigateTo({
    url: '/pages/learning/index'
  })
}
</script>

<template>
  <view class="assessment-result-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">能力评估报告</text>
        <text class="header-subtitle">了解你的优势与提升空间</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner">
        <view class="spinner"></view>
      </view>
      <text class="loading-text">正在生成评估报告...</text>
    </view>

    <!-- 加载失败 -->
    <view v-else-if="loadingFailed" class="loading-failed">
      <text class="i-mdi-alert-circle error-icon"></text>
      <text class="error-text">加载评估结果失败</text>
      <text class="error-description">请检查网络连接后重试</text>
      <button class="retry-button" @click="retryLoading">重新加载</button>
    </view>

    <!-- 评估结果内容 -->
    <view v-else-if="assessmentResult" class="result-container">
      <!-- 总分卡片 -->
      <view class="result-card total-score-card">
        <view class="score-circle">
          <text class="score-number">{{ assessmentResult.overallScore }}</text>
          <text class="score-label">总分</text>
        </view>
        <view class="score-details">
          <text class="stage-title">{{ userProfile?.currentStage ? userProfile.currentStage.replace('_', ' ') : 'new user' }}</text>
          <text class="stage-description">
            {{ stageDescriptions[userProfile?.currentStage || 'new_user'] }}
          </text>
        </view>
      </view>

      <!-- 能力雷达图 -->
      <view class="result-card radar-card">
        <text class="card-title">能力雷达图</text>
        <view class="radar-container">
          <AbilityRadarChart :data="radarData" />
        </view>
      </view>

      <!-- 能力详情卡片 -->
      <view class="result-card ability-details-card">
        <text class="card-title">能力详细分析</text>
        
        <view class="ability-item" v-for="(item, index) in radarData" :key="index">
          <view class="ability-header">
            <text class="ability-name">{{ item.name }}</text>
            <text class="ability-score">{{ item.value }}分</text>
          </view>
          <view class="ability-bar">
            <view class="ability-bar-fill" :style="{ width: item.value + '%' }"></view>
          </view>
          <text class="ability-description">
            {{ getAbilityDescription(Object.keys(abilityDescriptions)[index]) }}
          </text>
        </view>
      </view>

      <!-- 总结与建议 -->
      <view class="result-card summary-card">
        <text class="card-title">总结与建议</text>
        <text class="summary-text">
          根据你的能力评估结果，我们为你量身定制了学习计划和面试建议。你可以在学习资源页面查看推荐的内容，并通过模拟面试进一步提升你的面试表现。
        </text>
        <view class="tag-container">
          <text class="tag" v-if="assessmentResult.professionalKnowledge < 70">专业知识提升</text>
          <text class="tag" v-if="assessmentResult.languageExpression < 70">表达能力训练</text>
          <text class="tag" v-if="assessmentResult.logicalThinking < 70">逻辑思维训练</text>
          <text class="tag" v-if="assessmentResult.stressResistance < 70">抗压能力提升</text>
          <text class="tag" v-if="assessmentResult.teamCollaboration < 70">团队协作技巧</text>
          <text class="tag" v-if="assessmentResult.innovation < 70">创新思维培养</text>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="action-buttons">
        <button class="btn-secondary" @click="goToHome">返回首页</button>
        <button class="btn-primary" @click="viewRecommendations">查看推荐学习资源</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.assessment-result-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  will-change: transform;
  contain: layout style paint;
}

/* 顶部导航样式 */
.header {
  background: #fff;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.header-content {
  text-align: center;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 8rpx;
}

.header-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-spinner {
  margin-bottom: 20rpx;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 201, 167, 0.2);
  border-top-color: #00c9a7;
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 加载失败 */
.loading-failed {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
  color: #ff5252;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.error-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-button {
  background: #00c9a7;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 结果容器 */
.result-container {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding-bottom: 120rpx;
}

.result-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.5s ease-out;
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.card-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8rpx;
    height: 28rpx;
    background: linear-gradient(to bottom, #00c9a7, #00b39a);
    border-radius: 4rpx;
  }
}

/* 总分卡片 */
.total-score-card {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00c9a7, #00b39a);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.score-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
}

.score-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
}

.score-details {
  flex: 1;
}

.stage-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  text-transform: capitalize;
}

.stage-description {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 雷达图卡片 */
.radar-card {
  padding: 32rpx 20rpx 20rpx;
}

.radar-container {
  height: 500rpx;
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

/* 能力详情卡片 */
.ability-details-card {
  padding-bottom: 20rpx;
}

.ability-item {
  margin-bottom: 28rpx;
}

.ability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.ability-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.ability-score {
  font-size: 28rpx;
  font-weight: bold;
  color: #00c9a7;
}

.ability-bar {
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.ability-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #00c9a7, #00b39a);
  border-radius: 6rpx;
  transition: width 1s ease-out;
}

.ability-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 总结卡片 */
.summary-text {
  font-size: 26rpx;
  color: #444;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  display: inline-block;
  padding: 10rpx 20rpx;
  background: rgba(0, 201, 167, 0.1);
  color: #00c9a7;
  border-radius: 100rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 底部按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 10;
}

.btn-primary {
  flex: 2;
  height: 88rpx;
  background: linear-gradient(135deg, #00c9a7, #00b39a);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  transform: translateZ(0);

  &:active {
    transform: scale(0.98) translateZ(0);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
  }
}

.btn-secondary {
  flex: 1;
  height: 88rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transform: translateZ(0);

  &:active {
    background: #e0e0e0;
  }
}

/* 动画 */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx) translateZ(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
