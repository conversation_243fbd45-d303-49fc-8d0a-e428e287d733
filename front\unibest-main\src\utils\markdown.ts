/**
 * @description 轻量级Markdown解析工具
 * 使用 MarkdownIt 和 highlight.js 实现真正的 Markdown 渲染
 */

import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// Markdown渲染器初始化
const md = new MarkdownIt({
  html: true, // 允许HTML标签
  linkify: true, // 自动识别链接
  typographer: true, // 启用一些语言中性的替换和引号美化
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return (
          '<pre class="hljs"><code>' +
          hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
          '</code></pre>'
        )
      } catch (__) {}
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  },
})

/**
 * @description 将markdown文本转换为HTML
 * 使用 MarkdownIt 进行真正的 Markdown 渲染
 * @param markdown markdown文本
 * @returns 渲染后的HTML字符串
 */
export function markdownToHtml(markdown: string): string {
  if (!markdown || typeof markdown !== 'string') {
    return ''
  }

  try {
    return md.render(markdown)
  } catch (error) {
    console.error('Markdown 渲染失败:', error)
    return markdown // 如果渲染失败，返回原始文本
  }
}

/**
 * @description 将markdown文本转换为纯文本显示
 * 移除markdown语法标记，保留核心内容
 * @param markdown markdown文本
 * @returns 纯文本内容
 */
export function markdownToPlainText(markdown: string): string {
  if (!markdown || typeof markdown !== 'string') {
    return ''
  }

  let text = markdown

  // 移除代码块
  text = text.replace(/```[\s\S]*?```/g, '[代码块]')
  text = text.replace(/`([^`]+)`/g, '$1')

  // 移除标题标记
  text = text.replace(/^#{1,6}\s+/gm, '')

  // 移除粗体斜体标记
  text = text.replace(/\*\*([^*]+)\*\*/g, '$1')
  text = text.replace(/\*([^*]+)\*/g, '$1')

  // 移除链接标记
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')

  // 移除引用标记
  text = text.replace(/^>\s+/gm, '')

  // 移除列表标记
  text = text.replace(/^[-*+]\s+/gm, '• ')
  text = text.replace(/^\d+\.\s+/gm, '')

  // 移除表格标记
  text = text.replace(/\|/g, ' ')
  text = text.replace(/^[-:|\s]+$/gm, '')

  // 清理多余的空行
  text = text.replace(/\n{3,}/g, '\n\n')
  
  // 清理首尾空白
  text = text.trim()

  return text
}

/**
 * @description 提取markdown文本的纯文本摘要
 * @param markdown markdown文本
 * @param maxLength 最大长度
 * @returns 文本摘要
 */
export function extractTextSummary(markdown: string, maxLength: number = 100): string {
  const plainText = markdownToPlainText(markdown)
  
  if (plainText.length <= maxLength) {
    return plainText
  }
  
  return plainText.substring(0, maxLength).trim() + '...'
}

/**
 * @description 检测文本是否包含markdown语法
 * @param text 要检测的文本
 * @returns 是否包含markdown语法
 */
export function hasMarkdownSyntax(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false
  }

  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /\*\*.*?\*\*/,           // 粗体
    /\*.*?\*/,               // 斜体
    /```[\s\S]*?```/,        // 代码块
    /`.*?`/,                 // 行内代码
    /\[.*?\]\(.*?\)/,        // 链接
    /^[-*+]\s+/m,            // 列表
    /^\d+\.\s+/m,            // 有序列表
    /^>\s+/m,                // 引用
    /\|.*?\|/,               // 表格
  ]

  return markdownPatterns.some(pattern => pattern.test(text))
}

/**
 * @description 分段处理长文本，提升渲染性能
 * @param text 长文本
 * @param maxParagraphLength 每段最大长度
 * @returns 分段后的文本数组
 */
export function splitTextIntoParagraphs(text: string, maxParagraphLength: number = 200): string[] {
  if (!text || text.length <= maxParagraphLength) {
    return [text]
  }

  const paragraphs: string[] = []
  const sentences = text.split(/[。！？.!?]\s*/)
  
  let currentParagraph = ''
  
  for (const sentence of sentences) {
    if (!sentence.trim()) continue
    
    const testParagraph = currentParagraph + sentence + '。'
    
    if (testParagraph.length <= maxParagraphLength) {
      currentParagraph = testParagraph
    } else {
      if (currentParagraph) {
        paragraphs.push(currentParagraph.trim())
      }
      currentParagraph = sentence + '。'
    }
  }
  
  if (currentParagraph.trim()) {
    paragraphs.push(currentParagraph.trim())
  }
  
  return paragraphs.length > 0 ? paragraphs : [text]
}

/**
 * @description 计算文本的阅读时间（分钟）
 * @param text 文本内容
 * @param wordsPerMinute 每分钟阅读字数，中文默认300字
 * @returns 阅读时间（分钟）
 */
export function calculateReadingTime(text: string, wordsPerMinute: number = 300): number {
  if (!text) return 0
  
  const plainText = markdownToPlainText(text)
  const wordCount = plainText.length
  const minutes = Math.ceil(wordCount / wordsPerMinute)
  
  return Math.max(1, minutes)
}

/**
 * @description 高亮搜索关键词
 * @param text 文本内容
 * @param keyword 搜索关键词
 * @returns 高亮后的文本
 */
export function highlightKeyword(text: string, keyword: string): string {
  if (!text || !keyword) return text
  
  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '【$1】')
}

/**
 * @description 格式化代码块
 * @param code 代码内容
 * @param language 编程语言
 * @returns 格式化后的代码显示
 */
export function formatCodeBlock(code: string, language?: string): string {
  if (!code) return ''
  
  const langPrefix = language ? `[${language.toUpperCase()}] ` : '[代码] '
  const lines = code.split('\n')
  
  if (lines.length <= 3) {
    return `${langPrefix}${code}`
  }
  
  const preview = lines.slice(0, 3).join('\n')
  return `${langPrefix}${preview}\n...(共${lines.length}行)`
}
