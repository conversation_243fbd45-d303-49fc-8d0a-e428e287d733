/**
 * @description 成就徽章服务
 * <AUTHOR>
 */

import { httpGet } from '../utils/http'

/**
 * @description 接口响应数据类型
 */
export interface IResData<T> {
  code: number
  message: string
  data: T
}

/**
 * @description 徽章接口定义
 */
export interface Badge {
  id: string
  icon: string
  color: string
  title: string
  desc: string
  unlocked: boolean
  unlockedAt?: string
  isPinned?: boolean
  pinnedAt?: string
  category?: string
  rarity?: 'common' | 'rare' | 'epic' | 'legendary'
}

/**
 * @description 徽章分类
 */
export const BADGE_CATEGORIES = {
  interview: '面试成长',
  study: '学习进度',
  achievement: '特殊成就',
  skill: '技能提升',
} as const

/**
 * @description 获取徽章数据的API接口
 * @param params 查询参数
 */
export const fetchBadges = async (params?: {
  category?: string
  unlocked?: boolean
  rarity?: 'common' | 'rare' | 'epic' | 'legendary'
}): Promise<IResData<Badge[]>> => {
  try {
    // 调用后端API获取徽章数据
    return await httpGet<Badge[]>('/app/achievement/badges', params)
  } catch (error) {
    console.error('获取徽章数据失败:', error)
    // 返回一个模拟的成功响应
    return {
      code: 200,
      message: '获取成功(演示数据)',
      data: getMockBadges(),
    }
  }
}

/**
 * @description 获取所有徽章数据
 */
export const getAllBadges = (): Badge[] => {
  // 尝试从本地缓存获取徽章数据
  const cachedBadges = uni.getStorageSync('cached_badges')
  if (cachedBadges) {
    try {
      const badges = JSON.parse(cachedBadges)
      if (Array.isArray(badges) && badges.length > 0) {
        return badges
      }
    } catch (e) {
      console.error('解析缓存的徽章数据失败:', e)
    }
  }

  // 如果没有缓存或缓存无效，返回演示数据
  return getMockBadges()
}

/**
 * @description 从服务器获取所有徽章并更新缓存
 * @returns 成功或失败的标志
 */
export const refreshBadges = async (): Promise<boolean> => {
  try {
    const response = await fetchBadges()
    if (response.code === 200 && response.data) {
      // 更新本地缓存
      uni.setStorageSync('cached_badges', JSON.stringify(response.data))
      return true
    }
    return false
  } catch (error) {
    console.error('刷新徽章数据失败:', error)
    return false
  }
}

/**
 * @description 获取演示徽章数据
 */
const getMockBadges = (): Badge[] => {
  return [
    {
      id: '1',
      icon: 'trophy',
      color: 'badge-gold',
      title: '初试牛刀',
      desc: '完成首次面试练习',
      unlocked: true,
      unlockedAt: '2024-01-01',
      category: 'interview',
      rarity: 'common',
    },
    {
      id: '2',
      icon: 'fire',
      color: 'badge-red',
      title: '连续学习',
      desc: '连续学习7天不间断',
      unlocked: true,
      unlockedAt: '2024-01-07',
      category: 'study',
      rarity: 'common',
    },
    {
      id: '3',
      icon: 'star',
      color: 'badge-green',
      title: '高分达人',
      desc: '单次面试获得80+分',
      unlocked: true,
      unlockedAt: '2024-01-15',
      category: 'achievement',
      rarity: 'rare',
    },
    {
      id: '4',
      icon: 'medal',
      color: 'badge-blue',
      title: '面试达人',
      desc: '累计完成10次面试',
      unlocked: true,
      unlockedAt: '2024-02-01',
      category: 'interview',
      rarity: 'rare',
    },
    {
      id: '5',
      icon: 'graduation',
      color: 'badge-bronze',
      title: '知识渊博',
      desc: '专业知识能力达到85分',
      unlocked: true,
      unlockedAt: '2024-02-15',
      category: 'skill',
      rarity: 'epic',
    },
    {
      id: '6',
      icon: 'lightbulb',
      color: 'badge-silver',
      title: '思维敏捷',
      desc: '逻辑思维能力达到85分',
      unlocked: true,
      unlockedAt: '2024-03-01',
      category: 'skill',
      rarity: 'epic',
    },
    {
      id: '7',
      icon: 'book',
      color: 'badge-blue',
      title: '勤学好问',
      desc: '连续学习15天',
      unlocked: true,
      unlockedAt: '2024-03-10',
      category: 'study',
      rarity: 'rare',
    },
    {
      id: '8',
      icon: 'rocket',
      color: 'badge-orange',
      title: '快速成长',
      desc: '一个月内能力提升20分',
      unlocked: false,
      category: 'skill',
      rarity: 'epic',
    },
    {
      id: '9',
      icon: 'crown',
      color: 'badge-purple',
      title: '面试之王',
      desc: '累计完成50次面试',
      unlocked: false,
      category: 'interview',
      rarity: 'legendary',
    },
    {
      id: '10',
      icon: 'target',
      color: 'badge-gold',
      title: '全能选手',
      desc: '所有能力项均达到80分',
      unlocked: false,
      category: 'achievement',
      rarity: 'legendary',
    },
    {
      id: '11',
      icon: 'certificate',
      color: 'badge-purple',
      title: '完美表现',
      desc: '获得满分评价',
      unlocked: false,
      category: 'achievement',
      rarity: 'legendary',
    },
    {
      id: '12',
      icon: 'gem',
      color: 'badge-green',
      title: '坚持不懈',
      desc: '连续100天使用APP',
      unlocked: false,
      category: 'study',
      rarity: 'legendary',
    },
    {
      id: '13',
      icon: 'fire',
      color: 'badge-red',
      title: '学习狂人',
      desc: '连续学习30天',
      unlocked: false,
      category: 'study',
      rarity: 'epic',
    },
    {
      id: '14',
      icon: 'star',
      color: 'badge-gold',
      title: '完美主义',
      desc: '连续5次获得90+分',
      unlocked: false,
      category: 'achievement',
      rarity: 'epic',
    },
    {
      id: '15',
      icon: 'trophy',
      color: 'badge-gold',
      title: '传奇面试官',
      desc: '累计完成100次面试',
      unlocked: false,
      category: 'interview',
      rarity: 'legendary',
    },
  ]
}

/**
 * @description 获取置顶徽章的本地存储键
 */
const PINNED_BADGES_KEY = 'pinned_badges'

/**
 * @description 获取置顶徽章ID列表
 */
export const getPinnedBadgeIds = (): string[] => {
  try {
    const stored = uni.getStorageSync(PINNED_BADGES_KEY)
    return stored ? JSON.parse(stored) : ['1', '3', '4', '5'] // 默认置顶4个已解锁徽章
  } catch (error) {
    console.error('获取置顶徽章失败:', error)
    return ['1', '3', '4', '5']
  }
}

/**
 * @description 设置置顶徽章
 * @param badgeIds 要置顶的徽章ID数组，最多4个
 */
export const setPinnedBadges = (badgeIds: string[]): boolean => {
  try {
    // 限制最多4个置顶徽章
    const pinnedIds = badgeIds.slice(0, 4)
    uni.setStorageSync(PINNED_BADGES_KEY, JSON.stringify(pinnedIds))

    uni.showToast({
      title: '设置成功',
      icon: 'success',
    })

    return true
  } catch (error) {
    console.error('设置置顶徽章失败:', error)
    uni.showToast({
      title: '设置失败',
      icon: 'error',
    })
    return false
  }
}

/**
 * @description 获取置顶徽章数据
 */
export const getPinnedBadges = (): Badge[] => {
  const allBadges = getAllBadges()
  const pinnedIds = getPinnedBadgeIds()

  return pinnedIds
    .map((id) => allBadges.find((badge) => badge.id === id))
    .filter((badge): badge is Badge => badge !== undefined)
}

/**
 * @description 切换徽章置顶状态
 * @param badgeId 徽章ID
 */
export const toggleBadgePin = (badgeId: string): boolean => {
  const currentPinnedIds = getPinnedBadgeIds()
  const isCurrentlyPinned = currentPinnedIds.includes(badgeId)

  if (isCurrentlyPinned) {
    // 取消置顶
    const newPinnedIds = currentPinnedIds.filter((id) => id !== badgeId)
    return setPinnedBadges(newPinnedIds)
  } else {
    // 添加置顶
    if (currentPinnedIds.length >= 4) {
      uni.showToast({
        title: '最多只能置顶4个徽章',
        icon: 'none',
      })
      return false
    }

    const newPinnedIds = [...currentPinnedIds, badgeId]
    return setPinnedBadges(newPinnedIds)
  }
}

/**
 * @description 检查徽章是否已置顶
 * @param badgeId 徽章ID
 */
export const isBadgePinned = (badgeId: string): boolean => {
  const pinnedIds = getPinnedBadgeIds()
  return pinnedIds.includes(badgeId)
}

/**
 * @description 获取徽章稀有度颜色
 * @param rarity 稀有度
 */
export const getRarityColor = (rarity: Badge['rarity']): string => {
  const colorMap = {
    common: '#9CA3AF',
    rare: '#3B82F6',
    epic: '#7C3AED',
    legendary: '#F59E0B',
  }
  return colorMap[rarity || 'common']
}

/**
 * @description 获取徽章分类统计
 */
export const getBadgeStats = () => {
  const allBadges = getAllBadges()
  const unlockedBadges = allBadges.filter((badge) => badge.unlocked)

  const categoryStats = Object.entries(BADGE_CATEGORIES).map(([key, name]) => {
    const categoryBadges = allBadges.filter((badge) => badge.category === key)
    const unlockedInCategory = categoryBadges.filter((badge) => badge.unlocked)

    return {
      category: key,
      name,
      total: categoryBadges.length,
      unlocked: unlockedInCategory.length,
      progress: Math.round((unlockedInCategory.length / categoryBadges.length) * 100),
    }
  })

  return {
    total: allBadges.length,
    unlocked: unlockedBadges.length,
    progress: Math.round((unlockedBadges.length / allBadges.length) * 100),
    categories: categoryStats,
  }
}

/**
 * @description 解锁徽章
 * @param badgeId 徽章ID
 * @returns 是否解锁成功
 */
export const unlockBadge = async (badgeId: string): Promise<boolean> => {
  try {
    const response = await httpGet<{ success: boolean }>('/app/achievement/unlock', { badgeId })
    
    if (response.code === 200 && response.data?.success) {
      // 解锁成功，刷新缓存
      await refreshBadges()
      
      uni.showToast({
        title: '成就解锁成功',
        icon: 'success',
      })
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('解锁徽章失败:', error)
    
    // 演示模式下模拟解锁成功
    const cachedBadges = uni.getStorageSync('cached_badges')
    if (cachedBadges) {
      try {
        const badges = JSON.parse(cachedBadges)
        if (Array.isArray(badges)) {
          const badgeIndex = badges.findIndex(b => b.id === badgeId)
          if (badgeIndex >= 0) {
            badges[badgeIndex].unlocked = true
            badges[badgeIndex].unlockedAt = new Date().toISOString()
            uni.setStorageSync('cached_badges', JSON.stringify(badges))
            
            uni.showToast({
              title: '成就解锁成功(演示)',
              icon: 'success',
            })
            
            return true
          }
        }
      } catch (e) {
        console.error('解析缓存的徽章数据失败:', e)
      }
    }
    
    return false
  }
}
