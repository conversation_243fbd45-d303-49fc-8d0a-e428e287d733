<template>
  <view class="message-item" :class="`message-${message.role}`">
    <view class="message-avatar">
      <view v-if="message.role === 'user'" class="user-avatar">
        <view class="i-carbon-user text-20px"></view>
      </view>
      <view v-else class="ai-avatar-small" :style="{ backgroundColor: agentColor }">
        <view :class="agentIcon" class="text-20px"></view>
      </view>
    </view>
    
    <view class="message-content">
      <!-- 用户消息 -->
      <text v-if="message.role === 'user'" class="message-text user-text">
        {{ message.content }}
      </text>
      
      <!-- AI消息 -->
      <view v-else class="ai-message-container">
        <!-- Think思考过程 -->
        <view v-if="message.think && message.think.trim()" class="think-container">
          <view class="think-header">
            <view class="think-icon">
              <view class="i-carbon-idea text-16px"></view>
            </view>
            <text class="think-label">思考过程</text>
            <view class="think-toggle" @click="$emit('toggle-think', message.id)">
              <view :class="message.showThink ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'"
                class="text-14px"></view>
            </view>
          </view>
          <view v-if="message.showThink" class="think-content">
            <text class="think-text">{{ message.think }}</text>
          </view>
        </view>

        <!-- AI回答内容 -->
        <rich-text class="message-text ai-text markdown-content" 
          :class="getMessageClass(message)"
          :nodes="renderedContent"></rich-text>

        <!-- 流式输入指示器 -->
        <view v-if="message.isStreaming" class="streaming-indicator">
          <view class="cursor-blink"></view>
        </view>

        <!-- 错误状态 -->
        <view v-if="message.isError" class="error-indicator">
          <view class="error-content">
            <view class="i-carbon-warning text-16px text-red-500"></view>
            <text class="error-text">消息发送失败，请重试</text>
          </view>
          <view class="error-actions">
            <view class="retry-btn" @click="$emit('retry', message)">
              <view class="i-carbon-restart text-14px"></view>
              <text>重试</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 附件 -->
      <view v-if="message.attachments" class="message-attachments">
        <view v-for="(att, i) in message.attachments" :key="i" class="attachment-item"
          @click="$emit('preview-attachment', att)">
          <image v-if="att.type === 'image'" :src="att.url" mode="aspectFit" class="attachment-image" />
          <view v-else class="attachment-file">
            <view class="i-carbon-document text-24px"></view>
            <text class="file-name">{{ att.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { markdownToHtml } from '@/utils/markdown'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: number
  attachments?: Array<{
    type: 'image' | 'file' | 'voice'
    url: string
    name: string
  }>
  isStreaming?: boolean
  isError?: boolean
  think?: string
  showThink?: boolean
}

const props = defineProps<{
  message: Message
  agentColor: string
  agentIcon: string
}>()

defineEmits<{
  'toggle-think': [messageId: string]
  'retry': [message: Message]
  'preview-attachment': [attachment: any]
}>()

// 缓存渲染结果
const markdownCache = new Map<string, string>()

const renderedContent = computed(() => {
  if (props.message.role === 'user') {
    return props.message.content
  }

  const content = props.message.content || ''
  if (!content) return ''

  // 检查缓存
  if (markdownCache.has(content)) {
    return markdownCache.get(content)!
  }

  try {
    const rendered = markdownToHtml(content)
    // 限制缓存大小
    if (markdownCache.size > 50) {
      const firstKey = markdownCache.keys().next().value
      markdownCache.delete(firstKey)
    }
    markdownCache.set(content, rendered)
    return rendered
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return content
  }
})

const getMessageClass = (message: Message) => {
  if (message.isStreaming) return 'streaming'
  if (message.isError) return 'error'
  return ''
}
</script>

<style lang="scss" scoped>
.message-item {
  display: flex;
  padding: 24rpx;
  margin-bottom: 32rpx;

  &.message-user {
    flex-direction: row-reverse;

    .message-content {
      margin-right: 24rpx;
      margin-left: 0;
    }

    .user-text {
      background: linear-gradient(135deg, #00c9a7 0%, #00c49a 100%);
      color: white;
    }
  }

  &.message-assistant {
    .message-content {
      margin-left: 24rpx;
    }
  }
}

.message-avatar {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;

  .user-avatar,
  .ai-avatar-small {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }

  .user-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .ai-avatar-small {
    color: white;
  }
}

.message-content {
  flex: 1;
  min-width: 0;

  .message-text {
    display: block;
    padding: 24rpx;
    font-size: 28rpx;
    line-height: 1.6;
    border-radius: 24rpx;
    word-wrap: break-word;

    &.user-text {
      margin-left: auto;
      max-width: 80%;
    }

    &.ai-text {
      background: #f8f9fa;
      color: #333;

      &.streaming {
        border-bottom: 2px solid #00c9a7;
      }

      &.error {
        background: #fee;
        border-left: 4px solid #ff4444;
      }
    }
  }
}

.think-container {
  margin-bottom: 16rpx;
  background: #f0f8ff;
  border-radius: 16rpx;
  overflow: hidden;

  .think-header {
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    background: #e6f3ff;
    cursor: pointer;

    .think-icon {
      margin-right: 12rpx;
      color: #1890ff;
    }

    .think-label {
      flex: 1;
      font-size: 24rpx;
      color: #1890ff;
      font-weight: 500;
    }

    .think-toggle {
      color: #1890ff;
      transition: transform 0.3s;
    }
  }

  .think-content {
    padding: 20rpx;
    
    .think-text {
      font-size: 24rpx;
      line-height: 1.5;
      color: #666;
    }
  }
}

.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 16rpx;

  .cursor-blink {
    width: 2px;
    height: 20rpx;
    background: #00c9a7;
    animation: blink 1s infinite;
  }
}

.error-indicator {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #fee;
  border-radius: 12rpx;

  .error-content {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;

    .error-text {
      margin-left: 8rpx;
      font-size: 24rpx;
      color: #ff4444;
    }
  }

  .error-actions {
    display: flex;
    justify-content: flex-end;

    .retry-btn {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      font-size: 24rpx;
      color: #ff4444;
      background: white;
      border: 1px solid #ff4444;
      border-radius: 8rpx;
      transition: all 0.3s;

      &:active {
        background: #ff4444;
        color: white;
      }
    }
  }
}

.message-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;

  .attachment-item {
    border-radius: 12rpx;
    overflow: hidden;
    background: #f5f5f5;

    .attachment-image {
      width: 200rpx;
      height: 200rpx;
    }

    .attachment-file {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24rpx;
      width: 200rpx;

      .file-name {
        margin-top: 12rpx;
        font-size: 24rpx;
        color: #666;
        text-align: center;
      }
    }
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
