<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import HeadBar from '@/components/HeadBar.vue'
import {
  getResultSummary,
  getResultDetail,
  saveToHistory as saveToHistoryApi,
} from '@/service/interview-result'
/**
 * @description 面试结果页面
 * 展示面试评分结果和详细反馈建议
 * 支持从面试房间传递数据或使用演示数据
 */

// 页面参数
const pageParams = ref({
  sessionId: '',
  reportId: '',
})

// 面试结果数据
const interviewResult = ref({
  jobName: '机器学习工程师',
  company: '阿里巴巴',
  totalScore: 72,
  duration: '45分钟',
  date: '2025-07-18 09:36:05',
  answeredQuestions: 15,
  totalQuestions: 15,
  mode: 'AI',

  // 多模态分析数据
  multiModalMetrics: {
    speech: {
      clarity: 75,
      fluency: 70,
      emotion: 78,
      pace: 72,
      logic: 76,
    },
    video: {
      eyeContact: 68,
      expression: 82,
      gesture: 65,
      posture: 78,
      confidence: 74,
    },
    text: {
      structure: 72,
      relevance: 80,
      depth: 78,
      keywords: 85,
      grammar: 88,
    },
  },

  // 核心能力评测
  coreAbilities: {
    professionalKnowledge: 80,
    skillMatching: 75,
    communicationSkill: 70,
    logicalThinking: 78,
    innovation: 72,
    stressResistance: 68,
    teamwork: 74,
    leadership: 65,
  },

  // 传统维度数据（兼容原有设计）
  dimensions: [
    { name: '专业能力', value: 80, max: 100, icon: 'i-mdi-code-braces' },
    { name: '表达能力', value: 70, max: 100, icon: 'i-mdi-account-voice' },
    { name: '逻辑思维', value: 78, max: 100, icon: 'i-mdi-brain' },
    { name: '问题解决', value: 75, max: 100, icon: 'i-mdi-puzzle' },
    { name: '沟通能力', value: 68, max: 100, icon: 'i-mdi-forum' },
    { name: '综合素质', value: 72, max: 100, icon: 'i-mdi-star' },
  ],

  // 智能反馈
  intelligentFeedback: {
    overallScore: 72,
    strengths: [
      {
        ability: '专业知识',
        score: 80,
        description: '对机器学习算法和框架有较好的理解，能够清晰地表达技术概念',
      },
      {
        ability: '逻辑思维',
        score: 78,
        description: '具备良好的数据分析思维，能够理解模型评估和优化的逻辑',
      },
    ],
    weaknesses: [
      {
        ability: '表达能力',
        score: 70,
        description: '在描述复杂算法时表达略显不够流畅，需要加强技术表达能力',
      },
      {
        ability: '沟通能力',
        score: 68,
        description: '与面试官的互动略显被动，建议增强主动沟通的意识',
      },
    ],
    suggestions: [
      '建议加强对深度学习框架的实践经验',
      '建议提升数据预处理和特征工程的实际操作能力',
      '建议增强模型部署和生产环境优化的经验',
      '建议加强与业务团队的沟通协作能力',
    ],
  },

  feedback: [
    {
      type: 'strength',
      content: '对机器学习算法和框架有较好的理解，能够清晰地表达技术概念',
      icon: 'i-mdi-thumb-up',
    },
    {
      type: 'strength',
      content: '具备良好的数据分析思维，能够理解模型评估和优化的逻辑',
      icon: 'i-mdi-thumb-up',
    },
    {
      type: 'weakness',
      content: '在描述复杂算法时表达略显不够流畅，需要加强技术表达能力',
      icon: 'i-mdi-alert-circle',
    },
    {
      type: 'weakness',
      content: '与面试官的互动略显被动，建议增强主动沟通的意识',
      icon: 'i-mdi-alert-circle',
    },
    {
      type: 'improvement',
      content: '建议加强对深度学习框架的实践经验',
      icon: 'i-mdi-lightbulb',
    },
    {
      type: 'improvement',
      content: '建议提升数据预处理和特征工程的实际操作能力',
      icon: 'i-mdi-lightbulb',
    },
  ],
  recommendations: [
    {
      title: '机器学习进阶课程',
      description: '深入学习深度学习框架和算法优化技术',
      type: 'course',
      icon: 'i-mdi-school',
    },
    {
      title: 'AI面试模拟练习',
      description: '针对机器学习岗位的专项面试训练',
      type: 'practice',
      icon: 'i-mdi-account-multiple',
    },
    {
      title: '数据科学社区',
      description: '与其他机器学习工程师交流项目经验',
      type: 'community',
      icon: 'i-mdi-forum',
    },
  ],
  // 学习推荐配置选项
  recommendationsOptions: {
    title: '学习推荐',
    color: '#00C9A7',
    icon: 'i-mdi-school',
  },
})

// 加载状态
const loading = ref(true)

// 错误状态
const loadError = ref({
  hasError: false,
  message: '',
  details: '',
})

/**
 * @description 加载面试结果数据
 */
const loadInterviewResult = async () => {
  loading.value = true
  loadError.value.hasError = false

  try {
    // 优先从本地存储获取数据（面试房间保存的数据）
    const localReport = uni.getStorageSync('latestInterviewReport')
    if (localReport) {
      console.log('从本地存储加载面试报告:', localReport)

      // 更新基本信息
      interviewResult.value.jobName = localReport.jobId
        ? '机器学习工程师'
        : interviewResult.value.jobName
      interviewResult.value.mode = localReport.mode || 'standard'
      interviewResult.value.date = new Date(localReport.endTime).toLocaleString()
      interviewResult.value.duration = `${Math.round((localReport.endTime - localReport.startTime) / 60000)}分钟`
      interviewResult.value.answeredQuestions = localReport.completedQuestions || 0
      interviewResult.value.totalQuestions = localReport.totalQuestions || 10

      // 更新多模态数据
      if (localReport.multiModalMetrics) {
        interviewResult.value.multiModalMetrics = localReport.multiModalMetrics

        // 根据多模态数据重新计算维度分数
        const speech = localReport.multiModalMetrics.speech
        const video = localReport.multiModalMetrics.video
        const text = localReport.multiModalMetrics.text

        interviewResult.value.dimensions = [
          {
            name: '专业能力',
            value: Math.round((text.relevance + text.keywords + text.depth) / 3),
            max: 100,
            icon: 'i-mdi-code-braces',
          },
          {
            name: '表达能力',
            value: Math.round((speech.clarity + speech.fluency + video.expression) / 3),
            max: 100,
            icon: 'i-mdi-account-voice',
          },
          {
            name: '逻辑思维',
            value: Math.round((speech.logic + text.structure + text.depth) / 3),
            max: 100,
            icon: 'i-mdi-brain',
          },
          {
            name: '问题解决',
            value: Math.round((text.relevance + speech.logic + video.confidence) / 3),
            max: 100,
            icon: 'i-mdi-puzzle',
          },
          {
            name: '沟通能力',
            value: Math.round((video.eyeContact + video.expression + speech.emotion) / 3),
            max: 100,
            icon: 'i-mdi-forum',
          },
          {
            name: '综合素质',
            value: Math.round((video.confidence + speech.logic + text.grammar) / 3),
            max: 100,
            icon: 'i-mdi-star',
          },
        ]
      }

      // 更新核心能力和智能反馈
      if (localReport.coreAbilities) {
        interviewResult.value.coreAbilities = localReport.coreAbilities
      }

      if (localReport.intelligentFeedback) {
        interviewResult.value.intelligentFeedback = localReport.intelligentFeedback

        // 根据智能反馈更新feedback数组
        const newFeedback = []

        // 添加优势
        localReport.intelligentFeedback.strengths.forEach((strength) => {
          newFeedback.push({
            type: 'strength',
            content: strength.description,
            icon: 'i-mdi-thumb-up',
          })
        })

        // 添加劣势
        localReport.intelligentFeedback.weaknesses?.forEach((weakness) => {
          newFeedback.push({
            type: 'weakness',
            content: weakness.description,
            icon: 'i-mdi-alert-circle',
          })
        })

        // 添加建议
        localReport.intelligentFeedback.suggestions?.forEach((suggestion) => {
          newFeedback.push({
            type: 'improvement',
            content: suggestion,
            icon: 'i-mdi-lightbulb',
          })
        })

        if (newFeedback.length > 0) {
          interviewResult.value.feedback = newFeedback
        }
      }

      // 计算总分
      const dimensionScores = interviewResult.value.dimensions.map((d) => d.value)
      interviewResult.value.totalScore = Math.round(
        dimensionScores.reduce((sum, score) => sum + score, 0) / dimensionScores.length,
      )

      // 清除本地存储的临时数据
      uni.removeStorageSync('latestInterviewReport')
    }

    // 如果有sessionId或reportId，从后端获取数据
    if (pageParams.value.sessionId || pageParams.value.reportId) {
      await loadResultFromApi()
    }
    // 保存到历史记录
    await saveToHistory()
  } catch (error) {
    console.error('加载面试结果失败:', error)
    loadError.value = {
      hasError: true,
      message: '加载面试结果失败',
      details: error.message || '请稍后重试或使用演示数据',
    }
  } finally {
    loading.value = false
  }
}

/**
 * @description 从API加载结果数据
 */
const loadResultFromApi = async () => {
  try {
    const resultId = pageParams.value.reportId || pageParams.value.sessionId

    // 获取结果摘要
    const summaryResponse = await getResultSummary(resultId)
    if (summaryResponse.code === 200 && summaryResponse.data) {
      const summary = summaryResponse.data

      // 更新基本信息
      interviewResult.value.jobName = summary.jobName
      interviewResult.value.company = summary.company
      interviewResult.value.totalScore = summary.totalScore
      interviewResult.value.date = summary.date
      interviewResult.value.answeredQuestions = summary.answeredQuestions
      interviewResult.value.totalQuestions = summary.totalQuestions
      interviewResult.value.mode = summary.mode
    }

    // 获取详细结果
    const detailResponse = await getResultDetail(resultId)
    if (detailResponse.code === 200 && detailResponse.data) {
      const detail = detailResponse.data

      // 更新多模态数据
      if (detail.audioMetrics) {
        interviewResult.value.multiModalMetrics.speech = {
          clarity: detail.audioMetrics.clarity,
          fluency: detail.audioMetrics.fluency,
          emotion: detail.audioMetrics.confidence,
          pace: detail.audioMetrics.pace,
          logic: detail.audioMetrics.overall,
        }
      }

      if (detail.videoMetrics) {
        interviewResult.value.multiModalMetrics.video = {
          eyeContact: detail.videoMetrics.eyeContact,
          expression: detail.videoMetrics.expressions,
          gesture: detail.videoMetrics.gestures,
          posture: detail.videoMetrics.posture,
          confidence: detail.videoMetrics.overall,
        }
      }

      // 更新维度分数
      if (detail.dimensionScores) {
        interviewResult.value.dimensions = detail.dimensionScores.map((dim) => ({
          name: dim.dimension,
          value: dim.score,
          max: dim.maxScore,
          icon: getIconForDimension(dim.dimension),
        }))
      }

      // 更新智能反馈
      if (detail.overallFeedback) {
        interviewResult.value.intelligentFeedback.overallScore = detail.totalScore
        interviewResult.value.intelligentFeedback.strengths = detail.topStrengths || []
        interviewResult.value.intelligentFeedback.weaknesses = detail.topWeaknesses || []
      }
    }

    console.log('API结果数据加载成功')
  } catch (error) {
    console.error('从API加载结果失败:', error)
    throw error
  }
}


/**
 * @description 根据维度名称获取图标
 */
const getIconForDimension = (dimension: string): string => {
  const iconMap: Record<string, string> = {
    专业知识: 'i-mdi-code-braces',
    表达能力: 'i-mdi-account-voice',
    逻辑思维: 'i-mdi-brain',
    问题解决: 'i-mdi-puzzle',
    沟通能力: 'i-mdi-forum',
    综合素质: 'i-mdi-star',
    technical: 'i-mdi-code-braces',
    communication: 'i-mdi-account-voice',
    problemSolving: 'i-mdi-puzzle',
    teamwork: 'i-mdi-forum',
    leadership: 'i-mdi-star',
    creativity: 'i-mdi-lightbulb',
  }
  return iconMap[dimension] || 'i-mdi-circle'
}

/**
 * @description 保存面试记录到历史记录
 */
const saveToHistory = async () => {
  try {
    const historyRecord = {
      id: Date.now().toString(),
      sessionId: pageParams.value.sessionId,
      jobName: interviewResult.value.jobName,
      company: interviewResult.value.company,
      totalScore: interviewResult.value.totalScore,
      date: interviewResult.value.date,
      duration: interviewResult.value.duration,
      mode: interviewResult.value.mode,
      status: 'completed',
      dimensions: interviewResult.value.dimensions,
      multiModalMetrics: interviewResult.value.multiModalMetrics,
      coreAbilities: interviewResult.value.coreAbilities,
      intelligentFeedback: interviewResult.value.intelligentFeedback,
      createdAt: Date.now(),
    }

    // 保存到本地存储
    const existingHistory = uni.getStorageSync('interviewHistory') || []
    existingHistory.unshift(historyRecord)

    // 只保留最近50条记录
    if (existingHistory.length > 50) {
      existingHistory.splice(50)
    }

    uni.setStorageSync('interviewHistory', existingHistory)

    // 如果有reportId，保存到后端历史记录
    if (pageParams.value.reportId) {
      try {
        const response = await saveToHistoryApi(
          pageParams.value.reportId,
          interviewResult.value.jobName,
        )
        if (response.code === 200) {
          console.log('保存到历史记录成功:', response.data)
        }
      } catch (error) {
        console.error('保存到后端历史记录失败:', error)
      }
    }
  } catch (error) {
    console.error('保存历史记录失败:', error)
  }
}

/**
 * @description 页面加载时获取参数
 */
onLoad((options) => {
  pageParams.value = {
    sessionId: options.sessionId || '',
    reportId: options.reportId || '',
  }
})

/**
 * @description 页面挂载时加载数据
 */
onMounted(() => {
  loadInterviewResult()
})

/**
 * @description 获取评分等级信息
 * @param score 分数
 */
const getScoreLevel = (score: number) => {
  if (score >= 90) return { level: '优秀', color: '#52c41a', icon: 'i-mdi-medal' }
  if (score >= 80) return { level: '良好', color: '#00C9A7', icon: 'i-mdi-thumb-up' }
  if (score >= 70) return { level: '中等', color: '#faad14', icon: 'i-mdi-minus-circle' }
  if (score >= 60) return { level: '及格', color: '#fa8c16', icon: 'i-mdi-alert-circle' }
  return { level: '不及格', color: '#f5222d', icon: 'i-mdi-close-circle' }
}

// 当前分数等级
const scoreLevel = computed(() => getScoreLevel(interviewResult.value.totalScore))

// 完成进度百分比
const completionRate = computed(() => {
  return Math.round(
    (interviewResult.value.answeredQuestions / interviewResult.value.totalQuestions) * 100,
  )
})

/**
 * @description 获取维度分数颜色
 * @param value 分数值
 */
const getDimensionColor = (value: number) => {
  if (value >= 90) return '#52c41a'
  if (value >= 80) return '#00C9A7'
  if (value >= 70) return '#faad14'
  return '#f5222d'
}

/**
 * @description 重新开始面试
 */
const restartInterview = () => {
  uni.showModal({
    title: '重新面试',
    content: '确定要重新开始面试吗？',
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: '/pages/interview/select',
        })
      }
    },
  })
}

/**
 * @description 查看学习推荐
 */
const viewRecommendations = () => {
  uni.navigateTo({
    url: '/pages/learning/recommend',
  })
}

/**
 * @description 查看详细报告
 */
const viewDetailReport = () => {
  uni.navigateTo({
    url: '/pages/interview/detail',
  })
}

/**
 * @description 开始学习推荐项目
 * @param recommendation 推荐项目
 */
const startRecommendation = (recommendation: any) => {
  const urls = {
    course: '/pages/learning/resources',
    practice: '/pages/interview/select',
    community: '/pages/learning/community',
  }

  uni.navigateTo({
    url: urls[recommendation.type] || '/pages/learning/recommend',
  })
}

/**
 * @description 返回首页
 */
const goHome = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

/**
 * @description 重试加载
 */
const retryLoading = () => {
  loadError.value.hasError = false
  loadInterviewResult()
}
</script>

<template>
  <view class="result-container">
    <HeadBar title="面试结果" :is-tab-bar="false" />

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载面试结果...</text>
    </view>

    <!-- 加载错误 -->
    <view v-else-if="loadError.hasError" class="error-container">
      <view class="error-content">
        <text class="i-mdi-alert-circle error-icon"></text>
        <text class="error-title">{{ loadError.message }}</text>
        <text class="error-message">{{ loadError.details }}</text>
        <view class="error-actions">
          <button class="retry-btn" @click="retryLoading">
            <text class="i-mdi-refresh"></text>
            <text>重试</text>
          </button>
          <button class="home-btn" @click="goHome">
            <text class="i-mdi-home"></text>
            <text>返回首页</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 结果内容 -->
    <template v-else>
      <!-- 顶部结果概览 -->
      <view class="result-header">
        <view class="job-info">
          <text class="job-name">{{ interviewResult.jobName }}</text>
          <view class="company-info">
            <text class="i-mdi-office-building company-icon"></text>
            <text class="job-company">{{ interviewResult.company }}</text>
          </view>
          <view class="interview-meta">
            <text class="meta-item">
              <text class="i-mdi-clock-outline"></text>
              <text>{{ interviewResult.date }}</text>
            </text>
            <text class="meta-item">
              <text class="i-mdi-timer"></text>
              <text>用时 {{ interviewResult.duration }}</text>
            </text>
          </view>
        </view>

        <view class="score-section">
          <view class="score-circle">
            <text class="score-value">{{ interviewResult.totalScore }}</text>
            <text class="score-max">/100</text>
            <text class="score-label">总分</text>
          </view>
          <view class="score-info">
            <view class="level-badge" :style="{ backgroundColor: scoreLevel.color }">
              <text :class="scoreLevel.icon" class="level-icon"></text>
              <text class="level-text">{{ scoreLevel.level }}</text>
            </view>
            <text class="completion-text">
              完成度 {{ completionRate }}% ({{ interviewResult.answeredQuestions }}/{{
                interviewResult.totalQuestions
              }}题)
            </text>
          </view>
        </view>
      </view>

      <!-- 能力维度评分 -->
      <view class="dimensions-section">
        <view class="section-header">
          <text class="i-mdi-chart-box section-icon"></text>
          <text class="section-title">能力维度评估</text>
        </view>

        <view class="dimensions-grid">
          <view class="dimension-item" v-for="dimension in interviewResult.dimensions" :key="dimension.name">
            <view class="dimension-header">
              <text :class="dimension.icon" class="dimension-icon"></text>
              <text class="dimension-name">{{ dimension.name }}</text>
            </view>
            <view class="dimension-score">
              <text class="score-text" :style="{ color: getDimensionColor(dimension.value) }">
                {{ dimension.value }}
              </text>
              <text class="score-max">/{{ dimension.max }}</text>
            </view>
            <view class="dimension-bar">
              <view class="dimension-progress" :style="{
                width: (dimension.value / dimension.max) * 100 + '%',
                backgroundColor: getDimensionColor(dimension.value),
              }"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 反馈建议 -->
      <view class="feedback-section">
        <view class="section-header">
          <text class="i-mdi-comment-text section-icon"></text>
          <text class="section-title">评估反馈</text>
        </view>

        <!-- 优势 -->
        <view class="feedback-group">
          <text class="group-title">
            <text class="i-mdi-thumb-up group-icon"></text>
            <text>优势表现</text>
          </text>
          <view class="feedback-item strength"
            v-for="(item, index) in interviewResult.feedback.filter((f) => f.type === 'strength')" :key="'s' + index">
            <text :class="item.icon" class="feedback-icon"></text>
            <text class="feedback-content">{{ item.content }}</text>
          </view>
        </view>

        <!-- 待改进 -->
        <view class="feedback-group">
          <text class="group-title">
            <text class="i-mdi-alert-circle group-icon"></text>
            <text>待改进点</text>
          </text>
          <view class="feedback-item weakness"
            v-for="(item, index) in interviewResult.feedback.filter((f) => f.type === 'weakness')" :key="'w' + index">
            <text :class="item.icon" class="feedback-icon"></text>
            <text class="feedback-content">{{ item.content }}</text>
          </view>
        </view>

        <!-- 建议 -->
        <view class="feedback-group">
          <text class="group-title">
            <text class="i-mdi-lightbulb group-icon"></text>
            <text>提升建议</text>
          </text>
          <view class="feedback-item improvement" v-for="(item, index) in interviewResult.feedback.filter(
            (f) => f.type === 'improvement',
          )" :key="'i' + index">
            <text :class="item.icon" class="feedback-icon"></text>
            <text class="feedback-content">{{ item.content }}</text>
          </view>
        </view>
      </view>

      <!-- 学习推荐 暂时隐藏 TODO -->
      <!-- <view class="recommendations-section">
        <view class="section-header">
          <text class="i-mdi-school section-icon"></text>
          <text class="section-title">为您推荐</text>
        </view>

        <view class="recommendation-list">
          <view
            class="recommendation-item"
            v-for="recommendation in interviewResult.recommendations"
            :key="recommendation.title"
            @click="startRecommendation(recommendation)"
          >
            <view class="recommendation-icon">
              <text :class="recommendation.icon"></text>
            </view>
            <view class="recommendation-content">
              <text class="recommendation-title">{{ recommendation.title }}</text>
              <text class="recommendation-desc">{{ recommendation.description }}</text>
            </view>
            <text class="i-mdi-chevron-right recommendation-arrow"></text>
          </view>
        </view>
      </view> -->

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn secondary-btn" @click="viewDetailReport">
          <text class="i-mdi-file-document-outline"></text>
          <text>详细报告</text>
        </button>
        <button class="action-btn primary-btn" @click="restartInterview">
          <text class="i-mdi-refresh"></text>
          <text>再次面试</text>
        </button>
      </view>

      <!-- 底部返回按钮 -->
      <view class="bottom-actions">
        <button class="home-btn" @click="goHome">
          <text class="i-mdi-home"></text>
          <text>返回首页</text>
        </button>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.result-container {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
/* 加载状态样式 */
.loading-container {
  position: absolute;
  top: 88rpx;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(10rpx);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 30rpx;
  border: 6rpx solid #e2e8f0;
  border-top: 6rpx solid #00c9a7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #64748b;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
/* 加载错误样式 */
.error-container {
  position: absolute;
  top: 88rpx;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(10rpx);
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 600rpx;
  padding: 60rpx 40rpx;
  background: #fff;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  border-radius: 24rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
}

.error-icon {
  margin-bottom: 24rpx;
  font-size: 80rpx;
  color: #ff4d4f;
}

.error-title {
  margin-bottom: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.error-message {
  margin-bottom: 32rpx;
  font-size: 26rpx;
  line-height: 1.6;
  color: #64748b;
  text-align: center;
}

.error-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  width: 100%;
}

.retry-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 18rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border: none;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s ease;

  &:active {
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
    transform: translateY(2rpx);
  }
}

.home-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 18rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #64748b;
  background: #fff;
  border: 2rpx solid #e2e8f0;
  border-radius: 50rpx;
  transition: all 0.3s ease;

  &:active {
    background: #f8fafc;
    border-color: #cbd5e1;
  }
}

.result-header {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 40rpx 30rpx 60rpx;
  margin: 20rpx 20rpx 40rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.25);

  .job-info {
    flex: 1;
    padding-right: 20rpx;

    .job-name {
      display: block;
      margin-bottom: 16rpx;
      font-size: 36rpx;
      font-weight: 600;
      line-height: 1.2;
      color: #fff;
    }

    .company-info {
      display: flex;
      gap: 10rpx;
      align-items: center;
      margin-bottom: 20rpx;

      .company-icon {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
      }

      .job-company {
        font-size: 26rpx;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.95);
      }
    }

    .interview-meta {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .meta-item {
        display: flex;
        gap: 8rpx;
        align-items: center;
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.9);

        text:first-child {
          font-size: 20rpx;
        }
      }
    }
  }

  .score-section {
    flex-shrink: 0;
    text-align: center;

    .score-circle {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 24rpx;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(20rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;

      .score-value {
        font-size: 52rpx;
        font-weight: 700;
        line-height: 1;
        color: #fff;
      }

      .score-max {
        position: absolute;
        top: 40rpx;
        right: 24rpx;
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.8);
      }

      .score-label {
        margin-top: 6rpx;
        font-size: 22rpx;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .score-info {
      .level-badge {
        display: inline-flex;
        gap: 8rpx;
        align-items: center;
        padding: 10rpx 20rpx;
        margin-bottom: 16rpx;
        font-size: 24rpx;
        font-weight: 600;
        color: #1e293b;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 24rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        .level-icon {
          font-size: 22rpx;
        }
      }

      .completion-text {
        display: block;
        font-size: 22rpx;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

.dimensions-section,
.feedback-section,
.radar-chart-section,
.recommendations-section {
  margin: 0 20rpx 32rpx;

  .section-header {
    display: flex;
    gap: 12rpx;
    align-items: center;
    padding: 0 4rpx;
    margin-bottom: 24rpx;

    .section-icon {
      font-size: 32rpx;
      color: #00c9a7;
    }

    .section-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }
}

.dimensions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;

  .dimension-item {
    padding: 28rpx 24rpx;
    background: #fff;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
      transform: translateY(-2rpx);
    }

    .dimension-header {
      display: flex;
      gap: 10rpx;
      align-items: center;
      margin-bottom: 20rpx;

      .dimension-icon {
        font-size: 28rpx;
        color: #00c9a7;
      }

      .dimension-name {
        font-size: 26rpx;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .dimension-score {
      display: flex;
      gap: 6rpx;
      align-items: baseline;
      margin-bottom: 16rpx;

      .score-text {
        font-size: 36rpx;
        font-weight: 700;
      }

      .score-max {
        font-size: 22rpx;
        font-weight: 500;
        color: #94a3b8;
      }
    }

    .dimension-bar {
      height: 10rpx;
      overflow: hidden;
      background: #f1f5f9;
      border-radius: 6rpx;

      .dimension-progress {
        position: relative;
        height: 100%;
        border-radius: 6rpx;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);

        &::after {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          content: '';
          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
          animation: shimmer 2s infinite;
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.radar-chart-section {
  .radar-chart-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 20rpx;
    background: #fff;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  }
}

.feedback-section {
  .feedback-group {
    padding: 28rpx 24rpx;
    margin-bottom: 24rpx;
    background: #fff;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      display: flex;
      gap: 10rpx;
      align-items: center;
      margin-bottom: 24rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;

      .group-icon {
        font-size: 26rpx;
      }
    }

    .feedback-item {
      display: flex;
      gap: 16rpx;
      align-items: flex-start;
      padding: 16rpx;
      margin-bottom: 20rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      .feedback-icon {
        flex-shrink: 0;
        margin-top: 2rpx;
        font-size: 24rpx;
      }

      .feedback-content {
        flex: 1;
        font-size: 26rpx;
        font-weight: 400;
        line-height: 1.6;
      }

      &.strength {
        background: rgba(82, 196, 26, 0.05);
        border: 1rpx solid rgba(82, 196, 26, 0.1);

        .feedback-icon {
          color: #52c41a;
        }

        .feedback-content {
          color: #1e293b;
        }
      }

      &.weakness {
        background: rgba(250, 140, 22, 0.05);
        border: 1rpx solid rgba(250, 140, 22, 0.1);

        .feedback-icon {
          color: #fa8c16;
        }

        .feedback-content {
          color: #64748b;
        }
      }

      &.improvement {
        background: rgba(0, 201, 167, 0.05);
        border: 1rpx solid rgba(0, 201, 167, 0.1);

        .feedback-icon {
          color: #00c9a7;
        }

        .feedback-content {
          color: #1e293b;
        }
      }
    }
  }
}

.recommendation-list {
  .recommendation-item {
    display: flex;
    align-items: center;
    padding: 28rpx 24rpx;
    margin-bottom: 16rpx;
    background: #fff;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    border-radius: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
      transform: translateY(-2rpx);
    }

    .recommendation-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64rpx;
      height: 64rpx;
      margin-right: 24rpx;
      font-size: 28rpx;
      color: #fff;
      background: linear-gradient(135deg, #00c9a7, #4fd1c7);
      border-radius: 50%;
      box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
    }

    .recommendation-content {
      flex: 1;

      .recommendation-title {
        display: block;
        margin-bottom: 8rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
      }

      .recommendation-desc {
        font-size: 24rpx;
        line-height: 1.5;
        color: #64748b;
      }
    }

    .recommendation-arrow {
      font-size: 28rpx;
      color: #cbd5e1;
    }
  }
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 20rpx;
  margin-bottom: 32rpx;

  .action-btn {
    display: flex;
    gap: 10rpx;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    font-size: 28rpx;
    font-weight: 600;
    border: none;
    border-radius: 50rpx;
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
    }

    &.primary-btn {
      color: #fff;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      box-shadow: 0 8rpx 20rpx rgba(0, 201, 167, 0.3);

      &:active {
        box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
      }
    }

    &.recommend-btn {
      color: #fff;
      background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
      box-shadow: 0 8rpx 20rpx rgba(124, 58, 237, 0.3);

      &:active {
        box-shadow: 0 4rpx 12rpx rgba(124, 58, 237, 0.4);
      }
    }

    &.secondary-btn {
      color: #64748b;
      background: #fff;
      border: 2rpx solid #e2e8f0;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

      &:active {
        background: #f8fafc;
        border-color: #cbd5e1;
      }
    }
  }
}

.bottom-actions {
  padding: 0 20rpx;

  .home-btn {
    display: flex;
    gap: 10rpx;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #00c9a7;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border: 2rpx solid rgba(0, 201, 167, 0.2);
    border-radius: 50rpx;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 201, 167, 0.05);
      border-color: rgba(0, 201, 167, 0.3);
    }
  }
}
</style>
