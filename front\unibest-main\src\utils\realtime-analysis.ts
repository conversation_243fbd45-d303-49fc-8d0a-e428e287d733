/**
 * 实时分析管理器
 * 处理面试过程中的实时多模态分析
 */

import {
  createAudioStreamAnalysis,
  createVideoStreamAnalysis,
  comprehensiveAnalysis,
  type AudioAnalysisResult,
  type VideoAnalysisResult,
  type TextAnalysisResult,
  type MultimodalAnalysisResult,
} from '@/service/interview-room'

export interface RealTimeAnalysisManager {
  // SSE连接
  audioStream?: EventSource
  videoStream?: EventSource

  // 状态
  isActive: boolean

  // 方法
  start: (sessionId: string, token: string) => void
  stop: () => void
  uploadMediaChunk: (
    chunks: Blob[],
    sessionId: string,
    userAnswer: string,
    jobPosition?: string,
  ) => Promise<void>
}

export interface AnalysisCallbacks {
  onAudioResult?: (result: AudioAnalysisResult) => void
  onVideoResult?: (result: VideoAnalysisResult) => void
  onTextResult?: (result: TextAnalysisResult) => void
  onError?: (error: string) => void
  onProgress?: (progress: number, stage: string) => void
}

/**
 * 创建实时分析管理器
 */
export function createRealTimeAnalysisManager(
  callbacks: AnalysisCallbacks,
): RealTimeAnalysisManager {
  let audioStream: EventSource | undefined
  let videoStream: EventSource | undefined
  let isActive = false

  const start = (sessionId: string, token: string) => {
    if (isActive) return

    isActive = true

    // 启动音频流分析
    audioStream = createAudioStreamAnalysis(
      sessionId,
      token,
      callbacks.onProgress,
      (result) => {
        callbacks.onAudioResult?.(result as AudioAnalysisResult)
      },
      callbacks.onError,
    )

    // 启动视频流分析
    videoStream = createVideoStreamAnalysis(
      sessionId,
      token,
      callbacks.onProgress,
      (result) => {
        callbacks.onVideoResult?.(result as VideoAnalysisResult)
      },
      callbacks.onError,
    )
  }

  const stop = () => {
    isActive = false

    if (audioStream) {
      audioStream.close()
      audioStream = undefined
    }

    if (videoStream) {
      videoStream.close()
      videoStream = undefined
    }
  }

  const uploadMediaChunk = async (
    chunks: Blob[],
    sessionId: string,
    userAnswer: string,
    jobPosition?: string,
  ) => {
    if (chunks.length === 0) return

    try {
      const blob = new Blob(chunks, { type: 'video/webm' })

      // 分离音频和视频
      const audioBlob = new Blob(chunks, { type: 'audio/webm' })
      const videoBlob = blob

      // 创建文件对象
      const audioFile = new File([audioBlob], `audio_${Date.now()}.webm`, { type: 'audio/webm' })
      const videoFile = new File([videoBlob], `video_${Date.now()}.webm`, { type: 'video/webm' })

      // 进行综合分析
      const result = await comprehensiveAnalysis(
        sessionId,
        audioFile,
        videoFile,
        userAnswer,
        jobPosition,
      )

      if (result.code === 200 && result.data) {
        // 分别处理各种分析结果
        if (result.data.audioResult) {
          callbacks.onAudioResult?.(result.data.audioResult)
        }
        if (result.data.videoResult) {
          callbacks.onVideoResult?.(result.data.videoResult)
        }
        if (result.data.textResult) {
          callbacks.onTextResult?.(result.data.textResult)
        }
      } else {
        callbacks.onError?.(`分析失败: ${result.message}`)
      }
    } catch (error) {
      callbacks.onError?.(`上传失败: ${error}`)
    }
  }

  return {
    get audioStream() {
      return audioStream
    },
    get videoStream() {
      return videoStream
    },
    get isActive() {
      return isActive
    },
    start,
    stop,
    uploadMediaChunk,
  }
}

/**
 * 生成智能建议的工具函数
 */
export interface SmartSuggestion {
  id: string
  type: 'speech' | 'video' | 'text' | 'general'
  level: 'info' | 'warning' | 'error'
  title: string
  message: string
  action?: string
  icon: string
  timestamp: number
}

export function generateSuggestionsFromAudio(result: AudioAnalysisResult): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = []

  if (result.clarity < 70) {
    suggestions.push({
      id: `audio-clarity-${Date.now()}`,
      type: 'speech',
      level: 'warning',
      title: '语音清晰度',
      message: '建议放慢语速，提高发音清晰度',
      icon: 'i-mdi-microphone',
      timestamp: Date.now(),
    })
  }

  if (result.fluency < 70) {
    suggestions.push({
      id: `audio-fluency-${Date.now()}`,
      type: 'speech',
      level: 'info',
      title: '语言流畅度',
      message: '减少口头禅，保持语言连贯性',
      icon: 'i-mdi-account-voice',
      timestamp: Date.now(),
    })
  }

  return suggestions
}

export function generateSuggestionsFromVideo(result: VideoAnalysisResult): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = []

  if (result.eyeContact < 70) {
    suggestions.push({
      id: `video-eye-${Date.now()}`,
      type: 'video',
      level: 'info',
      title: '眼神交流',
      message: '建议多看镜头，增加眼神交流',
      icon: 'i-mdi-eye',
      timestamp: Date.now(),
    })
  }

  if (result.posture < 70) {
    suggestions.push({
      id: `video-posture-${Date.now()}`,
      type: 'video',
      level: 'warning',
      title: '坐姿仪态',
      message: '保持端正坐姿，展现专业形象',
      icon: 'i-mdi-human-handsup',
      timestamp: Date.now(),
    })
  }

  return suggestions
}

export function generateSuggestionsFromText(result: TextAnalysisResult): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = []

  if (result.starStructure < 70) {
    suggestions.push({
      id: `text-structure-${Date.now()}`,
      type: 'text',
      level: 'info',
      title: '回答结构',
      message: '建议使用STAR法则组织回答',
      icon: 'i-mdi-format-list-bulleted',
      timestamp: Date.now(),
    })
  }

  if (result.skillMatching < 70) {
    suggestions.push({
      id: `text-keywords-${Date.now()}`,
      type: 'text',
      level: 'warning',
      title: '关键词匹配',
      message: '回答中缺少相关技术关键词',
      icon: 'i-mdi-tag-multiple',
      timestamp: Date.now(),
    })
  }

  return suggestions
}

/**
 * 更新情绪数据的工具函数
 */
export function updateEmotionFromDetection(detectedEmotions: string[]) {
  const emotionData = {
    happy: 0,
    neutral: 0,
    sad: 0,
    angry: 0,
    surprised: 0,
  }

  // 根据检测到的情绪更新数据
  detectedEmotions.forEach((emotion) => {
    if (emotion in emotionData) {
      emotionData[emotion as keyof typeof emotionData] += 100 / detectedEmotions.length
    }
  })

  return emotionData
}
