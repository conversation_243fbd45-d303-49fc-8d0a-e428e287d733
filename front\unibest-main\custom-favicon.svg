<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e7d32;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="32" height="32" rx="6" ry="6" fill="url(#mainGradient)"/>
  
  <!-- 学习图标组合 -->
  <g transform="translate(6, 6)">
    <!-- 书本 -->
    <rect x="2" y="8" width="16" height="12" rx="1" ry="1" fill="#ffffff" opacity="0.9"/>
    <rect x="2" y="8" width="16" height="2" fill="#ffffff"/>
    
    <!-- 书本内容线条 -->
    <line x1="4" y1="12" x2="16" y2="12" stroke="#4CAF50" stroke-width="1" opacity="0.8"/>
    <line x1="4" y1="15" x2="14" y2="15" stroke="#4CAF50" stroke-width="1" opacity="0.8"/>
    <line x1="4" y1="18" x2="15" y2="18" stroke="#4CAF50" stroke-width="1" opacity="0.8"/>
    
    <!-- 问答气泡 -->
    <circle cx="14" cy="6" r="4" fill="#ffffff" stroke="#4CAF50" stroke-width="1.5"/>
    <text x="14" y="8.5" text-anchor="middle" font-family="Arial, sans-serif" font-size="5" font-weight="bold" fill="#4CAF50">?</text>
  </g>
</svg>