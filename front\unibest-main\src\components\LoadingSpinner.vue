<script setup lang="ts">
/**
 * @description 通用加载状态组件
 * 支持自定义加载文本、容器高度、背景色等
 * 兼容H5端和微信小程序端
 * <AUTHOR>
 */

// 定义组件属性
interface Props {
  /**
   * @description 加载提示文本
   * @default "加载中..."
   */
  text?: string
  /**
   * @description 容器高度
   * @default "calc(100vh - 88rpx)"
   */
  height?: string
  /**
   * @description 是否显示背景
   * @default true
   */
  showBackground?: boolean
  /**
   * @description 背景色
   * @default "transparent"
   */
  backgroundColor?: string
  /**
   * @description 主题色
   * @default "#00c9a7"
   */
  themeColor?: string
}

// 使用 defineProps 定义属性
const props = withDefaults(defineProps<Props>(), {
  text: '加载中...',
  height: 'calc(100vh - 88rpx)',
  showBackground: true,
  backgroundColor: 'transparent',
  themeColor: '#00c9a7',
})
// 计算加载容器的类名
const getLoadingContainerClass = () => {
  return {
    'with-background': props.showBackground,
  }
}
</script>

<template>
  <view
    class="loading-container"
    :class="getLoadingContainerClass()"
    :style="{
      height: height,
      backgroundColor: backgroundColor,
    }"
  >
    <view class="loading-spinner" :style="{ borderTopColor: themeColor }"></view>
    <text class="loading-text">{{ text }}</text>
  </view>
</template>

<style lang="scss" scoped>
/**
 * 加载状态组件样式
 * 兼容H5端和微信小程序端
 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &.with-background {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 24rpx;
    border: 6rpx solid #e2e8f0;
    border-top: 6rpx solid #00c9a7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 28rpx;
    color: #64748b;
    text-align: center;
  }
}

/**
 * 旋转动画
 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
