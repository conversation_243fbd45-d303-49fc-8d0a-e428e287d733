/**
 * 简历模块常量定义
 */

// API接口路径常量
export const RESUME_API_ENDPOINTS = {
  // 简历列表相关
  MY_RESUME_LIST: '/app/user/resume/my',
  RESUME_LIST: '/app/user/resume/list',

  // 简历详情相关
  RESUME_DETAIL: '/app/user/resume',

  // 简历操作相关
  UPLOAD_RESUME: '/app/user/resume/upload',
  RENAME_RESUME: '/app/user/resume/rename',
  SET_DEFAULT_RESUME: '/app/user/resume/default',
  CANCEL_DEFAULT_RESUME: '/app/user/resume/cancel-default',
  GET_DEFAULT_RESUME: '/app/user/resume/default',
  DELETE_RESUME: '/app/user/resume',
  DOWNLOAD_RESUME: '/app/user/resume/download',
  PREVIEW_RESUME: '/app/user/resume/preview',
} as const

// 文件类型相关常量
export const FILE_CONSTANTS = {
  // 支持的文件类型
  SUPPORTED_EXTENSIONS: ['.pdf', '.doc', '.docx'],

  // 文件大小限制(字节)
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB

  // 文件图标映射
  FILE_ICONS: {
    '.pdf': 'i-mdi-file-pdf-box',
    '.doc': 'i-mdi-file-word-box',
    '.docx': 'i-mdi-file-word-box',
    default: 'i-mdi-file-document',
  },
} as const

// 简历状态常量
export const RESUME_STATUS = {
  NORMAL: '0',
  DISABLED: '1',
} as const

// 默认简历标识常量
export const DEFAULT_RESUME_FLAG = {
  NO: 0,
  YES: 1,
} as const

// 通知消息常量
export const RESUME_MESSAGES = {
  SUCCESS: {
    UPLOAD: '简历上传成功',
    RENAME: '简历重命名成功',
    SET_DEFAULT: '设置默认简历成功',
    CANCEL_DEFAULT: '取消默认简历成功',
    DELETE: '简历删除成功',
    DOWNLOAD: '简历下载中...',
    PREVIEW: '正在预览简历...',
  },
  ERROR: {
    UPLOAD_FAILED: '简历上传失败',
    RENAME_FAILED: '简历重命名失败',
    SET_DEFAULT_FAILED: '设置默认简历失败',
    DELETE_FAILED: '简历删除失败',
    DOWNLOAD_FAILED: '简历下载失败',
    PREVIEW_FAILED: '简历预览失败',
    FILE_TOO_LARGE: '文件大小超过限制',
    UNSUPPORTED_FILE_TYPE: '不支持的文件类型',
    NETWORK_ERROR: '网络连接失败，请检查网络',
    SERVER_ERROR: '服务器错误，请稍后重试',
  },
  INFO: {
    LOADING: '正在加载...',
    PROCESSING: '正在处理...',
  },
} as const
