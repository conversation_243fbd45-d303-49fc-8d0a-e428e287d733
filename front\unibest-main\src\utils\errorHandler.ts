/**
 * 错误处理和降级方案工具
 * 提供网络异常处理、错误重试、降级策略等功能
 */

import { smartCache } from './smartCache'
import { performanceMonitor } from './performanceMonitor'

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  API_ERROR = 'API_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  message: string
  code?: string | number
  timestamp: string
  retryable: boolean
  fallbackData?: any
}

// 重试配置
export interface RetryConfig {
  maxRetries: number
  retryDelay: number
  backoffFactor: number
  retryableErrors: ErrorType[]
}

// 降级策略配置
export interface FallbackConfig {
  enableFallback: boolean
  fallbackData: any
  fallbackMessage: string
  cacheTimeout: number
}

class ErrorHandler {
  private readonly DEFAULT_RETRY_CONFIG: RetryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    backoffFactor: 2,
    retryableErrors: [ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR],
  }

  private errorLog: ErrorInfo[] = []
  private maxLogSize = 100

  /**
   * @description 处理错误并返回错误信息
   * @param error 原始错误对象
   * @param context 错误上下文信息
   */
  handleError(error: any, context: string = ''): ErrorInfo {
    const errorInfo = this.parseError(error, context)
    this.logError(errorInfo)
    this.showUserFriendlyMessage(errorInfo)
    return errorInfo
  }

  /**
   * @description 带重试的异步函数执行
   * @param fn 要执行的异步函数
   * @param retryConfig 重试配置
   */
  async executeWithRetry<T>(
    fn: () => Promise<T>,
    retryConfig: Partial<RetryConfig> = {},
  ): Promise<T> {
    const config = { ...this.DEFAULT_RETRY_CONFIG, ...retryConfig }
    let lastError: any

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        const errorInfo = this.parseError(error)

        // 如果不是可重试的错误，直接抛出
        if (!config.retryableErrors.includes(errorInfo.type)) {
          throw error
        }

        // 如果已达到最大重试次数，抛出错误
        if (attempt === config.maxRetries) {
          throw error
        }

        // 等待后重试
        const delay = config.retryDelay * Math.pow(config.backoffFactor, attempt)
        await this.sleep(delay)

        console.log(`重试第 ${attempt + 1} 次，延迟 ${delay}ms`)
      }
    }

    throw lastError
  }

  /**
   * @description 带降级策略的API调用
   * @param apiCall API调用函数
   * @param fallbackConfig 降级配置
   */
  async callWithFallback<T>(apiCall: () => Promise<T>, fallbackConfig: FallbackConfig): Promise<T> {
    const cacheKey = `api_${this.generateCacheKey(apiCall.toString())}`

    try {
      // 尝试正常API调用
      performanceMonitor.startTiming('api.call')
      const result = await this.executeWithRetry(apiCall)
      performanceMonitor.endTiming('api.call')

      // 成功时缓存结果到智能缓存
      if (fallbackConfig.enableFallback) {
        await smartCache.set(cacheKey, result, {
          ttl: fallbackConfig.cacheTimeout,
          tags: ['api', 'fallback'],
        })
        performanceMonitor.incrementCounter('cache.api-cached')
      }

      return result
    } catch (error) {
      const errorInfo = this.handleError(error, 'API调用失败')
      performanceMonitor.incrementCounter('api.errors')

      // 如果启用降级策略
      if (fallbackConfig.enableFallback) {
        console.warn('API调用失败，使用降级数据:', fallbackConfig.fallbackMessage)

        // 尝试从智能缓存获取数据
        const cachedResult = await smartCache.get<T>(cacheKey)
        if (cachedResult) {
          performanceMonitor.incrementCounter('cache.fallback-hits')
          return cachedResult
        }

        // 使用预设的降级数据
        if (fallbackConfig.fallbackData) {
          performanceMonitor.incrementCounter('cache.fallback-default')
          return fallbackConfig.fallbackData
        }
      }

      throw error
    }
  }

  /**
   * @description 网络状态检查
   */
  checkNetworkStatus(): Promise<boolean> {
    return new Promise((resolve) => {
      // 检查网络连接状态
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          resolve(isConnected)
        },
        fail: () => {
          resolve(false)
        },
      })
    })
  }

  /**
   * @description 网络状态监听
   * @param callback 网络状态变化回调
   */
  watchNetworkStatus(callback: (isOnline: boolean) => void): void {
    uni.onNetworkStatusChange((res) => {
      callback(res.isConnected)

      if (!res.isConnected) {
        uni.showToast({
          title: '网络连接断开',
          icon: 'none',
          duration: 1000,
        })
      } else {
        uni.showToast({
          title: '网络已连接',
          icon: 'success',
          duration: 1000,
        })
      }
    })
  }

  /**
   * @description 获取错误日志
   */
  getErrorLog(): ErrorInfo[] {
    return [...this.errorLog]
  }

  /**
   * @description 清空错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * @description 解析错误类型和信息
   */
  private parseError(error: any, context: string = ''): ErrorInfo {
    let type = ErrorType.UNKNOWN_ERROR
    let message = '未知错误'
    let code: string | number | undefined
    let retryable = false

    if (error.errMsg) {
      // uni-app 的错误格式
      if (error.errMsg.includes('request:fail')) {
        type = ErrorType.NETWORK_ERROR
        message = '网络连接失败'
        retryable = true
      } else if (error.errMsg.includes('timeout')) {
        type = ErrorType.TIMEOUT_ERROR
        message = '请求超时'
        retryable = true
      }
    } else if (error.response) {
      // HTTP 响应错误
      type = ErrorType.API_ERROR
      code = error.response.status
      message = error.response.data?.message || `请求失败 (${code})`
      retryable = (typeof code === 'number' && code >= 500) || code === 429 // 服务器错误或限流时可重试
    } else if (error.message) {
      message = error.message

      if (message.includes('Network') || message.includes('fetch')) {
        type = ErrorType.NETWORK_ERROR
        retryable = true
      } else if (message.includes('timeout')) {
        type = ErrorType.TIMEOUT_ERROR
        retryable = true
      } else if (message.includes('JSON') || message.includes('parse')) {
        type = ErrorType.DATA_ERROR
      }
    }

    return {
      type,
      message: context ? `${context}: ${message}` : message,
      code,
      timestamp: new Date().toISOString(),
      retryable,
    }
  }

  /**
   * @description 记录错误日志
   */
  private logError(errorInfo: ErrorInfo): void {
    this.errorLog.push(errorInfo)

    // 保持日志大小限制
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }

    // 控制台输出
    console.error('错误处理:', errorInfo)

    // 可以在这里添加发送错误日志到服务器的逻辑
  }

  /**
   * @description 显示用户友好的错误消息
   */
  private showUserFriendlyMessage(errorInfo: ErrorInfo): void {
    let userMessage = ''

    switch (errorInfo.type) {
      case ErrorType.NETWORK_ERROR:
        userMessage = '网络连接失败，请检查网络设置'
        break
      case ErrorType.TIMEOUT_ERROR:
        userMessage = '请求超时，请稍后重试'
        break
      case ErrorType.API_ERROR:
        if (errorInfo.code === 401) {
          userMessage = '身份验证失败，请重新登录'
        } else if (errorInfo.code === 403) {
          userMessage = '权限不足，无法访问'
        } else if (errorInfo.code === 404) {
          userMessage = '请求的资源不存在'
        } else if (errorInfo.code && typeof errorInfo.code === 'number' && errorInfo.code >= 500) {
          userMessage = '服务器出现问题，请稍后重试'
        } else {
          userMessage = '请求失败，请稍后重试'
        }
        break
      case ErrorType.DATA_ERROR:
        userMessage = '数据格式错误，请刷新页面'
        break
      default:
        userMessage = '操作失败，请稍后重试'
    }

    uni.showToast({
      title: userMessage,
      icon: 'none',
      duration: 3000,
    })
  }

  /**
   * @description 缓存API结果
   */
  private cacheResult(key: string, data: any, timeout: number): void {
    const cacheData = {
      data,
      timestamp: Date.now(),
      timeout,
    }

    try {
      uni.setStorageSync(`cache_${key}`, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('缓存结果失败:', error)
    }
  }

  /**
   * @description 获取缓存的结果
   */
  private getCachedResult<T>(key: string): T | null {
    try {
      const cachedData = uni.getStorageSync(`cache_${key}`)
      if (!cachedData) return null

      const parsed = JSON.parse(cachedData)
      const now = Date.now()

      // 检查是否过期
      if (now - parsed.timestamp > parsed.timeout) {
        uni.removeStorageSync(`cache_${key}`)
        return null
      }

      return parsed.data
    } catch (error) {
      console.warn('获取缓存结果失败:', error)
      return null
    }
  }

  /**
   * @description 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * @description 生成缓存键
   */
  private generateCacheKey(input: string): string {
    // Simple hash function for cache key generation
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16)
  }
}

// 导出单例实例
export const errorHandler = new ErrorHandler()

// 导出工具函数
export { ErrorHandler }
