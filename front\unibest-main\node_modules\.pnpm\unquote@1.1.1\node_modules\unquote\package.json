{"name": "unquote", "version": "1.1.1", "description": "Remove wrapping quotes from a string.", "main": "index.js", "scripts": {"test": "node test/*"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"tape": "^2.13.4"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "https://github.com/lakenen/node-unquote.git"}, "bugs": {"url": "https://github.com/lakenen/node-unquote/issues"}, "homepage": "https://github.com/lakenen/node-unquote", "keywords": ["string", "unquote", "quotes"]}