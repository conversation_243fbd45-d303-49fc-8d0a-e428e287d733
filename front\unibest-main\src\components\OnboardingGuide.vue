<script setup lang="ts">
import { ref, computed } from 'vue'
import type { UserGrowthStage } from '@/types/onboarding'

// Props
interface Props {
  visible: boolean
  userStage?: UserGrowthStage
}

const props = withDefaults(defineProps<Props>(), {
  userStage: 'new_user',
})

// Emits
const emit = defineEmits<{
  close: []
  startAssessment: []
  skip: []
}>()

// 响应式数据
const currentStep = ref(0)

// 步骤数据
const steps = ref([
  { title: '欢迎', icon: 'i-mdi-hand-wave' },
  { title: '个性化', icon: 'i-mdi-account-cog' },
  { title: '评估', icon: 'i-mdi-clipboard-check' },
])

// 方法
function nextStep() {
  if (currentStep.value < steps.value.length - 1) {
    currentStep.value++
  }
}

function previousStep() {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

function startAssessment() {
  emit('startAssessment')
}

function skipGuide() {
  emit('skip')
}

function handleClose() {
  emit('close')
}
</script>

<template>
  <view v-if="visible" class="onboarding-overlay">
    <view class="onboarding-content">
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="handleClose">
        <text class="i-mdi-close"></text>
      </view>

      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view
          v-for="(step, index) in steps"
          :key="index"
          class="step-dot"
          :class="{ active: index === currentStep, completed: index < currentStep }"
        >
          <text v-if="index < currentStep" class="i-mdi-check"></text>
          <text v-else>{{ index + 1 }}</text>
        </view>
      </view>

      <!-- 步骤内容 -->
      <view class="step-content">
        <!-- 第一步：欢迎 -->
        <view v-if="currentStep === 0" class="step-welcome">
          <view class="welcome-icon">
            <text class="i-mdi-hand-wave"></text>
          </view>
          <text class="welcome-title">欢迎来到智能面试平台！</text>
          <text class="welcome-desc">
            我是你的AI面试助手，将为你提供个性化的面试指导和能力提升建议。
            让我们开始这段成长之旅吧！
          </text>
          <view class="feature-list">
            <view class="feature-item">
              <text class="feature-icon i-mdi-brain"></text>
              <text class="feature-text">AI智能分析你的面试表现</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon i-mdi-chart-line"></text>
              <text class="feature-text">实时追踪你的能力成长</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon i-mdi-lightbulb"></text>
              <text class="feature-text">个性化推荐学习内容</text>
            </view>
          </view>
        </view>

        <!-- 第二步：个性化体验 -->
        <view v-if="currentStep === 1" class="step-personalization">
          <view class="personalization-icon">
            <text class="i-mdi-account-cog"></text>
          </view>
          <text class="personalization-title">专属于你的智能体验</text>
          <text class="personalization-desc">
            基于你的能力评估，我会为你定制专属的学习计划和面试建议
          </text>
          <view class="benefit-list">
            <view class="benefit-item">
              <view class="benefit-icon">
                <text class="i-mdi-target"></text>
              </view>
              <view class="benefit-content">
                <text class="benefit-title">精准定位</text>
                <text class="benefit-desc">准确识别你的强项和弱项</text>
              </view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <text class="i-mdi-rocket"></text>
              </view>
              <view class="benefit-content">
                <text class="benefit-title">快速提升</text>
                <text class="benefit-desc">针对性训练，高效提升面试技能</text>
              </view>
            </view>
            <view class="benefit-item">
              <view class="benefit-icon">
                <text class="i-mdi-trophy"></text>
              </view>
              <view class="benefit-content">
                <text class="benefit-title">成就解锁</text>
                <text class="benefit-desc">学习成果可视化，激励持续进步</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 第三步：评估邀请 -->
        <view v-if="currentStep === 2" class="step-assessment">
          <view class="assessment-icon">
            <text class="i-mdi-clipboard-check"></text>
          </view>
          <text class="assessment-title">开始你的能力评估</text>
          <text class="assessment-desc">
            完成一个简短的能力评估，帮助我更好地了解你的现状， 为你制定专属的学习计划
          </text>
          <view class="assessment-features">
            <view class="assessment-feature">
              <text class="feature-number">5</text>
              <text class="feature-label">分钟完成</text>
            </view>
            <view class="assessment-feature">
              <text class="feature-number">6</text>
              <text class="feature-label">个维度</text>
            </view>
            <view class="assessment-feature">
              <text class="feature-number">100%</text>
              <text class="feature-label">个性化</text>
            </view>
          </view>
          <view class="assessment-preview">
            <text class="preview-title">评估包含：</text>
            <view class="preview-items">
              <text class="preview-item">• 专业知识掌握程度</text>
              <text class="preview-item">• 逻辑思维分析能力</text>
              <text class="preview-item">• 语言表达沟通技巧</text>
              <text class="preview-item">• 抗压应变处理能力</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 导航按钮 -->
      <view class="navigation-buttons">
        <button v-if="currentStep > 0" class="nav-btn secondary" @click="previousStep">
          上一步
        </button>

        <button v-if="currentStep < steps.length - 1" class="nav-btn primary" @click="nextStep">
          下一步
        </button>

        <button
          v-if="currentStep === steps.length - 1"
          class="nav-btn primary"
          @click="startAssessment"
        >
          开始评估
        </button>
      </view>

      <!-- 底部操作 -->
      <view class="bottom-actions">
        <text class="skip-link" @click="skipGuide">跳过引导</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.onboarding-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.onboarding-content {
  position: relative;
  width: 90%;
  max-width: 640rpx;
  background: #fff;
  border-radius: 32rpx;
  padding: 48rpx 32rpx 32rpx;
  animation: slideUp 0.4s ease-out;
}

.close-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  border-radius: 50%;

  &:active {
    background: #e0e0e0;
  }
}

.step-indicator {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 48rpx;
}

.step-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  background: #f0f0f0;
  color: #999;
  transition: all 0.3s ease;

  &.active {
    background: #00c9a7;
    color: #fff;
    transform: scale(1.1);
  }

  &.completed {
    background: #00c9a7;
    color: #fff;
  }
}

.step-content {
  min-height: 480rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* 欢迎步骤样式 */
.step-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.welcome-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #00c9a7, #00b39a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  margin-bottom: 32rpx;
  animation: bounce 2s infinite;
}

.welcome-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 16rpx;
}

.welcome-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fc;
  border-radius: 16rpx;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  font-size: 24rpx;
  color: #00c9a7;
  background: rgba(0, 201, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #333;
}

/* 个性化步骤样式 */
.step-personalization {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.personalization-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  margin-bottom: 32rpx;
}

.personalization-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 16rpx;
}

.personalization-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32rpx;
}

.benefit-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.benefit-icon {
  width: 48rpx;
  height: 48rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #667eea;
  flex-shrink: 0;
}

.benefit-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #222;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
}

/* 评估步骤样式 */
.step-assessment {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.assessment-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  margin-bottom: 32rpx;
}

.assessment-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 16rpx;
}

.assessment-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32rpx;
}

.assessment-features {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 32rpx;
}

.assessment-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.feature-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #f5576c;
}

.feature-label {
  font-size: 24rpx;
  color: #666;
}

.assessment-preview {
  background: #f8f9fc;
  border-radius: 16rpx;
  padding: 24rpx;
  width: 100%;
}

.preview-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.preview-items {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.preview-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 导航按钮 */
.navigation-buttons {
  display: flex;
  gap: 16rpx;
  justify-content: center;
  margin-top: 32rpx;
}

.nav-btn {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;

  &.primary {
    background: #00c9a7;
    color: #fff;

    &:active {
      background: #00b39a;
      transform: scale(0.95);
    }
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;

    &:active {
      background: #e0e0e0;
    }
  }
}

/* 底部操作 */
.bottom-actions {
  display: flex;
  justify-content: center;
  margin-top: 24rpx;
}

.skip-link {
  font-size: 26rpx;
  color: #999;

  &:active {
    color: #666;
  }
}

/* 动画 */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(100rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
</style>
