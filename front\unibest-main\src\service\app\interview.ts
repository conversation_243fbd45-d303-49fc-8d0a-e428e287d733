/* eslint-disable */
// @ts-ignore
import { CustomRequestOptions } from '@/interceptors/request'
import { httpGet, httpPost, httpPut, httpDelete } from '@/utils/http'

// 面试记录类型定义
export interface InterviewRecord {
  id: number
  jobName: string
  company: string
  icon: string
  difficulty: string
  questionCount: number
  date: string
  time: string
  duration: string
  totalScore: number
  status: 'completed' | 'in-progress' | 'cancelled'
  timeAgo: string
  dimensions: {
    professional: number
    communication: number
    logic: number
    innovation: number
  }
  category: string
}

// 统计数据类型定义
export interface Statistics {
  totalInterviews: number
  averageScore: number
  monthlyImprovement: number
  improvementPercent: number
  currentLevel: string
  nextLevelProgress: number
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  category?: string
  [key: string]: any // 添加索引签名
}

// 分页响应
export interface PaginatedResponse<T> {
  records: T[]
  total: number
  hasMore: boolean
  page: number
  pageSize: number
}

/**
 * @description 获取面试历史记录
 * @param params 分页参数
 * @returns 历史记录列表
 */
export async function getHistoryRecords({
  params,
  options,
}: {
  params: PaginationParams
  options?: CustomRequestOptions
}) {
  return httpGet<PaginatedResponse<InterviewRecord>>('/app/interview/history', params, options)
}

/**
 * @description 获取统计数据
 * @returns 统计数据
 */
export async function getStatistics({
  options,
}: {
  options?: CustomRequestOptions
} = {}) {
  return httpGet<Statistics>('/app/interview/user/statistics', {}, options)
}

/**
 * @description 获取更多历史记录
 * @param params 分页参数，包含lastId
 * @returns 更多历史记录
 */
export async function getMoreHistoryRecords({
  params,
  options,
}: {
  params: PaginationParams & { lastId?: number; [key: string]: any }
  options?: CustomRequestOptions
}) {
  return httpGet<PaginatedResponse<InterviewRecord>>('/api/interview/history/more', params, options)
}

/**
 * @description 获取面试详情
 * @param id 面试记录ID
 * @returns 面试详情
 */
export async function getInterviewDetail({
  params,
  options,
}: {
  params: { id: number }
  options?: CustomRequestOptions
}) {
  const { id } = params
  return httpGet<InterviewRecord & { 
    questions: Array<{
      id: number
      question: string
      answer: string
      score: number
      feedback: string
    }>
  }>(`/app/interview/job/detail`, {
    jobId: id,
  }, options)
}

/**
 * @description 删除面试记录
 * @param id 面试记录ID
 * @returns 删除结果
 */
export async function deleteInterviewRecord({
  params,
  options,
}: {
  params: { id: number }
  options?: CustomRequestOptions
}) {
  const { id } = params
  return httpDelete<{ success: boolean }>(`/api/interview/${id}`, {}, options)
}

/**
 * @description 获取筛选选项
 * @returns 筛选选项列表
 */
export async function getFilterOptions({
  options,
}: {
  options?: CustomRequestOptions
} = {}) {
  return httpGet<Array<{
    id: string
    name: string
    icon: string
    count: number
  }>>('/api/interview/filter-options', {}, options)
} 