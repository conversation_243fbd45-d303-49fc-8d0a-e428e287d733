import { http } from '@/utils/http'

/**
 * @description 题库详情接口定义
 */
export interface QuestionBankDetail {
  id: string
  code: string
  title: string
  description: string
  icon: string
  color: string
  difficulty: string
  totalQuestions: number
  practiceCount: number
  progress: number
  categories: string[]
  isBookmarked: boolean
}

/**
 * @description 题目接口定义
 */
export interface Question {
  id: string
  title: string
  difficulty: string
  practiceCount: number
  correctRate: number
  commentCount: number
  category: string
  tags?: string[]
  acceptanceRate?: number
  isCompleted?: boolean
  description?: string
}

/**
 * @description 题目详情接口定义
 */
export interface QuestionDetail {
  id: string
  title: string
  description: string
  content: string
  difficulty: string
  tags: string[]
  acceptanceRate: number
  isCompleted: boolean
  practiceCount: number
  correctRate: number
  commentCount: number
  category: string
  bankId: string
  bankTitle: string
  createdAt: string
  updatedAt: string
}

/**
 * @description 题目查询参数接口定义
 */
export interface QuestionQueryParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  difficulty?: string
  category?: string
  completed?: boolean
  orderBy?: string
  orderDirection?: string
}

/**
 * @description 获取题库详细信息
 * @param bankId 题库ID
 * @returns 题库详细信息
 */
export const getQuestionBankDetail = (bankId: string) => {
  return http.get<QuestionBankDetail>(`/app/learning/question-banks/${bankId}/detail`)
}

/**
 * @description 获取题库分类题目
 * @param bankId 题库ID
 * @returns 按分类组织的题目列表
 */
export const getQuestionsByCategory = (
  bankId: string,
  pageNum: number,
  pageSize: number,
  category: string,
) => {
  return http.get<Record<string, Question[]>>(
    `/app/learning/question-banks/${bankId}/questions-by-category`,
    {
      pageNum,
      pageSize,
      category,
    },
  )
}

/**
 * @description 获取推荐题目
 * @param bankId 题库ID
 * @param limit 限制数量
 * @returns 推荐题目列表
 */
export const getRecommendedQuestions = (bankId: string, limit = 5) => {
  return http.get<Question[]>(`/app/learning/question-banks/${bankId}/recommended-questions`, {
    params: { limit },
  })
}

/**
 * @description 获取题库题目列表（支持筛选和搜索）
 * @param bankId 题库ID
 * @param params 查询参数
 * @returns 题目列表响应
 */
export const getQuestionList = (bankId: string, params: QuestionQueryParams) => {
  return http.get<{
    list: Question[]
    total: number
    page: number
    pageSize: number
  }>(`/app/learning/question-banks/${bankId}/questions`, {
    params: {
      ...params,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
    },
  })
}

/**
 * @description 获取题目详情
 * @param questionId 题目ID
 * @returns 题目详情
 */
export const getQuestionDetail = (questionId: string) => {
  return http.get<QuestionDetail>(`/app/learning/questions/${questionId}`)
}

/**
 * @description 搜索题目
 * @param bankId 题库ID
 * @param params 搜索参数
 * @returns 题目列表
 */
export const searchQuestions = (
  bankId: string,
  params: {
    keyword?: string
    difficulty?: string
    category?: string
    completed?: boolean
  },
) => {
  return http.get<Question[]>(`/app/learning/question-banks/${bankId}/questions/search`, {
    params,
  })
}

/**
 * @description 切换题库收藏状态
 * @param bankId 题库ID
 * @returns 收藏状态响应
 */
export const toggleQuestionBankBookmark = (bankId: string) => {
  return http.post<{
    isBookmarked: boolean
    message: string
  }>(`/app/learning/question-banks/${bankId}/toggle-bookmark`)
}
