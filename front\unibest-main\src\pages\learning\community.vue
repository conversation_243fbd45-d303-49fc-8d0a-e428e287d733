<script setup lang="ts">
import { ref } from 'vue'

// 在线用户数
const onlineUsers = ref(156)

// 社区分类
const communityTabs = ref([
  { key: 'all', name: '全部', count: 128 },
  { key: 'questions', name: '提问', count: 45 },
  { key: 'sharing', name: '分享', count: 32 },
  { key: 'discussion', name: '讨论', count: 28 },
  { key: 'experience', name: '经验', count: 23 },
])

// 当前选中的分类
const activeTab = ref('all')

// 帖子列表数据
const postList = ref([
  {
    id: 1,
    type: 'questions',
    title: '面试中如何更好地回答算法问题？',
    content: '最近在准备算法工程师面试，经常在面试中遇到算法题但是表达不清楚思路，求大神指点...',
    author: {
      id: 1,
      name: '小明同学',
      avatar: '/static/avatar1.jpg',
      level: 3,
    },
    publishTime: '2小时前',
    viewCount: 234,
    likeCount: 12,
    commentCount: 8,
    tags: ['算法', '面试技巧'],
    isHot: true,
    hasImage: false,
  },
  {
    id: 2,
    type: 'sharing',
    title: '分享我的面试通关秘籍 📚',
    content: '经过3个月的准备，终于拿到了心仪公司的offer！分享一下我的学习方法和面试经验...',
    author: {
      id: 2,
      name: '技术达人',
      avatar: '/static/avatar2.jpg',
      level: 5,
    },
    publishTime: '4小时前',
    viewCount: 567,
    likeCount: 45,
    commentCount: 23,
    tags: ['经验分享', '求职'],
    isHot: true,
    hasImage: true,
  },
  {
    id: 3,
    type: 'discussion',
    title: '关于动态规划的学习方法讨论',
    content: '动态规划一直是我的弱项，想和大家讨论一下有效的学习方法...',
    author: {
      id: 3,
      name: '努力小菜鸟',
      avatar: '/static/avatar3.jpg',
      level: 2,
    },
    publishTime: '6小时前',
    viewCount: 189,
    likeCount: 8,
    commentCount: 15,
    tags: ['动态规划', '学习方法'],
    isHot: false,
    hasImage: false,
  },
  {
    id: 4,
    type: 'experience',
    title: '从零基础到算法工程师的学习路径',
    content: '回顾我的学习历程，从完全零基础到现在能够独立解决算法问题，分享一些心得体会...',
    author: {
      id: 4,
      name: '编程老司机',
      avatar: '/static/avatar4.jpg',
      level: 6,
    },
    publishTime: '1天前',
    viewCount: 892,
    likeCount: 78,
    commentCount: 34,
    tags: ['学习路径', '经验'],
    isHot: false,
    hasImage: true,
  },
])

// 热门话题
const hotTopics = ref([
  { id: 1, name: '面试技巧', count: 234, icon: '💼' },
  { id: 2, name: '算法学习', count: 189, icon: '🧮' },
  { id: 3, name: '求职经验', count: 156, icon: '🎯' },
  { id: 4, name: 'LeetCode', count: 123, icon: '💻' },
])

/**
 * 返回上一页
 */
const goBack = () => {
  uni.navigateBack()
}

/**
 * 切换分类标签
 * @param tabKey 标签key
 */
const switchTab = (tabKey: string) => {
  activeTab.value = tabKey
}

/**
 * 获取帖子类型图标
 * @param type 帖子类型
 */
const getPostTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    questions: '❓',
    sharing: '📢',
    discussion: '💬',
    experience: '📖',
  }
  return iconMap[type] || '📝'
}

/**
 * 获取帖子类型名称
 * @param type 帖子类型
 */
const getPostTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    questions: '提问',
    sharing: '分享',
    discussion: '讨论',
    experience: '经验',
  }
  return nameMap[type] || '帖子'
}

/**
 * 获取用户等级颜色
 * @param level 用户等级
 */
const getUserLevelColor = (level: number) => {
  if (level >= 6) return 'text-purple-600'
  if (level >= 4) return 'text-blue-600'
  if (level >= 2) return 'text-green-600'
  return 'text-gray-600'
}

/**
 * 查看帖子详情
 * @param post 帖子信息
 */
const viewPostDetail = (post: any) => {
  uni.navigateTo({
    url: `/pages/learning/post-detail?id=${post.id}`,
  })
}

/**
 * 点赞帖子
 * @param post 帖子信息
 */
const likePost = (post: any) => {
  post.likeCount++
  uni.showToast({
    title: '点赞成功',
    icon: 'success',
  })
}

/**
 * 发布新帖子
 */
const createPost = () => {
  uni.navigateTo({
    url: '/pages/learning/create-post',
  })
}

/**
 * 查看热门话题
 * @param topic 话题信息
 */
const viewHotTopic = (topic: any) => {
  uni.navigateTo({
    url: `/pages/learning/topic?id=${topic.id}`,
  })
}

// 页面初始化处理
const initPage = () => {
  console.log('学习社区页面加载完成')
}

// 页面加载时执行初始化
initPage()
</script>

<template>
  <view class="community-container bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <view class="nav-bar bg-gradient-to-r from-teal-500 to-teal-600 text-white">
      <view class="status-bar"></view>
      <view class="nav-content flex items-center justify-between px-4 py-3">
        <view class="flex items-center">
          <button class="nav-btn mr-3" @click="goBack">
            <view class="i-fa-solid-arrow-left text-lg"></view>
          </button>
          <text class="text-lg font-medium">学习社区</text>
        </view>
        <button class="nav-btn" @click="createPost">
          <view class="i-fa-solid-plus text-lg"></view>
        </button>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view scroll-y class="community-content">
      <!-- 社区概览 -->
      <view class="community-overview bg-white mx-4 mt-4 rounded-xl p-6">
        <view class="flex items-center justify-between mb-4">
          <text class="text-lg font-bold text-gray-800">社区动态</text>
          <view class="flex items-center text-sm text-gray-600">
            <view class="w-2 h-2 bg-green-500 rounded-full mr-2"></view>
            <text>{{ onlineUsers }} 人在线</text>
          </view>
        </view>

        <!-- 热门话题 -->
        <view class="hot-topics mb-4">
          <text class="text-sm font-bold text-gray-800 mb-3 block">热门话题</text>
          <view class="topics-grid grid grid-cols-2 gap-3">
            <view
              v-for="topic in hotTopics"
              :key="topic.id"
              class="topic-card bg-gray-50 rounded-lg p-3 transition-all duration-200"
              @click="viewHotTopic(topic)"
            >
              <view class="flex items-center">
                <text class="text-xl mr-2">{{ topic.icon }}</text>
                <view class="flex-1">
                  <text class="font-bold text-gray-800 text-sm block">{{ topic.name }}</text>
                  <text class="text-xs text-gray-500">{{ topic.count }} 讨论</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分类标签 -->
      <view class="tabs-section bg-white mx-4 mt-4 rounded-xl p-4">
        <scroll-view scroll-x class="tabs-scroll">
          <view class="flex space-x-3 pb-1">
            <button
              v-for="tab in communityTabs"
              :key="tab.key"
              class="tab-btn flex-shrink-0 px-4 py-2 text-sm rounded-lg transition-all duration-200"
              :class="
                activeTab === tab.key
                  ? 'bg-teal-500 text-white'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              "
              @click="switchTab(tab.key)"
            >
              <text>{{ tab.name }}</text>
              <text class="ml-1 text-xs opacity-75">({{ tab.count }})</text>
            </button>
          </view>
        </scroll-view>
      </view>

      <!-- 帖子列表 -->
      <view class="posts-section mx-4 mt-4 pb-8">
        <view class="posts-list space-y-3">
          <view
            v-for="post in postList"
            :key="post.id"
            class="post-card bg-white rounded-lg p-4 border border-gray-200 transition-all duration-200"
            @click="viewPostDetail(post)"
          >
            <!-- 帖子头部 -->
            <view class="post-header flex items-start justify-between mb-3">
              <view class="flex items-center flex-1">
                <image
                  class="w-10 h-10 rounded-full mr-3"
                  :src="post.author.avatar"
                  mode="aspectFill"
                />
                <view class="flex-1">
                  <view class="flex items-center mb-1">
                    <text class="font-bold text-gray-800 text-sm mr-2">{{ post.author.name }}</text>
                    <view
                      class="level-badge px-2 py-0.5 text-xs rounded-full"
                      :class="`bg-gray-100 ${getUserLevelColor(post.author.level)}`"
                    >
                      Lv.{{ post.author.level }}
                    </view>
                  </view>
                  <text class="text-xs text-gray-500">{{ post.publishTime }}</text>
                </view>
              </view>

              <!-- 帖子类型标识 -->
              <view class="flex items-center">
                <view
                  v-if="post.isHot"
                  class="hot-badge bg-red-100 text-red-600 px-2 py-0.5 text-xs rounded-full mr-2"
                >
                  🔥 热门
                </view>
                <view class="type-badge bg-teal-100 text-teal-600 px-2 py-0.5 text-xs rounded-full">
                  {{ getPostTypeIcon(post.type) }} {{ getPostTypeName(post.type) }}
                </view>
              </view>
            </view>

            <!-- 帖子内容 -->
            <view class="post-content mb-4">
              <text class="font-bold text-gray-800 text-base mb-2 block">{{ post.title }}</text>
              <text class="text-gray-600 text-sm leading-relaxed block">{{ post.content }}</text>
            </view>

            <!-- 帖子标签 -->
            <view class="post-tags mb-4">
              <view class="flex flex-wrap gap-2">
                <view
                  v-for="tag in post.tags"
                  :key="tag"
                  class="tag bg-gray-100 text-gray-600 px-2 py-1 text-xs rounded"
                >
                  # {{ tag }}
                </view>
              </view>
            </view>

            <!-- 帖子统计 -->
            <view class="post-stats flex items-center justify-between">
              <view class="flex items-center space-x-4 text-sm text-gray-500">
                <view class="flex items-center">
                  <view class="i-fa-solid-eye mr-1"></view>
                  <text>{{ post.viewCount }}</text>
                </view>
                <view class="flex items-center">
                  <view class="i-fa-solid-comment mr-1"></view>
                  <text>{{ post.commentCount }}</text>
                </view>
              </view>

              <button
                class="like-btn flex items-center px-3 py-1 rounded-lg transition-colors duration-200"
                @click.stop="likePost(post)"
              >
                <view class="i-fa-solid-heart text-red-500 mr-1"></view>
                <text class="text-sm text-gray-600">{{ post.likeCount }}</text>
              </button>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more text-center py-8">
          <button class="load-more-btn bg-gray-100 text-gray-600 px-6 py-3 rounded-lg text-sm">
            加载更多
          </button>
        </view>
      </view>
    </scroll-view>

    <!-- 发帖按钮 -->
    <view class="floating-btn fixed bottom-20 right-6 z-50">
      <button
        class="fab bg-teal-500 text-white w-14 h-14 rounded-full shadow-lg flex items-center justify-center"
        @click="createPost"
      >
        <view class="i-fa-solid-pencil text-xl"></view>
      </button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.community-container {
  min-height: 100vh;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.status-bar {
  // #ifdef H5
  height: 0;
  // #endif

  // #ifdef MP-WEIXIN
  height: var(--status-bar-height);
  // #endif
}

.nav-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.community-content {
  margin-top: 70px;
  height: calc(100vh - 70px);

  // #ifdef MP-WEIXIN
  margin-top: calc(70px + var(--status-bar-height));
  height: calc(100vh - 70px - var(--status-bar-height));
  // #endif
}

.topic-card {
  // #ifdef H5
  &:hover {
    background-color: #e5e7eb;
    transform: translateY(-1px);
  }
  // #endif
}

.tab-btn {
  // #ifdef H5
  &:hover {
    transform: translateY(-1px);
  }
  // #endif
}

.post-card {
  // #ifdef H5
  &:hover {
    border-color: #14b8a6;
    box-shadow: 0 4px 12px rgba(20, 184, 166, 0.15);
  }
  // #endif
}

.like-btn {
  // #ifdef H5
  &:hover {
    background-color: #fef2f2;
  }
  // #endif
}

.fab {
  // #ifdef H5
  &:hover {
    background-color: #0f766e;
    transform: scale(1.05);
  }
  // #endif
}

.tabs-scroll {
  white-space: nowrap;
}

// 网格布局兼容性
.grid {
  display: flex;
  flex-wrap: wrap;
  margin: -6px;

  &.grid-cols-2 > view {
    width: calc(50% - 12px);
    margin: 6px;
  }
}

// 间距调整
.space-y-3 > view:not(:first-child) {
  margin-top: 12px;
}

.space-x-3 > view:not(:first-child) {
  margin-left: 12px;
}

.space-x-4 > view:not(:first-child) {
  margin-left: 16px;
}

.gap-2 > view:not(:first-child) {
  margin-left: 8px;
}

.gap-3 > view:not(:first-child) {
  margin-left: 12px;
}
</style>
