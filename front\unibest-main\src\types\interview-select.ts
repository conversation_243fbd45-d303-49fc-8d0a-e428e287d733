/**
 * 面试选择页面相关类型定义
 * 包含岗位列表、分类、排序、筛选等功能的类型
 */

import type { DifficultyLevel } from './interview'

// 基础响应类型
export interface IResData<T = any> {
  code: number
  message: string
  data: T
}

// 岗位基本信息（用于列表显示）
export interface JobItem {
  id: number
  categoryId: number
  name: string
  company: string
  logo?: string
  difficulty: DifficultyLevel
  duration: number
  questionCount: number
  tags: string[]
  description: string
  interviewers: number
  passRate: string
  createdAt?: string
  updatedAt?: string
}

// 岗位分类信息
export interface JobCategory {
  id: number
  name: string
  icon: string
  color: string
  description?: string
  jobCount?: number
}

// 面试模式信息
export interface InterviewMode {
  id: string
  name: string
  description: string
  icon: string
  color: string
  duration: number
  difficulty: DifficultyLevel
  features?: string[]
}

// 排序选项
export interface SortOption {
  key: string
  name: string
  description: string
  icon: string
}

// 筛选条件
export interface FilterConditions {
  categoryId?: number
  difficulty?: DifficultyLevel[]
  duration?: {
    min?: number
    max?: number
  }
  tags?: string[]
  company?: string[]
  passRate?: {
    min?: number
    max?: number
  }
  interviewerCount?: {
    min?: number
    max?: number
  }
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
}

// 获取岗位列表请求参数
export interface GetJobListParams extends PaginationParams {
  categoryId?: number
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: FilterConditions
}

// 获取岗位列表响应
export interface GetJobListResponse {
  jobs: JobItem[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
  categories?: JobCategory[]
  recommendedJobs?: JobItem[]
}

// 获取岗位分类请求参数
export interface GetCategoriesParams {
  includeJobCount?: boolean
}

// 获取岗位分类响应
export interface GetCategoriesResponse {
  categories: JobCategory[]
  total: number
}

// 获取面试模式请求参数
export interface GetInterviewModesParams {
  // 目前无需特殊参数
}

// 获取面试模式响应
export interface GetInterviewModesResponse {
  modes: InterviewMode[]
}

// 搜索建议请求参数
export interface GetSearchSuggestionsParams {
  keyword: string
  limit?: number
}

// 搜索建议项
export interface SuggestionItem {
  text: string
  type: string // job, category, question
  entityId?: number
}

// 搜索建议响应
export interface GetSearchSuggestionsResponse {
  suggestions: SuggestionItem[]
  originalQuery?: string
}

// 创建面试会话请求参数
export interface CreateInterviewSessionParams {
  jobId: number
  mode: string
  resumeUrl?: string
  customizedQuestions?: string[]
}

// 创建面试会话响应
export interface CreateInterviewSessionResponse {
  sessionId: string
  jobId: number
  mode: string
  estimatedDuration: number
  questionCount: number
  sessionToken?: string
  expiresAt?: string
}

// 收藏岗位请求参数
export interface FavoriteJobSelectParams {
  jobId: number
  isFavorited: boolean
}

// 设备检测结果
export interface DeviceCheckResult {
  camera: boolean
  microphone: boolean
  network: boolean
  allPassed: boolean
  errorMessages?: string[]
}

// 统计信息
export interface JobStatistics {
  totalJobs: number
  totalInterviews: number
  avgPassRate: number
  popularCategories: Array<{
    categoryId: number
    name: string
    jobCount: number
  }>
  hotJobs: JobItem[]
}

// 用户偏好设置
export interface UserPreferences {
  preferredCategories: number[]
  preferredDifficulty: DifficultyLevel[]
  preferredDuration: {
    min: number
    max: number
  }
  blacklistCompanies: string[]
  favoriteCompanies: string[]
}

// 面试历史记录简要信息
export interface InterviewHistoryItem {
  id: string
  jobId: number
  jobName: string
  company: string
  mode: string
  score: number
  completedAt: string
  duration: number
}

// 推荐理由
export interface RecommendationReason {
  type: 'match_profile' | 'hot_job' | 'similar_background' | 'skill_fit' | 'difficulty_suitable'
  reason: string
  confidence: number
} 