<script setup lang="ts">
import { ref, onMounted, watch, computed, defineProps, onUnmounted } from 'vue'
import uCharts from '@qiun/ucharts'

/**
 * @description 成长曲线组件
 * @prop growthData 成长数据数组
 * @prop width 宽度
 * @prop height 高度
 * <AUTHOR>
 */
interface GrowthDataItem {
  date: string
  专业知识?: number
  逻辑思维?: number
  语言表达?: number
  心理素质?: number
  团队协作?: number
}

interface Props {
  growthData: GrowthDataItem[]
  width?: number | string
  height?: number | string
}

const props = defineProps<Props>()

// 生成唯一的canvas ID
const canvasId = ref(`growth-chart-${Date.now()}-${Math.floor(Math.random() * 1000)}`)

// uCharts 实例
let growthChart = null

// 计算实际的宽高（统一使用px单位）
const actualWidth = computed(() => {
  const w = props.width || 700
  if (typeof w === 'number') {
    return uni.upx2px(w)
  }
  const value = parseInt(w.toString())
  if (w.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || 700
})

const actualHeight = computed(() => {
  const h = props.height || 500
  if (typeof h === 'number') {
    return uni.upx2px(h)
  }
  const value = parseInt(h.toString())
  if (h.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || 500
})

// 能力维度配置 - 优化颜色搭配
const abilities = [
  { key: '专业知识', color: '#00c9a7', name: '专业知识' },
  { key: '逻辑思维', color: '#4fd1c7', name: '逻辑思维' },
  { key: '语言表达', color: '#f59e0b', name: '语言表达' },
  { key: '心理素质', color: '#ef4444', name: '心理素质' },
  { key: '团队协作', color: '#8b5cf6', name: '团队协作' },
]

/**
 * @description 获取当前组件实例
 */
function getCurrentInstance() {
  // #ifdef MP-WEIXIN
  return getCurrentPages()[getCurrentPages().length - 1]
  // #endif
}

/**
 * @description 绘制成长曲线图
 */
const drawGrowthChart = () => {
  if (!props.growthData || props.growthData.length === 0) {
    console.warn('数据为空，无法绘制')
    return
  }

  // 获取canvas绘图上下文
  const context = uni.createCanvasContext(canvasId.value, getCurrentInstance())
  if (!context) {
    console.error('无法创建Canvas上下文')
    return
  }

  // 准备x轴数据（日期）
  const categories = props.growthData.map((item) => {
    // 简化日期显示，只显示月-日
    return item.date.length > 8 ? item.date.substring(5) : item.date
  })

  // 准备系列数据
  const seriesData = abilities.map((ability) => {
    const data = props.growthData.map((item) => {
      const value = item[ability.key as keyof GrowthDataItem] as number
      return typeof value === 'number' ? value : null
    })

    return {
      name: ability.name,
      data: data,
      color: ability.color,
      show: true,
      type: 'line',
      style: 'curve', // 平滑曲线
      pointShape: 'circle',
      pointSize: 4,
      lineWidth: 2.5,
    }
  })

  // 配置图表选项 - 优化字体和样式
  const chartOptions = {
    type: 'line',
    context: context,
    canvasId: canvasId.value,
    width: actualWidth.value,
    height: actualHeight.value,
    // #ifdef MP-WEIXIN
    $this: getCurrentInstance(),
    // #endif
    animation: true,
    fontSize: uni.upx2px(22),
    fontColor: '#334155',
    background: 'transparent',
    pixelRatio: 1,
    dataLabel: false,
    legend: {
      show: true,
      position: 'bottom',
      fontSize: uni.upx2px(22),
      fontColor: '#475569',
      itemGap: uni.upx2px(20),
      margin: uni.upx2px(15),
    },
    categories: categories,
    series: seriesData,
    xAxis: {
      type: 'grid',
      gridColor: 'rgba(71, 85, 105, 0.1)',
      gridType: 'dash',
      dashLength: 4,
      fontSize: uni.upx2px(22),
      fontColor: '#64748b',
      lineColor: 'rgba(71, 85, 105, 0.2)',
      calibration: true,
    },
    yAxis: {
      gridColor: 'rgba(71, 85, 105, 0.1)',
      gridType: 'dash',
      dashLength: 4,
      fontSize: uni.upx2px(22),
      fontColor: '#64748b',
      lineColor: 'rgba(71, 85, 105, 0.2)',
      min: 0,
      max: 100,
      splitNumber: 5,
      calibration: true,
      unit: '分',
    },
    extra: {
      line: {
        type: 'curve',
        width: 2.5,
        activeType: 'hollow',
        linearType: 'none',
        onShadow: true,
        shadowColor: 'rgba(0, 201, 167, 0.15)',
        shadowOffsetX: 0,
        shadowOffsetY: 3,
        shadowBlur: 8,
        dataLabel: false,
      },
      tooltip: {
        showBox: true,
        bgColor: 'rgba(51, 65, 85, 0.95)',
        bgOpacity: 0.95,
        fontColor: '#ffffff',
        fontSize: uni.upx2px(24),
        borderRadius: 8,
        borderColor: 'rgba(0, 201, 167, 0.3)',
        borderWidth: 1,
        padding: [8, 12],
      },
    },
  }

  // 如果已存在图表实例，先销毁
  if (growthChart) {
    growthChart = null
  }

  // 创建新的图表实例
  try {
    growthChart = new uCharts(chartOptions)
    console.log('uCharts成长曲线图创建成功')
  } catch (error) {
    console.error('创建uCharts成长曲线图失败:', error)
  }
}

/**
 * @description 获取趋势图标
 */
const getTrendIcon = (ability: string) => {
  if (props.growthData.length < 2) return 'i-fa-solid-minus'

  const latest = props.growthData[props.growthData.length - 1][ability as keyof GrowthDataItem] || 0
  const previous =
    props.growthData[props.growthData.length - 2][ability as keyof GrowthDataItem] || 0
  const diff = (latest as number) - (previous as number)

  if (diff > 0) return 'i-fa-solid-arrow-up'
  if (diff < 0) return 'i-fa-solid-arrow-down'
  return 'i-fa-solid-minus'
}

/**
 * @description 获取趋势颜色
 */
const getTrendColor = (ability: string) => {
  if (props.growthData.length < 2) return '#94a3b8'

  const latest = props.growthData[props.growthData.length - 1][ability as keyof GrowthDataItem] || 0
  const previous =
    props.growthData[props.growthData.length - 2][ability as keyof GrowthDataItem] || 0
  const diff = (latest as number) - (previous as number)

  if (diff > 0) return '#00c9a7'
  if (diff < 0) return '#ef4444'
  return '#94a3b8'
}

/**
 * @description 获取趋势值
 */
const getTrendValue = (ability: string) => {
  if (props.growthData.length < 2) return '0'

  const latest = props.growthData[props.growthData.length - 1][ability as keyof GrowthDataItem] || 0
  const previous =
    props.growthData[props.growthData.length - 2][ability as keyof GrowthDataItem] || 0
  const diff = (latest as number) - (previous as number)

  return diff > 0 ? `+${diff}` : diff.toString()
}

/**
 * @description 获取当前分数
 */
const getCurrentScore = (ability: string) => {
  if (props.growthData.length === 0) return 0
  const latest = props.growthData[props.growthData.length - 1][ability as keyof GrowthDataItem] || 0
  return latest as number
}

// 暴露更新方法
const updateChart = () => {
  if (props.growthData && props.growthData.length > 0) {
    drawGrowthChart()
  }
}

// 监听数据变化
watch(
  () => props.growthData,
  (newData) => {
    if (newData && newData.length > 0) {
      setTimeout(() => {
        drawGrowthChart()
      }, 100)
    }
  },
  { deep: true },
)

// 组件挂载后初始化
onMounted(() => {
  console.log('GrowthChart组件已挂载')

  setTimeout(() => {
    if (props.growthData && props.growthData.length > 0) {
      drawGrowthChart()
    }
  }, 300)
})

// 组件卸载时清理
onUnmounted(() => {
  if (growthChart) {
    growthChart = null
  }
})

// 导出方法供父组件调用
defineExpose({
  updateChart,
})
</script>

<template>
  <view class="growth-chart">
    <view v-if="growthData.length > 0" class="chart-container">
      <!-- 图表画布 -->
      <view class="canvas-wrapper">
        <canvas
          :canvas-id="canvasId"
          :id="canvasId"
          :style="{
            width: actualWidth + 'px',
            height: actualHeight + 'px',
          }"
          class="growth-chart-canvas"
          disable-scroll="true"
        />
      </view>

      <!-- 成长趋势指示器 -->
      <view class="growth-indicator" v-if="growthData.length > 1">
        <view class="indicator-header">
          <text class="indicator-title">最新成绩</text>
          <text class="indicator-subtitle">与上次对比</text>
        </view>

        <view class="abilities-grid">
          <view
            class="ability-card"
            v-for="(item, index) in ['专业知识', '逻辑思维', '语言表达', '心理素质', '团队协作']"
            :key="index"
          >
            <view class="ability-header">
              <view class="ability-icon" :style="{ background: abilities[index].color }">
                <text
                  :class="{
                    'i-fa-solid-graduation-cap': item === '专业知识',
                    'i-fa-solid-brain': item === '逻辑思维',
                    'i-fa-solid-comments': item === '语言表达',
                    'i-fa-solid-heart': item === '心理素质',
                    'i-fa-solid-users': item === '团队协作',
                  }"
                  class="ability-icon-text"
                ></text>
              </view>
              <text class="ability-name">{{ item }}</text>
            </view>

            <view class="ability-stats">
              <view class="current-score">
                <text class="score-value">{{ getCurrentScore(item) }}</text>
                <text class="score-unit">分</text>
              </view>

              <view class="trend-info">
                <text
                  class="trend-icon"
                  :class="getTrendIcon(item)"
                  :style="{ color: getTrendColor(item) }"
                ></text>
                <text class="trend-value" :style="{ color: getTrendColor(item) }">
                  {{ getTrendValue(item) }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <view class="empty-icon-wrapper">
        <text class="empty-icon i-fa-solid-chart-line"></text>
        <view class="empty-decoration"></view>
      </view>
      <text class="empty-title">暂无成长数据</text>
      <text class="empty-desc">完成面试后将生成您的能力成长曲线</text>
      <view class="empty-action">
        <view class="start-interview-btn">
          <text class="start-text">开始面试</text>
          <text class="i-fa-solid-arrow-right start-icon"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.growth-chart {
  width: 100%;
  min-height: 400rpx;
  position: relative;
}

// 图表容器
.chart-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

// 画布包装器
.canvas-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.growth-chart-canvas {
  display: block;
  border-radius: 16rpx;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 成长趋势指示器
.growth-indicator {
  width: 100%;
}

.indicator-header {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.1);
}

.indicator-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #334155;
  letter-spacing: 0.5rpx;
}

.indicator-subtitle {
  font-size: 22rpx;
  color: #64748b;
}

// 能力网格布局
.abilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

// 能力卡片
.ability-card {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(0, 201, 167, 0.08);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 40rpx;
    height: 40rpx;
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.1), transparent);
    border-radius: 0 0 0 40rpx;
  }

  &:active {
    transform: scale(0.98);
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 250, 252, 0.95) 100%
    );
  }
}

.ability-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.ability-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ability-icon-text {
  font-size: 18rpx;
  color: #ffffff;
}

.ability-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #334155;
  flex: 1;
}

// 能力统计
.ability-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.current-score {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #00c9a7;
  line-height: 1;
}

.score-unit {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.trend-info {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
}

.trend-icon {
  font-size: 18rpx;
}

.trend-value {
  font-size: 20rpx;
  font-weight: 600;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(248, 250, 252, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 80rpx;
    height: 80rpx;
    background: radial-gradient(circle, rgba(0, 201, 167, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: floating 4s ease-in-out infinite;
  }
}

.empty-icon-wrapper {
  position: relative;
  margin-bottom: 32rpx;
}

.empty-icon {
  font-size: 64rpx;
  color: #cbd5e1;
  display: block;
}

.empty-decoration {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #475569;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.empty-desc {
  font-size: 24rpx;
  line-height: 1.6;
  color: #64748b;
  margin-bottom: 32rpx;
  max-width: 400rpx;
}

.empty-action {
  margin-top: 16rpx;
}

.start-interview-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 28rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
  }
}

.start-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.start-icon {
  font-size: 20rpx;
  color: #ffffff;
}

// 动画定义
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-12rpx) rotate(3deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .abilities-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .ability-card {
    padding: 16rpx;
  }

  .ability-icon {
    width: 32rpx;
    height: 32rpx;
  }

  .ability-icon-text {
    font-size: 16rpx;
  }

  .ability-name {
    font-size: 22rpx;
  }

  .score-value {
    font-size: 28rpx;
  }

  .score-unit {
    font-size: 18rpx;
  }

  .trend-value {
    font-size: 18rpx;
  }

  .empty-state {
    min-height: 320rpx;
    padding: 32rpx 24rpx;
  }

  .empty-icon {
    font-size: 56rpx;
  }

  .empty-title {
    font-size: 28rpx;
  }

  .empty-desc {
    font-size: 22rpx;
  }

  .start-text {
    font-size: 24rpx;
  }
}

// H5端优化
/* #ifdef H5 */
.ability-card {
  cursor: pointer;

  &:hover {
    transform: translateY(-4rpx);
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 250, 252, 0.95) 100%
    );
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.12);
  }
}

.start-interview-btn {
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.3);
  }
}
/* #endif */

// 小程序端优化
/* #ifdef MP */
.canvas-wrapper {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}

.ability-card {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}

.empty-state {
  backdrop-filter: blur(6rpx);
  -webkit-backdrop-filter: blur(6rpx);
}
/* #endif */

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .canvas-wrapper {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
    border-color: rgba(0, 201, 167, 0.2);
  }

  .ability-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.7) 0%, rgba(15, 23, 42, 0.8) 100%);
    border-color: rgba(0, 201, 167, 0.15);
  }

  .indicator-title,
  .ability-name {
    color: #e2e8f0;
  }

  .indicator-subtitle,
  .score-unit {
    color: #94a3b8;
  }

  .empty-state {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5) 0%, rgba(15, 23, 42, 0.6) 100%);
    border-color: rgba(0, 201, 167, 0.15);
  }

  .empty-title {
    color: #e2e8f0;
  }

  .empty-desc {
    color: #94a3b8;
  }

  .empty-icon {
    color: #475569;
  }
}
</style>
