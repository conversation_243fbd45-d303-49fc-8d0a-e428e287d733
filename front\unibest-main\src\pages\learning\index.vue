<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import BottomTabBar from '@/components/BottomTabBar.vue'
// @ts-ignore
import NoData from '@/components/NoData.vue'
// 导入类型定义
import type { Ref } from 'vue'
import type {
  QuestionBank,
  // @ts-ignore
} from '@/types/learning'
// 导入API接口
import {
  getLearningStats,
  getResourceCategoryStats,
  getHotBooks,
  getHotVideos,
  getHotQuestionBanks,
  getMajorList, // 添加专业列表API
  type LearningStats,
  type HotBook,
  type HotVideo,
  // @ts-ignore
} from '@/service/learning'
// @ts-ignore
import type { Major } from '@/types/learning'
import {
  LEARNING_STORAGE_KEYS,
  LEARNING_NOTIFICATION_MESSAGES,
  // @ts-ignore
} from '@/types/learning-constants'

// 定义资源分类类型
interface ResourceCategory {
  id: string
  name: string
  icon: string
  color: string
  description: string
  path: string
  count?: number // 添加可选的count属性
}

// activetab
const activeTab: Ref<string> = ref('resources')

// 资源分类数据
const resourceCategories: Ref<ResourceCategory[]> = ref([
  {
    id: 'book',
    name: '面试宝典',
    icon: 'i-fa-solid-book',
    color: 'from-blue-500 to-blue-600',
    description: '精选面试书籍资源',
    path: '/pages/learning/book',
  },
  {
    id: 'video',
    name: '视频课程',
    icon: 'i-fa-solid-video',
    color: 'from-purple-500 to-purple-600',
    description: '专业视频教学课程',
    path: '/pages/learning/video',
  },
  {
    id: 'question',
    name: '题库资源',
    icon: 'i-fa-solid-database',
    color: 'from-green-500 to-green-600',
    description: '海量面试真题题库',
    path: '/pages/learning/resources',
  },
])

// 学习统计数据
const learningStats: Ref<LearningStats> = ref({
  totalHours: 0,
  completedCourses: 0,
  currentStreak: 0,
  weeklyProgress: 0,
})

// 热门书籍
const hotBooks: Ref<HotBook[]> = ref([])

// 热门视频
const hotVideos: Ref<HotVideo[]> = ref([])

// 热门题库
const hotQuestionBanks: Ref<QuestionBank[]> = ref([])

// 加载状态
const isLoading: Ref<boolean> = ref(false)
const loadingError: Ref<string> = ref('')

// 专业选择相关状态
const selectedMajor = ref('computer-science')
const showMajorSelector = ref(false)
const majors: Ref<Major[]> = ref([])

/**
 * @description 加载学习统计数据
 */
const loadLearningStats = async (): Promise<void> => {
  try {
    const res = await getLearningStats()
    if (res.code === 200 && res.data) {
      learningStats.value = res.data
    }
  } catch (error) {
    console.error('加载学习统计失败:', error)
    // 添加演示数据
    learningStats.value = {
      totalHours: 184,
      completedCourses: 24,
      currentStreak: 12,
      weeklyProgress: 12,
    }
  }
}

/**
 * @description 加载热门书籍数据
 */
const loadHotBooks = async (): Promise<void> => {
  try {
    const res = await getHotBooks({ limit: 5 })
    if (res.code === 200 && res.data) {
      hotBooks.value = res.data.map((book) => ({
        ...book,
        tags: book.tags.split(','),
      }))
    }
  } catch (error) {
    console.error('加载热门书籍失败:', error)
    // 演示数据
    hotBooks.value = [
      {
        id: 1,
        title: 'Java面试宝典',
        author: '张三',
        cover: 'https://via.placeholder.com/150',
        rating: 4.5,
        readCount: 1000,
        tags: ['Java', '面试', '宝典'],
      },
    ]
  }
}

/**
 * @description 加载热门视频课程数据
 */
const loadHotVideos = async (): Promise<void> => {
  try {
    const res = await getHotVideos({ limit: 4 })
    if (res.code === 200 && res.data) {
      hotVideos.value = res.data
    }
  } catch (error) {
    console.error('加载热门视频失败:', error)
    // 演示数据
    hotVideos.value = [
      {
        id: 1,
        title: 'Java面试宝典',
        instructor: '张三',
        thumbnail: 'https://via.placeholder.com/150',
        duration: '100分钟',
        viewCount: 1000,
        isFree: true,
        price: 0,
      },
    ]
  }
}

/**
 * @description 加载热门题库数据
 */
const loadHotQuestionBanks = async (): Promise<void> => {
  try {
    const params: any = { limit: 5 }
    // 如果有选择的专业，添加专业筛选
    if (selectedMajor.value) {
      params.majorId = selectedMajor.value
    }

    const res = await getHotQuestionBanks({ params })
    if (res.code === 200 && res.data) {
      hotQuestionBanks.value = res.data
    }
  } catch (error) {
    console.error('加载热门题库失败:', error)
  }
}



/**
 * @description 加载资源分类统计数据
 */
const loadResourceStats = async (): Promise<void> => {
  try {
    const params: any = {}
    // 如果有选择的专业，添加专业筛选
    if (selectedMajor.value) {
      params.majorId = selectedMajor.value
    }

    const res = await getResourceCategoryStats(params)
    if (res.code === 200 && res.data) {
      res.data.forEach((stat: any) => {
        const category = resourceCategories.value.find((c) => c.id === stat.id)
        if (category) {
          category.count = stat.count
        }
      })
    }
  } catch (error) {
    console.error('加载资源统计失败:', error)
  }
}

/**
 * @description 跳转到资源分类页面
 * @param category 资源分类对象
 */
const navigateToCategory = (category: ResourceCategory): void => {
  uni.navigateTo({
    url: category.path,
  })
}

/**
 * @description 查看书籍详情页面
 * @param book 书籍对象
 */
const viewBookDetail = (book: HotBook): void => {
  uni.navigateTo({
    url: `/pages/learning/book-detail?id=${book.id}`,
  })
}

/**
 * @description 查看视频详情页面
 * @param video 视频对象
 */
const viewVideoDetail = (video: HotVideo): void => {
  uni.navigateTo({
    url: `/pages/learning/video-detail?id=${video.id}`,
  })
}

/**
 * @description 查看题库详情页面
 * @param bank 题库对象
 */
const viewQuestionBankDetail = (bank: QuestionBank): void => {
  uni.navigateTo({
    url: `/pages/learning/question-bank?id=${bank.id}`,
  })
}



/**
 * @description 根据进度获取对应的颜色样式
 * @param progress 学习进度百分比
 * @returns 返回对应的颜色样式类名
 */
const getProgressColor = (progress: number): string => {
  if (progress >= 80) return 'bg-green-500'
  if (progress >= 60) return 'bg-blue-500'
  if (progress >= 40) return 'bg-yellow-500'
  return 'bg-red-500'
}

/**
 * @description 根据难度等级获取对应的颜色样式
 * @param difficulty 难度等级
 * @returns 返回对应的颜色样式类名
 */
const getDifficultyColor = (difficulty: string): string => {
  const colorMap: Record<string, string> = {
    简单: 'text-green-600 bg-green-100',
    easy: 'text-green-600 bg-green-100',
    中等: 'text-yellow-600 bg-yellow-100',
    medium: 'text-yellow-600 bg-yellow-100',
    困难: 'text-red-600 bg-red-100',
    hard: 'text-red-600 bg-red-100',
  }
  return colorMap[difficulty] || 'text-gray-600 bg-gray-100'
}



/**
 * @description 加载专业列表
 */
const loadMajors = async (): Promise<void> => {
  try {
    const res = await getMajorList()
    if (res.code === 200 && res.data) {
      majors.value = res.data
      // 从本地存储恢复上次选择的专业
      const savedMajor = uni.getStorageSync(LEARNING_STORAGE_KEYS.SELECTED_MAJOR)
      if (savedMajor && majors.value.some((m) => m.id === savedMajor)) {
        selectedMajor.value = savedMajor
      } else if (majors.value.length > 0) {
        selectedMajor.value = majors.value[0].id
      }
    }
  } catch (error) {
    console.error('加载专业列表失败:', error)
    uni.showToast({
      title: '加载专业列表失败',
      icon: 'error',
      duration: 1500,
    })
  }
}

/**
 * @description 切换专业选择器显示状态
 */
const toggleMajorSelector = (): void => {
  showMajorSelector.value = !showMajorSelector.value
}

/**
 * @description 关闭专业选择器
 */
const closeMajorSelector = (): void => {
  showMajorSelector.value = false
}

/**
 * @description 选择专业
 * @param majorId 专业ID
 */
const selectMajor = (majorId: string): void => {
  selectedMajor.value = majorId
  showMajorSelector.value = false

  // 保存选择到本地存储
  uni.setStorageSync(LEARNING_STORAGE_KEYS.SELECTED_MAJOR, majorId)

  const majorName = majors.value.find((m) => m.id === majorId)?.name || '未知'
  uni.showToast({
    title: LEARNING_NOTIFICATION_MESSAGES.SUCCESS.MAJOR_SWITCHED.replace('{major}', majorName),
    icon: 'success',
    duration: 1500,
  })

  // 重新加载数据以应用新的专业筛选
  reloadDataWithMajor()
}

/**
 * @description 获取专业选择器样式
 * @param majorId 专业ID
 */
const getMajorSelectorClass = (majorId: string): string => {
  return selectedMajor.value === majorId ? 'major-selector-active' : ''
}

/**
 * @description 根据选择的专业重新加载数据
 */
const reloadDataWithMajor = async (): Promise<void> => {
  await Promise.all([loadHotQuestionBanks(), loadResourceStats()])
}

/**
 * @description 初始化页面所有数据
 */
const initPageData = async (): Promise<void> => {
  isLoading.value = true
  loadingError.value = ''

  try {
    // 并行加载所有数据
    await Promise.all([
      loadLearningStats(),
      loadResourceStats(),
      loadHotBooks(),
      loadHotVideos(),
      loadHotQuestionBanks(),
      loadMajors(), // 添加专业数据加载
    ])
  } catch (error) {
    console.error('页面数据加载失败:', error)
    loadingError.value = '数据加载失败，请稍后重试'
    uni.showToast({
      title: '数据加载失败',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 获取视频观看次数
 * @param viewCount 观看次数
 * @returns 返回观看次数
 */
const getViewCount = (viewCount: number): string => {
  // 如果大于10000，则显示10k
  if (viewCount >= 10000) {
    return (viewCount / 10000).toFixed(1) + 'w'
  }
  // 如果大于1000，则显示1k
  if (viewCount >= 1000) {
    return (viewCount / 1000).toFixed(1) + 'k'
  }
  return viewCount.toString()
}

/**
 * @description 页面显示时刷新关键数据
 */
onShow(() => {
  // 刷新学习统计数据和根据专业筛选的数据
  loadLearningStats()
  reloadDataWithMajor()
})

/**
 * @description 页面加载时初始化数据
 */
onLoad(async () => {
  await initPageData()
  // 预加载
  uni.preloadPage({
    url: '/pages/learning/recommend',
  })
  uni.preloadPage({
    url: '/pages/learning/data-center',
  })
  uni.preloadPage({
    url: '/pages/learning/resources',
  })
  uni.preloadPage({
    url: '/pages/learning/video',
  })
  uni.preloadPage({
    url: '/pages/learning/book',
  })
})
</script>

<template>
  <view class="learning-home-container">
    <!-- 顶部导航 - 固定定位 -->
    <view class="nav-wrapper">
      <HeadBar
        title="学习中心"
        :showBack="false"
        :show-right-button="true"
        right-text="专业"
        right-text-width="140"
        @right-click="toggleMajorSelector"
      />
    </view>

    <!-- 专业选择下拉菜单 -->
    <view v-if="showMajorSelector" class="major-selector-overlay" @click="closeMajorSelector">
      <view class="major-selector-dropdown" @click.stop>
        <view class="major-selector-header">
          <text class="major-selector-title">选择专业</text>
          <button class="major-selector-close" @click="closeMajorSelector">
            <view class="i-mdi-close close-icon"></view>
          </button>
        </view>
        <scroll-view class="major-selector-list" scroll-y>
          <view
            v-for="major in majors"
            :key="major.id"
            class="major-selector-item"
            :class="getMajorSelectorClass(major.id)"
            @click="selectMajor(major.id)"
          >
            <view :class="major.icon" class="major-selector-icon"></view>
            <view class="major-selector-info">
              <text class="major-selector-name">{{ major.name }}</text>
              <text class="major-selector-count">{{ major.count }} 题</text>
            </view>
            <view v-if="selectedMajor === major.id" class="i-mdi-check major-selector-check"></view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 主要内容区域 - 可滚动 -->
    <view class="main-wrapper">
      <scroll-view class="main-content" scroll-y enhanced :show-scrollbar="false">
        <!-- 学习统计卡片 -->
        <!-- <view class="stats-card" @click="navigateToDataCenter"> -->
        <view class="stats-card">
          <view class="stats-header">
            <text class="stats-title">我的学习数据</text>
            <view class="stats-header-right">
              <text class="stats-date">本周</text>
              <!-- <view class="i-fa-solid-chevron-right stats-arrow"></view> -->
            </view>
          </view>
          <view class="stats-grid">
            <view class="stat-item">
              <view class="stat-icon bg-blue-100">
                <view class="i-fa-solid-clock text-blue-600"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ learningStats.totalHours }}</text>
                <text class="stat-label">学习时长(h)</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon bg-green-100">
                <view class="i-fa-solid-book-open text-green-600"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ learningStats.completedCourses }}</text>
                <text class="stat-label">完成课程</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon bg-orange-100">
                <view class="i-fa-solid-fire text-orange-600"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ learningStats.currentStreak }}</text>
                <text class="stat-label">连续天数</text>
              </view>
            </view>
          </view>
          <!-- 周进度条 -->
          <view class="weekly-progress">
            <view class="progress-header">
              <text class="progress-label">本周完成度</text>
              <text class="progress-value">{{ learningStats.weeklyProgress }}%</text>
            </view>
            <view class="progress-bar">
              <view
                class="progress-fill"
                :class="getProgressColor(learningStats.weeklyProgress)"
                :style="{ width: learningStats.weeklyProgress + '%' }"
              ></view>
            </view>
          </view>
        </view>



        <!-- 资源分类入口 -->
        <view class="resource-section">
          <view class="section-header-with-major">
            <text class="section-title">学习资源</text>
            <view v-if="majors.length > 0" class="current-major-info">
              <text class="current-major-label">当前专业：</text>
              <text class="current-major-name">
                {{ majors.find((m) => m.id === selectedMajor)?.name || '全部' }}
              </text>
            </view>
          </view>
          <view class="resource-grid" v-if="resourceCategories.length > 0">
            <view
              v-for="category in resourceCategories"
              :key="category.id"
              class="resource-card"
              @click="navigateToCategory(category)"
            >
              <view class="resource-icon-wrapper" :class="`bg-gradient-to-br ${category.color}`">
                <view :class="category.icon" class="resource-icon"></view>
              </view>
              <view class="resource-info">
                <text class="resource-name">{{ category.name }}</text>
                <text class="resource-desc">{{ category.description }}</text>
              </view>
              <view class="resource-arrow">
                <view class="i-fa-solid-chevron-right"></view>
              </view>
            </view>
          </view>
          <NoData
            v-else
            icon="i-fa-solid-folder-open"
            text="暂无资源"
            description="还没有可用的学习资源，稍后再来看看吧"
            custom-class="no-data--compact"
          />
        </view>

        <!-- 热门书籍 -->
        <view class="hot-section">
          <view class="section-header">
            <text class="section-title">热门书籍</text>
            <button class="see-more-btn" @click="navigateToCategory(resourceCategories[0])">
              <text>查看更多</text>
              <view class="i-fa-solid-arrow-right ml-1"></view>
            </button>
          </view>
          <scroll-view class="books-scroll" scroll-x v-if="hotBooks.length > 0">
            <view class="books-list">
              <view
                v-for="book in hotBooks"
                :key="book.id"
                class="book-card"
                @click="viewBookDetail(book)"
              >
                <image class="book-cover" :src="book.cover" mode="aspectFill" />
                <view class="book-info">
                  <text class="book-title">{{ book.title }}</text>
                  <text class="book-author">{{ book.author }}</text>
                  <view class="book-stats">
                    <view class="rating">
                      <view class="i-fa-solid-star text-yellow-500"></view>
                      <text>{{ book.rating }}</text>
                    </view>
                    <text class="read-count">{{ getViewCount(book.readCount) }}人阅读</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
          <NoData
            v-else
            icon="i-fa-solid-book"
            text="暂无书籍"
            description="还没有热门书籍推荐，稍后再来看看吧"
            custom-class="no-data--compact"
          />
        </view>

        <!-- 热门视频 -->
        <view class="hot-section">
          <view class="section-header">
            <text class="section-title">热门视频</text>
            <button class="see-more-btn" @click="navigateToCategory(resourceCategories[1])">
              <text>查看更多</text>
              <view class="i-fa-solid-arrow-right ml-1"></view>
            </button>
          </view>
          <view class="videos-list" v-if="hotVideos.length > 0">
            <view
              v-for="video in hotVideos"
              :key="video.id"
              class="video-card"
              @click="viewVideoDetail(video)"
            >
              <view class="video-thumbnail">
                <image :src="video.thumbnail" mode="aspectFill" />
                <view class="play-overlay">
                  <view class="i-fa-solid-play-circle"></view>
                </view>
                <view class="video-duration">{{ video.duration }}</view>
                <view v-if="video.isFree" class="free-badge">免费</view>
              </view>
              <view class="video-info">
                <text class="video-title">{{ video.title }}</text>
                <view class="video-meta">
                  <text class="instructor">{{ video.instructor }}</text>
                  <text class="view-count">{{ getViewCount(video.viewCount) }}次观看</text>
                </view>
                <text v-if="!video.isFree" class="video-price">¥{{ video.price }}</text>
              </view>
            </view>
          </view>

          <NoData
            v-else
            icon="i-fa-solid-video"
            text="暂无视频"
            description="还没有热门视频课程，稍后再来看看吧"
            custom-class="no-data--compact"
          />
        </view>

        <!-- 热门题库 -->
        <view class="hot-section">
          <view class="section-header">
            <text class="section-title">热门题库</text>
            <button class="see-more-btn" @click="navigateToCategory(resourceCategories[2])">
              <text>查看更多</text>
              <view class="i-fa-solid-arrow-right ml-1"></view>
            </button>
          </view>
          <view class="question-banks-list" v-if="hotQuestionBanks.length > 0">
            <view
              v-for="bank in hotQuestionBanks"
              :key="bank.id"
              class="question-bank-card"
              @click="viewQuestionBankDetail(bank)"
            >
              <view class="bank-header">
                <view class="bank-icon">
                  <view :class="bank.icon"></view>
                </view>
                <view class="bank-main">
                  <text class="bank-title">{{ bank.title }}</text>
                  <view class="bank-meta">
                    <text class="difficulty-badge" :class="getDifficultyColor(bank.difficulty)">
                      {{ bank.difficulty }}
                    </text>
                    <text class="question-count">{{ bank.totalQuestions || 0 }}题</text>
                    <text class="practice-count">{{ getViewCount(bank.practiceCount) }}人练习</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <NoData
            v-else
            icon="i-fa-solid-database"
            text="暂无题库"
            description="还没有热门题库推荐，稍后再来看看吧"
            custom-class="no-data--compact"
          />
        </view>

        <!-- 底部安全距离 -->
        <view class="bottom-safe-area"></view>
      </scroll-view>
    </view>

    <!-- 底部导航栏 - 固定定位 -->
    <view class="bottom-wrapper">
      <BottomTabBar :active-tab="activeTab" />
    </view>
  </view>
</template>

<style scoped lang="scss">
/* ==================== 页面整体布局 ==================== */
.learning-home-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

/* ==================== 顶部导航区域 ==================== */
.nav-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.2);

  // 添加安全距离支持
  // #ifdef MP-WEIXIN
  padding-top: var(--status-bar-height);
  // #endif
}

/* ==================== 主内容区域布局 ==================== */
.main-wrapper {
  position: absolute;
  top: 120rpx;
  left: 0;
  right: 0;
  bottom: 120rpx;

  // #ifdef MP-WEIXIN
  top: calc(120rpx + var(--status-bar-height));
  // #endif
}

.main-content {
  width: 100%;
  height: 100%;
  background: transparent;

  // 优化滚动性能
  // #ifdef MP-WEIXIN
  scroll-with-animation: true;
  // #endif

  // H5端滚动条样式
  // #ifdef H5
  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  // #endif
}

/* ==================== 底部导航区域 ==================== */
.bottom-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  // 添加安全距离支持
  // #ifdef MP-WEIXIN
  padding-bottom: env(safe-area-inset-bottom);
  // #endif

  // #ifdef H5
  padding-bottom: 0;
  // #endif
}

/* ==================== 底部安全距离 ==================== */
.bottom-safe-area {
  height: 60rpx;
  background: transparent;

  // #ifdef MP-WEIXIN
  height: calc(60rpx + env(safe-area-inset-bottom));
  // #endif

  // #ifdef H5
  height: 80rpx;
  // #endif
}

/* ==================== 学习统计卡片 ==================== */
.stats-card {
  margin: 0 32rpx 32rpx;
  padding: 40rpx;
  background: white;
  border-radius: 24rpx;
  border: 2rpx solid #f1f5f9;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  animation: slideInUp 0.6s ease-out 0.1s both;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // #ifdef H5
  &:hover {
    box-shadow: 0 12rpx 40rpx rgba(0, 201, 167, 0.15);
    transform: translateY(-2rpx);
    border-color: #00c9a7;

    .stats-arrow {
      transform: translateX(8rpx);
    }
  }
  // #endif

  &:active {
    transform: scale(0.98);
  }

  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .stats-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .stats-header-right {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .stats-date {
        font-size: 26rpx;
        color: #64748b;
        padding: 8rpx 20rpx;
        background: #f1f5f9;
        border-radius: 20rpx;
      }

      .stats-arrow {
        font-size: 24rpx;
        color: #00c9a7;
        transition: transform 0.3s ease;
      }
    }
  }

  .stats-grid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40rpx;

    .stat-item {
      display: flex;
      align-items: center;
      animation: fadeIn 0.6s ease-out 0.2s both;

      .stat-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        font-size: 36rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

        &.bg-blue-100 {
          background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        }

        &.bg-green-100 {
          background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        }

        &.bg-orange-100 {
          background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
        }
      }

      .stat-info {
        .stat-value {
          display: block;
          font-size: 36rpx;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4rpx;
        }

        .stat-label {
          display: block;
          font-size: 22rpx;
          color: #64748b;
        }
      }
    }
  }

  .weekly-progress {
    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .progress-label {
        font-size: 28rpx;
        color: #475569;
        font-weight: 500;
      }

      .progress-value {
        font-size: 28rpx;
        font-weight: 700;
        color: #00c9a7;
      }
    }

    .progress-bar {
      height: 16rpx;
      background: #e2e8f0;
      border-radius: 8rpx;
      overflow: hidden;
      box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);

      .progress-fill {
        height: 100%;
        border-radius: 8rpx;
        transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(90deg, #00c9a7 0%, #00b294 100%);
        box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.3);

        &.bg-green-500 {
          background: linear-gradient(90deg, #10b981 0%, #059669 100%);
        }

        &.bg-blue-500 {
          background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
        }

        &.bg-yellow-500 {
          background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
        }

        &.bg-red-500 {
          background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
        }
      }
    }
  }
}



/* ==================== 资源分类 ==================== */
.resource-section {
  margin: 0 32rpx 32rpx;

  .section-header-with-major {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    flex-wrap: wrap;
    gap: 16rpx;

    .section-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .current-major-info {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
      border: 1rpx solid #a7f3d0;
      border-radius: 20rpx;

      .current-major-label {
        font-size: 24rpx;
        color: #059669;
        font-weight: 500;
      }

      .current-major-name {
        font-size: 24rpx;
        color: #00c9a7;
        font-weight: 600;
      }
    }
  }

  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 32rpx;
  }

  .resource-grid {
    display: flex;
    flex-direction: column;
    gap: 24rpx;

    .resource-card {
      display: flex;
      align-items: center;
      padding: 32rpx;
      background: white;
      border-radius: 20rpx;
      border: 2rpx solid #f1f5f9;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      animation: slideInUp 0.6s ease-out both;

      @for $i from 1 through 3 {
        &:nth-child(#{$i}) {
          animation-delay: #{$i * 0.1}s;
        }
      }

      // #ifdef H5
      &:hover {
        border-color: #00c9a7;
        box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.15);
        transform: translateX(8rpx);

        .resource-arrow {
          transform: translateX(8rpx);
        }
      }
      // #endif

      &:active {
        transform: scale(0.98);
      }

      .resource-icon-wrapper {
        width: 112rpx;
        height: 112rpx;
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 32rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

        .resource-icon {
          font-size: 48rpx;
          color: white;
        }
      }

      .resource-info {
        flex: 1;

        .resource-name {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 8rpx;
        }

        .resource-desc {
          display: block;
          font-size: 26rpx;
          color: #64748b;
          margin-bottom: 8rpx;
          line-height: 1.5;
        }

        .resource-count {
          display: block;
          font-size: 24rpx;
          color: #00c9a7;
          font-weight: 600;
        }
      }

      .resource-arrow {
        color: #cbd5e1;
        font-size: 32rpx;
        transition: transform 0.3s ease;
      }
    }
  }
}

/* ==================== 热门内容区域 ==================== */
.hot-section {
  margin: 0 32rpx 32rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    position: relative;

    .section-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1e293b;
    }

    .see-more-btn {
      display: flex;
      position: absolute;
      right: 0;
      align-items: center;
      font-size: 26rpx;
      color: #00c9a7;
      background: none;
      border: none;
      font-weight: 500;
      transition: all 0.2s ease;

      // #ifdef H5
      &:hover {
        color: #00b294;
        transform: translateX(4rpx);
      }
      // #endif
    }
  }

  // 书籍列表
  .books-scroll {
    white-space: nowrap;

    .books-list {
      display: inline-flex;
      gap: 24rpx;
      padding-bottom: 8rpx;

      .book-card {
        width: 280rpx;
        background: white;
        border-radius: 20rpx;
        border: 2rpx solid #f1f5f9;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
        overflow: hidden;
        transition: all 0.3s ease;
        animation: fadeIn 0.6s ease-out both;

        @for $i from 1 through 3 {
          &:nth-child(#{$i}) {
            animation-delay: #{$i * 0.1}s;
          }
        }

        // #ifdef H5
        &:hover {
          border-color: #00c9a7;
          box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.15);
          transform: translateY(-8rpx);
        }
        // #endif

        .book-cover {
          width: 100%;
          height: 360rpx;
          transition: transform 0.3s ease;
        }

        .book-info {
          padding: 24rpx;

          .book-title {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .book-author {
            display: block;
            font-size: 24rpx;
            color: #64748b;
            margin-bottom: 16rpx;
          }

          .book-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 22rpx;
            color: #94a3b8;
            margin-bottom: 16rpx;

            .rating {
              display: flex;
              align-items: center;
              gap: 4rpx;
              font-weight: 500;
            }
          }

          .book-tags {
            display: flex;
            gap: 8rpx;
            flex-wrap: wrap;

            .tag {
              padding: 6rpx 16rpx;
              background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
              border: 1rpx solid #a7f3d0;
              border-radius: 16rpx;
              font-size: 20rpx;
              color: #00c9a7;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // 视频列表
  .videos-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;

    .video-card {
      display: flex;
      background: white;
      border-radius: 20rpx;
      border: 2rpx solid #f1f5f9;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
      overflow: hidden;
      transition: all 0.3s ease;
      animation: slideInUp 0.6s ease-out both;

      @for $i from 1 through 2 {
        &:nth-child(#{$i}) {
          animation-delay: #{$i * 0.1}s;
        }
      }

      // #ifdef H5
      &:hover {
        border-color: #00c9a7;
        box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.15);
        transform: translateY(-4rpx);

        .play-overlay {
          opacity: 1;
        }
      }
      // #endif

      .video-thumbnail {
        position: relative;
        width: 240rpx;
        height: 180rpx;
        flex-shrink: 0;

        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          inset: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.4);
          color: white;
          font-size: 64rpx;
          opacity: 0.8;
          transition: opacity 0.3s ease;
          backdrop-filter: blur(2rpx);
        }

        .video-duration {
          position: absolute;
          bottom: 8rpx;
          right: 8rpx;
          padding: 6rpx 12rpx;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          font-size: 20rpx;
          border-radius: 8rpx;
          backdrop-filter: blur(4rpx);
        }

        .free-badge {
          position: absolute;
          top: 8rpx;
          left: 8rpx;
          padding: 6rpx 16rpx;
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
          font-size: 20rpx;
          font-weight: 500;
          border-radius: 12rpx;
          box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
        }
      }

      .video-info {
        flex: 1;
        padding: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .video-title {
          display: block;
          font-size: 30rpx;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 12rpx;
          line-height: 1.4;
        }

        .video-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 24rpx;
          color: #64748b;
          margin-bottom: 12rpx;
        }

        .video-price {
          display: inline-block;
          font-size: 32rpx;
          font-weight: 700;
          color: #ef4444;
          align-self: flex-end;
        }
      }
    }
  }

  // 题库列表
  .question-banks-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;

    .question-bank-card {
      padding: 32rpx;
      background: white;
      border-radius: 20rpx;
      border: 2rpx solid #f1f5f9;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;
      animation: slideInUp 0.6s ease-out both;

      @for $i from 1 through 2 {
        &:nth-child(#{$i}) {
          animation-delay: #{$i * 0.1}s;
        }
      }

      // #ifdef H5
      &:hover {
        border-color: #00c9a7;
        box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.15);
        transform: translateY(-4rpx);
      }
      // #endif

      &:active {
        transform: scale(0.99);
      }

      .bank-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 24rpx;

        .bank-icon {
          width: 96rpx;
          height: 96rpx;
          background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
          border: 2rpx solid #a7f3d0;
          border-radius: 24rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
          font-size: 48rpx;
          color: #00c9a7;
        }

        .bank-main {
          flex: 1;

          .bank-title {
            display: block;
            font-size: 32rpx;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16rpx;
            line-height: 1.3;
          }

          .bank-meta {
            display: flex;
            gap: 24rpx;
            align-items: center;
            font-size: 24rpx;

            .difficulty-badge {
              padding: 6rpx 16rpx;
              border-radius: 16rpx;
              font-weight: 500;
            }

            .question-count,
            .practice-count {
              color: #64748b;
            }
          }
        }
      }

      .bank-progress {
        display: flex;
        align-items: center;
        gap: 24rpx;
        padding: 24rpx;
        background: #f8fafc;
        border-radius: 16rpx;
        margin-top: 24rpx;

        .progress-bar-small {
          flex: 1;
          height: 12rpx;
          background: #e2e8f0;
          border-radius: 6rpx;
          overflow: hidden;

          .progress-fill-small {
            height: 100%;
            background: linear-gradient(90deg, #00c9a7 0%, #00b294 100%);
            border-radius: 6rpx;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
          }
        }

        .completion-rate {
          font-size: 24rpx;
          color: #00c9a7;
          font-weight: 600;
        }
      }
    }
  }
}

/* ==================== 动画定义 ==================== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%,
  60%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* ==================== 辅助样式类 ==================== */
.ml-1 {
  margin-left: 8rpx;
}

.mr-2 {
  margin-right: 16rpx;
}

/* ==================== 难度颜色样式 ==================== */
.text-green-600 {
  color: #059669;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-yellow-600 {
  color: #d97706;
}

.bg-yellow-100 {
  background-color: #fef3c7;
}

.text-red-600 {
  color: #dc2626;
}

.bg-red-100 {
  background-color: #fecaca;
}

.text-gray-600 {
  color: #4b5563;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.text-blue-600 {
  color: #2563eb;
}

.text-yellow-500 {
  color: #eab308;
}

/* ==================== 专业选择器样式 ==================== */
.major-selector-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 200rpx;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
}

.major-selector-dropdown {
  width: 90%;
  max-width: 600rpx;
  max-height: 70vh;
  overflow: hidden;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  animation: slideDown 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.major-selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: #f8fafc;
  border-bottom: 1rpx solid #e2e8f0;

  .major-selector-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .major-selector-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 72rpx;
    height: 72rpx;
    background: #e2e8f0;
    border: none !important;
    border-radius: 50%;
    transition: all 0.2s ease;
    margin-right: 20rpx;

    &:active {
      background: #cbd5e1;
      transform: scale(0.95);
    }

    .close-icon {
      font-size: 28rpx;
      color: #64748b;
    }
  }
}

.major-selector-list {
  max-height: 60vh;
  padding: 16rpx 0;
}

.major-selector-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8fafc;
  }

  &.major-selector-active {
    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
    border-left: 6rpx solid #00c9a7;

    .major-selector-name {
      font-weight: 600;
      color: #00c9a7;
    }
  }

  .major-selector-icon {
    margin-right: 24rpx;
    font-size: 40rpx;
    color: #00c9a7;
  }

  .major-selector-info {
    flex: 1;

    .major-selector-name {
      display: block;
      margin-bottom: 8rpx;
      font-size: 28rpx;
      color: #1e293b;
      transition: all 0.2s ease;
    }

    .major-selector-count {
      font-size: 22rpx;
      color: #64748b;
    }
  }

  .major-selector-check {
    font-size: 32rpx;
    font-weight: 600;
    color: #00c9a7;
  }
}

/* 专业选择器动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-80rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
