<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// 引入类型定义
import type { Ref } from 'vue'

// 定义专业类型
interface Major {
  id: string
  name: string
  icon: string
}

// 定义学习书籍类型
interface StudyBook {
  id: number
  title: string
  icon: string
  progress: number
}

// 定义题库类型
interface QuestionBank {
  id: number
  title: string
  description: string
  questionCount: number
  difficulty: 'easy' | 'medium' | 'hard'
  category: string
  completedCount: number
  rating: number
  isHot: boolean
  tags: string[]
  icon: string
  bgColor: string
}

// 定义统计数据类型
interface TodayStats {
  completed: number
  total: number
  accuracy: number
}

// 定义记忆统计类型
interface MemoryStats {
  todayCount: number
  completed: number
  total: number
}

// 定义筛选选项类型
interface FilterOption {
  key: string
  label: string
  count: number
}

// 当前选择的专业
const currentMajor: Ref<string> = ref('计算机科学')
const showMajorDropdown: Ref<boolean> = ref(false)

// 搜索相关
const searchQuery: Ref<string> = ref('')
const showSearchSuggestions: Ref<boolean> = ref(false)
const showSearchInput: Ref<boolean> = ref(false)

// 筛选相关
const activeFilter: Ref<string> = ref('all')

// 今日统计数据
const todayStats: Ref<TodayStats> = ref({
  completed: 12,
  total: 156,
  accuracy: 85,
})

// 学习资源书籍
const studyBooks: Ref<StudyBook[]> = ref([
  { id: 1, title: '算法导论', icon: '📚', progress: 65 },
  { id: 2, title: '深入理解计算机系统', icon: '💻', progress: 42 },
  { id: 3, title: '设计模式', icon: '🎨', progress: 78 },
  { id: 4, title: '数据库系统概念', icon: '🗄️', progress: 23 },
])

// 智能背诵统计
const memoryStats: Ref<MemoryStats> = ref({
  todayCount: 25,
  completed: 18,
  total: 25,
})

// 专业列表
const majors: Ref<Major[]> = ref([
  { id: 'computer-science', name: '计算机科学', icon: '💻' },
  { id: 'ai-engineering', name: '人工智能', icon: '🤖' },
  { id: 'data-science', name: '数据科学', icon: '📊' },
  { id: 'software-engineering', name: '软件工程', icon: '⚙️' },
  { id: 'cybersecurity', name: '网络安全', icon: '🔒' },
  { id: 'iot-engineering', name: '物联网工程', icon: '🌐' },
])

// 题库数据
const questionBanks: Ref<QuestionBank[]> = ref([
  {
    id: 1,
    title: '数据结构与算法',
    description: '包含栈、队列、树、图等核心数据结构题目',
    questionCount: 156,
    difficulty: 'medium',
    category: 'computer-science',
    completedCount: 45,
    rating: 4.8,
    isHot: true,
    tags: ['算法', '数据结构', '编程'],
    icon: '🌳',
    bgColor: 'from-blue-500 to-blue-600',
  },
  {
    id: 2,
    title: '操作系统原理',
    description: '进程管理、内存管理、文件系统等核心概念',
    questionCount: 89,
    difficulty: 'hard',
    category: 'computer-science',
    completedCount: 23,
    rating: 4.7,
    isHot: false,
    tags: ['操作系统', '进程', '内存'],
    icon: '💾',
    bgColor: 'from-green-500 to-green-600',
  },
  {
    id: 3,
    title: '计算机网络',
    description: 'TCP/IP协议、网络安全、路由算法等',
    questionCount: 124,
    difficulty: 'medium',
    category: 'computer-science',
    completedCount: 67,
    rating: 4.6,
    isHot: true,
    tags: ['网络', 'TCP/IP', '协议'],
    icon: '🌐',
    bgColor: 'from-purple-500 to-purple-600',
  },
  {
    id: 4,
    title: '数据库系统',
    description: 'SQL查询、事务处理、索引优化等',
    questionCount: 98,
    difficulty: 'easy',
    category: 'computer-science',
    completedCount: 78,
    rating: 4.5,
    isHot: false,
    tags: ['数据库', 'SQL', '事务'],
    icon: '🗄️',
    bgColor: 'from-orange-500 to-orange-600',
  },
  {
    id: 5,
    title: '机器学习基础',
    description: '监督学习、无监督学习、深度学习入门',
    questionCount: 142,
    difficulty: 'hard',
    category: 'ai-engineering',
    completedCount: 12,
    rating: 4.9,
    isHot: true,
    tags: ['机器学习', '深度学习', 'AI'],
    icon: '🧠',
    bgColor: 'from-pink-500 to-pink-600',
  },
  {
    id: 6,
    title: '自然语言处理',
    description: '文本分析、语言模型、情感分析等',
    questionCount: 76,
    difficulty: 'hard',
    category: 'ai-engineering',
    completedCount: 8,
    rating: 4.7,
    isHot: false,
    tags: ['NLP', '文本分析', '语言模型'],
    icon: '📝',
    bgColor: 'from-teal-500 to-teal-600',
  },
])

// 搜索建议
const searchSuggestions: Ref<string[]> = ref([
  '数据结构',
  '算法',
  '操作系统',
  '计算机网络',
  '数据库',
  '机器学习',
  '深度学习',
  'SQL',
])

// 筛选选项
const filterOptions: Ref<FilterOption[]> = ref([
  { key: 'all', label: '全部题库', count: 0 },
  { key: 'easy', label: '简单', count: 0 },
  { key: 'medium', label: '中等', count: 0 },
  { key: 'hard', label: '困难', count: 0 },
  { key: 'hot', label: '热门', count: 0 },
  { key: 'completed', label: '已完成', count: 0 },
])

/**
 * @description 根据搜索和筛选条件过滤题库
 */
const filteredQuestionBanks = computed(() => {
  let filtered = questionBanks.value

  // 根据搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (bank) =>
        bank.title.toLowerCase().includes(query) ||
        bank.description.toLowerCase().includes(query) ||
        bank.tags.some((tag) => tag.toLowerCase().includes(query)),
    )
  }

  // 根据筛选条件筛选
  if (activeFilter.value !== 'all') {
    switch (activeFilter.value) {
      case 'easy':
      case 'medium':
      case 'hard':
        filtered = filtered.filter((bank) => bank.difficulty === activeFilter.value)
        break
      case 'hot':
        filtered = filtered.filter((bank) => bank.isHot)
        break
      case 'completed':
        filtered = filtered.filter((bank) => bank.completedCount > 0)
        break
    }
  }

  return filtered
})

/**
 * @description 更新筛选选项的计数
 */
const updateFilterCounts = (): void => {
  filterOptions.value.forEach((option) => {
    switch (option.key) {
      case 'all':
        option.count = questionBanks.value.length
        break
      case 'easy':
        option.count = questionBanks.value.filter((bank) => bank.difficulty === 'easy').length
        break
      case 'medium':
        option.count = questionBanks.value.filter((bank) => bank.difficulty === 'medium').length
        break
      case 'hard':
        option.count = questionBanks.value.filter((bank) => bank.difficulty === 'hard').length
        break
      case 'hot':
        option.count = questionBanks.value.filter((bank) => bank.isHot).length
        break
      case 'completed':
        option.count = questionBanks.value.filter((bank) => bank.completedCount > 0).length
        break
    }
  })
}

/**
 * @description 选择专业并更新题库筛选
 * @param major 专业对象
 */
const selectMajor = (major: Major): void => {
  currentMajor.value = major.name
  showMajorDropdown.value = false
  // 这里可以根据专业筛选题库
}

/**
 * @description 处理搜索输入事件
 */
const handleSearch = (): void => {
  showSearchSuggestions.value = false
  // 搜索逻辑已在计算属性中处理
}

/**
 * @description 选择搜索建议并填入搜索框
 * @param suggestion 搜索建议文本
 */
const selectSuggestion = (suggestion: string): void => {
  searchQuery.value = suggestion
  showSearchSuggestions.value = false
}

/**
 * @description 设置筛选条件
 * @param filterKey 筛选条件的键值
 */
const setFilter = (filterKey: string): void => {
  activeFilter.value = filterKey
}

/**
 * @description 处理题库卡片点击事件，跳转到题库详情页
 * @param bank 题库对象
 */
const handleQuestionBankClick = (bank: QuestionBank): void => {
  // 跳转到题库详情页面
  uni.navigateTo({
    url: `/pages/learning/question-bank-detail?id=${bank.id}&title=${encodeURIComponent(bank.title)}`,
  })
}

/**
 * @description 获取难度等级的中文显示文本
 * @param difficulty 英文难度等级
 * @returns 返回中文难度等级
 */
const getDifficultyText = (difficulty: string): string => {
  const map: Record<string, string> = {
    easy: '简单',
    medium: '中等',
    hard: '困难',
  }
  return map[difficulty] || difficulty
}

/**
 * @description 获取难度等级对应的颜色样式
 * @param difficulty 难度等级
 * @returns 返回对应的CSS颜色类名
 */
const getDifficultyColor = (difficulty: string): string => {
  const map: Record<string, string> = {
    easy: 'text-green-500',
    medium: 'text-yellow-500',
    hard: 'text-red-500',
  }
  return map[difficulty] || 'text-gray-500'
}

/**
 * @description 计算题库的完成进度百分比
 * @param bank 题库对象
 * @returns 返回完成百分比
 */
const getProgressPercentage = (bank: QuestionBank): number => {
  return Math.round((bank.completedCount / bank.questionCount) * 100)
}

/**
 * @description 通用页面导航方法
 * @param page 页面路径或标识
 */
const navigateTo = (page: string): void => {
  console.log('导航到:', page)
  // 这里可以添加实际的导航逻辑
}

/**
 * @description 查看所有书籍列表
 */
const showAllBooks = (): void => {
  console.log('查看所有书籍')
  // 跳转到书籍列表页面
  uni.navigateTo({
    url: '/pages/learning/book',
  })
}

/**
 * @description 打开指定书籍的详情页面
 * @param book 书籍对象
 */
const openBook = (book: StudyBook): void => {
  console.log('打开书籍:', book.title)
  // 跳转到书籍详情页面
  uni.navigateTo({
    url: `/pages/learning/book-detail?id=${book.id}`,
  })
}

/**
 * @description 开始智能背诵功能
 */
const startSmartMemory = (): void => {
  console.log('开始智能背诵')
  // 跳转到智能背诵页面
  uni.navigateTo({
    url: '/pages/learning/memory',
  })
}

// 生命周期
onMounted(() => {
  updateFilterCounts()
})
</script>

<template>
  <view class="page-container">
    <!-- iOS状态栏 -->
    <view class="status-bar"></view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 头部区域 -->
      <view class="header">
        <view class="header-content">
          <text class="page-title">学习资料库</text>
          <view class="search-icon" @click="showSearchInput = !showSearchInput">
            <text class="fas fa-search"></text>
          </view>
        </view>

        <!-- 搜索输入框 -->
        <view v-if="showSearchInput" class="search-container">
          <input
            v-model="searchQuery"
            class="search-input"
            placeholder="搜索题库、资料..."
            @input="handleSearch"
          />
          <view v-if="showSearchSuggestions" class="search-suggestions">
            <view
              v-for="suggestion in searchSuggestions"
              :key="suggestion"
              class="suggestion-item"
              @click="selectSuggestion(suggestion)"
            >
              {{ suggestion }}
            </view>
          </view>
        </view>
      </view>

      <!-- 专业选择下拉 -->
      <view class="major-selector">
        <view class="selector-header" @click="showMajorDropdown = !showMajorDropdown">
          <text class="selector-text">{{ currentMajor }}</text>
          <text class="fas fa-chevron-down"></text>
        </view>
        <view v-if="showMajorDropdown" class="dropdown-menu">
          <view
            v-for="major in majors"
            :key="major.id"
            class="dropdown-item"
            @click="selectMajor(major)"
          >
            <text class="major-icon">{{ major.icon }}</text>
            <text class="major-name">{{ major.name }}</text>
          </view>
        </view>
      </view>

      <!-- 快速筛选标签 -->
      <view class="quick-filters">
        <scroll-view scroll-x class="filter-scroll">
          <view class="filter-tabs">
            <view
              v-for="filter in filterOptions"
              :key="filter.key"
              class="filter-tab"
              :class="{ active: activeFilter === filter.key }"
              @click="setFilter(filter.key)"
            >
              <text>{{ filter.label }}</text>
              <text v-if="filter.count > 0" class="count">({{ filter.count }})</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 学习概览卡片 -->
      <view class="overview-card">
        <view class="card-header">
          <text class="card-title">学习概览</text>
          <text class="card-subtitle">今日学习进度</text>
        </view>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ todayStats.completed }}</text>
            <text class="stat-label">今日完成</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ todayStats.total }}</text>
            <text class="stat-label">总题目数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ todayStats.accuracy }}%</text>
            <text class="stat-label">正确率</text>
          </view>
        </view>
      </view>

      <!-- 专业题库区域 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">专业题库</text>
          <text class="section-subtitle">{{ filteredQuestionBanks.length }}个题库</text>
        </view>

        <view class="question-banks">
          <view
            v-for="bank in filteredQuestionBanks"
            :key="bank.id"
            class="bank-card"
            @click="handleQuestionBankClick(bank)"
          >
            <view class="bank-header">
              <view class="bank-icon">{{ bank.icon }}</view>
              <view class="bank-info">
                <text class="bank-title">{{ bank.title }}</text>
                <text class="bank-subtitle">{{ bank.questionCount }}道题目</text>
              </view>
              <view v-if="bank.isHot" class="hot-badge">热门</view>
            </view>

            <text class="bank-description">{{ bank.description }}</text>

            <view class="bank-progress">
              <view class="progress-info">
                <text class="progress-text">学习进度</text>
                <text class="progress-percent">{{ getProgressPercentage(bank) }}%</text>
              </view>
              <view class="progress-bar">
                <view
                  class="progress-fill"
                  :style="{ width: getProgressPercentage(bank) + '%' }"
                ></view>
              </view>
            </view>

            <view class="bank-footer">
              <view class="tags">
                <text v-for="tag in bank.tags.slice(0, 3)" :key="tag" class="tag">
                  {{ tag }}
                </text>
              </view>
              <view class="difficulty" :class="'difficulty-' + bank.difficulty">
                {{ getDifficultyText(bank.difficulty) }}
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="filteredQuestionBanks.length === 0" class="empty-state">
          <text class="fas fa-search empty-icon"></text>
          <text class="empty-title">没有找到相关题库</text>
          <text class="empty-subtitle">试试调整搜索关键词或筛选条件</text>
        </view>
      </view>

      <!-- 学习资源区域 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">学习资源</text>
          <text class="view-all" @click="showAllBooks">查看全部</text>
        </view>

        <scroll-view scroll-x class="books-scroll">
          <view class="books-container">
            <view
              v-for="book in studyBooks"
              :key="book.id"
              class="book-card"
              @click="openBook(book)"
            >
              <view class="book-cover">
                <text class="book-icon">{{ book.icon }}</text>
              </view>
              <text class="book-title">{{ book.title }}</text>
              <text class="book-progress">{{ book.progress }}%</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 智能背诵区域 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">智能背诵</text>
          <text class="view-all" @click="startSmartMemory">开始背诵</text>
        </view>

        <view class="memory-card">
          <view class="memory-header">
            <text class="memory-title">今日背诵计划</text>
            <text class="memory-count">{{ memoryStats.todayCount }}个知识点</text>
          </view>
          <view class="memory-progress">
            <text class="memory-text">
              完成进度: {{ memoryStats.completed }}/{{ memoryStats.total }}
            </text>
            <view class="memory-bar">
              <view
                class="memory-fill"
                :style="{ width: (memoryStats.completed / memoryStats.total) * 100 + '%' }"
              ></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <view class="bottom-nav">
      <view class="nav-item" @click="navigateTo('home')">
        <text class="fas fa-home nav-icon"></text>
        <text class="nav-label">首页</text>
      </view>
      <view class="nav-item active" @click="navigateTo('study')">
        <text class="fas fa-book nav-icon"></text>
        <text class="nav-label">学习</text>
      </view>
      <view class="nav-item" @click="navigateTo('practice')">
        <text class="fas fa-code nav-icon"></text>
        <text class="nav-label">练习</text>
      </view>
      <view class="nav-item" @click="navigateTo('profile')">
        <text class="fas fa-user nav-icon"></text>
        <text class="nav-label">我的</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* iOS状态栏 */
.status-bar {
  height: 44px;
  background: rgba(0, 0, 0, 0.1);
  // #ifdef APP-PLUS
  height: var(--status-bar-height);
  // #endif
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  background: #f8f9fa;
  border-radius: 20px 20px 0 0;
  margin-top: 10px;
  padding: 20px;
  min-height: calc(100vh - 54px - 80px);
}

/* 头部区域 */
.header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
}

.search-icon {
  width: 40px;
  height: 40px;
  background: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.search-container {
  position: relative;
  margin-top: 15px;
}

.search-input {
  width: 100%;
  padding: 15px 20px;
  border: none;
  border-radius: 25px;
  background: #fff;
  font-size: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  z-index: 100;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

/* 专业选择器 */
.major-selector {
  position: relative;
  margin-bottom: 20px;
}

.selector-header {
  background: #fff;
  padding: 15px 20px;
  border-radius: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.selector-header:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.selector-text {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  z-index: 100;
  animation: slideDown 0.3s ease;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.major-icon {
  font-size: 20px;
  margin-right: 12px;
}

.major-name {
  font-size: 16px;
  color: #2c3e50;
}

/* 快速筛选 */
.quick-filters {
  margin-bottom: 20px;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  gap: 10px;
  padding: 5px 0;
}

.filter-tab {
  flex-shrink: 0;
  padding: 10px 20px;
  background: #fff;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.filter-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.count {
  margin-left: 5px;
  opacity: 0.7;
}

/* 概览卡片 */
.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 25px;
  color: #fff;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

/* 区域样式 */
.section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
}

.section-subtitle {
  font-size: 14px;
  color: #666;
}

.view-all {
  font-size: 14px;
  color: #667eea;
  cursor: pointer;
  font-weight: 500;
}

.view-all:hover {
  text-decoration: underline;
}

/* 题库卡片 */
.question-banks {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bank-card {
  background: #fff;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.bank-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.bank-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.bank-icon {
  font-size: 24px;
  margin-right: 15px;
}

.bank-info {
  flex: 1;
}

.bank-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
  display: block;
}

.bank-subtitle {
  font-size: 14px;
  color: #666;
}

.hot-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

.bank-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: block;
}

.bank-progress {
  margin-bottom: 15px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 14px;
  color: #666;
}

.progress-percent {
  font-size: 14px;
  font-weight: bold;
  color: #667eea;
}

.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.5s ease;
}

.bank-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  background: #f8f9fa;
  color: #666;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
}

.difficulty {
  padding: 5px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.difficulty-easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-hard {
  background: #f8d7da;
  color: #721c24;
}

/* 学习资源 */
.books-scroll {
  white-space: nowrap;
}

.books-container {
  display: flex;
  gap: 15px;
  padding: 5px 0;
}

.book-card {
  flex-shrink: 0;
  width: 120px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-card:hover {
  transform: translateY(-5px);
}

.book-cover {
  width: 80px;
  height: 100px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.book-icon {
  font-size: 24px;
  color: #fff;
}

.book-title {
  font-size: 12px;
  color: #2c3e50;
  margin-bottom: 5px;
  display: block;
  line-height: 1.3;
}

.book-progress {
  font-size: 11px;
  color: #667eea;
  font-weight: bold;
}

/* 智能背诵 */
.memory-card {
  background: #fff;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.memory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.memory-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.memory-count {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.memory-progress {
  margin-bottom: 10px;
}

.memory-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.memory-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.memory-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 20px;
  display: block;
}

.empty-title {
  font-size: 18px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.empty-subtitle {
  font-size: 14px;
  color: #999;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: #fff;
  display: flex;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  // #ifdef APP-PLUS
  padding-bottom: env(safe-area-inset-bottom);
  // #endif
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item.active {
  color: #667eea;
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.nav-label {
  font-size: 12px;
}

/* 动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .main-content {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .stats-grid {
    gap: 10px;
  }

  .bank-card {
    padding: 15px;
  }

  .books-container {
    gap: 10px;
  }

  .book-card {
    width: 100px;
  }

  .book-cover {
    width: 70px;
    height: 90px;
  }
}
</style>
