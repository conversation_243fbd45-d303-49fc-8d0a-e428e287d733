/**
 * 数字人调试工具
 * <AUTHOR>
 * @date 2025-07-20
 */

import { debugWebSocket, fullTest, getStreamUrl, updateStreamUrl } from '@/service/avatar'

/**
 * 调试数字人推流地址获取问题
 */
export class AvatarDebugger {
  
  /**
   * 运行完整的调试流程
   */
  static async runFullDebug() {
    console.log('🔍 开始数字人调试流程...')
    
    try {
      // 1. 运行完整测试
      console.log('📋 步骤1: 运行完整测试流程')
      const testResult = await fullTest()
      console.log('✅ 完整测试结果:', testResult)
      
      return {
        success: true,
        message: '调试完成',
        data: testResult.data
      }
      
    } catch (error: any) {
      console.error('❌ 调试失败:', error)
      return {
        success: false,
        message: error.message || '调试失败',
        error: error
      }
    }
  }
  
  /**
   * 调试WebSocket消息
   */
  static async debugWebSocketMessages() {
    console.log('🔍 开始WebSocket消息调试...')
    
    try {
      const result = await debugWebSocket()
      console.log('✅ WebSocket调试结果:', result)
      
      return {
        success: true,
        message: 'WebSocket调试完成',
        data: result.data
      }
      
    } catch (error: any) {
      console.error('❌ WebSocket调试失败:', error)
      return {
        success: false,
        message: error.message || 'WebSocket调试失败',
        error: error
      }
    }
  }
  
  /**
   * 检查推流地址获取
   */
  static async checkStreamUrl(sessionId: string) {
    console.log('🔍 检查推流地址获取...', sessionId)
    
    try {
      // 1. 尝试更新推流地址
      console.log('📋 步骤1: 更新推流地址')
      await updateStreamUrl(sessionId)
      
      // 2. 获取推流地址
      console.log('📋 步骤2: 获取推流地址')
      const result = await getStreamUrl(sessionId)
      console.log('✅ 推流地址获取结果:', result)
      
      return {
        success: result.code === 200,
        message: result.message,
        streamUrl: result.data
      }
      
    } catch (error: any) {
      console.error('❌ 推流地址检查失败:', error)
      return {
        success: false,
        message: error.message || '推流地址检查失败',
        error: error
      }
    }
  }
  
  /**
   * 定期检查推流地址
   */
  static startStreamUrlMonitor(sessionId: string, interval: number = 3000) {
    console.log('🔍 启动推流地址监控...', sessionId)
    
    let checkCount = 0
    const maxChecks = 10 // 最多检查10次
    
    const timer = setInterval(async () => {
      checkCount++
      console.log(`📋 第${checkCount}次检查推流地址...`)
      
      try {
        const result = await this.checkStreamUrl(sessionId)
        
        if (result.success && result.streamUrl) {
          console.log('✅ 推流地址获取成功:', result.streamUrl)
          clearInterval(timer)
          return result.streamUrl
        } else {
          console.log('⏳ 推流地址尚未获取，继续等待...')
        }
        
        if (checkCount >= maxChecks) {
          console.log('⚠️ 达到最大检查次数，停止监控')
          clearInterval(timer)
        }
        
      } catch (error) {
        console.error('❌ 推流地址检查出错:', error)
        if (checkCount >= maxChecks) {
          clearInterval(timer)
        }
      }
    }, interval)
    
    return timer
  }
  
  /**
   * 输出调试信息
   */
  static logDebugInfo() {
    console.log('🔍 数字人调试信息:')
    console.log('📋 可用的调试方法:')
    console.log('  - AvatarDebugger.runFullDebug() - 运行完整调试流程')
    console.log('  - AvatarDebugger.debugWebSocketMessages() - 调试WebSocket消息')
    console.log('  - AvatarDebugger.checkStreamUrl(sessionId) - 检查推流地址')
    console.log('  - AvatarDebugger.startStreamUrlMonitor(sessionId) - 启动推流地址监控')
    console.log('📋 后端调试接口:')
    console.log('  - POST /app/avatar/test/debug-websocket - 调试WebSocket消息')
    console.log('  - POST /app/avatar/test/full-test - 完整测试流程')
    console.log('  - GET /app/avatar/stream-url/{sessionId} - 获取推流地址')
    console.log('  - POST /app/avatar/update-stream-url/{sessionId} - 更新推流地址')
  }
}

// 全局暴露调试器
if (typeof window !== 'undefined') {
  (window as any).AvatarDebugger = AvatarDebugger
  console.log('🔧 数字人调试器已加载，使用 AvatarDebugger.logDebugInfo() 查看可用方法')
}

export default AvatarDebugger
