<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { onLoad, onBackPress } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import { API_CONFIG, apiRequest, type PaymentOrderResponse } from '../../config/api'
// 定义商品类型枚举
type ProductType = 'book' | 'video' | 'question-bank' | 'course' | 'plan'

// 定义订单信息类型
interface OrderInfo {
  orderNo: string
  payToken: string
  productId: number
  productType: ProductType
  productTitle: string
  amount: number
  paymentMethod: string
  status: string
  createTime: string
  expireTime: string
}

// 页面状态
const isLoading = ref(false)
const isPageLoaded = ref(false)
const isPaying = ref(false)
const paymentProgress = ref(0)
const showSuccessAnimation = ref(false)
const paymentStep = ref('')

// 取消订单相关状态
const isCancelling = ref(false)
const showCancelAnimation = ref(false)
const cancelProgress = ref(0)
const cancelStep = ref('')

// SSE相关状态
const sseConnected = ref(false)
const sseTimeout = ref(false)
const showManualQuery = ref(false)
let sseEventSource: EventSource | null = null
let sseTimeoutTimer: number | null = null

// 订单信息
const orderInfo = ref<OrderInfo>({
  orderNo: '',
  payToken: '',
  productId: 0,
  productType: 'book',
  productTitle: '',
  amount: 0,
  paymentMethod: 'alipay',
  status: 'pending',
  createTime: '',
  expireTime: '',
})

// 支付方式显示信息
const paymentMethodInfo = computed(() => {
  const methodMap = {
    alipay: {
      name: '支付宝支付',
      icon: '../../static/svg/alipay-pay.svg',
      description: '支付宝安全支付',
    },
    wechat: {
      name: '微信支付',
      icon: '../../static/svg/wechat-pay.svg',
      description: '微信安全支付',
    },
  }
  return methodMap[orderInfo.value.paymentMethod as keyof typeof methodMap] || methodMap.alipay
})

// 获取商品类型显示文本
const productTypeText = computed(() => {
  const typeMap: Record<ProductType, string> = {
    book: '电子书籍',
    video: '视频课程',
    'question-bank': '题库练习',
    course: '专业课程',
    plan: '学习计划',
  }
  return typeMap[orderInfo.value.productType] || '商品'
})

// 获取订单状态显示文本
const orderStatusText = computed(() => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消',
    expired: '已过期',
  }
  return statusMap[orderInfo.value.status] || orderInfo.value.status
})

// 添加倒计时相关状态
const paymentCountdown = ref(60) // 倒计时秒数，默认60秒
const isCountingDown = ref(false) // 是否正在倒计时
const showTimeoutConfirm = ref(false) // 是否显示超时确认弹框
let countdownTimer: number | null = null

/**
 * @description 获取订单详情信息
 * @param orderNo 订单号
 */
const fetchOrderInfo = async (orderNo: string): Promise<void> => {
  try {
    isLoading.value = true

    // 调用后端API获取订单详情
    const result = await apiRequest<PaymentOrderResponse>({
      url: `${API_CONFIG.PAYMENT.QUERY_STATUS}?orderNo=${orderNo}`,
      method: 'GET',
    })

    if (result.code === 200) {
      const data = result.data
      orderInfo.value = {
        orderNo: data.orderNo,
        payToken: data.payToken,
        productId: data.productId,
        productType: data.productType as ProductType,
        productTitle: data.productTitle,
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        status: data.status,
        createTime: data.createTime,
        expireTime: data.expireTime,
      }
    } else {
      throw new Error(result.msg || '获取订单信息失败')
    }
  } catch (error) {
    console.error('获取订单信息失败:', error)
    uni.showToast({
      title: '获取订单信息失败',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 开始支付流程
 */
const startPayment = async (): Promise<void> => {
  if (isPaying.value) return

  if (orderInfo.value.status !== 'pending') {
    uni.showToast({
      title: '订单状态异常，无法支付',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  try {
    switch (orderInfo.value.paymentMethod) {
      case 'alipay':
        await handleAlipayPayment()
        break
      case 'wechat':
        await handleWechatPayment()
        break
      default:
        throw new Error('不支持的支付方式')
    }
    isPaying.value = true
    // 开始倒计时
    startPaymentCountdown()
  } catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      title: '支付失败，请重试',
      icon: 'error',
      duration: 2000,
    })
    // 停止倒计时
    stopPaymentCountdown()
    isPaying.value = false
  }
}

/**
 * @description 开始支付倒计时
 */
const startPaymentCountdown = (): void => {
  isCountingDown.value = true
  paymentCountdown.value = 60
  showTimeoutConfirm.value = false

  countdownTimer = setInterval(() => {
    paymentCountdown.value--

    if (paymentCountdown.value <= 0) {
      // 倒计时结束，显示超时确认弹框
      isCountingDown.value = false
      showTimeoutConfirm.value = true
      clearInterval(countdownTimer!)
      countdownTimer = null

      // 更新支付步骤提示
      paymentStep.value = '支付超时，请确认是否已完成支付'
    }
  }, 1000)
}

/**
 * @description 停止支付倒计时
 */
const stopPaymentCountdown = (): void => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  isCountingDown.value = false
  paymentCountdown.value = 60
}

/**
 * @description 用户确认已支付
 */
const confirmPaymentComplete = async (): Promise<void> => {
  try {
    showTimeoutConfirm.value = false
    paymentStep.value = '正在查询支付状态...'

    // 查询支付状态
    await manualQueryPaymentStatus()
  } catch (error) {
    console.error('确认支付失败:', error)
    uni.showToast({
      title: '查询失败，请重试',
      icon: 'error',
      duration: 2000,
    })
    showTimeoutConfirm.value = true
  }
}

/**
 * @description 用户确认未支付，取消支付流程
 */
const confirmPaymentCancel = (): void => {
  showTimeoutConfirm.value = false
  isPaying.value = false
  paymentProgress.value = 0
  paymentStep.value = ''

  uni.showToast({
    title: '支付已取消',
    icon: 'none',
    duration: 2000,
  })
}

/**
 * @description 处理支付宝支付
 */
const handleAlipayPayment = async (): Promise<void> => {
  try {
    // 开始支付动画
    paymentStep.value = '正在准备支付...'
    await simulatePaymentProgress()

    paymentStep.value = '正在跳转支付页面...'
    paymentProgress.value = 80

    // #ifdef H5
    // H5端直接跳转到支付页面
    const payUrl = `${API_CONFIG.PAYMENT.ALIPAY_PAY}?orderNo=${orderInfo.value.orderNo}&payToken=${orderInfo.value.payToken}`
    window.open(payUrl, '_blank')
    // 建立SSE连接监听支付状态
    setTimeout(() => {
      connectToPaymentSSE(orderInfo.value.orderNo, orderInfo.value.payToken)
    }, 1000)
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序端暂不支持支付宝支付
    uni.showModal({
      title: '提示',
      content: '请在H5端使用支付宝支付功能',
      showCancel: false,
    })
    isPaying.value = false
    paymentProgress.value = 0
    // #endif
  } catch (error: any) {
    console.error('支付宝支付失败:', error)
    paymentStep.value = '支付失败'
    uni.showToast({
      title: error.message || '支付失败，请重试',
      icon: 'error',
      duration: 2000,
    })
    isPaying.value = false
    paymentProgress.value = 0
  }
}

/**
 * @description 处理微信支付
 */
const handleWechatPayment = async (): Promise<void> => {
  uni.showModal({
    title: '暂未开放',
    content: '微信支付功能正在开发中，敬请期待',
    showCancel: false,
  })
}

/**
 * @description 模拟支付进度动画
 */
const simulatePaymentProgress = async (): Promise<void> => {
  const steps = [
    { progress: 20, text: '验证订单信息...' },
    { progress: 40, text: '连接支付网关...' },
    { progress: 60, text: '加密传输中...' },
    { progress: 80, text: '准备跳转支付...' },
    { progress: 100, text: '正在跳转...' },
  ]

  for (const step of steps) {
    paymentProgress.value = step.progress
    paymentStep.value = step.text
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
}

/**
 * @description 模拟取消订单进度动画
 */
const simulateCancelProgress = async (): Promise<void> => {
  const steps = [
    { progress: 20, text: '验证订单状态...' },
    { progress: 40, text: '连接服务器...' },
    { progress: 60, text: '处理取消请求...' },
    { progress: 80, text: '更新订单状态...' },
    { progress: 100, text: '取消完成...' },
  ]

  for (const step of steps) {
    cancelProgress.value = step.progress
    cancelStep.value = step.text
    await new Promise((resolve) => setTimeout(resolve, 400))
  }
}

// 其他支付相关函数将在下一部分添加...

/**
 * @description 返回上一页（已禁用，防止用户在支付过程中误操作）
 */
const goBack = (): void => {
  // 支付确认页面不允许返回，给出提示
  uni.showModal({
    title: '提示',
    content: '支付确认过程中无法返回，请完成支付或联系客服处理',
    showCancel: false,
    confirmText: '知道了',
  })
}

/**
 * @description 初始化页面动画效果
 */
const initPage = async (): Promise<void> => {
  await nextTick()
  setTimeout(() => {
    isPageLoaded.value = true
  }, 100)
}

// 页面加载时获取参数
onLoad((options: any) => {
  const { orderNo, payToken, productId, productType, paymentMethod } = options


  if (orderNo) {
    // 设置基本订单信息
    orderInfo.value.orderNo = orderNo
    orderInfo.value.payToken = payToken || ''
    orderInfo.value.productId = parseInt(productId) || 0
    orderInfo.value.productType = productType || 'book'
    orderInfo.value.paymentMethod = paymentMethod || 'alipay'

    // 获取完整订单信息
    fetchOrderInfo(orderNo)
  } else {
    uni.showToast({
      title: '订单信息异常',
      icon: 'error',
      duration: 2000,
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})

// 页面挂载时初始化动画
onMounted(() => {
  initPage()
})

/**
 * @description 页面卸载时清理资源
 */
onUnmounted(() => {
  // 清理倒计时
  stopPaymentCountdown()

  // 清理SSE连接
  if (sseEventSource) {
    sseEventSource.close()
    sseEventSource = null
  }

  // 清理定时器
  if (sseTimeoutTimer) {
    clearTimeout(sseTimeoutTimer)
    sseTimeoutTimer = null
  }

})

/**
 * @description 建立SSE连接监听支付状态
 * @param orderNo 订单号
 * @param payToken 支付token
 */
const connectToPaymentSSE = (orderNo: string, payToken: string): void => {
  try {
    // #ifdef H5
    // 关闭已有连接
    if (sseEventSource) {
      sseEventSource.close()
    }

    // 建立SSE连接
    const sseUrl = `${API_CONFIG.PAYMENT.SSE_CONNECT}?orderNo=${orderNo}&payToken=${payToken}`
    sseEventSource = new EventSource(sseUrl)

    // 连接建立成功
    sseEventSource.addEventListener('connected', (event: MessageEvent) => {
      sseConnected.value = true
      paymentStep.value = '等待支付结果...'

      // 设置1分钟超时
      sseTimeoutTimer = setTimeout(() => {
        sseTimeout.value = true
        showManualQuery.value = true
        paymentStep.value = '支付超时，可手动查询'
      }, 60000) // 1分钟
    })

    // 支付成功
    sseEventSource.addEventListener('payment-success', (event: MessageEvent) => {
      clearSSETimeout()
      handlePaymentSuccess()
    })

    // 支付失败
    sseEventSource.addEventListener('payment-failed', (event: MessageEvent) => {
      clearSSETimeout()
      const data = JSON.parse(event.data)
      uni.showToast({
        title: data.data?.reason || '支付失败',
        icon: 'error',
        duration: 3000,
      })
      isPaying.value = false
      paymentProgress.value = 0
    })

    // 支付取消
    sseEventSource.addEventListener('payment-cancelled', (event: MessageEvent) => {
      clearSSETimeout()
      uni.showToast({
        title: '支付已取消',
        icon: 'none',
        duration: 2000,
      })
      isPaying.value = false
      paymentProgress.value = 0
    })

    // 查询结果
    sseEventSource.addEventListener('query-result', (event: MessageEvent) => {
      const data = JSON.parse(event.data)
      const orderData = data.data

      if (orderData.status === 'paid') {
        handlePaymentSuccess()
      } else {
        uni.showToast({
          title: `订单状态：${getOrderStatusText(orderData.status)}`,
          icon: 'none',
          duration: 2000,
        })
      }
    })

    // 心跳消息
    sseEventSource.addEventListener('heartbeat', (event: MessageEvent) => {
    })

    // 连接错误
    sseEventSource.onerror = (event) => {
      console.error('SSE连接错误:', event)
      sseConnected.value = false

      // 如果还在支付中，显示手动查询按钮
      if (isPaying.value) {
        showManualQuery.value = true
        paymentStep.value = '连接异常，可手动查询'
      }
    }

    // 连接打开
    sseEventSource.onopen = () => {
      console.log('SSE连接已打开')
    }
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序不支持SSE，使用轮询
    startPollingOrderStatus(orderNo)
    // #endif
  } catch (error) {
    console.error('建立SSE连接失败:', error)
    showManualQuery.value = true
    paymentStep.value = '连接失败，可手动查询'
  }
}

/**
 * @description 清理SSE超时定时器
 */
const clearSSETimeout = (): void => {
  if (sseTimeoutTimer) {
    clearTimeout(sseTimeoutTimer)
    sseTimeoutTimer = null
  }
}

/**
 * @description 手动查询支付状态
 */
const manualQueryPaymentStatus = async (): Promise<void> => {
  try {
    showManualQuery.value = false
    paymentStep.value = '正在查询支付状态...'

    // 首先尝试通过SSE查询
    if (sseEventSource && sseConnected.value) {
      await apiRequest({
        url: API_CONFIG.PAYMENT.SSE_QUERY,
        method: 'POST',
        data: {
          orderNo: orderInfo.value.orderNo,
          payToken: orderInfo.value.payToken,
        },
      })

      // 等待SSE响应
      setTimeout(() => {
        if (isPaying.value) {
          showManualQuery.value = true
          paymentStep.value = '查询完成，可再次查询'
        }
      }, 3000)

      return
    }

    // 如果SSE不可用，直接查询API
    const result = await apiRequest<PaymentOrderResponse>({
      url: `${API_CONFIG.PAYMENT.QUERY_STATUS}?orderNo=${orderInfo.value.orderNo}`,
      method: 'GET',
    })

    if (result.code === 200) {
      const orderData = result.data

      if (orderData.status === 'paid') {
        handlePaymentSuccess()
      } else {
        uni.showToast({
          title: `当前状态：${getOrderStatusText(orderData.status)}`,
          icon: 'none',
          duration: 2000,
        })
        showManualQuery.value = true
        paymentStep.value = '可继续查询或等待'
      }
    }
  } catch (error: any) {
    console.error('手动查询支付状态失败:', error)
    uni.showToast({
      title: '查询失败，请重试',
      icon: 'error',
      duration: 2000,
    })
    showManualQuery.value = true
    paymentStep.value = '查询失败，可重试'
  }
}

/**
 * @description 获取订单状态显示文本
 * @param status 订单状态
 * @returns 显示文本
 */
const getOrderStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消',
    expired: '已过期',
  }
  return statusMap[status] || status
}

/**
 * @description 开始轮询订单状态（微信小程序使用）
 * @param orderNo 订单号
 */
const startPollingOrderStatus = (orderNo: string): void => {
  let pollCount = 0
  const maxPolls = 60 // 最多轮询60次（5分钟）

  const pollTimer = setInterval(async () => {
    try {
      pollCount++

      // 查询订单状态
      const result = await apiRequest<PaymentOrderResponse>({
        url: `${API_CONFIG.PAYMENT.QUERY_STATUS}?orderNo=${orderNo}`,
        method: 'GET',
      })

      if (result.code === 200) {
        const orderData = result.data

        if (orderData.status === 'paid') {
          // 支付成功
          clearInterval(pollTimer)
          handlePaymentSuccess()
          return
        } else if (orderData.status === 'cancelled' || orderData.status === 'expired') {
          // 支付取消或过期
          clearInterval(pollTimer)
          uni.showToast({
            title: orderData.status === 'cancelled' ? '支付已取消' : '支付已过期',
            icon: 'none',
            duration: 2000,
          })
          isPaying.value = false
          paymentProgress.value = 0
          return
        }
      }

      // 达到最大轮询次数，停止轮询
      if (pollCount >= maxPolls) {
        clearInterval(pollTimer)
        uni.showToast({
          title: '支付超时，请检查支付状态',
          icon: 'none',
          duration: 2000,
        })
        isPaying.value = false
        paymentProgress.value = 0
      }
    } catch (error) {
      console.error('查询订单状态失败:', error)
      // 查询失败不中断轮询，继续尝试
    }
  }, 5000) // 每5秒查询一次
}

/**
 * @description 获取支付成功后的跳转页面
 * @returns 返回跳转页面的路径
 */
const getSuccessRedirectUrl = (): string => {
  const { productType, productId } = orderInfo.value

  const routeMap: Record<ProductType, string> = {
    book: `/pages/learning/book-reader?id=${productId}`,
    video: `/pages/learning/video-player?id=${productId}`,
    'question-bank': `/pages/learning/question-bank-detail?id=${productId}`,
    course: `/pages/learning/course-detail?id=${productId}`,
    plan: `/pages/learning/plan-detail?id=${productId}`,
  }

  return routeMap[productType] || `/pages/learning/index`
}

/**
 * @description 处理支付成功
 */
const handlePaymentSuccess = (): void => {
  // 停止倒计时
  stopPaymentCountdown()

  // 清理SSE连接和定时器
  clearSSETimeout()
  if (sseEventSource) {
    sseEventSource.close()
    sseEventSource = null
  }

  // 重置SSE相关状态
  sseConnected.value = false
  showManualQuery.value = false
  sseTimeout.value = false
  showTimeoutConfirm.value = false

  // 显示支付成功动画
  showSuccessAnimation.value = true
  paymentStep.value = '支付成功！'
  paymentProgress.value = 100

  // 播放成功动画
  setTimeout(() => {
    uni.showToast({
      title: '支付成功！',
      icon: 'success',
      duration: 2000,
    })
  }, 500)

  setTimeout(() => {
    // 根据商品类型跳转到对应页面
    const redirectUrl = getSuccessRedirectUrl()
    uni.redirectTo({
      url: redirectUrl,
    })
  }, 2500)
}

/**
 * @description 拦截返回按钮操作
 * @returns 返回true表示拦截返回，false表示允许返回
 */
const handleBackPress = (): boolean => {
  // 如果正在支付中，拦截返回操作
  if (isPaying.value) {
    uni.showModal({
      title: '支付进行中',
      content: '正在处理支付，请勿离开页面',
      showCancel: false,
      confirmText: '知道了',
    })
    return true // 拦截返回
  }

  // 如果订单状态为待支付，也拦截返回
  if (orderInfo.value.status === 'pending') {
    uni.showModal({
      title: '确认离开',
      content: '订单尚未支付，确定要离开吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认离开，可以跳转到资源学习首页
          uni.reLaunch({
            url: '/pages/learning/index',
          })
        }
      },
    })
    return true // 拦截返回
  }

  return false // 允许返回
}

/**
 * @description 取消订单
 */
const cancelOrder = () => {
  uni.showModal({
    title: '确认取消订单',
    content: '确定要取消订单吗？取消后将无法恢复',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 显示取消动画
          isCancelling.value = true
          showCancelAnimation.value = true
          cancelProgress.value = 0
          cancelStep.value = '开始取消订单...'

          // 开始动画
          await simulateCancelProgress()

          // 执行取消订单API
          const result = await apiRequest({
            url: `${API_CONFIG.PAYMENT.CANCEL_ORDER}?orderNo=${orderInfo.value.orderNo}`,
            method: 'POST',
          })

          if (result.code === 200) {
            // 更新订单状态
            orderInfo.value.status = 'cancelled'

            // 显示成功状态
            cancelStep.value = '取消成功！'

            // 延迟隐藏动画并跳转
            setTimeout(() => {
              showCancelAnimation.value = false
              isCancelling.value = false

              uni.showToast({
                title: '取消订单成功',
                icon: 'success',
                duration: 2000,
              })

              // 延迟跳转到学习页面
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/learning/index',
                })
              }, 500)
            }, 1000)
          } else {
            // 显示失败状态
            cancelStep.value = '取消失败！'

            setTimeout(() => {
              showCancelAnimation.value = false
              isCancelling.value = false

              uni.showToast({
                title: result.msg || '取消订单失败',
                icon: 'error',
                duration: 2000,
              })
            }, 1000)
          }
        } catch (error: any) {
          console.error('取消订单失败:', error)

          // 显示错误状态
          cancelStep.value = '取消失败！'

          setTimeout(() => {
            showCancelAnimation.value = false
            isCancelling.value = false

            uni.showToast({
              title: error.message || '取消订单失败',
              icon: 'error',
              duration: 2000,
            })
          }, 1000)
        }
      }
    },
  })
}

// 页面返回拦截
onBackPress(() => {
  return handleBackPress()
})
</script>

<template>
  <view class="pay-confirm-container">
    <HeadBar title="确认支付" :show-back="false" @back="goBack" />

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
      <view v-if="!isLoading" class="content-wrapper fade-in">
        <!-- 订单信息卡片 -->
        <view class="order-section">
          <view class="order-card">
            <!-- 订单头部 -->
            <view class="order-header">
              <view class="order-title-wrapper">
                <view class="i-fa-file-invoice order-icon"></view>
                <text class="order-title">订单详情</text>
              </view>
              <view class="order-status" :class="'status-' + orderInfo.status">
                <text class="status-text">{{ orderStatusText }}</text>
              </view>
            </view>

            <!-- 订单基本信息 -->
            <view class="order-info">
              <view class="order-item">
                <text class="item-label">订单号</text>
                <text class="item-value">{{ orderInfo.orderNo }}</text>
              </view>
              <view class="order-item">
                <text class="item-label">商品名称</text>
                <text class="item-value">{{ orderInfo.productTitle }}</text>
              </view>
              <view class="order-item">
                <text class="item-label">商品类型</text>
                <text class="item-value">{{ productTypeText }}</text>
              </view>
              <view class="order-item">
                <text class="item-label">支付金额</text>
                <text class="item-value amount">¥{{ orderInfo.amount }}</text>
              </view>
              <view class="order-item">
                <text class="item-label">创建时间</text>
                <text class="item-value">{{ orderInfo.createTime }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 支付方式信息 -->
        <view class="payment-method-section">
          <view class="section-header">
            <text class="section-title">支付方式</text>
          </view>

          <view class="payment-method-card">
            <view class="method-info">
              <view class="method-icon-wrapper">
                <image :src="paymentMethodInfo.icon" class="method-icon" mode="aspectFit" />
              </view>
              <view class="method-details">
                <text class="method-name">{{ paymentMethodInfo.name }}</text>
                <text class="method-desc">{{ paymentMethodInfo.description }}</text>
              </view>
              <view class="method-badge">
                <text class="badge-text">已选择</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 支付金额汇总 -->
        <view class="amount-summary-section">
          <view class="section-header">
            <text class="section-title">费用明细</text>
          </view>

          <view class="amount-summary-card">
            <view class="summary-item">
              <text class="summary-label">商品金额</text>
              <text class="summary-value">¥{{ orderInfo.amount }}</text>
            </view>
            <view class="summary-divider"></view>
            <view class="summary-item total-item">
              <text class="summary-label">应付总额</text>
              <text class="summary-value total-amount">¥{{ orderInfo.amount }}</text>
            </view>
          </view>
        </view>

        <!-- 支付说明 -->
        <view class="payment-tips-section">
          <view class="tips-card">
            <view class="tips-header">
              <view class="i-fa-info-circle tips-icon"></view>
              <text class="tips-title">支付说明</text>
            </view>
            <view class="tips-content">
              <text class="tips-text">• 点击"立即支付"将跳转到安全的支付页面</text>
              <text class="tips-text">• 支付过程中请勿关闭浏览器或离开页面</text>
              <text class="tips-text">• 支付完成后将自动跳转到学习页面</text>
              <text class="tips-text">• 如遇问题请联系客服协助处理</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部支付按钮 -->
    <view class="bottom-bar">
      <view class="pay-info">
        <text class="pay-label">应付金额</text>
        <text class="pay-amount">¥{{ orderInfo.amount }}</text>
      </view>
      <view class="buttons-container">
        <!-- 取消订单 -->
        <button class="cancel-btn" @click="cancelOrder" v-if="orderInfo.status === 'pending'">
          取消订单
        </button>
        <button
          class="pay-btn"
          :class="{
            'pay-btn-loading': isPaying,
            'pay-btn-pulse': !isPaying && orderInfo.status === 'pending',
            'pay-btn-disabled': orderInfo.status !== 'pending',
          }"
          :disabled="isPaying || orderInfo.status !== 'pending'"
          @click="startPayment"
        >
          <view v-if="isPaying" class="loading-icon">
            <view class="i-fa-spinner loading-spinner"></view>
          </view>
          <text class="pay-btn-text">
            {{
              isPaying
                ? '支付中...'
                : orderInfo.status === 'pending'
                  ? '立即支付'
                  : orderInfo.status === 'paid'
                    ? '已支付'
                    : '无法支付'
            }}
          </text>
        </button>
      </view>
    </view>

    <!-- 支付进度遮罩 -->
    <view v-if="isPaying" class="payment-overlay">
      <view class="payment-modal">
        <!-- 支付图标动画 -->
        <view class="payment-icon-wrapper">
          <view class="payment-icon-bg">
            <view class="i-fa-solid-credit-card payment-icon"></view>
          </view>
        </view>

        <!-- 支付进度条 -->
        <view class="payment-progress">
          <view class="progress-bg">
            <view class="progress-fill" :style="{ width: paymentProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ paymentProgress }}%</text>
        </view>

        <!-- 支付步骤提示 -->
        <view class="payment-step">
          <text class="step-text">{{ paymentStep }}</text>
        </view>

        <!-- 倒计时显示 -->
        <view v-if="isCountingDown && !showTimeoutConfirm" class="countdown-section">
          <view class="countdown-circle">
            <text class="countdown-number">{{ paymentCountdown }}</text>
          </view>
          <text class="countdown-tip">请在 {{ paymentCountdown }} 秒内完成支付</text>
        </view>

        <!-- 超时确认弹框 -->
        <view v-if="showTimeoutConfirm" class="timeout-confirm-section">
          <view class="timeout-icon">
            <view class="i-fa-clock timeout-clock"></view>
          </view>
          <text class="timeout-title">支付超时</text>
          <text class="timeout-message">已超过1分钟，请确认您是否已完成支付？</text>

          <view class="timeout-buttons">
            <button class="confirm-btn paid-btn" @click="confirmPaymentComplete">
              <view class="i-fa-check btn-icon"></view>
              <text class="btn-text">已支付</text>
            </button>
            <button class="confirm-btn cancel-btn" @click="confirmPaymentCancel">
              <view class="i-fa-times btn-icon"></view>
              <text class="btn-text">未支付</text>
            </button>
          </view>
        </view>

        <!-- 手动查询按钮 -->
        <view v-if="showManualQuery && !showTimeoutConfirm" class="manual-query-section">
          <button class="manual-query-btn" @click="manualQueryPaymentStatus">
            <view class="i-fa-search query-icon"></view>
            <text class="query-text">手动查询支付状态</text>
          </button>
          <text class="query-tip">
            {{ sseTimeout ? '超过1分钟未收到支付结果' : '连接异常，请手动查询' }}
          </text>
        </view>

        <!-- SSE连接状态 -->
        <view v-if="sseConnected && !showTimeoutConfirm" class="connection-status">
          <view class="status-indicator connected">
            <view class="i-fa-wifi status-icon"></view>
            <text class="status-text">实时连接中</text>
          </view>
        </view>

        <!-- 支付安全提示 -->
        <view v-if="!showTimeoutConfirm" class="payment-security">
          <view class="security-item">
            <view class="i-fa-shield-alt security-icon"></view>
            <text class="security-text">安全支付</text>
          </view>
          <view class="security-item">
            <view class="i-fa-lock security-icon"></view>
            <text class="security-text">数据加密</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付成功动画 -->
    <view v-if="showSuccessAnimation" class="success-overlay">
      <view class="success-animation">
        <view class="success-circle">
          <view class="i-fa-solid-check success-check"></view>
        </view>
        <text class="success-title">支付成功！</text>
        <text class="success-subtitle">正在为您跳转...</text>
      </view>
    </view>

    <!-- 取消订单动画 -->
    <view v-if="showCancelAnimation" class="cancel-overlay">
      <view class="cancel-modal">
        <!-- 取消图标动画 -->
        <view class="cancel-icon-wrapper">
          <view class="cancel-icon-bg">
            <view class="i-fa-solid-times cancel-icon"></view>
          </view>
        </view>

        <!-- 取消进度条 -->
        <view class="cancel-progress">
          <view class="progress-bg">
            <view
              class="progress-fill cancel-progress-fill"
              :style="{ width: cancelProgress + '%' }"
            ></view>
          </view>
          <text class="progress-text">{{ cancelProgress }}%</text>
        </view>

        <!-- 取消步骤提示 -->
        <view class="cancel-step">
          <text class="step-text">{{ cancelStep }}</text>
        </view>

        <!-- 取消安全提示 -->
        <view class="cancel-security">
          <view class="security-item">
            <view class="i-fa-shield-alt security-icon"></view>
            <text class="security-text">安全取消</text>
          </view>
          <view class="security-item">
            <view class="i-fa-lock security-icon"></view>
            <text class="security-text">数据保护</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <LoadingCard :visible="isLoading" text="正在加载订单信息..." />

    <!-- 支付中.... -->
    <view v-if="isPaying" class="pay-loading">
      <view class="pay-loading-text">支付中...</view>
      <view class="pay-loading-icon">
        <view class="i-fa-spinner loading-spinner"></view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.pay-confirm-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8fafc;
}

// 主要内容区域
.main-content {
  flex: 1;
  padding-bottom: 140rpx; // 为底部按钮留出空间
}

.content-wrapper {
  padding: 0 20rpx;

  &.fade-in {
    animation: fadeIn 0.6s ease-out;
  }
}

// 订单信息区域
.order-section {
  margin: 20rpx 0 32rpx;

  .order-card {
    background: white;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 2rpx solid #f1f5f9;

      .order-title-wrapper {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .order-icon {
          font-size: 32rpx;
          color: #00c9a7;
        }

        .order-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
        }
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
        font-size: 22rpx;
        font-weight: 500;

        &.status-pending {
          background: #fef3c7;
          color: #92400e;
        }

        &.status-paid {
          background: #d1fae5;
          color: #065f46;
        }

        &.status-cancelled {
          background: #fee2e2;
          color: #991b1b;
        }

        &.status-expired {
          background: #f3f4f6;
          color: #6b7280;
        }
      }
    }

    .order-info {
      padding: 24rpx 32rpx 32rpx;

      .order-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .item-label {
          font-size: 26rpx;
          color: #64748b;
        }

        .item-value {
          font-size: 26rpx;
          font-weight: 500;
          color: #1e293b;
          max-width: 60%;
          text-align: right;

          &.amount {
            font-size: 32rpx;
            font-weight: 700;
            color: #e11d48;
          }
        }
      }
    }
  }
}

// 区域标题
.section-header {
  margin-bottom: 24rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1e293b;
  }
}

// 支付方式区域
.payment-method-section {
  margin-bottom: 32rpx;

  .payment-method-card {
    background: white;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .method-info {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .method-icon-wrapper {
        width: 72rpx;
        height: 72rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8fafc;
        border-radius: 16rpx;

        .method-icon {
          width: 72rpx;
          height: 72rpx;
        }
      }

      .method-details {
        flex: 1;

        .method-name {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
        }

        .method-desc {
          display: block;
          font-size: 24rpx;
          color: #64748b;
        }
      }

      .method-badge {
        padding: 8rpx 16rpx;
        background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
        border: 2rpx solid #00c9a7;
        border-radius: 12rpx;

        .badge-text {
          font-size: 22rpx;
          font-weight: 600;
          color: #00c9a7;
        }
      }
    }
  }
}

// 金额汇总区域
.amount-summary-section {
  margin-bottom: 32rpx;

  .amount-summary-card {
    background: white;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .summary-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.total-item {
        padding-top: 20rpx;

        .summary-label {
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
        }

        .total-amount {
          font-size: 36rpx;
          font-weight: 700;
          color: #e11d48;
        }
      }

      .summary-label {
        font-size: 26rpx;
        color: #64748b;
      }

      .summary-value {
        font-size: 26rpx;
        font-weight: 500;
        color: #1e293b;
      }
    }

    .summary-divider {
      height: 2rpx;
      margin: 20rpx 0;
      background: #f1f5f9;
    }
  }
}

// 支付说明区域
.payment-tips-section {
  margin-bottom: 32rpx;

  .tips-card {
    background: white;
    border-radius: 20rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .tips-header {
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin-bottom: 20rpx;

      .tips-icon {
        font-size: 28rpx;
        color: #3b82f6;
      }

      .tips-title {
        font-size: 26rpx;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .tips-content {
      .tips-text {
        display: block;
        margin-bottom: 12rpx;
        font-size: 24rpx;
        line-height: 1.6;
        color: #64748b;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 底部支付栏
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 2rpx solid #f1f5f9;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);

  .pay-info {
    flex: 1;
    margin-right: 24rpx;

    .pay-label {
      display: block;
      margin-bottom: 4rpx;
      font-size: 22rpx;
      color: #64748b;
    }

    .pay-amount {
      font-size: 32rpx;
      font-weight: 700;
      color: #e11d48;
    }
  }

  // 按钮容器
  .buttons-container {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .pay-btn {
    min-width: 240rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    color: white;
    border: none;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
    transition: all 0.3s ease;

    &:not(:disabled):active {
      transform: scale(0.95);
    }

    &.pay-btn-loading {
      background: #94a3b8;
      box-shadow: none;
    }

    &.pay-btn-disabled {
      background: #94a3b8;
      box-shadow: none;
      opacity: 0.6;
    }

    &.pay-btn-pulse {
      animation: pulse 2s infinite;
    }

    .loading-icon {
      .loading-spinner {
        font-size: 32rpx;
        animation: spin 1s linear infinite;
      }
    }

    .pay-btn-text {
      font-size: 28rpx;
      font-weight: 600;
    }
  }

  .cancel-btn {
    min-width: 140rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 2rpx solid #dc2626;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(220, 38, 38, 0.15);
    transition: all 0.3s ease;
    font-size: 26rpx;
    font-weight: 600;

    &:active {
      transform: scale(0.95);
      background: linear-gradient(135deg, #fecaca 0%, #f87171 100%);
      color: white;
      box-shadow: 0 2rpx 8rpx rgba(220, 38, 38, 0.25);
    }

    &:hover {
      background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
      box-shadow: 0 6rpx 16rpx rgba(220, 38, 38, 0.2);
    }

    &:active::before {
      filter: brightness(0) invert(1);
    }
  }
}

// 支付进度遮罩
.payment-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;

  .payment-modal {
    background: white;
    padding: 60rpx 40rpx;
    border-radius: 20rpx;
    width: 80%;
    max-width: 400rpx;
    text-align: center;
    animation: slideUp 0.3s ease;

    .payment-icon-wrapper {
      position: relative;
      margin-bottom: 40rpx;

      .payment-icon-bg {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto;
        border-radius: 50%;
        background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;
      }

      .payment-icon {
        font-size: 48rpx;
        color: #00c9a7;
        animation: bounce 1s infinite;
      }
    }

    .payment-progress {
      position: relative;
      margin-bottom: 40rpx;

      .progress-bg {
        height: 20rpx;
        background: #f1f5f9;
        border-radius: 10rpx;
        overflow: hidden;
        position: relative;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
          border-radius: 10rpx;
          transition: width 0.3s ease;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressShine 1.5s infinite;
          }
        }
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24rpx;
        font-weight: 600;
        color: #1e293b;
        z-index: 1;
      }
    }

    .payment-step {
      margin-bottom: 40rpx;

      .step-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
        animation: textPulse 1s infinite;
      }
    }

    .countdown-section {
      margin-bottom: 20rpx;

      .countdown-circle {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;
      }

      .countdown-number {
        font-size: 48rpx;
        font-weight: 700;
        color: white;
      }
    }

    .countdown-tip {
      font-size: 24rpx;
      color: #64748b;
    }

    .timeout-confirm-section {
      margin-bottom: 20rpx;

      .timeout-icon {
        margin-bottom: 16rpx;

        .timeout-clock {
          font-size: 48rpx;
          color: #f59e0b;
          animation: clockPulse 1s infinite;
        }
      }

      .timeout-title {
        display: block;
        margin-bottom: 12rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: #1e293b;
      }

      .timeout-message {
        display: block;
        margin-bottom: 32rpx;
        font-size: 24rpx;
        line-height: 1.6;
        color: #64748b;
      }

      .timeout-buttons {
        display: flex;
        justify-content: center;
        gap: 32rpx;

        .confirm-btn {
          background: none;
          border: none;
          padding: 16rpx 32rpx;
          border-radius: 12rpx;
          font: inherit;
          cursor: pointer;
          outline: inherit;
          display: flex;
          align-items: center;
          gap: 8rpx;
          transition: all 0.3s ease;
          min-width: 120rpx;
          justify-content: center;

          &:active {
            transform: scale(0.95);
          }

          &.paid-btn {
            background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
            border: 2rpx solid #00c9a7;

            .btn-icon {
              font-size: 24rpx;
              color: #00c9a7;
            }

            .btn-text {
              font-size: 24rpx;
              font-weight: 600;
              color: #00c9a7;
            }
          }

          &.cancel-btn {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border: 2rpx solid #e11d48;

            .btn-icon {
              font-size: 24rpx;
              color: #e11d48;
            }

            .btn-text {
              font-size: 24rpx;
              font-weight: 600;
              color: #e11d48;
            }
          }
        }
      }
    }

    .manual-query-section {
      margin-bottom: 20rpx;

      .manual-query-btn {
        background: none;
        border: none;
        padding: 0;
        margin: 0;
        font: inherit;
        cursor: pointer;
        outline: inherit;
        display: flex;
        align-items: center;
        gap: 8rpx;

        .query-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }

        .query-text {
          font-size: 24rpx;
          font-weight: 600;
          color: #00c9a7;
        }
      }

      .query-tip {
        font-size: 22rpx;
        color: #64748b;
      }
    }

    .connection-status {
      margin-bottom: 20rpx;

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .status-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }

        .status-text {
          font-size: 24rpx;
          color: #00c9a7;
        }
      }
    }

    .payment-security {
      display: flex;
      justify-content: center;
      gap: 32rpx;

      .security-item {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .security-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }

        .security-text {
          font-size: 24rpx;
          color: #64748b;
        }
      }
    }
  }
}

// 支付成功动画
.success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;

  .success-animation {
    background: white;
    padding: 60rpx 40rpx;
    border-radius: 20rpx;
    width: 80%;
    max-width: 400rpx;
    text-align: center;
    animation: scaleIn 0.5s ease;

    .success-circle {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      margin: 0 auto 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: successScale 0.6s ease;
      box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

      .success-check {
        font-size: 48rpx;
        color: white;
        animation: checkIn 0.5s ease 0.3s both;
      }
    }

    .success-title {
      display: block;
      margin-bottom: 16rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #1e293b;
      animation: slideUp 0.5s ease 0.6s both;
    }

    .success-subtitle {
      display: block;
      font-size: 24rpx;
      color: #64748b;
      animation: slideUp 0.5s ease 0.8s both;
    }
  }
}

// 取消订单动画
.cancel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;

  .cancel-modal {
    background: white;
    padding: 60rpx 40rpx;
    border-radius: 20rpx;
    width: 80%;
    max-width: 400rpx;
    text-align: center;
    animation: slideUp 0.3s ease;

    .cancel-icon-wrapper {
      position: relative;
      margin-bottom: 40rpx;

      .cancel-icon-bg {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto;
        border-radius: 50%;
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;
        border: 3rpx solid #dc2626;
      }

      .cancel-icon {
        font-size: 48rpx;
        color: #dc2626;
        animation: cancelRotate 1s infinite;
      }
    }

    .cancel-progress {
      position: relative;
      margin-bottom: 40rpx;

      .progress-bg {
        height: 20rpx;
        background: #f1f5f9;
        border-radius: 10rpx;
        overflow: hidden;
        position: relative;

        .cancel-progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
          border-radius: 10rpx;
          transition: width 0.3s ease;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressShine 1.5s infinite;
          }
        }
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24rpx;
        font-weight: 600;
        color: #1e293b;
        z-index: 1;
      }
    }

    .cancel-step {
      margin-bottom: 40rpx;

      .step-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #1e293b;
        animation: textPulse 1s infinite;
      }
    }

    .cancel-security {
      display: flex;
      justify-content: center;
      gap: 32rpx;

      .security-item {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .security-icon {
          font-size: 24rpx;
          color: #dc2626;
        }

        .security-text {
          font-size: 24rpx;
          color: #64748b;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes successScale {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes checkIn {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes textPulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes clockPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes cancelRotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
