/**
 * @description 面试房间演示数据
 * 当API接口调用失败时，使用这些数据进行演示
 */

// 演示面试官信息
export const demoInterviewer = {
  name: 'AI面试官小智',
  avatar: '',
  rating: 4.9,
  experience: '5年面试经验',
  specialties: ['前端技术', '项目经验', '团队协作'],
  introduction: '我是您的AI面试官，将为您提供专业的面试体验和实时反馈。'
}

// 演示公司信息
export const demoCompany = {
  name: '创新科技有限公司',
  logo: '',
  industry: '互联网/软件',
  scale: '500-1000人',
  location: '北京市朝阳区',
  description: '专注于前端技术创新的科技公司'
}

// 演示职位信息
export const demoJob = {
  title: '高级前端开发工程师',
  department: '技术部',
  level: '高级',
  salary: '20K-35K',
  requirements: [
    '3年以上前端开发经验',
    '熟练掌握Vue/React等主流框架',
    '具备良好的代码规范和团队协作能力',
    '有移动端开发经验优先'
  ],
  responsibilities: [
    '负责前端产品的开发和维护',
    '参与产品需求分析和技术方案设计',
    '优化前端性能，提升用户体验',
    '指导初级开发人员，推动技术创新'
  ]
}

// 演示面试问题扩展数据
export const demoQuestionDetails = {
  1: {
    background: '这是一道考察前端基础知识的问题',
    scoringCriteria: [
      { point: '框架理解深度', weight: 30, description: '对框架核心概念的理解' },
      { point: '实际应用经验', weight: 25, description: '在项目中的实际使用经验' },
      { point: '技术对比分析', weight: 25, description: '能否客观分析不同技术的优劣' },
      { point: '表达逻辑性', weight: 20, description: '回答的逻辑性和条理性' }
    ],
    referenceAnswer: '一个优秀的回答应该包含框架的核心特性、适用场景、与其他框架的对比，以及具体的项目实践经验。'
  },
  2: {
    background: '这道题考察候选人的问题解决能力和项目经验',
    scoringCriteria: [
      { point: '问题分析能力', weight: 35, description: '能否准确定位和分析问题' },
      { point: '解决方案设计', weight: 30, description: '解决方案的合理性和创新性' },
      { point: '实施过程管理', weight: 20, description: '项目实施的规划和执行' },
      { point: '结果评估', weight: 15, description: '对解决效果的量化评估' }
    ],
    referenceAnswer: '应该按照STAR法则来回答，重点突出问题的复杂性、解决思路的创新性和最终的效果。'
  }
}

// 演示实时建议模板
export const demoSuggestionTemplates = {
  speech: [
    {
      condition: (metrics: any) => metrics.clarity < 75,
      suggestion: {
        type: 'speech' as const,
        level: 'warning' as const,
        title: '语音清晰度',
        message: '建议放慢语速，注意发音清晰度',
        action: '深呼吸，放慢语速',
        icon: 'i-mdi-microphone-variant'
      }
    },
    {
      condition: (metrics: any) => metrics.volume < 70,
      suggestion: {
        type: 'speech' as const,
        level: 'info' as const,
        title: '音量控制',
        message: '音量偏低，建议适当提高音量',
        action: '调整麦克风距离',
        icon: 'i-mdi-volume-high'
      }
    }
  ],
  video: [
    {
      condition: (metrics: any) => metrics.eyeContact < 70,
      suggestion: {
        type: 'video' as const,
        level: 'warning' as const,
        title: '眼神交流',
        message: '建议增加与镜头的眼神接触',
        action: '看向摄像头',
        icon: 'i-mdi-eye'
      }
    },
    {
      condition: (metrics: any) => metrics.posture < 75,
      suggestion: {
        type: 'video' as const,
        level: 'info' as const,
        title: '坐姿调整',
        message: '保持良好坐姿，身体略向前倾',
        action: '调整坐姿',
        icon: 'i-mdi-human-male'
      }
    }
  ],
  text: [
    {
      condition: (metrics: any) => metrics.structure < 70,
      suggestion: {
        type: 'text' as const,
        level: 'warning' as const,
        title: '回答结构',
        message: '建议按照STAR法则组织回答',
        action: '使用STAR结构',
        icon: 'i-mdi-format-list-bulleted'
      }
    },
    {
      condition: (metrics: any) => metrics.keywords < 65,
      suggestion: {
        type: 'text' as const,
        level: 'critical' as const,
        title: '关键词缺失',
        message: '回答中缺少重要技术关键词',
        action: '补充技术词汇',
        icon: 'i-mdi-key'
      }
    }
  ]
}

// 演示评分标准
export const demoScoringStandards = {
  excellent: { min: 90, label: '优秀', color: '#52C41A', description: '表现出色，完全符合岗位要求' },
  good: { min: 80, label: '良好', color: '#00C9A7', description: '表现良好，基本符合岗位要求' },
  average: { min: 70, label: '一般', color: '#FAAD14', description: '表现一般，需要进一步提升' },
  poor: { min: 0, label: '待改进', color: '#F5222D', description: '表现不佳，需要重点改进' }
}

// 演示面试报告模板
export const demoReportTemplate = {
  summary: {
    overallScore: 85,
    level: 'good',
    recommendation: '建议录用',
    highlights: [
      '技术基础扎实，对前端框架有深入理解',
      '项目经验丰富，具备解决复杂问题的能力',
      '表达清晰，逻辑思维能力强'
    ],
    improvements: [
      '可以进一步提升架构设计能力',
      '建议加强对新技术趋势的关注'
    ]
  },
  detailedAnalysis: {
    technicalSkills: {
      score: 88,
      strengths: ['框架掌握', '项目实践', '代码质量'],
      weaknesses: ['架构设计', '性能优化']
    },
    softSkills: {
      score: 82,
      strengths: ['沟通表达', '学习能力'],
      weaknesses: ['领导力', '创新思维']
    }
  }
}

// 演示面试流程配置
export const demoInterviewFlow = {
  phases: [
    { name: '自我介绍', duration: 180, questions: [1] },
    { name: '技术基础', duration: 600, questions: [2, 3, 7] },
    { name: '项目经验', duration: 480, questions: [4, 8] },
    { name: '综合素质', duration: 360, questions: [5, 6] },
    { name: '反向提问', duration: 180, questions: [10] }
  ],
  totalDuration: 1800, // 30分钟
  breakTime: 60 // 1分钟休息时间
}
