// ==================== 智能面试系统 - 基础样式 ====================

// 导入变量和混入
@import './variables';
@import './mixins';

// ==================== 全局重置 ====================
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

// ==================== 页面基础样式 ====================
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $text-primary;
  background: $bg-gradient;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ==================== 项目固定尺寸：430 * 932 ====================
body {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
}

#app {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
}

// ==================== 通用布局类 ====================
.container {
  width: 100%;
  padding: 0 $spacing-xl;
  margin: 0 auto;
}

.flex {
  display: flex;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

// ==================== 间距工具类 ====================
.m-0 {
  margin: 0;
}
.m-xs {
  margin: $spacing-xs;
}
.m-sm {
  margin: $spacing-sm;
}
.m-base {
  margin: $spacing-base;
}
.m-lg {
  margin: $spacing-lg;
}
.m-xl {
  margin: $spacing-xl;
}

.mt-0 {
  margin-top: 0;
}
.mt-xs {
  margin-top: $spacing-xs;
}
.mt-sm {
  margin-top: $spacing-sm;
}
.mt-base {
  margin-top: $spacing-base;
}
.mt-lg {
  margin-top: $spacing-lg;
}
.mt-xl {
  margin-top: $spacing-xl;
}

.mb-0 {
  margin-bottom: 0;
}
.mb-xs {
  margin-bottom: $spacing-xs;
}
.mb-sm {
  margin-bottom: $spacing-sm;
}
.mb-base {
  margin-bottom: $spacing-base;
}
.mb-lg {
  margin-bottom: $spacing-lg;
}
.mb-xl {
  margin-bottom: $spacing-xl;
}

.p-0 {
  padding: 0;
}
.p-xs {
  padding: $spacing-xs;
}
.p-sm {
  padding: $spacing-sm;
}
.p-base {
  padding: $spacing-base;
}
.p-lg {
  padding: $spacing-lg;
}
.p-xl {
  padding: $spacing-xl;
}

// ==================== 文本工具类 ====================
.text-xs {
  font-size: $font-size-xs;
}
.text-sm {
  font-size: $font-size-sm;
}
.text-base {
  font-size: $font-size-base;
}
.text-lg {
  font-size: $font-size-lg;
}
.text-xl {
  font-size: $font-size-xl;
}
.text-2xl {
  font-size: $font-size-2xl;
}
.text-3xl {
  font-size: $font-size-3xl;
}

.text-light {
  font-weight: $font-weight-light;
}
.text-normal {
  font-weight: $font-weight-normal;
}
.text-medium {
  font-weight: $font-weight-medium;
}
.text-semibold {
  font-weight: $font-weight-semibold;
}
.text-bold {
  font-weight: $font-weight-bold;
}

.text-primary {
  color: $text-primary;
}
.text-secondary {
  color: $text-secondary;
}
.text-tertiary {
  color: $text-tertiary;
}
.text-disabled {
  color: $text-disabled;
}
.text-inverse {
  color: $text-inverse;
}

.text-brand {
  color: $primary-color;
}
.text-success {
  color: $success-color;
}
.text-warning {
  color: $warning-color;
}
.text-error {
  color: $error-color;
}
.text-info {
  color: $info-color;
}

.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.text-ellipsis {
  @include text-ellipsis;
}

.text-ellipsis-2 {
  @include text-ellipsis(2);
}

.text-ellipsis-3 {
  @include text-ellipsis(3);
}

// ==================== 背景工具类 ====================
.bg-primary {
  background: $bg-primary;
}
.bg-secondary {
  background: $bg-secondary;
}
.bg-tertiary {
  background: $bg-tertiary;
}

.bg-brand {
  background: $primary-color;
}
.bg-success {
  background: $success-color;
}
.bg-warning {
  background: $warning-color;
}
.bg-error {
  background: $error-color;
}
.bg-info {
  background: $info-color;
}

.bg-gradient-primary {
  background: $gradient-primary;
}

.bg-gradient-secondary {
  background: $gradient-secondary;
}

// ==================== 圆角工具类 ====================
.rounded-none {
  border-radius: 0;
}
.rounded-xs {
  border-radius: $radius-xs;
}
.rounded-sm {
  border-radius: $radius-sm;
}
.rounded-base {
  border-radius: $radius-base;
}
.rounded-md {
  border-radius: $radius-md;
}
.rounded-lg {
  border-radius: $radius-lg;
}
.rounded-xl {
  border-radius: $radius-xl;
}
.rounded-2xl {
  border-radius: $radius-2xl;
}
.rounded-full {
  border-radius: $radius-full;
}

// ==================== 阴影工具类 ====================
.shadow-none {
  box-shadow: none;
}
.shadow-xs {
  box-shadow: $shadow-xs;
}
.shadow-sm {
  box-shadow: $shadow-sm;
}
.shadow-base {
  box-shadow: $shadow-base;
}
.shadow-md {
  box-shadow: $shadow-md;
}
.shadow-lg {
  box-shadow: $shadow-lg;
}
.shadow-xl {
  box-shadow: $shadow-xl;
}

.shadow-primary {
  box-shadow: $shadow-primary;
}

// ==================== 边框工具类 ====================
.border-none {
  border: none;
}
.border-light {
  border: 1rpx solid $border-light;
}
.border-medium {
  border: 1rpx solid $border-medium;
}
.border-dark {
  border: 1rpx solid $border-dark;
}
.border-brand {
  border: 1rpx solid $primary-color;
}

// ==================== 显示工具类 ====================
.hidden {
  display: none;
}
.block {
  display: block;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}

// ==================== 位置工具类 ====================
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.sticky {
  position: sticky;
}

// ==================== 层级工具类 ====================
.z-dropdown {
  z-index: $z-index-dropdown;
}
.z-sticky {
  z-index: $z-index-sticky;
}
.z-fixed {
  z-index: $z-index-fixed;
}
.z-modal {
  z-index: $z-index-modal;
}

// ==================== 动画工具类 ====================
.fade-in {
  @include fade-in;
}

.slide-in-up {
  @include slide-in-up;
}

.scale-in {
  @include scale-in;
}

.transition-fast {
  transition: all $transition-fast;
}

.transition-base {
  transition: all $transition-base;
}

.transition-slow {
  transition: all $transition-slow;
}

// ==================== 智能面试系统专用样式 ====================
// 页面容器
.page-container {
  position: relative;
  min-height: 100vh;
  background: $bg-gradient;
}

// 导航栏容器
.nav-wrapper {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: $z-index-fixed;
  background: $gradient-primary;
  box-shadow: 0 4rpx 20rpx rgba($primary-color, 0.2);
}

// 主内容区域
.main-wrapper {
  min-height: 100vh;
  padding-top: $navbar-height;
  padding-bottom: $tabbar-height;
}

// 底部导航容器
.bottom-wrapper {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-fixed;
  background: $bg-primary;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

// 内容卡片
.content-card {
  @include card-base;
  padding: $card-padding;
  margin-bottom: $spacing-lg;
}

// 数据卡片
.data-card {
  @include data-card;
}

// 状态徽章
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  border-radius: $radius-base;

  &.success {
    @include status-success;
  }

  &.warning {
    @include status-warning;
  }

  &.error {
    @include status-error;
  }

  &.info {
    @include status-info;
  }
}

// 加载骨架屏
.skeleton {
  @include loading-skeleton;
  border-radius: $radius-sm;

  &.skeleton-text {
    height: $font-size-base;
    margin-bottom: $spacing-xs;
  }

  &.skeleton-title {
    width: 60%;
    height: $font-size-lg;
    margin-bottom: $spacing-base;
  }

  &.skeleton-button {
    height: $btn-height-base;
    border-radius: $radius-xl;
  }

  &.skeleton-card {
    height: 200rpx;
    border-radius: $card-radius;
  }
}

// 空状态
.empty-state {
  @include flex-center;
  flex-direction: column;
  padding: $spacing-4xl $spacing-xl;
  text-align: center;

  .empty-icon {
    margin-bottom: $spacing-lg;
    font-size: 120rpx;
    color: $text-disabled;
  }

  .empty-title {
    margin-bottom: $spacing-sm;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-secondary;
  }

  .empty-desc {
    font-size: $font-size-sm;
    line-height: $line-height-relaxed;
    color: $text-tertiary;
  }
}

// 分割线
.divider {
  height: 1rpx;
  margin: $spacing-lg 0;
  background: $border-light;

  &.vertical {
    width: 1rpx;
    height: auto;
    margin: 0 $spacing-lg;
  }
}

// 安全区域
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

// ==================== 响应式样式 ====================
@include mobile {
  .container {
    padding: 0 $spacing-base;
  }

  .main-wrapper {
    padding: $spacing-base;
  }
}

@include tablet {
  .container {
    max-width: 750rpx;
  }
}

@include desktop {
  .container {
    max-width: 1200rpx;
  }

  .main-wrapper {
    padding: $spacing-xl;
  }
}

// ==================== 性能优化 ====================
// GPU加速的元素
.gpu-accelerate {
  @include gpu-accelerate;
}

// 滚动优化
.smooth-scroll {
  @include smooth-scroll;
}

// ==================== 可访问性 ====================
// 聚焦样式
*:focus {
  outline: 2rpx solid $primary-color;
  outline-offset: 2rpx;
}

// 屏幕阅读器专用
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
