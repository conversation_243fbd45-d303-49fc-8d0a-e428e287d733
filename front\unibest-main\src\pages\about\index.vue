<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// 关于我们页面
// 展示应用信息、版本信息、用户协议等内容

// 应用版本信息
const appInfo = ref({
  name: 'AI面试助手',
  version: '1.0.0',
  buildDate: '2024-01-15',
  description: '基于AI技术的多模态模拟面试评测智能体，为高校学生提供专业的面试训练和评测服务',
})

// 开发团队信息
const teamInfo = ref([
  { name: '产品经理', value: '张三' },
  { name: '技术负责人', value: '李四' },
  { name: 'UI设计师', value: '王五' },
  { name: 'AI算法工程师', value: '赵六' },
])

// 联系方式
const contactInfo = ref([
  { name: '客服邮箱', value: '<EMAIL>', type: 'email' },
  { name: '官方网站', value: 'https://www.aiinterview.com', type: 'website' },
  { name: '技术支持', value: '************', type: 'phone' },
])

// 功能特色
const features = ref([
  { icon: 'i-fa-solid-brain', title: 'AI智能评测', desc: '基于深度学习的多模态分析技术' },
  { icon: 'i-fa-solid-chart-line', title: '数据可视化', desc: '直观的雷达图展示面试表现' },
  { icon: 'i-fa-solid-graduation-cap', title: '个性化推荐', desc: '根据面试结果推荐学习资源' },
  { icon: 'i-fa-solid-mobile-alt', title: '多端支持', desc: '支持小程序、H5、APP多端使用' },
])

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 跳转到用户协议
const goToAgreement = () => {
  uni.navigateTo({ url: '/pages/about/agreement' })
}

// 跳转到隐私政策
const goToPrivacy = () => {
  uni.navigateTo({ url: '/pages/about/privacy-policy' })
}

// 复制文本到剪贴板
const copyText = (text: string, type: string) => {
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: `已复制${type}`,
        icon: 'success',
      })
    },
  })
}

// 拨打电话
const makeCall = (phone: string) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

// 处理联系方式点击
const handleContactClick = (contact: any) => {
  switch (contact.type) {
    case 'email':
    case 'website':
      copyText(contact.value, contact.name)
      break
    case 'phone':
      makeCall(contact.value)
      break
  }
}
</script>

<template>
  <view class="about-page">
    <!-- 头部导航 -->
    <HeadBar title="关于我们" :show-back="true" :show-right-button="false" />
    <view class="placeholder"></view>

    <view class="content-area" style="margin-top: 80rpx">
      <!-- 应用Logo和基本信息 -->
      <view class="app-info-card">
        <view class="app-logo">
          <text class="i-fa-solid-robot" style="font-size: 80rpx; color: #00c9a7"></text>
        </view>
        <text class="app-name">{{ appInfo.name }}</text>
        <text class="app-version">版本 {{ appInfo.version }}</text>
        <text class="app-desc">{{ appInfo.description }}</text>
      </view>

      <!-- 功能特色 -->
      <view class="features-section">
        <text class="section-title">功能特色</text>
        <view class="features-grid">
          <view class="feature-item" v-for="(feature, index) in features" :key="index">
            <view class="feature-icon">
              <text :class="feature.icon" style="font-size: 32rpx; color: #00c9a7"></text>
            </view>
            <text class="feature-title">{{ feature.title }}</text>
            <text class="feature-desc">{{ feature.desc }}</text>
          </view>
        </view>
      </view>

      <!-- 开发团队 -->
      <view class="team-section">
        <text class="section-title">开发团队</text>
        <view class="team-card">
          <view class="team-item" v-for="(member, index) in teamInfo" :key="index">
            <text class="team-role">{{ member.name }}</text>
            <text class="team-name">{{ member.value }}</text>
          </view>
        </view>
      </view>

      <!-- 联系我们 -->
      <view class="contact-section">
        <text class="section-title">联系我们</text>
        <view class="contact-card">
          <view
            class="contact-item"
            v-for="(contact, index) in contactInfo"
            :key="index"
            @click="handleContactClick(contact)"
          >
            <view class="contact-icon">
              <text
                :class="
                  contact.type === 'email'
                    ? 'i-fa-solid-envelope'
                    : contact.type === 'website'
                      ? 'i-fa-solid-globe'
                      : 'i-fa-solid-phone'
                "
                style="font-size: 24rpx"
              ></text>
            </view>
            <view class="contact-info">
              <text class="contact-label">{{ contact.name }}</text>
              <text class="contact-value">{{ contact.value }}</text>
            </view>
            <text class="i-fa-solid-copy" style="font-size: 20rpx; color: #999"></text>
          </view>
        </view>
      </view>

      <!-- 法律信息 -->
      <view class="legal-section">
        <text class="section-title">法律信息</text>
        <view class="legal-card">
          <view class="legal-item" @click="goToAgreement">
            <text class="legal-title">用户协议</text>
            <text class="i-fa-solid-chevron-right" style="font-size: 20rpx; color: #999"></text>
          </view>
          <view class="legal-item" @click="goToPrivacy">
            <text class="legal-title">隐私政策</text>
            <text class="i-fa-solid-chevron-right" style="font-size: 20rpx; color: #999"></text>
          </view>
        </view>
      </view>

      <!-- 版权信息 -->
      <view class="copyright">
        <text class="copyright-text">© 2024 AI面试助手团队</text>
        <text class="build-info">构建日期: {{ appInfo.buildDate }}</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.about-page {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background: #f7f9fc;
}

// 头部导航
.nav-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 24rpx;
  color: #333;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.2s;

  &:active {
    background: #e0e0e0;
    transform: scale(0.9);
  }
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}

.placeholder {
  width: 60rpx;
}

// 内容区域
.content-area {
  padding: 40rpx 32rpx;
}

// 应用信息卡片
.app-info-card {
  padding: 60rpx 40rpx 50rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.app-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 32rpx auto;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0, 201, 167, 0.2);
}

.app-name {
  display: block;
  margin-bottom: 12rpx;
  font-size: 42rpx;
  font-weight: bold;
  color: #222;
}

.app-version {
  display: block;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  color: #00c9a7;
}

.app-desc {
  display: block;
  font-size: 26rpx;
  line-height: 1.6;
  color: #666;
}

// 功能特色
.features-section {
  margin-bottom: 40rpx;
}

.section-title {
  position: relative;
  display: block;
  padding-left: 8rpx;
  margin-bottom: 32rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #222;

  &::after {
    position: absolute;
    bottom: -8rpx;
    left: 0;
    width: 64rpx;
    height: 6rpx;
    content: '';
    background: #00c9a7;
    border-radius: 4rpx;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.feature-item {
  padding: 32rpx 20rpx;
  text-align: center;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s;

  &:active {
    transform: translateY(-4rpx);
  }
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 20rpx auto;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
  border-radius: 50%;
}

.feature-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #222;
}

.feature-desc {
  font-size: 22rpx;
  line-height: 1.4;
  color: #666;
}

// 开发团队
.team-section {
  margin-bottom: 40rpx;
}

.team-card {
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.team-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.team-role {
  font-size: 28rpx;
  color: #666;
}

.team-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #222;
}

// 联系我们
.contact-section {
  margin-bottom: 40rpx;
}

.contact-card {
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  color: #00c9a7;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
  border-radius: 50%;
}

.contact-info {
  flex: 1;
}

.contact-label {
  display: block;
  margin-bottom: 4rpx;
  font-size: 26rpx;
  color: #666;
}

.contact-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #222;
}

// 法律信息
.legal-section {
  margin-bottom: 40rpx;
}

.legal-card {
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }
}

.legal-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #222;
}

// 版权信息
.copyright {
  padding: 40rpx 0;
  text-align: center;
}

.copyright-text {
  display: block;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.build-info {
  font-size: 22rpx;
  color: #ccc;
}
</style>
