<script setup lang="ts">
import { ref, computed } from 'vue'

/**
 * @description 面试历史表格组件
 * @prop historyData 历史面试数据数组
 * <AUTHOR>
 */
interface HistoryItem {
  id: string
  date: string
  position: string
  company: string
  score: number
  duration: string
  status: 'excellent' | 'good' | 'fair' | 'poor'
}

const props = defineProps<{
  historyData: HistoryItem[]
}>()

const emit = defineEmits<{
  'view-report': [id: string]
}>()

// 排序字段和顺序
const sortField = ref<'date' | 'score'>('date')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 筛选条件
const filterStatus = ref<string>('all')

/**
 * @description 获取状态标签样式
 */
const getStatusClass = (status: string) => {
  const classMap = {
    excellent: 'badge-excellent',
    good: 'badge-good',
    fair: 'badge-fair',
    poor: 'badge-poor',
  }
  return classMap[status] || ''
}

/**
 * @description 获取状态标签文本
 */
const getStatusText = (status: string) => {
  const textMap = {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '待提升',
  }
  return textMap[status] || ''
}

/**
 * @description 获取排序图标
 */
const getSortIcon = (field: 'date' | 'score') => {
  if (sortField.value !== field) return 'i-fa-solid-sort'
  return sortOrder.value === 'asc' ? 'i-fa-solid-sort-up' : 'i-fa-solid-sort-down'
}

/**
 * @description 处理排序
 */
const handleSort = (field: 'date' | 'score') => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortOrder.value = 'desc'
  }
}

/**
 * @description 处理筛选
 */
const handleFilter = (status: string) => {
  filterStatus.value = status
}

/**
 * @description 计算显示的数据
 */
const displayData = computed(() => {
  let result = [...props.historyData]

  // 筛选
  if (filterStatus.value !== 'all') {
    result = result.filter((item) => item.status === filterStatus.value)
  }

  // 排序
  result.sort((a, b) => {
    let compareValue = 0
    if (sortField.value === 'date') {
      compareValue = new Date(a.date).getTime() - new Date(b.date).getTime()
    } else if (sortField.value === 'score') {
      compareValue = a.score - b.score
    }
    return sortOrder.value === 'asc' ? compareValue : -compareValue
  })

  return result
})

/**
 * @description 查看报告
 */
const viewReport = (id: string) => {
  emit('view-report', id)
}
</script>

<template>
  <view class="interview-history">
    <!-- 头部区域 -->
    <view class="history-header">
      <view class="header-content">
        <text class="header-title">面试历史记录</text>
        <text class="header-subtitle">{{ displayData.length }} 条记录</text>
      </view>

      <!-- 操作区域 -->
      <view class="header-actions">
        <!-- 排序选择器 -->
        <view class="sort-selector">
          <view
            class="sort-option"
            :class="{ active: sortField === 'date' }"
            @click="handleSort('date')"
          >
            <text class="sort-text">按时间</text>
            <text class="sort-arrow" :class="getSortIcon('date')"></text>
          </view>
          <view
            class="sort-option"
            :class="{ active: sortField === 'score' }"
            @click="handleSort('score')"
          >
            <text class="sort-text">按分数</text>
            <text class="sort-arrow" :class="getSortIcon('score')"></text>
          </view>
        </view>

        <!-- 筛选器 -->
        <view class="filter-container">
          <scroll-view class="filter-scroll" scroll-x>
            <view class="filter-tabs">
              <view
                class="filter-tab"
                :class="{ active: filterStatus === 'all' }"
                @click="handleFilter('all')"
              >
                全部
              </view>
              <view
                class="filter-tab"
                :class="{ active: filterStatus === 'excellent' }"
                @click="handleFilter('excellent')"
              >
                优秀
              </view>
              <view
                class="filter-tab"
                :class="{ active: filterStatus === 'good' }"
                @click="handleFilter('good')"
              >
                良好
              </view>
              <view
                class="filter-tab"
                :class="{ active: filterStatus === 'fair' }"
                @click="handleFilter('fair')"
              >
                一般
              </view>
              <view
                class="filter-tab"
                :class="{ active: filterStatus === 'poor' }"
                @click="handleFilter('poor')"
              >
                待提升
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="history-content">
      <!-- 面试记录卡片 -->
      <view
        class="interview-card"
        v-for="item in displayData"
        :key="item.id"
        @click="viewReport(item.id)"
      >
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="job-info">
            <text class="job-title">{{ item.position }}</text>
            <view class="company-info">
              <text class="company-name">{{ item.company }}</text>
              <text class="interview-date">{{ item.date }}</text>
            </view>
          </view>

          <view class="score-section">
            <view class="score-display">
              <text class="score-number">{{ item.score }}</text>
              <text class="score-unit">分</text>
            </view>
            <view class="status-badge" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </view>
          </view>
        </view>

        <!-- 卡片底部 -->
        <view class="card-footer">
          <view class="duration-info">
            <text class="duration-icon i-fa-solid-clock"></text>
            <text class="duration-text">面试时长 {{ item.duration }}</text>
          </view>

          <view class="action-button">
            <text class="action-text">查看详情</text>
            <text class="action-icon i-fa-solid-arrow-right"></text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="displayData.length === 0" class="empty-container">
        <view class="empty-content">
          <text class="empty-icon i-fa-solid-clipboard-list"></text>
          <text class="empty-title">暂无面试记录</text>
          <text class="empty-desc">完成面试后，记录将在这里显示</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.interview-history {
  position: relative;
  // 毛玻璃背景效果 - 参考HeadBar的设计
  background: rgba(0, 201, 167, 0.85);
  backdrop-filter: blur(20rpx) saturate(180%);
  -webkit-backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 24rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.3),
    0 16rpx 64rpx rgba(0, 201, 167, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);

  // 毛玻璃渐变叠加层
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 201, 167, 0.3) 0%,
      rgba(79, 209, 199, 0.2) 50%,
      rgba(124, 58, 237, 0.1) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  // 装饰元素
  &::after {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 30rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8rpx);
    -webkit-backdrop-filter: blur(8rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: floating 6s ease-in-out infinite;
    z-index: 1;
  }
}

// 头部区域 - 毛玻璃效果
.history-header {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15rpx) saturate(150%);
  -webkit-backdrop-filter: blur(15rpx) saturate(150%);
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 2;
  box-shadow: inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

.header-content {
  margin-bottom: 24rpx;
}

.header-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 1rpx;
  background: linear-gradient(45deg, #fff, #f0f8ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleGlow 3s ease-in-out infinite;
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

// 排序选择器 - 毛玻璃按钮
.sort-selector {
  display: flex;
  gap: 16rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15rpx) saturate(150%);
  -webkit-backdrop-filter: blur(15rpx) saturate(150%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.15),
    0 2rpx 8rpx rgba(255, 255, 255, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);

  &.active {
    background: rgba(255, 255, 255, 0.4);
    color: #fff;
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow:
      0 6rpx 20rpx rgba(0, 0, 0, 0.2),
      0 3rpx 10rpx rgba(255, 255, 255, 0.3),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.sort-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #fff;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.sort-arrow {
  font-size: 20rpx;
  color: #fff;
  opacity: 0.8;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

// 筛选器 - 毛玻璃标签
.filter-container {
  width: 100%;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tabs {
  display: inline-flex;
  gap: 12rpx;
  padding: 4rpx 0;
}

.filter-tab {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx) saturate(120%);
  -webkit-backdrop-filter: blur(10rpx) saturate(120%);
  border-radius: 20rpx;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);

  &.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow:
      0 4rpx 16rpx rgba(0, 0, 0, 0.15),
      0 2rpx 8rpx rgba(255, 255, 255, 0.25),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 内容区域 - 毛玻璃容器
.history-content {
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx) saturate(120%);
  -webkit-backdrop-filter: blur(10rpx) saturate(120%);
  min-height: 400rpx;
  max-height: 800rpx;
  overflow-y: auto;
  position: relative;
  z-index: 2;
}

// 面试卡片 - 毛玻璃卡片
.interview-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx) saturate(150%);
  -webkit-backdrop-filter: blur(20rpx) saturate(150%);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.15),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  // 卡片装饰元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #00c9a7, #4fd1c7, #00c9a7);
    opacity: 0.8;
  }

  &:hover {
    transform: translateY(-4rpx) scale(1.02);
    box-shadow:
      0 16rpx 48rpx rgba(0, 201, 167, 0.25),
      0 8rpx 24rpx rgba(0, 0, 0, 0.12),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  }

  &:active {
    transform: translateY(-2rpx) scale(0.98);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  padding: 28rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
}

.job-info {
  flex: 1;
  min-width: 0;
}

.job-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.company-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.company-name {
  font-size: 26rpx;
  color: #475569;
  font-weight: 500;
}

.interview-date {
  font-size: 24rpx;
  color: #94a3b8;
}

.score-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
  flex-shrink: 0;
}

.score-display {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1), rgba(79, 209, 199, 0.05));
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.2);
}

.score-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #00c9a7;
  text-shadow: 0 2rpx 4rpx rgba(0, 201, 167, 0.3);
}

.score-unit {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  white-space: nowrap;
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.badge-excellent {
  color: #00c9a7;
  background: rgba(0, 201, 167, 0.15);
  border: 1rpx solid rgba(0, 201, 167, 0.3);
}

.badge-good {
  color: #007aff;
  background: rgba(0, 122, 255, 0.15);
  border: 1rpx solid rgba(0, 122, 255, 0.3);
}

.badge-fair {
  color: #f0ad4e;
  background: rgba(240, 173, 78, 0.15);
  border: 1rpx solid rgba(240, 173, 78, 0.3);
}

.badge-poor {
  color: #dd524d;
  background: rgba(221, 82, 77, 0.15);
  border: 1rpx solid rgba(221, 82, 77, 0.3);
}

.card-footer {
  padding: 20rpx 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.duration-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.duration-icon {
  font-size: 20rpx;
  color: #64748b;
}

.duration-text {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 201, 167, 0.9);
  backdrop-filter: blur(15rpx) saturate(150%);
  -webkit-backdrop-filter: blur(15rpx) saturate(150%);
  color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4rpx 16rpx rgba(0, 201, 167, 0.3),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);

  &:hover {
    transform: scale(1.05);
    background: rgba(0, 201, 167, 1);
    box-shadow:
      0 6rpx 20rpx rgba(0, 201, 167, 0.4),
      0 3rpx 10rpx rgba(0, 0, 0, 0.15),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
  }

  &:active {
    transform: scale(0.95);
  }
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.action-icon {
  font-size: 18rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

// 空状态 - 毛玻璃效果
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: rgba(255, 255, 255, 0.6);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: floating 4s ease-in-out infinite;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.empty-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.5;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

// 动画定义 - 参考HeadBar的动画
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(5deg);
  }
}

@keyframes titleGlow {
  0%,
  100% {
    text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8);
  }
}

// H5端增强效果
/* #ifdef H5 */
.interview-history {
  backdrop-filter: blur(25rpx) saturate(200%);
  -webkit-backdrop-filter: blur(25rpx) saturate(200%);
}

.interview-card {
  cursor: pointer;

  &:hover {
    backdrop-filter: blur(25rpx) saturate(180%);
    -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  }
}

.sort-option,
.filter-tab,
.action-button {
  cursor: pointer;
  user-select: none;
}
/* #endif */

// 响应式适配
@media screen and (max-width: 750rpx) {
  .header-title {
    font-size: 36rpx;
  }

  .sort-text,
  .filter-tab {
    font-size: 24rpx;
  }

  .job-title {
    font-size: 30rpx;
  }

  .score-number {
    font-size: 42rpx;
  }
}
</style>
