import request from '@/utils/request'
import { CustomRequestOptions } from '@/interceptors/request'
import { ApiResponse } from '../app'

// 类型定义
export interface UserAbilities {
  professionalKnowledge: number // 专业知识
  logicalThinking: number // 逻辑思维
  languageExpression: number // 语言表达
  stressResistance: number // 抗压能力
  teamCollaboration: number // 团队协作
  innovation: number // 创新能力
}

export interface StudyStats {
  totalInterviews: number // 总面试次数
  averageScore: number // 平均得分
  improvementRate: number // 提升率
  targetPosition: string // 目标岗位
  recentInterviews?: number // 最近7天面试次数
  weeklyProgress?: number // 本周进度
}

export interface SmartTask {
  id: number
  title: string
  description: string
  type: 'skill' | 'expression' | 'knowledge' | 'practice'
  priority: 'high' | 'medium' | 'low'
  link: string
  estimatedTime?: number // 预计完成时间（分钟）
  deadline?: string // 截止日期
}

export interface DashboardSummary {
  user: {
    id: string
    name: string
    avatar?: string
    targetPosition?: string
    level?: number
  }
  welcomeMessage: string
  aiMotivation: string
  todayTasks?: number // 今日待完成任务数
  unreadMessages?: number // 未读消息数
  nextInterview?: {
    // 下次面试安排
    time: string
    position: string
    company: string
  }
}

export interface InterviewHistory {
  id: number
  company: string
  position: string
  date: string
  duration: string
  score: number
  status: 'completed' | 'in-progress' | 'scheduled'
  feedback?: string
}

/**
 * @description 获取首页仪表盘汇总数据
 * @param options 请求选项
 */
export async function getDashboardSummary({
  options,
}: {
  options?: CustomRequestOptions
} = {}) {
  return request<ApiResponse>('/dashboard/summary', {
    method: 'GET',
    ...(options || {}),
  })
}

/**
 * @description 获取用户能力评估数据
 * @param options 请求选项
 */
export async function getUserAbilities({
  options,
}: {
  options?: CustomRequestOptions
} = {}) {
  return request<ApiResponse>('/dashboard/abilities', {
    method: 'GET',
    ...(options || {}),
  })
}

/**
 * @description 获取学习统计数据
 * @param options 请求选项
 */
export async function getStudyStats({
  options,
}: {
  options?: CustomRequestOptions
} = {}) {
  return request<ApiResponse>('/dashboard/study-stats', {
    method: 'GET',
    ...(options || {}),
  })
}

/**
 * @description 获取智能推荐任务
 * @param params 查询参数
 * @param options 请求选项
 */
export async function getSmartTasks({
  params,
  options,
}: {
  params?: {
    limit?: number // 获取任务数量
    type?: string // 任务类型
  }
  options?: CustomRequestOptions
} = {}) {
  return request<ApiResponse>('/dashboard/smart-tasks', {
    method: 'GET',
    params: params || { limit: 5 },
    ...(options || {}),
  })
}

/**
 * @description 获取最近面试记录
 * @param params 查询参数
 * @param options 请求选项
 */
export async function getRecentInterviews({
  params,
  options,
}: {
  params?: {
    limit?: number // 获取记录数量
    page?: number // 页码
  }
  options?: CustomRequestOptions
} = {}) {
  return request<ApiResponse>('/dashboard/recent-interviews', {
    method: 'GET',
    params: params || { limit: 5, page: 1 },
    ...(options || {}),
  })
}

/**
 * @description 更新用户目标岗位
 * @param params 更新参数
 * @param options 请求选项
 */
export async function updateTargetPosition({
  params,
  options,
}: {
  params: {
    targetPosition: string
  }
  options?: CustomRequestOptions
}) {
  return request<ApiResponse>('/dashboard/update-target', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
    ...(options || {}),
  })
}

/**
 * @description 标记任务为已完成
 * @param params 任务参数
 * @param options 请求选项
 */
export async function completeTask({
  params,
  options,
}: {
  params: {
    taskId: number
  }
  options?: CustomRequestOptions
}) {
  return request<ApiResponse>('/dashboard/complete-task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
    ...(options || {}),
  })
}

/**
 * @description 获取首页所有数据（聚合接口）
 * @param options 请求选项
 */
export async function getDashboardData({
  options,
}: {
  options?: CustomRequestOptions
} = {}) {
  return request<ApiResponse>('/dashboard/all', {
    method: 'GET',
    ...(options || {}),
  })
}
