/**
 * @description 面试房间页面演示数据集成示例
 * 展示如何在面试房间页面中集成演示数据
 *
 * 注意：这个文件仅用于展示使用方法，不会被实际引入到项目中
 * 实际使用时，需要在面试房间页面的script部分添加相应的代码
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { integrateInterviewRoomDemoData, cleanupInterviewRoomDemoData } from './integration'
import { interviewRoomHelper } from './roomHelper'

/**
 * 面试房间页面中集成演示数据的示例
 * 在面试房间页面的script部分添加以下代码
 */
export const setupInterviewRoomWithDemoData = () => {
  // 演示模式标志
  const isDemoMode = ref(false)

  // 在页面加载时集成演示数据
  onMounted(() => {
    // 集成演示数据
    const { isDemoMode: demoMode } = integrateInterviewRoomDemoData()
    isDemoMode.value = demoMode

    if (isDemoMode.value) {
      console.log('面试房间页面使用演示模式')
      // 可以在这里添加演示模式下的特殊处理
    }
  })

  // 在页面卸载时清理演示数据
  onUnmounted(() => {
    // 清理演示数据
    cleanupInterviewRoomDemoData()
  })

  // 创建WebSocket连接的辅助函数
  const createWebSocketConnection = (url: string) => {
    // 使用演示数据系统提供的WebSocket创建函数
    // 这样在WebSocket连接失败时，会自动使用模拟的WebSocket连接
    return interviewRoomHelper.createWebSocket(url)
  }

  return {
    isDemoMode,
    createWebSocketConnection,
  }
}

/**
 * 面试房间页面中使用演示数据的示例
 * 在面试房间页面的script部分添加以下代码
 */
export const useInterviewRoomDemoData = () => {
  // 集成演示数据
  const { isDemoMode, createWebSocketConnection } = setupInterviewRoomWithDemoData()

  // 创建WebSocket连接
  const createAnalysisWebSocket = (url: string) => {
    const ws = createWebSocketConnection(url)

    // 设置WebSocket事件处理
    ws.onopen = (event) => {
      console.log('WebSocket连接已打开:', event)
    }

    ws.onmessage = (event) => {
      console.log('收到WebSocket消息:', event.data)

      // 处理消息
      try {
        const data = JSON.parse(event.data)

        // 根据消息类型处理
        if (data.type === 'audioAnalysis') {
          // 处理音频分析结果
          console.log('音频分析结果:', data)
        } else if (data.type === 'videoAnalysis') {
          // 处理视频分析结果
          console.log('视频分析结果:', data)
        } else if (data.type === 'textAnalysis') {
          // 处理文本分析结果
          console.log('文本分析结果:', data)
        } else if (data.type === 'emotionUpdate') {
          // 处理情绪更新
          console.log('情绪更新:', data)
        } else if (data.type === 'suggestionUpdate') {
          // 处理建议更新
          console.log('建议更新:', data)
        } else if (data.type === 'metricsUpdate') {
          // 处理指标更新
          console.log('指标更新:', data)
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event)
    }

    ws.onerror = (event) => {
      console.error('WebSocket连接错误:', event)
    }

    return ws
  }

  return {
    isDemoMode,
    createAnalysisWebSocket,
  }
}

/**
 * 面试房间页面中使用演示数据的完整示例
 *
 * 在面试房间页面的script部分，可以这样使用：
 *
 * ```typescript
 * import { integrateInterviewRoomDemoData, cleanupInterviewRoomDemoData } from '@/utils/demoData/interview/integration'
 * import { interviewRoomHelper } from '@/utils/demoData/interview/roomHelper'
 *
 * // 在onMounted中集成演示数据
 * onMounted(() => {
 *   // 集成演示数据
 *   const { isDemoMode } = integrateInterviewRoomDemoData()
 *
 *   if (isDemoMode) {
 *     console.log('面试房间页面使用演示模式')
 *     // 可以在这里添加演示模式下的特殊处理
 *   }
 * })
 *
 * // 在onUnmounted中清理演示数据
 * onUnmounted(() => {
 *   // 清理演示数据
 *   cleanupInterviewRoomDemoData()
 * })
 *
 * // 创建WebSocket连接
 * const createWebSocketConnection = (url: string) => {
 *   return interviewRoomHelper.createWebSocket(url)
 * }
 *
 * // 在需要创建WebSocket连接的地方使用
 * const ws = createWebSocketConnection('wss://example.com/ws')
 * ```
 */

export default {
  setupInterviewRoomWithDemoData,
  useInterviewRoomDemoData,
}
