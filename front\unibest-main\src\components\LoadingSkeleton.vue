<template>
  <view class="skeleton-container">
    <!-- 统计卡片骨架 -->
    <view class="skeleton-stats-card">
      <view class="skeleton-header">
        <view class="skeleton-text-block">
          <view class="skeleton-line title"></view>
          <view class="skeleton-line subtitle"></view>
        </view>
        <view class="skeleton-circle"></view>
      </view>
      <view class="skeleton-stats-grid">
        <view class="skeleton-stat-item" v-for="i in 4" :key="i">
          <view class="skeleton-line value"></view>
          <view class="skeleton-line label"></view>
        </view>
      </view>
    </view>

    <!-- 快速操作骨架 -->
    <view class="skeleton-section">
      <view class="skeleton-section-title">
        <view class="skeleton-line title"></view>
      </view>
      <view class="skeleton-actions-grid">
        <view class="skeleton-action-item" v-for="i in 4" :key="i">
          <view class="skeleton-icon"></view>
          <view class="skeleton-content">
            <view class="skeleton-line title"></view>
            <view class="skeleton-line subtitle"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐任务骨架 -->
    <view class="skeleton-section">
      <view class="skeleton-section-title">
        <view class="skeleton-line title"></view>
      </view>
      <view class="skeleton-task-item" v-for="i in 2" :key="i">
        <view class="skeleton-priority-bar"></view>
        <view class="skeleton-content">
          <view class="skeleton-line title"></view>
          <view class="skeleton-line subtitle"></view>
        </view>
      </view>
    </view>

    <!-- 建议列表骨架 -->
    <view class="skeleton-section">
      <view class="skeleton-section-title">
        <view class="skeleton-line title"></view>
      </view>
      <view class="skeleton-suggestion-item" v-for="i in 3" :key="i">
        <view class="skeleton-icon"></view>
        <view class="skeleton-content">
          <view class="skeleton-line title"></view>
          <view class="skeleton-line subtitle"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @description 加载骨架屏组件
 * 在数据加载时提供更好的用户体验
 */
</script>

<style scoped lang="scss">
.skeleton-container {
  padding: 20rpx;
}

// 骨架屏基础样式
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-line {
  @extend .skeleton-base;
  border-radius: 4rpx;
  
  &.title {
    height: 32rpx;
    margin-bottom: 8rpx;
  }
  
  &.subtitle {
    height: 24rpx;
    width: 70%;
  }
  
  &.value {
    height: 28rpx;
    width: 60rpx;
    margin-bottom: 8rpx;
  }
  
  &.label {
    height: 20rpx;
    width: 80rpx;
  }
}

.skeleton-circle {
  @extend .skeleton-base;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.skeleton-icon {
  @extend .skeleton-base;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.skeleton-priority-bar {
  @extend .skeleton-base;
  width: 6rpx;
  height: 60rpx;
  border-radius: 3rpx;
}

// 统计卡片骨架
.skeleton-stats-card {
  padding: 40rpx;
  margin-bottom: 30rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

  .skeleton-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 40rpx;

    .skeleton-text-block {
      flex: 1;
    }
  }

  .skeleton-stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30rpx;

    .skeleton-stat-item {
      text-align: center;
    }
  }
}

// 区块骨架
.skeleton-section {
  margin-bottom: 30rpx;

  .skeleton-section-title {
    margin-bottom: 24rpx;

    .skeleton-line.title {
      width: 120rpx;
    }
  }
}

// 操作网格骨架
.skeleton-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;

  .skeleton-action-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

    .skeleton-icon {
      margin-right: 20rpx;
    }

    .skeleton-content {
      flex: 1;
    }
  }
}

// 任务项骨架
.skeleton-task-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

  .skeleton-priority-bar {
    margin-right: 20rpx;
  }

  .skeleton-content {
    flex: 1;
  }
}

// 建议项骨架
.skeleton-suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

  .skeleton-icon {
    margin-right: 20rpx;
  }

  .skeleton-content {
    flex: 1;
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .skeleton-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .skeleton-stats-card .skeleton-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }
}
</style> 