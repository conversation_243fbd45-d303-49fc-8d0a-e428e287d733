<script setup lang="ts">
import { ref, onMounted } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
import {
  updateUserProfile,
  resetUserProfile,
  uploadAvatar,
  type UserProfile,
  type UserProfileDto,
  // @ts-ignore
} from '@/service/profile'
// @ts-ignore
import { useUserStore } from '@/store/user'
// 个人信息页
// 用于展示和编辑用户的个人资料

// 用户资料
const profile = ref<Partial<UserProfile>>({
  avatar: '',
  name: '',
  userName: '',
  gender: '男',
  major: '',
  grade: '大一',
  school: '',
  introduction: '',
})

// 加载状态
const loading = ref(false)

// 选择性别
const genderOptions = ['男', '女']

// 选择年级
const gradeOptions = ['大一', '大二', '大三', '大四', '研究生']

// 表单验证状态
const formErrors = ref({
  name: '',
})

// 通知提示
const notificationText = ref('')
const notificationType = ref<'success' | 'error' | 'info'>('info')
const notificationVisible = ref(false)
let notificationTimer: any = null

/**
 * @description 显示通知栏
 * @param message 通知内容
 * @param type 通知类型（info/success/error）
 * @param duration 显示时长（毫秒）
 */
const showNotification = (
  message: string,
  type: 'success' | 'error' | 'info' = 'info',
  duration = 3000,
) => {
  notificationText.value = message
  notificationType.value = type
  notificationVisible.value = true

  if (notificationTimer) clearTimeout(notificationTimer)
  notificationTimer = setTimeout(() => {
    notificationVisible.value = false
  }, duration)
}

/**
 * @description 返回上一页
 */
const goBack = () => {
  uni.navigateBack()
}

/**
 * @description 加载用户个人信息
 */
const loadUserProfile = async () => {
  // 从store中获取用户信息
  const user = useUserStore()
  profile.value = user.userInfo
}

// 页面加载时获取用户资料
onMounted(() => {
  loadUserProfile()
})

/**
 * @description 头像上传处理
 */
const onChooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        handleAvatarUpload(res.tempFilePaths[0])
      }
    },
    fail: () => {
      showNotification('头像选择失败，请重试', 'error')
    },
  })
}

/**
 * @description 处理头像上传
 * @param filePath 文件路径
 */
const handleAvatarUpload = async (filePath: string) => {
  try {
    loading.value = true
    showNotification('正在上传头像...', 'info')

    const result = await uploadAvatar(filePath)

    // 更新头像URL
    profile.value.avatar = result.url

    // 同步更新到后端
    await updateUserProfile({
      params: {
        avatar: result.url,
      },
    })

    showNotification('头像更换成功', 'success')
  } catch (error) {
    console.error('头像上传失败:', error)
    showNotification('头像上传失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}

/**
 * @description 验证表单数据
 * @returns 验证是否通过
 */
const validateForm = (): boolean => {
  let isValid = true

  // 重置错误信息
  formErrors.value = {
    name: '',
  }

  // 验证姓名
  if (!profile.value.name.trim()) {
    formErrors.value.name = '请输入昵称'
    isValid = false
  }

  return isValid
}

/**
 * @description 重置表单数据
 */
const onReset = () => {
  uni.showModal({
    title: '确认重置',
    content: '确定要重置所有信息吗？此操作不可撤销。',
    success: async (res) => {
      if (res.confirm) {
        try {
          loading.value = true
          await resetUserProfile()

          // 重新加载数据
          await loadUserProfile()

          showNotification('信息已重置', 'success')
        } catch (error) {
          console.error('重置失败:', error)
          showNotification('重置失败，请重试', 'error')
        } finally {
          loading.value = false
        }
      }
    },
  })
}

/**
 * @description 保存用户资料
 */
const onSave = async () => {
  if (!validateForm()) {
    showNotification('请检查表单信息', 'error')
    return
  }

  try {
    loading.value = true

    const profileData: UserProfileDto = {
      name: profile.value.name,
      gender: profile.value.gender,
      school: profile.value.school,
      major: profile.value.major,
      grade: profile.value.grade,
      introduction: profile.value.introduction,
    }

    await updateUserProfile({ params: profileData })

    showNotification('保存成功', 'success')

    // 延迟返回，让用户看到保存成功的提示
    setTimeout(() => {
      goBack()
    }, 1500)
  } catch (error) {
    console.error('保存失败:', error)
    showNotification('保存失败，请重试', 'error')
  } finally {
    loading.value = false
  }
}
</script>
<template>
  <view class="profile-page">
    <HeadBar title="个人信息" :show-back="true" :show-right-button="false" />

    <!-- 加载遮罩 -->
    <view v-if="loading" class="loading-mask">
      <view class="loading-spinner">
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 通知提示 -->
    <view
      v-if="notificationVisible"
      class="notification"
      :class="{
        success: notificationType === 'success',
        error: notificationType === 'error',
      }"
    >
      <text v-if="notificationType === 'success'" class="i-fa-solid-check-circle mr-2"></text>
      <text v-else-if="notificationType === 'error'" class="i-fa-solid-times-circle mr-2"></text>
      <text v-else class="i-fa-solid-info-circle mr-2"></text>
      <text>{{ notificationText }}</text>
    </view>

    <!-- 个人信息表单 -->
    <view class="content-area">
      <!-- 装饰圆圈 -->
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>

      <view class="profile-card">
        <!-- 头像 -->
        <view class="avatar-section">
          <view class="avatar-box" @click="onChooseAvatar">
            <image
              :src="profile.avatar || 'https://via.placeholder.com/150x150/00C9A7/FFFFFF?text=头像'"
              class="avatar"
              mode="aspectFill"
            />
            <view class="avatar-edit">
              <text class="i-fa-solid-camera" style="margin-right: 8rpx; font-size: 20rpx"></text>
            </view>
          </view>
        </view>

        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>

          <view class="form-item">
            <text class="form-label required">昵称</text>
            <view class="input-container">
              <input
                v-model="profile.name"
                class="form-input"
                :class="{ 'input-error': formErrors.name }"
                placeholder="请输入昵称"
              />
              <text class="i-fa-solid-user input-icon"></text>
            </view>
            <text v-if="formErrors.name" class="error-text">{{ formErrors.name }}</text>
          </view>

          <view class="form-item">
            <text class="form-label">性别</text>
            <view class="input-container">
              <picker
                mode="selector"
                :range="genderOptions"
                @change="(e) => (profile.gender = genderOptions[e.detail.value])"
              >
                <view class="form-input picker-value">{{ profile.gender }}</view>
              </picker>
              <text class="i-fa-solid-venus-mars input-icon"></text>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">学校</text>
            <view class="input-container">
              <input v-model="profile.school" class="form-input" placeholder="请输入学校名称" />
              <text class="i-fa-solid-university input-icon"></text>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">专业</text>
            <view class="input-container">
              <input v-model="profile.major" class="form-input" placeholder="请输入专业" />
              <text class="i-fa-solid-book input-icon"></text>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">年级</text>
            <view class="input-container">
              <picker
                mode="selector"
                :range="gradeOptions"
                @change="(e) => (profile.grade = gradeOptions[e.detail.value])"
              >
                <view class="form-input picker-value">{{ profile.grade }}</view>
              </picker>
              <text class="i-fa-solid-graduation-cap input-icon"></text>
            </view>
          </view>
        </view>

        <!-- 个人简介 -->
        <view class="form-section">
          <view class="section-title">个人简介</view>

          <view class="form-item textarea-item">
            <view class="textarea-container">
              <textarea
                v-model="profile.introduction"
                class="form-textarea"
                placeholder="请简要介绍一下自己（选填）"
                maxlength="200"
              />
              <view class="word-count">{{ profile.introduction.length }}/200</view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="reset-btn" @click="onReset" :disabled="loading">
            <text>重置</text>
          </button>
          <button class="save-btn" @click="onSave" :disabled="loading">
            <text>保存</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
<style scoped lang="scss">
/* 全局变量 */
$primary-color: #00c9a7;
$primary-dark: #00b39a;
$error-color: #ff5252;
$text-dark: #333333;
$text-light: #666666;
$text-lighter: #999999;
$border-color: #e0e0e0;
$bg-color: #f7f9fc;

/* 基础样式 */
.profile-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: $bg-color;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 200rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.loading-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: $text-dark;
}

/* 通知提示 */
.notification {
  position: fixed;
  top: 20rpx;
  /* #ifdef MP */
  top: calc(var(--status-bar-height, 0) + 20rpx);
  left: 50%;
  z-index: 2000;
  max-width: 600rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  text-align: center;
  background: $primary-color;
  border-radius: 16rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.3);
  transform: translateX(-50%);
  animation: slideDown 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  /* #endif */
}

.notification.success {
  background: linear-gradient(135deg, $primary-color, $primary-dark);
}

.notification.error {
  background: linear-gradient(135deg, #ff6b6b, $error-color);
}

/* 内容区域 */
.content-area {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  min-height: calc(100vh - 88rpx);
  /* #ifdef MP */
  min-height: calc(100vh - 88rpx - var(--status-bar-height, 0));
  /* #endif */
  padding: 40rpx 0;
  overflow: hidden;
}

/* 装饰圆圈 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.4;
}

.decoration-circle-1 {
  top: 160rpx;
  right: 60rpx;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, #e0f7f2 0%, #b5ece2 100%);
  animation: floating 5s ease-in-out infinite;
}

.decoration-circle-2 {
  bottom: 300rpx;
  left: 60rpx;
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #d1f5ee 0%, #a5e0d5 100%);
  animation: floating 4s ease-in-out infinite reverse;
}

/* 表单卡片 */
.profile-card {
  position: relative;
  z-index: 10;
  margin: 24rpx 40rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.08);
}

/* 头像部分 */
.avatar-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 0;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f0ff 100%);
}

.avatar-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s;

  &:active {
    transform: scale(0.95);
  }
}

.avatar {
  width: 150rpx;
  height: 150rpx;
  border: 3rpx solid #ffffff;
  border-radius: 50%;
}

.avatar-edit {
  position: absolute;
  left: 50%;
  bottom: -36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 24rpx;
  font-size: 36rpx;
  color: #ffffff;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.2);
  transform: translateX(-50%);
}

/* 表单部分 */
.form-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-of-type {
    border-bottom: none;
  }
}

.section-title {
  position: relative;
  padding-left: 8rpx;
  margin-bottom: 32rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: $text-dark;

  &::after {
    position: absolute;
    bottom: -8rpx;
    left: 0;
    width: 64rpx;
    height: 6rpx;
    content: '';
    background: $primary-color;
    border-radius: 4rpx;
  }
}

.form-item {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: $text-dark;

  &.required::after {
    margin-left: 4rpx;
    color: $error-color;
    content: '*';
  }
}

.input-container {
  position: relative;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 96rpx;
  padding: 0 96rpx 0 30rpx;
  font-size: 32rpx;
  color: $text-dark;
  background: #ffffff;
  border: 2rpx solid $border-color;
  border-radius: 16rpx;
  transition: all 0.3s;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 2rpx rgba(0, 201, 167, 0.1);
  }
}

.input-error {
  border-color: $error-color;
}

.input-icon {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 32rpx;
  color: $text-lighter;
  transform: translateY(-50%);
}

.error-text {
  display: block;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: $error-color;
}

.picker-value {
  line-height: 96rpx;
}

.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}

.textarea-container {
  position: relative;
  width: 100%;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 200rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: $text-dark;
  background: #ffffff;
  border: 2rpx solid $border-color;
  border-radius: 16rpx;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 2rpx rgba(0, 201, 167, 0.1);
  }
}

.word-count {
  position: absolute;
  right: 20rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: $text-lighter;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 40rpx 30rpx;
}

.reset-btn,
.save-btn {
  display: flex;
  flex: 1;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  /* #ifdef H5 */
  cursor: pointer;
  border: none;
  border-radius: 48rpx;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &::after {
    border: none;
  }
  &:hover {
    opacity: 0.9;
  }
  /* #endif */

  &:active {
    transform: scale(0.98) translateY(-4rpx);
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
  }
}

.reset-btn {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  box-shadow: 0 16rpx 40rpx rgba(108, 117, 125, 0.2);

  &:active {
    box-shadow: 0 8rpx 16rpx rgba(108, 117, 125, 0.15);
  }
}

.save-btn {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  box-shadow: 0 16rpx 40rpx rgba(0, 201, 167, 0.2);

  &:active {
    box-shadow: 0 8rpx 16rpx rgba(0, 201, 167, 0.15);
  }
}

/* 动画 */
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(5deg);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}
</style>
