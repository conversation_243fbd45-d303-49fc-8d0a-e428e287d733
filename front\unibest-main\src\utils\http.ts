/**
 * @description 网络请求工具函数
 * <AUTHOR>
 */

import { CustomRequestOptions } from '@/interceptors/request'
import { getEnvBaseUrl } from '@/utils'
import { platform } from '@/utils/platform'
import qs from 'qs'

// SSE请求选项接口
export interface SSERequestOptions {
  url: string
  query?: Record<string, any>
  onMessage: (data: string) => void
  onError: (error: string) => void
  onComplete: () => void
  /** 自定义事件监听器 */
  eventListeners?: {
    [eventType: string]: (event: MessageEvent) => void
  }
}

// SSE连接控制器接口
export interface SSEController {
  close: () => void
}

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',

      // 响应成功
      success(res) {
        console.log(res)
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 2.1 提取核心数据 res.data
          if (res.data.code === 200) {
            resolve(res.data as IResData<T>)
          } else {
            // uni.showToast({
            //   icon: 'none',
            //   title: (res.data as IResData<T>).message || '请求错误',
            // })
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            // uni.showToast({
            //   icon: 'none',
            //   title: (res.data as IResData<T>).message || '请求错误',
            // })
            reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpGet = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
  })
}

/**
 * PUT 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpPut = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'PUT',
    header,
  })
}

/**
 * DELETE 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpDelete = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'DELETE',
    header,
  })
}

/**
 * 构建完整的URL（复用拦截器逻辑）
 * @param url 原始URL
 * @param query 查询参数
 * @returns 完整的URL
 */
const buildFullUrl = (url: string, query?: Record<string, any>): string => {
  let fullUrl = url

  // 处理查询参数
  if (query) {
    const queryStr = qs.stringify(query)
    if (fullUrl.includes('?')) {
      fullUrl += `&${queryStr}`
    } else {
      fullUrl += `?${queryStr}`
    }
  }

  // 处理基础URL（复用拦截器逻辑）
  if (!fullUrl.startsWith('http')) {
    const baseUrl = getEnvBaseUrl()

    if (JSON.parse(__VITE_APP_PROXY__)) {
      fullUrl = import.meta.env.VITE_APP_PROXY_PREFIX + fullUrl
    } else {
      fullUrl = baseUrl + fullUrl
    }

    // #ifndef H5
    fullUrl = baseUrl + fullUrl
  }

  return fullUrl
}

/**
 * 获取请求头（复用拦截器逻辑）
 * @param customHeaders 自定义请求头
 * @returns 完整的请求头
 */
const getRequestHeaders = (customHeaders?: Record<string, any>): Record<string, any> => {
  const headers: Record<string, any> = {
    platform,
    ...customHeaders,
  }

  // 添加token
  const tokenName = uni.getStorageSync('tokenName')
  const tokenValue = uni.getStorageSync('token')
  if (tokenValue !== undefined && tokenValue !== '') {
    headers[tokenName] = tokenName + ' ' + tokenValue
  }

  return headers
}

/**
 * SSE 请求
 * @param options SSE请求选项
 * @returns SSE连接控制器
 */
export const httpSSE = (options: SSERequestOptions): SSEController => {
  try {
    // 构建完整URL
    const fullUrl = buildFullUrl(options.url, options.query)

    // 创建EventSource连接
    const eventSource = new EventSource(fullUrl, {
      withCredentials: true,
    })
    let isCompleted = false

    // 监听token事件
    eventSource.addEventListener('token', (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.token) {
          options.onMessage(data.token)
        }
      } catch (e) {
        console.warn('解析token事件失败:', e)
        options.onMessage(event.data)
      }
    })

    // 监听完成事件
    eventSource.addEventListener('complete', (event) => {
      isCompleted = true
      options.onComplete()
      eventSource.close()
    })

    // 监听错误事件
    eventSource.addEventListener('error', (event) => {
      try {
        const messageEvent = event as MessageEvent
        if (messageEvent.data) {
          const data = JSON.parse(messageEvent.data)
          options.onError(data.data || data.message || '服务器错误')
        } else {
          options.onError('服务器错误')
        }
      } catch (e) {
        options.onError('服务器错误')
      }
      eventSource.close()
    })

    // 监听会话事件
    eventSource.addEventListener('session', (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('会话创建:', data.sessionId)
      } catch (e) {
        console.warn('解析会话事件失败:', e)
      }
    })

    // 监听开始事件
    eventSource.addEventListener('start', (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('流式响应开始:', data.messageId)
      } catch (e) {
        console.warn('解析开始事件失败:', e)
      }
    })

    // 自定义事件监听器
    if (options.eventListeners) {
      Object.entries(options.eventListeners).forEach(([eventType, listener]) => {
        eventSource.addEventListener(eventType, listener)
      })
    }

    // 通用消息处理器
    eventSource.onmessage = (event) => {
      console.log('收到通用SSE消息:', event.data)
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'token' && data.token) {
          options.onMessage(data.token)
        } else if (data.type === 'complete' || data.type === 'done') {
          isCompleted = true
          options.onComplete()
          eventSource.close()
        } else if (data.type === 'error') {
          options.onError(data.data || data.message || '服务器错误')
          eventSource.close()
        } else if (data.type === 'message' || data.type === 'content') {
          options.onMessage(data.data || data.content || data.message || event.data)
        }
      } catch (e) {
        options.onMessage(event.data)
      }
    }

    // 错误处理器
    eventSource.onerror = (event) => {
      console.error('SSE连接错误:', event)

      if (isCompleted) {
        console.log('SSE连接已正常完成，忽略关闭事件')
        return
      }

      if (eventSource.readyState === EventSource.CLOSED) {
        console.log('SSE连接已关闭')
        if (!isCompleted) {
          options.onError('连接意外断开')
        }
      } else if (eventSource.readyState === EventSource.CONNECTING) {
        console.log('SSE连接重连中...')
      } else {
        options.onError('连接错误')
      }

      if (!isCompleted) {
        eventSource.close()
      }
    }

    return {
      close: () => {
        isCompleted = true
        eventSource.close()
      },
    }
  } catch (error) {
    console.error('创建SSE连接失败:', error)
  }
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete
http.sse = httpSSE
