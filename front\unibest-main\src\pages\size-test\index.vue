<template>
  <div class="size-test-page">
    <div class="header">
      <h1>尺寸测试页面</h1>
      <p>项目规定尺寸：430 × 932</p>
    </div>
    
    <div class="content">
      <div class="size-info">
        <h2>当前窗口信息</h2>
        <div class="info-item">
          <span>窗口宽度：</span>
          <span>{{ windowWidth }}px</span>
        </div>
        <div class="info-item">
          <span>窗口高度：</span>
          <span>{{ windowHeight }}px</span>
        </div>
        <div class="info-item">
          <span>设备像素比：</span>
          <span>{{ pixelRatio }}</span>
        </div>
        <div class="info-item">
          <span>屏幕宽度：</span>
          <span>{{ screenWidth }}px</span>
        </div>
        <div class="info-item">
          <span>屏幕高度：</span>
          <span>{{ screenHeight }}px</span>
        </div>
      </div>
      
      <div class="size-demo">
        <h2>尺寸演示</h2>
        <div class="demo-box">
          <p>这是一个演示容器</p>
          <p>宽度应该不超过 430px</p>
          <p>高度应该不超过 932px</p>
        </div>
      </div>
      
      <div class="test-buttons">
        <button @click="refreshInfo" class="btn">刷新信息</button>
        <button @click="toggleDebug" class="btn">{{ debugMode ? '关闭' : '开启' }}调试模式</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const windowWidth = ref(0)
const windowHeight = ref(0)
const pixelRatio = ref(0)
const screenWidth = ref(0)
const screenHeight = ref(0)
const debugMode = ref(false)

// 获取系统信息
const getSystemInfo = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    windowWidth.value = systemInfo.windowWidth
    windowHeight.value = systemInfo.windowHeight
    pixelRatio.value = systemInfo.pixelRatio
    screenWidth.value = systemInfo.screenWidth
    screenHeight.value = systemInfo.screenHeight
    
    console.log('系统信息:', systemInfo)
  } catch (error) {
    console.error('获取系统信息失败:', error)
  }
}

// 刷新信息
const refreshInfo = () => {
  getSystemInfo()
  uni.showToast({
    title: '信息已刷新',
    icon: 'success'
  })
}

// 切换调试模式
const toggleDebug = () => {
  debugMode.value = !debugMode.value
  const appElement = document.querySelector('#app')
  if (appElement) {
    if (debugMode.value) {
      appElement.classList.add('debug-layout')
    } else {
      appElement.classList.remove('debug-layout')
    }
  }
}

// 页面加载时获取信息
onMounted(() => {
  getSystemInfo()
})
</script>

<style scoped lang="scss">
.size-test-page {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    font-size: 16px;
  }
}

.content {
  max-width: 100%;
}

.size-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    span:first-child {
      color: #666;
    }
    
    span:last-child {
      color: #333;
      font-weight: 500;
    }
  }
}

.size-demo {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
  }
  
  .demo-box {
    background: #f8f9fa;
    border: 2px dashed #007aff;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    
    p {
      margin: 5px 0;
      color: #333;
    }
  }
}

.test-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
  
  .btn {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background: #0056cc;
    }
    
    &:active {
      background: #004499;
    }
  }
}
</style>
