/**
 * 分享功能工具函数
 * 提供性能优化的分享相关工具方法
 * <AUTHOR>
 */

// 防抖函数，避免重复点击
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

// 节流函数，限制执行频率
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 图片预加载，提升分享体验
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`))
    img.src = src
  })
}

// 批量预加载图片
export async function preloadImages(srcs: string[]): Promise<void> {
  const promises = srcs.map(src => preloadImage(src).catch(() => {})) // 忽略单个图片加载失败
  await Promise.all(promises)
}

// 检测设备能力
export interface DeviceCapabilities {
  hasClipboard: boolean
  hasShare: boolean
  hasVibration: boolean
  platform: 'h5' | 'mp-weixin' | 'mp-qq' | 'app' | 'unknown'
}

export function getDeviceCapabilities(): DeviceCapabilities {
  const capabilities: DeviceCapabilities = {
    hasClipboard: false,
    hasShare: false,
    hasVibration: false,
    platform: 'unknown'
  }

  // #ifdef H5
  capabilities.platform = 'h5'
  capabilities.hasClipboard = !!(navigator.clipboard || document.execCommand)
  capabilities.hasShare = !!(navigator.share)
  capabilities.hasVibration = !!(navigator.vibrate)
  // #endif

  // #ifdef MP-WEIXIN
  capabilities.platform = 'mp-weixin'
  capabilities.hasClipboard = true
  capabilities.hasShare = true
  capabilities.hasVibration = true
  // #endif

  // #ifdef MP-QQ
  capabilities.platform = 'mp-qq'
  capabilities.hasClipboard = true
  capabilities.hasShare = true
  capabilities.hasVibration = true
  // #endif

  // #ifdef APP-PLUS
  capabilities.platform = 'app'
  capabilities.hasClipboard = true
  capabilities.hasShare = true
  capabilities.hasVibration = true
  // #endif

  return capabilities
}

// 生成分享链接
export interface ShareLinkOptions {
  baseUrl: string
  path: string
  params?: Record<string, string | number>
  utm?: {
    source?: string
    medium?: string
    campaign?: string
    content?: string
  }
}

export function generateShareLink(options: ShareLinkOptions): string {
  const { baseUrl, path, params = {}, utm = {} } = options
  
  const url = new URL(path, baseUrl)
  
  // 添加基础参数
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.set(key, String(value))
  })
  
  // 添加UTM参数
  if (utm.source) url.searchParams.set('utm_source', utm.source)
  if (utm.medium) url.searchParams.set('utm_medium', utm.medium)
  if (utm.campaign) url.searchParams.set('utm_campaign', utm.campaign)
  if (utm.content) url.searchParams.set('utm_content', utm.content)
  
  return url.toString()
}

// 分享数据验证
export interface ShareDataValidation {
  isValid: boolean
  errors: string[]
}

export function validateShareData(data: {
  title?: string
  content?: string
  url?: string
  imageUrl?: string
}): ShareDataValidation {
  const errors: string[] = []
  
  if (!data.title || data.title.trim().length === 0) {
    errors.push('分享标题不能为空')
  } else if (data.title.length > 100) {
    errors.push('分享标题不能超过100个字符')
  }
  
  if (!data.content || data.content.trim().length === 0) {
    errors.push('分享内容不能为空')
  } else if (data.content.length > 500) {
    errors.push('分享内容不能超过500个字符')
  }
  
  if (data.url && !isValidUrl(data.url)) {
    errors.push('分享链接格式不正确')
  }
  
  if (data.imageUrl && !isValidUrl(data.imageUrl)) {
    errors.push('分享图片链接格式不正确')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// URL验证
function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 分享统计
export interface ShareStats {
  platform: string
  timestamp: number
  success: boolean
  error?: string
  duration?: number
}

class ShareAnalytics {
  private stats: ShareStats[] = []
  private maxStats = 100 // 最多保存100条记录

  record(stat: ShareStats): void {
    this.stats.push(stat)
    
    // 保持数组大小
    if (this.stats.length > this.maxStats) {
      this.stats.shift()
    }
    
    // 可以在这里上报到分析服务
    this.reportToAnalytics(stat)
  }

  getStats(): ShareStats[] {
    return [...this.stats]
  }

  getSuccessRate(platform?: string): number {
    const filteredStats = platform 
      ? this.stats.filter(s => s.platform === platform)
      : this.stats
    
    if (filteredStats.length === 0) return 0
    
    const successCount = filteredStats.filter(s => s.success).length
    return successCount / filteredStats.length
  }

  private reportToAnalytics(stat: ShareStats): void {
    // 这里可以集成第三方分析服务
    console.log('Share analytics:', stat)
  }
}

export const shareAnalytics = new ShareAnalytics()

// 内存优化：对象池
class ObjectPool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn: (obj: T) => void

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 5) {
    this.createFn = createFn
    this.resetFn = resetFn
    
    // 预创建对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(createFn())
    }
  }

  get(): T {
    return this.pool.pop() || this.createFn()
  }

  release(obj: T): void {
    this.resetFn(obj)
    this.pool.push(obj)
  }
}

// 分享事件对象池
interface ShareEvent {
  platform: string
  timestamp: number
  data: any
}

export const shareEventPool = new ObjectPool<ShareEvent>(
  () => ({ platform: '', timestamp: 0, data: null }),
  (obj) => {
    obj.platform = ''
    obj.timestamp = 0
    obj.data = null
  }
)

// 错误处理
export class ShareError extends Error {
  constructor(
    message: string,
    public platform: string,
    public code?: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'ShareError'
  }
}

// 重试机制
export async function retryShare<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (i < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
      }
    }
  }
  
  throw lastError!
}

// 性能监控
export function measurePerformance<T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T {
  return ((...args: Parameters<T>) => {
    const start = performance.now()
    const result = fn(...args)
    
    if (result instanceof Promise) {
      return result.finally(() => {
        const end = performance.now()
        console.log(`${name} took ${end - start} milliseconds`)
      })
    } else {
      const end = performance.now()
      console.log(`${name} took ${end - start} milliseconds`)
      return result
    }
  }) as T
}
