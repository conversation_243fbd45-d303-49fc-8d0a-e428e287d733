/**
 * @description API拦截器
 * 拦截API请求，在请求失败时提供演示数据
 */

import { demoDataManager } from './DemoDataManager'

// API拦截器接口
export interface ApiInterceptor {
  // 请求拦截器
  requestInterceptor(config: any): any

  // 响应拦截器
  responseInterceptor(response: any): any

  // 错误拦截器
  errorInterceptor(error: any): any
}

// API拦截器实现
class ApiInterceptorImpl implements ApiInterceptor {
  private static instance: ApiInterceptorImpl

  // 私有构造函数，确保单例模式
  private constructor() {
    console.log('[ApiInterceptor] 初始化完成')
  }

  // 获取单例实例
  public static getInstance(): ApiInterceptorImpl {
    if (!ApiInterceptorImpl.instance) {
      ApiInterceptorImpl.instance = new ApiInterceptorImpl()
    }
    return ApiInterceptorImpl.instance
  }

  // 请求拦截器
  public requestInterceptor(config: any): any {
    // 在请求发送前不做特殊处理，只记录请求信息
    console.log(`[ApiInterceptor] 请求: ${config.url}`)
    return config
  }

  // 响应拦截器
  public responseInterceptor(response: any): any {
    // 响应成功，直接返回
    return response
  }

  // 错误拦截器
  public errorInterceptor(error: any): any {
    // 如果演示数据管理器未启用，直接抛出错误
    if (!demoDataManager.isEnabled()) {
      return Promise.reject(error)
    }

    // 获取请求配置
    const config = error.config || {}
    const url = config.url || ''
    const method = config.method || 'GET'
    const params = method.toUpperCase() === 'GET' ? config.params : config.data

    console.log(`[ApiInterceptor] 请求失败: ${url}，尝试使用演示数据`)

    try {
      // 尝试获取演示数据
      const demoData = demoDataManager.getDemoData(url, params)

      // 构造成功的响应对象
      const response = {
        data: demoData,
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
        request: {},
        // 添加标记，表示这是演示数据
        isDemo: true,
      }

      console.log(`[ApiInterceptor] 使用演示数据成功: ${url}`)

      // 返回成功的响应
      return Promise.resolve(response)
    } catch (demoError) {
      console.error(`[ApiInterceptor] 获取演示数据失败: ${url}`, demoError)

      // 如果获取演示数据失败，继续抛出原始错误
      return Promise.reject(error)
    }
  }
}

// 导出单例实例
export const apiInterceptor = ApiInterceptorImpl.getInstance()

// 集成到Axios
export const setupAxiosInterceptors = (axios: any) => {
  // 添加请求拦截器
  axios.interceptors.request.use(
    (config: any) => apiInterceptor.requestInterceptor(config),
    (error: any) => Promise.reject(error),
  )

  // 添加响应拦截器
  axios.interceptors.response.use(
    (response: any) => apiInterceptor.responseInterceptor(response),
    (error: any) => apiInterceptor.errorInterceptor(error),
  )

  console.log('[ApiInterceptor] Axios拦截器设置完成')
}

// 集成到uni-app请求
export const setupUniRequestInterceptors = () => {
  // 保存原始的uni.request方法
  const originalRequest = uni.request

  // 重写uni.request方法
  uni.request = function (options: any) {
    // 应用请求拦截器
    const interceptedOptions = apiInterceptor.requestInterceptor(options)

    // 调用原始的请求方法
    return originalRequest({
      ...interceptedOptions,
      success: (res: any) => {
        // 应用响应拦截器
        const interceptedResponse = apiInterceptor.responseInterceptor(res)

        // 调用原始的成功回调
        if (interceptedOptions.success) {
          interceptedOptions.success(interceptedResponse)
        }
      },
      fail: (err: any) => {
        // 应用错误拦截器
        apiInterceptor
          .errorInterceptor(err)
          .then((res: any) => {
            // 如果错误拦截器返回了演示数据，调用成功回调
            if (interceptedOptions.success) {
              interceptedOptions.success(res)
            }
          })
          .catch((error: any) => {
            // 如果错误拦截器没有处理错误，调用原始的失败回调
            if (interceptedOptions.fail) {
              interceptedOptions.fail(error)
            }
          })
      },
    })
  }

  console.log('[ApiInterceptor] uni-app请求拦截器设置完成')
}

export default apiInterceptor
