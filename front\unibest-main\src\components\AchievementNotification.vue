<script setup lang="ts">
import { ref, computed } from 'vue'

interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

interface Props {
  visible: boolean
  achievement: Achievement | null
}

interface Emits {
  (e: 'close'): void
  (e: 'view-all'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 稀有度配置
const rarityConfig = {
  common: {
    bgColor: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
    borderColor: '#4CAF50',
    glowColor: 'rgba(76, 175, 80, 0.3)',
    title: '普通成就',
  },
  rare: {
    bgColor: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
    borderColor: '#2196F3',
    glowColor: 'rgba(33, 150, 243, 0.3)',
    title: '稀有成就',
  },
  epic: {
    bgColor: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)',
    borderColor: '#9C27B0',
    glowColor: 'rgba(156, 39, 176, 0.3)',
    title: '史诗成就',
  },
  legendary: {
    bgColor: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
    borderColor: '#FF9800',
    glowColor: 'rgba(255, 152, 0, 0.3)',
    title: '传奇成就',
  },
}

// 当前成就的稀有度配置
const currentRarity = computed(() => {
  if (!props.achievement) return rarityConfig.common
  return rarityConfig[props.achievement.rarity] || rarityConfig.common
})

/**
 * @description 关闭通知
 */
function closeNotification() {
  emit('close')
}

/**
 * @description 查看所有成就
 */
function viewAllAchievements() {
  emit('view-all')
  emit('close')
}
</script>

<template>
  <view v-if="visible && achievement" class="achievement-overlay">
    <view class="achievement-container" :style="{ borderColor: currentRarity.borderColor }">
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="closeNotification">
        <text class="i-mdi-close"></text>
      </view>

      <!-- 背景光效 -->
      <view
        class="achievement-glow"
        :style="{ boxShadow: `0 0 60rpx ${currentRarity.glowColor}` }"
      ></view>

      <!-- 成就内容 -->
      <view class="achievement-content">
        <!-- 标题 -->
        <text class="achievement-title">🎉 成就解锁！</text>

        <!-- 稀有度标签 -->
        <view class="rarity-badge" :style="{ background: currentRarity.bgColor }">
          <text>{{ currentRarity.title }}</text>
        </view>

        <!-- 成就图标 -->
        <view class="achievement-icon" :style="{ background: currentRarity.bgColor }">
          <text :class="achievement.icon"></text>
        </view>

        <!-- 成就信息 -->
        <view class="achievement-info">
          <text class="achievement-name">{{ achievement.name }}</text>
          <text class="achievement-desc">{{ achievement.description }}</text>
        </view>

        <!-- 动画效果 -->
        <view class="celebration-effects">
          <view v-for="i in 6" :key="i" class="confetti" :class="`confetti-${i}`"></view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="achievement-actions">
        <button class="btn-primary" @click="viewAllAchievements">查看所有成就</button>
        <button class="btn-secondary" @click="closeNotification">继续学习</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.achievement-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(15rpx);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  animation: overlayFadeIn 0.3s ease-out;
}

.achievement-container {
  position: relative;
  width: 100%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 32rpx;
  border: 4rpx solid;
  padding: 48rpx;
  overflow: hidden;
  animation: achievementPop 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.close-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 24rpx;
  z-index: 10;
  transition: all 0.3s ease;

  &:active {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(0.95);
  }
}

.achievement-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: glowPulse 2s infinite ease-in-out;
}

.achievement-content {
  position: relative;
  text-align: center;
  z-index: 5;
}

.achievement-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 16rpx;
  animation: titleBounce 0.8s ease-out 0.2s both;
}

.rarity-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 32rpx;
  text-transform: uppercase;
  letter-spacing: 1rpx;
  animation: badgeSlideIn 0.6s ease-out 0.4s both;
}

.achievement-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  animation: iconSpin 0.8s ease-out 0.6s both;
}

.achievement-info {
  margin-bottom: 40rpx;
  animation: infoFadeIn 0.6s ease-out 0.8s both;
}

.achievement-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
}

.achievement-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.celebration-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.confetti {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background: #ffd700;
  animation: confettiFall 2s linear infinite;

  &.confetti-1 {
    left: 10%;
    background: #ff6b6b;
    animation-delay: 0s;
  }

  &.confetti-2 {
    left: 25%;
    background: #4ecdc4;
    animation-delay: 0.2s;
  }

  &.confetti-3 {
    left: 40%;
    background: #45b7d1;
    animation-delay: 0.4s;
  }

  &.confetti-4 {
    left: 60%;
    background: #96ceb4;
    animation-delay: 0.6s;
  }

  &.confetti-5 {
    left: 75%;
    background: #ffeaa7;
    animation-delay: 0.8s;
  }

  &.confetti-6 {
    left: 90%;
    background: #dda0dd;
    animation-delay: 1s;
  }
}

.achievement-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  animation: actionsFadeIn 0.6s ease-out 1s both;
}

.btn-primary,
.btn-secondary {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.2);
  }
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;

  &:active {
    background: #e0e0e0;
    transform: translateY(1rpx);
  }
}

// 动画定义
@keyframes overlayFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes achievementPop {
  0% {
    opacity: 0;
    transform: scale(0.3) rotateY(180deg);
  }
  50% {
    transform: scale(1.05) rotateY(90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
  }
}

@keyframes glowPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes titleBounce {
  0% {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  60% {
    transform: translateY(10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes badgeSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-50rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes iconSpin {
  0% {
    opacity: 0;
    transform: rotateY(-180deg) scale(0);
  }
  50% {
    transform: rotateY(-90deg) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: rotateY(0deg) scale(1);
  }
}

@keyframes infoFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes actionsFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes confettiFall {
  0% {
    opacity: 1;
    transform: translateY(-100rpx) rotateZ(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(400rpx) rotateZ(360deg);
  }
}
</style>
