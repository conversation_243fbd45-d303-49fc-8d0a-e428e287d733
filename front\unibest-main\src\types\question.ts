/**
 * 题目模块类型定义
 */

/**
 * 题目类型枚举
 */
export type QuestionType = 'single' | 'multiple' | 'judge' | 'essay' | 'code'

/**
 * 题目难度枚举
 */
export type QuestionDifficulty = '简单' | '中等' | '困难'

/**
 * 题目选项
 */
export interface QuestionOption {
  id: string
  label: string
  content: string
}

/**
 * 基础题目信息
 */
export interface Question {
  id: string
  title: string
  description?: string
  difficulty: QuestionDifficulty
  practiceCount: number
  correctRate: number
  commentCount: number
  category: string
  tags?: string[]
  acceptanceRate?: number
  isCompleted?: boolean
}

/**
 * 题目详情
 */
export interface QuestionDetail {
  id: string
  title: string
  content: string
  answer: string
  analysis: string
  difficulty: QuestionDifficulty
  tags: string[]
  practiceCount: number
  correctRate: number
  commentCount: number
  isBookmarked: boolean
  questionType: QuestionType
  options: QuestionOption[]
  correctAnswer: string
  createTime: string
  updateTime: string
  bankId?: string
  bankTitle?: string
  category?: string
}

/**
 * 评论用户信息
 */
export interface CommentUser {
  id: string
  nickname: string
  avatar: string
}

/**
 * 评论信息
 */
export interface Comment {
  id: number | string
  author: string
  avatar: string
  content: string
  time: string
  likes: number
  liked?: boolean
  user?: CommentUser
  createTime?: string
  updateTime?: string
  questionId?: string
  parentId?: string | null
  replies?: Comment[]
  replyTo?: string // 回复目标用户
}

/**
 * 创建评论参数
 */
export interface CommentCreate {
  questionId: string
  content: string
  parentId?: string
}

/**
 * 评论查询参数
 */
export interface CommentQueryParams {
  questionId: string
  page?: number
  pageSize?: number
  orderBy?: 'createTime' | 'likes'
  orderDirection?: 'asc' | 'desc'
}

/**
 * 评论列表响应
 */
export interface CommentListResponse {
  list: Comment[]
  total: number
  page: number
  pageSize: number
  hasMore?: boolean
}

/**
 * 点赞评论响应
 */
export interface LikeCommentResponse {
  isLiked: boolean
  likeCount: number
  message?: string
}

/**
 * 题目收藏参数
 */
export interface QuestionBookmarkParams {
  questionId: string
  isBookmarked: boolean
}

/**
 * 题目练习记录
 */
export interface QuestionPracticeRecord {
  id: string
  questionId: string
  userId: string
  isCorrect: boolean
  userAnswer: string
  timeSpent: number
  createTime: string
}

/**
 * 题目统计信息
 */
export interface QuestionStats {
  totalPractice: number
  correctCount: number
  correctRate: number
  avgTimeSpent: number
  difficultyDistribution: Record<QuestionDifficulty, number>
}
