/**
 * @description AI聊天演示数据模块
 * 提供AI聊天相关的演示数据和生成器
 */

import { demoDataManager } from '../DemoDataManager'
import agentData, { createDemoAgents, getDemoAgentById } from './agentData'
import sessionData, {
  createDemoChatMessage,
  createDemoChatSession,
  createDemoChatSessionList,
} from './sessionData'
import replyGenerator, { ReplyGenerator } from './replyGenerator'
import type { AIAgent } from './agentData'
import type { ChatMessage, ChatSession } from './sessionData'
import type { ReplyGeneratorOptions } from './replyGenerator'

// API路径常量
const API_PATHS = {
  GET_ENABLED_AGENTS: 'chat/getEnabledAgents',
  GET_AGENT_BY_ID: 'chat/getAgentById',
  GET_SESSION_LIST: 'chat/getSessionList',
  GET_SESSION_BY_ID: 'chat/getSessionById',
  CREATE_SESSION: 'chat/createSession',
  DELETE_SESSION: 'chat/deleteSession',
  SEND_MESSAGE: 'chat/sendMessage',
  STREAM_MESSAGE: 'chat/streamMessage',
}

// 注册AI聊天演示数据
export const registerChatDemoData = () => {
  // 注册获取启用的AI助手列表演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_ENABLED_AGENTS, () => {
    const agents = createDemoAgents()
    return {
      code: 200,
      message: 'success',
      data: agents,
    }
  })

  // 注册根据ID获取AI助手演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_AGENT_BY_ID, (params: { id: string }) => {
    const agent = getDemoAgentById(params.id)

    if (agent) {
      return {
        code: 200,
        message: 'success',
        data: agent,
      }
    } else {
      return {
        code: 404,
        message: 'Agent not found',
        data: null,
      }
    }
  })

  // 注册获取会话列表演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.GET_SESSION_LIST, () => {
    const sessions = createDemoChatSessionList()
    return {
      code: 200,
      message: 'success',
      data: sessions,
    }
  })

  // 注册根据ID获取会话演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.GET_SESSION_BY_ID,
    (params: { id: string }) => {
      const sessions = createDemoChatSessionList()
      const session = sessions.find((s) => s.id === params.id)

      if (session) {
        return {
          code: 200,
          message: 'success',
          data: session,
        }
      } else {
        return {
          code: 404,
          message: 'Session not found',
          data: null,
        }
      }
    },
  )

  // 注册创建会话演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.CREATE_SESSION,
    (params: { agentId: string; title?: string }) => {
      const session = createDemoChatSession(params.agentId, {
        id: `session-${Date.now()}`,
        title: params.title,
      })

      return {
        code: 200,
        message: 'success',
        data: session,
      }
    },
  )

  // 注册删除会话演示数据生成器
  demoDataManager.registerDemoDataGenerator(API_PATHS.DELETE_SESSION, (params: { id: string }) => {
    return {
      code: 200,
      message: 'success',
      data: true,
    }
  })

  // 注册发送消息演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.SEND_MESSAGE,
    (params: {
      sessionId: string
      agentId: string
      content: string
      attachments?: Array<{
        type: 'image' | 'file'
        url: string
        name: string
      }>
    }) => {
      // 创建用户消息
      const userMessage = createDemoChatMessage('user', params.content, {
        attachments: params.attachments,
      })

      // 生成AI回复
      const aiMessage = replyGenerator.generateReply(params.content, params.agentId, {
        includeThinking: true,
      })

      return {
        code: 200,
        message: 'success',
        data: {
          userMessage,
          aiMessage,
        },
      }
    },
  )

  // 注册流式消息演示数据生成器
  demoDataManager.registerDemoDataGenerator(
    API_PATHS.STREAM_MESSAGE,
    (params: {
      sessionId: string
      agentId: string
      content: string
      attachments?: Array<{
        type: 'image' | 'file'
        url: string
        name: string
      }>
    }) => {
      // 创建用户消息
      const userMessage = createDemoChatMessage('user', params.content, {
        attachments: params.attachments,
      })

      // 生成AI回复（流式）
      const aiMessage = createDemoChatMessage('assistant', '', {
        isStreaming: true,
        think: replyGenerator.generateReply(params.content, params.agentId, {
          includeThinking: true,
        }).think,
      })

      return {
        code: 200,
        message: 'success',
        data: {
          userMessage,
          aiMessage,
        },
      }
    },
  )

  console.log('[ChatDemoData] 注册AI聊天演示数据完成')
}

// 导出所有模块
export {
  agentData,
  sessionData,
  replyGenerator,
  createDemoAgents,
  getDemoAgentById,
  createDemoChatMessage,
  createDemoChatSession,
  createDemoChatSessionList,
  ReplyGenerator,
  API_PATHS,
}

// 导出类型
export type {
  AIAgent,
  ChatMessage,
  ChatSession,
  ReplyGeneratorOptions,
}

// 默认导出
export default {
  registerChatDemoData,
  API_PATHS,
}
