<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

/**
 * @description 404错误页面
 * 当用户访问不存在的页面或参数错误时显示
 */

// 页面状态
const isAnimated = ref(false)
const errorCode = ref('404')
const errorMessage = ref('抱歉，页面走丢了')
const second = ref(5) // 倒计时秒数
const countdownTimer = ref<number | null>(null) // 倒计时定时器
const suggestions = ref([
  '检查网络连接是否正常',
  '确认访问链接是否正确',
  '尝试刷新页面或重新进入',
  '返回首页重新开始学习',
])

/**
 * @description 开始倒计时
 */
const startCountdown = (): void => {
  countdownTimer.value = setInterval(() => {
    second.value--
    if (second.value <= 0) {
      stopCountdown()
      goHome()
    }
  }, 1000)
}

/**
 * @description 停止倒计时
 */
const stopCountdown = (): void => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

/**
 * @description 返回首页
 */
const goHome = (): void => {
  stopCountdown() // 停止倒计时
  uni.switchTab({
    url: '/pages/index/index',
    fail: () => {
      // 如果switchTab失败，尝试navigateTo
      uni.navigateTo({
        url: '/pages/index/index',
        fail: () => {
          // 最后尝试redirectTo
          uni.redirectTo({
            url: '/pages/index/index',
          })
        },
      })
    },
  })
}

/**
 * @description 返回上一页
 */
const goBack = (): void => {
  stopCountdown() // 停止倒计时
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    // 如果没有上一页，跳转到首页
    goHome()
  }
}

/**
 * @description 重新加载页面
 */
const reload = (): void => {
  stopCountdown() // 停止倒计时
  // H5端支持刷新
  // #ifdef H5
  window.location.reload()
  // #endif

  // 小程序端返回上一页重新进入
  // #ifndef H5
  uni.showToast({
    title: '正在重新加载...',
    icon: 'loading',
    duration: 1000,
  })
  setTimeout(() => {
    goBack()
  }, 1000)
  // #endif
}

/**
 * @description 联系客服
 */
const contactSupport = (): void => {
  stopCountdown() // 停止倒计时
  uni.showModal({
    title: '联系客服',
    content:
      '遇到问题？我们来帮助您！\n\n客服邮箱：<EMAIL>\n工作时间：9:00-18:00',
    showCancel: true,
    cancelText: '取消',
    confirmText: '好的',
    success: (res) => {
      if (res.confirm) {
        // 可以在这里添加跳转到客服页面的逻辑
        uni.showToast({
          title: '感谢您的反馈',
          icon: 'success',
          duration: 2000,
        })
      }
      // 无论用户选择什么，都重新开始倒计时
      setTimeout(() => {
        startCountdown()
      }, 100)
    },
  })
}

/**
 * @description 页面初始化动画
 */
const initAnimation = (): void => {
  setTimeout(() => {
    isAnimated.value = true
    // 动画完成后开始倒计时
    startCountdown()
  }, 300)
}

/**
 * @description 获取动画类名
 */
const getAnimatedClass = (): string => {
  return isAnimated.value ? 'animated' : ''
}

// 页面加载时获取参数
onLoad((options) => {
  // 可以根据传入的参数自定义错误信息
  if (options.code) {
    errorCode.value = options.code
  }
  if (options.message) {
    errorMessage.value = decodeURIComponent(options.message)
  }
})

// 页面挂载时启动动画
onMounted(() => {
  initAnimation()
})

// 页面销毁时清理逻辑
onUnmounted(() => {
  stopCountdown()
})
</script>

<template>
  <view class="error-page">
    <HeadBar title="页面未找到" :show-back="true" :custom-back="true" @back="goBack" />

    <!-- 主要内容区域 -->
    <view class="main-content">
      <view class="error-container" :class="getAnimatedClass()">
        <!-- 404图标和文字 -->
        <view class="error-icon-section">
          <view class="error-icon">
            <text class="icon-404">404</text>
          </view>

          <view class="error-text">
            <text class="error-title">{{ errorMessage }}</text>
            <text class="error-desc">您访问的页面可能已经删除、更名或暂时不可用</text>
          </view>
        </view>

        <!-- 建议列表 -->
        <view class="suggestions-section">
          <text class="suggestions-title">您可以尝试：</text>
          <view class="suggestions-list">
            <view
              v-for="(suggestion, index) in suggestions"
              :key="index"
              class="suggestion-item"
              :style="{ animationDelay: `${index * 0.1 + 0.5}s` }"
            >
              <view class="suggestion-icon">
                <text class="i-fa-solid-lightbulb"></text>
              </view>
              <text class="suggestion-text">{{ suggestion }}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="actions-section">
          <view class="action-buttons">
            <view class="action-btn secondary" @click="goBack">
              <text class="btn-icon i-fa-solid-arrow-left"></text>
              <text class="btn-text">返回上页</text>
            </view>

            <view class="action-btn primary" @click="goHome">
              <text class="btn-icon i-fa-solid-home"></text>
              <text class="btn-text">回到首页({{ second }}s)</text>
            </view>
          </view>

          <view class="extra-actions">
            <view class="extra-btn" @click="reload">
              <text class="extra-icon i-fa-solid-redo"></text>
              <text class="extra-text">重新加载</text>
            </view>

            <view class="extra-btn" @click="contactSupport">
              <text class="extra-icon i-fa-solid-headset"></text>
              <text class="extra-text">联系客服</text>
            </view>
          </view>
        </view>

        <!-- 底部提示 -->
        <view class="footer-tip">
          <text class="tip-text">如果问题持续存在，请联系我们的客服团队</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-content {
  padding: 40rpx 30rpx;
  min-height: calc(100vh - 88rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  width: 100%;
  max-width: 600rpx;
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.animated {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 404图标区域 */
.error-icon-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.error-icon {
  position: relative;
  margin-bottom: 40rpx;
}

.icon-404 {
  font-size: 120rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
  line-height: 1.2;
}

.error-text {
  text-align: center;
}

.error-title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 15rpx;
}

.error-desc {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.5;
}

/* 建议区域 */
.suggestions-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.suggestions-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 30rpx;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  opacity: 0;
  transform: translateX(-30rpx);
  animation: slideInLeft 0.6s ease-out forwards;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.suggestion-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;

  .i-fa-solid-lightbulb {
    font-size: 20rpx;
    color: white;
  }
}

.suggestion-text {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.4;
}

/* 操作按钮区域 */
.actions-section {
  margin-bottom: 40rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-btn.secondary {
  background: white;
  border: 2rpx solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 30rpx;
  font-weight: bold;
}

.extra-actions {
  display: flex;
  justify-content: center;
  gap: 60rpx;
}

.extra-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
  }
}

.extra-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 201, 167, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #00c9a7;
  margin-bottom: 8rpx;
}

.extra-text {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 底部提示 */
.footer-tip {
  text-align: center;
  padding: 30rpx 0;
}

.tip-text {
  font-size: 24rpx;
  color: #95a5a6;
  line-height: 1.4;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .icon-404 {
    font-size: 100rpx;
  }

  .floating-book,
  .floating-question,
  .floating-light {
    width: 50rpx;
    height: 50rpx;

    text {
      font-size: 24rpx;
    }
  }

  .action-buttons {
    flex-direction: column;
  }

  .extra-actions {
    gap: 40rpx;
  }
}
</style>
