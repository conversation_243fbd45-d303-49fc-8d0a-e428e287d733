/**
 * @description 性能监控工具
 * 用于监控页面性能指标，帮助识别性能瓶颈
 */

/**
 * @description 性能指标接口
 */
export interface PerformanceMetrics {
  /** 页面加载时间 */
  loadTime: number
  /** 首屏渲染时间 */
  firstPaint: number
  /** DOM元素数量 */
  domCount: number
  /** 内存使用量 */
  memoryUsage: number
  /** FPS（帧率） */
  fps: number
  /** 滚动性能 */
  scrollPerformance: number
}

/**
 * @description 性能监控类
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private startTime: number = 0
  private metrics: Partial<PerformanceMetrics> = {}
  private fpsCounter: number = 0
  private lastFrameTime: number = 0
  private frameCount: number = 0

  private constructor() {
    this.startTime = Date.now()
  }

  /**
   * @description 获取单例实例
   */
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * @description 开始性能监控
   */
  startMonitoring(): void {
    this.startTime = Date.now()
    this.startFPSMonitoring()
  }

  /**
   * @description 标记页面加载完成
   */
  markPageLoaded(): void {
    this.metrics.loadTime = Date.now() - this.startTime
    console.log(`页面加载耗时: ${this.metrics.loadTime}ms`)
  }

  /**
   * @description 标记首屏渲染完成
   */
  markFirstPaint(): void {
    this.metrics.firstPaint = Date.now() - this.startTime
    console.log(`首屏渲染耗时: ${this.metrics.firstPaint}ms`)
  }

  /**
   * @description 计算DOM元素数量
   */
  measureDOMCount(): number {
    // #ifdef H5
    if (typeof document !== 'undefined') {
      this.metrics.domCount = document.querySelectorAll('*').length
      return this.metrics.domCount
    }
    // #endif

    // 小程序环境下的估算
    this.metrics.domCount = 0
    return 0
  }

  /**
   * @description 获取内存使用情况
   */
  getMemoryUsage(): number {
    // #ifdef H5
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory
      this.metrics.memoryUsage = memory.usedJSHeapSize
      return this.metrics.memoryUsage
    }
    // #endif

    return 0
  }

  /**
   * @description 开始FPS监控
   */
  private startFPSMonitoring(): void {
    let frames = 0
    let startTime = Date.now()

    const countFrame = () => {
      frames++
      const currentTime = Date.now()

      if (currentTime - startTime >= 1000) {
        this.metrics.fps = Math.round((frames * 1000) / (currentTime - startTime))
        frames = 0
        startTime = currentTime
      }

      // #ifdef H5
      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(countFrame)
      } else {
        setTimeout(countFrame, 16) // 约60fps
      }
      // #endif

      // #ifndef H5
      setTimeout(countFrame, 16)
      // #endif
    }

    countFrame()
  }

  /**
   * @description 测量滚动性能
   * @param callback 滚动回调函数
   * @returns 滚动处理函数
   */
  measureScrollPerformance(callback?: () => void): () => void {
    let scrollCount = 0
    let startTime = Date.now()

    const scrollHandler = () => {
      scrollCount++
      const currentTime = Date.now()

      if (currentTime - startTime >= 1000) {
        this.metrics.scrollPerformance = scrollCount
        scrollCount = 0
        startTime = currentTime
      }

      callback?.()
    }

    return scrollHandler
  }

  /**
   * @description 检查性能是否符合标准
   */
  checkPerformanceThresholds(): {
    passed: boolean
    warnings: string[]
  } {
    const warnings: string[] = []

    // 检查页面加载时间
    if (this.metrics.loadTime && this.metrics.loadTime > 800) {
      warnings.push(`页面加载时间过长: ${this.metrics.loadTime}ms (建议 < 800ms)`)
    }

    // 检查首屏渲染时间
    if (this.metrics.firstPaint && this.metrics.firstPaint > 500) {
      warnings.push(`首屏渲染时间过长: ${this.metrics.firstPaint}ms (建议 < 500ms)`)
    }

    // 检查DOM元素数量
    if (this.metrics.domCount && this.metrics.domCount > 200) {
      warnings.push(`DOM元素过多: ${this.metrics.domCount} (建议 < 200)`)
    }

    // 检查内存使用
    if (this.metrics.memoryUsage && this.metrics.memoryUsage > 150 * 1024 * 1024) {
      warnings.push(
        `内存使用过高: ${Math.round(this.metrics.memoryUsage / 1024 / 1024)}MB (建议 < 150MB)`,
      )
    }

    // 检查FPS
    if (this.metrics.fps && this.metrics.fps < 50) {
      warnings.push(`帧率过低: ${this.metrics.fps}fps (建议 > 50fps)`)
    }

    return {
      passed: warnings.length === 0,
      warnings,
    }
  }

  /**
   * @description 获取当前性能指标
   */
  getMetrics(): PerformanceMetrics {
    return {
      loadTime: this.metrics.loadTime || 0,
      firstPaint: this.metrics.firstPaint || 0,
      domCount: this.measureDOMCount(),
      memoryUsage: this.getMemoryUsage(),
      fps: this.metrics.fps || 0,
      scrollPerformance: this.metrics.scrollPerformance || 0,
    }
  }

  /**
   * @description 生成性能报告
   */
  generateReport(): string {
    const metrics = this.getMetrics()
    const check = this.checkPerformanceThresholds()

    let report = '=== 性能监控报告 ===\n'
    report += `页面加载时间: ${metrics.loadTime}ms\n`
    report += `首屏渲染时间: ${metrics.firstPaint}ms\n`
    report += `DOM元素数量: ${metrics.domCount}\n`
    report += `内存使用: ${Math.round(metrics.memoryUsage / 1024 / 1024)}MB\n`
    report += `帧率: ${metrics.fps}fps\n`
    report += `滚动性能: ${metrics.scrollPerformance}\n`

    if (!check.passed) {
      report += '\n=== 性能警告 ===\n'
      check.warnings.forEach((warning) => {
        report += `⚠️ ${warning}\n`
      })
    } else {
      report += '\n✅ 所有性能指标均符合标准'
    }

    return report
  }

  /**
   * @description 上报性能数据
   * @param data 额外的上报数据
   */
  reportMetrics(data?: Record<string, any>): void {
    const metrics = this.getMetrics()
    const reportData = {
      ...metrics,
      timestamp: Date.now(),
      userAgent: navigator?.userAgent || 'unknown',
      ...data,
    }

    // 这里可以发送到分析服务
    console.log('性能数据上报:', reportData)

    // 在开发环境下显示性能报告
    // #ifdef H5
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      console.log(this.generateReport())
    }
    // #endif
  }
}

/**
 * @description 创建防抖函数，用于性能优化
 * @param func 要防抖的函数
 * @param delay 延迟时间
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timer: number | null = null

  return (...args: Parameters<T>) => {
    if (timer) {
      clearTimeout(timer)
    }

    timer = setTimeout(() => {
      func(...args)
      timer = null
    }, delay)
  }
}

/**
 * @description 创建节流函数，用于性能优化
 * @param func 要节流的函数
 * @param delay 节流间隔
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let lastTime = 0

  return (...args: Parameters<T>) => {
    const currentTime = Date.now()

    if (currentTime - lastTime >= delay) {
      func(...args)
      lastTime = currentTime
    }
  }
}

/**
 * @description 测量函数执行时间
 * @param func 要测量的函数
 * @param name 函数名称
 */
export function measureExecutionTime<T extends (...args: any[]) => any>(
  func: T,
  name: string = 'function',
): T {
  return ((...args: Parameters<T>) => {
    const startTime = Date.now()
    const result = func(...args)
    const endTime = Date.now()

    console.log(`${name} 执行时间: ${endTime - startTime}ms`)
    return result
  }) as T
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()
