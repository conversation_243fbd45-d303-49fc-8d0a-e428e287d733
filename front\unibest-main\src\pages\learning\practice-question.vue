<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import HeadBar from '@/components/HeadBar.vue'

/**
 * 页面功能：题目练习页面
 * 支持：代码编辑、语音回答、视频录制、文字输入等多种练习方式
 * 集成AI智能辅导功能
 */

// 页面参数
const questionId = ref('')
const questionData = ref<any>({})
const bankId = ref('')
const questionIndex = ref(0)
const totalQuestions = ref(0)

// 练习模式
const practiceMode = ref<'code' | 'voice' | 'video' | 'text'>('code')
const availableModes = ref<string[]>([])

// 代码编辑相关
const codeContent = ref('')
const codeLanguage = ref('python')
const codeRunning = ref(false)
const codeResult = ref('')

// 语音相关
const isRecording = ref(false)
const recordingTime = ref(0)
const audioPath = ref('')
const recordingTimer = ref<any>(null)

// 视频相关
const isVideoRecording = ref(false)
const videoPath = ref('')
const videoRecordingTime = ref(0)
const videoTimer = ref<any>(null)

// 文字输入相关
const textAnswer = ref('')

// AI辅导相关
const showAIHelper = ref(false)
const aiSuggestion = ref('')
const isAIThinking = ref(false)

// 题目导航相关
const isNavigating = ref(false)

/**
 * 方法名: loadQuestionData
 * 功能: 加载题目数据并确定可用的练习模式
 * 参数: 无
 * 返回值: 无
 */
const loadQuestionData = async () => {
  try {
    // 模拟获取题目数据
    const mockData = {
      id: questionId.value,
      title: '快速排序算法实现',
      type: 'algorithm', // algorithm, theory, behavioral, technical
      content:
        '请实现一个快速排序算法，要求：\n1. 支持任意数组排序\n2. 时间复杂度为O(n log n)\n3. 原地排序',
      difficulty: '中等',
      tags: ['排序算法', '分治法'],
    }

    questionData.value = mockData

    // 根据题目类型确定可用的练习模式
    switch (mockData.type) {
      case 'algorithm':
        availableModes.value = ['code', 'text']
        practiceMode.value = 'code'
        break
      case 'behavioral':
        availableModes.value = ['voice', 'video', 'text']
        practiceMode.value = 'voice'
        break
      case 'technical':
        availableModes.value = ['voice', 'text', 'code']
        practiceMode.value = 'text'
        break
      default:
        availableModes.value = ['text']
        practiceMode.value = 'text'
    }
  } catch (error) {
    console.error('加载题目数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

/**
 * 方法名: switchMode
 * 功能: 切换练习模式
 * 参数: mode - 练习模式
 * 返回值: 无
 */
const switchMode = (mode: typeof practiceMode.value) => {
  practiceMode.value = mode
}

/**
 * 方法名: runCode
 * 功能: 运行代码
 * 参数: 无
 * 返回值: 无
 */
const runCode = async () => {
  if (!codeContent.value.trim()) {
    uni.showToast({
      title: '请输入代码',
      icon: 'none',
    })
    return
  }

  codeRunning.value = true
  codeResult.value = ''

  try {
    // 模拟代码运行
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 模拟运行结果
    codeResult.value = `运行成功！

输入：[3, 1, 4, 1, 5, 9, 2, 6]
输出：[1, 1, 2, 3, 4, 5, 6, 9]

时间复杂度：O(n log n)
空间复杂度：O(log n)

测试通过：5/5
- 基本功能测试 ✓
- 边界条件测试 ✓
- 大数据量测试 ✓
- 重复元素测试 ✓
- 空数组测试 ✓`
  } catch (error) {
    codeResult.value = '运行出错：' + error
  } finally {
    codeRunning.value = false
  }
}

/**
 * 方法名: startRecording
 * 功能: 开始录音
 * 参数: 无
 * 返回值: 无
 */
const startRecording = () => {
  isRecording.value = true
  recordingTime.value = 0

  // 开始计时
  recordingTimer.value = setInterval(() => {
    recordingTime.value++
    if (recordingTime.value >= 300) {
      // 最长5分钟
      stopRecording()
    }
  }, 1000)

  // 调用录音API
  const recorderManager = uni.getRecorderManager()
  recorderManager.start({
    duration: 300000, // 5分钟
    sampleRate: 44100,
    numberOfChannels: 2,
    encodeBitRate: 192000,
    format: 'mp3',
  })

  recorderManager.onStop((res) => {
    audioPath.value = res.tempFilePath
    uni.showToast({
      title: '录音完成',
      icon: 'success',
    })
  })
}

/**
 * 方法名: stopRecording
 * 功能: 停止录音
 * 参数: 无
 * 返回值: 无
 */
const stopRecording = () => {
  isRecording.value = false
  clearInterval(recordingTimer.value)

  const recorderManager = uni.getRecorderManager()
  recorderManager.stop()
}

/**
 * 方法名: playAudio
 * 功能: 播放录音
 * 参数: 无
 * 返回值: 无
 */
const playAudio = () => {
  if (!audioPath.value) return

  const innerAudioContext = uni.createInnerAudioContext()
  innerAudioContext.src = audioPath.value
  innerAudioContext.play()
}

/**
 * 方法名: startVideoRecording
 * 功能: 开始视频录制
 * 参数: 无
 * 返回值: 无
 */
const startVideoRecording = () => {
  // #ifdef MP-WEIXIN
  const cameraContext = uni.createCameraContext()

  isVideoRecording.value = true
  videoRecordingTime.value = 0

  videoTimer.value = setInterval(() => {
    videoRecordingTime.value++
    if (videoRecordingTime.value >= 180) {
      // 最长3分钟
      stopVideoRecording()
    }
  }, 1000)

  cameraContext.startRecord({
    success: () => {
      console.log('开始录制')
    },
    timeoutCallback: (res) => {
      videoPath.value = res.tempVideoPath
      isVideoRecording.value = false
      clearInterval(videoTimer.value)
    },
  })
  // #endif

  // #ifdef H5
  uni.showToast({
    title: 'H5暂不支持视频录制',
    icon: 'none',
  })
  // #endif
}

/**
 * 方法名: stopVideoRecording
 * 功能: 停止视频录制
 * 参数: 无
 * 返回值: 无
 */
const stopVideoRecording = () => {
  // #ifdef MP-WEIXIN
  const cameraContext = uni.createCameraContext()

  cameraContext.stopRecord({
    success: (res) => {
      videoPath.value = res.tempVideoPath
      isVideoRecording.value = false
      clearInterval(videoTimer.value)

      uni.showToast({
        title: '录制完成',
        icon: 'success',
      })
    },
  })
  // #endif
}

/**
 * 方法名: toggleAIHelper
 * 功能: 切换AI助手显示
 * 参数: 无
 * 返回值: 无
 */
const toggleAIHelper = () => {
  showAIHelper.value = !showAIHelper.value
  if (showAIHelper.value && !aiSuggestion.value) {
    getAISuggestion()
  }
}

/**
 * 方法名: getAISuggestion
 * 功能: 获取AI建议
 * 参数: 无
 * 返回值: 无
 */
const getAISuggestion = async () => {
  isAIThinking.value = true

  try {
    // 模拟AI分析
    await new Promise((resolve) => setTimeout(resolve, 2000))

    aiSuggestion.value = `基于您的练习情况，我有以下建议：

1. **解题思路**：
   - 快速排序的核心是选择基准元素（pivot）
   - 将数组分为小于基准和大于基准的两部分
   - 递归地对两部分进行排序

2. **代码优化建议**：
   - 考虑使用三数取中法选择基准元素
   - 对于小数组可以切换到插入排序
   - 注意处理边界条件

3. **常见错误**：
   - 忘记处理空数组或单元素数组
   - 递归终止条件设置错误
   - 分区过程中的索引越界

需要我详细解释某个部分吗？`
  } catch (error) {
    aiSuggestion.value = '获取AI建议失败，请稍后重试'
  } finally {
    isAIThinking.value = false
  }
}

/**
 * 方法名: submitAnswer
 * 功能: 提交答案
 * 参数: 无
 * 返回值: 无
 */
const submitAnswer = async () => {
  let answer = ''

  switch (practiceMode.value) {
    case 'code':
      if (!codeContent.value.trim()) {
        uni.showToast({
          title: '请输入代码',
          icon: 'none',
        })
        return
      }
      answer = codeContent.value
      break
    case 'voice':
      if (!audioPath.value) {
        uni.showToast({
          title: '请先录音',
          icon: 'none',
        })
        return
      }
      answer = audioPath.value
      break
    case 'video':
      if (!videoPath.value) {
        uni.showToast({
          title: '请先录制视频',
          icon: 'none',
        })
        return
      }
      answer = videoPath.value
      break
    case 'text':
      if (!textAnswer.value.trim()) {
        uni.showToast({
          title: '请输入答案',
          icon: 'none',
        })
        return
      }
      answer = textAnswer.value
      break
  }

  try {
    // 模拟提交
    uni.showLoading({ title: '提交中...' })
    await new Promise((resolve) => setTimeout(resolve, 2000))
    uni.hideLoading()

    // 跳转到练习结果页
    uni.navigateTo({
      url: `/pages/learning/practice-result?questionId=${questionId.value}&mode=${practiceMode.value}&answer=${encodeURIComponent(answer)}`,
    })
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '提交失败',
      icon: 'none',
    })
  }
}

/**
 * 方法名: formatTime
 * 功能: 格式化时间显示
 * 参数: seconds - 秒数
 * 返回值: 格式化的时间字符串
 */
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 方法名: codeEditorHeight
 * 功能: 计算代码编辑器高度
 * 参数: 无
 * 返回值: 高度值
 */
const codeEditorHeight = computed(() => {
  const lines = codeContent.value.split('\n').length
  return Math.max(400, Math.min(lines * 30 + 100, 800))
})

/**
 * 方法名: onLoad
 * 功能: 页面加载初始化
 * 参数: options - 页面参数
 * 返回值: 无
 */
onLoad((options: any) => {
  if (options && options.id) {
    questionId.value = options.id
    loadQuestionData()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="practice-page">
    <HeadBar title="题目练习" :show-back="true" />

    <scroll-view scroll-y class="main-scroll" :style="{ height: 'calc(100vh - 88rpx)' }">
      <!-- 题目信息卡片 -->
      <view class="question-card">
        <view class="question-header">
          <text class="question-title">{{ questionData.title }}</text>
          <view class="question-tags">
            <view class="difficulty-tag" :class="`difficulty-${questionData.difficulty}`">
              {{ questionData.difficulty }}
            </view>
            <view v-for="tag in questionData.tags" :key="tag" class="tag-item">
              {{ tag }}
            </view>
          </view>
        </view>
        <text class="question-content">{{ questionData.content }}</text>
      </view>

      <!-- 练习模式切换 -->
      <view class="mode-switcher" v-if="availableModes.length > 1">
        <view
          v-for="mode in availableModes"
          :key="mode"
          class="mode-item"
          :class="{ active: practiceMode === mode }"
          @click="switchMode(mode)"
        >
          <view
            class="mode-icon"
            :class="{
              'i-mdi-code-braces': mode === 'code',
              'i-mdi-microphone': mode === 'voice',
              'i-mdi-video': mode === 'video',
              'i-mdi-text': mode === 'text',
            }"
          ></view>
          <text class="mode-text">
            {{
              mode === 'code'
                ? '代码'
                : mode === 'voice'
                  ? '语音'
                  : mode === 'video'
                    ? '视频'
                    : '文字'
            }}
          </text>
        </view>
      </view>

      <!-- 练习区域 -->
      <view class="practice-area">
        <!-- 代码编辑模式 -->
        <view v-if="practiceMode === 'code'" class="code-mode">
          <view class="code-toolbar">
            <view class="language-selector">
              <text class="label">语言：</text>
              <picker
                mode="selector"
                :range="['Python', 'JavaScript', 'Java', 'C++']"
                @change="
                  (e) => (codeLanguage = ['python', 'javascript', 'java', 'cpp'][e.detail.value])
                "
              >
                <view class="picker-value">
                  {{
                    codeLanguage === 'python'
                      ? 'Python'
                      : codeLanguage === 'javascript'
                        ? 'JavaScript'
                        : codeLanguage === 'java'
                          ? 'Java'
                          : 'C++'
                  }}
                  <view class="arrow-icon i-mdi-chevron-down"></view>
                </view>
              </picker>
            </view>
            <button class="run-btn" @click="runCode" :loading="codeRunning">
              <view class="btn-icon i-mdi-play" v-if="!codeRunning"></view>
              运行代码
            </button>
          </view>

          <textarea
            v-model="codeContent"
            class="code-editor"
            :style="{ height: codeEditorHeight + 'rpx' }"
            placeholder="在这里输入你的代码..."
            :maxlength="-1"
          />

          <view v-if="codeResult" class="code-result">
            <view class="result-header">
              <view class="result-icon i-mdi-console"></view>
              <text class="result-title">运行结果</text>
            </view>
            <text class="result-content">{{ codeResult }}</text>
          </view>
        </view>

        <!-- 语音模式 -->
        <view v-else-if="practiceMode === 'voice'" class="voice-mode">
          <view class="voice-recorder">
            <view
              class="record-btn"
              :class="{ recording: isRecording }"
              @click="isRecording ? stopRecording() : startRecording()"
            >
              <view
                class="record-icon"
                :class="isRecording ? 'i-mdi-stop' : 'i-mdi-microphone'"
              ></view>
              <text class="record-text">{{ isRecording ? '停止录音' : '开始录音' }}</text>
            </view>

            <view v-if="isRecording" class="recording-time">
              <view class="time-dot"></view>
              <text class="time-text">{{ formatTime(recordingTime) }}</text>
            </view>
          </view>

          <view v-if="audioPath" class="audio-preview">
            <view class="preview-header">
              <view class="preview-icon i-mdi-waveform"></view>
              <text class="preview-title">录音预览</text>
            </view>
            <view class="audio-controls">
              <button class="play-btn" @click="playAudio">
                <view class="btn-icon i-mdi-play"></view>
                播放录音
              </button>
              <button class="rerecord-btn" @click="audioPath = ''">
                <view class="btn-icon i-mdi-refresh"></view>
                重新录制
              </button>
            </view>
          </view>
        </view>

        <!-- 视频模式 -->
        <view v-else-if="practiceMode === 'video'" class="video-mode">
          <!-- #ifdef MP-WEIXIN -->
          <camera device-position="front" flash="off" class="camera-view" v-if="!videoPath">
            <view class="camera-overlay">
              <view
                class="video-record-btn"
                :class="{ recording: isVideoRecording }"
                @click="isVideoRecording ? stopVideoRecording() : startVideoRecording()"
              >
                <view
                  class="record-icon"
                  :class="isVideoRecording ? 'i-mdi-stop' : 'i-mdi-video'"
                ></view>
              </view>
              <view v-if="isVideoRecording" class="video-timer">
                {{ formatTime(videoRecordingTime) }}
              </view>
            </view>
          </camera>

          <video v-if="videoPath" :src="videoPath" controls class="video-preview"></video>
          <!-- #endif -->

          <!-- #ifdef H5 -->
          <view class="platform-tip">
            <view class="tip-icon i-mdi-information"></view>
            <text class="tip-text">H5平台暂不支持视频录制，请使用其他练习模式</text>
          </view>
          <!-- #endif -->
        </view>

        <!-- 文字模式 -->
        <view v-else-if="practiceMode === 'text'" class="text-mode">
          <textarea
            v-model="textAnswer"
            class="text-editor"
            placeholder="请输入你的答案..."
            :maxlength="2000"
          />
          <view class="text-tools">
            <text class="char-count">{{ textAnswer.length }}/2000</text>
          </view>
        </view>
      </view>

      <!-- AI助手 -->
      <view class="ai-helper-section">
        <view class="ai-toggle" @click="toggleAIHelper">
          <view class="ai-icon i-mdi-robot"></view>
          <text class="ai-title">AI智能辅导</text>
          <view
            class="toggle-icon"
            :class="showAIHelper ? 'i-mdi-chevron-up' : 'i-mdi-chevron-down'"
          ></view>
        </view>

        <view v-if="showAIHelper" class="ai-content">
          <view v-if="isAIThinking" class="ai-thinking">
            <view class="thinking-icon"></view>
            <text class="thinking-text">AI正在分析题目...</text>
          </view>
          <text v-else class="ai-suggestion">{{ aiSuggestion }}</text>

          <view class="ai-actions">
            <button class="ai-btn" @click="getAISuggestion">
              <view class="btn-icon i-mdi-refresh"></view>
              重新分析
            </button>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" @click="submitAnswer">
          <view class="btn-icon i-mdi-send"></view>
          提交答案
        </button>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
/* 页面容器 */
.practice-page {
  min-height: 100vh;
  background: #f8fafc;
}

.main-scroll {
  background: transparent;
}
/* 题目信息卡片 */
.question-card {
  padding: 32rpx;
  margin: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.question-header {
  margin-bottom: 24rpx;
}

.question-title {
  display: block;
  margin-bottom: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.4;
  color: #1e293b;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.difficulty-tag {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  border-radius: 16rpx;

  &.difficulty-简单 {
    color: #16a34a;
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  }

  &.difficulty-中等 {
    color: #d97706;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
  }

  &.difficulty-困难 {
    color: #dc2626;
    background: linear-gradient(135deg, #fee2e2, #fecaca);
  }
}

.tag-item {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #2563eb;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border-radius: 16rpx;
}

.question-content {
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  color: #475569;
  white-space: pre-wrap;
}
/* 模式切换器 */
.mode-switcher {
  display: flex;
  gap: 16rpx;
  padding: 16rpx;
  margin: 0 24rpx 24rpx;
  background: #ffffff;
  border-radius: 20rpx;
}

.mode-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.3s;

  &.active {
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    border-color: #00c9a7;

    .mode-icon,
    .mode-text {
      font-size: 36rpx;
      color: #64748b;
    }

    .mode-text {
      font-size: 24rpx;
      font-weight: 500;
      color: #64748b;
    }
  }

  &:active:not(.active) {
    transform: scale(0.95);
  }
}
/* 练习区域 */
.practice-area {
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
/* 代码模式 */
.code-mode {
  .code-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }

  .language-selector {
    display: flex;
    gap: 12rpx;
    align-items: center;

    .label {
      font-size: 26rpx;
      color: #64748b;
    }

    .picker-value {
      display: flex;
      gap: 8rpx;
      align-items: center;
      padding: 12rpx 20rpx;
      font-size: 26rpx;
      color: #1e293b;
      background: #f8fafc;
      border: 2rpx solid #e2e8f0;
      border-radius: 12rpx;

      .arrow-icon {
        font-size: 20rpx;
        color: #94a3b8;
      }
    }
  }

  .run-btn {
    display: flex;
    gap: 8rpx;
    align-items: center;
    padding: 12rpx 24rpx;
    font-size: 26rpx;
    font-weight: 500;
    color: #ffffff;
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    border: none;
    border-radius: 16rpx;

    .btn-icon {
      font-size: 24rpx;
    }

    &:active:not(:disabled) {
      transform: scale(0.95);
    }

    .code-editor {
      box-sizing: border-box;
      width: 100%;
      padding: 24rpx;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 26rpx;
      line-height: 1.6;
      color: #1e293b;
      background: #f8fafc;
      border: 2rpx solid #e2e8f0;
      border-radius: 16rpx;
    }

    .code-result {
      padding: 24rpx;
      margin-top: 24rpx;
      background: #f0fdf4;
      border: 2rpx solid #bbf7d0;
      border-radius: 16rpx;

      .result-header {
        display: flex;
        gap: 12rpx;
        align-items: center;
        margin-bottom: 16rpx;

        .result-icon {
          font-size: 28rpx;
          color: #16a34a;
        }

        .result-title {
          font-size: 26rpx;
          font-weight: 600;
          color: #16a34a;
        }
      }

      .result-content {
        display: block;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 24rpx;
        line-height: 1.6;
        color: #166534;
        white-space: pre-wrap;
      }
    }
  }
}
/* 语音模式 */
.voice-mode {
  .voice-recorder {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 0;
  }

  .record-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 200rpx;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 6rpx solid #cbd5e1;
    border-radius: 50%;
    transition: all 0.3s;

    &.recording {
      background: linear-gradient(135deg, #fee2e2, #fecaca);
      border-color: #f87171;
      animation: pulse 2s infinite;

      .record-icon,
      .record-text {
        color: #dc2626;
      }

      &:active:not(.recording) {
        transform: scale(0.95);
      }
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(248, 113, 113, 0.4);
      }
      70% {
        box-shadow: 0 0 0 20rpx rgba(248, 113, 113, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(248, 113, 113, 0);
      }
    }

    .record-icon {
      margin-bottom: 16rpx;
      font-size: 64rpx;
      color: #64748b;
    }

    .record-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #64748b;
    }

    .recording-time {
      display: flex;
      gap: 12rpx;
      align-items: center;
      margin-top: 32rpx;

      .time-dot {
        width: 16rpx;
        height: 16rpx;
        background: #dc2626;
        border-radius: 50%;
        animation: blink 1s infinite;
      }

      @keyframes blink {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .time-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #dc2626;
      }
    }
  }

  .audio-preview {
    padding: 24rpx;
    margin-top: 48rpx;
    background: #f8fafc;
    border-radius: 16rpx;

    .preview-header {
      display: flex;
      gap: 12rpx;
      align-items: center;
      margin-bottom: 20rpx;

      .preview-icon {
        font-size: 28rpx;
        color: #00c9a7;
      }

      .preview-title {
        font-size: 26rpx;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .audio-controls {
      display: flex;
      gap: 16rpx;

      .play-btn,
      .rerecord-btn {
        display: flex;
        flex: 1;
        gap: 8rpx;
        align-items: center;
        justify-content: center;
        padding: 16rpx;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        border-radius: 16rpx;

        .btn-icon {
          font-size: 24rpx;
        }
      }

      .play-btn {
        color: #ffffff;
        background: linear-gradient(135deg, #00c9a7, #4fd1c7);
      }

      .rerecord-btn {
        color: #64748b;
        background: #e2e8f0;
      }
    }
  }
}
/* 视频模式 */
.video-mode {
  .camera-view {
    position: relative;
    width: 100%;
    height: 600rpx;
    overflow: hidden;
    background: #000000;
    border-radius: 16rpx;
  }

  .camera-overlay {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 48rpx;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  }

  .video-record-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.9);
    border: 6rpx solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transition: all 0.3s;

    &.recording {
      background: #dc2626;

      .record-icon {
        font-size: 48rpx;
        color: #ffffff;
      }
    }
  }

  .video-timer {
    padding: 8rpx 24rpx;
    margin-top: 24rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 20rpx;
  }

  .video-preview {
    width: 100%;
    height: 600rpx;
    border-radius: 16rpx;
  }

  .platform-tip {
    display: flex;
    gap: 16rpx;
    align-items: center;
    padding: 32rpx;
    background: #fef3c7;
    border: 2rpx solid #fde68a;
    border-radius: 16rpx;

    .tip-icon {
      flex-shrink: 0;
      font-size: 32rpx;
      color: #d97706;
    }

    .tip-text {
      font-size: 26rpx;
      line-height: 1.6;
      color: #92400e;
    }
  }
}
/* 文字模式 */
.text-mode {
  .text-editor {
    box-sizing: border-box;
    width: 100%;
    min-height: 400rpx;
    padding: 24rpx;
    font-size: 28rpx;
    line-height: 1.6;
    color: #1e293b;
    background: #f8fafc;
    border: 2rpx solid #e2e8f0;
    border-radius: 16rpx;
  }

  .text-tools {
    margin-top: 16rpx;
    text-align: right;

    .char-count {
      font-size: 24rpx;
      color: #94a3b8;
    }
  }
}
/* AI助手 */
.ai-helper-section {
  margin: 0 24rpx 24rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.ai-toggle {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);

  .ai-icon {
    margin-right: 12rpx;
    font-size: 32rpx;
    color: #2563eb;
  }

  .ai-title {
    flex: 1;
    font-size: 28rpx;
    font-weight: 600;
    color: #1e293b;
  }

  .toggle-icon {
    font-size: 28rpx;
    color: #64748b;
  }
}

.ai-content {
  padding: 32rpx;

  .ai-thinking {
    display: flex;
    gap: 16rpx;
    align-items: center;
    justify-content: center;
    padding: 48rpx;

    .thinking-icon {
      width: 32rpx;
      height: 32rpx;
      border: 4rpx solid #e0f2fe;
      border-top: 4rpx solid #2563eb;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .thinking-text {
      font-size: 26rpx;
      color: #64748b;
    }
  }

  .ai-suggestion {
    display: block;
    font-size: 26rpx;
    line-height: 1.8;
    color: #475569;
    white-space: pre-wrap;
  }

  .ai-actions {
    margin-top: 24rpx;

    .ai-btn {
      display: flex;
      gap: 8rpx;
      align-items: center;
      justify-content: center;
      padding: 16rpx 32rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #2563eb;
      background: #e0f2fe;
      border: none;
      border-radius: 16rpx;

      .btn-icon {
        font-size: 24rpx;
      }

      &:active {
        background: #bfdbfe;
      }
    }
  }
}
/* 提交按钮 */
.submit-section {
  padding: 24rpx 24rpx 48rpx;

  .submit-btn {
    display: flex;
    gap: 12rpx;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 28rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    border: none;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

    &:active {
      box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
      transform: translateY(2rpx);
    }
  }
}
/* H5端滚动条优化 */
// #ifdef H5
.main-scroll::-webkit-scrollbar {
  width: 6rpx;
}

.main-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3rpx;
}

.main-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 201, 167, 0.6);
  border-radius: 3rpx;
}
.main-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 201, 167, 0.8);
}
// #endif
</style>
