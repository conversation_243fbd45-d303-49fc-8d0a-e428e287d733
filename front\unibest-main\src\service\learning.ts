/**
 * 学习资源服务模块
 * 统一管理所有学习资源相关的API接口
 * <AUTHOR>
 */
import { httpGet, httpPost } from '@/utils/http'
import type {
  ApiResponse,
  Major,
  QuestionBank,
  QuestionBankVO,
  QuestionBankListParams,
  QuestionBankListResponse,
  ToggleBookmarkParams,
  Book,
  BookChapter,
  BookDetail,
  BookListParams,
  BookListResponse,
  ChapterListResponse,
  ReadingRecord,
  ReadingSettings,
  ReadingStats,
  CategoryOption,
  LearningDataCenterStats,
  LearningRecord,
  LearningRecordQueryParams,
  LearningRecordListResponse,
  LearningAnalysis,
  AiLearningAdvice,
  LearningGoal,
  LearningAchievement,
  AiChatHistory,
} from '@/types/learning'
import { LEARNING_API_ENDPOINTS } from '@/types/learning-constants'

/**
 * 题目接口定义
 */
export interface Question {
  id: string
  title: string
  content?: string
  difficulty: string
  practiceCount: number
  correctRate: number
  commentCount: number
  category: string
  tags?: string[]
  type: string
  acceptanceRate?: number
  isCompleted?: boolean
  isBookmarked?: boolean
  createTime: string
  estimatedTime?: number
}

/**
 * 题目详情接口定义
 */
export interface QuestionDetail {
  id: string
  title: string
  description: string
  content: string
  difficulty: string
  tags: string[]
  acceptanceRate: number
  isCompleted: boolean
  practiceCount: number
  correctRate: number
  commentCount: number
  category: string
  bankId: string
  bankTitle: string
  createdAt: string
  updatedAt: string
}

/**
 * 题目查询参数
 */
export interface QuestionQueryParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  difficulty?: string
  category?: string
  completed?: boolean
  orderBy?: string
  orderDirection?: string
}

/**
 * 题目列表响应数据
 */
export interface QuestionListResponse {
  list: Question[]
  total: number
  page: number
  pageSize: number
}

/**
 * 题目统计信息
 */
export interface QuestionStatistics {
  totalQuestions: number
  easyCount: number
  mediumCount: number
  hardCount: number
  averageCorrectRate: number
}

/**
 * 学习统计数据接口
 */
export interface LearningStats {
  totalHours: number
  completedCourses: number
  currentStreak: number
  weeklyProgress: number
}

/**
 * 热门书籍接口
 */
export interface HotBook {
  id: number
  title: string
  cover: string
  author: string
  rating: number
  readCount: number
  tags: string[]
  price?: number
  isFree?: boolean
}

/**
 * 热门视频接口
 */
export interface HotVideo {
  id: number
  title: string
  thumbnail: string
  instructor: string
  duration: string
  viewCount: number
  isFree: boolean
  price?: number
}

/**
 * 今日推荐接口
 */
export interface TodayRecommendation {
  type: string
  title: string
  description: string
  progress: number
  nextLesson: string
  courseId?: string
}

/**
 * AI对话消息接口
 */
export interface AiMessage {
  type: 'user' | 'ai'
  content: string
  time: string
}

/**
 * AI对话请求参数
 */
export interface AiChatParams {
  message: string
  context?: string
  history?: AiMessage[]
}

/**
 * 获取专业列表
 * @returns 专业列表响应
 */
export function getMajorList(): Promise<ApiResponse<Major[]>> {
  return httpGet<Major[]>(LEARNING_API_ENDPOINTS.GET_MAJOR_LIST)
}

/**
 * 获取题库列表
 * @param params 查询参数
 * @returns 题库列表响应
 */
export function getQuestionBankList(params: {
  params: QuestionBankListParams
}): Promise<ApiResponse<QuestionBankListResponse>> {
  return httpGet<QuestionBankListResponse>(
    LEARNING_API_ENDPOINTS.GET_QUESTION_BANK_LIST,
    params.params,
  )
}

/**
 * 切换题库收藏状态
 * @param params 收藏参数
 * @returns API响应
 */
export function toggleQuestionBankBookmark(params: {
  params: ToggleBookmarkParams
}): Promise<ApiResponse<{ isBookmarked: boolean }>> {
  return httpPost<{ isBookmarked: boolean }>(LEARNING_API_ENDPOINTS.TOGGLE_BOOKMARK, params.params)
}

/**
 * 搜索题库
 * @param params 搜索参数
 * @returns 题库列表响应
 */
export function searchQuestionBanks(params: {
  params: { keyword: string; majorId?: string }
}): Promise<ApiResponse<QuestionBankVO[]>> {
  return httpGet<QuestionBankVO[]>(LEARNING_API_ENDPOINTS.SEARCH_QUESTION_BANKS, params.params)
}

/**
 * 获取题库详情
 * @param params 题库ID和专业ID
 * @returns 题库详情响应
 */
export function getQuestionBankDetail(params: {
  params: { bankId: string; majorId: string }
}): Promise<ApiResponse<QuestionBank>> {
  return httpGet<QuestionBank>(
    `${LEARNING_API_ENDPOINTS.GET_QUESTION_BANK_DETAIL}/${params.params.bankId}`,
    { majorId: params.params.majorId },
  )
}

/**
 * 获取热门题库
 * @param params 查询参数
 * @returns 题库列表响应
 */
export function getHotQuestionBanks(params?: {
  params?: { limit?: number }
}): Promise<ApiResponse<QuestionBank[]>> {
  return httpGet<QuestionBank[]>(LEARNING_API_ENDPOINTS.GET_HOT_QUESTION_BANKS, params?.params)
}

/**
 * 获取最新题库
 * @param params 查询参数
 * @returns 题库列表响应
 */
export function getNewQuestionBanks(params?: {
  params?: { limit?: number }
}): Promise<ApiResponse<QuestionBank[]>> {
  return httpGet<QuestionBank[]>(LEARNING_API_ENDPOINTS.GET_NEW_QUESTION_BANKS, params?.params)
}

/**
 * 获取题库题目列表（支持筛选和搜索）
 * @param bankId 题库ID
 * @param params 查询参数
 * @returns 题目列表响应
 */
export function getQuestionList(
  bankId: string,
  params: QuestionQueryParams = {},
): Promise<ApiResponse<QuestionListResponse>> {
  return httpGet<QuestionListResponse>(`/app/learning/question-banks/${bankId}/questions`, params)
}

/**
 * 获取题库题目统计信息
 * @param bankId 题库ID
 * @returns 统计信息响应
 */
export function getQuestionStatistics(bankId: string): Promise<ApiResponse<QuestionStatistics>> {
  return httpGet<QuestionStatistics>(`/app/learning/question-banks/${bankId}/statistics`)
}

/**
 * 获取题目详情
 * @param questionId 题目ID
 * @returns 题目详情响应
 */
export function getQuestionDetail(questionId: string): Promise<ApiResponse<QuestionDetail>> {
  return httpGet<QuestionDetail>(`/app/learning/questions/${questionId}`)
}

/**
 * 搜索题目
 * @param bankId 题库ID
 * @param params 搜索参数
 * @returns 题目列表响应
 */
export function searchQuestions(
  bankId: string,
  params: {
    keyword?: string
    difficulty?: string
    category?: string
    completed?: boolean
  } = {},
): Promise<ApiResponse<Question[]>> {
  return httpGet<Question[]>(`/app/learning/question-banks/${bankId}/questions/search`, params)
}

/**
 * 切换题目收藏状态
 * @param questionId 题目ID
 * @param params 收藏参数
 * @returns 收藏状态响应
 */
export function toggleQuestionBookmark(
  questionId: string,
  params: { isBookmarked: boolean },
): Promise<ApiResponse<{ isBookmarked: boolean; message: string }>> {
  return httpPost<{ isBookmarked: boolean; message: string }>(
    `/app/learning/questions/${questionId}/bookmark`,
    params,
  )
}

/**
 * 开始练习题目
 * @param questionId 题目ID
 * @param params 练习参数
 * @returns 练习响应
 */
export function startQuestionPractice(
  questionId: string,
  params: { bankId: string; majorId?: string },
): Promise<ApiResponse<{ practiceId: string; question: QuestionDetail }>> {
  return httpPost<{ practiceId: string; question: QuestionDetail }>(
    `/app/learning/questions/${questionId}/practice`,
    params,
  )
}

/**
 * 获取题目评论列表
 * @param questionId 题目ID
 * @param params 查询参数
 * @returns 评论列表响应
 */
export function getQuestionComments(
  questionId: string,
  params: {
    page?: number
    pageSize?: number
    orderBy?: string
    orderDirection?: string
  } = {},
): Promise<ApiResponse<{ list: any[]; total: number; page: number; pageSize: number }>> {
  return httpGet<{ list: any[]; total: number; page: number; pageSize: number }>(
    `/app/learning/questions/${questionId}/comments`,
    params,
  )
}

/**
 * 创建题目评论
 * @param questionId 题目ID
 * @param params 评论参数
 * @returns 评论响应
 */
export function createQuestionComment(
  questionId: string,
  params: { content: string; parentId?: string },
): Promise<ApiResponse<any>> {
  return httpPost<any>(`/app/learning/questions/${questionId}/comments`, params)
}

/**
 * 切换题库收藏状态（新版）
 * @param bankId 题库ID
 * @returns 收藏状态响应
 */
export function toggleQuestionBankBookmarkNew(
  bankId: string,
): Promise<ApiResponse<{ isBookmarked: boolean; message: string }>> {
  return httpPost<{ isBookmarked: boolean; message: string }>(
    `/app/learning/question-banks/${bankId}/toggle-bookmark`,
    {},
  )
}

/**
 * 获取题库完整详情（包含学习进度等信息）
 * @param bankId 题库ID
 * @returns 题库完整详情响应
 */
export function getQuestionBankFullDetail(bankId: string): Promise<ApiResponse<any>> {
  return httpGet<any>(`/app/learning/question-banks/${bankId}/detail`)
}

/**
 * 获取题库分类题目
 * @param bankId 题库ID
 * @returns 按分类组织的题目列表响应
 */
export function getQuestionsByCategory(
  bankId: string,
): Promise<ApiResponse<Record<string, Question[]>>> {
  return httpGet<Record<string, Question[]>>(
    `/app/learning/question-banks/${bankId}/questions-by-category`,
  )
}

/**
 * 获取推荐题目
 * @param bankId 题库ID
 * @param params 查询参数
 * @returns 推荐题目列表响应
 */
export function getRecommendedQuestions(
  bankId: string,
  params: { limit?: number } = {},
): Promise<ApiResponse<Question[]>> {
  return httpGet<Question[]>(`/app/learning/question-banks/${bankId}/recommended-questions`, params)
}

/**
 * 获取学习统计数据
 * @returns 学习统计响应
 */
export function getLearningStats(): Promise<ApiResponse<LearningStats>> {
  return httpGet<LearningStats>('/app/learning/stats')
}

/**
 * 获取热门书籍列表
 * @param params 查询参数
 * @returns 书籍列表响应
 */
export function getHotBooks(params?: { limit?: number }): Promise<ApiResponse<HotBook[]>> {
  return httpGet<HotBook[]>('/app/learning/books/hot', params)
}

/**
 * 获取热门视频列表
 * @param params 查询参数
 * @returns 视频列表响应
 */
export function getHotVideos(params?: { limit?: number }): Promise<ApiResponse<HotVideo[]>> {
  return httpGet<HotVideo[]>('/app/video/hot', params)
}

/**
 * 获取今日推荐
 * @returns 今日推荐响应
 */
export function getTodayRecommendation(): Promise<ApiResponse<TodayRecommendation>> {
  return httpGet<TodayRecommendation>('/app/learning/today-recommendation')
}

/**
 * 发送AI对话消息
 * @param params 对话参数
 * @returns AI回复响应
 */
export function sendAiChatMessage(params: AiChatParams): Promise<
  ApiResponse<{
    reply: string
    suggestions?: string[]
  }>
> {
  return httpPost<{ reply: string; suggestions?: string[] }>('/app/learning/ai-chat', params)
}

/**
 * 获取AI对话建议
 * @returns 建议列表响应
 */
export function getAiChatSuggestions(): Promise<ApiResponse<string[]>> {
  return httpGet<string[]>('/app/learning/ai-chat/suggestions')
}

/**
 * 获取资源分类统计
 * @returns 资源统计响应
 */
export function getResourceCategoryStats(): Promise<
  ApiResponse<
    Array<{
      id: string
      name: string
      count: number
    }>
  >
> {
  return httpGet<Array<{ id: string; name: string; count: number }>>(
    '/app/learning/resource-category-stats',
  )
}

/**
 * @description 获取专业下的所有题库列表（增强版）
 * @param majorId 专业ID
 * @param params 查询参数
 * @returns 题库列表响应
 */
export const getMajorQuestionBankList = (majorId: string, params?: QuestionBankListParams) => {
  return httpGet<QuestionBankListResponse>(`/app/learning/majors/${majorId}/question-banks`, {
    params,
  })
}

/**
 * @description 获取专业题库统计信息
 * @param majorId 专业ID
 * @returns 统计信息响应
 */
export const getMajorQuestionBankStatistics = (majorId: string) => {
  return httpGet<{
    totalBanks: number
    totalQuestions: number
    averageProgress: number
    bookmarkedBanks: number
    completedBanks: number
  }>(`/app/learning/majors/${majorId}/question-banks/statistics`)
}

/**
 * @description 获取专业题库筛选选项计数
 * @param majorId 专业ID
 * @returns 筛选选项计数响应
 */
export const getMajorQuestionBankFilterCounts = (majorId: string) => {
  return httpGet<{
    all: number
    easy: number
    medium: number
    hard: number
    bookmarked: number
    completed: number
  }>(`/app/learning/majors/${majorId}/question-banks/filter-counts`)
}

/**
 * @description 重置专业题库筛选条件
 * @param majorId 专业ID
 * @returns 重置结果响应
 */
export const resetMajorQuestionBankFilters = (majorId: string) => {
  return httpPost<QuestionBankListResponse>(
    `/app/learning/majors/${majorId}/question-banks/reset-filters`,
  )
}

/**
 * ============================================================================
 * 书籍相关 API 接口
 * ============================================================================
 */

/**
 * @description 获取书籍列表
 * @param params 查询参数
 * @returns 书籍列表响应
 */
export function getBookList(params: BookListParams = {}): Promise<ApiResponse<BookListResponse>> {
  return httpGet<BookListResponse>(LEARNING_API_ENDPOINTS.GET_BOOK_LIST, params)
}

/**
 * @description 获取书籍详情
 * @param bookId 书籍ID
 * @returns 书籍详情响应
 */
export function getBookDetail(bookId: number): Promise<ApiResponse<BookDetail>> {
  const url = LEARNING_API_ENDPOINTS.GET_BOOK_DETAIL.replace('{bookId}', bookId.toString())
  return httpGet<BookDetail>(url)
}

/**
 * @description 获取热门书籍
 * @param params 查询参数
 * @returns 热门书籍列表
 */
export function getHotBookList(params?: { limit?: number }): Promise<ApiResponse<Book[]>> {
  return httpGet<Book[]>(LEARNING_API_ENDPOINTS.GET_HOT_BOOKS, params)
}

/**
 * @description 获取最新书籍
 * @param params 查询参数
 * @returns 最新书籍列表
 */
export function getNewBookList(params?: { limit?: number }): Promise<ApiResponse<Book[]>> {
  return httpGet<Book[]>(LEARNING_API_ENDPOINTS.GET_NEW_BOOKS, params)
}

/**
 * @description 搜索书籍
 * @param params 搜索参数
 * @returns 书籍搜索结果
 */
export function searchBooks(params: {
  keyword?: string
  category?: string
  difficulty?: string
  isFree?: boolean
}): Promise<ApiResponse<Book[]>> {
  return httpGet<Book[]>(LEARNING_API_ENDPOINTS.SEARCH_BOOKS, params)
}

/**
 * @description 获取书籍章节列表
 * @param bookId 书籍ID
 * @returns 章节列表响应
 */
export function getBookChapters(bookId: number): Promise<ApiResponse<ChapterListResponse>> {
  const url = LEARNING_API_ENDPOINTS.GET_BOOK_CHAPTERS.replace('{bookId}', bookId.toString())
  return httpGet<ChapterListResponse>(url)
}

/**
 * @description 获取章节详情
 * @param chapterId 章节ID
 * @returns 章节详情响应
 */
export function getChapterDetail(chapterId: number): Promise<ApiResponse<BookChapter>> {
  const url = LEARNING_API_ENDPOINTS.GET_CHAPTER_DETAIL.replace('{chapterId}', chapterId.toString())
  return httpGet<BookChapter>(url)
}

/**
 * @description 获取阅读记录
 * @param bookId 书籍ID
 * @returns 阅读记录响应
 */
export function getReadingRecord(bookId: number): Promise<ApiResponse<ReadingRecord>> {
  const url = LEARNING_API_ENDPOINTS.GET_READING_RECORD.replace('{bookId}', bookId.toString())
  return httpGet<ReadingRecord>(url)
}

/**
 * @description 更新阅读进度
 * @param bookId 书籍ID
 * @param params 进度参数
 * @returns 更新结果
 */
export function updateReadingProgress(
  bookId: number,
  params: {
    chapterId: number
    progress: number
    readTime: number
    bookmarkPosition?: number
  },
): Promise<ApiResponse<{ message: string }>> {
  const url = LEARNING_API_ENDPOINTS.UPDATE_READING_PROGRESS.replace('{bookId}', bookId.toString())
  return httpPost<{ message: string }>(url, params)
}

/**
 * @description 获取阅读统计
 * @returns 阅读统计响应
 */
export function getReadingStats(): Promise<ApiResponse<ReadingStats>> {
  return httpGet<ReadingStats>(LEARNING_API_ENDPOINTS.GET_READING_STATS)
}

/**
 * @description 切换书籍收藏状态
 * @param bookId 书籍ID
 * @returns 收藏状态响应
 */
export function toggleBookBookmark(
  bookId: number,
): Promise<ApiResponse<{ isBookmarked: boolean; message: string }>> {
  const url = LEARNING_API_ENDPOINTS.TOGGLE_BOOK_BOOKMARK.replace('{bookId}', bookId.toString())
  return httpPost<{ isBookmarked: boolean; message: string }>(url, {})
}

/**
 * @description 获取相关书籍推荐
 * @param bookId 书籍ID
 * @param params 查询参数
 * @returns 相关书籍列表
 */
export function getRelatedBooks(
  bookId: number,
  params?: { limit?: number },
): Promise<ApiResponse<Book[]>> {
  const url = LEARNING_API_ENDPOINTS.GET_RELATED_BOOKS.replace('{bookId}', bookId.toString())
  return httpGet<Book[]>(url, params)
}

/**
 * ============================================================================
 * 学习数据中心相关 API 接口
 * ============================================================================
 */

/**
 * @description 获取学习数据中心统计信息
 * @returns 学习数据中心统计响应
 */
export function getLearningDataCenterStats(): Promise<ApiResponse<LearningDataCenterStats>> {
  return httpGet<LearningDataCenterStats>('/app/learning/data-center/stats')
}

/**
 * @description 获取学习记录列表
 * @param params 查询参数
 * @returns 学习记录列表响应
 */
export function getLearningRecords(
  params: LearningRecordQueryParams = {},
): Promise<ApiResponse<LearningRecordListResponse>> {
  return httpGet<LearningRecordListResponse>('/app/learning/data-center/records', params)
}

/**
 * @description 删除学习记录
 * @param recordId 记录ID
 * @returns 删除结果响应
 */
export function deleteLearningRecord(recordId: string): Promise<ApiResponse<{ message: string }>> {
  return httpPost<{ message: string }>(`/app/learning/data-center/records/${recordId}/delete`, {})
}

/**
 * @description 批量删除学习记录
 * @param recordIds 记录ID列表
 * @returns 批量删除结果响应
 */
export function batchDeleteLearningRecords(
  recordIds: string[],
): Promise<ApiResponse<{ message: string; deletedCount: number }>> {
  return httpPost<{ message: string; deletedCount: number }>(
    '/app/learning/data-center/records/batch-delete',
    { recordIds },
  )
}

/**
 * @description 获取学习分析数据
 * @param params 查询参数
 * @returns 学习分析数据响应
 */
export function getLearningAnalysis(
  params: {
    timeRange?: 'week' | 'month' | 'quarter' | 'year'
    majorId?: string
  } = {},
): Promise<ApiResponse<LearningAnalysis>> {
  return httpGet<LearningAnalysis>('/app/learning/data-center/analysis', params)
}

/**
 * @description 获取AI学习建议列表
 * @param params 查询参数
 * @returns AI学习建议列表响应
 */
export function getAiLearningAdvice(
  params: {
    page?: number
    pageSize?: number
    type?: 'study_plan' | 'weakness_improvement' | 'motivation' | 'time_management'
    priority?: 'high' | 'medium' | 'low'
    isRead?: boolean
  } = {},
): Promise<
  ApiResponse<{
    list: AiLearningAdvice[]
    total: number
    page: number
    pageSize: number
  }>
> {
  return httpGet<{
    list: AiLearningAdvice[]
    total: number
    page: number
    pageSize: number
  }>('/app/learning/data-center/ai-advice', params)
}

/**
 * @description 生成新的AI学习建议
 * @param params 生成参数
 * @returns 新生成的AI建议响应
 */
export function generateAiLearningAdvice(params: {
  analysisData?: any
  focusArea?: string
  learningGoals?: string[]
}): Promise<ApiResponse<AiLearningAdvice>> {
  return httpPost<AiLearningAdvice>('/app/learning/data-center/ai-advice/generate', params)
}

/**
 * @description 标记AI建议为已读
 * @param adviceId 建议ID
 * @returns 标记结果响应
 */
export function markAiAdviceAsRead(adviceId: string): Promise<ApiResponse<{ message: string }>> {
  return httpPost<{ message: string }>(`/app/learning/data-center/ai-advice/${adviceId}/read`, {})
}

/**
 * @description 标记AI建议为已实施
 * @param adviceId 建议ID
 * @returns 标记结果响应
 */
export function markAiAdviceAsImplemented(
  adviceId: string,
): Promise<ApiResponse<{ message: string }>> {
  return httpPost<{ message: string }>(
    `/app/learning/data-center/ai-advice/${adviceId}/implement`,
    {},
  )
}

/**
 * @description 获取学习目标列表
 * @param params 查询参数
 * @returns 学习目标列表响应
 */
export function getLearningGoals(
  params: {
    page?: number
    pageSize?: number
    type?: 'daily' | 'weekly' | 'monthly' | 'custom'
    status?: 'active' | 'completed' | 'paused' | 'failed'
  } = {},
): Promise<
  ApiResponse<{
    list: LearningGoal[]
    total: number
    page: number
    pageSize: number
  }>
> {
  return httpGet<{
    list: LearningGoal[]
    total: number
    page: number
    pageSize: number
  }>('/app/learning/data-center/goals', params)
}

/**
 * @description 创建学习目标
 * @param params 目标参数
 * @returns 创建结果响应
 */
export function createLearningGoal(params: {
  title: string
  description: string
  type: 'daily' | 'weekly' | 'monthly' | 'custom'
  target: number
  unit: 'minutes' | 'questions' | 'books' | 'courses'
  deadline?: string
}): Promise<ApiResponse<LearningGoal>> {
  return httpPost<LearningGoal>('/app/learning/data-center/goals', params)
}

/**
 * @description 更新学习目标
 * @param goalId 目标ID
 * @param params 更新参数
 * @returns 更新结果响应
 */
export function updateLearningGoal(
  goalId: string,
  params: {
    title?: string
    description?: string
    target?: number
    deadline?: string
    status?: 'active' | 'completed' | 'paused' | 'failed'
  },
): Promise<ApiResponse<LearningGoal>> {
  return httpPost<LearningGoal>(`/app/learning/data-center/goals/${goalId}`, params)
}

/**
 * @description 删除学习目标
 * @param goalId 目标ID
 * @returns 删除结果响应
 */
export function deleteLearningGoal(goalId: string): Promise<ApiResponse<{ message: string }>> {
  return httpPost<{ message: string }>(`/app/learning/data-center/goals/${goalId}/delete`, {})
}

/**
 * @description 获取学习成就列表
 * @param params 查询参数
 * @returns 学习成就列表响应
 */
export function getLearningAchievements(
  params: {
    type?: 'time' | 'question' | 'streak' | 'accuracy' | 'completion'
    rarity?: 'common' | 'rare' | 'epic' | 'legendary'
    isUnlocked?: boolean
  } = {},
): Promise<ApiResponse<LearningAchievement[]>> {
  return httpGet<LearningAchievement[]>('/app/learning/data-center/achievements', params)
}

/**
 * @description 获取AI对话历史列表
 * @param params 查询参数
 * @returns AI对话历史列表响应
 */
export function getAiChatHistories(
  params: {
    page?: number
    pageSize?: number
    keyword?: string
    startDate?: string
    endDate?: string
  } = {},
): Promise<
  ApiResponse<{
    list: AiChatHistory[]
    total: number
    page: number
    pageSize: number
  }>
> {
  return httpGet<{
    list: AiChatHistory[]
    total: number
    page: number
    pageSize: number
  }>('/app/learning/data-center/ai-chat-histories', params)
}

/**
 * @description 删除AI对话历史
 * @param historyId 历史记录ID
 * @returns 删除结果响应
 */
export function deleteAiChatHistory(historyId: string): Promise<ApiResponse<{ message: string }>> {
  return httpPost<{ message: string }>(
    `/app/learning/data-center/ai-chat-histories/${historyId}/delete`,
    {},
  )
}

/**
 * @description 清空AI对话历史
 * @returns 清空结果响应
 */
export function clearAiChatHistories(): Promise<
  ApiResponse<{ message: string; deletedCount: number }>
> {
  return httpPost<{ message: string; deletedCount: number }>(
    '/app/learning/data-center/ai-chat-histories/clear',
    {},
  )
}
