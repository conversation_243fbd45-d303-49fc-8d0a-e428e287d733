<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import Loading from '@/components/Loading.vue'
// @ts-ignore
import CategoryFilter from '@/components/CategoryFilter.vue'
// 引入API接口
import {
  getQuestionBankList,
  toggleQuestionBankBookmark,
  searchQuestionBanks,
  // @ts-ignore
} from '@/service/learning'
// @ts-ignore
import type { QuestionBankVO } from '@/types/learning'
import {
  DIFFICULTY_STYLE_MAP,
  LEARNING_NOTIFICATION_MESSAGES,
  LEARNING_STORAGE_KEYS,
  // @ts-ignore
} from '@/types/learning-constants'

// 页面加载状态
const isPageLoaded = ref(false)
const isProgressAnimated = ref(false)
const isLoading = ref(false)
// 新增：区分初始化加载和操作加载
const isInitialLoading = ref(true)

// 加载类型状态
const loadingType = ref<'normal' | 'category' | 'search' | 'filter'>('normal')

// 当前选中的专业（从本地存储读取）
const selectedMajor = ref('computer-science')

// 当前选中的难度筛选
const selectedFilter = ref('all')

// 搜索关键词
const searchQuery = ref('')

// 题库数据
const questionBanks = ref<QuestionBankVO[]>([])

// 当前页码
const currentPage = ref(1)
const pageSize = ref(10)
const totalBanks = ref(0)

// 定义分类选项类型
interface CategoryOption {
  key: string
  name: string
  icon: string
}

// 分类筛选选项 - 适配CategoryFilter组件格式
const filterOptions = ref<CategoryOption[]>([
  { key: 'all', name: '全部题库', icon: 'i-mdi-view-grid' },
  { key: 'easy', name: '简单', icon: 'i-mdi-emoticon-happy' },
  { key: 'medium', name: '中等', icon: 'i-mdi-emoticon-neutral' },
  { key: 'hard', name: '困难', icon: 'i-mdi-emoticon-dead' },
  { key: 'hot', name: '热门', icon: 'i-mdi-fire' },
  { key: 'new', name: '最新', icon: 'i-mdi-new-box' },
])

// 当前显示的题库
const currentBanks = computed(() => {
  let banks = questionBanks.value

  // 应用搜索
  if (searchQuery.value) {
    return banks.filter(
      (bank) =>
        bank.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        bank.description.toLowerCase().includes(searchQuery.value.toLowerCase()),
    )
  }

  return banks
})

/**
 * @description 获取加载文本
 * @returns 返回当前加载类型对应的提示文本
 */
const getLoadingText = computed(() => {
  const textMap = {
    normal: '加载中...',
    category: '正在筛选题库...',
    search: '正在搜索题库...',
    filter: '正在应用筛选条件...',
  }
  return textMap[loadingType.value]
})

/**
 * @description 从本地存储读取选择的专业
 */
const loadSelectedMajor = () => {
  const savedMajor = uni.getStorageSync(LEARNING_STORAGE_KEYS.SELECTED_MAJOR)
  if (savedMajor) {
    selectedMajor.value = savedMajor
  }
}

/**
 * 加载题库列表
 */
const loadQuestionBanks = async (showLoading = false) => {
  // 只有在非初始化加载时才显示操作加载状态
  if (showLoading && !isInitialLoading.value) {
    if (isLoading.value) return
    isLoading.value = true
  }

  try {
    console.log(selectedMajor.value)
    const res = await getQuestionBankList({
      params: {
        majorId: selectedMajor.value,
        filter: selectedFilter.value as 'all' | 'easy' | 'medium' | 'hard' | 'hot' | 'new',
        page: currentPage.value,
        pageSize: pageSize.value,
      },
    })
    console.log(res)

    if (res.code === 200 && res.data) {
      questionBanks.value = res.data.list
      totalBanks.value = res.data.total

      // 恢复收藏状态（从本地存储）
      const bookmarkedIds = uni.getStorageSync(LEARNING_STORAGE_KEYS.BOOKMARKED_BANKS) || []
      questionBanks.value.forEach((bank) => {
        bank.isBookmarked = bookmarkedIds.includes(bank.id)
      })
    }
  } catch (error) {
    console.error('加载题库列表失败:', error)
    uni.showToast({
      title: LEARNING_NOTIFICATION_MESSAGES.ERROR.LOAD_BANKS_FAILED,
      icon: 'error',
      duration: 1500,
    })
  } finally {
    if (showLoading && !isInitialLoading.value) {
      isLoading.value = false
    }
  }
}

/**
 * 搜索题库
 */
const performSearch = async () => {
  if (!searchQuery.value.trim()) {
    loadingType.value = 'normal' // 设置加载类型为普通加载
    loadQuestionBanks(true)
    return
  }

  loadingType.value = 'search' // 设置加载类型为搜索
  isLoading.value = true
  try {
    const res = await searchQuestionBanks({
      params: {
        keyword: searchQuery.value,
        majorId: selectedMajor.value,
      },
    })

    if (res.code === 200 && res.data) {
      questionBanks.value = res.data

      // 恢复收藏状态
      const bookmarkedIds = uni.getStorageSync(LEARNING_STORAGE_KEYS.BOOKMARKED_BANKS) || []
      questionBanks.value.forEach((bank) => {
        bank.isBookmarked = bookmarkedIds.includes(bank.id)
      })
    }
  } catch (error) {
    console.error('搜索题库失败:', error)
    uni.showToast({
      title: LEARNING_NOTIFICATION_MESSAGES.ERROR.SEARCH_FAILED,
      icon: 'error',
      duration: 1500,
    })
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 处理分类筛选改变事件
 * @param filter 筛选的 key 值
 */
const handleFilterChange = (filter: string): void => {
  selectedFilter.value = filter
  currentPage.value = 1
  loadingType.value = 'category' // 设置加载类型为分类筛选
  loadQuestionBanks(true)
}

/**
 * @description 处理分类筛选重置事件
 */
const handleFilterReset = (): void => {
  selectedFilter.value = 'all'
  searchQuery.value = ''
  currentPage.value = 1
  loadingType.value = 'filter' // 设置加载类型为筛选
  loadQuestionBanks(true)
  uni.showToast({
    title: LEARNING_NOTIFICATION_MESSAGES.SUCCESS.FILTER_RESET,
    icon: 'success',
    duration: 1500,
  })
}

/**
 * 搜索处理
 */
const handleSearch = (value: string) => {
  searchQuery.value = value
  performSearch()
}

/**
 * 清除搜索
 */
const clearSearch = () => {
  searchQuery.value = ''
  loadingType.value = 'normal' // 设置加载类型为普通加载
  loadQuestionBanks()
}

/**
 * 获取难度样式
 */
const getDifficultyStyle = (difficulty: string) => {
  return DIFFICULTY_STYLE_MAP[difficulty] || 'bg-gray-100 text-gray-700'
}

/**
 * 打开题库详情
 */
const openQuestionBankDetail = (bankId: string) => {
  uni.navigateTo({
    url: `/pages/learning/question-bank?id=${bankId}&major=${selectedMajor.value}`,
  })
}

/**
 * 查看全部题库
 */
const showAllQuestionBanks = () => {
  uni.navigateTo({
    url: '/pages/learning/all-question-banks?major=' + selectedMajor.value,
  })
}

/**
 * 切换题库收藏状态
 */
const toggleBookmark = async (bank: QuestionBankVO) => {
  try {
    const res = await toggleQuestionBankBookmark({
      params: {
        bankId: bank.id,
        isBookmarked: !bank.isBookmarked,
      },
    })

    if (res.code === 200) {
      bank.isBookmarked = res.data.isBookmarked

      // 更新本地存储
      let bookmarkedIds = uni.getStorageSync(LEARNING_STORAGE_KEYS.BOOKMARKED_BANKS) || []
      if (bank.isBookmarked) {
        bookmarkedIds.push(bank.id)
      } else {
        bookmarkedIds = bookmarkedIds.filter((id) => id !== bank.id)
      }
      uni.setStorageSync(LEARNING_STORAGE_KEYS.BOOKMARKED_BANKS, bookmarkedIds)

      uni.showToast({
        title: bank.isBookmarked
          ? LEARNING_NOTIFICATION_MESSAGES.SUCCESS.BOOKMARK_ADDED
          : LEARNING_NOTIFICATION_MESSAGES.SUCCESS.BOOKMARK_REMOVED,
        icon: 'success',
        duration: 1500,
      })
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    uni.showToast({
      title: LEARNING_NOTIFICATION_MESSAGES.ERROR.BOOKMARK_FAILED,
      icon: 'error',
      duration: 1500,
    })
  }
}

/**
 * 获取动画延迟
 */
const getAnimationDelay = (index) => {
  return `${index * 0.1}s`
}

/**
 * 页面初始化
 */
const initPage = async () => {
  await nextTick()
  setTimeout(() => {
    isPageLoaded.value = true
  }, 100)

  setTimeout(() => {
    isProgressAnimated.value = true
  }, 800)
}

/**
 * 页面数据初始化
 */
const initializePageData = async () => {
  try {
    // 从本地存储读取专业选择，然后加载题库数据
    loadSelectedMajor()
    loadingType.value = 'normal' // 设置加载类型为普通加载
    await loadQuestionBanks()
  } catch (error) {
    console.error('页面数据初始化失败:', error)
    uni.showToast({
      title: '页面加载失败，请重试',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    // 关闭初始化加载状态
    isInitialLoading.value = false
  }
}

// 页面加载时执行初始化
onMounted(async () => {
  // 启动页面UI初始化动画
  initPage()

  // 加载页面数据
  await initializePageData()
})
</script>

<template>
  <view class="resources-container">

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
    <HeadBar title="学习资源" />
        <view v-if="!isInitialLoading" class="content-wrapper fade-in">
        <!-- 头部欢迎区域 -->
        <view class="welcome-section">
          <view class="welcome-content">
            <text class="welcome-title">学习资源中心</text>
            <text class="welcome-subtitle">精心整理的专业题库，助力面试成功</text>
          </view>
          <view class="welcome-decoration">
            <view class="i-mdi-book-open-page-variant decoration-icon"></view>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-section">
          <view class="search-box">
            <view class="i-mdi-magnify search-icon"></view>
            <input
              type="text"
              class="search-input"
              placeholder="搜索题目、书籍或知识点..."
              v-model="searchQuery"
              @input="handleSearch"
            />
            <button v-if="searchQuery" class="clear-btn" @click="clearSearch">
              <view class="i-mdi-close clear-icon"></view>
            </button>
          </view>
        </view>

        <!-- 快速筛选标签 -->
        <CategoryFilter
          class="category-filter"
          title="快速筛选"
          :category-options="filterOptions"
          :selected-category="selectedFilter"
          max-width="100%"
          :show-icon="true"
          @change="handleFilterChange"
          @reset="handleFilterReset"
        />

        <!-- 专业题库 -->
        <view class="question-banks-section">
          <view class="section-header">
            <view class="section-title-wrapper">
              <text class="section-title">专业题库</text>
              <text class="section-subtitle">根据你的专业精心筛选的题库内容</text>
            </view>
            <button class="view-all-btn" @click="showAllQuestionBanks">
              <text class="view-all-text">查看全部</text>
              <view class="i-mdi-arrow-right view-all-icon"></view>
            </button>
          </view>

          <Loading
            v-if="isLoading"
            :visible="isLoading"
            :text="getLoadingText"
            size="medium"
            type="spinner"
          />

          <!-- 题库列表 -->
          <view v-else-if="currentBanks.length > 0" class="banks-list">
            <view
              v-for="(bank, index) in currentBanks"
              :key="bank.id"
              class="bank-card"
              :style="{ animationDelay: getAnimationDelay(index) }"
              @click="openQuestionBankDetail(bank.id)"
            >
              <view class="bank-header">
                <view class="bank-info">
                  <view class="bank-icon">
                    <view :class="bank.icon" class="icon-element"></view>
                  </view>
                  <view class="bank-details">
                    <text class="bank-title">{{ bank.title }}</text>
                    <view class="bank-meta">
                      <view class="difficulty-tag" :class="getDifficultyStyle(bank.difficulty)">
                        <text class="difficulty-text">{{ bank.difficulty }}</text>
                      </view>
                      <text class="question-count">{{ bank.totalQuestions }}题</text>
                    </view>
                  </view>
                </view>
              </view>

              <text class="bank-description">{{ bank.description }}</text>

              <view class="bank-stats">
                <view class="stats-info">
                  <view class="stat-item-small">
                    <view class="i-mdi-book-open-variant stat-icon"></view>
                    <text class="stat-text">{{ bank.totalQuestions }} 题</text>
                  </view>
                  <view class="stat-item-small">
                    <view class="i-mdi-account-group stat-icon"></view>
                    <text class="stat-text">
                      {{ (bank.practiceCount / 1000).toFixed(1) }}k 人练习
                    </text>
                  </view>
                </view>
              </view>

              <view class="categories-section">
                <view v-for="category in bank.categories" :key="category" class="category-tag">
                  <text class="category-text">{{ category }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <view class="i-mdi-file-search-outline empty-icon"></view>
            <text class="empty-title">暂无匹配的题库</text>
            <text class="empty-desc">尝试调整筛选条件或切换其他专业</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载状态 -->
    <LoadingCard :visible="isInitialLoading" :text="'正在加载页面...'" />
  </view>
</template>

<style scoped lang="scss">
.resources-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

// 主要内容区域
.main-content {
  flex: 1;
  background: transparent;
}

.content-wrapper {
  padding: 0 20rpx 160rpx;

  &.fade-in {
    animation: fadeIn 0.6s ease-out;
  }
}

// 欢迎区域
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  margin: 20rpx 0 32rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);

  .welcome-content {
    flex: 1;

    .welcome-title {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: white;
    }

    .welcome-subtitle {
      font-size: 24rpx;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .welcome-decoration {
    .decoration-icon {
      font-size: 64rpx;
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 搜索框
.search-section {
  margin-bottom: 32rpx;

  .search-box {
    display: flex;
    align-items: center;
    padding: 24rpx 28rpx;
    background: white;
    border: 2rpx solid #e2e8f0;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #00c9a7;
      box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.15);
    }

    .search-icon {
      margin-right: 16rpx;
      font-size: 32rpx;
      color: #64748b;
    }

    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #1e293b;
      background: transparent;
      border: none;
      outline: none;

      &::placeholder {
        color: #94a3b8;
      }
    }

    .clear-btn {
      padding: 8rpx;
      background: transparent;
      border: none;

      .clear-icon {
        font-size: 28rpx;
        color: #94a3b8;
      }
    }
  }
}
// 分类筛选
.category-filter {
  margin-bottom: 28rpx;
}

// 题库区域
.question-banks-section {
  .section-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .section-title-wrapper {
      flex: 1;

      .section-title {
        display: block;
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 700;
        color: #1e293b;
      }

      .section-subtitle {
        font-size: 24rpx;
        line-height: 1.4;
        color: #64748b;
      }
    }

    .view-all-btn {
      display: flex;
      gap: 8rpx;
      align-items: center;
      padding: 16rpx 24rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: #00c9a7;
      background: rgba(0, 201, 167, 0.1);
      border: 1rpx solid rgba(0, 201, 167, 0.2);
      border-radius: 20rpx;
      transition: all 0.2s ease;

      &:active {
        background: rgba(0, 201, 167, 0.15);
        transform: scale(0.98);
      }

      .view-all-icon {
        font-size: 20rpx;
      }
    }
  }

  .banks-list {
    .bank-card {
      margin-bottom: 24rpx;
      overflow: hidden;
      background: white;
      border: 2rpx solid #f1f5f9;
      border-radius: 20rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      opacity: 0;
      transition: all 0.3s ease;
      transform: translateY(30rpx);
      animation: slideInUp 0.6s ease-out forwards;
      &:active {
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
        transform: translateY(-4rpx);
      }
      .bank-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 28rpx;
        padding-bottom: 20rpx;
        .bank-info {
          display: flex;
          flex: 1;
          align-items: center;
          .bank-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80rpx;
            height: 80rpx;
            margin-right: 24rpx;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20rpx;
            .icon-element {
              font-size: 36rpx;
              width: 36rpx;
              height: 36rpx;
              color: #00c9a7;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
          .bank-details {
            flex: 1;
            .bank-title {
              display: block;
              margin-bottom: 12rpx;
              font-size: 28rpx;
              font-weight: 600;
              line-height: 1.3;
              color: #1e293b;
            }
            .bank-meta {
              display: flex;
              gap: 16rpx;
              align-items: center;

              .difficulty-tag {
                padding: 6rpx 16rpx;
                font-size: 20rpx;
                font-weight: 500;
                border-radius: 12rpx;

                &.bg-green-100 {
                  color: #16a34a;
                  background: #dcfce7;
                }

                &.bg-yellow-100 {
                  color: #d97706;
                  background: #fef3c7;
                }

                &.bg-red-100 {
                  color: #dc2626;
                  background: #fee2e2;
                }
              }

              .question-count {
                font-size: 22rpx;
                color: #64748b;
              }
            }
          }
        }
      }

      .bank-description {
        padding: 0 28rpx;
        margin-bottom: 24rpx;
        font-size: 24rpx;
        line-height: 1.6;
        color: #64748b;
      }

      .bank-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 28rpx;
        background: #f8fafc;
        border-top: 1rpx solid #e2e8f0;

        .stats-info {
          display: flex;
          gap: 32rpx;

          .stat-item-small {
            display: flex;
            gap: 8rpx;
            align-items: center;

            .stat-icon {
              font-size: 24rpx;
              color: #00c9a7;
            }

            .stat-text {
              font-size: 22rpx;
              color: #64748b;
            }
          }
        }

        .progress-info {
          display: flex;
          gap: 12rpx;
          align-items: center;

          .progress-bar-small {
            width: 100rpx;
            height: 12rpx;
            overflow: hidden;
            background: #e2e8f0;
            border-radius: 6rpx;

            .progress-fill-small {
              height: 100%;
              background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
              border-radius: 6rpx;
              transition: width 1s ease-out;
            }
          }

          .progress-text {
            font-size: 22rpx;
            font-weight: 500;
            color: #00c9a7;
          }
        }
      }

      .categories-section {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        align-items: center;
        padding: 20rpx 28rpx 28rpx;

        .category-tag {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12rpx 16rpx;
          min-height: 24rpx;
          background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
          border: 1rpx solid #a7f3d0;
          border-radius: 24rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.1);
          transition: all 0.2s ease;

          &:active {
            transform: scale(0.98);
            background: linear-gradient(135deg, #dcfce7 0%, #d1fae5 100%);
          }

          .category-text {
            font-size: 18rpx;
            font-weight: 400;
            color: #059669;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
          }
        }

        .more-categories {
          padding: 8rpx 16rpx;
          background: #e2e8f0;
          border-radius: 16rpx;

          .more-text {
            font-size: 20rpx;
            color: #64748b;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      margin-bottom: 32rpx;
      font-size: 120rpx;
      color: #cbd5e1;
    }

    .empty-title {
      margin-bottom: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #475569;
    }

    .empty-desc {
      margin-bottom: 40rpx;
      font-size: 24rpx;
      line-height: 1.5;
      color: #64748b;
      text-align: center;
    }

    .empty-action-btn {
      display: flex;
      gap: 12rpx;
      align-items: center;
      padding: 20rpx 32rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: white;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border: none;
      border-radius: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
