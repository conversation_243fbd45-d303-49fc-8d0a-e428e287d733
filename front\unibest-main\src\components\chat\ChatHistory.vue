<template>
  <view class="history-dialog" :class="{ active: show }" @click.stop="$emit('close')">
    <view class="history-content" @click.stop>
      <view class="history-header">
        <view class="i-carbon-time text-20px mr-8px"></view>
        <text class="history-title">历史记录</text>
        <view class="flex-1"></view>
        <view class="history-actions">
          <view class="header-btn" @click="toggleSearch">
            <view class="i-carbon-search text-18px"></view>
          </view>
          <view class="header-btn" @click="$emit('clear-all')">
            <view class="i-carbon-trash-can text-18px"></view>
          </view>
          <view class="header-btn" @click="$emit('close')">
            <view class="i-carbon-close text-18px"></view>
          </view>
        </view>
      </view>

      <!-- 搜索框 -->
      <view v-if="showSearchInput" class="search-container">
        <view class="search-input-wrapper">
          <view class="i-carbon-search text-16px search-icon"></view>
          <input class="search-input" type="text" placeholder="搜索历史记录..." 
            v-model="searchKeyword" @input="handleSearch" />
          <view v-if="searchKeyword" class="clear-search" @click="clearSearch">
            <view class="i-carbon-close text-14px"></view>
          </view>
        </view>
      </view>

      <view class="history-body">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载历史记录中...</text>
        </view>

        <!-- 空状态 -->
        <view v-else-if="sessionCount === 0" class="empty-state">
          <view class="empty-icon">
            <view class="i-carbon-chat text-48px"></view>
          </view>
          <text class="empty-text">
            {{ searchKeyword ? '没有找到相关记录' : '暂无历史记录' }}
          </text>
          <text class="empty-desc">
            {{ searchKeyword ? '尝试使用其他关键词搜索' : '开始新的对话来创建历史记录' }}
          </text>
        </view>

        <!-- 历史记录列表 -->
        <scroll-view v-else class="history-list" scroll-y>
          <view v-for="(group, date) in filteredSessions" :key="date" class="date-group">
            <view class="date-header">{{ date }}</view>
            <view class="session-group">
              <view v-for="session in group" :key="session.id" class="history-item"
                :class="{ active: session.id === currentSessionId }" 
                @click="$emit('load-session', session)">
                <view class="session-main">
                  <view class="session-header">
                    <view class="session-title" v-html="highlightKeyword(session.title)"></view>
                    <view class="session-time">{{ formatTime(session.updatedAt) }}</view>
                  </view>
                  <view class="session-preview">
                    <text class="preview-text" v-html="highlightKeyword(getPreview(session))"></text>
                  </view>
                  <view class="session-meta">
                    <view class="message-count">
                      <view class="i-carbon-chat text-12px mr-4px"></view>
                      <text>{{ session.messages?.length || 0 }} 条消息</text>
                    </view>
                  </view>
                </view>
                <view class="session-actions">
                  <view class="action-icon" @click.stop="$emit('delete-session', session.id)">
                    <view class="i-carbon-trash-can text-16px"></view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Session {
  id: string
  title: string
  messages: any[]
  updatedAt: number
  createdAt: number
}

const props = defineProps<{
  show: boolean
  sessions: Session[]
  loading: boolean
  currentSessionId: string
}>()

defineEmits<{
  close: []
  'clear-all': []
  'load-session': [session: Session]
  'delete-session': [sessionId: string]
}>()

const showSearchInput = ref(false)
const searchKeyword = ref('')

const sessionCount = computed(() => {
  return searchKeyword.value ? Object.values(filteredSessions.value).flat().length : props.sessions.length
})

// 按日期分组
const groupedSessions = computed(() => {
  const groups: Record<string, Session[]> = {}
  
  props.sessions.forEach(session => {
    const date = formatDate(session.updatedAt)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(session)
  })

  // 排序
  const sortedGroups: Record<string, Session[]> = {}
  Object.keys(groups)
    .sort((a, b) => getDateValue(b) - getDateValue(a))
    .forEach(date => {
      sortedGroups[date] = groups[date].sort((a, b) => b.updatedAt - a.updatedAt)
    })

  return sortedGroups
})

// 过滤后的会话
const filteredSessions = computed(() => {
  if (!searchKeyword.value.trim()) {
    return groupedSessions.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  const filtered: Record<string, Session[]> = {}

  Object.entries(groupedSessions.value).forEach(([date, sessions]) => {
    const matchedSessions = sessions.filter(session => {
      return session.title.toLowerCase().includes(keyword) ||
             getPreview(session).toLowerCase().includes(keyword)
    })

    if (matchedSessions.length > 0) {
      filtered[date] = matchedSessions
    }
  })

  return filtered
})

const toggleSearch = () => {
  showSearchInput.value = !showSearchInput.value
  if (!showSearchInput.value) {
    searchKeyword.value = ''
  }
}

const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const clearSearch = () => {
  searchKeyword.value = ''
}

const highlightKeyword = (text: string): string => {
  if (!searchKeyword.value.trim() || !text) {
    return text
  }

  const keyword = searchKeyword.value.trim()
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="search-highlight">$1</span>')
}

const formatDate = (timestamp: number): string => {
  const now = new Date()
  const date = new Date(timestamp)
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  if (targetDate.getTime() === today.getTime()) {
    return '今天'
  } else if (targetDate.getTime() === yesterday.getTime()) {
    return '昨天'
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

const getDateValue = (dateStr: string): number => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  
  switch (dateStr) {
    case '今天':
      return today.getTime()
    case '昨天':
      return today.getTime() - 24 * 60 * 60 * 1000
    default:
      const match = dateStr.match(/(\d+)月(\d+)日/)
      if (match) {
        const month = parseInt(match[1]) - 1
        const day = parseInt(match[2])
        return new Date(now.getFullYear(), month, day).getTime()
      }
      return 0
  }
}

const getPreview = (session: Session): string => {
  const userMessages = session.messages?.filter(msg => msg.role === 'user') || []
  if (userMessages.length === 0) {
    return '暂无对话内容'
  }

  const lastMessage = userMessages[userMessages.length - 1]
  const preview = lastMessage.content.replace(/\n/g, ' ').trim()
  return preview.length > 50 ? preview.substring(0, 50) + '...' : preview
}
</script>

<style lang="scss" scoped>
.history-dialog {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: rgba(0, 0, 0, 0);
  transition: all 0.3s;

  &.active {
    pointer-events: auto;
    background: rgba(0, 0, 0, 0.5);
  }
}

.history-content {
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 800rpx;
  height: 80%;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s;

  .history-dialog.active & {
    transform: scale(1);
    opacity: 1;
  }
}

.history-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #00c49a 100%);
  color: white;
  border-radius: 24rpx 24rpx 0 0;

  .history-title {
    font-size: 36rpx;
    font-weight: 600;
  }

  .history-actions {
    display: flex;
    gap: 16rpx;

    .header-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64rpx;
      height: 64rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transition: all 0.3s;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.9);
      }
    }
  }
}

.search-container {
  padding: 24rpx;
  border-bottom: 1px solid #f0f0f0;

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 0 32rpx;

    .search-icon {
      color: #999;
      margin-right: 16rpx;
    }

    .search-input {
      flex: 1;
      height: 80rpx;
      font-size: 28rpx;
      background: transparent;
      border: none;
      outline: none;
    }

    .clear-search {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: #ddd;
      border-radius: 50%;
      color: white;

      &:active {
        background: #ccc;
      }
    }
  }
}

.history-body {
  flex: 1;
  overflow: hidden;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f0f0f0;
    border-top: 4rpx solid #00c9a7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 24rpx;
  }

  .empty-icon {
    margin-bottom: 24rpx;
    color: #ddd;
  }

  .loading-text,
  .empty-text {
    font-size: 32rpx;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 24rpx;
    color: #ccc;
  }
}

.history-list {
  height: 100%;
  padding: 24rpx;
}

.date-group {
  margin-bottom: 32rpx;

  .date-header {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 16rpx;
    padding-left: 16rpx;
  }
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s;

  &.active {
    background: linear-gradient(135deg, #e8f8f5 0%, #d4f4ed 100%);
  }

  &:active {
    transform: scale(0.98);
  }

  .session-main {
    flex: 1;
    min-width: 0;

    .session-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8rpx;

      .session-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .session-time {
        font-size: 24rpx;
        color: #999;
        flex-shrink: 0;
        margin-left: 16rpx;
      }
    }

    .session-preview {
      margin-bottom: 8rpx;

      .preview-text {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .session-meta {
      display: flex;
      align-items: center;

      .message-count {
        display: flex;
        align-items: center;
        font-size: 20rpx;
        color: #999;
      }
    }
  }

  .session-actions {
    margin-left: 16rpx;

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64rpx;
      height: 64rpx;
      color: #999;
      border-radius: 50%;
      transition: all 0.3s;

      &:active {
        background: #eee;
        color: #ff4444;
      }
    }
  }
}

:deep(.search-highlight) {
  background: #fff3cd;
  color: #856404;
  padding: 0 4rpx;
  border-radius: 4rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
