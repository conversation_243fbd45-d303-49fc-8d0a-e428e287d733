<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import HeadBar from '@/components/HeadBar.vue'
import { getUserFeedbackList, getFeedbackStats, getFeedbackDetail } from '@/service/feedback'
import { formatTime } from '@/utils/time'
import type {
  FeedbackDetail,
  FeedbackListParams,
  FeedbackStats,
  FeedbackStatus,
} from '@/types/feedback'

// 导入演示数据系统
import { initDemoDataSystem } from '@/utils/demoData'
import { registerFeedbackDemoData, API_PATHS } from '@/utils/demoData/feedback'
import { demoDataManager } from '@/utils/demoData/DemoDataManager'

// 初始化演示数据系统
initDemoDataSystem({ enableDemoData: true })
// 注册反馈演示数据
registerFeedbackDemoData()

/**
 * @description 反馈列表页面
 * 用户可以查看自己提交的所有反馈记录和详细信息
 */

// 页面加载状态
const isLoading = ref(false)
const isRefreshing = ref(false)
const isLoadingMore = ref(false)

// 反馈列表数据
const feedbackList = ref<FeedbackDetail[]>([])

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  hasMore: true,
})

// 查询参数
const queryParams = reactive<FeedbackListParams>({
  pageNum: 1,
  pageSize: 10,
  type: '',
  status: undefined,
})

// 统计数据
const stats = ref<FeedbackStats>({
  totalCount: 0,
  pendingCount: 0,
  processingCount: 0,
  resolvedCount: 0,
  typeStats: {},
})

// 筛选选项
const filterOptions = reactive({
  showFilter: false,
  types: [
    { label: '全部类型', value: '' },
    { label: '功能建议', value: '功能建议' },
    { label: '内容问题', value: '内容问题' },
    { label: '使用问题', value: '使用问题' },
    { label: '其他', value: '其他' },
  ],
  statuses: [
    { label: '全部状态', value: undefined },
    { label: '待处理', value: 'PENDING' as FeedbackStatus },
    { label: '处理中', value: 'PROCESSING' as FeedbackStatus },
    { label: '已解决', value: 'RESOLVED' as FeedbackStatus },
    { label: '已关闭', value: 'CLOSED' as FeedbackStatus },
  ],
})

// 选中的反馈详情
const selectedFeedback = ref<FeedbackDetail | null>(null)
const showDetailModal = ref(false)

/**
 * @description 获取状态显示文本
 * @param status 状态值
 * @returns 显示文本
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    PENDING: '待处理',
    PROCESSING: '处理中',
    RESOLVED: '已解决',
    CLOSED: '已关闭',
  }
  return statusMap[status] || status
}

/**
 * @description 获取状态样式类名
 * @param status 状态值
 * @returns 样式类名
 */
const getStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    PENDING: 'status-pending',
    PROCESSING: 'status-processing',
    RESOLVED: 'status-resolved',
    CLOSED: 'status-closed',
  }
  return classMap[status] || 'status-default'
}

/**
 * @description 获取类型图标
 * @param type 反馈类型
 * @returns 图标类名
 */
const getTypeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    功能建议: 'i-fa-solid-lightbulb',
    内容问题: 'i-fa-solid-exclamation-triangle',
    使用问题: 'i-fa-solid-question-circle',
    其他: 'i-fa-solid-comment-dots',
  }
  return iconMap[type] || 'i-fa-solid-comment'
}

/**
 * @description 加载反馈列表
 * @param isRefresh 是否为刷新操作
 */
const loadFeedbackList = async (isRefresh = false) => {
  if (isRefresh) {
    isRefreshing.value = true
    queryParams.pageNum = 1
    pagination.pageNum = 1
  } else if (queryParams.pageNum > 1) {
    isLoadingMore.value = true
  } else {
    isLoading.value = true
  }

  try {
    let res;
    
    try {
      // 尝试调用真实API
      res = await getUserFeedbackList(queryParams)
    } catch (apiError) {
      console.error('API调用失败，使用演示数据:', apiError)
      
      // API调用失败，使用演示数据
      res = demoDataManager.getDemoData(API_PATHS.GET_FEEDBACK_LIST, queryParams)
    }

    if (res.code === 200) {
      if (isRefresh || queryParams.pageNum === 1) {
        feedbackList.value = res.rows
      } else {
        feedbackList.value.push(...res.rows)
      }
      pagination.total = res.total
      pagination.hasMore = res.rows.length === queryParams.pageSize
    } else {
      uni.showToast({
        title: res.message || '获取反馈列表失败',
        icon: 'none',
      })
    }
  } catch (error: any) {
    console.error('获取反馈列表失败:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none',
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
    isLoadingMore.value = false
  }
}

/**
 * @description 加载统计数据
 */
const loadStats = async () => {
  try {
    let res;
    
    try {
      // 尝试调用真实API
      res = await getFeedbackStats()
    } catch (apiError) {
      console.error('API调用失败，使用演示数据:', apiError)
      
      // API调用失败，使用演示数据
      res = demoDataManager.getDemoData(API_PATHS.GET_FEEDBACK_STATS)
    }
    
    if (res.code === 200) {
      stats.value = res.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

/**
 * @description 刷新数据
 */
const handleRefresh = () => {
  loadFeedbackList(true)
  loadStats()
}

/**
 * @description 加载更多
 */
const handleLoadMore = () => {
  if (pagination.hasMore && !isLoadingMore.value) {
    queryParams.pageNum++
    pagination.pageNum++
    loadFeedbackList()
  }
}

/**
 * @description 切换筛选面板
 */
const toggleFilter = () => {
  filterOptions.showFilter = !filterOptions.showFilter
}

/**
 * @description 应用筛选条件
 */
const applyFilter = () => {
  queryParams.pageNum = 1
  pagination.pageNum = 1
  filterOptions.showFilter = false
  loadFeedbackList()
}

/**
 * @description 重置筛选条件
 */
const resetFilter = () => {
  queryParams.type = ''
  queryParams.status = undefined
  applyFilter()
}

/**
 * @description 查看反馈详情
 * @param feedback 反馈项
 */
const viewDetail = async (feedback: FeedbackDetail) => {
  try {
    let res;
    
    try {
      // 尝试调用真实API
      res = await getFeedbackDetail(feedback.id)
    } catch (apiError) {
      console.error('API调用失败，使用演示数据:', apiError)
      
      // API调用失败，使用演示数据
      res = demoDataManager.getDemoData(API_PATHS.GET_FEEDBACK_DETAIL, { id: feedback.id })
    }
    
    if (res.code === 200) {
      selectedFeedback.value = res.data
      showDetailModal.value = true
    } else {
      uni.showToast({
        title: res.message || '获取详情失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取反馈详情失败:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * @description 关闭详情弹窗
 */
const closeDetail = () => {
  showDetailModal.value = false
  selectedFeedback.value = null
}

/**
 * @description 去提交新反馈
 */
const goToSubmit = () => {
  uni.navigateBack()
}

// 页面加载时获取数据
onMounted(() => {
  loadFeedbackList()
  loadStats()
})
</script>

<template>
  <view class="feedback-list-page">
    <HeadBar
      title="我的反馈"
      :show-back="true"
      :show-right-button="true"
      :right-text="'筛选'"
      :right-icon="'i-fa-solid-filter'"
      @right-click="toggleFilter"
    />

    <!-- 统计卡片 -->
    <view class="stats-section" style="margin-top: 80rpx">
      <view class="stats-card">
        <view class="stats-header">
          <text class="i-fa-solid-chart-bar stats-icon"></text>
          <text class="stats-title">反馈统计</text>
        </view>
        <view class="stats-content">
          <view class="stat-item">
            <text class="stat-number">{{ stats.totalCount }}</text>
            <text class="stat-label">总反馈</text>
          </view>
          <view class="stat-item pending">
            <text class="stat-number">{{ stats.pendingCount }}</text>
            <text class="stat-label">待处理</text>
          </view>
          <view class="stat-item processing">
            <text class="stat-number">{{ stats.processingCount }}</text>
            <text class="stat-label">处理中</text>
          </view>
          <view class="stat-item resolved">
            <text class="stat-number">{{ stats.resolvedCount }}</text>
            <text class="stat-label">已解决</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选面板 -->
    <transition name="filter-slide">
      <view v-if="filterOptions.showFilter" class="filter-panel" @click="toggleFilter">
        <view class="filter-content" @click.stop>
          <view class="filter-header">
            <text class="filter-title">筛选条件</text>
            <text class="i-fa-solid-times close-icon" @click="toggleFilter"></text>
          </view>

          <view class="filter-section">
            <text class="filter-label">反馈类型</text>
            <view class="filter-options">
              <view
                v-for="type in filterOptions.types"
                :key="type.value"
                class="filter-option"
                :class="{ active: queryParams.type === type.value }"
                @click="queryParams.type = type.value"
              >
                {{ type.label }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <text class="filter-label">处理状态</text>
            <view class="filter-options">
              <view
                v-for="status in filterOptions.statuses"
                :key="status.value || 'all'"
                class="filter-option"
                :class="[{ active: queryParams.status === status.value }]"
                @click="queryParams.status = status.value"
              >
                {{ status.label }}
              </view>
            </view>
          </view>

          <view class="filter-actions">
            <button class="reset-btn" @click="resetFilter">重置</button>
            <button class="apply-btn" @click="applyFilter">应用</button>
          </view>
        </view>
      </view>
    </transition>

    <!-- 反馈列表 -->
    <view class="list-section">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-container">
        <text class="i-fa-solid-spinner loading-icon"></text>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="feedbackList.length === 0" class="empty-container">
        <text class="i-fa-solid-comment-slash empty-icon"></text>
        <text class="empty-text">暂无反馈记录</text>
        <button class="submit-btn" @click="goToSubmit">去提交反馈</button>
      </view>

      <!-- 反馈列表 -->
      <view v-else class="feedback-list">
        <view
          v-for="feedback in feedbackList"
          :key="feedback.id"
          class="feedback-item"
          @click="viewDetail(feedback)"
        >
          <view class="feedback-header">
            <view class="type-info">
              <text :class="getTypeIcon(feedback.type)" class="type-icon"></text>
              <text class="type-text">{{ feedback.type }}</text>
            </view>
            <view class="status-badge" :class="getStatusClass(feedback.status)">
              {{ getStatusText(feedback.status) }}
            </view>
          </view>

          <view class="feedback-content">
            <text class="content-text">{{ feedback.content }}</text>
          </view>

          <view class="feedback-footer">
            <text class="time-text">{{ formatTime(feedback.createTime) }}</text>
            <text class="i-fa-solid-chevron-right arrow-icon"></text>
          </view>

          <!-- 处理回复 -->
          <view v-if="feedback.reply" class="reply-section">
            <text class="reply-label">处理回复：</text>
            <text class="reply-text">{{ feedback.reply }}</text>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="pagination.hasMore" class="load-more" @click="handleLoadMore">
          <text v-if="isLoadingMore" class="i-fa-solid-spinner loading-icon"></text>
          <text>{{ isLoadingMore ? '加载中...' : '点击加载更多' }}</text>
        </view>
      </view>
    </view>

    <!-- 详情弹窗 -->
    <transition name="modal-fade">
      <view v-if="showDetailModal" class="detail-modal" @click="closeDetail">
        <transition name="modal-slide">
          <view v-if="showDetailModal" class="detail-content" @click.stop>
            <view class="detail-header">
              <text class="detail-title">反馈详情</text>
              <text class="i-fa-solid-times close-icon" @click="closeDetail"></text>
            </view>

            <view v-if="selectedFeedback" class="detail-body">
              <view class="detail-item">
                <text class="detail-label">反馈类型</text>
                <view class="detail-value type-value">
                  <text :class="getTypeIcon(selectedFeedback.type)" class="type-icon"></text>
                  <text>{{ selectedFeedback.type }}</text>
                </view>
              </view>

              <view class="detail-item">
                <text class="detail-label">处理状态</text>
                <view class="detail-value">
                  <view class="status-badge" :class="getStatusClass(selectedFeedback.status)">
                    {{ getStatusText(selectedFeedback.status) }}
                  </view>
                </view>
              </view>

              <view class="detail-item">
                <text class="detail-label">反馈内容</text>
                <text class="detail-value content-value">{{ selectedFeedback.content }}</text>
              </view>

              <view v-if="selectedFeedback.contactInfo" class="detail-item">
                <text class="detail-label">联系方式</text>
                <text class="detail-value">{{ selectedFeedback.contactInfo }}</text>
              </view>

              <view class="detail-item">
                <text class="detail-label">提交时间</text>
                <text class="detail-value">
                  {{ new Date(selectedFeedback.createTime).toLocaleString() }}
                </text>
              </view>

              <view v-if="selectedFeedback.reply" class="detail-item">
                <text class="detail-label">处理回复</text>
                <text class="detail-value reply-value">{{ selectedFeedback.reply }}</text>
              </view>

              <view v-if="selectedFeedback.handler" class="detail-item">
                <text class="detail-label">处理人</text>
                <text class="detail-value">{{ selectedFeedback.handler }}</text>
              </view>

              <view class="detail-item">
                <text class="detail-label">设备信息</text>
                <text class="detail-value">{{ selectedFeedback.deviceInfo || '未知' }}</text>
              </view>
            </view>
          </view>
        </transition>
      </view>
    </transition>
  </view>
</template>

<style scoped lang="scss">
.feedback-list-page {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background: #f7f9fc;
}

// 统计卡片
.stats-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.stats-card {
  padding: 32rpx;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;

  .stats-icon {
    margin-right: 12rpx;
    font-size: 32rpx;
  }

  .stats-title {
    font-size: 28rpx;
    font-weight: 600;
  }
}

.stats-content {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;

  .stat-number {
    display: block;
    margin-bottom: 8rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .stat-label {
    font-size: 22rpx;
    opacity: 0.9;
  }

  &.pending .stat-number {
    color: #ffd93d;
  }

  &.processing .stat-number {
    color: #74b9ff;
  }

  &.resolved .stat-number {
    color: #6c5ce7;
  }
}

// 筛选面板
.filter-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  background: rgba(0, 0, 0, 0.5);
}

.filter-content {
  width: 600rpx;
  height: 100%;
  background: #fff;
  box-shadow: -4rpx 0 20rpx rgba(0, 0, 0, 0.1);
}

// 筛选面板动画
.filter-slide-enter-active,
.filter-slide-leave-active {
  transition: all 0.3s ease;
}

.filter-slide-enter-active .filter-content,
.filter-slide-leave-active .filter-content {
  transition: transform 0.3s ease;
}

.filter-slide-enter-from {
  background: rgba(0, 0, 0, 0);
}

.filter-slide-enter-from .filter-content {
  transform: translateX(100%);
}

.filter-slide-leave-to {
  background: rgba(0, 0, 0, 0);
}

.filter-slide-leave-to .filter-content {
  transform: translateX(100%);
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 32rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .filter-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #222;
  }

  .close-icon {
    padding: 16rpx;
    font-size: 24rpx;
    color: #999;
  }
}

.filter-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .filter-label {
    display: block;
    margin-bottom: 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #222;
  }
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 32rpx;
  transition: all 0.3s ease;

  &.active {
    color: #fff;
    background: #00c9a7;
  }
}

.filter-actions {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;

  button {
    flex: 1;
    height: 80rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    border-radius: 16rpx;
  }

  .reset-btn {
    color: #666;
    background: #f5f5f5;
  }

  .apply-btn {
    color: #fff;
    background: #00c9a7;
  }
}

// 列表区域
.list-section {
  padding: 0 32rpx;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  text-align: center;
}

.loading-icon {
  margin-bottom: 16rpx;
  font-size: 48rpx;
  color: #00c9a7;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

.empty-icon {
  margin-bottom: 24rpx;
  font-size: 120rpx;
  color: #ddd;
}

.empty-text {
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #999;
}

.submit-btn {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  color: #fff;
  background: #00c9a7;
  border: none;
  border-radius: 32rpx;
}

// 反馈列表
.feedback-list {
  .feedback-item {
    padding: 32rpx;
    margin-bottom: 24rpx;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      transform: translateY(2rpx);
    }
  }
}

.feedback-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.type-info {
  display: flex;
  align-items: center;

  .type-icon {
    margin-right: 8rpx;
    font-size: 24rpx;
    color: #00c9a7;
  }

  .type-text {
    font-size: 24rpx;
    color: #666;
  }
}

.status-badge {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 16rpx;

  &.status-pending {
    color: #e17055;
    background: rgba(255, 217, 61, 0.2);
  }

  &.status-processing {
    color: #0984e3;
    background: rgba(116, 185, 255, 0.2);
  }

  &.status-resolved {
    color: #6c5ce7;
    background: rgba(108, 92, 231, 0.2);
  }

  &.status-closed {
    color: #636e72;
    background: rgba(153, 153, 153, 0.2);
  }
}

.feedback-content {
  margin-bottom: 16rpx;

  .content-text {
    display: -webkit-box;
    overflow: hidden;
    font-size: 28rpx;
    line-height: 1.6;
    color: #333;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

.feedback-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .time-text {
    font-size: 22rpx;
    color: #999;
  }

  .arrow-icon {
    font-size: 16rpx;
    color: #ccc;
  }
}

.reply-section {
  padding-top: 16rpx;
  margin-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;

  .reply-label {
    margin-right: 8rpx;
    font-size: 22rpx;
    color: #00c9a7;
  }

  .reply-text {
    font-size: 24rpx;
    line-height: 1.5;
    color: #666;
  }
}

.load-more {
  padding: 32rpx 0;
  font-size: 24rpx;
  color: #999;
  text-align: center;

  .loading-icon {
    margin-right: 8rpx;
    margin-bottom: 0;
  }
}

// 详情弹窗动画
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  background: rgba(0, 0, 0, 0);
}

.modal-slide-enter-active,
.modal-slide-leave-active {
  transition: all 0.3s ease;
}

.modal-slide-enter-from,
.modal-slide-leave-to {
  opacity: 0;
  transform: translateY(50rpx) scale(0.9);
}

// 详情弹窗
.detail-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(0, 0, 0, 0.5);
}

.detail-content {
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
  background: #fff;
  border-radius: 24rpx;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .detail-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #222;
  }

  .close-icon {
    padding: 16rpx;
    font-size: 24rpx;
    color: #999;
  }
}

.detail-body {
  padding: 32rpx;
}

.detail-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .detail-label {
    display: block;
    margin-bottom: 12rpx;
    font-size: 24rpx;
    color: #666;
  }

  .detail-value {
    font-size: 28rpx;
    line-height: 1.6;
    color: #333;

    &.type-value {
      display: flex;
      align-items: center;

      .type-icon {
        margin-right: 8rpx;
        font-size: 24rpx;
        color: #00c9a7;
      }
    }

    &.content-value,
    &.reply-value {
      padding: 16rpx;
      background: #f5f5f5;
      border-radius: 12rpx;
    }

    &.reply-value {
      background: rgba(0, 201, 167, 0.1);
      border-left: 4rpx solid #00c9a7;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
