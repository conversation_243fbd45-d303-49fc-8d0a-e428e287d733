/**
 * @description 演示数据存储
 * 负责存储和管理演示数据，支持内存存储和持久化存储
 */

import { DemoDataStore } from './DemoDataManager'

// 内存存储实现
export class MemoryDemoDataStore implements DemoDataStore {
  private store: Map<string, any> = new Map()

  public get<T>(key: string): T | null {
    return this.store.has(key) ? (this.store.get(key) as T) : null
  }

  public set(key: string, data: any): void {
    this.store.set(key, data)
  }

  public has(key: string): boolean {
    return this.store.has(key)
  }

  public remove(key: string): void {
    this.store.delete(key)
  }

  public clear(): void {
    this.store.clear()
  }
}

// 本地存储实现
export class LocalStorageDemoDataStore implements DemoDataStore {
  private prefix: string

  constructor(prefix: string = 'demo_data_') {
    this.prefix = prefix
  }

  public get<T>(key: string): T | null {
    try {
      const data = uni.getStorageSync(this.getKey(key))
      return data ? (JSON.parse(data) as T) : null
    } catch (error) {
      console.error(`[LocalStorageDemoDataStore] 获取数据失败: ${key}`, error)
      return null
    }
  }

  public set(key: string, data: any): void {
    try {
      uni.setStorageSync(this.getKey(key), JSON.stringify(data))
    } catch (error) {
      console.error(`[LocalStorageDemoDataStore] 设置数据失败: ${key}`, error)
    }
  }

  public has(key: string): boolean {
    try {
      return uni.getStorageSync(this.getKey(key)) !== ''
    } catch (error) {
      console.error(`[LocalStorageDemoDataStore] 检查数据失败: ${key}`, error)
      return false
    }
  }

  public remove(key: string): void {
    try {
      uni.removeStorageSync(this.getKey(key))
    } catch (error) {
      console.error(`[LocalStorageDemoDataStore] 删除数据失败: ${key}`, error)
    }
  }

  public clear(): void {
    try {
      const keys = uni.getStorageInfoSync().keys
      keys.forEach((key) => {
        if (key.startsWith(this.prefix)) {
          uni.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('[LocalStorageDemoDataStore] 清空数据失败', error)
    }
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`
  }
}

// 混合存储实现（内存 + 本地存储）
export class HybridDemoDataStore implements DemoDataStore {
  private memoryStore: MemoryDemoDataStore
  private persistentStore: LocalStorageDemoDataStore
  private persistKeys: Set<string> = new Set()

  constructor(prefix: string = 'demo_data_') {
    this.memoryStore = new MemoryDemoDataStore()
    this.persistentStore = new LocalStorageDemoDataStore(prefix)
  }

  public get<T>(key: string): T | null {
    // 优先从内存中获取
    const memoryData = this.memoryStore.get<T>(key)
    if (memoryData !== null) {
      return memoryData
    }

    // 如果内存中没有，且是需要持久化的键，则从本地存储获取
    if (this.persistKeys.has(key)) {
      const persistentData = this.persistentStore.get<T>(key)
      if (persistentData !== null) {
        // 将数据加载到内存中
        this.memoryStore.set(key, persistentData)
        return persistentData
      }
    }

    return null
  }

  public set(key: string, data: any): void {
    // 保存到内存
    this.memoryStore.set(key, data)

    // 如果是需要持久化的键，则保存到本地存储
    if (this.persistKeys.has(key)) {
      this.persistentStore.set(key, data)
    }
  }

  public has(key: string): boolean {
    // 优先检查内存
    if (this.memoryStore.has(key)) {
      return true
    }

    // 如果内存中没有，且是需要持久化的键，则检查本地存储
    if (this.persistKeys.has(key)) {
      return this.persistentStore.has(key)
    }

    return false
  }

  public remove(key: string): void {
    // 从内存中删除
    this.memoryStore.remove(key)

    // 如果是需要持久化的键，则从本地存储中删除
    if (this.persistKeys.has(key)) {
      this.persistentStore.remove(key)
    }
  }

  public clear(): void {
    // 清空内存
    this.memoryStore.clear()

    // 清空本地存储
    this.persistentStore.clear()
  }

  // 设置需要持久化的键
  public setPersistKey(key: string, persist: boolean = true): void {
    if (persist) {
      this.persistKeys.add(key)

      // 如果内存中有数据，则保存到本地存储
      const data = this.memoryStore.get(key)
      if (data !== null) {
        this.persistentStore.set(key, data)
      }
    } else {
      this.persistKeys.delete(key)
    }
  }

  // 批量设置需要持久化的键
  public setPersistKeys(keys: string[], persist: boolean = true): void {
    keys.forEach((key) => this.setPersistKey(key, persist))
  }
}

// 导出默认存储实例
export const memoryDemoDataStore = new MemoryDemoDataStore()
export const localStorageDemoDataStore = new LocalStorageDemoDataStore()
export const hybridDemoDataStore = new HybridDemoDataStore()

export default hybridDemoDataStore
