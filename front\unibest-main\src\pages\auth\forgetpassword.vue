<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { sendSmsCode, checkPhoneExists, resetPassword } from '@/service/auth'

// 响应式状态
const currentTime = ref('9:41')
const phone = ref('')
const code = ref('')
const newPassword = ref('')
const confirmPassword = ref('')

// 当前步骤：1-手机验证，2-验证码验证，3-设置新密码
const currentStep = ref(1)

const notificationText = ref('')
const notificationType = ref<'success' | 'error' | 'info'>('info')
const notificationVisible = ref(false)
let notificationTimer: any = null

const phoneError = ref('')
const codeError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')

const sendCodeBtnText = ref('获取验证码')
const sendCodeBtnDisabled = ref(false)
let countdownInterval: any = null

// 控制密码可见性
const passwordVisible = ref(false)
const confirmPasswordVisible = ref(false)

const isLoading = ref(false)
const submitBtnText = ref('下一步')
const submitIconVisible = ref(true)

/**
 * 更新时间
 */
function updateTime() {
  const now = new Date()
  currentTime.value = `${now.getHours().toString().padStart(2, '0')}:${now
    .getMinutes()
    .toString()
    .padStart(2, '0')}`
}

/**
 * 验证手机号
 * @param p 手机号
 * @returns 是否有效
 */
function validatePhone(p: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(p)
}

/**
 * 验证验证码
 * @param c 验证码
 * @returns 是否有效
 */
function validateCode(c: string): boolean {
  return c.length === 6 && /^\d{6}$/.test(c)
}

/**
 * 验证密码强度
 * @param p 密码
 * @returns 是否有效
 */
function validatePassword(p: string): boolean {
  // 密码至少8位，包含字母和数字
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/
  return passwordRegex.test(p)
}

/**
 * 显示通知
 * @param message 消息
 * @param type 类型
 * @param duration 持续时间
 */
function showNotification(
  message: string,
  type: 'success' | 'error' | 'info' = 'info',
  duration = 3000,
) {
  notificationText.value = message
  notificationType.value = type
  notificationVisible.value = true

  if (notificationTimer) clearTimeout(notificationTimer)
  notificationTimer = setTimeout(() => {
    notificationVisible.value = false
  }, duration)
}

/**
 * 显示输入错误
 * @param field 字段
 * @param message 消息
 */
function showInputError(field: 'phone' | 'code' | 'password' | 'confirmPassword', message: string) {
  if (field === 'phone') {
    phoneError.value = message
  } else if (field === 'code') {
    codeError.value = message
  } else if (field === 'password') {
    passwordError.value = message
  } else if (field === 'confirmPassword') {
    confirmPasswordError.value = message
  }

  // 3秒后自动清除错误
  setTimeout(() => {
    clearInputError(field)
  }, 3000)
}

/**
 * 清除输入错误
 * @param field 字段
 */
function clearInputError(field: 'phone' | 'code' | 'password' | 'confirmPassword') {
  if (field === 'phone') {
    phoneError.value = ''
  } else if (field === 'code') {
    codeError.value = ''
  } else if (field === 'password') {
    passwordError.value = ''
  } else if (field === 'confirmPassword') {
    confirmPasswordError.value = ''
  }
}

/**
 * 切换密码可见性
 */
function togglePasswordVisibility(type: 'new' | 'confirm') {
  if (type === 'new') {
    passwordVisible.value = !passwordVisible.value
  } else {
    confirmPasswordVisible.value = !confirmPasswordVisible.value
  }
}

/**
 * 发送验证码
 */
async function handleSendCode() {
  if (!phone.value) {
    showInputError('phone', '请输入手机号码')
    return
  }
  if (!validatePhone(phone.value)) {
    showInputError('phone', '请输入正确的手机号码格式')
    return
  }

  sendCodeBtnDisabled.value = true
  let countdown = 60
  sendCodeBtnText.value = `${countdown}s后重发`

  countdownInterval = setInterval(() => {
    countdown--
    if (countdown <= 0) {
      clearInterval(countdownInterval)
      sendCodeBtnText.value = '获取验证码'
      sendCodeBtnDisabled.value = false
    } else {
      sendCodeBtnText.value = `${countdown}s后重发`
    }
  }, 1000)

  try {
    // 调用后端发送验证码接口
    const res = await sendSmsCode({
      params: { phone: phone.value },
    })

    if (res.code === 200) {
      showNotification('验证码已发送到您的手机', 'success')

      // 开发环境下自动填入验证码（生产环境应移除）
      // #ifdef DEV
      setTimeout(() => {
        code.value = '123456'
        showNotification('开发模式：验证码为 123456', 'info')
      }, 500)
      // #endif
    } else {
      showNotification(res.message || '验证码发送失败', 'error')
      clearInterval(countdownInterval)
      sendCodeBtnText.value = '获取验证码'
      sendCodeBtnDisabled.value = false
    }
  } catch (error) {
    showNotification('验证码发送失败，请稍后重试', 'error')
    clearInterval(countdownInterval)
    sendCodeBtnText.value = '获取验证码'
    sendCodeBtnDisabled.value = false
  }
}

/**
 * 处理步骤提交
 */
async function handleSubmit() {
  if (isLoading.value) return

  // 根据当前步骤处理
  if (currentStep.value === 1) {
    // 第一步：验证手机号
    clearInputError('phone')

    if (!phone.value) {
      showInputError('phone', '请输入手机号码')
      return
    }
    if (!validatePhone(phone.value)) {
      showInputError('phone', '请输入正确的手机号码格式')
      return
    }

    setLoadingState(true)

    try {
      // 检查手机号是否存在
      const res = await checkPhoneExists({
        params: { phone: phone.value },
      })

      if (res.code === 200) {
        if (res.data?.exists) {
          // 手机号存在，发送验证码
          await handleSendCode()
          currentStep.value = 2
          submitBtnText.value = '验证'
        } else {
          showNotification('该手机号未注册，请先注册账号', 'error')
        }
      } else {
        showNotification(res.message || '验证失败，请稍后再试', 'error')
      }
    } catch (error) {
      showNotification('验证失败，请稍后再试', 'error')
    } finally {
      setLoadingState(false)
    }
  } else if (currentStep.value === 2) {
    // 第二步：验证验证码
    clearInputError('code')

    if (!code.value) {
      showInputError('code', '请输入验证码')
      return
    }
    if (!validateCode(code.value)) {
      showInputError('code', '请输入6位数字验证码')
      return
    }

    // 这里可以添加验证验证码的逻辑
    currentStep.value = 3
    submitBtnText.value = '重置密码'
  } else if (currentStep.value === 3) {
    // 第三步：设置新密码
    clearInputError('password')
    clearInputError('confirmPassword')

    if (!newPassword.value) {
      showInputError('password', '请输入新密码')
      return
    }
    if (!validatePassword(newPassword.value)) {
      showInputError('password', '密码至少8位，包含字母和数字')
      return
    }
    if (!confirmPassword.value) {
      showInputError('confirmPassword', '请确认新密码')
      return
    }
    if (newPassword.value !== confirmPassword.value) {
      showInputError('confirmPassword', '两次输入的密码不一致')
      return
    }

    setLoadingState(true)

    try {
      // 调用重置密码接口
      const res = await resetPassword({
        params: {
          phone: phone.value,
          code: code.value,
          password: newPassword.value,
        },
      })

      if (res.code === 200) {
        showNotification('密码重置成功，请使用新密码登录', 'success')
        setTimeout(() => {
          goToLogin()
        }, 1500)
      } else {
        showNotification(res.message || '密码重置失败，请稍后再试', 'error')
      }
    } catch (error) {
      showNotification('密码重置失败，请稍后再试', 'error')
    } finally {
      setLoadingState(false)
    }
  }
}

/**
 * 设置加载状态
 * @param loading 是否加载
 */
function setLoadingState(loading: boolean) {
  isLoading.value = loading
  if (loading) {
    submitIconVisible.value = false
  } else {
    submitIconVisible.value = true
  }
}

/**
 * 返回登录页
 */
function goToLogin() {
  uni.navigateTo({ url: '/pages/auth/login' })
}

/**
 * 页面加载时
 */
onMounted(() => {
  updateTime()
  const timerId = setInterval(updateTime, 1000) // Update time every second

  onBeforeUnmount(() => {
    if (countdownInterval) clearInterval(countdownInterval)
    clearInterval(timerId)
  })
})
</script>

<template>
  <view class="forget-container">
    <!-- 通知区域 -->
    <view v-if="notificationVisible" class="notification" :class="notificationType">
      <text class="notification-icon" :class="{
        'i-fa-solid-check-circle': notificationType === 'success',
        'i-fa-solid-exclamation-circle': notificationType === 'error',
        'i-fa-solid-info-circle': notificationType === 'info'
      }"></text>
      <text class="notification-message">{{ notificationText }}</text>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-area bg-gray-50 relative">
      <!-- 背景装饰元素 -->
      <view class="decoration-circle decoration-circle-1"></view>
      <view class="decoration-circle decoration-circle-2"></view>
      <view class="decoration-circle decoration-circle-3"></view>

      <view class="page-content">
        <!-- 页面标题 -->
        <view class="page-header">
          <view class="logo">
            <text class="i-fa-solid-key text-white"></text>
          </view>
          <text class="title">找回密码</text>
          <text class="subtitle">重置您的账号密码</text>
        </view>

        <!-- 步骤指示器 -->
        <view class="steps-container">
          <view class="steps">
            <view class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
              <view class="step-dot">
                <text v-if="currentStep > 1" class="i-fa-solid-check"></text>
                <text v-else>1</text>
              </view>
              <text class="step-label">验证手机</text>
            </view>
            <view class="step-line" :class="{ active: currentStep >= 2 }"></view>
            <view class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
              <view class="step-dot">
                <text v-if="currentStep > 2" class="i-fa-solid-check"></text>
                <text v-else>2</text>
              </view>
              <text class="step-label">验证码验证</text>
            </view>
            <view class="step-line" :class="{ active: currentStep >= 3 }"></view>
            <view class="step" :class="{ active: currentStep >= 3 }">
              <view class="step-dot">
                <text v-if="currentStep > 3" class="i-fa-solid-check"></text>
                <text v-else>3</text>
              </view>
              <text class="step-label">设置新密码</text>
            </view>
          </view>
        </view>

        <!-- 表单容器 -->
        <view class="form-container">
          <!-- 步骤1: 手机号验证 -->
          <view v-if="currentStep === 1" class="step-form">
            <view class="form-item">
              <text class="form-label">手机号 <text class="required">*</text></text>
              <view class="input-container">
                <input
                  type="number"
                  v-model="phone"
                  placeholder="请输入您的手机号码"
                  class="form-input"
                  :class="{ 'input-error': phoneError }"
                  maxlength="11"
                />
                <text class="input-icon i-fa-solid-mobile-screen-button"></text>
              </view>
              <text v-if="phoneError" class="error-text">{{ phoneError }}</text>
            </view>

            <button
              type="button"
              @click="handleSubmit"
              :disabled="isLoading"
              class="submit-btn"
              :class="{ 'btn-loading': isLoading }"
            >
              <view class="btn-content">
                <view v-if="isLoading" class="loading-spinner"></view>
                <text v-else class="i-fa-solid-arrow-right mr-2"></text>
                <text>{{ submitBtnText }}</text>
              </view>
            </button>
          </view>

          <!-- 步骤2: 验证码验证 -->
          <view v-if="currentStep === 2" class="step-form">
            <view class="form-item">
              <text class="form-label">验证码 <text class="required">*</text></text>
              <view class="code-container">
                <view class="input-container flex-1">
                  <input
                    type="number"
                    v-model="code"
                    placeholder="请输入验证码"
                    class="form-input"
                    :class="{ 'input-error': codeError }"
                    maxlength="6"
                  />
                  <text class="input-icon i-fa-solid-shield-halved"></text>
                </view>
                <button
                  type="button"
                  @click="handleSendCode"
                  :disabled="sendCodeBtnDisabled"
                  class="code-btn"
                  :class="{ 'btn-disabled': sendCodeBtnDisabled }"
                >
                  {{ sendCodeBtnText }}
                </button>
              </view>
              <text v-if="codeError" class="error-text">{{ codeError }}</text>
            </view>

            <button
              type="button"
              @click="handleSubmit"
              :disabled="isLoading"
              class="submit-btn"
              :class="{ 'btn-loading': isLoading }"
            >
              <view class="btn-content">
                <view v-if="isLoading" class="loading-spinner"></view>
                <text v-else class="i-fa-solid-arrow-right mr-2"></text>
                <text>{{ submitBtnText }}</text>
              </view>
            </button>
          </view>

          <!-- 步骤3: 设置新密码 -->
          <view v-if="currentStep === 3" class="step-form">
            <view class="form-item">
              <text class="form-label">新密码 <text class="required">*</text></text>
              <view class="input-container">
                <input
                  :type="passwordVisible ? 'text' : 'password'"
                  v-model="newPassword"
                  placeholder="请输入新密码"
                  class="form-input"
                  :class="{ 'input-error': passwordError }"
                  maxlength="20"
                />
                <text class="input-icon-toggle" @click="togglePasswordVisibility('new')">
                  <text v-if="passwordVisible" class="i-fa-solid-eye-slash"></text>
                  <text v-else class="i-fa-solid-eye"></text>
                </text>
              </view>
              <text v-if="passwordError" class="error-text">{{ passwordError }}</text>
            </view>

            <view class="form-item">
              <text class="form-label">确认密码 <text class="required">*</text></text>
              <view class="input-container">
                <input
                  :type="confirmPasswordVisible ? 'text' : 'password'"
                  v-model="confirmPassword"
                  placeholder="请再次输入密码"
                  class="form-input"
                  :class="{ 'input-error': confirmPasswordError }"
                  maxlength="20"
                />
                <text class="input-icon-toggle" @click="togglePasswordVisibility('confirm')">
                  <text v-if="confirmPasswordVisible" class="i-fa-solid-eye-slash"></text>
                  <text v-else class="i-fa-solid-eye"></text>
                </text>
              </view>
              <text v-if="confirmPasswordError" class="error-text">{{ confirmPasswordError }}</text>
            </view>

            <button
              type="button"
              @click="handleSubmit"
              :disabled="isLoading"
              class="submit-btn"
              :class="{ 'btn-loading': isLoading }"
            >
              <view class="btn-content">
                <view v-if="isLoading" class="loading-spinner"></view>
                <text v-else class="i-fa-solid-check mr-2"></text>
                <text>{{ submitBtnText }}</text>
              </view>
            </button>
          </view>

          <!-- 返回登录 -->
          <view class="login-link">
            <text @click="goToLogin" class="link-text">
              <text class="i-fa-solid-arrow-left mr-1"></text>
              返回登录
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
/* 基础样式 */
.forget-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fdfc 0%, #e8f5f2 100%);
  overflow-x: hidden;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease;
  
  &.success {
    border-left: 8rpx solid #00C9A7;
  }
  
  &.error {
    border-left: 8rpx solid #F56565;
  }
  
  &.info {
    border-left: 8rpx solid #3498db;
  }
}

.notification-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
  
  .success & {
    color: #00C9A7;
  }
  
  .error & {
    color: #F56565;
  }
  
  .info & {
    color: #3498db;
  }
}

.notification-message {
  font-size: 28rpx;
  color: #333;
}

/* 主内容区域 */
.content-area {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding: 40rpx 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 装饰圆圈 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  backdrop-filter: blur(20rpx);
}

.decoration-circle-1 {
  top: 160rpx;
  right: 60rpx;
  width: 240rpx;
  height: 240rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.2) 0%, rgba(0, 201, 167, 0.4) 100%);
  animation: floating 3s ease-in-out infinite;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);
}

.decoration-circle-2 {
  bottom: 300rpx;
  left: 60rpx;
  width: 180rpx;
  height: 180rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.3) 100%);
  animation: floating 4s ease-in-out infinite;
  animation-direction: reverse;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.1);
}

.decoration-circle-3 {
  top: 50%;
  right: -80rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.2) 100%);
  animation: floating 5s ease-in-out infinite;
}

/* 页面内容 */
.page-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 750rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  width: 100%;
  margin-bottom: 40rpx;
  text-align: center;
  animation: slideInDown 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 20rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
  }

  text {
    font-size: 60rpx;
    position: relative;
    z-index: 1;
  }
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 10rpx;
  background: linear-gradient(135deg, #333 0%, #00B39A 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #777;
}

/* 步骤指示器 */
.steps-container {
  margin-bottom: 40rpx;
  animation: slideInDown 0.6s cubic-bezier(0.19, 1, 0.22, 1) 0.2s both;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 20rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.step-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #E5E7EB;
  color: #666;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &.active {
    background: #00C9A7;
    color: #fff;
  }
}

.step-line {
  flex: 1;
  height: 4rpx;
  background: #E5E7EB;
  transition: all 0.3s ease;
  
  &.active {
    background: #00C9A7;
  }
}

.step-label {
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.step.active .step-dot {
  background: #00C9A7;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
}

.step.active .step-label {
  color: #333;
  font-weight: 600;
}

.step.completed .step-dot {
  background: #00B39A;
}

/* 表单容器 */
.form-container {
  width: 100%;
  animation: slideInUp 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0.2s both;
}

.step-form {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 24rpx;
  animation: slideInLeft 0.6s cubic-bezier(0.19, 1, 0.22, 1) calc(var(--item-index, 0) * 0.1s + 0.3s) both;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.required {
  color: #F56565;
  margin-left: 4rpx;
}

.input-container {
  position: relative;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 96rpx;
  padding: 0 96rpx 0 30rpx;
  font-size: 30rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:focus {
    border-color: #00C9A7;
    box-shadow: 0 0 0 2rpx rgba(0, 201, 167, 0.2);
    background: #fff;
    transform: translateY(-2rpx);
  }

  &::placeholder {
    color: #A3A3A3;
  }

  &:focus::placeholder {
    color: transparent;
  }
}

.input-error {
  border-color: #F56565 !important;
  box-shadow: 0 0 0 2rpx rgba(245, 101, 101, 0.2) !important;
  animation: shake 0.5s ease-in-out;
}

.input-icon {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 36rpx;
  color: #A3A3A3;
  transform: translateY(-50%);
}

.input-icon-toggle {
  position: absolute;
  top: 50%;
  right: 30rpx;
  font-size: 36rpx;
  color: #A3A3A3;
  transform: translateY(-50%);
  cursor: pointer;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;

  &:active {
    color: #00C9A7;
    background: rgba(0, 201, 167, 0.1);
    transform: translateY(-50%) scale(0.95);
  }
}

.error-text {
  display: block;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #F56565;
  animation: slideInDown 0.3s ease-out;
}

/* 验证码输入 */
.code-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.code-btn {
  height: 96rpx;
  min-width: 180rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #00C9A7;
  background: #FFFFFF;
  border: 2rpx solid #00C9A7;
  border-radius: 16rpx;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
}

.btn-disabled {
  opacity: 0.5;
}

/* 提交按钮 */
.submit-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  margin: 40rpx 0 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #00C9A7 0%, #00B39A 100%);
  border: none;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
  animation: slideInUp 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.6s both;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 4rpx 8rpx rgba(0, 201, 167, 0.2);

    &::before {
      opacity: 1;
    }
  }
}

.btn-loading {
  opacity: 0.8;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 登录链接 */
.login-link {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20rpx 0 40rpx;
}

.link-text {
  display: inline-flex;
  align-items: center;
  font-size: 28rpx;
  color: #777;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  
  &:active {
    color: #00C9A7;
    background: rgba(0, 201, 167, 0.1);
    transform: scale(0.95);
  }
}

/* 动画 */
@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

@keyframes slideDown {
  from {
    transform: translate(-50%, -20rpx);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5rpx);
  }
}
</style>

