<template>
  <view :class="cardClasses" @click="handleClick">
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title" class="ui-card__header">
      <slot name="header">
        <view class="ui-card__title-wrapper">
          <text v-if="title" class="ui-card__title">{{ title }}</text>
          <text v-if="subtitle" class="ui-card__subtitle">{{ subtitle }}</text>
        </view>
      </slot>
      <view v-if="$slots.extra" class="ui-card__extra">
        <slot name="extra"></slot>
      </view>
    </view>

    <!-- 卡片内容 -->
    <view v-if="$slots.default" class="ui-card__body">
      <slot></slot>
    </view>

    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="ui-card__footer">
      <slot name="footer"></slot>
    </view>

    <!-- 渐变顶部条 -->
    <view v-if="showTopBar" class="ui-card__top-bar"></view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 卡片标题
  title?: string
  // 卡片副标题
  subtitle?: string
  // 是否显示阴影
  shadow?: boolean
  // 是否可点击
  clickable?: boolean
  // 是否显示顶部渐变条
  showTopBar?: boolean
  // 卡片类型
  type?: 'default' | 'glass' | 'data' | 'stats'
  // 自定义类名
  customClass?: string
  // 内边距
  padding?: 'none' | 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  shadow: true,
  clickable: false,
  showTopBar: false,
  type: 'default',
  customClass: '',
  padding: 'medium',
})

const emit = defineEmits<{
  click: [event: Event]
}>()

// 计算卡片类名
const cardClasses = computed(() => {
  const classes = [
    'ui-card',
    `ui-card--${props.type}`,
    `ui-card--padding-${props.padding}`,
    props.customClass,
  ]

  if (props.shadow) classes.push('ui-card--shadow')
  if (props.clickable) classes.push('ui-card--clickable')

  return classes.join(' ')
})

// 处理点击事件
const handleClick = (event: Event) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables';
@import '@/styles/mixins';

.ui-card {
  position: relative;
  overflow: hidden;
  background: $bg-primary;
  border-radius: $card-radius;
  transition: all $transition-base;

  // 卡片类型样式
  &--default {
    background: $bg-primary;
  }

  &--glass {
    @include card-glass;
  }

  &--data {
    @include data-card;
  }

  &--stats {
    @include data-card;
    color: $text-inverse;
    background: linear-gradient(135deg, $primary-color 0%, $primary-color-light 100%);

    .ui-card__title {
      color: $text-inverse;
    }

    .ui-card__subtitle {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  // 阴影样式
  &--shadow {
    box-shadow: $card-shadow;
  }

  // 可点击样式
  &--clickable {
    cursor: pointer;

    &:hover {
      box-shadow: $shadow-lg;
      transform: translateY(-4rpx);
    }

    &:active {
      box-shadow: $shadow-sm;
      transform: translateY(2rpx);
    }
  }

  // 内边距样式
  &--padding-none {
    .ui-card__header,
    .ui-card__body,
    .ui-card__footer {
      padding: 0;
    }
  }

  &--padding-small {
    .ui-card__header,
    .ui-card__body,
    .ui-card__footer {
      padding: $spacing-base;
    }
  }

  &--padding-medium {
    .ui-card__header,
    .ui-card__body,
    .ui-card__footer {
      padding: $spacing-lg;
    }
  }

  &--padding-large {
    .ui-card__header,
    .ui-card__body,
    .ui-card__footer {
      padding: $spacing-xl;
    }
  }
}

// 卡片头部
.ui-card__header {
  @include flex-between;
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-light;

  .ui-card__title-wrapper {
    flex: 1;
  }

  .ui-card__extra {
    margin-left: $spacing-base;
  }
}

// 卡片标题
.ui-card__title {
  display: block;
  margin-bottom: $spacing-xs;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
}

// 卡片副标题
.ui-card__subtitle {
  display: block;
  font-size: $font-size-sm;
  color: $text-secondary;
}

// 卡片内容
.ui-card__body {
  padding: $spacing-lg;
}

// 卡片底部
.ui-card__footer {
  padding: $spacing-lg;
  background: $bg-secondary;
  border-top: 1rpx solid $border-light;
}

// 顶部渐变条
.ui-card__top-bar {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 4rpx;
  background: $gradient-primary;
  border-radius: $card-radius $card-radius 0 0;
}

// 特殊卡片样式
.ui-card--glass {
  .ui-card__header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .ui-card__footer {
    background: rgba(255, 255, 255, 0.05);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}

// 数据卡片特殊样式
.ui-card--data {
  .ui-card__header {
    padding-bottom: 0;
    border-bottom: none;
  }
}

// 统计卡片特殊样式
.ui-card--stats {
  .ui-card__header {
    border-bottom-color: rgba(255, 255, 255, 0.2);
  }

  .ui-card__footer {
    background: rgba(255, 255, 255, 0.1);
    border-top-color: rgba(255, 255, 255, 0.2);
  }
}

// 响应式适配
@include mobile {
  .ui-card {
    border-radius: $radius-md;

    &__header,
    &__body,
    &__footer {
      padding: $spacing-base;
    }
  }

  .ui-card__title {
    font-size: $font-size-base;
  }
}
</style>
