<script setup lang="ts">
import { ref, onMounted } from 'vue'
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
import type { ResumeDetail } from '@/types/resume'
import {
  getMyResumeList,
  uploadResume,
  renameResume,
  setDefaultResume,
  deleteResume,
  previewResume,
  getFileIcon,
} from '@/service/resume'

/**
 * 简历管理页面
 * 用于上传和管理用户的个人简历
 */

// 简历列表
const resumeList = ref<ResumeDetail[]>([])

// 是否显示操作菜单
const showMenu = ref(false)

// 当前选中的简历
const currentResume = ref<ResumeDetail | null>(null)

// 是否正在加载
const loading = ref(false)

// 页面是否已加载完成
const pageLoaded = ref(false)

// 正在删除的简历ID
const deletingResumeId = ref<number | null>(null)

/**
 * @description 加载简历列表
 */
const loadResumeList = async (): Promise<void> => {
  try {
    loading.value = true
    const response = await getMyResumeList()
    console.log(response)
    if (response.code === 200) {
      resumeList.value = response.data || []
    } else {
      uni.showToast({
        title: response.message || '获取简历列表失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('加载简历列表失败：', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 打开简历预览
 * @param resume 简历信息
 */
const previewResumeFile = async (resume: ResumeDetail): Promise<void> => {
  try {
    // 跳转到简历预览页面，只传递resumeId
    uni.navigateTo({
      url: `/pages/user/resume-preview?resumeId=${resume.resumeId}`,
    })
  } catch (error) {
    console.error('跳转预览页面失败：', error)
    uni.showToast({
      title: '预览失败，请稍后重试',
      icon: 'none',
    })
  }
}

/**
 * @description 显示操作菜单
 * @param resume 简历信息
 */
const showOperations = (resume: ResumeDetail): void => {
  currentResume.value = resume
  showMenu.value = true
}

/**
 * @description 隐藏操作菜单
 */
const hideMenu = (): void => {
  showMenu.value = false
  currentResume.value = null
}

/**
 * @description 设为默认简历
 */
const setAsDefault = async (): Promise<void> => {
  if (!currentResume.value) return

  try {
    const response = await setDefaultResume({ params: { resumeId: currentResume.value.resumeId } })
    if (response.code === 200) {
      // 更新本地数据
      resumeList.value.forEach((item) => {
        item.isDefault = item.resumeId === currentResume.value!.resumeId ? 1 : 0
      })
      // 重新排序
      resumeList.value.sort((a, b) => b.isDefault - a.isDefault)

      uni.showToast({
        title: '已设为默认简历',
        icon: 'success',
      })
    } else {
      uni.showToast({
        title: response.message || '设置失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('设置默认简历失败：', error)
    uni.showToast({
      title: '设置失败，请重试',
      icon: 'none',
    })
  } finally {
    hideMenu()
  }
}

/**
 * @description 重命名简历
 */
const renameResumeFile = (): void => {
  if (!currentResume.value) return

  // 保存当前简历的引用，避免在异步回调中丢失
  const resume = currentResume.value

  uni.showModal({
    title: '重命名简历',
    editable: true,
    placeholderText: '请输入新名称',
    content: resume.resumeName,
    success: async (res: UniApp.ShowModalRes) => {
      if (res.confirm && res.content?.trim()) {
        try {
          console.log('重命名简历', resume.resumeId, res.content.trim())
          const response = await renameResume({
            params: {
              resumeId: resume.resumeId,
              resumeName: res.content.trim(),
            },
          })
          if (response.code === 200) {
            // 更新本地数据
            const index = resumeList.value.findIndex((item) => item.resumeId === resume.resumeId)
            if (index !== -1) {
              resumeList.value[index].resumeName = res.content.trim()
            }

            uni.showToast({
              title: '重命名成功',
              icon: 'success',
            })
          } else {
            uni.showToast({
              title: response.message || '重命名失败',
              icon: 'none',
            })
          }
        } catch (error) {
          console.error('重命名失败：', error)
          uni.showToast({
            title: '重命名失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })

  hideMenu()
}

/**
 * @description 删除简历
 */
const deleteResumeFile = (): void => {
  if (!currentResume.value) return
  console.log('删除简历', currentResume.value)
  // 保存当前简历的引用，避免在异步回调中丢失
  const resume = currentResume.value
  uni.showModal({
    title: '删除简历',
    content: '确定要删除这份简历吗？',
    success: async (res: UniApp.ShowModalRes) => {
      if (res.confirm) {
        try {
          // 设置删除中状态
          deletingResumeId.value = resume.resumeId

          const response = await deleteResume({ params: { resumeIds: [resume.resumeId] } })
          if (response.code === 200) {
            // 延迟移除，让动画播放完
            setTimeout(() => {
              resumeList.value = resumeList.value.filter(
                (item) => item.resumeId !== resume.resumeId,
              )
              deletingResumeId.value = null
            }, 300)

            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })
          } else {
            deletingResumeId.value = null
            uni.showToast({
              title: response.message || '删除失败',
              icon: 'none',
            })
          }
        } catch (error) {
          console.error('删除失败：', error)
          deletingResumeId.value = null
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
  hideMenu()
}

/**
 * @description 上传新简历
 */
const uploadResumeFile = (): void => {
  // H5端使用input选择文件
  // #ifdef H5
  console.log('上传新简历H5')
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf,.doc,.docx'
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]
    if (!file) return

    try {
      uni.showLoading({
        title: '正在上传...',
      })

      // 提取文件信息
      const fileName = file.name
      const fileType = file.type
      const fileSize = file.size

      // 验证文件大小(最大10MB)
      if (fileSize > 10 * 1024 * 1024) {
        uni.showToast({
          title: '文件大小不能超过10MB',
          icon: 'none',
        })
        return
      }

      // 创建临时文件路径（H5端使用Blob URL）
      const tempFilePath = URL.createObjectURL(file)

      const response = await uploadResume({
        filePath: tempFilePath,
        fileName: fileName,
        fileType: fileType,
        formData: {
          fileSize: fileSize.toString(),
        },
      })
      console.log('上传响应：', response)

      if (response.code === 200) {
        uni.showToast({
          title: '上传成功',
          icon: 'success',
        })
        // 重新加载列表
        loadResumeList()
      } else {
        uni.showToast({
          title: response.message || '上传失败',
          icon: 'none',
        })
      }
    } catch (error) {
      console.error('上传失败：', error)
      uni.showToast({
        title: '上传失败，请重试',
        icon: 'none',
      })
    } finally {
      uni.hideLoading()
    }
  }
  input.click()
  // #endif

  // 小程序端使用chooseMessageFile选择文件
  // #ifdef MP-WEIXIN
  console.log('上传新简历小程序')
  uni.chooseMessageFile({
    count: 1,
    type: 'file',
    extension: ['pdf', 'doc', 'docx'],
    success: async (res: any) => {
      if (res.tempFiles && res.tempFiles.length > 0) {
        const file = res.tempFiles[0]

        // 验证文件大小(最大10MB)
        if (file.size > 10 * 1024 * 1024) {
          uni.showToast({
            title: '文件大小不能超过10MB',
            icon: 'none',
          })
          return
        }

        try {
          uni.showLoading({
            title: '正在上传...',
          })

          // 提取文件信息
          const fileName = file.name
          const fileType = file.type || ''
          const fileSize = file.size

          const response = await uploadResume({
            filePath: file.path,
            fileName: fileName,
            fileType: fileType,
            formData: {
              fileSize: fileSize.toString(),
            },
          })

          if (response.code === 200) {
            uni.showToast({
              title: '上传成功',
              icon: 'success',
            })
            // 重新加载列表
            loadResumeList()
          } else {
            uni.showToast({
              title: response.message || '上传失败',
              icon: 'none',
            })
          }
        } catch (error) {
          console.error('上传失败：', error)
          uni.showToast({
            title: '上传失败，请重试',
            icon: 'none',
          })
        } finally {
          uni.hideLoading()
        }
      }
    },
    fail: () => {
      uni.showToast({
        title: '选择文件失败',
        icon: 'none',
      })
    },
  })
  // #endif
}

/**
 * @description 格式化创建时间
 * @param dateStr 时间字符串
 * @returns 格式化后的时间
 */
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * @description 获取文件图标样式
 * @param fileName 文件名
 * @returns 文件图标样式
 */
const getFileIconStyle = (fileName: string): string => {
  const fileType = fileName.toLowerCase().split('.').pop()
  if (fileType === 'pdf') {
    return 'color: #ff4d4f;'
  } else if (fileType === 'doc' || fileType === 'docx') {
    return 'color: #1890ff;'
  } else {
    return 'color: #1890ff;'
  }
}

// 页面加载时初始化数据
onMounted(async () => {
  await loadResumeList()
  // 延迟显示页面内容，创建进入动画效果
  setTimeout(() => {
    pageLoaded.value = true
  }, 100)
})
</script>

<template>
  <view class="resume-page">
    <HeadBar title="简历管理" :show-back="true" :show-right-button="false" />

    <view class="resume-page-content" :class="pageLoaded ? 'page-loaded' : ''">
      <!-- 上传按钮 -->
      <view class="upload-btn animate-slide-up" @click="uploadResumeFile" style="margin-top: 80rpx">
        <text class="i-mdi-plus" style="margin-right: 16rpx; font-size: 24rpx"></text>
        <text>上传简历</text>
      </view>

      <!-- 加载中状态 -->
      <LoadingCard :visible="loading" text="正在加载简历列表..." />

      <!-- 内容区域 -->
      <template v-if="!loading">
        <!-- 简历列表 -->
        <view class="resume-list animate-slide-up" v-if="resumeList.length > 0">
          <transition-group name="list-item" tag="view">
            <view
              class="resume-item"
              :class="{ deleting: deletingResumeId === resume.resumeId }"
              v-for="(resume, index) in resumeList"
              :key="resume.resumeId"
              :style="{ 'animation-delay': `${index * 100}ms` }"
            >
              <!-- 默认标签 - 定位到右上角 -->
              <view class="default-tag-corner animate-bounce-in" v-if="resume.isDefault">默认</view>

              <!-- 简历图标 -->
              <view class="resume-icon animate-scale-in">
                <text
                  :class="getFileIcon({ params: { fileName: resume.originalName } })"
                  style="font-size: 40rpx"
                  :style="getFileIconStyle(resume.originalName)"
                ></text>
              </view>

              <!-- 简历信息 -->
              <view class="resume-info" @click="previewResumeFile(resume)">
                <view class="resume-name">
                  <text>{{ resume.resumeName }}</text>
                </view>
                <view class="resume-meta">
                  <text>{{ resume.fileSizeStr }} · {{ formatDate(resume.createTime) }}</text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="resume-actions">
                <text
                  class="i-mdi-dots-vertical action-icon"
                  style="font-size: 32rpx; color: #999"
                  @click="showOperations(resume)"
                ></text>
              </view>
            </view>
          </transition-group>
        </view>

        <!-- 空状态 -->
        <view class="empty-state animate-fade-in" v-else>
          <view class="empty-icon animate-float">
            <text class="i-mdi-file-upload" style="font-size: 64rpx; color: #ccc"></text>
          </view>
          <text class="empty-text">您还没有上传简历</text>
          <text class="empty-desc">上传简历后，可在面试中自动推荐给面试官</text>
        </view>
      </template>

      <!-- 简历提示 -->
      <view class="resume-tips animate-slide-up">
        <view class="tip-title">
          <text
            class="i-mdi-lightbulb tip-icon"
            style="margin-right: 8rpx; font-size: 24rpx; color: #f59e42"
          ></text>
          <text>简历小贴士</text>
        </view>
        <view class="tip-list">
          <text class="tip-item">• 简洁明了：突出关键技能和经验</text>
          <text class="tip-item">• 量化成果：用数据说明你的成就</text>
          <text class="tip-item">• 关键词优化：匹配职位描述中的关键词</text>
          <text class="tip-item">• 检查错误：确保没有拼写和语法错误</text>
        </view>
      </view>
    </view>

    <!-- 操作菜单弹窗 - 移到页面根部 -->
    <view class="menu-overlay" v-if="showMenu" @click="hideMenu">
      <view class="menu-content" @click.stop>
        <view class="menu-item" @click="setAsDefault">
          <text
            class="i-mdi-check-circle"
            style="margin-right: 16rpx; font-size: 24rpx; color: #00b39a"
          ></text>
          <text>设为默认简历</text>
        </view>
        <view class="menu-item" @click="renameResumeFile">
          <text
            class="i-mdi-pencil"
            style="margin-right: 16rpx; font-size: 24rpx; color: #2563eb"
          ></text>
          <text>重命名</text>
        </view>
        <view class="menu-item menu-item-danger" @click="deleteResumeFile">
          <text
            class="i-mdi-delete"
            style="margin-right: 16rpx; font-size: 24rpx; color: #ff4d4f"
          ></text>
          <text>删除</text>
        </view>
        <view class="menu-cancel" @click="hideMenu">取消</view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 页面进入动画
.resume-page-content {
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.page-loaded {
    opacity: 1;
    transform: translateY(0);
  }
}

// 基础动画类
.animate-slide-up {
  animation: slideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

// 关键帧动画定义
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 列表项过渡动画
.list-item-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.list-item-leave-active {
  transition: all 0.3s ease-in;
}

.list-item-enter-from {
  opacity: 0;
  transform: translateX(-100rpx);
}

.list-item-leave-to {
  opacity: 0;
  transform: translateX(100rpx);
}

// 菜单过渡动画 - 简化不用的过渡效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUpMenu {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.menu-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease-out;

  // 小程序端优化
  /* #ifdef MP-WEIXIN */
  backdrop-filter: blur(4rpx);
  /* #endif */
}

.menu-content {
  width: 100%;
  max-width: 750rpx;
  max-height: 80vh;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: slideUpMenu 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;

  // H5端额外样式
  /* #ifdef H5 */
  box-shadow:
    0 -8rpx 32rpx rgba(0, 0, 0, 0.15),
    0 0 0 1rpx rgba(0, 0, 0, 0.05);
  /* #endif */
}

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
  background: #fff;

  // 添加点击动画反馈
  &:active {
    background: #f8f9fa;
    transform: scale(0.98);
  }

  &.menu-item-danger {
    color: #ff4d4f;

    &:active {
      background: #fff2f0;
    }
  }

  &:last-of-type {
    border-bottom: none;
  }

  // 添加图标动画
  text {
    transition: transform 0.2s ease;
  }

  &:active text {
    transform: scale(1.1);
  }
}

.menu-cancel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  margin-top: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  background: #f8f9fa;
  border-radius: 12rpx 12rpx 0 0;
  transition: all 0.2s ease;

  &:active {
    background: #e9ecef;
    transform: scale(0.98);
  }

  // 添加上边框分隔线
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 32rpx;
    right: 32rpx;
    height: 1rpx;
    background: #e9ecef;
  }
}

.default-tag-corner {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  z-index: 1;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  color: #00b39a;
  background: #e6f7f5;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 179, 154, 0.1);
}

.resume-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.resume-page-content {
  padding: 0 32rpx;
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  margin-bottom: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(135deg, #00c9a7 0%, #00b39a 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0); // 启用硬件加速

  &:active {
    transform: scale(0.98) translateZ(0);
    box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.2);
  }
}

.resume-list {
  margin-bottom: 32rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.resume-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  transform: translateZ(0); // 启用硬件加速

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
    transform: scale(0.99) translateZ(0);
  }

  &.deleting {
    opacity: 0;
    transform: translateX(100rpx) scale(0.9) translateZ(0);
  }
}

.resume-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  background: #fff2f0;
  border-radius: 16rpx;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.resume-info {
  flex: 1;
  transition: transform 0.2s ease;

  &:active {
    transform: translateX(4rpx);
  }
}

.resume-name {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.resume-meta {
  font-size: 22rpx;
  color: #999;
}

.resume-actions {
  padding: 16rpx;
}

.action-icon {
  transition: all 0.2s ease;

  &:active {
    transform: scale(1.2);
    color: #00b39a !important;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64rpx 0;
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.empty-icon {
  margin-bottom: 24rpx;
}

.empty-text {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

.resume-tips {
  padding: 24rpx;
  background: #fffbeb;
  border: 1rpx solid #fef3c7;
  border-radius: 24rpx;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2rpx);
  }
}

.tip-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #92400e;
}

.tip-icon {
  animation: pulse 2s infinite;
}

.tip-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #92400e;
  opacity: 0;
  transform: translateX(-20rpx);
  animation: slideInLeft 0.6s ease forwards;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.3s;
  }
  &:nth-child(4) {
    animation-delay: 0.4s;
  }
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
