/**
 * 埋点插件
 * 为Vue应用提供全局埋点功能
 *
 * <AUTHOR>
 */

import trackingUtils from '@/utils/trackingUtils'

export default {
  install(app) {
    // 将埋点工具挂载到全局属性
    app.config.globalProperties.$track = trackingUtils

    // 提供全局方法
    app.provide('$track', trackingUtils)

    // 自动埋点路由变化
    if (app.config.globalProperties.$router) {
      app.config.globalProperties.$router.afterEach((to, from) => {
        // 页面访问埋点
        trackingUtils.trackPageView(to.name || to.path, {
          fromPage: from.name || from.path,
          routeParams: to.params,
          routeQuery: to.query,
        })
      })
    }

    // 自动埋点用户登录状态变化
    const originalLogin = app.config.globalProperties.$store?.dispatch
    if (originalLogin) {
      app.config.globalProperties.$store.dispatch = function (type, payload) {
        const result = originalLogin.call(this, type, payload)

        // 监听登录相关的action
        if (type === 'user/login' || type === 'user/loginByToken') {
          result
            .then(() => {
              trackingUtils.trackLogin(payload?.loginMethod || 'unknown')
            })
            .catch(() => {
              // 登录失败不记录
            })
        }

        return result
      }
    }
  },
}

/**
 * 埋点指令
 * 使用方式：v-track="{ event: 'BUTTON_CLICK', data: { buttonName: 'submit' } }"
 */
export const trackingDirective = {
  mounted(el, binding) {
    if (!binding.value) return

    const { event, data = {}, immediate = false } = binding.value

    if (immediate) {
      trackingUtils.track(event, data)
    } else {
      el.addEventListener('click', () => {
        trackingUtils.track(event, data)
      })
    }
  },
}

/**
 * 埋点混入
 * 为组件提供埋点相关的方法和生命周期钩子
 */
export const trackingMixin = {
  data() {
    return {
      pageStartTime: Date.now(),
    }
  },

  mounted() {
    // 记录页面加载时间
    this.pageStartTime = Date.now()

    // 如果组件有trackOnMount配置，自动发送埋点
    if (this.$options.trackOnMount) {
      const { event, data } = this.$options.trackOnMount
      this.$track.track(event, {
        ...data,
        componentName: this.$options.name || 'Unknown',
      })
    }
  },

  beforeUnmount() {
    // 记录页面停留时间
    const stayTime = Date.now() - this.pageStartTime

    if (this.$options.trackOnUnmount) {
      const { event, data } = this.$options.trackOnUnmount
      this.$track.track(
        event,
        {
          ...data,
          stayTime,
          componentName: this.$options.name || 'Unknown',
        },
        true,
      ) // 立即发送
    }
  },

  methods: {
    /**
     * 便捷的埋点方法
     */
    $trackEvent(event, data = {}) {
      this.$track.track(event, {
        ...data,
        componentName: this.$options.name || 'Unknown',
      })
    },

    /**
     * 立即发送埋点
     */
    $trackEventImmediate(event, data = {}) {
      this.$track.track(
        event,
        {
          ...data,
          componentName: this.$options.name || 'Unknown',
        },
        true,
      )
    },
  },
}
