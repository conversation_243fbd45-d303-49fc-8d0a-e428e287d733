@import 'variables';
@import 'mixins';

@each $font-family
  in (
    'Material Icons',
    'Material Icons Outlined',
    'Material Icons Round',
    'Material Icons Sharp',
    'Material Icons Two Tone'
  )
{
  $full-class-name: to-lower-case($font-family);
  $full-class-name: material-icons-str-replace($full-class-name, ' ', '-');
  $class-name: material-icons-str-replace(
    $full-class-name,
    'material-icons',
    $material-icons-css-prefix
  );

  /**
   * @deprecated As of 1.0, use .#{$full-class-name} instead of .#{$class-name}
   */
  .#{$class-name} {
    @include material-icons-font-class($font-family);
  }
}

@each $name, $codepoint in $material-icons-codepoints {
  $class-name: material-icons-str-replace(
    $name,
    $material-icons-css-search,
    $material-icons-css-replace
  );

  .#{$material-icons-css-prefix}-#{$class-name} {
    @include material-icon($name);
  }
}
