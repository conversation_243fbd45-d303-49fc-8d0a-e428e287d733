<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

/**
 * @description H5摄像头组件
 * @param autoInit 是否自动初始化摄像头
 * @param initialFacing 初始摄像头朝向
 */
const props = defineProps({
  autoInit: {
    type: Boolean,
    default: true,
  },
  initialFacing: {
    type: String,
    default: 'user', // 'user'(前置) 或 'environment'(后置)
  },
  definition: {
    type: Boolean,
    default: false, // false 正常  true 高清
  },
  // 添加强制前置摄像头属性
  forceFrontCamera: {
    type: Boolean,
    default: true, // 默认强制使用前置摄像头
  }
})

// 组件事件
const emit = defineEmits(['ready', 'error', 'close'])

// 状态
const isReady = ref(false)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const facingMode = ref(props.initialFacing)
const isMobileDevice = ref(false)

// 引用
const containerRef = ref(null)
const videoRef = ref(null)
const canvasRef = ref(null)
const mediaStream = ref(null)

// 尺寸相关
const windowWidth = ref(0)
const windowHeight = ref(0)
const canvasWidth = ref(300)
const canvasHeight = ref(200)
const maskWidth = ref(0)
const maskHeight = ref(0)

// 闪光灯相关
const track = ref(null)
const isUseTorch = ref(false)
const trackStatus = ref(false)

/**
 * @description 检测是否为移动设备
 */
const checkMobileDevice = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  const mobileRegex = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
  isMobileDevice.value = mobileRegex.test(userAgent.toLowerCase())

  // 非移动设备或强制前置摄像头模式下强制使用前置摄像头
  if (!isMobileDevice.value || props.forceFrontCamera) {
    facingMode.value = 'user'
  }
}

/**
 * @description 初始化摄像头
 */
const initCamera = async () => {
  if (isLoading.value) return
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''

  // 检查是否HTTPS环境
  if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
    hasError.value = true
    errorMessage.value = '请在HTTPS环境中使用摄像头组件'
    isLoading.value = false
    emit('error', { error: new Error(errorMessage.value) })
    return
  }

  try {
    // 获取窗口尺寸
    windowWidth.value = document.documentElement.clientWidth || document.body.clientWidth
    windowHeight.value = document.documentElement.clientHeight || document.body.clientHeight

    // 计算遮罩尺寸
    maskWidth.value = windowWidth.value / 2 - 150
    maskHeight.value = windowHeight.value / 2 - 100

    // 创建视频元素
    if (!videoRef.value) {
      const video = document.createElement('video')
      video.width = windowWidth.value
      video.height = windowHeight.value
      video.setAttribute('playsinline', true)
      video.style.position = 'absolute'
      video.style.top = '0'
      video.style.left = '0'
      video.style.width = '100%'
      video.style.height = '100%'
      video.style.objectFit = 'cover'
      videoRef.value = video

      // 创建canvas元素
      const canvas = document.createElement('canvas')
      canvas.id = 'canvas'
      canvas.width = canvasWidth.value
      canvas.height = canvasHeight.value
      canvas.style = 'display:none;'
      canvasRef.value = canvas

      // 将元素添加到容器中
      if (containerRef.value) {
        containerRef.value.appendChild(video)
        containerRef.value.appendChild(canvas)
      }
    }

    // 设置视频流
    let width = transtion(windowHeight.value)
    let height = transtion(windowWidth.value)

    // 如果强制使用前置摄像头，则覆盖facingMode
    if (props.forceFrontCamera) {
      facingMode.value = 'user'
    }

    const videoParam = {
      audio: false,
      video: {
        facingMode: {
          exact: facingMode.value
        },
        width,
        height
      }
    }

    // 获取媒体流
    const stream = await navigator.mediaDevices.getUserMedia(videoParam)
    mediaStream.value = stream

    // 设置视频源
    videoRef.value.srcObject = stream
    videoRef.value.play()

    // 获取视频轨道
    track.value = stream.getVideoTracks()[0]

    // 检查是否支持闪光灯
    setTimeout(() => {
      if (track.value && track.value.getCapabilities && track.value.getCapabilities().torch) {
        isUseTorch.value = true
      } else {
        isUseTorch.value = false
      }

      // 发送就绪事件
      isReady.value = true
      isLoading.value = false
      emit('ready', { stream })
    }, 500)

  } catch (error) {
    console.error('摄像头初始化失败:', error)
    hasError.value = true
    isLoading.value = false

    // 设置错误信息
    if (error.name === 'NotAllowedError') {
      errorMessage.value = '摄像头权限被拒绝'
    } else if (error.name === 'NotFoundError') {
      errorMessage.value = '未找到摄像头设备'
    } else if (error.name === 'NotSupportedError') {
      errorMessage.value = '浏览器不支持摄像头功能'
    } else if (error.name === 'NotReadableError' || error.name === 'OverconstrainedError') {
      errorMessage.value = '无法访问摄像头，可能被其他应用占用'
    } else {
      errorMessage.value = error.message || '摄像头初始化失败'
    }

    emit('error', { error })
  }
}

/**
 * @description 关闭摄像头
 */
const closeCamera = () => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach((track) => track.stop())
    mediaStream.value = null
  }

  if (videoRef.value && videoRef.value.srcObject) {
    videoRef.value.srcObject = null
  }

  isReady.value = false
  emit('close')
}

/**
 * @description 切换前后摄像头
 */
const switchCamera = async () => {
  // 如果强制使用前置摄像头，则不允许切换
  if (props.forceFrontCamera) {
    console.log('强制使用前置摄像头模式，不允许切换')
    return
  }

  // 只有在移动设备上才允许切换摄像头
  if (!isMobileDevice.value) return

  facingMode.value = facingMode.value === 'user' ? 'environment' : 'user'
  await closeCamera()
  await initCamera()
}

/**
 * @description 切换闪光灯
 */
const toggleTorch = () => {
  if (!track.value || !isUseTorch.value) return

  trackStatus.value = !trackStatus.value
  track.value.applyConstraints({
    advanced: [{
      torch: trackStatus.value
    }]
  })
}

/**
 * @description 获取当前视频帧
 * @param {boolean} highQuality 是否使用高清模式
 * @returns {string} 图片的base64数据
 */
const captureFrame = (highQuality = false) => {
  if (!videoRef.value || !canvasRef.value) return null

  try {
    const ctx = canvasRef.value.getContext('2d')

    // 获取视频的实际尺寸
    const videoWidth = videoRef.value.videoWidth
    const videoHeight = videoRef.value.videoHeight

    // 如果是高清模式，使用更大的画布尺寸
    if (highQuality) {
      // 设置canvas尺寸为视频的实际尺寸
      canvasRef.value.width = videoWidth
      canvasRef.value.height = videoHeight

      // 直接绘制整个视频画面
      ctx.drawImage(
        videoRef.value,
        0,
        0,
        videoWidth,
        videoHeight,
        0,
        0,
        canvasRef.value.width,
        canvasRef.value.height
      )

      // 获取图像数据
      const imageData = canvasRef.value.toDataURL('image/jpeg', 1.0)

      // 恢复原始尺寸
      canvasRef.value.width = canvasWidth.value
      canvasRef.value.height = canvasHeight.value

      console.log('imageData', imageData)
      return imageData
    } else {
      // 普通模式 - 同样截取完整画面
      canvasRef.value.width = videoWidth
      canvasRef.value.height = videoHeight

      ctx.drawImage(
        videoRef.value,
        0,
        0,
        videoWidth,
        videoHeight,
        0,
        0,
        canvasRef.value.width,
        canvasRef.value.height
      )

      const imageData = canvasRef.value.toDataURL('image/jpeg', 0.9)

      // 恢复原始尺寸
      canvasRef.value.width = canvasWidth.value
      canvasRef.value.height = canvasHeight.value

      return imageData
    }
  } catch (e) {
    console.log('截图出错', e)
    return null
  }
}

/**
 * @description 根据分辨率设置转换数值
 */
const transtion = (number) => {
  return props.definition ? number * 2.6 : number * 1.6
}

// 暴露方法给父组件
defineExpose({
  initCamera,
  closeCamera,
  switchCamera,
  toggleTorch,
  captureFrame,
  isReady,
  hasError,
  errorMessage,
  isUseTorch,
  trackStatus,
  isMobileDevice
})

// 生命周期钩子
onMounted(() => {
  // 检测设备类型
  checkMobileDevice()

  if (props.autoInit) {
    // 延迟初始化，确保DOM已完全挂载
    setTimeout(() => {
      initCamera()
    }, 300)
  }
})

onUnmounted(() => {
  closeCamera()
})
</script>

<template>
  <div class="h5-camera" ref="containerRef">
    <!-- 加载状态 -->
    <div class="camera-status" v-if="isLoading">
      <div class="loading-spinner">
        <i class="fa fa-spinner fa-spin spinner-icon"></i>
        <span class="status-text">正在启动摄像头...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div class="camera-error" v-if="hasError">
      <i class="fa fa-exclamation-circle error-icon"></i>
      <span class="error-text">{{ errorMessage }}</span>
      <button class="retry-btn" @click="initCamera">重试</button>
    </div>

  </div>
</template>

<style scoped>
.h5-camera {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #000;
}

.camera-status,
.camera-error {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner-icon {
  margin-bottom: 10px;
  font-size: 40px;
  animation: spin 1s linear infinite;
}

.error-icon {
  margin-bottom: 10px;
  font-size: 40px;
  color: #ff4d4f;
}

.status-text,
.error-text {
  font-size: 14px;
  text-align: center;
}

.retry-btn {
  padding: 8px 16px;
  margin-top: 15px;
  color: white;
  cursor: pointer;
  background-color: #1890ff;
  border: none;
  border-radius: 4px;
}

.camera-controls {
  position: absolute;
  right: 0;
  bottom: 20px;
  left: 0;
  z-index: 20;
  display: flex;
  gap: 20px;
  justify-content: center;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  color: white;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 8px;
}

.control-btn i {
  margin-bottom: 5px;
  font-size: 20px;
}

.control-btn span {
  font-size: 12px;
}

.scan-box {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 11;
  width: 300px;
  height: 200px;
  overflow: hidden;
  border: 0.1rem solid rgba(0, 255, 51, 0.2);
  transform: translate(-50%, -50%);
}

.scan-box:after,
.scan-box:before,
.angle:after,
.angle:before {
  position: absolute;
  z-index: 12;
  display: block;
  width: 3vw;
  height: 3vw;
  content: '';
  border: 0.2rem solid transparent;
}

.scan-box:after,
.scan-box:before {
  top: 0;
  border-top-color: #00ff33;
}

.angle:after,
.angle:before {
  bottom: 0;
  border-bottom-color: #00ff33;
}

.scan-box:before,
.angle:before {
  left: 0;
  border-left-color: #00ff33;
}

.scan-box:after,
.angle:after {
  right: 0;
  border-right-color: #00ff33;
}

.mask {
  position: absolute;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.55);
}

.mask1 {
  top: 0;
  right: 0;
  left: 0;
}

.mask2 {
  right: 0;
  height: 200px;
}

.mask3 {
  right: 0;
  bottom: 0;
  left: 0;
}

.mask4 {
  left: 0;
  height: 200px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.camera-notice {
  position: absolute;
  top: 10px;
  right: 0;
  left: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
}

.notice-text {
  font-size: 12px;
  color: white;
}
</style>
