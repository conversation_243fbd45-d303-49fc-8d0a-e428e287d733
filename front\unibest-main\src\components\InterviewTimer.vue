<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

/**
 * @description 面试计时器组件
 * 在面试过程中显示剩余时间并提供时间提醒
 */
const props = defineProps({
  // 总时长(秒)
  totalTime: {
    type: Number,
    default: 1800 // 默认30分钟
  },
  // 是否自动开始
  autoStart: {
    type: Boolean,
    default: false
  },
  // 是否处于暂停状态
  isPaused: {
    type: Boolean,
    default: false
  },
  // 提醒时间点(秒)，如[300, 60]表示剩余5分钟和1分钟时提醒
  reminderPoints: {
    type: Array as () => number[],
    default: () => [300, 60]
  }
})

// 组件事件
const emit = defineEmits(['timeout', 'reminder', 'update'])

// 状态
const isRunning = ref(false)
const timeRemaining = ref(props.totalTime)
const startTime = ref<number | null>(null)
const pausedTime = ref<number | null>(null)
const elapsedTime = ref(0)

// 提醒状态
const reminder = ref({
  show: false,
  type: 'info', // info, warning, danger
  icon: 'fa fa-clock',
  message: '',
  timestamp: 0
})

// 已触发的提醒点
const triggeredReminders = ref<number[]>([])

// 定时器
let timer: ReturnType<typeof setInterval> | null = null

/**
 * @description 格式化时间显示
 * @param seconds 秒数
 */
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化后的时间显示
const formattedTime = computed(() => formatTime(timeRemaining.value))

// 进度百分比
const progressPercentage = computed(() => {
  return ((props.totalTime - timeRemaining.value) / props.totalTime) * 100
})

/**
 * @description 开始计时
 */
const startTimer = () => {
  if (isRunning.value) return
  
  isRunning.value = true
  
  // 如果是从暂停状态恢复
  if (pausedTime.value !== null) {
    startTime.value = Date.now() - elapsedTime.value * 1000
    pausedTime.value = null
  } else {
    startTime.value = Date.now()
    elapsedTime.value = 0
    timeRemaining.value = props.totalTime
    triggeredReminders.value = []
  }
  
  // 启动定时器
  timer = setInterval(updateTimer, 1000)
  
  emit('update', { 
    isRunning: isRunning.value,
    timeRemaining: timeRemaining.value,
    elapsedTime: elapsedTime.value
  })
}

/**
 * @description 暂停计时
 */
const pauseTimer = () => {
  if (!isRunning.value) return
  
  isRunning.value = false
  pausedTime.value = Date.now()
  
  // 清除定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  
  emit('update', { 
    isRunning: isRunning.value,
    timeRemaining: timeRemaining.value,
    elapsedTime: elapsedTime.value
  })
}

/**
 * @description 重置计时器
 */
const resetTimer = () => {
  // 清除定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  
  isRunning.value = false
  startTime.value = null
  pausedTime.value = null
  elapsedTime.value = 0
  timeRemaining.value = props.totalTime
  triggeredReminders.value = []
  
  emit('update', { 
    isRunning: isRunning.value,
    timeRemaining: timeRemaining.value,
    elapsedTime: elapsedTime.value
  })
}

/**
 * @description 更新计时器
 */
const updateTimer = () => {
  if (!isRunning.value || !startTime.value) return
  
  // 计算已经过的时间
  elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
  
  // 计算剩余时间
  timeRemaining.value = Math.max(0, props.totalTime - elapsedTime.value)
  
  // 检查是否需要提醒
  checkReminders()
  
  // 检查是否超时
  if (timeRemaining.value <= 0) {
    timeRemaining.value = 0
    pauseTimer()
    emit('timeout')
  }
  
  // 发送更新事件
  emit('update', { 
    isRunning: isRunning.value,
    timeRemaining: timeRemaining.value,
    elapsedTime: elapsedTime.value
  })
}

/**
 * @description 检查是否需要提醒
 */
const checkReminders = () => {
  if (!props.reminderPoints || props.reminderPoints.length === 0) return
  
  // 检查每个提醒点
  props.reminderPoints.forEach(point => {
    // 如果当前剩余时间等于提醒点且该提醒点尚未触发
    if (timeRemaining.value === point && !triggeredReminders.value.includes(point)) {
      // 记录已触发的提醒点
      triggeredReminders.value.push(point)
      
      // 设置提醒类型
      const type = point <= 60 ? 'danger' : 'warning'
      const icon = type === 'danger' ? 'fa fa-exclamation-triangle' : 'fa fa-clock'
      const message = point >= 60 ? 
        `还剩${Math.floor(point / 60)}分钟` : 
        `还剩${point}秒`
      
      // 显示提醒
      showReminder(type, icon, message)
      
      // 发送提醒事件
      emit('reminder', { 
        type,
        point,
        message,
        timeRemaining: timeRemaining.value
      })
    }
  })
}

/**
 * @description 显示提醒
 */
const showReminder = (type: string, icon: string, message: string) => {
  reminder.value = {
    show: true,
    type,
    icon,
    message,
    timestamp: Date.now()
  }
  
  // 3秒后自动隐藏
  setTimeout(() => {
    if (reminder.value.timestamp === Date.now()) {
      dismissReminder()
    }
  }, 3000)
}

/**
 * @description 关闭提醒
 */
const dismissReminder = () => {
  reminder.value.show = false
}

/**
 * @description 切换计时器状态
 */
const toggleTimer = () => {
  if (isRunning.value) {
    pauseTimer()
  } else {
    startTimer()
  }
}

// 暴露方法给父组件
defineExpose({
  startTimer,
  pauseTimer,
  resetTimer,
  toggleTimer,
  formatTime,
  dismissReminder,
  timeRemaining,
  isRunning
})

// 生命周期钩子
onMounted(() => {
  if (props.autoStart) {
    startTimer()
  }
})

onUnmounted(() => {
  // 清理定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 监听暂停状态变化
watch(() => props.isPaused, (newValue) => {
  if (newValue) {
    pauseTimer()
  } else {
    startTimer()
  }
})
</script>

<template>
  <div class="interview-timer">
    <div class="timer-display" :class="{ 'warning': timeRemaining <= 300, 'danger': timeRemaining <= 60 }">
      <i class="fa fa-clock timer-icon"></i>
      <span class="timer-text">{{ formattedTime }}</span>
    </div>
    
    <div class="timer-progress">
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
          :class="{ 
            'warning': timeRemaining <= 300, 
            'danger': timeRemaining <= 60 
          }"
        ></div>
      </div>
    </div>
    
    <div class="timer-controls" v-if="$slots.controls">
      <slot name="controls" :toggle="toggleTimer" :reset="resetTimer" :is-running="isRunning"></slot>
    </div>
    
    <!-- 时间提醒弹窗 -->
    <div class="time-reminder" v-if="reminder.show">
      <div class="reminder-content" :class="reminder.type">
        <i :class="reminder.icon" class="reminder-icon"></i>
        <span class="reminder-text">{{ reminder.message }}</span>
        <button class="dismiss-reminder" @click="dismissReminder">
          <i class="fa fa-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.interview-timer {
  position: relative;
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 201, 167, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.timer-display.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.timer-display.danger {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.timer-icon {
  font-size: 14px;
  color: inherit;
}

.timer-text {
  font-size: 14px;
  font-weight: 600;
  color: inherit;
}

.timer-progress {
  margin-top: 6px;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-fill.warning {
  background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
}

.progress-fill.danger {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
}

.timer-controls {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.time-reminder {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.reminder-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.reminder-content.warning {
  border-left: 3px solid #faad14;
}

.reminder-content.danger {
  border-left: 3px solid #ff4d4f;
}

.reminder-icon {
  font-size: 16px;
}

.reminder-icon.fa-clock {
  color: #faad14;
}

.reminder-icon.fa-exclamation-triangle {
  color: #ff4d4f;
}

.reminder-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.dismiss-reminder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  color: #999;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dismiss-reminder:hover {
  background: #e6e6e6;
  color: #666;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
</style> 