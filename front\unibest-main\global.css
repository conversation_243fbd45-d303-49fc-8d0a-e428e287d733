/* 
  智能面试系统 - 全局样式
  基于index.vue的设计风格统一整个项目的UI主题
  
  @author: rjb-sias
  @create-date: 2025-06-17
  @last-update-date: 2025-01-18
*/
/* uni-app 兼容性样式 */
/* scroll-view 滚动条全局不显示 */
::-webkit-scrollbar {
  display: none;
}
/* 按钮全局不显示边框 */
uni-button:after {
  border: none !important;
}
/* 全局页面样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #222;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 项目固定尺寸：430 * 932 */
body {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
}

#app {
  max-width: 430px;
  max-height: 932px;
  margin: 0 auto;
  overflow-x: hidden;
}
/* 全局容器样式 */
.container {
  width: 100%;
  padding: 0 32rpx;
  margin: 0 auto;
}
/* 全局动画性能优化 */
* {
  box-sizing: border-box;
}
/* 提升滚动性能 */
.scroll-view {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
