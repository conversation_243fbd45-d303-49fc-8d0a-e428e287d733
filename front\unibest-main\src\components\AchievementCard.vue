<template>
  <div
    class="achievement-card"
    :class="{
      completed: achievement.isCompleted === '1',
      'in-progress': achievement.progress > 0 && achievement.isCompleted !== '1',
    }"
    @click="onCardClick"
  >
    <!-- 成就图标 -->
    <div class="achievement-icon">
      <img
        :src="achievement.achievementIcon || '/icons/default-achievement.png'"
        :alt="achievement.achievementName"
        @error="onImageError"
      />
      <!-- 完成标识 -->
      <div v-if="achievement.isCompleted === '1'" class="completed-badge">
        <i class="icon-check"></i>
      </div>
    </div>

    <!-- 成就信息 -->
    <div class="achievement-info">
      <h3 class="achievement-name">{{ achievement.achievementName }}</h3>
      <p class="achievement-desc">{{ achievement.achievementDesc }}</p>

      <!-- 成就类型标签 -->
      <div class="achievement-type">
        <span class="type-tag" :class="getTypeClass(achievement.achievementType)">
          {{ getTypeLabel(achievement.achievementType) }}
        </span>
      </div>

      <!-- 进度条 -->
      <div v-if="achievement.isCompleted !== '1'" class="progress-section">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: achievement.progress + '%' }"></div>
        </div>
        <div class="progress-text">
          <span>{{ achievement.currentValue || 0 }} / {{ achievement.targetValue || 1 }}</span>
          <span class="progress-percent">{{ Math.round(achievement.progress || 0) }}%</span>
        </div>
      </div>

      <!-- 奖励积分 -->
      <div class="reward-section">
        <span class="reward-points">
          <i class="icon-star"></i>
          {{ achievement.rewardPoints || 0 }} 积分
        </span>

        <!-- 解锁时间 -->
        <span v-if="achievement.unlockTime" class="unlock-time">
          {{ formatUnlockTime(achievement.unlockTime) }}
        </span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="achievement-actions">
      <button
        v-if="achievement.isCompleted === '1' && achievement.isNotified !== '1'"
        @click.stop="markAsNotified"
        class="action-btn notify-btn"
      >
        标记已读
      </button>

      <button
        v-if="achievement.isCompleted !== '1'"
        @click.stop="viewProgress"
        class="action-btn progress-btn"
      >
        查看进度
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AchievementCard',

  props: {
    achievement: {
      type: Object,
      required: true,
    },
  },

  emits: ['card-click', 'mark-notified', 'view-progress'],

  methods: {
    /**
     * 卡片点击事件
     */
    onCardClick() {
      // 发送成就卡片点击埋点
      this.$track.track('ACHIEVEMENT_CARD_CLICK', {
        achievementId: this.achievement.id,
        achievementCode: this.achievement.achievementCode,
        achievementName: this.achievement.achievementName,
        isCompleted: this.achievement.isCompleted,
        progress: this.achievement.progress,
      })

      this.$emit('card-click', this.achievement)
    },

    /**
     * 标记为已通知
     */
    markAsNotified() {
      // 发送标记已读埋点
      this.$track.track('ACHIEVEMENT_MARK_NOTIFIED', {
        achievementId: this.achievement.id,
        achievementCode: this.achievement.achievementCode,
      })

      this.$emit('mark-notified', this.achievement)
    },

    /**
     * 查看进度
     */
    viewProgress() {
      // 发送查看进度埋点
      this.$track.track('ACHIEVEMENT_VIEW_PROGRESS', {
        achievementId: this.achievement.id,
        achievementCode: this.achievement.achievementCode,
        currentProgress: this.achievement.progress,
      })

      this.$emit('view-progress', this.achievement)
    },

    /**
     * 图片加载错误处理
     */
    onImageError(event) {
      event.target.src = '/icons/default-achievement.png'
    },

    /**
     * 获取成就类型样式类
     */
    getTypeClass(type) {
      const typeMap = {
        LOGIN: 'type-login',
        LEARNING: 'type-learning',
        SOCIAL: 'type-social',
        TIME: 'type-time',
        CUSTOM: 'type-custom',
      }
      return typeMap[type] || 'type-default'
    },

    /**
     * 获取成就类型标签
     */
    getTypeLabel(type) {
      const labelMap = {
        LOGIN: '登录',
        LEARNING: '学习',
        SOCIAL: '社交',
        TIME: '时长',
        CUSTOM: '特殊',
      }
      return labelMap[type] || '其他'
    },

    /**
     * 格式化解锁时间
     */
    formatUnlockTime(unlockTime) {
      if (!unlockTime) return ''

      const date = new Date(unlockTime)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天解锁'
      } else if (diffDays === 1) {
        return '昨天解锁'
      } else if (diffDays < 7) {
        return `${diffDays}天前解锁`
      } else {
        return date.toLocaleDateString()
      }
    },
  },
}
</script>

<style scoped>
.achievement-card {
  background: #fff;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.achievement-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.achievement-card.completed {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
}

.achievement-card.in-progress {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
}

.achievement-icon {
  position: relative;
  text-align: center;
  margin-bottom: 15px;
}

.achievement-icon img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.completed-badge {
  position: absolute;
  top: -5px;
  right: 50%;
  transform: translateX(50%);
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.achievement-info {
  text-align: center;
}

.achievement-name {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #333;
}

.achievement-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.achievement-type {
  margin-bottom: 15px;
}

.type-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-login {
  background: #e3f2fd;
  color: #1976d2;
}
.type-learning {
  background: #f3e5f5;
  color: #7b1fa2;
}
.type-social {
  background: #e8f5e8;
  color: #388e3c;
}
.type-time {
  background: #fff3e0;
  color: #f57c00;
}
.type-custom {
  background: #fce4ec;
  color: #c2185b;
}
.type-default {
  background: #f5f5f5;
  color: #757575;
}

.progress-section {
  margin-bottom: 15px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.progress-percent {
  font-weight: 500;
  color: #007bff;
}

.reward-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.reward-points {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #ff9800;
}

.unlock-time {
  font-size: 12px;
  color: #999;
}

.achievement-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notify-btn {
  background: #28a745;
  color: white;
}

.notify-btn:hover {
  background: #218838;
}

.progress-btn {
  background: #007bff;
  color: white;
}

.progress-btn:hover {
  background: #0056b3;
}

/* 图标样式 */
.icon-check::before {
  content: '✓';
}

.icon-star::before {
  content: '★';
}
</style>
