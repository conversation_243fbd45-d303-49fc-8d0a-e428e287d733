<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  getAllBadges,
  getPinnedBadgeIds,
  toggleBadgePin,
  isBadgePinned,
  BADGE_CATEGORIES,
  getBadgeStats,
  type Badge,
} from '../service/achievement'
// @ts-ignore
import CategoryFilter from '@/components/CategoryFilter.vue'

/**
 * @description 徽章管理组件
 * <AUTHOR>
 */

interface Props {
  /**
   * @description 是否显示置顶设置按钮
   */
  showPinControls?: boolean
  /**
   * @description 每行显示的徽章数量
   */
  columnsPerRow?: number
  /**
   * @description 是否显示分类筛选
   */
  showCategoryFilter?: boolean
  /**
   * @description 是否显示统计信息
   */
  showStats?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPinControls: true,
  columnsPerRow: 3,
  showCategoryFilter: true,
  showStats: true,
})

const emit = defineEmits<{
  'badge-click': [badge: Badge]
  'pin-changed': [badgeId: string, isPinned: boolean]
}>()

// 响应式数据
const badges = ref<Badge[]>([])
const pinnedIds = ref<string[]>([])
const selectedCategory = ref<string>('all')
const searchKeyword = ref<string>('')
const stats = ref<any>({})

/**
 * @description 获取图标类名
 */
const getIconClass = (icon: string): string => {
  const iconMap: Record<string, string> = {
    trophy: 'i-fa-solid-trophy',
    fire: 'i-fa-solid-fire',
    star: 'i-fa-solid-star',
    medal: 'i-fa-solid-medal',
    crown: 'i-fa-solid-crown',
    gem: 'i-fa-solid-gem',
    rocket: 'i-fa-solid-rocket',
    graduation: 'i-fa-solid-user-graduate',
    book: 'i-fa-solid-book',
    lightbulb: 'i-fa-solid-lightbulb',
    target: 'i-fa-solid-bullseye',
    certificate: 'i-fa-solid-certificate',
  }
  return iconMap[icon] || 'i-fa-solid-award'
}

/**
 * @description 过滤后的徽章列表
 */
const filteredBadges = computed(() => {
  let result = badges.value

  // 分类筛选
  if (selectedCategory.value !== 'all') {
    result = result.filter((badge) => badge.category === selectedCategory.value)
  }

  // 关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (badge) =>
        badge.title.toLowerCase().includes(keyword) || badge.desc.toLowerCase().includes(keyword),
    )
  }

  // 排序：已解锁优先，置顶优先
  return result.sort((a, b) => {
    // 先按解锁状态排序
    if (a.unlocked !== b.unlocked) {
      return a.unlocked ? -1 : 1
    }
    // 再按置顶状态排序
    const aIsPinned = pinnedIds.value.includes(a.id)
    const bIsPinned = pinnedIds.value.includes(b.id)
    if (aIsPinned !== bIsPinned) {
      return aIsPinned ? -1 : 1
    }
    // 最后按ID排序
    return parseInt(a.id) - parseInt(b.id)
  })
})

/**
 * @description 分类选项
 */
const categoryOptions = computed(() => [
  { key: 'all', name: '全部', count: badges.value.length, icon: 'i-fa-solid-th-large' },
  ...Object.entries(BADGE_CATEGORIES).map(([key, name]) => ({
    key,
    name,
    count: badges.value.filter((badge) => badge.category === key).length,
    icon: getCategoryIcon(key),
  })),
])

/**
 * @description 获取分类图标
 */
const getCategoryIcon = (category: string): string => {
  const iconMap: Record<string, string> = {
    interview: 'i-fa-solid-user-tie',
    study: 'i-fa-solid-book',
    achievement: 'i-fa-solid-trophy',
    skill: 'i-fa-solid-cogs',
  }
  return iconMap[category] || 'i-fa-solid-tag'
}

/**
 * @description 处理徽章点击
 */
const onBadgeClick = (badge: Badge) => {
  emit('badge-click', badge)
}

/**
 * @description 处理徽章长按事件
 * @param badge 长按的徽章
 */
const onBadgeLongPress = (badge: Badge) => {
  if (!badge.unlocked) {
    uni.showToast({
      title: '只能设置已解锁的徽章',
      icon: 'none',
    })
    return
  }

  const isPinned = pinnedIds.value.includes(badge.id)
  const actionItems = [isPinned ? '取消置顶' : '设为置顶', '查看详情']

  uni.showActionSheet({
    itemList: actionItems,
    itemColor: '#333',
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          // 切换置顶状态
          togglePinStatus(badge)
          break
        case 1:
          // 显示详情
          emit('badge-click', badge)
          break
      }
    },
    fail: (res) => {
      console.log('用户取消操作')
    },
  })
}

/**
 * @description 切换置顶状态
 * @param badge 徽章对象
 */
const togglePinStatus = (badge: Badge) => {
  const success = toggleBadgePin(badge.id)
  if (success) {
    loadPinnedIds()
    const isPinned = pinnedIds.value.includes(badge.id)
    emit('pin-changed', badge.id, isPinned)

    uni.showToast({
      title: isPinned ? '已设为置顶' : '已取消置顶',
      icon: 'success',
      duration: 1500,
    })
  } else {
    const isPinned = pinnedIds.value.includes(badge.id)
    uni.showToast({
      title: isPinned ? '取消置顶失败' : '设置置顶失败',
      icon: 'error',
    })
  }
}

/**
 * @description 处理置顶切换（保留原方法用于兼容）
 */
const onTogglePin = (badge: Badge, event: Event) => {
  event.stopPropagation()
  togglePinStatus(badge)
}

/**
 * @description 加载数据
 */
const loadData = () => {
  badges.value = getAllBadges()
  loadPinnedIds()
  stats.value = getBadgeStats()
}

/**
 * @description 加载置顶徽章ID
 */
const loadPinnedIds = () => {
  pinnedIds.value = getPinnedBadgeIds()
}

/**
 * @description 获取稀有度标签
 */
const getRarityLabel = (rarity: Badge['rarity']): string => {
  const rarityMap = {
    common: '普通',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
  }
  return rarityMap[rarity || 'common']
}

/**
 * @description 获取稀有度颜色
 */
const getRarityColor = (rarity: Badge['rarity']): string => {
  const colorMap = {
    common: '#9CA3AF',
    rare: '#3B82F6',
    epic: '#7C3AED',
    legendary: '#F59E0B',
  }
  return colorMap[rarity || 'common']
}

// 生命周期
onMounted(() => {
  loadData()
})

// 暴露方法给父组件
defineExpose({
  loadData,
  loadPinnedIds,
})
</script>

<template>
  <view class="badge-manager">
    <!-- 统计信息 -->
    <view v-if="showStats" class="stats-section">
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{ stats.unlocked || 0 }}</text>
          <text class="stat-label">已获得</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ stats.total || 0 }}</text>
          <text class="stat-label">总成就</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ stats.progress || 0 }}%</text>
          <text class="stat-label">完成度</text>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-box">
        <text class="i-fa-solid-search search-icon"></text>
        <input
          v-model="searchKeyword"
          class="search-input"
          placeholder="搜索徽章..."
          placeholder-style="color: #999"
        />
        <text
          v-if="searchKeyword"
          class="i-fa-solid-times clear-icon"
          @click="searchKeyword = ''"
        ></text>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view v-if="showCategoryFilter" class="category-section">
      <CategoryFilter
        title="分类筛选"
        :category-options="categoryOptions"
        :selected-category="selectedCategory"
        @change="selectedCategory = $event"
        @reset="selectedCategory = 'all'"
      />
    </view>

    <!-- 徽章网格 -->
    <view class="badges-section">
      <view class="badges-grid" :style="`grid-template-columns: repeat(${columnsPerRow}, 1fr)`">
        <view
          v-for="badge in filteredBadges"
          :key="badge.id"
          class="badge-item"
          :class="{
            locked: !badge.unlocked,
            pinned: pinnedIds.includes(badge.id),
          }"
          @click="onBadgeClick(badge)"
          @longpress="onBadgeLongPress(badge)"
        >
          <!-- 置顶标识 -->
          <view v-if="pinnedIds.includes(badge.id)" class="pin-indicator">
            <text class="i-fa-solid-thumbtack pin-icon"></text>
          </view>

          <!-- 稀有度标识 -->
          <view
            class="rarity-indicator"
            :style="`background-color: ${getRarityColor(badge.rarity)}`"
          >
            <text class="rarity-text">{{ getRarityLabel(badge.rarity) }}</text>
          </view>

          <view class="badge-icon-wrapper">
            <view class="badge-icon" :class="badge.color">
              <text :class="getIconClass(badge.icon)" style="font-size: 32rpx; color: #fff"></text>
            </view>
            <!-- 未解锁遮罩 -->
            <view v-if="!badge.unlocked" class="lock-overlay">
              <text class="i-fa-solid-lock" style="font-size: 24rpx; color: #999"></text>
            </view>
          </view>

          <text class="badge-title">{{ badge.title }}</text>
          <text class="badge-desc">{{ badge.desc }}</text>

          <!-- 解锁时间 -->
          <text v-if="badge.unlocked && badge.unlockedAt" class="unlock-time">
            {{ badge.unlockedAt }}
          </text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredBadges.length === 0" class="empty-state">
        <text class="i-fa-solid-trophy empty-icon"></text>
        <text class="empty-text">暂无相关徽章</text>
        <text class="empty-desc">尝试更换搜索条件或分类</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.badge-manager {
  width: 100%;
}

// 统计信息
.stats-section {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.08);
}

.stats-grid {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  display: block;
  margin-bottom: 8rpx;
  font-size: 40rpx;
  font-weight: bold;
  color: #00c9a7;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 1rpx;
  height: 48rpx;
  margin: 0 32rpx;
  background: #e0e0e0;
}

// 搜索栏
.search-section {
  margin-bottom: 20rpx;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 40rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;

  &:focus-within {
    background: #fff;
    border-color: #00c9a7;
    box-shadow: 0 0 0 6rpx rgba(0, 201, 167, 0.1);
  }
}

.search-icon {
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #999;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #999;
  transition: color 0.3s;

  &:active {
    color: #666;
  }
}

// 分类筛选区域（现在使用CategoryFilter组件）
.category-section {
  margin-bottom: 24rpx;
}

// 徽章网格
.badges-section {
  min-height: 300rpx;
}

.badges-grid {
  display: grid;
  gap: 20rpx;
}

// 徽章项
.badge-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 180rpx;
  padding: 20rpx 12rpx 12rpx 12rpx;
  text-align: center;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s;
  overflow: hidden;

  &:active:not(.locked) {
    background: #f5faff;
    transform: scale(0.95);
  }

  &.locked {
    opacity: 0.6;
  }

  &.pinned {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border: 2rpx solid #fbbf24;
  }
}

// 置顶标识
.pin-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background: #fbbf24;
  border-radius: 50%;
  z-index: 2;
}

.pin-icon {
  font-size: 16rpx;
  color: #fff;
}

// 稀有度标识
.rarity-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  z-index: 2;
}

.rarity-text {
  font-size: 18rpx;
  color: #fff;
  font-weight: 500;
}

// 徽章图标容器
.badge-icon-wrapper {
  position: relative;
  margin-bottom: 12rpx;
}

.badge-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  box-shadow: 0 6rpx 14rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s;

  .badge-item:active:not(.locked) & {
    transform: scale(0.9);
  }
}

// 徽章颜色
.badge-gold {
  background: linear-gradient(135deg, #ffd700, #ffc300);
}

.badge-silver {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
}

.badge-bronze {
  background: linear-gradient(135deg, #cd7f32, #b87333);
}

.badge-blue {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.badge-green {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.badge-purple {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.badge-red {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.badge-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

// 锁定遮罩
.lock-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}

// 徽章文本
.badge-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #222;
  line-height: 1.2;
}

.badge-desc {
  font-size: 20rpx;
  line-height: 1.4;
  color: #666;
  margin-bottom: 8rpx;
}

.unlock-time {
  font-size: 18rpx;
  color: #999;
  margin-bottom: 16rpx;
}

// 置顶控制按钮已移除，现在使用长按交互

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300rpx;
  text-align: center;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .badges-grid {
    gap: 16rpx;
  }

  .badge-item {
    min-height: 160rpx;
    padding: 16rpx 10rpx 10rpx 10rpx;
  }

  .badge-icon {
    width: 60rpx;
    height: 60rpx;
  }
}

.empty-icon {
  margin-bottom: 24rpx;
  font-size: 80rpx;
  color: #d1d5db;
}

.empty-text {
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #6b7280;
}

.empty-desc {
  font-size: 24rpx;
  color: #9ca3af;
}
</style>
