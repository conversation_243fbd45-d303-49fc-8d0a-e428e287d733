<script lang="ts">
import { ref, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'

/**
 * <AUTHOR>
 * @description 底部导航栏组件
 */

// 底部导航栏组件
export default {
  name: 'TabBar',
  props: {
    activeTab: {
      type: String,
      default: 'home',
    },
    // 是否显示徽章
    showBadge: {
      type: Boolean,
      default: true,
    },
    // 徽章数据
    badgeData: {
      type: Object,
      default: () => ({}),
    },
    // 是否启用震动反馈
    enableVibrate: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['tabChange', 'tabLongPress'],
  setup(props, { emit }) {
    // 当前激活的tab
    const currentTab = ref(props.activeTab)

    // 是否正在切换
    const isSwitching = ref(false)

    // 长按定时器
    const longPressTimer = ref<number | null>(null)

    // tab配置数据 - 参考center.vue的颜色风格，与pages.json保持一致
    const tabConfig = ref([
      {
        key: 'home',
        name: '首页',
        icon: 'i-fa-solid-home',
        activeIcon: 'i-fa-solid-home',
        path: '/pages/index/index',
        isTabPage: true, // 这是tabBar页面
        color: '#00C9A7', // 使用center.vue的主题色
        bgColor: 'linear-gradient(135deg, #E0F2FE, #BFDBFE)',
        description: '智能面试助手',
      },
      {
        key: 'interview',
        name: '面试',
        icon: 'i-fa-solid-video',
        activeIcon: 'i-fa-solid-video',
        path: '/pages/interview/index',
        isTabPage: false,
        color: '#00C9A7',
        bgColor: 'linear-gradient(135deg, #EDE9FE, #DDD6FE)',
        description: '模拟面试',
      },
      {
        key: 'resources',
        name: '资源',
        icon: 'i-fa-solid-book',
        activeIcon: 'i-fa-solid-book-open',
        path: '/pages/learning/index',
        isTabPage: false,
        color: '#00C9A7',
        bgColor: 'linear-gradient(135deg, #DCFCE7, #BBF7D0)',
        description: '学习资源推荐',
      },
      // {
      //   key: 'aichat',
      //   name: 'AI助手',
      //   icon: 'i-fa-solid-robot',
      //   activeIcon: 'i-fa-solid-comments',
      //   path: '/pages/aichat/index',
      //   isTabPage: false,
      //   color: '#00C9A7',
      //   bgColor: 'linear-gradient(135deg, #FFEDD5, #FED7AA)',
      //   description: '智能AI',
      // },
      {
        key: 'profile',
        name: '我的',
        icon: 'i-fa-solid-user',
        activeIcon: 'i-fa-solid-user-circle',
        path: '/pages/user/center',
        isTabPage: false,
        color: '#00C9A7',
        bgColor: 'linear-gradient(135deg, #FEE2E2, #FECACA)',
        description: '个人中心',
      },
    ])

    // 计算当前页面路径
    const getCurrentPath = () => {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        return `/${currentPage.route}`
      }
      return ''
    }

    // 根据当前路径更新激活状态
    const updateActiveTab = () => {
      const currentPath = getCurrentPath()
      const matchedTab = tabConfig.value.find(
        (tab) =>
          currentPath.includes(tab.path) ||
          // 兼容旧的index页面路径
          (tab.key === 'home' && (currentPath === '/pages/index/index' || currentPath === '/')),
      )
      if (matchedTab) {
        currentTab.value = matchedTab.key
      }
    }

    // 获取徽章数量
    const getBadgeCount = (tabKey: string) => {
      return props.badgeData[tabKey] || 0
    }

    // 是否显示徽章
    const shouldShowBadge = (tabKey: string) => {
      return props.showBadge && getBadgeCount(tabKey) > 0
    }

    // 格式化徽章数字
    const formatBadgeCount = (count: number) => {
      if (count > 99) return '99+'
      return count.toString()
    }

    // 触发震动反馈
    const triggerVibrate = () => {
      if (!props.enableVibrate) return
      // #ifdef APP-PLUS || MP
      uni.vibrateShort({
        type: 'light',
      })
      // #endif
    }

    // 切换tab
    const switchTab = (tabKey: string) => {
      if (isSwitching.value || currentTab.value === tabKey) return

      isSwitching.value = true
      const targetTab = tabConfig.value.find((tab) => tab.key === tabKey)

      if (!targetTab) {
        isSwitching.value = false
        return
      }

      // 触发反馈
      triggerVibrate()

      // 更新当前tab
      currentTab.value = tabKey

      // 发送事件
      emit('tabChange', tabKey)

      // 页面跳转
      setTimeout(() => {
        try {
          if (targetTab.isTabPage) {
            uni.switchTab({ url: targetTab.path })
          } else {
            uni.navigateTo({ url: targetTab.path })
          }
        } catch (error) {
          try {
            uni.navigateTo({ url: targetTab.path })
          } catch (error) {
            console.error('页面跳转失败:', error)
          }
        }
        isSwitching.value = false
      }, 200)
    }

    // 长按事件开始
    const onTouchStart = (tabKey: string) => {
      longPressTimer.value = setTimeout(() => {
        const targetTab = tabConfig.value.find((tab) => tab.key === tabKey)
        if (targetTab) {
          // 触发震动
          // #ifdef APP-PLUS || MP-WEIXIN
          uni.vibrateLong()
          // #endif

          // 显示描述信息
          uni.showToast({
            title: targetTab.description,
            icon: 'none',
            duration: 2000,
          })

          // 发送长按事件
          emit('tabLongPress', tabKey)
        }
      }, 800)
    }

    // 长按事件结束
    const onTouchEnd = () => {
      if (longPressTimer.value) {
        clearTimeout(longPressTimer.value)
        longPressTimer.value = null
      }
    }

    // 监听页面显示
    onShow(() => {
      updateActiveTab()
      // uni.preloadPage({
      //   url: '/pages/learning/data-center',
      // })
      // 预加载底部的页面
      uni.preloadPage({
        url: '/pages/interview/index',
      })
      uni.preloadPage({
        url: '/pages/learning/index',
      })
      uni.preloadPage({
        url: '/pages/user/center',
      })
    })

    return {
      currentTab,
      isSwitching,
      tabConfig,
      switchTab,
      onTouchStart,
      onTouchEnd,
      getBadgeCount,
      shouldShowBadge,
      formatBadgeCount,
    }
  },
}
</script>

<template>
  <view class="tabbar">
    <!-- 背景装饰元素 -->
    <view class="tabbar__bg-deco deco1"></view>
    <view class="tabbar__bg-deco deco2"></view>

    <view
      v-for="tab in tabConfig"
      :key="tab.key"
      class="tabbar-item"
      :class="{
        active: currentTab === tab.key,
        switching: isSwitching && currentTab === tab.key,
      }"
      @click="switchTab(tab.key)"
      @touchstart="onTouchStart(tab.key)"
      @touchend="onTouchEnd"
      @touchcancel="onTouchEnd"
    >
      <!-- 图标容器 -->
      <view class="icon-container">
        <!-- 激活背景 -->
        <view
          v-if="currentTab === tab.key"
          class="active-bg"
          :style="{ background: tab.bgColor }"
        ></view>

        <text
          :class="[currentTab === tab.key ? tab.activeIcon : tab.icon]"
          class="tab-icon"
          :style="{
            color: currentTab === tab.key ? tab.color : '#bbb',
          }"
        ></text>

        <!-- 徽章 -->
        <view v-if="shouldShowBadge(tab.key)" class="badge" :style="{ backgroundColor: tab.color }">
          <text class="badge-text">{{ formatBadgeCount(getBadgeCount(tab.key)) }}</text>
        </view>

        <!-- 激活指示器 -->
        <view
          v-if="currentTab === tab.key"
          class="active-indicator"
          :style="{ backgroundColor: tab.color }"
        ></view>
      </view>

      <!-- 文字标签 -->
      <text
        class="tabbar-text"
        :class="{ active: currentTab === tab.key }"
        :style="{ color: currentTab === tab.key ? tab.color : '#888' }"
      >
        {{ tab.name }}
      </text>

      <!-- 点击波纹效果 -->
      <view
        v-if="isSwitching && currentTab === tab.key"
        class="ripple-effect"
        :style="{ backgroundColor: `${tab.color}20` }"
      ></view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 底部导航栏 - 毛玻璃效果风格，与HeadBar保持一致
.tabbar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 120rpx;
  padding-bottom: env(safe-area-inset-bottom);
  overflow: hidden;

  // 毛玻璃背景效果
  background: rgba(255, 255, 255, 0.85);
  -webkit-backdrop-filter: blur(25rpx) saturate(180%);
  backdrop-filter: blur(25rpx) saturate(180%);
  border-top: 1rpx solid rgba(0, 201, 167, 0.2);
  box-shadow:
    0 -4rpx 20rpx rgba(0, 201, 167, 0.15),
    0 -8rpx 40rpx rgba(0, 201, 167, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);

  // 添加毛玻璃渐变叠加层
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
    content: '';
    background: linear-gradient(
      180deg,
      rgba(0, 201, 167, 0.1) 0%,
      rgba(79, 209, 199, 0.05) 50%,
      transparent 100%
    );
  }

  // 背景装饰元素 - 增强毛玻璃效果
  &__bg-deco {
    position: absolute;
    z-index: 2;
    background: rgba(0, 201, 167, 0.12);
    -webkit-backdrop-filter: blur(10rpx);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(0, 201, 167, 0.2);
    border-radius: 50%;
    box-shadow:
      0 4rpx 15rpx rgba(0, 201, 167, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.3);

    &.deco1 {
      top: -20rpx;
      right: 60rpx;
      width: 80rpx;
      height: 80rpx;
      animation: floating 6s ease-in-out infinite;
    }

    &.deco2 {
      bottom: -15rpx;
      left: 80rpx;
      width: 50rpx;
      height: 50rpx;
      animation: floating 5s ease-in-out infinite reverse;
    }
  }

  // 支持暗黑模式
  @media (prefers-color-scheme: dark) {
    background: rgba(20, 20, 20, 0.9);
    border-top-color: rgba(255, 255, 255, 0.15);
    box-shadow:
      0 -4rpx 20rpx rgba(0, 0, 0, 0.3),
      0 -8rpx 40rpx rgba(0, 0, 0, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.1);

    &::before {
      background: linear-gradient(
        180deg,
        rgba(0, 201, 167, 0.15) 0%,
        rgba(79, 209, 199, 0.08) 50%,
        transparent 100%
      );
    }
  }
}

.tabbar-item {
  position: relative;
  z-index: 3;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 8rpx;
  margin: 0 6rpx;
  overflow: hidden;
  border-radius: 24rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  // 点击态效果 - 毛玻璃风格
  &:active {
    background: rgba(0, 201, 167, 0.08);
    -webkit-backdrop-filter: blur(10rpx);
    backdrop-filter: blur(10rpx);
    transform: scale(0.95) translateY(-2rpx);
  }

  // 激活状态 - 毛玻璃背景
  &.active {
    background: rgba(0, 201, 167, 0.15);
    -webkit-backdrop-filter: blur(15rpx) saturate(150%);
    backdrop-filter: blur(15rpx) saturate(150%);
    border: 1rpx solid rgba(0, 201, 167, 0.3);
    box-shadow:
      0 4rpx 20rpx rgba(0, 201, 167, 0.25),
      0 8rpx 40rpx rgba(0, 201, 167, 0.15),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
    transform: translateY(-4rpx);

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      pointer-events: none;
      content: '';
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.2) 0%, rgba(79, 209, 199, 0.1) 100%);
      border-radius: inherit;
    }

    .icon-container {
      filter: drop-shadow(0 2rpx 8rpx rgba(0, 201, 167, 0.3));
      transform: scale(1.15);
    }
  }

  // 切换动画
  &.switching {
    .icon-container {
      animation: tabBounce 0.6s ease-in-out;
    }
  }

  // 悬停效果（H5端）
  @media (hover: hover) {
    &:hover:not(.active) {
      background: rgba(0, 201, 167, 0.05);
      -webkit-backdrop-filter: blur(8rpx);
      backdrop-filter: blur(8rpx);
      border: 1rpx solid rgba(0, 201, 167, 0.1);
      box-shadow: 0 2rpx 10rpx rgba(0, 201, 167, 0.1);
      transform: translateY(-2rpx);
    }
  }

  // 暗黑模式适配
  @media (prefers-color-scheme: dark) {
    &.active {
      background: rgba(0, 201, 167, 0.2);
      border-color: rgba(0, 201, 167, 0.4);
      box-shadow:
        0 4rpx 20rpx rgba(0, 201, 167, 0.3),
        0 8rpx 40rpx rgba(0, 201, 167, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
    }

    &:hover:not(.active) {
      background: rgba(0, 201, 167, 0.08);
      border-color: rgba(0, 201, 167, 0.15);
    }
  }
}

.icon-container {
  position: relative;
  z-index: 4;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 8rpx;
  border-radius: 16rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  // 激活状态的图标容器
  .tabbar-item.active & {
    background: rgba(255, 255, 255, 0.2);
    -webkit-backdrop-filter: blur(8rpx);
    backdrop-filter: blur(8rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 2rpx 8rpx rgba(0, 201, 167, 0.2),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
  }

  // 暗黑模式适配
  @media (prefers-color-scheme: dark) {
    .tabbar-item.active & {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.active-bg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  opacity: 0.8;
  animation: activeBgPulse 2s ease-in-out infinite;
}

.tab-icon {
  position: relative;
  z-index: 2;
  display: block;
  font-size: 44rpx;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  -webkit-backdrop-filter: blur(8rpx) saturate(150%);
  backdrop-filter: blur(8rpx) saturate(150%);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  box-shadow:
    0 4rpx 15rpx rgba(255, 107, 107, 0.5),
    0 2rpx 8rpx rgba(255, 107, 107, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  animation: badgePulse 2s infinite;

  // 添加毛玻璃光泽效果
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    pointer-events: none;
    content: '';
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: inherit;
  }
}

.badge-text {
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
}

.active-indicator {
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  width: 32rpx;
  height: 6rpx;
  border-radius: 3rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transform: translateX(-50%);
  animation: indicatorSlide 0.4s ease-out;
}

.tabbar-text {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  letter-spacing: 0.5rpx;
  transition: all 0.3s ease;

  &.active {
    font-weight: 600;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
  }

  // 悬停效果（H5端）
  @media (hover: hover) {
    .tabbar-item:hover:not(.active) & {
      color: #00c9a7;
      text-shadow: 0 1rpx 2rpx rgba(0, 201, 167, 0.2);
    }
  }

  // 支持暗黑模式
  @media (prefers-color-scheme: dark) {
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);

    &.active {
      text-shadow:
        0 1rpx 3rpx rgba(79, 209, 199, 0.4),
        0 0 8rpx rgba(79, 209, 199, 0.3);
    }

    .tabbar-item:hover:not(.active) & {
      color: #4fd1c7;
      text-shadow: 0 1rpx 2rpx rgba(79, 209, 199, 0.3);
    }
  }
}

.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

// 动画定义 - 参考center.vue的动画风格
@keyframes floating {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-8rpx) rotate(3deg);
  }
}

@keyframes tabBounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: scale(1);
  }
  40%,
  43% {
    transform: scale(1.2);
  }
  70% {
    transform: scale(1.1);
  }
  90% {
    transform: scale(1.05);
  }
}

@keyframes activeBgPulse {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05);
  }
}

@keyframes badgePulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes indicatorSlide {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 32rpx;
    opacity: 1;
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 140rpx;
    height: 140rpx;
    opacity: 0;
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .tabbar {
    height: 110rpx;
  }

  .tab-icon {
    font-size: 40rpx !important;
  }

  .tabbar-text {
    font-size: 20rpx;
  }
}

// H5端特殊样式 - 增强毛玻璃效果
/* #ifdef H5 */
.tabbar {
  -webkit-backdrop-filter: blur(35rpx) saturate(200%);
  backdrop-filter: blur(35rpx) saturate(200%);
  border-top: 1rpx solid rgba(0, 201, 167, 0.25);
  box-shadow:
    0 -6rpx 25rpx rgba(0, 201, 167, 0.2),
    0 -12rpx 50rpx rgba(0, 201, 167, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);

  // 添加H5端渐变遮罩
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
    content: '';
    background: linear-gradient(
      180deg,
      rgba(0, 201, 167, 0.08) 0%,
      rgba(79, 209, 199, 0.04) 50%,
      transparent 100%
    );
  }

  .tabbar-item {
    &:hover:not(.active) {
      background: rgba(0, 201, 167, 0.1);
      -webkit-backdrop-filter: blur(12rpx) saturate(150%);
      backdrop-filter: blur(12rpx) saturate(150%);
      border: 1rpx solid rgba(0, 201, 167, 0.2);
      border-radius: 20rpx;
      box-shadow:
        0 4rpx 15rpx rgba(0, 201, 167, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
      transform: translateY(-3rpx);
    }

    &.active:hover {
      background: rgba(0, 201, 167, 0.2);
      -webkit-backdrop-filter: blur(20rpx) saturate(180%);
      backdrop-filter: blur(20rpx) saturate(180%);
      box-shadow:
        0 6rpx 25rpx rgba(0, 201, 167, 0.3),
        0 12rpx 50rpx rgba(0, 201, 167, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
    }
  }
}
/* #endif */

// 小程序端特殊样式 - 毛玻璃风格
/* #ifdef MP */
.tabbar {
  .tabbar-item {
    // 确保点击区域足够大
    min-height: 90rpx;

    &:active {
      background: rgba(0, 201, 167, 0.12);
      -webkit-backdrop-filter: blur(10rpx);
      backdrop-filter: blur(10rpx);
      border: 1rpx solid rgba(0, 201, 167, 0.2);
      border-radius: 20rpx;
      box-shadow:
        0 2rpx 10rpx rgba(0, 201, 167, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
    }
  }
}
/* #endif */
</style>
