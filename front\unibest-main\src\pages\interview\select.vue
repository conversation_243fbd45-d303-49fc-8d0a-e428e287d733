<template>
  <view class="select-container">
    <!-- 顶部导航栏 -->
    <HeadBar title="面试岗位选择" :show-back="true" :show-right-button="true" right-icon="i-mdi-tune-vertical" right-text=""
      :right-text-width="64" :right-button-height="64" @right-click="openRightWindow" />

    <!-- 主要内容区域 -->
    <!-- @ts-ignore -->
    <scroll-view class="main-content" scroll-y :show-scrollbar="false" :refresher-enabled="true"
      :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore">
      <!-- 全局加载状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-content">
          <text class="i-mdi-loading loading-icon"></text>
          <text class="loading-text">正在加载数据...</text>
        </view>
      </view>

      <!-- 主要内容 -->
      <view v-else>
        <!-- 欢迎横幅 -->
        <view class="welcome-banner">
          <view class="banner-content">
            <view class="banner-text">
              <text class="banner-title">AI 智能面试助手</text>
              <text class="banner-subtitle">专业模拟面试，提升求职竞争力</text>
            </view>
          </view>
        </view>

        <!-- 智能搜索区域 -->
        <view class="search-section">
          <view class="search-container">
            <view class="search-input-wrapper">
              <text class="i-mdi-magnify search-icon"></text>
              <input class="search-input" placeholder="搜索职位、公司或技能标签" v-model="searchKeyword"
                placeholder-class="search-placeholder" @input="handleSearchInput" @focus="handleSearchFocus"
                @blur="handleSearchBlur" />
              <text v-if="searchKeyword" class="i-mdi-close clear-icon" @click="clearSearch"></text>
            </view>
            <view class="search-suggestions" v-if="showSearchSuggestions && searchSuggestions.length > 0">
              <!-- 优化建议标签容器 - 支持同行显示和自动换行 -->
              <view class="suggestion-tags-container">
                <view class="suggestion-item" v-for="suggestion in searchSuggestions" :key="suggestion.text"
                  @click="selectSearchSuggestion(suggestion)">
                  <!-- 建议标签卡片 -->
                  <view class="suggestion-card">
                    <text class="suggestion-text">{{ suggestion.text }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

        </view>

        <!-- 职位列表区域 -->
        <view class="section-container job-section">
          <view class="job-list-header" v-if="filteredJobs.length > 0">
            <view class="header-left">
              <text class="result-count">共找到 {{ filteredJobs.length }} 个职位</text>
              <text class="result-tip">为您推荐最匹配的岗位</text>
            </view>
            <view class="header-right">
              <view class="sort-selector" @click="openSortModal">
                <text class="i-mdi-sort-variant sort-icon"></text>
                <text class="sort-text">{{ getCurrentSortOption.name }}</text>
                <text class="i-mdi-chevron-down"></text>
              </view>
            </view>
          </view>

          <!-- 空状态优化 -->
          <view v-if="filteredJobs.length === 0" class="empty-state">
            <view class="empty-illustration">
              <text class="i-mdi-briefcase-search-outline empty-icon"></text>
              <view class="empty-decoration"></view>
            </view>
            <text class="empty-title">暂无相关职位</text>
            <text class="empty-subtitle">试试调整搜索条件或选择其他分类</text>
            <button class="empty-action" @click="clearSearch">
              <text class="i-mdi-refresh"></text>
              <text>重新搜索</text>
            </button>
          </view>

          <!-- 职位卡片列表 -->
          <view class="job-list">
            <view class="job-card" v-for="(job, index) in filteredJobs" :key="job.id">
              <!-- 职位卡片头部 -->
              <view class="job-card-header">
                <view class="job-main">
                  <view class="job-title-row">
                    <text class="job-name">{{ job.name }}</text>
                    <view class="job-hot-badge" v-if="index < 3">
                      <text class="i-mdi-fire"></text>
                      <text>热门</text>
                    </view>
                  </view>
                  <view class="company-row">
                    <view class="company-info">
                      <text class="i-mdi-domain company-icon"></text>
                      <text class="company-name">{{ job.company }}</text>
                    </view>
                    <view class="job-stats-mini">
                      <text class="stat">{{ job.interviewers }}人面试</text>
                      <text class="stat success">{{ job.passRate }}通过</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 职位描述 -->
              <view class="job-description">
                <text class="description-text">{{ job.description }}</text>
              </view>

              <!-- 职位详细信息 -->
              <view class="job-details">
                <view class="detail-row">
                  <view class="detail-item">
                    <text class="i-mdi-gauge detail-icon"></text>
                    <text class="detail-label">难度</text>
                    <view class="difficulty-display">
                      <text class="difficulty-stars" :style="{ color: getDifficultyColor(job.difficulty) }">
                        {{ renderStars(job.difficulty) }}
                      </text>
                    </view>
                  </view>
                  <view class="detail-item">
                    <text class="i-mdi-clock-outline detail-icon"></text>
                    <text class="detail-label">时长</text>
                    <text class="detail-value">{{ job.duration }}分钟</text>
                  </view>
                  <view class="detail-item">
                    <text class="i-mdi-help-circle-outline detail-icon"></text>
                    <text class="detail-label">题目</text>
                    <text class="detail-value">{{ job.questionCount }}题</text>
                  </view>
                </view>
              </view>

              <!-- 技能标签 -->
              <view class="job-tags">
                <text class="tag" v-for="(tag, tagIndex) in job.tags" :key="tagIndex">
                  {{ tag }}
                </text>
              </view>

              <!-- 操作区域 -->
              <view class="job-actions">
                <button class="action-btn favorite-btn" @click="toggleFavorite(job.id, job.isFavorited || false)">
                  <text :class="job.isFavorited ? 'i-mdi-heart' : 'i-mdi-heart-outline'"
                    :style="{ color: job.isFavorited ? '#ff4757' : '#666' }"></text>
                  <text>{{ job.isFavorited ? '已收藏' : '收藏' }}</text>
                </button>
                <button class="action-btn secondary-btn" @click="viewJobDetail(job.id)">
                  <text class="i-mdi-information-outline"></text>
                  <text>查看详情</text>
                </button>
                <button class="action-btn primary-btn" @click="startInterview(job.id)">
                  <text class="i-mdi-play-circle"></text>
                  <text>开始面试</text>
                </button>
              </view>

              <!-- 推荐理由 -->
              <view class="recommendation-reason" v-if="index < 2">
                <text class="i-mdi-lightbulb-outline reason-icon"></text>
                <text class="reason-text">基于您的背景，这是一个很好的匹配岗位</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 主要内容结束 -->

      <!-- 加载更多状态 -->
      <view class="load-more-status" v-if="!loading">
        <view v-if="loadingJobs" class="loading-more">
          <text class="loading-icon i-mdi-loading"></text>
          <text class="loading-text">加载更多数据...</text>
        </view>
        <view v-else-if="!pageInfo.hasMore" class="no-more-data">
          <text class="no-more-text">-- 已加载全部数据 --</text>
        </view>
      </view>
    </scroll-view>



    <!-- 排序弹窗 -->
    <view v-show="isSortModalVisible" class="sort-modal-overlay" @click.self="closeSortModal">
      <view class="sort-modal" @click.stop>
        <!-- 弹窗头部 -->
        <view class="sort-modal-header">
          <view class="header-content">
            <text class="modal-title">选择排序方式</text>
            <text class="modal-subtitle">找到最适合的岗位</text>
          </view>
          <button class="close-btn" @click="closeSortModal">
            <text class="i-fa-solid-times">X</text>
          </button>
        </view>

        <view class="sort-modal-content-wrapper">
          <!-- 排序选项列表 -->
          <scroll-view class="sort-modal-content" scroll-y show-scrollbar="false">
            <view class="sort-options">
              <view :class="['sort-option', { active: currentSort === option.key }]" v-for="option in sortOptions"
                :key="option.key" @click="selectSort(option.key)">
                <view class="option-icon">
                  <text :class="option.icon"></text>
                </view>
                <view class="option-content">
                  <text class="option-name">{{ option.name }}</text>
                  <text class="option-description">{{ option.description }}</text>
                </view>
                <view class="option-check" v-if="currentSort === option.key">
                  <text class="i-mdi-check-circle"></text>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 弹窗底部 -->
        <view class="sort-modal-footer">
          <view class="current-sort-info">
            <text class="current-sort-label">当前排序：</text>
            <text class="current-sort-name">{{ getCurrentSortOption.name }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <BottomTabBar :active-tab="activeTab" :show-badge="true" :badge-data="badgeData" @tab-change="onTabChange"
      @tab-long-press="onTabLongPress" />

    <!-- 右侧弹窗 -->
    <view v-show="isRightWindowVisible" class="right-window-overlay" @click.self="closeRightWindow">
      <view class="right-window" @click.stop>
        <!-- 右侧弹窗头部 -->
        <view class="right-window-header">
          <view class="header-content">
            <text class="window-title">筛选设置</text>
            <text class="window-subtitle">选择面试模式和岗位类别</text>
          </view>
          <button class="close-btn" @click="closeRightWindow">
            <text class="i-fa-solid-times">X</text>
          </button>
        </view>

        <!-- 右侧弹窗内容 -->
        <scroll-view class="right-window-content" scroll-y show-scrollbar="false">
          <view class="right-window-content-wrapper">
            <!-- 面试模式选择 -->
            <view class="filter-section">
              <view class="filter-header">
                <view class="filter-title">
                  <text class="i-mdi-cog-outline title-icon"></text>
                  <text class="title-text">面试模式</text>
                </view>
                <text class="filter-subtitle">选择合适的面试难度</text>
              </view>

              <view class="mode-list">
                <view :class="['mode-item', { active: selectedMode === mode.id }]" v-for="mode in interviewModes"
                  :key="mode.id" @click="selectMode(mode.id)">
                  <view class="mode-icon" :style="getModeIconStyle(mode.id)">
                    <text :class="mode.icon"></text>
                  </view>
                  <view class="mode-info">
                    <text class="mode-name">{{ mode.name }}</text>
                    <text class="mode-description">{{ mode.description }}</text>
                    <view class="mode-stats">
                      <text class="stat">{{ mode.duration }}分钟</text>
                      <text class="stat">难度{{ mode.difficulty }}/5</text>
                    </view>
                  </view>
                  <view class="mode-check" v-if="selectedMode === mode.id">
                    <text class="i-mdi-check-circle"></text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 岗位分类选择 -->
            <view class="filter-section">
              <view class="filter-header">
                <view class="filter-title">
                  <text class="i-mdi-briefcase-outline title-icon"></text>
                  <text class="title-text">岗位类别</text>
                </view>
                <text class="filter-subtitle">选择感兴趣的岗位类型</text>
              </view>

              <view class="category-grid">
                <view :class="['category-item', { active: activeCategory === item.id }]" v-for="item in categories"
                  :key="item.id" @click="changeCategory(item.id)">
                  <view class="category-icon" :style="getCategoryIconStyle(item.id)">
                    <text :class="item.icon"></text>
                  </view>
                  <text class="category-name">{{ item.name }}</text>
                  <view class="category-check" v-if="activeCategory === item.id">
                    <text class="i-mdi-check"></text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 快速筛选 -->
            <view class="filter-section">
              <view class="filter-header">
                <view class="filter-title">
                  <text class="i-mdi-filter-outline title-icon"></text>
                  <text class="title-text">快速筛选</text>
                </view>
              </view>

              <view class="quick-filters">
                <button :class="['quick-filter-btn', { active: quickFilterState.hot }]" @click="quickFilter('hot')">
                  <text class="i-mdi-fire"></text>
                  <text>热门岗位</text>
                  <view v-if="quickFilterState.hot" class="filter-active-indicator">
                    <text class="i-mdi-check"></text>
                  </view>
                </button>
                <button :class="['quick-filter-btn', { active: quickFilterState.easy }]" @click="quickFilter('easy')">
                  <text class="i-mdi-star"></text>
                  <text>简单模式</text>
                  <view v-if="quickFilterState.easy" class="filter-active-indicator">
                    <text class="i-mdi-check"></text>
                  </view>
                </button>
                <button :class="['quick-filter-btn', { active: quickFilterState.tech }]" @click="quickFilter('tech')">
                  <text class="i-mdi-code-tags"></text>
                  <text>技术岗位</text>
                  <view v-if="quickFilterState.tech" class="filter-active-indicator">
                    <text class="i-mdi-check"></text>
                  </view>
                </button>
                <button class="quick-filter-btn reset-btn" @click="quickFilter('reset')">
                  <text class="i-mdi-refresh"></text>
                  <text>重置筛选</text>
                </button>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 右侧弹窗底部 -->
        <view class="right-window-footer">
          <button class="footer-btn apply-btn" @click="applyFilters">
            <text class="i-mdi-check"></text>
            <text>应用筛选</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
// @ts-ignore
import BottomTabBar from '@/components/BottomTabBar.vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
import {
  getJobList,
  getCategories,
  getInterviewModes,
  createInterviewSession,
  favoriteJob as favoriteJobSelect,
  getSearchSuggestions as getSearchSuggestionsApi,
} from '../../service/interview-select'
import type {
  JobItem,
  JobCategory,
  InterviewMode,
  CreateInterviewSessionParams,
  SuggestionItem,
} from '../../types/interview-select'

/**
 * @description 面试岗位选择页
 * 用户可以选择不同类型的岗位进行模拟面试
 * 高度仿真的智能面试场景选择器
 */

// 激活的导航标签
const activeTab = ref('interview')
const badgeData = ref({
  interview: 0,
  resources: 0,
  history: 0,
  profile: 0,
})

// 右侧窗口是否可见
const isRightWindowVisible = ref(false)

// 刷新状态
const isRefreshing = ref(false)

// 排序相关状态
const sortOptions = ref([
  {
    key: 'smart',
    name: '智能排序',
    description: '基于匹配度和热度综合排序',
    icon: 'i-mdi-brain',
  },
  {
    key: 'hot',
    name: '热门优先',
    description: '按面试人数从高到低排序',
    icon: 'i-mdi-fire',
  },
  {
    key: 'difficulty_asc',
    name: '难度从低到高',
    description: '适合新手用户练习',
    icon: 'i-mdi-trending-up',
  },
  {
    key: 'difficulty_desc',
    name: '难度从高到低',
    description: '挑战高难度面试',
    icon: 'i-mdi-trending-down',
  },
  {
    key: 'duration_asc',
    name: '时间从短到长',
    description: '快速练习优先',
    icon: 'i-mdi-clock-fast',
  },
  {
    key: 'duration_desc',
    name: '时间从长到短',
    description: '深度面试优先',
    icon: 'i-mdi-clock',
  },
  {
    key: 'pass_rate_desc',
    name: '通过率从高到低',
    description: '容易通过的岗位优先',
    icon: 'i-mdi-check-circle',
  },
  {
    key: 'pass_rate_asc',
    name: '通过率从低到高',
    description: '具有挑战性的岗位',
    icon: 'i-mdi-alert-circle',
  },
])

const currentSort = ref('smart')
const isSortModalVisible = ref(false)

/**
 * @description 标签页切换处理
 * @param tab 标签页标识
 */
const onTabChange = (tab: string) => {
  activeTab.value = tab
}

/**
 * @description 标签页长按处理
 * @param tab 标签页标识
 */
const onTabLongPress = (tab: string) => {
  activeTab.value = tab
}

// 岗位分类列表（从API加载）
const categories = ref<JobCategory[]>([])

// 当前选中的分类
const activeCategory = ref(0) // 0表示全部分类

// 职位列表数据（从API加载）
const jobs = ref<JobItem[]>([])

// 加载状态
const loading = ref(true)
const loadingJobs = ref(false)

// 分页信息
const pageInfo = ref({
  page: 1,
  pageSize: 20,
  total: 0,
  hasMore: true,
})

// 搜索关键词
const searchKeyword = ref('')

// 快速筛选状态
const quickFilterState = ref({
  hot: false, // 热门岗位
  easy: false, // 简单模式
  tech: false, // 技术岗位
})

// 根据当前分类和搜索关键词筛选职位
const filteredJobs = computed(() => {
  let filtered = [...jobs.value]

  // 1. 根据分类筛选
  if (activeCategory.value && activeCategory.value !== 0) {
    filtered = filtered.filter((job) => job.categoryId === activeCategory.value)
  }

  // 2. 根据搜索关键词筛选
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    filtered = filtered.filter((job) => {
      return (
        job.name.toLowerCase().includes(keyword) ||
        job.company.toLowerCase().includes(keyword) ||
        job.description.toLowerCase().includes(keyword) ||
        job.tags.some((tag) => tag.toLowerCase().includes(keyword))
      )
    })
  }

  // 3. 根据快速筛选条件筛选
  if (quickFilterState.value.hot) {
    // 筛选热门岗位（面试人数 > 100 或者是前3个）
    filtered = filtered.filter((job, index) => job.interviewers > 100 || index < 3)
  }

  if (quickFilterState.value.easy) {
    // 筛选简单模式（难度 <= 2）
    filtered = filtered.filter((job) => job.difficulty <= 2)
  }

  if (quickFilterState.value.tech) {
    // 筛选技术岗位（分类ID为1，或者包含技术相关标签）
    filtered = filtered.filter(
      (job) =>
        job.categoryId === 1 ||
        job.tags.some((tag) =>
          [
            '前端',
            '后端',
            '全栈',
            'JavaScript',
            'Vue',
            'React',
            'Node.js',
            'Python',
            'Java',
          ].includes(tag),
        ),
    )
  }

  // 4. 根据面试模式筛选（如果有特定模式要求）
  if (selectedMode.value && selectedMode.value !== 'standard') {
    // 可以根据不同模式添加特定筛选逻辑
    // 例如：压力模式只显示高难度岗位
    if (selectedMode.value === 'pressure') {
      filtered = filtered.filter((job) => job.difficulty >= 4)
    }
  }

  return filtered
})

/**
 * @description 搜索建议显示状态
 */
const showSearchSuggestions = ref(false)
const searchSuggestions = ref<SuggestionItem[]>([])

/**
 * @description 搜索输入处理（防抖）
 */
let searchTimer: number | null = null
const handleSearchInput = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(async () => {
    if (searchKeyword.value.trim()) {
      await loadSearchSuggestions()
      showSearchSuggestions.value = true
    } else {
      showSearchSuggestions.value = false
    }

    // 重新加载岗位列表
    await loadJobList(true)
  }, 500)
}

/**
 * @description 搜索框获得焦点处理
 */
const handleSearchFocus = () => {
  // 如果有搜索建议，则显示
  if (searchSuggestions.value.length > 0) {
    showSearchSuggestions.value = true
  }
}

/**
 * @description 搜索框失去焦点处理
 */
const handleSearchBlur = () => {
  // 延迟隐藏建议，给用户点击建议的时间
  setTimeout(() => {
    showSearchSuggestions.value = false
  }, 200)
}

/**
 * @description 加载搜索建议
 */
const loadSearchSuggestions = async () => {
  try {
    const response = await getSearchSuggestionsApi({
      keyword: searchKeyword.value,
      limit: 5,
    })
    console.log('搜索建议响应:', response.data);

    if (response.code === 200 && response.data) {
      searchSuggestions.value = response.data.suggestions || []
    }
  } catch (error) {
    console.error('加载搜索建议失败:', error)
    // 设置空数组避免显示错误
    searchSuggestions.value = []
  }
}

/**
 * @description 选择搜索建议
 * @param suggestion 建议项
 */
const selectSearchSuggestion = async (suggestion: SuggestionItem) => {
  searchKeyword.value = suggestion.text
  showSearchSuggestions.value = false
  // 重新加载岗位列表
  await loadJobList(true)
}

/**
 * @description 获取当前排序选项
 * @returns 当前排序选项对象
 */
const getCurrentSortOption = computed(() => {
  return (
    sortOptions.value.find((option) => option.key === currentSort.value) || sortOptions.value[0]
  )
})

/**
 * @description 打开排序弹窗
 */
const openSortModal = () => {
  isSortModalVisible.value = true
}

/**
 * @description 关闭排序弹窗
 */
const closeSortModal = () => {
  isSortModalVisible.value = false
}

/**
 * @description 选择排序方式
 * @param sortKey 排序键值
 */
const selectSort = async (sortKey: string) => {
  currentSort.value = sortKey
  closeSortModal()

  // 重新加载岗位列表
  await loadJobList(true)

  // 显示排序成功提示
  uni.showToast({
    title: `已切换为${getCurrentSortOption.value.name}`,
    icon: 'success',
    duration: 1500,
  })
}

/**
 * @description 切换岗位分类
 * @param categoryId 分类ID
 */
const changeCategory = async (categoryId: number) => {
  // 如果点击的是当前已选中的分类，则不做任何操作
  if (activeCategory.value === categoryId) {
    return
  }

  activeCategory.value = categoryId

  // 清除快速筛选状态，避免冲突
  quickFilterState.value.hot = false
  quickFilterState.value.easy = false
  quickFilterState.value.tech = false

  // 重新加载岗位列表
  await loadJobList(true)

  // 显示切换成功提示
  const categoryName = categories.value.find((cat) => cat.id === categoryId)?.name || '全部'
  uni.showToast({
    title: `已切换到${categoryName}分类`,
    icon: 'success',
    duration: 1500,
  })
}

// 面试模式选择（从API加载）
const interviewModes = ref<InterviewMode[]>([])

// 当前选中的面试模式
const selectedMode = ref('standard')

// 简历上传相关
const resumeUpload = ref({
  isUploaded: false,
  fileName: '',
  fileUrl: '',
  isUploading: false,
  customizedQuestions: [],
})



/**
 * @description 页面初始化加载数据
 */
onLoad((options: any) => {
  // 处理页面参数
  if (options.categoryId) {
    activeCategory.value = parseInt(options.categoryId)
  }

  console.log('页面初始化，当前分类:', activeCategory.value)

  // 初始化页面数据
  initPageData()
})

/**
 * @description 初始化页面数据
 */
const initPageData = async () => {
  loading.value = true

  try {
    // 并行加载基础数据
    await Promise.all([loadCategories(), loadInterviewModes(), loadJobList()])
  } catch (error) {
    console.error('初始化页面数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * @description 加载岗位分类
 */
const loadCategories = async () => {
  try {
    const response = await getCategories({ includeJobCount: true })
    if (response.code === 200 && response.data) {
      // 在分类列表前面添加"全部"选项
      const allCategory = {
        id: 0,
        name: '全部',
        icon: 'i-mdi-view-grid',
        color: '#00c9a7',
        jobCount: 0
      }
      categories.value = [allCategory, ...response.data.categories]
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    // 如果加载失败，至少提供一个"全部"选项
    categories.value = [{
      id: 0,
      name: '全部',
      icon: 'i-mdi-view-grid',
      color: '#00c9a7',
      jobCount: 0
    }]
  }
}

/**
 * @description 加载面试模式
 */
const loadInterviewModes = async () => {
  try {
    const response = await getInterviewModes()
    if (response.code === 200 && response.data) {
      interviewModes.value = response.data.modes
    }
  } catch (error) {
    console.error('加载面试模式失败:', error)
  }
}

/**
 * @description 加载岗位列表
 */
const loadJobList = async (isRefresh = false) => {
  if (isRefresh) {
    pageInfo.value.page = 1
    pageInfo.value.hasMore = true
  }

  if (!pageInfo.value.hasMore && !isRefresh) {
    return
  }

  loadingJobs.value = true

  try {
    const response = await getJobList({
      page: pageInfo.value.page,
      pageSize: pageInfo.value.pageSize,
      categoryId: activeCategory.value === 0 ? undefined : activeCategory.value, // 0表示全部，不传分类参数
      keyword: searchKeyword.value,
      sortBy: currentSort.value,
      sortOrder: 'desc',
    })
    if (response.code === 200 && response.data) {
      const { jobs: newJobs, total, hasMore } = response.data

      console.log('加载职位数据成功:', {
        newJobsCount: newJobs.length,
        total,
        hasMore,
        isRefresh,
        currentCategory: activeCategory.value
      })

      if (isRefresh) {
        jobs.value = newJobs
      } else {
        jobs.value.push(...newJobs)
      }

      pageInfo.value.total = total
      pageInfo.value.hasMore = hasMore
      pageInfo.value.page += 1
    } else {
      console.error('加载职位数据失败:', response)
    }
  } catch (error) {
    console.error('加载岗位列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loadingJobs.value = false
  }
}

// 当前准备开始面试的职位ID
const currentJobId = ref<number | null>(null)

/**
 * @description 选择面试模式
 * @param modeId 模式ID
 */
const selectMode = (modeId: string) => {
  selectedMode.value = modeId
}





/**
 * @description 开始面试
 * @param jobId 职位ID
 */
const startInterview = async (jobId: number) => {
  // 记录当前职位ID
  currentJobId.value = jobId

  // 直接开始面试，不进行设备检测
  await confirmStartInterview()
}

/**
 * @description 确认开始面试
 */
const confirmStartInterview = async () => {
  if (!currentJobId.value) {
    return
  }

  // 创建面试会话
  try {
    const sessionParams: CreateInterviewSessionParams = {
      jobId: currentJobId.value,
      mode: selectedMode.value,
      resumeUrl: resumeUpload.value.fileUrl,
      customizedQuestions: resumeUpload.value.customizedQuestions,
    }

    const response = await createInterviewSession(sessionParams)

    if (response.code === 200 && response.data) {
      const { sessionId, sessionToken } = response.data

      // 跳转到面试房间
      uni.navigateTo({
        url: `/pages/interview/room?sessionId=${sessionId}&jobId=${currentJobId.value}&mode=${selectedMode.value}&token=${sessionToken}`,
      })
    } else {
      throw new Error(response.message || '创建面试会话失败')
    }
  } catch (error) {
    console.error('创建面试会话失败:', error)
    uni.showToast({
      title: error.message || '创建面试会话失败',
      icon: 'none',
    })
  }
}

/**
 * @description 确认开始演示面试
 */
const confirmStartDemo = async () => {
  if (!currentJobId.value) {
    return
  }

  try {
    // 演示模式：生成模拟会话ID
    const demoSessionId = `demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 获取选中的职位信息
    const selectedJob = jobs.value.find((job) => job.id === currentJobId.value)

    // 显示开始提示
    uni.showToast({
      title: '进入演示模式',
      icon: 'success',
      duration: 1500,
    })

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
      // 跳转到面试房间（演示模式）
      uni.navigateTo({
        url: `/pages/interview/room?sessionId=${demoSessionId}&jobId=${currentJobId.value}&mode=${selectedMode.value}&demo=true&jobName=${encodeURIComponent(selectedJob?.name || '前端开发工程师')}&company=${encodeURIComponent(selectedJob?.company || '演示公司')}`,
      })
    }, 1500)
  } catch (error) {
    console.error('启动演示面试失败:', error)
    uni.showToast({
      title: '启动演示失败',
      icon: 'none',
    })
  }
}



/**
 * @description 查看岗位详情
 * @param jobId 职位ID
 */
const viewJobDetail = (jobId: number) => {
  // 找到对应的职位信息
  const selectedJob = jobs.value.find((job) => job.id === jobId)
  if (!selectedJob) {
    uni.showToast({
      title: '职位信息不存在',
      icon: 'none',
    })
    return
  }

  // 跳转到职位详情页面
  uni.navigateTo({
    url: `/pages/interview/job-detail?jobId=${jobId}&categoryId=${selectedJob.categoryId}`,
  })
}

/**
 * @description 渲染难度星级
 * @param difficulty 难度等级
 */
const renderStars = (difficulty: number) => {
  const filledStars = '★'.repeat(difficulty)
  const emptyStars = '☆'.repeat(5 - difficulty)
  return filledStars + emptyStars
}

/**
 * @description 获取难度颜色
 * @param difficulty 难度等级
 */
const getDifficultyColor = (difficulty: number) => {
  if (difficulty <= 2) return '#52C41A'
  if (difficulty <= 3) return '#FAAD14'
  if (difficulty <= 4) return '#FA8C16'
  return '#F5222D'
}

/**
 * @description 打开右侧筛选弹窗
 */
const openRightWindow = () => {
  isRightWindowVisible.value = true
}

/**
 * @description 关闭右侧筛选弹窗
 */
const closeRightWindow = () => {
  isRightWindowVisible.value = false
}

/**
 * @description 快速筛选功能
 * @param type 筛选类型
 */
const quickFilter = async (type: string) => {
  switch (type) {
    case 'hot':
      // 筛选热门岗位
      quickFilterState.value.hot = !quickFilterState.value.hot
      quickFilterState.value.easy = false
      quickFilterState.value.tech = false
      uni.showToast({
        title: quickFilterState.value.hot ? '已筛选热门岗位' : '已取消热门筛选',
        icon: 'success',
      })
      break
    case 'easy':
      // 切换到简单模式
      quickFilterState.value.easy = !quickFilterState.value.easy
      quickFilterState.value.hot = false
      quickFilterState.value.tech = false
      selectedMode.value = 'standard'
      uni.showToast({
        title: quickFilterState.value.easy ? '已筛选简单岗位' : '已取消简单筛选',
        icon: 'success',
      })
      break
    case 'tech':
      // 筛选技术岗位
      quickFilterState.value.tech = !quickFilterState.value.tech
      quickFilterState.value.hot = false
      quickFilterState.value.easy = false
      uni.showToast({
        title: quickFilterState.value.tech ? '已筛选技术岗位' : '已取消技术筛选',
        icon: 'success',
      })
      break
    case 'reset':
      // 重置所有筛选
      quickFilterState.value.hot = false
      quickFilterState.value.easy = false
      quickFilterState.value.tech = false
      selectedMode.value = 'standard'
      activeCategory.value = 0 // 0表示全部分类
      searchKeyword.value = ''
      showSearchSuggestions.value = false
      currentSort.value = 'smart'

      // 重新加载数据
      await loadJobList(true)

      uni.showToast({
        title: '已重置所有筛选',
        icon: 'success',
      })
      break
  }
}

/**
 * @description 应用筛选设置
 */
const applyFilters = async () => {
  console.log('应用筛选设置:', {
    activeCategory: activeCategory.value,
    selectedMode: selectedMode.value,
    quickFilterState: quickFilterState.value,
    searchKeyword: searchKeyword.value,
  })

  // 关闭弹窗
  isRightWindowVisible.value = false

  // 重新加载岗位列表以应用筛选条件
  await loadJobList(true)

  // 显示应用成功提示
  uni.showToast({
    title: '筛选设置已应用',
    icon: 'success',
  })
}
/**
 * @description 清空搜索
 */
const clearSearch = async () => {
  searchKeyword.value = ''
  showSearchSuggestions.value = false
  // 重新加载岗位列表
  await loadJobList(true)
}

/**
 * @description 获取面试模式样式
 * @param modeId 模式ID
 * @returns 样式
 */
const getModeIconStyle = (modeId: string) => {
  const mode = interviewModes.value.find((mode) => mode.id === modeId)
  return {
    '--mode-color': mode?.color,
  }
}

/**
 * @description 获取分类样式
 * @param categoryId 分类ID
 * @returns 样式
 */
const getCategoryIconStyle = (categoryId: number) => {
  const category = categories.value.find((category) => category.id === categoryId)
  return {
    '--category-color': category?.color,
  }
}

/**
 * @description 收藏/取消收藏岗位
 * @param jobId 岗位ID
 * @param isFavorited 是否收藏
 */
const toggleFavorite = async (jobId: number, isFavorited: boolean) => {
  try {
    const response = await favoriteJobSelect({
      jobId,
      isFavorited: !isFavorited,
    })

    if (response.code === 200) {
      // 更新本地状态
      const job = jobs.value.find((j) => j.id === jobId)
      if (job) {
        job.isFavorited = !isFavorited
      }

      uni.showToast({
        title: !isFavorited ? '收藏成功' : '取消收藏',
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

// 刷新处理
const onRefresh = async () => {
  isRefreshing.value = true
  await loadJobList(true)
  isRefreshing.value = false
}

// 加载更多处理
const onLoadMore = async () => {
  await loadJobList()
}
</script>

<style lang="scss" scoped>
.select-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #f0f9ff 100%);

  // 确保变量在组件内生效
  --primary-color: #00c9a7;
  --primary-light: #4fd1c7;
  --primary-dark: #00a693;
  --secondary-color: #7c3aed;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --border-color: #e2e8f0;
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);
  --radius-sm: 12rpx;
  --radius-md: 16rpx;
  --radius-lg: 20rpx;
  --radius-xl: 24rpx;
}

// 加载状态样式
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-content {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    align-items: center;

    .loading-icon {
      font-size: 64rpx;
      color: var(--primary-color);
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 28rpx;
      color: var(--text-secondary);
    }
  }
}

// 主要内容区域
.main-content {
  position: relative;
  z-index: 1;
  height: calc(100vh - 88rpx - 120rpx);
  padding-bottom: 40rpx;

  // 添加滚动优化
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

// 欢迎横幅
.welcome-banner {
  position: relative;
  padding: 20rpx 30rpx;
  margin: 20rpx 20rpx 30rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 24rpx;
  box-shadow: 0 10rpx 40rpx rgba(0, 201, 167, 0.25);

  // 添加装饰性背景
  &::before {
    position: absolute;
    top: -50%;
    right: -30%;
    width: 200rpx;
    height: 200rpx;
    content: '';
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
  }

  .banner-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .banner-text {
      flex: 1;

      .banner-title {
        display: block;
        margin-bottom: 12rpx;
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .banner-subtitle {
        display: block;
        font-size: 24rpx;
        line-height: 1.5;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .banner-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10rpx);
      border-radius: 50%;

      text {
        font-size: 48rpx;
        color: #fff;
      }
    }
  }

  .banner-stats {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .stat-item {
      text-align: center;

      .stat-number {
        display: block;
        margin-bottom: 6rpx;
        font-size: 28rpx;
        font-weight: 700;
        color: #fff;
      }

      .stat-label {
        font-size: 20rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .stat-divider {
      width: 2rpx;
      height: 40rpx;
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// 搜索区域
.search-section {
  position: relative;
  z-index: 50;
  margin: 0 20rpx 30rpx;

  .search-container {
    position: relative;
    z-index: 100;

    .search-input-wrapper {
      display: flex;
      align-items: center;
      height: 88rpx;
      padding: 0 24rpx;
      background: #ffffff;
      border: 2rpx solid transparent;
      border-radius: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:focus-within {
        border-color: #00c9a7;
        box-shadow: 0 0 0 6rpx rgba(0, 201, 167, 0.1);
        transform: translateY(-2rpx);
      }

      .search-icon {
        margin-right: 16rpx;
        font-size: 36rpx;
        color: #00c9a7;
        transition: color 0.3s ease;
      }

      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #1f2937;
        background: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: #9ca3af;
        }
      }

      .clear-icon {
        padding: 8rpx;
        font-size: 32rpx;
        color: #9ca3af;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background: #f1f5f9;
          transform: scale(0.9);
        }
      }
    }

    .search-suggestions {
      position: absolute;
      top: 100%;
      right: 0;
      left: 0;
      z-index: 9999;
      max-height: 300rpx;
      padding: 20rpx;
      margin-top: 8rpx;
      overflow-y: auto;
      background: #ffffff;
      backdrop-filter: blur(10rpx);
      border: 1rpx solid #e2e8f0;
      border-radius: 20rpx;
      box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);

      // 添加一个小三角形指示器
      &::before {
        position: absolute;
        top: -8rpx;
        left: 50rpx;
        width: 0;
        height: 0;
        content: '';
        filter: drop-shadow(0 -2rpx 4rpx rgba(0, 0, 0, 0.1));
        border-right: 8rpx solid transparent;
        border-bottom: 8rpx solid #ffffff;
        border-left: 8rpx solid transparent;
      }

      // 优化建议标签容器 - 支持同行显示和自动换行
      .suggestion-tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 8rpx 0;

        // 响应式布局优化
        @media (max-width: 750rpx) {
          gap: 10rpx;
        }

        @media (max-width: 600rpx) {
          gap: 8rpx;
        }
      }

      .suggestion-item {
        display: inline-flex;
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        .suggestion-card {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: flex-start;
          max-width: 100%;
          min-height: 60rpx;
          padding: 14rpx 24rpx;
          overflow: hidden;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 80%);
          border: 2rpx solid #cccccc;
          border-radius: 28rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          // 响应式padding
          @media (max-width: 750rpx) {
            min-height: 56rpx;
            padding: 12rpx 20rpx;
            border-radius: 24rpx;
          }

          @media (max-width: 600rpx) {
            min-height: 52rpx;
            padding: 10rpx 16rpx;
            border-radius: 20rpx;
          }

          // 添加微妙的渐变边框效果
          &::before {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            padding: 2rpx;
            content: '';
            background: linear-gradient(135deg, #00c9a7, #4fd1c7, #7c3aed);
            border-radius: 28rpx;
            opacity: 0;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            transition: opacity 0.3s ease;

            @media (max-width: 750rpx) {
              border-radius: 24rpx;
            }

            @media (max-width: 600rpx) {
              border-radius: 20rpx;
            }
          }

          // 添加微妙的内阴影效果
          &::after {
            position: absolute;
            top: 1rpx;
            right: 1rpx;
            bottom: 1rpx;
            left: 1rpx;
            pointer-events: none;
            content: '';
            background: rgba(255, 255, 255, 0.5);
            border-radius: 26rpx;
            opacity: 0.8;

            @media (max-width: 750rpx) {
              border-radius: 22rpx;
            }

            @media (max-width: 600rpx) {
              border-radius: 18rpx;
            }
          }

          .suggestion-text {
            position: relative;
            z-index: 2;
            max-width: 200rpx;
            overflow: hidden;
            font-size: 26rpx;
            font-weight: 600;
            color: #374151;
            text-overflow: ellipsis;
            text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
            white-space: nowrap;

            // 响应式字体大小
            @media (max-width: 750rpx) {
              max-width: 180rpx;
              font-size: 24rpx;
            }

            @media (max-width: 600rpx) {
              max-width: 160rpx;
              font-size: 22rpx;
            }
          }

          .suggestion-type {
            position: relative;
            z-index: 2;
            flex-shrink: 0;
            padding: 4rpx 10rpx;
            margin-left: 12rpx;
            font-size: 18rpx;
            font-weight: 500;
            color: #9ca3af;
            white-space: nowrap;
            background: rgba(156, 163, 175, 0.1);
            border-radius: 12rpx;

            // 响应式字体大小
            @media (max-width: 750rpx) {
              padding: 3rpx 8rpx;
              margin-left: 10rpx;
              font-size: 16rpx;
            }

            @media (max-width: 600rpx) {
              padding: 2rpx 6rpx;
              margin-left: 8rpx;
              font-size: 14rpx;
            }
          }
        }

        // 悬停效果
        &:hover .suggestion-card {
          background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
          border-color: #00c9a7;
          box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.15);
          transform: translateY(-2rpx) scale(1.02);

          &::before {
            opacity: 1;
          }

          .suggestion-text {
            color: #1f2937;
          }

          .suggestion-type {
            color: #00a693;
            background: rgba(0, 201, 167, 0.1);
          }
        }

        // 点击效果
        &:active .suggestion-card {
          background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
          border-color: #00a693;
          box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
          transform: translateY(0) scale(0.98);

          .suggestion-text {
            color: #ffffff;
            text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
          }

          .suggestion-type {
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
  }

  // 筛选状态指示器
  .filter-indicators {
    display: flex;
    gap: 16rpx;
    align-items: center;
    justify-content: space-between;
    margin-top: 20rpx;

    .filter-tags {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      gap: 12rpx;

      .filter-tag {
        display: flex;
        gap: 8rpx;
        align-items: center;
        padding: 8rpx 16rpx;
        font-size: 22rpx;
        color: #64748b;
        background: #f1f5f9;
        border: 1rpx solid #e2e8f0;
        border-radius: 20rpx;
        transition: all 0.3s ease;

        &.search-tag {
          color: #fff;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-color: #3b82f6;
        }

        &.hot-tag {
          color: #fff;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border-color: #ef4444;
        }

        &.easy-tag {
          color: #fff;
          background: linear-gradient(135deg, #10b981, #059669);
          border-color: #10b981;
        }

        &.tech-tag {
          color: #fff;
          background: linear-gradient(135deg, #8b5cf6, #7c3aed);
          border-color: #8b5cf6;
        }

        &.category-tag {
          color: #fff;
          background: linear-gradient(135deg, #f59e0b, #d97706);
          border-color: #f59e0b;
        }

        text:first-child {
          font-size: 20rpx;
        }

        text:last-child {
          padding: 4rpx;
          font-size: 18rpx;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          transition: all 0.3s ease;

          &:active {
            background: rgba(255, 255, 255, 0.4);
            transform: scale(0.9);
          }
        }
      }
    }

    .clear-all-filters {
      display: flex;
      gap: 6rpx;
      align-items: center;
      padding: 8rpx 16rpx;
      font-size: 22rpx;
      color: #dc2626;
      background: #fee2e2;
      border: 1rpx solid #fecaca;
      border-radius: 20rpx;
      transition: all 0.3s ease;

      &:active {
        background: #fecaca;
        transform: scale(0.95);
      }

      text:first-child {
        font-size: 20rpx;
      }
    }
  }
}

// 通用区块容器
.section-container {
  margin: 0 20rpx 30rpx;

  .section-header {
    margin-bottom: 24rpx;

    .section-title {
      display: flex;
      gap: 12rpx;
      align-items: center;
      margin-bottom: 8rpx;

      .title-icon {
        font-size: 28rpx;
        color: var(--primary-color);
      }

      .title-text {
        font-size: 32rpx;
        font-weight: 700;
        color: var(--text-primary);
      }
    }

    .section-subtitle {
      margin-left: 40rpx;
      font-size: 24rpx;
      color: var(--text-secondary);
    }
  }
}

// 面试模式选择
.mode-section {
  .mode-grid {
    display: flex;
    gap: 20rpx;

    .mode-card {
      position: relative;
      flex: 1;
      padding: 24rpx 20rpx;
      cursor: pointer;
      background: #ffffff;
      border: 3rpx solid transparent;
      border-radius: 20rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &:active {
        transform: translateY(4rpx);
      }

      &.active {
        background: linear-gradient(135deg, #00c9a7, #4fd1c7);
        border-color: #00c9a7;
        box-shadow: 0 12rpx 40rpx rgba(0, 201, 167, 0.3);
        transform: translateY(-6rpx);

        .mode-icon text {
          color: #fff;
        }

        .mode-content {
          .mode-name {
            color: #fff;
          }

          .mode-description {
            color: rgba(255, 255, 255, 0.9);
          }

          .mode-stats .stat {
            color: rgba(255, 255, 255, 0.8);

            text:first-child {
              color: rgba(255, 255, 255, 0.9);
            }
          }
        }
      }

      .mode-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;

        .mode-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64rpx;
          height: 64rpx;
          background: var(--bg-secondary);
          border-radius: 50%;
          transition: all 0.3s ease;

          text {
            font-size: 32rpx;
            color: var(--mode-color);
            transition: color 0.3s ease;
          }
        }

        .mode-badge {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32rpx;
          height: 32rpx;
          background: #fff;
          border-radius: 50%;
          box-shadow: var(--shadow-sm);

          text {
            font-size: 20rpx;
            color: var(--primary-color);
          }
        }
      }

      .mode-content {
        .mode-name {
          margin-bottom: 8rpx;
          font-size: 26rpx;
          font-weight: 600;
          color: var(--text-primary);
          transition: color 0.3s ease;
        }

        .mode-description {
          margin-bottom: 16rpx;
          font-size: 22rpx;
          line-height: 1.4;
          color: var(--text-secondary);
          transition: color 0.3s ease;
        }

        .mode-stats {
          display: flex;
          gap: 16rpx;

          .stat {
            display: flex;
            gap: 6rpx;
            align-items: center;
            font-size: 20rpx;
            color: var(--text-tertiary);

            text:first-child {
              font-size: 18rpx;
              color: var(--text-secondary);
            }
          }
        }
      }
    }
  }
}

// 岗位分类区域
.category-section {
  .category-scroll {
    overflow: hidden;

    .category-list {
      display: inline-flex;
      gap: 20rpx;
      padding: 0 20rpx;

      .category-card {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 120rpx;
        padding: 20rpx 24rpx;
        background: #ffffff;
        border: 2rpx solid transparent;
        border-radius: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

        &:active {
          transform: scale(0.95);
        }

        &.active {
          background: linear-gradient(135deg, #00c9a7, #4fd1c7);
          border-color: #00c9a7;
          box-shadow: 0 8rpx 30rpx rgba(0, 201, 167, 0.25);
          transform: translateY(-4rpx);

          .category-icon text {
            color: #fff;
          }

          .category-name {
            color: #fff;
          }
        }

        .category-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60rpx;
          height: 60rpx;
          margin-bottom: 12rpx;
          background: var(--bg-secondary);
          border-radius: 50%;
          transition: all 0.3s ease;

          text {
            font-size: 32rpx;
            color: var(--category-color);
            transition: color 0.3s ease;
          }
        }

        .category-name {
          font-size: 24rpx;
          font-weight: 600;
          color: var(--text-primary);
          text-align: center;
          transition: color 0.3s ease;
        }

        .category-indicator {
          position: absolute;
          bottom: -3rpx;
          left: 50%;
          width: 30rpx;
          height: 6rpx;
          background: #fff;
          border-radius: 3rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
          transform: translateX(-50%);
        }
      }
    }
  }
}

// 职位列表区域
.job-section {
  position: relative;
  z-index: 1;

  .job-list-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0 8rpx;
    margin-bottom: 24rpx;

    .header-left {
      flex: 1;

      .result-count {
        display: block;
        margin-bottom: 6rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: var(--text-primary);
      }

      .result-tip {
        display: block;
        font-size: 22rpx;
        color: var(--text-secondary);
      }
    }

    .header-right {
      .sort-selector {
        display: flex;
        gap: 8rpx;
        align-items: center;
        padding: 12rpx 20rpx;
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;

        &:active {
          background: var(--bg-secondary);
          transform: scale(0.98);
        }

        .sort-icon {
          font-size: 24rpx;
          color: var(--primary-color);
        }

        .sort-text {
          font-size: 24rpx;
          color: var(--text-secondary);
        }

        text:last-child {
          font-size: 20rpx;
          color: var(--text-tertiary);
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 80rpx 40rpx;
    text-align: center;

    .empty-illustration {
      position: relative;
      margin-bottom: 40rpx;

      .empty-icon {
        font-size: 120rpx;
        color: var(--text-tertiary);
        opacity: 0.6;
      }

      .empty-decoration {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 160rpx;
        height: 160rpx;
        border: 4rpx dashed var(--border-color);
        border-radius: 50%;
        opacity: 0.3;
        transform: translate(-50%, -50%);
      }
    }

    .empty-title {
      margin-bottom: 12rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-primary);
    }

    .empty-subtitle {
      margin-bottom: 40rpx;
      font-size: 24rpx;
      line-height: 1.5;
      color: var(--text-secondary);
    }

    .empty-action {
      display: flex;
      gap: 8rpx;
      align-items: center;
      padding: 16rpx 32rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #fff;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      border: none;
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-md);
      transition: all 0.3s ease;

      &:active {
        box-shadow: var(--shadow-sm);
        transform: translateY(2rpx);
      }

      text:first-child {
        font-size: 28rpx;
      }
    }
  }

  .job-list {
    .job-card {
      position: relative;
      padding: 32rpx;
      margin-bottom: 24rpx;
      overflow: hidden;
      background: #ffffff;
      border: 1rpx solid #e2e8f0;
      border-radius: 20rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &:active {
        border-color: #00c9a7;
        box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);
        transform: translateY(-4rpx);
      }

      // 添加卡片装饰
      &::before {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 6rpx;
        content: '';
        background: linear-gradient(90deg, #00c9a7, #4fd1c7);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:active::before {
        opacity: 1;
      }

      .job-card-header {
        margin-bottom: 24rpx;

        .job-main {
          .job-title-row {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 16rpx;

            .job-name {
              flex: 1;
              margin-right: 16rpx;
              font-size: 32rpx;
              font-weight: 700;
              line-height: 1.3;
              color: var(--text-primary);
            }

            .job-hot-badge {
              display: flex;
              gap: 4rpx;
              align-items: center;
              padding: 6rpx 12rpx;
              font-size: 20rpx;
              font-weight: 600;
              color: #fff;
              background: linear-gradient(135deg, var(--accent-color), #fb923c);
              border-radius: var(--radius-sm);
              box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.3);

              text:first-child {
                font-size: 18rpx;
              }
            }
          }

          .company-row {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .company-info {
              display: flex;
              gap: 8rpx;
              align-items: center;

              .company-icon {
                font-size: 24rpx;
                color: var(--primary-color);
              }

              .company-name {
                font-size: 26rpx;
                font-weight: 500;
                color: var(--text-secondary);
              }
            }

            .job-stats-mini {
              display: flex;
              gap: 16rpx;

              .stat {
                font-size: 22rpx;
                color: var(--text-tertiary);

                &.success {
                  font-weight: 600;
                  color: var(--success-color);
                }
              }
            }
          }
        }
      }

      .job-description {
        margin-bottom: 24rpx;

        .description-text {
          font-size: 24rpx;
          line-height: 1.6;
          color: var(--text-secondary);
        }
      }

      .job-details {
        margin-bottom: 24rpx;

        .detail-row {
          display: flex;
          gap: 24rpx;

          .detail-item {
            display: flex;
            flex: 1;
            gap: 8rpx;
            align-items: center;
            padding: 16rpx;
            background: var(--bg-secondary);
            border-radius: var(--radius-md);

            .detail-icon {
              font-size: 24rpx;
              color: var(--primary-color);
            }

            .detail-label {
              margin-right: 4rpx;
              font-size: 22rpx;
              color: var(--text-tertiary);
            }

            .detail-value {
              font-size: 22rpx;
              font-weight: 600;
              color: var(--text-primary);
            }

            .difficulty-display {
              .difficulty-stars {
                font-size: 20rpx;
                font-weight: 700;
              }
            }
          }
        }
      }

      .job-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-bottom: 32rpx;

        .tag {
          padding: 10rpx 16rpx;
          font-size: 22rpx;
          font-weight: 500;
          color: var(--primary-color);
          background: rgba(0, 201, 167, 0.1);
          border: 1rpx solid rgba(0, 201, 167, 0.2);
          border-radius: var(--radius-md);
          transition: all 0.3s ease;

          &:nth-child(even) {
            color: var(--secondary-color);
            background: rgba(124, 58, 237, 0.1);
            border-color: rgba(124, 58, 237, 0.2);
          }
        }
      }

      .job-actions {
        display: flex;
        flex-direction: row;
        gap: 16rpx;
        align-items: center;

        .action-btn {
          display: flex;
          flex: 1;
          flex-direction: row;
          gap: 8rpx;
          align-items: center;
          justify-content: center;
          min-width: 0;
          height: 88rpx;
          font-size: 26rpx;
          font-weight: 600;
          white-space: nowrap;
          border: none;
          border-radius: var(--radius-lg);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &.secondary-btn {
            color: var(--text-secondary);
            background: var(--bg-secondary);
            border: 2rpx solid var(--border-color);

            &:active {
              color: var(--primary-color);
              background: var(--bg-tertiary);
              border-color: var(--primary-color);
              transform: scale(0.98);
            }
          }

          &.primary-btn {
            color: #fff;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);

            &:active {
              box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
              transform: translateY(2rpx);
            }
          }

          text:first-child {
            font-size: 28rpx;
          }
        }
      }

      .recommendation-reason {
        display: flex;
        gap: 12rpx;
        align-items: center;
        padding: 16rpx;
        margin-top: 20rpx;
        background: linear-gradient(135deg, #fef3e2, #fdf2f8);
        border-left: 4rpx solid var(--accent-color);
        border-radius: var(--radius-md);

        .reason-icon {
          font-size: 24rpx;
          color: var(--accent-color);
        }

        .reason-text {
          font-size: 22rpx;
          line-height: 1.4;
          color: var(--text-secondary);
        }
      }
    }
  }
}

// 排序弹窗样式
.sort-modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sort-modal {
  width: 85%;
  max-width: 600rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  .sort-modal-header {
    position: relative;
    padding: 32rpx 32rpx 20rpx;
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);

    &::after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1rpx;
      content: '';
      background: rgba(255, 255, 255, 0.1);
    }

    .header-content {
      .modal-title {
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .modal-subtitle {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .close-btn {
      position: absolute;
      top: 12rpx;
      right: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10rpx);
      border: none;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.9);
      }

      text {
        font-size: 28rpx;
        color: #fff;
      }
    }
  }

  .sort-modal-content-wrapper {
    display: flex;
    flex-direction: column;
    max-height: 60vh;
    overflow: hidden;

    .sort-modal-content {
      flex: 1;
      padding: 24rpx 0;
      overflow-y: auto;

      .sort-options {
        .sort-option {
          position: relative;
          display: flex;
          align-items: center;
          padding: 20rpx;
          margin-bottom: 16rpx;
          background: var(--bg-secondary);
          border: 2rpx solid transparent;
          border-radius: var(--radius-lg);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:last-child {
            margin-bottom: 0;
          }

          &:active {
            transform: scale(0.98);
          }

          &.active {
            background: linear-gradient(135deg, #00c9a7, #4fd1c7);
            border-color: #00c9a7;
            box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);

            .option-icon text {
              color: #ffd700;
            }

            .option-content {
              .option-name {
                color: #fff;
              }

              .option-description {
                color: rgba(255, 255, 255, 0.9);
              }
            }
          }

          .option-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56rpx;
            height: 56rpx;
            margin-right: 16rpx;
            background: var(--bg-primary);
            border-radius: 50%;
            transition: all 0.3s ease;

            text {
              font-size: 28rpx;
              color: var(--primary-color);
              transition: color 0.3s ease;
            }
          }

          .option-content {
            flex: 1;

            .option-name {
              margin-bottom: 6rpx;
              font-size: 26rpx;
              font-weight: 600;
              color: var(--text-primary);
              transition: color 0.3s ease;
            }

            .option-description {
              font-size: 22rpx;
              line-height: 1.4;
              color: var(--text-secondary);
              transition: color 0.3s ease;
            }
          }

          .option-check {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32rpx;
            height: 32rpx;

            text {
              font-size: 24rpx;
              color: #fff;
            }
          }
        }
      }
    }
  }

  .sort-modal-footer {
    padding: 20rpx 32rpx 32rpx;
    background: var(--bg-secondary);
    border-top: 1rpx solid var(--border-color);

    .current-sort-info {
      display: flex;
      gap: 8rpx;
      align-items: center;
      justify-content: center;

      .current-sort-label {
        font-size: 24rpx;
        color: var(--text-secondary);
      }

      .current-sort-name {
        font-size: 24rpx;
        font-weight: 600;
        color: var(--primary-color);
      }
    }
  }
}



// 右侧弹窗样式
.right-window-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9998;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  animation: fadeIn 0.3s ease-out;
}

.right-window {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 80%;
  max-width: 600rpx;
  height: 100%;
  background: #ffffff;
  box-shadow: -8rpx 0 40rpx rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  .right-window-header {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 40rpx 32rpx 24rpx;
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);

    &::after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 1rpx;
      content: '';
      background: rgba(255, 255, 255, 0.1);
    }

    .header-content {
      flex: 1;

      .window-title {
        display: block;
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .window-subtitle {
        display: block;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .close-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10rpx);
      border: none;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.9);
      }

      text {
        font-size: 28rpx;
        color: #fff;
      }
    }
  }

  .right-window-content {
    flex: 1;
    overflow-y: auto;

    .right-window-content-wrapper {
      padding: 12rpx 24rpx;
    }

    .filter-section {
      margin-bottom: 48rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-header {
        margin-bottom: 24rpx;

        .filter-title {
          display: flex;
          gap: 12rpx;
          align-items: center;
          margin-bottom: 8rpx;

          .title-icon {
            font-size: 28rpx;
            color: var(--primary-color);
          }

          .title-text {
            font-size: 28rpx;
            font-weight: 700;
            color: var(--text-primary);
          }
        }

        .filter-subtitle {
          margin-left: 40rpx;
          font-size: 22rpx;
          color: var(--text-secondary);
        }
      }

      // 面试模式列表
      .mode-list {
        .mode-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 12rpx 24rpx;
          margin-bottom: 16rpx;
          background: var(--bg-secondary);
          border: 2rpx solid transparent;
          border-radius: var(--radius-lg);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:active {
            transform: scale(0.98);
          }

          &.active {
            background: linear-gradient(135deg, #00c9a7, #4fd1c7);
            border-color: #00c9a7;
            box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);

            .mode-icon text {
              color: #ffd700;
              /* 改为金色 */
            }

            .mode-info {
              .mode-name {
                color: #fff;
              }

              .mode-description {
                color: rgba(255, 255, 255, 0.9);
              }

              .mode-stats .stat {
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }

          .mode-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56rpx;
            height: 56rpx;
            margin-right: 16rpx;
            background: var(--bg-primary);
            border-radius: 50%;
            transition: all 0.3s ease;

            text {
              font-size: 28rpx;
              color: var(--mode-color);
              transition: color 0.3s ease;
            }
          }

          .mode-info {
            flex: 1;

            .mode-name {
              margin-bottom: 6rpx;
              font-size: 24rpx;
              font-weight: 600;
              color: var(--text-primary);
              transition: color 0.3s ease;
            }

            .mode-description {
              margin-bottom: 8rpx;
              font-size: 20rpx;
              line-height: 1.4;
              color: var(--text-secondary);
              transition: color 0.3s ease;
            }

            .mode-stats {
              display: flex;
              gap: 12rpx;

              .stat {
                font-size: 18rpx;
                color: var(--text-tertiary);
                transition: color 0.3s ease;
              }
            }
          }

          .mode-check {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32rpx;
            height: 32rpx;

            text {
              font-size: 24rpx;
              color: #fff;
            }
          }
        }
      }

      // 岗位分类网格
      .category-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16rpx;

        .category-item {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 24rpx 16rpx;
          background: var(--bg-secondary);
          border: 2rpx solid transparent;
          border-radius: var(--radius-lg);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:active {
            transform: scale(0.95);
          }

          &.active {
            background: linear-gradient(135deg, #00c9a7, #4fd1c7);
            border-color: #00c9a7;
            box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);

            .category-icon text {
              color: #ffd700;
              /* 改为金色 */
            }

            .category-name {
              color: #fff;
            }
          }

          .category-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48rpx;
            height: 48rpx;
            margin-bottom: 12rpx;
            background: var(--bg-primary);
            border-radius: 50%;
            transition: all 0.3s ease;

            text {
              font-size: 24rpx;
              color: var(--category-color);
              transition: color 0.3s ease;
            }
          }

          .category-name {
            font-size: 20rpx;
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
            transition: color 0.3s ease;
          }

          .category-check {
            position: absolute;
            top: 8rpx;
            right: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24rpx;
            height: 24rpx;
            background: #fff;
            border-radius: 50%;
            box-shadow: var(--shadow-sm);

            text {
              font-size: 16rpx;
              color: var(--primary-color);
            }
          }
        }
      }

      // 快速筛选按钮
      .quick-filters {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12rpx;

        .quick-filter-btn {
          position: relative;
          display: flex;
          gap: 8rpx;
          align-items: center;
          justify-content: center;
          height: 72rpx;
          padding: 0 20rpx;
          font-size: 22rpx;
          font-weight: 500;
          color: var(--text-secondary);
          background: var(--bg-secondary);
          border: 2rpx solid var(--border-color);
          border-radius: var(--radius-lg);
          transition: all 0.3s ease;

          &:active {
            color: #fff;
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: scale(0.98);
          }

          &.active {
            color: #fff;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-color: var(--primary-color);
            box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
            transform: translateY(-2rpx);

            text:first-child {
              color: #fff;
            }
          }

          &.reset-btn {
            color: #dc2626;
            background: #fee2e2;
            border-color: #fecaca;

            &:active {
              color: #dc2626;
              background: #fecaca;
              border-color: #f87171;
            }
          }

          text:first-child {
            font-size: 24rpx;
          }

          .filter-active-indicator {
            position: absolute;
            top: -6rpx;
            right: -6rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24rpx;
            height: 24rpx;
            background: #10b981;
            border-radius: 50%;
            box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);

            text {
              font-size: 14rpx;
              color: #fff;
            }
          }
        }
      }
    }
  }

  .right-window-footer {
    padding: 24rpx 32rpx 40rpx;
    background: var(--bg-primary);
    border-top: 1rpx solid var(--border-color);

    .footer-btn {
      display: flex;
      gap: 12rpx;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      font-size: 28rpx;
      font-weight: 700;
      color: #fff;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      border: none;
      border-radius: var(--radius-lg);
      box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:active {
        box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.4);
        transform: translateY(2rpx);
      }

      text:first-child {
        font-size: 32rpx;
      }
    }
  }
}

// 右侧弹窗动画
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

// 深色模式支持（可选）
@media (prefers-color-scheme: dark) {
  .select-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
  }

  .welcome-banner {
    background: linear-gradient(135deg, #0d4f3c 0%, #2d5a5a 100%);
  }

  .right-window {
    background: #1f1f1f;
  }
}

// 滚动条美化
::-webkit-scrollbar {
  width: 8rpx;
  height: 8rpx;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4rpx;

  &:hover {
    background: var(--primary-dark);
  }
}

// 无障碍优化
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes wave {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

// 响应式设计
@media (max-width: 750px) {
  .welcome-banner {
    .banner-content {
      flex-direction: column;
      gap: 20rpx;
      align-items: flex-start;

      .banner-icon {
        align-self: center;
      }
    }

    .banner-stats {
      flex-wrap: wrap;
      gap: 16rpx;
    }
  }

  .mode-section .mode-grid {
    flex-direction: column;
    gap: 16rpx;
  }

  .job-section .job-list .job-card {
    .job-details .detail-row {
      flex-direction: column;
      gap: 12rpx;
    }

    .job-actions {
      flex-direction: row;
      gap: 12rpx;
    }
  }

  .device-check-modal {
    width: 95%;
    margin: 20rpx;

    .modal-footer {
      flex-direction: column;
      gap: 12rpx;
    }
  }
}

// 平板适配
@media (min-width: 768px) {
  .main-content {
    max-width: 1200rpx;
    margin: 0 auto;
  }

  .job-section .job-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;

    .job-card {
      margin-bottom: 0;
    }
  }

  .mode-section .mode-grid {
    max-width: 800rpx;
    margin: 0 auto;
  }
}

// 增强动画效果
@keyframes floatIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30rpx);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 进入动画
.welcome-banner {
  animation: floatIn 0.6s ease-out;
}

.search-section {
  animation: floatIn 0.6s ease-out 0.1s both;
}

.mode-section {
  animation: floatIn 0.6s ease-out 0.2s both;
}

.category-section {
  animation: floatIn 0.6s ease-out 0.3s both;
}

.job-section {
  animation: floatIn 0.6s ease-out 0.4s both;
}

// 职位卡片交错动画
.job-list .job-card {
  animation: slideInLeft 0.5s ease-out;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.3s;
  }

  &:nth-child(4) {
    animation-delay: 0.4s;
  }

  &:nth-child(5) {
    animation-delay: 0.5s;
  }
}

// 微交互优化
.search-input-wrapper:hover {
  box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.15);
}

.mode-card:hover {
  transform: translateY(-2rpx);
}

.category-card:hover {
  transform: translateY(-2rpx);
}

.job-card:hover {
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
}

// 加载更多状态
.load-more-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  margin: 20rpx 0;

  .loading-more,
  .no-more-data {
    display: flex;
    gap: 12rpx;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 16rpx;
    font-size: 24rpx;
    color: var(--text-secondary);
    text-align: center;
  }

  .loading-icon {
    font-size: 28rpx;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
  }

  .no-more-text {
    position: relative;
    padding: 0 30rpx;
    color: var(--text-tertiary);

    &:before,
    &:after {
      position: absolute;
      top: 50%;
      width: 80rpx;
      height: 1rpx;
      content: '';
      background: var(--border-color);
    }

    &:before {
      left: -60rpx;
    }

    &:after {
      right: -60rpx;
    }
  }
}
</style>
