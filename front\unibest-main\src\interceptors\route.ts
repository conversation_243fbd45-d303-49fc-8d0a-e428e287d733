/**
 * <AUTHOR>
 * @description 路由拦截，通常也是登录拦截和404页面拦截
 * @version 1.0.0
 * 可以设置路由白名单，或者黑名单，看业务需要选哪一个
 * 我这里应为大部分都可以随便进入，所以使用黑名单
 */
import { useUserStore } from '@/store'
import {
  needLoginPages as _needLoginPages,
  getNeedLoginPages,
  getLastPage,
  getAllPages,
} from '@/utils'

// TODO Check
const loginRoute = '/pages/auth/login'
const notFoundRoute = '/pages/404/404'

const isLogined = () => {
  const userStore = useUserStore()
  return userStore.isLogined
}

const isDev = import.meta.env.DEV

/**
 * 获取所有已配置的页面路径
 * @returns 所有页面路径的数组
 */
const getAllPagePaths = (): string[] => {
  // 获取所有页面，不过滤任何条件（传入空字符串）
  const allPages = getAllPages('')
  return allPages.map((page) => page.path)
}

/**
 * 检查页面是否存在
 * @param path 页面路径
 * @returns 页面是否存在
 */
const isPageExists = (path: string): boolean => {
  const allPagePaths = getAllPagePaths()
  return allPagePaths.includes(path)
}

// 黑名单登录拦截器 - （适用于大部分页面不需要登录，少部分页面需要登录）
const navigateToInterceptor = {
  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
  // 增加对相对路径的处理，BY 网友 @ideal
  invoke({ url }: { url: string }) {
    // console.log(url) // /pages/route-interceptor/index?name=feige&age=30
    let path = url.split('?')[0]
    console.log('拦截到的路径:', path)

    // 处理相对路径
    if (!path.startsWith('/')) {
      const currentPath = getLastPage().route
      const normalizedCurrentPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}`
      const baseDir = normalizedCurrentPath.substring(0, normalizedCurrentPath.lastIndexOf('/'))
      path = `${baseDir}/${path}`
    }

    // 首先检查页面是否存在（404检查）
    if (!isPageExists(path)) {
      console.log('页面不存在，跳转到404页面:', path)
      // 如果要跳转的页面本身就是404页面，则允许通过，避免无限循环
      if (path === notFoundRoute) {
        return true
      }
      // 跳转到404页面
      uni.redirectTo({ url: notFoundRoute })
      return false
    }

    // 页面存在，继续进行登录检查
    let needLoginPages: string[] = []
    // 为了防止开发时出现BUG，这里每次都获取一下。生产环境可以移到函数外，性能更好
    if (isDev) {
      needLoginPages = getNeedLoginPages()
    } else {
      needLoginPages = _needLoginPages
    }
    const isNeedLogin = needLoginPages.includes(path)
    if (!isNeedLogin) {
      return true
    }
    const hasLogin = isLogined()
    if (hasLogin) {
      return true
    }
    const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(url)}`
    uni.navigateTo({ url: redirectRoute })
    return false
  },
}

/**
 * 路由拦截器
 * 包含登录拦截和404页面拦截功能
 */
export const routeInterceptor = {
  install() {
    uni.addInterceptor('navigateTo', navigateToInterceptor)
    uni.addInterceptor('reLaunch', navigateToInterceptor)
    uni.addInterceptor('redirectTo', navigateToInterceptor)
    uni.addInterceptor('switchTab', navigateToInterceptor)
  },
}
