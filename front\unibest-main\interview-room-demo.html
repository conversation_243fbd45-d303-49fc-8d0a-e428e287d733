<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能面试助手 - 面试房间</title>
  <!-- Vue 3 CDN -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- Font Awesome 图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <!--
    智能面试助手 - 纯Vue 3实现版本

    功能特性：
    1. 完整的面试流程管理
    2. 实时摄像头预览和控制
    3. 录音功能和状态管理
    4. 情绪分析显示
    5. 问题切换和进度跟踪
    6. 响应式设计，支持移动端

    技术栈：
    - Vue 3 Composition API
    - Web MediaDevices API
    - CSS3 动画和响应式设计
    - Font Awesome 图标

    使用说明：
    1. 打开页面后会自动请求摄像头和麦克风权限
    2. 点击"开始面试"按钮开始面试流程
    3. 支持暂停、下一题、结束面试等操作
    4. 面试过程中会实时显示录音状态和情绪分析
  -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(to bottom, #f8fbfd, #edf4f8);
      min-height: 100vh;
    }

    .interview-room {
      position: relative;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    /* 加载遮罩 */
    .loading-overlay {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(6px);
    }

    .loading-content {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: center;
    }

    .loading-spinner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border-radius: 50%;
      box-shadow: 0 3px 10px rgba(0, 201, 167, 0.3);
    }

    .spinner-icon {
      font-size: 20px;
      color: #fff;
      animation: spin 1.5s linear infinite;
    }

    .loading-message {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    /* 顶部导航栏 */
    .header {
      position: sticky;
      top: 0;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 15px;
      background: #fff;
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
    }

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      color: #00c9a7;
      background: #f5f9fc;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: rgba(0, 201, 167, 0.1);
      transform: scale(0.95);
    }

    .header-center {
      flex: 1;
      text-align: center;
    }

    .title {
      margin-bottom: 2px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .subtitle {
      font-size: 12px;
      color: #666;
    }

    .time-display {
      min-width: 50px;
      padding: 4px 8px;
      font-size: 12px;
      font-weight: 600;
      color: #00c9a7;
      text-align: center;
      background: rgba(0, 201, 167, 0.1);
      border-radius: 10px;
    }

    /* 主内容区域 */
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      gap: 10px;
      min-height: 0;
      overflow: hidden;
    }

    /* 面试场景区域 */
    .interview-scene {
      display: flex;
      gap: 10px;
      min-height: 200px;
      max-height: 50vh;
      flex-shrink: 0;
    }

    .side-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-height: 0;
    }

    /* 摄像头容器 */
    .camera-container {
      flex: 1;
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      min-height: 100px;
      aspect-ratio: 16/9;
    }

    .camera-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .camera-wrapper.loading {
      background: #f5f5f5;
    }

    .camera-wrapper.error {
      background: #fff2f0;
    }

    .video-stream {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
    }

    .camera-wrapper.hidden .video-stream {
      opacity: 0;
    }

    .camera-hidden-notice {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
      color: #666;
    }

    .notice-icon {
      font-size: 24px;
    }

    .notice-text {
      font-size: 12px;
    }

    .camera-status {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
    }

    .loading-spinner-camera {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .loading-spinner-camera .spinner-icon {
      font-size: 24px;
      color: #00c9a7;
    }

    .status-text {
      font-size: 14px;
      color: #666;
    }

    .error-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .error-icon {
      font-size: 24px;
      color: #ff4d4f;
    }

    .error-text {
      font-size: 14px;
      color: #666;
      text-align: center;
    }

    .retry-btn,
    .test-btn {
      padding: 6px 12px;
      font-size: 12px;
      color: #fff;
      background: #00c9a7;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .retry-btn:hover,
    .test-btn:hover {
      background: #00a085;
      transform: scale(0.95);
    }

    .mic-status {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      color: #fff;
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .mic-status.active {
      background: rgba(0, 201, 167, 0.8);
      animation: pulse 2s infinite;
    }

    .camera-controls-panel {
      position: absolute;
      bottom: 10px;
      right: 10px;
      display: flex;
      gap: 6px;
    }

    .control-btn-mini {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background: rgba(0, 0, 0, 0.6);
      border: none;
      border-radius: 50%;
      color: #fff;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .control-btn-mini:hover {
      background: rgba(0, 201, 167, 0.8);
      transform: scale(0.9);
    }

    /* 情绪检测区域 */
    .emotion-detection {
      padding: 10px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .detection-header {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 10px;
    }

    .detection-icon {
      font-size: 16px;
      color: #00c9a7;
    }

    .detection-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .emotion-indicators {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .emotion-item {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .emotion-label {
      min-width: 40px;
      font-size: 12px;
      color: #666;
    }

    .emotion-bar-container {
      flex: 1;
      height: 6px;
      background: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
    }

    .emotion-bar {
      height: 100%;
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .emotion-value {
      min-width: 30px;
      font-size: 12px;
      font-weight: 600;
      color: #333;
      text-align: right;
    }

    /* 动画 */
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.8;
        transform: scale(1.05);
      }
    }

    /* 问题卡片 */
    .question-card {
      padding: 15px;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      margin-bottom: 10px;
    }

    .question-card.question-changing {
      transform: scale(0.98);
      opacity: 0.8;
    }

    .question-content .question-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .question-number {
      display: flex;
      align-items: baseline;
      gap: 2px;
    }

    .number-text {
      font-size: 24px;
      font-weight: 700;
      color: #00c9a7;
    }

    .total-text {
      font-size: 14px;
      color: #999;
    }

    .question-type-badge {
      padding: 4px 8px;
      font-size: 11px;
      font-weight: 500;
      border-radius: 10px;
    }

    .question-type-badge.type-technical {
      color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
    }

    .question-type-badge.type-experience {
      color: #52c41a;
      background: rgba(82, 196, 26, 0.1);
    }

    .question-type-badge.type-soft_skill {
      color: #fa8c16;
      background: rgba(250, 140, 22, 0.1);
    }

    .question-text {
      font-size: 16px;
      font-weight: 500;
      line-height: 1.6;
      color: #333;
      margin-bottom: 12px;
    }

    .tips-section {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      padding: 10px;
      background: #f8fbfd;
      border-radius: 6px;
      margin-bottom: 12px;
    }

    .tips-icon {
      font-size: 14px;
      color: #faad14;
      margin-top: 1px;
    }

    .tips-text {
      flex: 1;
      font-size: 13px;
      line-height: 1.5;
      color: #666;
    }

    .question-progress .progress-bar {
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    /* 悬浮窗样式 */
    .recording-status-float,
    .recording-placeholder-float {
      position: fixed;
      top: 100px;
      right: 15px;
      z-index: 100;
      padding: 6px 10px;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 10px;
      backdrop-filter: blur(5px);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
    }

    .recording-status-float {
      background: rgba(0, 201, 167, 0.9);
      animation: pulse 2s infinite;
    }

    .status-text,
    .placeholder-text {
      font-size: 12px;
      color: #fff;
      font-weight: 600;
    }

    .question-count {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.8);
    }

    .voice-indicator-float {
      position: fixed;
      top: 150px;
      right: 15px;
      z-index: 100;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 3px;
      padding: 6px 10px;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 10px;
      backdrop-filter: blur(5px);
    }

    .voice-icon {
      font-size: 12px;
      color: #00c9a7;
      animation: pulse 1.5s infinite;
    }

    .voice-text {
      font-size: 11px;
      color: #fff;
    }

    .recording-duration {
      font-size: 10px;
      color: #00c9a7;
      font-weight: 600;
      font-family: 'Courier New', monospace;
    }

    .recording-stats-float {
      position: fixed;
      top: 200px;
      left: 15px;
      z-index: 100;
      padding: 4px 8px;
      background: rgba(0, 201, 167, 0.9);
      border-radius: 8px;
      backdrop-filter: blur(5px);
    }

    .stats-text {
      font-size: 10px;
      color: #fff;
      font-weight: 500;
    }

    /* 时间提醒悬浮窗 */
    .time-reminder-float {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 200;
      animation: slideInDown 0.3s ease;
      max-width: calc(100vw - 40px);
    }

    .reminder-content {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 15px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .reminder-content.warning {
      border-left: 3px solid #faad14;
    }

    .reminder-content.danger {
      border-left: 3px solid #ff4d4f;
    }

    .reminder-icon {
      font-size: 16px;
      flex-shrink: 0;
    }

    .reminder-icon.fas.fa-clock {
      color: #faad14;
    }

    .reminder-icon.fas.fa-exclamation-triangle {
      color: #ff4d4f;
    }

    .reminder-text {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      flex: 1;
    }

    .dismiss-reminder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background: transparent;
      border: none;
      border-radius: 50%;
      color: #999;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .dismiss-reminder:hover {
      background: #f5f5f5;
      color: #666;
    }

    /* 智能建议悬浮窗 */
    .smart-suggestions-float {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 150;
      max-width: 300px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      animation: slideInDown 0.3s ease;
    }

    .suggestions-header {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .suggestions-icon {
      font-size: 14px;
      color: #faad14;
    }

    .suggestions-title {
      flex: 1;
      font-size: 13px;
      font-weight: 600;
      color: #333;
    }

    .close-all-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;
      background: transparent;
      border: none;
      border-radius: 50%;
      color: #999;
      font-size: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .close-all-btn:hover {
      background: #f5f5f5;
      color: #666;
    }

    .suggestions-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .suggestion-card {
      padding: 8px 12px;
      border-bottom: 1px solid #f8f8f8;
    }

    .suggestion-card:last-child {
      border-bottom: none;
    }

    .suggestion-card.level-high {
      border-left: 3px solid #ff4d4f;
    }

    .suggestion-card.level-medium {
      border-left: 3px solid #faad14;
    }

    .suggestion-card.level-low {
      border-left: 3px solid #52c41a;
    }

    .suggestion-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .suggestion-header {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .suggestion-type-icon {
      font-size: 12px;
      color: #666;
    }

    .suggestion-title-text {
      flex: 1;
      font-size: 12px;
      font-weight: 600;
      color: #333;
    }

    .dismiss-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      background: transparent;
      border: none;
      border-radius: 50%;
      color: #999;
      font-size: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .dismiss-btn:hover {
      background: #f5f5f5;
      color: #666;
    }

    .suggestion-message {
      font-size: 11px;
      line-height: 1.4;
      color: #666;
    }

    .suggestion-action {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 10px;
      color: #00c9a7;
    }

    .action-icon {
      font-size: 8px;
    }

    /* 文本输入区域 */
    .input-area {
      padding: 10px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      margin-bottom: 10px;
    }

    .text-input {
      width: 100%;
      min-height: 60px;
      padding: 8px;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      background: #f8f9fa;
      border: 1px solid transparent;
      border-radius: 6px;
      resize: vertical;
      transition: all 0.3s ease;
    }

    .text-input:focus {
      border-color: #00c9a7;
      background: #fff;
      box-shadow: 0 0 0 2px rgba(0, 201, 167, 0.1);
      outline: none;
    }

    .text-input::placeholder {
      color: #999;
    }

    .input-meta {
      display: flex;
      justify-content: flex-end;
      margin-top: 6px;
    }

    .char-count {
      font-size: 12px;
      color: #999;
    }

    /* 底部控制区域 */
    .footer-controls {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      padding: 15px;
      background: #fff;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
    }

    .start-interview-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 20px;
      font-size: 14px;
      font-weight: 600;
      color: #fff;
      background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
      border: none;
      border-radius: 25px;
      box-shadow: 0 3px 10px rgba(0, 201, 167, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .start-interview-btn:hover {
      background: linear-gradient(135deg, #00a085 0%, #3fb8a8 100%);
      transform: scale(0.95);
    }

    .control-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      color: #666;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .control-btn:hover {
      background: #e6e6e6;
      transform: scale(0.9);
    }

    .control-btn.btn-active {
      background: #00c9a7;
      color: #fff;
    }

    .control-btn.btn-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .end-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 20px;
      font-size: 14px;
      font-weight: 600;
      color: #fff;
      background: #ff4d4f;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .end-btn:hover {
      background: #d9363e;
      transform: scale(0.95);
    }

    /* 答题完成庆祝动画 */
    .completion-celebration {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 1);
      animation: fadeIn 0.3s ease;
    }

    .celebration-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
      padding: 30px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
      animation: slideInUp 0.5s ease;
    }

    .celebration-icon {
      font-size: 60px;
      color: #52c41a;
      animation: pulse 1s infinite;
    }

    .celebration-text {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .celebration-particles {
      position: relative;
      width: 100px;
      height: 100px;
    }

    .particle {
      position: absolute;
      width: 8px;
      height: 8px;
      background: #faad14;
      border-radius: 50%;
      animation: particleFloat 2s ease-out forwards;
    }

    .particle:nth-child(1) {
      top: 50%;
      left: 50%;
      animation-delay: var(--delay, 0s);
      transform: translate(-50%, -50%);
    }

    .particle:nth-child(2) {
      top: 30%;
      left: 70%;
      background: #52c41a;
      animation-delay: var(--delay, 0.1s);
    }

    .particle:nth-child(3) {
      top: 70%;
      left: 30%;
      background: #1890ff;
      animation-delay: var(--delay, 0.2s);
    }

    .particle:nth-child(4) {
      top: 20%;
      left: 30%;
      background: #fa8c16;
      animation-delay: var(--delay, 0.3s);
    }

    .particle:nth-child(5) {
      top: 80%;
      left: 70%;
      background: #eb2f96;
      animation-delay: var(--delay, 0.4s);
    }

    .particle:nth-child(6) {
      top: 50%;
      left: 20%;
      background: #722ed1;
      animation-delay: var(--delay, 0.5s);
    }

    @keyframes particleFloat {
      0% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
      100% {
        opacity: 0;
        transform: translateY(-50px) scale(0.5);
      }
    }

    /* 面试完成动画 */
    .interview-completion {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      animation: fadeIn 0.3s ease forwards;
    }

    .completion-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(0, 201, 167, 1) 0%, rgba(79, 209, 199, 0.9) 100%);
      backdrop-filter: blur(10px);
    }

    .completion-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      padding: 40px 30px;
      background: #fff;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      animation: slideInUp 0.5s ease;
    }

    .completion-icon {
      font-size: 80px;
      color: #faad14;
      animation: pulse 2s infinite;
    }

    .completion-title {
      font-size: 24px;
      font-weight: 700;
      color: #333;
    }

    .completion-subtitle {
      font-size: 14px;
      color: #666;
    }

    .completion-progress {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .progress-circle {
      position: relative;
      width: 60px;
      height: 60px;
      border: 4px solid #f0f0f0;
      border-radius: 50%;
      overflow: hidden;
    }

    .progress-circle .progress-fill {
      position: absolute;
      top: -4px;
      left: -4px;
      width: 60px;
      height: 60px;
      border: 4px solid #00c9a7;
      border-radius: 50%;
      border-right-color: transparent;
      border-bottom-color: transparent;
      transform-origin: center;
      transition: transform 0.3s ease;
    }

    .progress-text {
      position: absolute;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes slideInUp {
      0% {
        opacity: 0;
        transform: translateY(30px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes slideInDown {
      0% {
        opacity: 0;
        transform: translateY(-20px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 响应式设计 */
    @media (max-height: 600px) {
      .interview-scene {
        min-height: 150px;
        max-height: 40vh;
      }
    }

    @media (max-width: 768px) {
      .interview-scene {
        flex-direction: column;
        min-height: 150px;
        max-height: 60vh;
      }

      .side-content {
        min-height: 100px;
      }

      .camera-container {
        min-height: 90px;
      }

      .main-content {
        padding-bottom: 80px;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="interview-room">
      <!-- 全屏加载遮罩 -->
      <div class="loading-overlay" v-if="loadingState.isInitializing">
        <div class="loading-content">
          <div class="loading-spinner">
            <i class="fas fa-spinner spinner-icon"></i>
          </div>
          <span class="loading-message">{{ loadingState.message || '正在加载...' }}</span>
        </div>
      </div>

      <!-- 顶部导航栏 -->
      <div class="header">
        <button class="back-btn" @click="exitInterview">
          <i class="fas fa-chevron-left"></i>
        </button>
        <div class="header-center">
          <div class="title">智能面试助手</div>
          <div class="subtitle">
            第 {{ interviewState.currentQuestion + 1 }}/{{ interviewState.totalQuestions }} 题
          </div>
        </div>
        <div class="time-display">
          <span>{{ formatTime(interviewState.timeRemaining) }}</span>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 面试场景区域 -->
        <div class="interview-scene" v-if="!loadingState.isInitializing">
          <!-- 右侧内容区域 -->
          <div class="side-content">
            <!-- 摄像头预览区域 -->
            <div class="camera-container">
              <div
                class="camera-wrapper"
                :class="{
                  loading: cameraStatus.isLoading,
                  error: cameraStatus.error,
                  hidden: !showUserVideo,
                }"
              >
                <!-- 视频流 -->
                <video
                  ref="videoRef"
                  class="video-stream"
                  autoplay
                  playsinline
                  muted
                  v-show="cameraStatus.isReady && !cameraStatus.error"
                ></video>

                <!-- 摄像头隐藏提示 -->
                <div class="camera-hidden-notice" v-if="!showUserVideo && cameraStatus.isReady">
                  <i class="fas fa-eye-slash notice-icon"></i>
                  <span class="notice-text">摄像头已隐藏</span>
                </div>

                <!-- 加载中或错误状态显示 -->
                <div class="camera-status" v-if="cameraStatus.isLoading || cameraStatus.error">
                  <div v-if="cameraStatus.isLoading" class="loading-spinner-camera">
                    <i class="fas fa-spinner fa-spin spinner-icon"></i>
                    <span class="status-text">加载中...</span>
                  </div>
                  <div v-else-if="cameraStatus.error" class="error-info">
                    <i class="fas fa-exclamation-circle error-icon"></i>
                    <span class="error-text">{{ cameraStatus.error }}</span>
                    <button class="retry-btn" @click="initMediaDevices">重试</button>
                  </div>
                </div>

                <!-- 麦克风状态指示器 -->
                <div
                  class="mic-status"
                  :class="{ active: audioStatus.isReady && interviewState.isRecording }"
                >
                  <i class="fas fa-microphone" v-if="audioStatus.isReady"></i>
                  <i class="fas fa-microphone-slash" v-else></i>
                </div>

                <!-- 摄像头控制按钮组 -->
                <div class="camera-controls-panel">
                  <button class="control-btn-mini" @click="toggleUserVideo">
                    <i :class="showUserVideo ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                  </button>
                  <button class="control-btn-mini" @click="switchCamera">
                    <i class="fas fa-camera"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 表情检测结果区域 -->
            <div class="emotion-detection" v-if="interviewState.isRecording">
              <div class="detection-header">
                <i class="fas fa-smile detection-icon"></i>
                <span class="detection-title">情绪分析</span>
              </div>
              <div class="emotion-indicators">
                <div class="emotion-item" v-for="(value, emotion) in emotionData" :key="emotion">
                  <span class="emotion-label">{{ getEmotionLabel(emotion) }}</span>
                  <div class="emotion-bar-container">
                    <div
                      class="emotion-bar"
                      :style="{ width: value + '%', background: getEmotionColor(emotion) }"
                    ></div>
                  </div>
                  <span class="emotion-value">{{ value }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 问题卡片 -->
        <div class="question-card" :class="{ 'question-changing': isQuestionChanging }">
          <div class="question-content" :key="interviewState.currentQuestion">
            <div class="question-header">
              <div class="question-number">
                <span class="number-text">{{ interviewState.currentQuestion + 1 }}</span>
                <span class="total-text">/{{ interviewState.totalQuestions }}</span>
              </div>
              <div class="question-type-badge" :class="`type-${currentQuestionData.type}`">
                <span class="type-text">{{ getQuestionTypeLabel(currentQuestionData.type) }}</span>
              </div>
            </div>

            <div class="question-text">{{ currentQuestionData.question }}</div>

            <div class="tips-section" v-if="currentQuestionData.tips">
              <div class="tips-icon">
                <i class="fas fa-lightbulb"></i>
              </div>
              <span class="tips-text">{{ currentQuestionData.tips }}</span>
            </div>

            <div class="question-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: ((interviewState.currentQuestion + 1) / interviewState.totalQuestions) * 100 + '%',
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 录制状态悬浮窗 -->
        <div class="recording-status-float" v-if="interviewState.isRecording">
          <span class="status-text">录制中</span>
          <span class="question-count">第{{ interviewState.currentQuestion + 1 }}题</span>
        </div>
        <div class="recording-placeholder-float" v-else-if="interviewState.isStarted">
          <span class="placeholder-text">录制暂停</span>
        </div>
        <div class="recording-placeholder-float" v-else>
          <span class="placeholder-text">准备录制</span>
        </div>

        <!-- 语音状态指示器悬浮窗 -->
        <div class="voice-indicator-float" v-if="interviewState.isRecording">
          <i class="fas fa-waveform voice-icon"></i>
          <span class="voice-text">正在录音</span>
          <span class="recording-duration">{{ formatRecordingDuration() }}</span>
        </div>

        <!-- 录音统计悬浮窗 -->
        <div
          class="recording-stats-float"
          v-if="interviewState.isStarted && questionRecordings.length > 0"
        >
          <span class="stats-text">已录制: {{ questionRecordings.length }}题</span>
        </div>

        <!-- 时间提醒悬浮窗 -->
        <div class="time-reminder-float" v-if="timeReminder.show">
          <div class="reminder-content" :class="timeReminder.type">
            <i class="reminder-icon" :class="timeReminder.icon"></i>
            <span class="reminder-text">{{ timeReminder.message }}</span>
            <button class="dismiss-reminder" @click="dismissTimeReminder">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- 实时智能建议悬浮窗 -->
        <div
          class="smart-suggestions-float"
          v-if="interviewState.isRecording && realTimeSuggestions.length > 0"
        >
          <div class="suggestions-header">
            <i class="fas fa-lightbulb suggestions-icon"></i>
            <span class="suggestions-title">智能建议</span>
            <button class="close-all-btn" @click="realTimeSuggestions = []">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="suggestions-list">
            <div
              class="suggestion-card"
              v-for="suggestion in realTimeSuggestions"
              :key="suggestion.id"
              :class="[`level-${suggestion.level}`, `type-${suggestion.type}`]"
            >
              <div class="suggestion-content">
                <div class="suggestion-header">
                  <i :class="suggestion.icon" class="suggestion-type-icon"></i>
                  <span class="suggestion-title-text">{{ suggestion.title }}</span>
                  <button class="dismiss-btn" @click="dismissSuggestion(suggestion.id)">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <span class="suggestion-message">{{ suggestion.message }}</span>
                <div class="suggestion-action" v-if="suggestion.action">
                  <i class="fas fa-arrow-right action-icon"></i>
                  <span class="action-text">{{ suggestion.action }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文本输入区域 -->
        <div class="input-area">
          <textarea
            class="text-input"
            placeholder="可在此补充文字回答..."
            v-model="userAnswer"
            maxlength="500"
          ></textarea>
          <div class="input-meta">
            <span class="char-count">{{ userAnswer.length }}/500</span>
          </div>
        </div>
      </div>

      <!-- 底部控制区域 -->
      <div class="footer-controls">
        <!-- 未开始状态 -->
        <template v-if="!interviewState.isStarted">
          <button class="start-interview-btn" @click="startInterview">
            <i class="fas fa-play"></i>
            <span>开始面试</span>
          </button>
        </template>

        <!-- 进行中状态 -->
        <template v-else>
          <button
            class="control-btn pause-btn"
            @click="togglePause"
            :class="{ 'btn-active': interviewState.isPaused }"
          >
            <i :class="interviewState.isPaused ? 'fas fa-play' : 'fas fa-pause'"></i>
          </button>

          <button
            class="control-btn next-btn"
            @click="nextQuestion"
            :disabled="interviewState.currentQuestion >= interviewState.totalQuestions - 1"
            :class="{
              'btn-disabled': interviewState.currentQuestion >= interviewState.totalQuestions - 1,
            }"
          >
            <i class="fas fa-step-forward"></i>
          </button>

          <button class="end-btn" @click="endInterview">
            <i class="fas fa-stop"></i>
            <span>结束</span>
          </button>
        </template>
      </div>

      <!-- 答题完成庆祝动画 -->
      <div class="completion-celebration" v-if="showCompletionAnimation">
        <div class="celebration-content">
          <div class="celebration-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="celebration-text">题目回答完成！</div>
          <div class="celebration-particles">
            <div
              class="particle"
              v-for="i in 6"
              :key="i"
              :style="{ '--delay': i * 0.1 + 's' }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 面试完成动画 -->
      <div class="interview-completion" v-if="showInterviewCompletion">
        <div class="completion-overlay"></div>
        <div class="completion-content">
          <div class="completion-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="completion-title">面试完成！</div>
          <div class="completion-subtitle">正在生成面试报告...</div>
          <div class="completion-progress">
            <div class="progress-circle">
              <div
                class="progress-fill"
                :style="{ transform: `rotate(${completionProgress * 3.6}deg)` }"
              ></div>
            </div>
            <span class="progress-text">{{ completionProgress }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    const { createApp, ref, computed, onMounted, onUnmounted, nextTick } = Vue;

    createApp({
      setup() {
        // 面试会话参数
        const sessionParams = ref({
          sessionId: '',
          jobId: 0,
          mode: 'standard',
          isDemo: false,
          jobName: '',
          company: '',
        });

        // 媒体录制管理
        const mediaRecorder = ref(null);
        const recordedChunks = ref([]);
        const videoStream = ref(null);
        const audioStream = ref(null);
        const videoRef = ref(null);

        const cameraStatus = ref({
          isReady: false,
          isLoading: false,
          error: '',
          isFrontCamera: true,
        });

        const audioStatus = ref({
          isReady: false,
          isLoading: false,
          error: '',
        });

        // 录音数据管理
        const questionRecordings = ref([]);
        const currentRecording = ref({
          isRecording: false,
          startTime: 0,
          questionId: 0,
        });

        // 录音时长显示
        const recordingDuration = ref(0);
        const recordingTimer = ref(null);

        // 演示题目数据
        const questionBank = ref([
          {
            id: 1,
            type: 'technical',
            question: '请介绍一下你最熟悉的前端框架，并说明它的主要特点和适用场景。',
            tips: '建议使用STAR法则来回答：Situation(情况)、Task(任务)、Action(行动)、Result(结果)',
            timeLimit: 180,
            difficulty: 3,
            expectedKeywords: ['Vue', 'React', 'Angular', '组件化', '响应式', '虚拟DOM'],
            evaluationPoints: ['技术深度', '实际应用', '场景分析'],
          },
          {
            id: 2,
            type: 'experience',
            question: '请描述一个你在项目中遇到的技术难题，以及你是如何解决的？',
            tips: '重点说明问题的复杂性、解决思路和最终效果',
            timeLimit: 240,
            difficulty: 4,
            expectedKeywords: ['问题分析', '解决方案', '技术选型', '效果评估'],
            evaluationPoints: ['问题理解', '解决能力', '技术深度'],
          },
          {
            id: 3,
            type: 'soft_skill',
            question: '在团队协作中，你如何处理与同事的技术分歧？',
            tips: '展现你的沟通能力和团队合作精神',
            timeLimit: 180,
            difficulty: 2,
            expectedKeywords: ['沟通', '协作', '妥协', '共识'],
            evaluationPoints: ['沟通能力', '团队意识', '解决冲突'],
          },
          {
            id: 4,
            type: 'technical',
            question: '请解释JavaScript的事件循环机制，以及异步编程的最佳实践。',
            tips: '结合具体例子来说明概念和应用',
            timeLimit: 240,
            difficulty: 4,
            expectedKeywords: ['事件循环', 'Promise', 'async/await', '微任务', '宏任务'],
            evaluationPoints: ['基础理论', '实际应用', '代码质量'],
          },
          {
            id: 5,
            type: 'career',
            question: '你的职业规划是什么？为什么选择我们公司？',
            tips: '结合个人发展和公司发展方向来回答',
            timeLimit: 180,
            difficulty: 2,
            expectedKeywords: ['职业发展', '技能提升', '公司文化', '个人价值'],
            evaluationPoints: ['职业规划', '求职动机', '价值匹配'],
          },
        ]);

        // 面试状态管理
        const interviewState = ref({
          isStarted: false,
          isPaused: false,
          isRecording: false,
          currentQuestion: 0,
          timeRemaining: 1800,
          totalQuestions: questionBank.value.length,
          startTime: null,
        });

        // 当前问题数据
        const currentQuestionData = computed(() => {
          return questionBank.value[interviewState.value.currentQuestion] || questionBank.value[0];
        });

        // 用户回答
        const userAnswer = ref('');

        // 加载状态
        const loadingState = ref({
          isInitializing: false,
          message: '',
          progress: 0,
        });

        // 显示状态
        const showUserVideo = ref(true);
        const emotionData = ref({
          happy: 78,
          neutral: 12,
          sad: 5,
          angry: 2,
          surprised: 3,
        });

        // 动画相关状态
        const isQuestionChanging = ref(false);
        const showCompletionAnimation = ref(false);
        const showInterviewCompletion = ref(false);
        const completionProgress = ref(0);

        // 时间提醒状态
        const timeReminder = ref({
          show: false,
          type: 'info',
          icon: 'fas fa-clock',
          message: '',
          questionStartTime: 0,
          shown: false,
          lastReminder: 0,
        });

        // 实时建议数据
        const realTimeSuggestions = ref([]);

        // 定时器管理
        let timer = null;
        const timers = [];

        const clearAllTimers = () => {
          timers.forEach((id) => clearTimeout(id));
          timers.length = 0;
          if (timer) {
            clearInterval(timer);
            timer = null;
          }
        };

        /**
         * 切换用户视频显示状态
         */
        const toggleUserVideo = () => {
          showUserVideo.value = !showUserVideo.value;
        };

        /**
         * 获取情绪标签
         */
        const getEmotionLabel = (emotion) => {
          const labels = {
            happy: '开心',
            neutral: '平静',
            sad: '悲伤',
            angry: '生气',
            surprised: '惊讶',
            fear: '恐惧',
            disgusted: '厌恶',
            contempt: '轻蔑',
            confused: '困惑',
          };
          return labels[emotion] || emotion;
        };

        /**
         * 获取情绪颜色
         */
        const getEmotionColor = (emotion) => {
          const colors = {
            happy: '#52C41A',
            neutral: '#1890FF',
            sad: '#722ED1',
            angry: '#F5222D',
            surprised: '#FA8C16',
            fear: '#EB2F96',
            disgusted: '#FA541C',
            contempt: '#13C2C2',
            confused: '#FAAD14',
          };
          return colors[emotion] || '#999';
        };

        /**
         * 格式化时间显示
         */
        const formatTime = (seconds) => {
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        };

        /**
         * 格式化录音时长显示
         */
        const formatRecordingDuration = () => {
          const minutes = Math.floor(recordingDuration.value / 60);
          const seconds = recordingDuration.value % 60;
          return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        };

        /**
         * 获取问题类型标签
         */
        const getQuestionTypeLabel = (type) => {
          const labels = {
            technical: '技术',
            experience: '经验',
            soft_skill: '软技能',
            architecture: '架构',
            career: '职业',
            engineering: '工程化',
            decision: '决策',
            ux: '用户体验',
            question: '提问',
          };
          return labels[type] || type;
        };

        /**
         * 初始化媒体设备
         */
        const initMediaDevices = async () => {
          try {
            cameraStatus.value.isLoading = true;
            audioStatus.value.isLoading = true;
            cameraStatus.value.error = '';
            audioStatus.value.error = '';

            // 初始化摄像头
            await initCamera();
            // 初始化录音设备
            await initAudioRecording();

            cameraStatus.value.isReady = true;
            cameraStatus.value.isLoading = false;
            audioStatus.value.isReady = true;
            audioStatus.value.isLoading = false;
          } catch (error) {
            cameraStatus.value.error = '设备初始化失败';
            audioStatus.value.error = '录音设备初始化失败';
            cameraStatus.value.isLoading = false;
            audioStatus.value.isLoading = false;
          }
        };

        /**
         * 初始化摄像头
         */
        const initCamera = async () => {
          try {
            console.log('开始初始化摄像头，使用', cameraStatus.value.isFrontCamera ? '前置' : '后置', '摄像头');

            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
              throw new Error('浏览器不支持摄像头功能');
            }

            // 停止之前的流
            if (videoStream.value) {
              console.log('停止之前的视频流');
              videoStream.value.getTracks().forEach((track) => track.stop());
            }

            const constraints = {
              video: {
                facingMode: cameraStatus.value.isFrontCamera ? 'user' : 'environment',
                width: { ideal: 640 },
                height: { ideal: 480 },
              },
              audio: false,
            };

            console.log('请求摄像头权限，约束条件:', constraints);
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            videoStream.value = stream;
            console.log('获取视频流成功');

            if (videoRef.value) {
              console.log('设置视频元素源');
              videoRef.value.srcObject = stream;

              await new Promise((resolve, reject) => {
                videoRef.value.onloadedmetadata = () => {
                  console.log('视频元数据加载完成');
                  resolve();
                };
                videoRef.value.onerror = () => reject(new Error('视频加载失败'));
                setTimeout(() => reject(new Error('视频加载超时')), 10000);
              });

              try {
                await videoRef.value.play();
                console.log('视频播放成功');
              } catch (playError) {
                console.warn('自动播放失败，可能需要用户交互:', playError);
              }
            } else {
              console.warn('videoRef.value 为空，无法设置视频源');
            }

            console.log('摄像头初始化成功');
          } catch (error) {
            console.error('摄像头初始化失败:', error);
            throw error;
          }
        };

        /**
         * 初始化录音设备
         */
        const initAudioRecording = async () => {
          try {
            audioStream.value = await navigator.mediaDevices.getUserMedia({
              audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 44100,
                channelCount: 1,
              },
            });

            mediaRecorder.value = new MediaRecorder(audioStream.value, {
              mimeType: 'audio/webm;codecs=opus',
            });

            setupRecordingEvents();
            console.log('录音设备初始化成功');
          } catch (error) {
            console.error('录音设备初始化失败:', error);
            throw error;
          }
        };

        /**
         * 设置录音事件监听
         */
        const setupRecordingEvents = () => {
          if (!mediaRecorder.value) return;

          mediaRecorder.value.ondataavailable = (event) => {
            if (event.data.size > 0) {
              recordedChunks.value.push(event.data);
            }
          };

          mediaRecorder.value.onstop = () => {
            if (recordedChunks.value.length > 0) {
              const audioBlob = new Blob(recordedChunks.value, { type: 'audio/webm' });
              saveQuestionRecording(audioBlob);
              recordedChunks.value = [];
            }
          };

          mediaRecorder.value.onstart = () => {
            console.log('录音开始');
            currentRecording.value.isRecording = true;
            interviewState.value.isRecording = true;
          };

          mediaRecorder.value.onerror = (event) => {
            console.error('录音错误:', event.error);
            audioStatus.value.error = '录音过程中发生错误';
          };
        };

        /**
         * 保存问题录音数据
         */
        const saveQuestionRecording = (audioBlob) => {
          const endTime = Date.now();
          const startTime = currentRecording.value.startTime;
          const duration = endTime - startTime;

          questionRecordings.value.push({
            questionId: currentRecording.value.questionId,
            questionText: currentQuestionData.value.question,
            audioBlob,
            startTime,
            endTime,
            duration,
          });

          console.log(`问题 ${currentRecording.value.questionId + 1} 录音保存成功，时长: ${Math.round(duration / 1000)}秒`);
          currentRecording.value.isRecording = false;
          currentRecording.value.startTime = 0;
        };

        /**
         * 切换摄像头
         */
        const switchCamera = async () => {
          cameraStatus.value.isFrontCamera = !cameraStatus.value.isFrontCamera;
          try {
            await initCamera();
            console.log('摄像头切换成功:', cameraStatus.value.isFrontCamera ? '前置' : '后置');
          } catch (error) {
            console.error('切换摄像头失败:', error);
            cameraStatus.value.error = '切换摄像头失败';
          }
        };

        /**
         * 开始面试
         */
        const startInterview = async () => {
          interviewState.value.isStarted = true;
          interviewState.value.startTime = Date.now();

          // 启动计时器
          startTimer();

          // 开始第一题的录音
          await startRecording();

          console.log('面试开始，第一题录音已启动');
        };

        /**
         * 启动计时器
         */
        const startTimer = () => {
          timer = setInterval(() => {
            if (!interviewState.value.isPaused && interviewState.value.timeRemaining > 0) {
              interviewState.value.timeRemaining--;

              // 时间提醒
              checkTimeReminder();
            }
          }, 1000);
        };

        /**
         * 检查时间提醒
         */
        const checkTimeReminder = () => {
          const remaining = interviewState.value.timeRemaining;

          if (remaining === 300 && !timeReminder.value.shown) {
            // 5分钟提醒
            showTimeReminder('warning', '还剩5分钟');
          } else if (remaining === 60) {
            // 1分钟提醒
            showTimeReminder('danger', '还剩1分钟');
          }
        };

        /**
         * 显示时间提醒
         */
        const showTimeReminder = (type, message) => {
          timeReminder.value = {
            show: true,
            type,
            icon: type === 'danger' ? 'fas fa-exclamation-triangle' : 'fas fa-clock',
            message,
            questionStartTime: Date.now(),
            shown: true,
            lastReminder: Date.now(),
          };

          // 3秒后自动隐藏
          setTimeout(() => {
            timeReminder.value.show = false;
          }, 3000);
        };

        /**
         * 关闭时间提醒
         */
        const dismissTimeReminder = () => {
          timeReminder.value.show = false;
        };

        /**
         * 关闭建议
         */
        const dismissSuggestion = (id) => {
          const index = realTimeSuggestions.value.findIndex((s) => s.id === id);
          if (index > -1) {
            realTimeSuggestions.value.splice(index, 1);
          }
        };

        /**
         * 生成智能建议（模拟）
         */
        const generateSmartSuggestions = () => {
          // 模拟智能建议数据
          const suggestions = [
            {
              id: Date.now() + Math.random(),
              type: 'speaking',
              level: 'medium',
              icon: 'fas fa-microphone',
              title: '语速建议',
              message: '建议适当放慢语速，让面试官更好地理解您的回答',
              action: '调整语速'
            },
            {
              id: Date.now() + Math.random() + 1,
              type: 'content',
              level: 'high',
              icon: 'fas fa-lightbulb',
              title: '内容建议',
              message: '可以结合具体的项目经验来回答这个问题',
              action: '添加实例'
            }
          ];

          // 随机显示建议
          if (Math.random() > 0.7 && realTimeSuggestions.value.length < 2) {
            const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
            if (!realTimeSuggestions.value.find(s => s.type === randomSuggestion.type)) {
              realTimeSuggestions.value.push(randomSuggestion);
            }
          }
        };

        /**
         * 开始录音
         */
        const startRecording = async () => {
          if (!audioStatus.value.isReady) {
            console.warn('录音设备未准备就绪');
            return;
          }

          try {
            currentRecording.value.startTime = Date.now();
            currentRecording.value.questionId = interviewState.value.currentQuestion;
            currentRecording.value.isRecording = true;

            interviewState.value.isRecording = true;

            // 开始录音时长计时器
            startRecordingTimer();

            if (mediaRecorder.value && mediaRecorder.value.state === 'inactive') {
              recordedChunks.value = [];
              mediaRecorder.value.start(1000);
              console.log('录音开始');
            }

            // 定期生成智能建议
            const suggestionInterval = setInterval(() => {
              if (interviewState.value.isRecording) {
                generateSmartSuggestions();
              } else {
                clearInterval(suggestionInterval);
              }
            }, 10000); // 每10秒检查一次
          } catch (error) {
            console.error('开始录音失败:', error);
            audioStatus.value.error = '开始录音失败';
          }
        };

        /**
         * 停止录音
         */
        const stopRecording = async () => {
          if (!currentRecording.value.isRecording) {
            return;
          }

          try {
            interviewState.value.isRecording = false;
            currentRecording.value.isRecording = false;

            stopRecordingTimer();

            if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
              mediaRecorder.value.stop();
              console.log('录音停止');
            }
          } catch (error) {
            console.error('停止录音失败:', error);
            audioStatus.value.error = '停止录音失败';
          }
        };

        /**
         * 开始录音时长计时器
         */
        const startRecordingTimer = () => {
          recordingDuration.value = 0;
          recordingTimer.value = setInterval(() => {
            if (currentRecording.value.isRecording && currentRecording.value.startTime) {
              recordingDuration.value = Math.floor((Date.now() - currentRecording.value.startTime) / 1000);
            }
          }, 1000);
        };

        /**
         * 停止录音时长计时器
         */
        const stopRecordingTimer = () => {
          if (recordingTimer.value) {
            clearInterval(recordingTimer.value);
            recordingTimer.value = null;
          }
          recordingDuration.value = 0;
        };

        /**
         * 切换暂停状态
         */
        const togglePause = () => {
          interviewState.value.isPaused = !interviewState.value.isPaused;
        };

        /**
         * 下一题
         */
        const nextQuestion = async () => {
          if (interviewState.value.currentQuestion >= interviewState.value.totalQuestions - 1) {
            return;
          }

          await stopRecording();

          // 显示题目切换动画
          isQuestionChanging.value = true;

          // 显示答题完成动画
          showCompletionAnimation.value = true;
          setTimeout(() => {
            showCompletionAnimation.value = false;
          }, 2000);

          setTimeout(async () => {
            interviewState.value.currentQuestion++;
            isQuestionChanging.value = false;
            userAnswer.value = '';

            await startRecording();

            console.log(`切换到第 ${interviewState.value.currentQuestion + 1} 题，录音已启动`);
          }, 500);
        };

        /**
         * 结束面试
         */
        const endInterview = async () => {
          await stopRecording();

          showInterviewCompletion.value = true;

          // 模拟生成报告进度
          let progress = 0;
          const progressTimer = setInterval(() => {
            progress += Math.random() * 10;
            completionProgress.value = Math.min(100, Math.floor(progress));

            if (completionProgress.value >= 100) {
              clearInterval(progressTimer);
              setTimeout(() => {
                alert('面试报告生成完成！');
                showInterviewCompletion.value = false;
                // 这里可以跳转到结果页面
              }, 1000);
            }
          }, 200);

          cleanup();
        };

        /**
         * 退出面试
         */
        const exitInterview = () => {
          if (confirm('确定要退出面试吗？当前进度将不会保存。')) {
            cleanup();
            window.history.back();
          }
        };

        /**
         * 清理资源
         */
        const cleanup = () => {
          clearAllTimers();

          if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
            mediaRecorder.value.stop();
          }

          if (videoStream.value) {
            videoStream.value.getTracks().forEach((track) => track.stop());
          }

          if (audioStream.value) {
            audioStream.value.getTracks().forEach((track) => track.stop());
          }

          recordedChunks.value = [];
          currentRecording.value.isRecording = false;
        };

        // 页面加载时初始化
        onMounted(() => {
          // 立即初始化设备，不延迟
          loadingState.value.isInitializing = true;
          loadingState.value.message = '正在初始化摄像头和麦克风...';

          // 立即开始初始化
          initMediaDevices()
            .then(() => {
              loadingState.value.message = '设备初始化完成';
              console.log('摄像头和麦克风初始化成功');
              setTimeout(() => {
                loadingState.value.isInitializing = false;
              }, 500);
            })
            .catch((error) => {
              console.error('设备初始化失败:', error);
              loadingState.value.message = '设备初始化失败: ' + error.message;
              // 显示错误信息更长时间，让用户看到
              setTimeout(() => {
                loadingState.value.isInitializing = false;
              }, 3000);
            });
        });

        // 页面卸载时清理资源
        onUnmounted(() => {
          cleanup();
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
          if (document.hidden) {
            // 页面隐藏时暂停面试
            if (interviewState.value.isStarted && !interviewState.value.isPaused) {
              togglePause();
            }
          }
        });

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
          // 可以在这里处理响应式布局调整
          console.log('窗口大小已变化');
        });

        return {
          // 状态
          sessionParams,
          cameraStatus,
          audioStatus,
          interviewState,
          currentQuestionData,
          userAnswer,
          loadingState,
          showUserVideo,
          emotionData,
          questionRecordings,
          currentRecording,
          recordingDuration,
          videoRef,
          isQuestionChanging,
          showCompletionAnimation,
          showInterviewCompletion,
          completionProgress,
          timeReminder,
          realTimeSuggestions,

          // 方法
          toggleUserVideo,
          getEmotionLabel,
          getEmotionColor,
          formatTime,
          formatRecordingDuration,
          getQuestionTypeLabel,
          initMediaDevices,
          switchCamera,
          startInterview,
          startRecording,
          stopRecording,
          togglePause,
          nextQuestion,
          endInterview,
          exitInterview,
          dismissTimeReminder,
          dismissSuggestion,
        };
      },
    }).mount('#app');
  </script>
</body>
</html>
