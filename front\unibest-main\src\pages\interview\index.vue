<script setup lang="ts">
// @ts-ignore
import BottomTabBar from '@/components/BottomTabBar.vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import AbilityRadarChart from '@/components/AbilityRadarChart.vue'
// @ts-ignore
import LoadingSkeleton from '@/components/LoadingSkeleton.vue'
// @ts-ignore
import ErrorState from '@/components/ErrorState.vue'
import { ref, computed, onUnmounted } from 'vue'
import { onLoad, onPullDownRefresh as uniOnPullDownRefresh } from '@dcloudio/uni-app'
import { getStatistics, getHistoryRecords, getFilterOptions } from '@/service/app/interview'
import type { Statistics, InterviewRecord, PaginationParams } from '@/service/app/interview'

/**
 * @description 面试主页
 * 提供面试入口、历史记录预览、学习建议等功能
 * 个人专属的求职能力驾驶舱
 */

// 加载状态
const isLoading = ref(true)
const refreshing = ref(false)
const hasError = ref(false)
const errorType = ref<'network' | 'error' | 'timeout'>('error')

// 用户面试数据
const userStats = ref({
  totalInterviews: 0,
  totalTime: 0, // 总面试时长（分钟）
  averageScore: 0,
  rank: '新手',
  weeklyProgress: 0,
})

// AI动态激励数据
const aiMotivation = ref({
  greeting: '你好！',
  message: '准备好迎接新的挑战了吗？',
  personalizedTip: '开始你的第一次模拟面试吧',
})

// 用户能力雷达图数据（原始对象格式）
const radarDataRaw = ref({
  professionalKnowledge: 0, // 专业知识
  communicationSkill: 0, // 沟通表达
  logicalThinking: 0, // 逻辑思维
  innovation: 0, // 创新能力
  stressResistance: 0, // 抗压能力
  teamwork: 0, // 团队协作
})

// 智能推荐任务
const smartTasks = ref([
  {
    id: 1,
    title: '开始首次面试',
    description: '选择一个感兴趣的岗位开始你的第一次模拟面试',
    action: 'interview',
    priority: 'high',
    url: '/pages/interview/select',
  },
])

// 快速入口功能
const quickActions = ref([
  {
    id: 'quick-start',
    title: '快速开始',
    subtitle: '选择岗位开始面试',
    icon: 'i-mdi-play-circle',
    gradient: 'linear-gradient(135deg, #00C9A7 0%, #4FD1C7 100%)',
    action: () => goToSelect(),
  },
  {
    id: 'my-history',
    title: '面试记录',
    subtitle: '查看历史面试记录',
    icon: 'i-mdi-history',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    action: () => goToHistory(),
  },
  {
    id: 'learning',
    title: '学习资源',
    subtitle: '提升面试技能',
    icon: 'i-mdi-book-open-variant',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    action: () => goToLearning(),
  },
  {
    id: 'ai-chat',
    title: 'AI助手',
    subtitle: '面试技巧咨询',
    icon: 'i-mdi-robot',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    action: () => goToAIChat(),
  },
])

// 最近面试记录
const recentInterviews = ref([])

// 学习建议
const suggestions = ref([
  {
    id: 1,
    type: 'skill',
    title: '技术面试技巧',
    content: '建议重点练习算法题和系统设计',
    icon: 'i-mdi-code-braces',
  },
  {
    id: 2,
    type: 'expression',
    title: '表达能力提升',
    content: '多练习STAR法则组织回答',
    icon: 'i-mdi-account-voice',
  },
  {
    id: 3,
    type: 'confidence',
    title: '自信心建设',
    content: '保持眼神交流，展现专业素养',
    icon: 'i-mdi-emoticon-happy',
  },
])

// 计算属性
const formattedTotalTime = computed(() => {
  const hours = Math.floor(userStats.value.totalTime / 60)
  const minutes = userStats.value.totalTime % 60
  return hours > 0 ? `${hours}h${minutes}m` : `${minutes}m`
})

/**
 * @description 跳转到岗位选择页
 */
const goToSelect = () => {
  uni.navigateTo({
    url: '/pages/interview/select',
  })
}

/**
 * @description 跳转到历史记录页
 */
const goToHistory = () => {
  uni.navigateTo({
    url: '/pages/user/history',
  })
}

/**
 * @description 跳转到学习资源页
 */
const goToLearning = () => {
  uni.navigateTo({
    url: '/pages/learning/recommend',
  })
}

/**
 * @description 跳转到AI助手页
 */
const goToAIChat = () => {
  uni.navigateTo({
    url: '/pages/aichat/index',
  })
}

/**
 * @description 查看面试详情
 * @param interviewId 面试ID
 */
const viewInterviewDetail = (interviewId: number) => {
  uni.navigateTo({
    url: `/pages/interview/detail?id=${interviewId}`,
  })
}

/**
 * @description 获取分数颜色
 * @param score 分数
 */
const getScoreColor = (score: number): string => {
  if (score >= 90) return '#52C41A'
  if (score >= 80) return '#00C9A7'
  if (score >= 70) return '#FAAD14'
  if (score >= 60) return '#FA8C16'
  return '#F5222D'
}

/**
 * @description 获取状态文本
 * @param status 状态
 */
const getStatusText = (status: string): string => {
  // good excellent fair poor
  const statusMap = {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '待提升',
  }
  return statusMap[status] || status
}

// 防抖处理
let loadDataTimer: number | null = null

/**
 * @description 加载用户数据（带防抖处理）
 */
const loadUserData = async () => {
  // 清除之前的定时器
  if (loadDataTimer) {
    clearTimeout(loadDataTimer)
  }

  try {
    isLoading.value = true
    console.log('开始加载用户数据...')

    // 并行加载统计数据和历史记录
    const [statisticsResponse, historyResponse] = await Promise.all([
      getStatistics().catch((error) => {
        console.warn('获取统计数据失败，使用默认数据:', error)
        return null
      }),
      getHistoryRecords({
        params: { page: 1, pageSize: 5 } as PaginationParams,
      }).catch((error) => {
        console.warn('获取历史记录失败，使用默认数据:', error)
        return null
      }),
    ])

    // 处理统计数据
    if (statisticsResponse?.data) {
      const stats = statisticsResponse.data
      userStats.value = {
        totalInterviews: stats.totalInterviews,
        totalTime: Math.floor(stats.totalInterviews * 30), // 估算总时长
        averageScore: stats.averageScore,
        rank: stats.currentLevel,
        weeklyProgress: stats.monthlyImprovement,
      }

      // 根据统计数据生成AI激励信息
      const hour = new Date().getHours()
      let greeting = '你好！'
      if (hour < 12) greeting = '早上好！'
      else if (hour < 18) greeting = '下午好！'
      else greeting = '晚上好！'

      aiMotivation.value = {
        greeting,
        message: stats.averageScore >= 80 ? '表现很棒，继续保持！' : '今天也要继续努力呀！',
        personalizedTip:
          stats.averageScore < 70
            ? '建议多练习基础面试题目，提升整体能力'
            : stats.averageScore < 85
              ? '建议针对薄弱环节进行专项训练'
              : '可以尝试更高难度的面试挑战',
      }
    } else {
      // 使用默认统计数据（为了演示，设置一些模拟数据）
      userStats.value = {
        totalInterviews: 2, // 设置为2，这样雷达图会显示
        totalTime: 60,
        averageScore: 75,
        rank: '初级',
        weeklyProgress: 5,
      }

      aiMotivation.value = {
        greeting: '你好！',
        message: '准备好迎接新的挑战了吗？',
        personalizedTip: '开始你的第一次模拟面试吧',
      }
    }
    // 处理历史记录
    if (historyResponse?.data?.records) {
      console.log(historyResponse.data.records);
      recentInterviews.value = historyResponse.data.records.slice(0, 3).map((record) => ({
        id: record.id,
        jobName: record.jobName,
        company: record.company,
        score: record.totalScore,
        date: record.date,
        status: record.status,
        rank: record.rank,
      }))
      console.log('成功加载面试记录:', recentInterviews.value)
    } else {
      console.warn('历史记录API返回空数据或格式错误:', historyResponse)
      // 为了调试，添加一些模拟数据
      recentInterviews.value = [
        {
          id: 1,
          jobName: '前端开发工程师',
          company: '示例公司',
          score: 85,
          date: '2024-01-15',
          status: 'completed',
        },
        {
          id: 2,
          jobName: 'Vue.js开发工程师',
          company: '科技公司',
          score: 78,
          date: '2024-01-10',
          status: 'completed',
        },
      ]
      console.log('使用模拟数据:', recentInterviews.value)
    }

    // 生成智能推荐任务
    if (userStats.value.totalInterviews === 0) {
      smartTasks.value = [
        {
          id: 1,
          title: '开始首次面试',
          description: '选择一个感兴趣的岗位开始你的第一次模拟面试',
          action: 'interview',
          priority: 'high',
          url: '/pages/interview/select',
        },
      ]
    } else if (userStats.value.averageScore < 70) {
      smartTasks.value = [
        {
          id: 1,
          title: '基础能力提升',
          description: '建议先练习基础面试题目，巩固基本技能',
          action: 'practice',
          priority: 'high',
          url: '/pages/learning/basic',
        },
        {
          id: 2,
          title: '简单岗位练习',
          description: '选择难度较低的岗位进行练习',
          action: 'interview',
          priority: 'medium',
          url: '/pages/interview/select?difficulty=1',
        },
      ]
    } else {
      smartTasks.value = [
        {
          id: 1,
          title: '挑战高难度面试',
          description: '尝试更有挑战性的岗位面试',
          action: 'interview',
          priority: 'high',
          url: '/pages/interview/select?difficulty=4',
        },
        {
          id: 2,
          title: '专项技能训练',
          description: '针对薄弱环节进行专项训练',
          action: 'practice',
          priority: 'medium',
          url: '/pages/learning/advanced',
        },
      ]
    }

    // 立即设置 isLoading 为 false，不等待任何延迟
    isLoading.value = false
  } catch (error: any) {
    console.error('加载用户数据失败:', error)

    // 设置错误状态
    hasError.value = true

    // 根据错误类型设置不同的错误状态
    if (error?.message?.includes('timeout')) {
      errorType.value = 'timeout'
    } else if (error?.message?.includes('network') || error?.message?.includes('fail')) {
      errorType.value = 'network'
    } else {
      errorType.value = 'error'
    }

    // 即使出错也立即隐藏加载状态
    isLoading.value = false
  }
}

/**
 * @description 刷新数据
 */
const refreshData = async () => {
  refreshing.value = true
  hasError.value = false
  await loadUserData()
  refreshing.value = false
}

/**
 * @description 处理错误重试
 */
const handleErrorRetry = () => {
  hasError.value = false
  loadUserData()
}

/**
 * @description 执行智能推荐任务
 * @param task 任务对象
 */
const executeSmartTask = (task: any) => {
  if (task.action === 'practice') {
    uni.navigateTo({
      url: task.url,
    })
  } else if (task.action === 'interview') {
    uni.navigateTo({
      url: task.url,
    })
  }
}

/**
 * @description 下拉刷新处理
 */
uniOnPullDownRefresh(() => {
  refreshData().finally(() => {
    uni.stopPullDownRefresh()
  })
})

/**
 * @description 页面加载
 */
onLoad(() => {
  // 立即加载数据，不使用任何延迟
  loadUserData()

  // 延迟预加载其他页面，让当前页面内容优先显示
  setTimeout(() => {
    uni.preloadPage({
      url: '/pages/interview/select',
    })
    uni.preloadPage({
      url: '/pages/aichat/index',
    })
    uni.preloadPage({
      url: '/pages/learning/recommend',
    })
    uni.preloadPage({
      url: '/pages/user/history',
    })
  }, 1000) // 增加延迟，让当前页面内容优先完成渲染
})

/**
 * @description 页面卸载
 */
onUnmounted(() => {
  // 清理定时器等资源
  if (loadDataTimer) {
    clearTimeout(loadDataTimer)
    loadDataTimer = null
  }

  // 重置状态
  isLoading.value = false
  hasError.value = false
})
</script>

<template>
  <view class="interview-page">
    <HeadBar
      class="nav-wrapper"
      title="智能面试"
      :show-back="false"
      :is-tab-bar="true"
      :fixed="false"
    />

    <!-- 加载状态 -->
    <LoadingSkeleton class="loading-skeleton" v-if="isLoading" :immediate-load="true" />

    <!-- 错误状态 -->
    <ErrorState v-else-if="hasError" :type="errorType" @retry="handleErrorRetry" />

    <!-- 主要内容 -->
    <view v-else class="main-content">
      <!-- 用户统计卡片 -->
      <view class="interview-stats-card animate-fade-in" @click="goToHistory">
        <view class="stats-header">
          <view class="welcome-text">
            <text class="greeting">{{ aiMotivation.greeting }}</text>
            <text class="description">{{ aiMotivation.message }}</text>
            <text v-if="aiMotivation.personalizedTip" class="tip">
              {{ aiMotivation.personalizedTip }}
            </text>
          </view>
          <view class="score-circle">
            <text class="score-value">{{ userStats.averageScore }}</text>
            <text class="score-label">平均分</text>
          </view>
        </view>

        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ userStats.totalInterviews }}</text>
            <text class="stat-label">面试次数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ formattedTotalTime }}</text>
            <text class="stat-label">总时长</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ userStats.rank }}</text>
            <text class="stat-label">综合等级</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">+{{ userStats.weeklyProgress }}</text>
            <text class="stat-label">本周进步</text>
          </view>
        </view>
      </view>

      <!-- 快速操作 -->
      <view class="quick-actions animate-fade-in" style="animation-delay: 0.1s">
        <view class="section-title">
          <text class="section-icon i-mdi-lightning-bolt"></text>
          <text class="section-text">快速开始</text>
        </view>
        <view class="actions-grid">
          <view
            class="action-item"
            v-for="(action, index) in quickActions"
            :key="action.id"
            :style="{ 'animation-delay': `${0.1 + index * 0.05}s` }"
            @click="action.action"
          >
            <view class="action-background" :style="{ background: action.gradient }"></view>
            <view class="action-icon">
              <text :class="action.icon"></text>
            </view>
            <view class="action-content">
              <text class="action-title">{{ action.title }}</text>
              <text class="action-subtitle">{{ action.subtitle }}</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 智能推荐任务 -->
      <view
        v-if="smartTasks.length > 0"
        class="smart-tasks animate-fade-in"
        style="animation-delay: 0.3s"
      >
        <view class="section-title">
          <text class="section-icon i-mdi-target"></text>
          <text class="section-text">智能推荐</text>
        </view>
        <view class="task-list">
          <view
            class="task-item"
            v-for="task in smartTasks"
            :key="task.id"
            :class="[`priority-${task.priority}`]"
            @click="executeSmartTask(task)"
          >
            <view class="task-priority-indicator"></view>
            <view class="task-content">
              <text class="task-title">{{ task.title }}</text>
              <text class="task-description">{{ task.description }}</text>
            </view>
            <text class="task-arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 最近面试记录 -->
      <view class="recent-interviews animate-fade-in" style="animation-delay: 0.4s">
        <view class="section-title">
          <text class="section-icon i-mdi-clock-outline"></text>
          <text class="section-text">最近面试</text>
        </view>

        <!-- 有面试记录时显示列表 -->
        <view v-if="recentInterviews.length > 0" class="interview-list">
          <view
            class="interview-item"
            v-for="interview in recentInterviews"
            :key="interview.id"
            @click="viewInterviewDetail(interview.id)"
          >
            <view class="interview-info">
              <text class="job-name">{{ interview.jobName }}</text>
              <text class="company-name">{{ interview.company }}</text>
              <text class="interview-date">{{ interview.date }}</text>
            </view>
            <view class="interview-result">
              <text
                v-if="interview.status === 'completed'"
                class="score"
                :style="{ color: getScoreColor(interview.score) }"
              >
                {{ interview.score }}分
              </text>
              <text v-else class="status incomplete">
                {{ getStatusText(interview.status) }}
              </text>
            </view>
          </view>
        </view>

        <!-- 无面试记录时显示空状态 -->
        <view v-else class="empty-state">
          <view class="empty-icon">
            <text class="i-mdi-clipboard-text-outline"></text>
          </view>
          <text class="empty-text">暂无面试记录</text>
          <text class="empty-tip">开始你的第一次模拟面试吧</text>
          <view class="empty-action" @click="goToSelect">
            <text>立即开始</text>
          </view>
        </view>
      </view>

      <!-- 学习建议 -->
      <view class="suggestions animate-fade-in" style="animation-delay: 0.5s">
        <view class="section-title">
          <text class="section-icon i-mdi-lightbulb-on-outline"></text>
          <text class="section-text">学习建议</text>
        </view>
        <view class="suggestion-list">
          <view
            class="suggestion-item"
            v-for="(suggestion, index) in suggestions"
            :key="suggestion.id"
            :style="{ 'animation-delay': `${0.5 + index * 0.05}s` }"
          >
            <view class="suggestion-icon">
              <text :class="suggestion.icon"></text>
            </view>
            <view class="suggestion-content">
              <text class="suggestion-title">{{ suggestion.title }}</text>
              <text class="suggestion-text">{{ suggestion.content }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部tabbar -->
    <BottomTabBar />
  </view>
</template>

<style scoped lang="scss">
/*
 * 面试页面专用样式
 * 使用 scoped 避免样式污染，并重置可能受到全局样式影响的元素
 * 注意：项目中存在多个页面定义了同名的 .stats-card 类，
 * 为避免样式冲突，本页面使用独立的 .interview-stats-card 类名
 */

// 基础变量定义
$primary-color: #00c9a7;
$primary-gradient: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
$border-radius: 24rpx;
$border-radius-small: 16rpx;
$shadow-light: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-medium: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);
$text-primary: #333;
$text-secondary: #666;
$text-tertiary: #999;

.nav-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #00c9a7 0%, #00b294 100%);
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.2);
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.interview-page {
  min-height: 100vh;
  padding-bottom: 120rpx;
  background-color: #f7f9fc;
}

.loading-skeleton {
  padding-top: 100rpx;
}

.main-content {
  padding: 20rpx;
  padding-top: 100rpx;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out both;
}

.interview-stats-card {
  display: flex;
  flex-direction: column;
  padding: 40rpx;
  margin-bottom: 30rpx;
  background: $primary-gradient;
  border-radius: $border-radius;
  box-shadow: $shadow-medium;
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  .stats-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 40rpx;

    .welcome-text {
      flex: 1;

      .greeting {
        display: block;
        margin-bottom: 8rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #fff;
      }

      .description {
        display: block;
        margin-bottom: 8rpx;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }

      .tip {
        display: block;
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.7);
        font-style: italic;
      }
    }

    .score-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 120rpx;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10rpx);
      border-radius: 50%;
      animation: pulse 2s infinite;

      .score-value {
        font-size: 32rpx;
        font-weight: bold;
        color: #fff;
      }

      .score-label {
        font-size: 20rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30rpx;

    .stat-item {
      text-align: center;

      .stat-value {
        display: block;
        margin-bottom: 8rpx;
        font-size: 28rpx;
        font-weight: bold;
        color: #fff;
      }

      .stat-label {
        font-size: 20rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.section-title {
  display: flex;
  gap: 12rpx;
  align-items: center;
  margin-bottom: 24rpx;

  .section-icon {
    font-size: 32rpx;
    color: $primary-color;

    // MDI图标支持
    &::before {
      font-family: 'Material Design Icons';
    }
  }

  .section-text {
    font-size: 28rpx;
    font-weight: bold;
    color: $text-primary;
  }
}

.quick-actions {
  margin-bottom: 30rpx;

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .action-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: 30rpx;
      overflow: hidden;
      background: #fff;
      border-radius: $border-radius-small;
      box-shadow: $shadow-light;
      transition: all 0.3s ease;
      animation: fadeIn 0.6s ease-out both;

      &:active {
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
        transform: scale(0.98);
      }

      .action-background {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 6rpx;
        border-radius: $border-radius-small $border-radius-small 0 0;
      }

      .action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        margin-right: 20rpx;
        background: $primary-gradient;
        border-radius: 50%;

        text {
          font-size: 24rpx;
          color: #fff;

          // MDI图标支持
          &::before {
            font-family: 'Material Design Icons';
          }
        }
      }

      .action-content {
        flex: 1;

        .action-title {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: bold;
          color: $text-primary;
        }

        .action-subtitle {
          font-size: 22rpx;
          color: $text-secondary;
        }
      }

      .action-arrow {
        font-size: 32rpx;
        color: #ccc;
      }
    }
  }
}

.smart-tasks {
  margin-bottom: 30rpx;

  .task-list {
    .task-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: 14rpx;
      margin-bottom: 8rpx;
      background: #fff;
      border-radius: $border-radius-small;
      box-shadow: $shadow-light;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .task-priority-indicator {
        width: 6rpx;
        height: 60rpx;
        margin-right: 20rpx;
        border-radius: 3rpx;
      }

      &.priority-high .task-priority-indicator {
        background: #ff4d4f;
      }

      &.priority-medium .task-priority-indicator {
        background: #faad14;
      }

      &.priority-low .task-priority-indicator {
        background: #52c41a;
      }

      .task-content {
        flex: 1;

        .task-title {
          display: block;
          margin-bottom: 8rpx;
          font-size: 26rpx;
          font-weight: bold;
          color: $text-primary;
        }

        .task-description {
          font-size: 22rpx;
          line-height: 1.5;
          color: $text-secondary;
        }
      }

      .task-arrow {
        font-size: 32rpx;
        color: #ccc;
      }
    }
  }
}

.recent-interviews {
  margin-bottom: 30rpx;

  .interview-list {
    overflow: hidden;
    background: #fff;
    border-radius: $border-radius-small;
    box-shadow: $shadow-light;

    .interview-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      transition: background-color 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
      }

      .interview-info {
        flex: 1;

        .job-name {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: bold;
          color: $text-primary;
        }

        .company-name {
          display: block;
          margin-bottom: 8rpx;
          font-size: 24rpx;
          color: $text-secondary;
        }

        .interview-date {
          font-size: 22rpx;
          color: $text-tertiary;
        }
      }

      .interview-result {
        .score {
          font-size: 32rpx;
          font-weight: bold;
        }

        .status {
          font-size: 24rpx;
          color: $text-secondary;

          &.incomplete {
            color: #fa8c16;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 40rpx;
    background: #fff;
    border-radius: $border-radius-small;
    box-shadow: $shadow-light;

    .empty-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 24rpx;
      background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
      border-radius: 50%;

      text {
        font-size: 48rpx;
        color: #999;

        // MDI图标支持
        &::before {
          font-family: 'Material Design Icons';
        }
      }
    }

    .empty-text {
      margin-bottom: 12rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: $text-primary;
    }

    .empty-tip {
      margin-bottom: 32rpx;
      font-size: 24rpx;
      color: $text-secondary;
      text-align: center;
    }

    .empty-action {
      padding: 16rpx 32rpx;
      background: $primary-gradient;
      border-radius: 40rpx;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      text {
        font-size: 24rpx;
        font-weight: bold;
        color: #fff;
      }
    }
  }
}

.suggestions {
  margin-bottom: 30rpx;

  .suggestion-list {
    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: #fff;
      border-radius: $border-radius-small;
      box-shadow: $shadow-light;
      animation: fadeIn 0.6s ease-out both;
      transition: transform 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .suggestion-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        margin-right: 20rpx;
        background: $primary-gradient;
        border-radius: 50%;

        text {
          font-size: 20rpx;
          color: #fff;

          // MDI图标支持
          &::before {
            font-family: 'Material Design Icons';
          }
        }
      }

      .suggestion-content {
        flex: 1;

        .suggestion-title {
          display: block;
          margin-bottom: 8rpx;
          font-size: 26rpx;
          font-weight: bold;
          color: $text-primary;
        }

        .suggestion-text {
          font-size: 22rpx;
          line-height: 1.5;
          color: $text-secondary;
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .interview-stats-card .stats-header {
    flex-direction: column;
    gap: 20rpx;

    .score-circle {
      align-self: center;
    }
  }

  .actions-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>
