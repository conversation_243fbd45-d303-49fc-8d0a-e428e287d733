/**
 * 面试首页接口服务
 * 提供面试首页和统计相关接口
 */

import { httpGet } from '@/utils/http'
import { IResData } from '@/types/interview-select'

// API 端点常量
const INTERVIEW_INDEX_API_ENDPOINTS = {
  GET_USER_STATISTICS: '/interview/user/statistics',
  GET_RECENT_INTERVIEWS: '/interview/user/recent',
  GET_RECOMMENDED_JOBS: '/interview/user/recommendations',
  GET_SKILL_ASSESSMENT: '/interview/user/skills',
  GET_LEARNING_PROGRESS: '/interview/user/learning-progress',
  GET_IMPROVEMENT_SUGGESTIONS: '/interview/user/improvement-suggestions',
  GET_MOTIVATION_QUOTE: '/interview/user/motivation-quote',
  GET_STREAK_DATA: '/interview/user/streak',
}

// 用户统计数据
export interface UserStatistics {
  totalInterviews: number
  totalTime: number
  averageScore: number
  bestScore: number
  improvementRate: number
  completedInterviews: number
  inProgressInterviews: number
  rank: string
  rankPercentile: number
  weeklyProgress: number
}

// 近期面试记录
export interface RecentInterview {
  id: string
  jobName: string
  company: string
  date: string
  duration: string
  score: number
  status: 'completed' | 'in-progress' | 'scheduled'
  mode: string
}

// 推荐岗位
export interface RecommendedJob {
  id: number
  name: string
  company: string
  logo: string
  matchScore: number
  difficulty: number
  reason: string
}

// 技能评估
export interface SkillAssessment {
  technicalSkills: Array<{
    name: string
    level: number
    maxLevel: number
    progress: number
  }>
  softSkills: Array<{
    name: string
    level: number
    maxLevel: number
    progress: number
  }>
  overallLevel: number
  strengths: string[]
  weaknesses: string[]
}

// 学习进度
export interface LearningProgress {
  completedResources: number
  totalResources: number
  completionRate: number
  timeSpent: number
  recentResources: Array<{
    id: string
    title: string
    type: string
    progress: number
    lastAccessed: string
  }>
}

// 提升建议
export interface ImprovementSuggestion {
  id: string
  area: string
  suggestion: string
  priority: 'high' | 'medium' | 'low'
  resourceUrl?: string
  resourceType?: string
  timeEstimate?: number
}

// 激励语录
export interface MotivationQuote {
  quote: string
  author: string
  category: string
}

// 连续签到数据
export interface StreakData {
  currentStreak: number
  longestStreak: number
  totalDays: number
  lastCheckIn: string
  streakDates: string[]
  rewards: Array<{
    streakCount: number
    reward: string
    claimed: boolean
  }>
}

/**
 * @description 获取用户统计数据
 * @returns 用户统计数据
 */
export async function getUserStatistics(): Promise<IResData<UserStatistics>> {
  return httpGet<UserStatistics>(INTERVIEW_INDEX_API_ENDPOINTS.GET_USER_STATISTICS)
}

/**
 * @description 获取近期面试记录
 * @param limit 数量限制（可选）
 * @returns 近期面试记录
 */
export async function getRecentInterviews(limit?: number): Promise<IResData<RecentInterview[]>> {
  return httpGet<RecentInterview[]>(INTERVIEW_INDEX_API_ENDPOINTS.GET_RECENT_INTERVIEWS, { limit })
}

/**
 * @description 获取推荐岗位
 * @param limit 数量限制（可选）
 * @returns 推荐岗位列表
 */
export async function getRecommendedJobs(limit?: number): Promise<IResData<RecommendedJob[]>> {
  return httpGet<RecommendedJob[]>(INTERVIEW_INDEX_API_ENDPOINTS.GET_RECOMMENDED_JOBS, { limit })
}

/**
 * @description 获取技能评估
 * @returns 技能评估数据
 */
export async function getSkillAssessment(): Promise<IResData<SkillAssessment>> {
  return httpGet<SkillAssessment>(INTERVIEW_INDEX_API_ENDPOINTS.GET_SKILL_ASSESSMENT)
}

/**
 * @description 获取学习进度
 * @returns 学习进度数据
 */
export async function getLearningProgress(): Promise<IResData<LearningProgress>> {
  return httpGet<LearningProgress>(INTERVIEW_INDEX_API_ENDPOINTS.GET_LEARNING_PROGRESS)
}

/**
 * @description 获取提升建议
 * @param count 数量限制（可选）
 * @returns 提升建议列表
 */
export async function getImprovementSuggestions(count?: number): Promise<IResData<ImprovementSuggestion[]>> {
  return httpGet<ImprovementSuggestion[]>(INTERVIEW_INDEX_API_ENDPOINTS.GET_IMPROVEMENT_SUGGESTIONS, { count })
}

/**
 * @description 获取激励语录
 * @param category 类别（可选）
 * @returns 激励语录
 */
export async function getMotivationQuote(category?: string): Promise<IResData<MotivationQuote>> {
  return httpGet<MotivationQuote>(INTERVIEW_INDEX_API_ENDPOINTS.GET_MOTIVATION_QUOTE, { category })
}

/**
 * @description 获取连续签到数据
 * @returns 连续签到数据
 */
export async function getStreakData(): Promise<IResData<StreakData>> {
  return httpGet<StreakData>(INTERVIEW_INDEX_API_ENDPOINTS.GET_STREAK_DATA)
} 