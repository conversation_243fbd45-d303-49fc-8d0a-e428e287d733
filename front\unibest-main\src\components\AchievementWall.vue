<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'

/**
 * <AUTHOR>
 * @description 成就徽章墙组件
 */
/**
 * @description 成就徽章墙组件
 * @prop badges 徽章数组
 * @prop isShowStats 是否显示统计信息
 */
interface Badge {
  id: string // 徽章ID
  icon: string // 图标
  color: string // 颜色
  title: string // 标题
  desc: string // 描述
  unlocked: boolean // 是否解锁
  unlockedAt?: string // 解锁时间
}

const props = defineProps<{
  badges: Badge[]
  isShowStats?: boolean
}>()

const emit = defineEmits<{
  'badge-click': [badge: Badge]
}>()

/**
 * @description 根据徽章数量计算网格列数
 */
const gridColumns = computed(() => {
  const badgeCount = props.badges.length

  if (badgeCount === 0) return 1
  if (badgeCount === 1) return 1
  if (badgeCount === 2) return 2
  if (badgeCount <= 4) return badgeCount <= 3 ? 3 : 4
  if (badgeCount <= 6) return 3
  if (badgeCount <= 8) return 4
  if (badgeCount <= 12) return 4

  // 超过12个徽章时，使用5列
  return 5
})

/**
 * @description 计算网格样式类名
 */
const gridClass = computed(() => {
  const badgeCount = props.badges.length
  return [
    'badges-grid',
    `grid-cols-${gridColumns.value}`,
    {
      'single-badge': badgeCount === 1,
      'few-badges': badgeCount > 1 && badgeCount <= 4,
      'many-badges': badgeCount > 4,
    },
  ]
})

/**
 * @description 计算解锁统计
 */
const unlockedCount = computed(() => {
  return props.badges.filter((badge) => badge.unlocked).length
})

/**
 * @description 计算完成度百分比
 */
const completionRate = computed(() => {
  if (props.badges.length === 0) return 0
  return Math.round((unlockedCount.value / props.badges.length) * 100)
})

/**
 * @description 处理徽章点击事件
 */
const onBadgeClick = (badge: Badge) => {
  emit('badge-click', badge)
}

/**
 * @description 获取图标类名
 */
const getIconClass = (icon: string): string => {
  const iconMap: Record<string, string> = {
    trophy: 'i-fa-solid-trophy',
    fire: 'i-fa-solid-fire',
    star: 'i-fa-solid-star',
    medal: 'i-fa-solid-medal',
    crown: 'i-fa-solid-crown',
    gem: 'i-fa-solid-gem',
    rocket: 'i-fa-solid-rocket',
    graduation: 'i-fa-solid-user-graduate',
    book: 'i-fa-solid-book',
    lightbulb: 'i-fa-solid-lightbulb',
    target: 'i-fa-solid-bullseye',
    certificate: 'i-fa-solid-certificate',
    clock: 'i-fa-solid-clock',
    heart: 'i-fa-solid-heart',
    shield: 'i-fa-solid-shield',
  }
  return iconMap[icon] || 'i-fa-solid-award'
}
/**
 * @description 获取网格样式
 */
const getGridStyle = () => {
  return {
    gridTemplateColumns: `repeat(${gridColumns.value}, 1fr)`,
    justifyItems: 'center',
  }
}

/**
 * @description 获取徽章样式类名
 */
const getBadgeClass = (badge: Badge) => {
  return {
    locked: !badge.unlocked,
    'highlight-badge': badge.unlocked,
  }
}
</script>

<template>
  <view class="achievement-wall">
    <!-- 头部信息 -->
    <view class="wall-header" v-if="isShowStats">
      <text class="wall-title">成就徽章墙</text>
      <text class="wall-subtitle">记录你的每一个进步时刻</text>
    </view>

    <!-- 徽章展示区域 -->
    <view v-if="badges.length > 0" class="badges-container">
      <view :class="gridClass" :style="getGridStyle()">
        <view
          class="badge-item"
          :class="getBadgeClass(badge)"
          v-for="badge in badges"
          :key="badge.id"
          @click="onBadgeClick(badge)"
        >
          <view class="badge-icon-wrapper">
            <view class="badge-icon" :class="badge.color">
              <text :class="getIconClass(badge.icon)" style="font-size: 32rpx; color: #fff"></text>
            </view>
            <!-- 未解锁遮罩 -->
            <view v-if="!badge.unlocked" class="lock-overlay">
              <text class="i-fa-solid-lock" style="font-size: 24rpx; color: #999"></text>
            </view>
          </view>

          <text class="badge-title">{{ badge.title }}</text>
          <text class="badge-desc">{{ badge.desc }}</text>

          <!-- 解锁时间 -->
          <text v-if="badge.unlocked && badge.unlockedAt" class="unlock-time">
            {{ badge.unlockedAt }}
          </text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <text class="i-fa-solid-trophy" style="font-size: 64rpx; color: #ccc"></text>
      <text class="empty-text">暂无成就徽章</text>
      <text class="empty-desc">努力学习，解锁你的第一个成就！</text>
    </view>

    <!-- 统计信息 -->
    <view class="achievement-stats" v-if="isShowStats && badges.length > 0">
      <view class="stat-item">
        <text class="stat-value">{{ unlockedCount }}</text>
        <text class="stat-label">已获得</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ badges.length }}</text>
        <text class="stat-label">总成就</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ completionRate }}%</text>
        <text class="stat-label">完成度</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.achievement-wall {
  overflow: hidden;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

// 头部
.wall-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.wall-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}

.wall-subtitle {
  font-size: 24rpx;
  color: #666;
}

// 徽章容器
.badges-container {
  padding: 24rpx;
  min-height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 徽章网格 - 基础样式
.badges-grid {
  display: grid;
  gap: 16rpx;
  width: 100%;
  max-width: 100%;
  justify-items: center;
  align-items: start;

  // 单个徽章时居中显示
  &.single-badge {
    justify-content: center;
    .badge-item {
      width: 120rpx;
    }
  }

  // 少量徽章时适当调整
  &.few-badges {
    max-width: 80%;
    margin: 0 auto;
  }

  // 多个徽章时正常显示
  &.many-badges {
    max-width: 100%;
  }
}

// 徽章项
.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 140rpx;
  padding: 16rpx 8rpx;
  text-align: center;
  border-radius: 20rpx;
  transition: all 0.3s;
  cursor: pointer;
  position: relative;
  width: 100%;
  max-width: 160rpx;

  &:active:not(.locked) {
    background: #f5faff;
    transform: scale(0.95);
  }

  &.locked {
    opacity: 0.6;
  }

  // 高亮已解锁徽章
  &.highlight-badge {
    &:hover {
      background: rgba(0, 201, 167, 0.05);
    }
  }
}

// 徽章图标容器
.badge-icon-wrapper {
  position: relative;
  margin-bottom: 12rpx;
}

.badge-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s;

  .badge-item:active:not(.locked) & {
    transform: scale(0.9);
  }
}

// 徽章颜色
.badge-gold {
  background: linear-gradient(135deg, #ffd700, #ffc300);
}

.badge-silver {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
}

.badge-bronze {
  background: linear-gradient(135deg, #cd7f32, #b87333);
}

.badge-blue {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.badge-green {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.badge-purple {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.badge-red {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.badge-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

// 锁定遮罩
.lock-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}

// 徽章文本
.badge-title {
  display: block;
  margin-bottom: 4rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #222;
  line-height: 1.2;
  word-break: break-all;
  text-align: center;
}

.badge-desc {
  font-size: 18rpx;
  line-height: 1.3;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.unlock-time {
  margin-top: 8rpx;
  font-size: 18rpx;
  color: #999;
}

// 空状态
.empty-state {
  padding: 80rpx 32rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

// 统计信息
.achievement-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: #f9f9f9;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  display: block;
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #00c9a7;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 1rpx;
  height: 48rpx;
  margin: 0 32rpx;
  background: #e0e0e0;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .badges-container {
    padding: 20rpx;
  }

  .badges-grid {
    gap: 12rpx;

    &.few-badges {
      max-width: 90%;
    }
  }

  .badge-item {
    min-height: 120rpx;
    padding: 12rpx 6rpx;
    max-width: 140rpx;
  }

  .badge-icon {
    width: 50rpx;
    height: 50rpx;
  }

  .badge-title {
    font-size: 20rpx;
  }

  .badge-desc {
    font-size: 16rpx;
  }

  .empty-state {
    padding: 60rpx 24rpx;
  }

  .empty-text {
    font-size: 24rpx;
  }

  .empty-desc {
    font-size: 20rpx;
  }
}

// 超小屏幕适配（小程序环境）
@media screen and (max-width: 600rpx) {
  .badges-grid {
    &.grid-cols-5 {
      grid-template-columns: repeat(4, 1fr) !important;
    }

    &.grid-cols-4 {
      grid-template-columns: repeat(3, 1fr) !important;
    }
  }

  .badge-item {
    max-width: 120rpx;
    min-height: 100rpx;
    padding: 8rpx 4rpx;
  }

  .badge-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .badge-title {
    font-size: 18rpx;
  }

  .badge-desc {
    font-size: 14rpx;
  }
}
</style>

<!-- 
使用示例：

<AchievementWall 
  :badges="badgeList" 
  :isShowStats="true"
  @badge-click="handleBadgeClick"
/>

数据格式示例：
const badgeList = [
  {
    id: '1',
    icon: 'trophy',
    color: 'badge-gold',
    title: '初学者',
    desc: '完成首次学习',
    unlocked: true,
    unlockedAt: '2024-01-01'
  },
  {
    id: '2', 
    icon: 'fire',
    color: 'badge-red',
    title: '连续学习',
    desc: '连续7天学习',
    unlocked: false
  },
  {
    id: '3',
    icon: 'star',
    color: 'badge-blue', 
    title: '学霸',
    desc: '完成100道题目',
    unlocked: true,
    unlockedAt: '2024-01-15'
  }
]

支持的图标：
- trophy: 奖杯
- fire: 火焰  
- star: 星星
- medal: 奖牌
- crown: 皇冠
- gem: 宝石
- rocket: 火箭
- graduation: 毕业帽
- book: 书本
- lightbulb: 灯泡
- target: 靶心
- certificate: 证书
- clock: 时钟
- heart: 心形
- shield: 盾牌

支持的颜色：
- badge-gold: 金色渐变
- badge-silver: 银色渐变  
- badge-bronze: 铜色渐变
- badge-blue: 蓝色渐变
- badge-green: 绿色渐变
- badge-purple: 紫色渐变
- badge-red: 红色渐变
- badge-orange: 橙色渐变

自适应布局规则：
- 1个徽章：居中显示，单列
- 2个徽章：2列显示
- 3个徽章：3列显示  
- 4个徽章：4列显示
- 5-6个徽章：3列显示
- 7-8个徽章：4列显示
- 9-12个徽章：4列显示
- 超过12个：5列显示（在小屏幕上会自动调整为3-4列）
-->
