{"name": "parse-bmfont-xml", "version": "1.1.6", "description": "parses XML BMFont files into a JavaScript object", "main": "lib/index.js", "browser": "lib/browser.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {"xml-parse-from-string": "^1.0.0", "xml2js": "^0.5.0"}, "devDependencies": {"brfs": "^1.4.0", "browserify": "^9.0.3", "faucet": "0.0.1", "tape": "^3.5.0", "testling": "^1.7.1", "xhr": "^2.0.1"}, "scripts": {"test-browser": "browserify test/test-browser.js -t brfs | testling | faucet", "test-node": "node test/test.js | faucet", "test": "npm run test-node && npm run test-browser"}, "keywords": ["xml", "parse", "convert", "bmfont", "bm", "bitmap", "font", "bitmaps", "angel", "angelcode", "code", "text", "gl", "sprite", "sprites", "stackgl"], "repository": {"type": "git", "url": "git://github.com/mattdesl/parse-bmfont-xml.git"}, "homepage": "https://github.com/mattdesl/parse-bmfont-xml", "bugs": {"url": "https://github.com/mattdesl/parse-bmfont-xml/issues"}}