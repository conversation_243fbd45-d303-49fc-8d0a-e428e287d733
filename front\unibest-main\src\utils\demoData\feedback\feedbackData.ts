/**
 * @description 用户反馈演示数据
 * 提供用户反馈列表和详情的演示数据
 */

// 反馈状态类型
export type FeedbackStatus = 'PENDING' | 'PROCESSING' | 'RESOLVED' | 'CLOSED'

// 反馈详情
export interface FeedbackDetail {
  id: string
  type: string
  status: FeedbackStatus
  content: string
  createTime: number
  updateTime: number
  contactInfo?: string
  reply?: string
  handler?: string
  deviceInfo?: string
}

// 反馈统计数据
export interface FeedbackStats {
  totalCount: number
  pendingCount: number
  processingCount: number
  resolvedCount: number
  typeStats: Record<string, number>
}

// 反馈列表查询参数
export interface FeedbackListParams {
  pageNum: number
  pageSize: number
  type?: string
  status?: FeedbackStatus
}

// 反馈列表响应
export interface FeedbackListResponse {
  rows: FeedbackDetail[]
  total: number
}

// 创建演示反馈详情
export const createDemoFeedbackDetail = (
  id: string,
  type: string,
  status: FeedbackStatus,
): FeedbackDetail => {
  const now = Date.now()
  const createTime = now - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000) // 随机1-30天前
  const updateTime = createTime + Math.floor(Math.random() * (now - createTime))

  const feedback: FeedbackDetail = {
    id,
    type,
    status,
    content: getFeedbackContent(type),
    createTime,
    updateTime,
  }

  // 根据状态添加回复和处理人
  if (status === 'PROCESSING' || status === 'RESOLVED' || status === 'CLOSED') {
    feedback.reply = getReplyContent(status)
    feedback.handler = '客服小王'
  }

  // 随机添加联系方式
  if (Math.random() > 0.5) {
    feedback.contactInfo = '13800138000'
  }

  // 添加设备信息
  feedback.deviceInfo = 'iPhone 13, iOS 15.4, App v1.2.3'

  return feedback
}

// 获取反馈内容
const getFeedbackContent = (type: string): string => {
  const contents: Record<string, string[]> = {
    功能建议: [
      '希望能增加暗黑模式，晚上使用更护眼。',
      '建议增加批量操作功能，提高效率。',
      '希望能增加数据导出功能，方便备份和分析。',
      '建议增加自定义主题功能，让界面更个性化。',
    ],
    内容问题: [
      '首页的轮播图显示不完整，有时会出现空白。',
      '部分文章内容有错别字，影响阅读体验。',
      '视频播放页面的字幕有时会与画面不同步。',
      '某些产品描述与实际功能不符，希望更新。',
    ],
    使用问题: [
      '登录时经常提示网络错误，但我的网络是正常的。',
      '使用搜索功能时，结果加载很慢，有时会卡住。',
      '在安卓手机上使用时，某些按钮点击没有反应。',
      '应用经常在后台运行一段时间后自动关闭。',
    ],
    其他: [
      '希望能提供更多的客服支持渠道，如在线客服。',
      '建议增加用户交流社区，方便分享使用心得。',
      '希望能提供更详细的使用教程，特别是对新功能的介绍。',
      '对于付费功能，希望能提供更多的支付方式。',
    ],
  }

  const typeContents = contents[type] || contents['其他']
  return typeContents[Math.floor(Math.random() * typeContents.length)]
}

// 获取回复内容
const getReplyContent = (status: FeedbackStatus): string => {
  const replies: Record<string, string[]> = {
    PROCESSING: [
      '您的反馈我们已经收到，正在处理中，请耐心等待。',
      '感谢您的反馈，我们已经将问题转交给相关团队处理，会尽快给您回复。',
      '您提出的问题我们正在积极解决，可能需要一些时间，请您稍安勿躁。',
    ],
    RESOLVED: [
      '您反馈的问题已经解决，最新版本中已经修复，请更新后体验。',
      '感谢您的反馈，我们已经根据您的建议进行了优化，欢迎体验新版本。',
      '问题已经解决，如果您还有其他问题，欢迎随时反馈。',
    ],
    CLOSED: [
      '由于技术限制，暂时无法实现您的建议，感谢您的理解。',
      '您反馈的问题我们已经记录，会在后续版本中考虑优化。',
      '该问题已经超出了我们的服务范围，建议您联系相关部门处理。',
    ],
  }

  const statusReplies = replies[status] || []
  return statusReplies.length > 0
    ? statusReplies[Math.floor(Math.random() * statusReplies.length)]
    : '感谢您的反馈，我们会尽快处理。'
}

// 创建演示反馈列表
export const createDemoFeedbackList = (params: FeedbackListParams): FeedbackListResponse => {
  const { pageNum, pageSize, type, status } = params

  // 创建所有可能的反馈类型
  const types = ['功能建议', '内容问题', '使用问题', '其他']
  const statuses: FeedbackStatus[] = ['PENDING', 'PROCESSING', 'RESOLVED', 'CLOSED']

  // 创建演示数据
  const allFeedbacks: FeedbackDetail[] = []

  // 生成100条演示数据
  for (let i = 1; i <= 100; i++) {
    const feedbackType = types[Math.floor(Math.random() * types.length)]
    const feedbackStatus = statuses[Math.floor(Math.random() * statuses.length)]

    allFeedbacks.push(createDemoFeedbackDetail(`demo-feedback-${i}`, feedbackType, feedbackStatus))
  }

  // 根据查询参数筛选
  let filteredFeedbacks = [...allFeedbacks]

  if (type) {
    filteredFeedbacks = filteredFeedbacks.filter((feedback) => feedback.type === type)
  }

  if (status) {
    filteredFeedbacks = filteredFeedbacks.filter((feedback) => feedback.status === status)
  }

  // 按创建时间降序排序
  filteredFeedbacks.sort((a, b) => b.createTime - a.createTime)

  // 分页
  const start = (pageNum - 1) * pageSize
  const end = start + pageSize
  const pagedFeedbacks = filteredFeedbacks.slice(start, end)

  return {
    rows: pagedFeedbacks,
    total: filteredFeedbacks.length,
  }
}

// 创建演示反馈统计数据
export const createDemoFeedbackStats = (): FeedbackStats => {
  // 随机生成各种状态的数量
  const pendingCount = Math.floor(Math.random() * 10) + 5
  const processingCount = Math.floor(Math.random() * 15) + 10
  const resolvedCount = Math.floor(Math.random() * 30) + 20
  const totalCount = pendingCount + processingCount + resolvedCount

  // 随机生成各种类型的数量
  const typeStats: Record<string, number> = {
    功能建议: Math.floor(Math.random() * 20) + 10,
    内容问题: Math.floor(Math.random() * 15) + 5,
    使用问题: Math.floor(Math.random() * 25) + 15,
    其他: Math.floor(Math.random() * 10) + 5,
  }

  return {
    totalCount,
    pendingCount,
    processingCount,
    resolvedCount,
    typeStats,
  }
}

// 导出演示数据
export default {
  createDemoFeedbackDetail,
  createDemoFeedbackList,
  createDemoFeedbackStats,
}
