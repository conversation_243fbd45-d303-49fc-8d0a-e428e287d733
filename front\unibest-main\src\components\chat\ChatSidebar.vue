<template>
  <view class="sidebar" :class="{ active: show }" @click.self="$emit('close')">
    <view class="sidebar-content" @click.stop>
      <view class="sidebar-header">
        <view class="i-carbon-bot text-20px mr-8px"></view>
        <text class="sidebar-title">AI助手</text>
        <view class="flex-1"></view>
        <view class="i-carbon-close text-20px" @click="$emit('close')"></view>
      </view>

      <view class="agent-list">
        <view v-for="agent in agents" :key="agent.id" class="agent-item"
          :class="{ active: agent.id === currentAgentId }" @click="$emit('switch-agent', agent.id)">
          <view class="agent-icon" :style="{ backgroundColor: agent.color }">
            <view :class="agent.icon" class="text-16px text-white"></view>
          </view>
          <view class="agent-info">
            <text class="agent-name">{{ agent.name }}</text>
            <text class="agent-desc">{{ agent.description }}</text>
          </view>
        </view>
      </view>

      <view class="sidebar-actions">
        <view class="action-btn" @click="$emit('new-chat')">
          <view class="i-carbon-add text-16px mr-8px"></view>
          <text>新建对话</text>
        </view>
        <view class="action-btn" @click="$emit('show-history')">
          <view class="i-carbon-time text-16px mr-8px"></view>
          <text>历史记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Agent {
  id: string
  name: string
  description: string
  icon: string
  color: string
}

defineProps<{
  show: boolean
  agents: Agent[]
  currentAgentId: string
}>()

defineEmits<{
  close: []
  'switch-agent': [agentId: string]
  'new-chat': []
  'show-history': []
}>()
</script>

<style lang="scss" scoped>
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.3s;

  &.active {
    pointer-events: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  width: 85%;
  max-width: 640rpx;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 8rpx 0 48rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-100%);

  .sidebar:global(.active) & {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    padding: 32rpx;
    color: #fff;
    background: linear-gradient(135deg, #018d71 0%, #00c49a 100%);

    .sidebar-title {
      font-size: 40rpx;
      font-weight: 600;
    }
  }

  .agent-list {
    flex: 1;
    padding: 16rpx;
    overflow-x: hidden;
    overflow-y: auto;

    .agent-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background-color: #f8f8f8;
      border-radius: 24rpx;
      transition: all 0.3s;

      &.active {
        background: linear-gradient(135deg, #e8f8f5 0%, #d4f4ed 100%);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }

      &:active {
        transform: scale(0.98);
      }

      .agent-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72rpx;
        height: 72rpx;
        margin-right: 24rpx;
        border-radius: 20rpx;
      }

      .agent-info {
        flex: 1;
        overflow: hidden;

        .agent-name {
          display: block;
          overflow: hidden;
          font-size: 30rpx;
          font-weight: 500;
          color: #333;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .agent-desc {
          display: block;
          margin-top: 8rpx;
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  .sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    padding: 32rpx;

    .action-btn {
      display: flex;
      align-items: center;
      padding: 24rpx;
      font-size: 28rpx;
      color: #333;
      background-color: #f8f8f8;
      border-radius: 16rpx;
      transition: all 0.3s;

      &:active {
        background-color: #e8f8f8;
        transform: scale(0.98);
      }
    }
  }
}
</style>
