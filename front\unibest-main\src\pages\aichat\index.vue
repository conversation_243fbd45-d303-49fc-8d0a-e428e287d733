<template>
  <view class="ai-chat-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="handleBack">
        <view class="i-carbon-arrow-left text-24px"></view>
      </view>
      <view class="nav-center">
        <view class="ai-selector" @click="handleAgentSelectorClick">
          <view :class="currentAgent.icon" class="text-16px mr-4px" :style="{ color: currentAgent.color }"></view>
          <text>{{ currentAgent.name }}</text>
          <view class="i-carbon-chevron-down text-12px ml-4px"></view>
        </view>
      </view>
      <view class="nav-right" @click="handleMore">
        <view class="i-carbon-overflow-menu-horizontal text-24px"></view>
      </view>
    </view>

    <!-- 侧边栏 - 使用组件 -->
    <ChatSidebar
      :show="showSidebar"
      :agents="agents"
      :current-agent-id="chatStore.currentAgentId"
      @close="closeSidebar"
      @switch-agent="switchAgent"
      @new-chat="createNewChat"
      @show-history="showHistoryDialog"
    />

    <!-- 历史记录弹窗 - 使用组件 -->
    <ChatHistory
      v-if="showHistory"
      :show="showHistory"
      :sessions="chatStore.sessionList || []"
      :loading="historyLoading"
      :current-session-id="currentSessionId"
      @close="closeHistory"
      @clear-all="clearAllHistory"
      @load-session="loadHistorySession"
      @delete-session="deleteHistorySession"
    />

    <!-- 主内容区容器 -->
    <view class="content-container">
      <!-- 主内容区 -->
      <view class="main-content" v-if="!currentSession || currentSession.messages.length === 0">
        <!-- 欢迎动画 - 延迟加载 -->
        <view v-if="showWelcome" class="welcome-section">
          <view class="ai-avatar-large" :style="{ backgroundColor: currentAgent.color }">
            <view :class="currentAgent.icon" class="text-48px text-white"></view>
          </view>
          <view class="welcome-title">
            <text class="gradient-text">{{ currentAgent.name }}</text>
          </view>
          <view class="welcome-desc">
            <text>{{ currentAgent.description }}</text>
          </view>
        </view>

        <!-- 加载占位符 -->
        <view v-else class="welcome-loading">
          <view class="loading-placeholder"></view>
        </view>
      </view>

      <!-- 聊天内容区 -->
      <scroll-view v-else class="chat-content" scroll-y :scroll-top="scrollTop" :scroll-into-view="scrollIntoView"
        @scroll="handleScroll" :enhanced="true">
        <view class="message-list">
          <!-- 使用组件化的消息列表 -->
          <ChatMessage
            v-for="(message, index) in visibleMessages"
            :key="message.id"
            :id="`msg-${index}`"
            :message="message"
            :agent-color="currentAgent.color"
            :agent-icon="currentAgent.icon"
            @toggle-think="toggleThink"
            @retry="retryMessage"
            @preview-attachment="previewAttachment"
          />

          <!-- 底部空隙区域 -->
          <view class="message-spacer" :style="{ height: inputAreaBottomSafeDistance + 120 + 'rpx' }"></view>

          <!-- 加载中提示 -->
          <view v-if="showLoadingIndicator" class="message-item message-assistant">
            <view class="message-avatar">
              <view class="ai-avatar-small" :style="{ backgroundColor: currentAgent.color }">
                <view :class="currentAgent.icon" class="text-20px"></view>
              </view>
            </view>
            <view class="message-content">
              <view class="typing-indicator">
                <view class="typing-dot"></view>
                <view class="typing-dot"></view>
                <view class="typing-dot"></view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 扩展功能面板 -->
      <view class="extensions-panel" :class="{ active: showExtensions }" @click.self="closeExtensions">
        <view class="panel-content" @click.stop>
          <view class="panel-header">
            <text class="panel-title">扩展功能</text>
            <view class="i-carbon-close text-20px" @click="closeExtensions"></view>
          </view>
          <view class="extensions-grid">
            <view v-for="ext in extensions" :key="ext.id" class="extension-item"
              :class="{ active: activeExtensions.includes(ext.id) }" @click="toggleExtension(ext.id)">
              <view :class="ext.icon" class="ext-icon"></view>
              <text class="ext-name">{{ ext.name }}</text>
              <text class="ext-desc">{{ ext.description }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 底部输入区 -->
    <view class="input-area">
      <!-- 附件预览 -->
      <scroll-view v-if="attachments.length > 0" class="attachment-preview" scroll-x :show-scrollbar="false">
        <view class="attachment-list">
          <view v-for="(att, index) in attachments" :key="index" class="preview-item">
            <image v-if="att.type === 'image'" :src="att.url" mode="aspectFill" class="preview-image" />
            <view v-else class="preview-file">
              <view class="i-carbon-document text-24px"></view>
              <text class="file-name">{{ att.name }}</text>
            </view>
            <view class="remove-btn" @click="removeAttachment(index)">
              <view class="i-carbon-close-filled text-12px"></view>
            </view>
          </view>
        </view>
      </scroll-view>

      <view class="input-container">
        <!-- 左侧功能按钮 -->
        <view class="left-actions">
          <view class="action-btn" @click="handleAttachment">
            <view class="i-carbon-attachment text-22px"></view>
          </view>
          <view class="action-btn" @click="showExtensions = true">
            <view class="i-carbon-apps text-22px"></view>
          </view>
        </view>

        <!-- 文字输入框 -->
        <textarea class="input-field" placeholder="输入你的问题..." v-model="inputText" :disabled="isLoading"
          @confirm="handleSend" auto-height :maxlength="1000" />

        <!-- 右侧发送按钮 -->
        <view class="right-actions">
          <!-- 加载时显示取消按钮 -->
          <view v-if="isLoading" class="action-btn cancel" @click="handleCancelRequest">
            <view class="i-carbon-close text-20px"></view>
          </view>
          <!-- 有输入内容时显示发送按钮 -->
          <view v-else-if="canSend" class="action-btn active" @click="handleSend">
            <view class="i-carbon-send-filled text-20px"></view>
          </view>
          <!-- 默认状态显示麦克风按钮（暂时禁用）
            <view v-else class="action-btn disabled">
              <view class="i-carbon-microphone text-22px"></view>
            </view> -->
          <!-- 默认发送按钮 禁用 -->
          <view v-else class="action-btn active" @click="handleSend">
            <view class="i-carbon-send-filled text-20px"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// @ts-ignore
import { ref, computed, nextTick, watch } from 'vue'
import { useChatStore, AI_AGENTS } from '@/store/chat'
import { chatApi } from '@/service/chat-api'
import { markdownToHtml } from '@/utils/markdown'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { performanceMonitor } from '@/utils/performance'

// 导入组件
import ChatSidebar from '@/components/chat/ChatSidebar.vue'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import ChatHistory from '@/components/chat/ChatHistory.vue'

// 系统信息状态
const systemInfo = ref<any>({})
const statusBarHeight = ref(0)
const safeAreaInsets = ref({ top: 0, bottom: 0 })
const navBarHeight = ref(76) // 默认导航栏高度

// 使用聊天状态管理
const chatStore = useChatStore()
const currentSession = computed(() => chatStore.currentSession)
const isLoading = computed(() => chatStore.isLoading)
const currentSessionId = computed(() => chatStore.currentSessionId)
const currentAgent = computed(() => {
  // 优先使用store中的currentAgent，保证数据一致性
  const storeAgent = chatStore.currentAgent

  if (storeAgent) {
    return storeAgent
  }

  // 备用：从本地agents列表中查找匹配当前agentId的智能体
  const currentId = chatStore.currentAgentId
  const matchedAgent = agents.value.find((agent: any) => agent.id === currentId)

  if (matchedAgent) {
    return matchedAgent
  }

  // 兜底方案：使用agents列表中的第一个，如果没有则使用本地配置的第一个
  const fallbackAgent = agents.value.length > 0 ? agents.value[0] : AI_AGENTS[0]
  return fallbackAgent
})

// 智能代理列表：优先使用后端数据，备用本地配置
// 优化：移除computed中的async逻辑，采用响应式数据+初始化加载
const agents = ref(AI_AGENTS) // 初始化时使用本地配置，避免空白显示

const formatAgent = (agent: any) => {
  const formatted = {
    id: agent.type || agent.id, // 保持id一致性非常重要
    name: agent.name,
    description: agent.description,
    icon: agent.icon || 'i-carbon-bot',
    color: agent.color || '#00c9a7',
  }
  return formatted
}

const loadAgents = async () => {
  // 优先使用store中的availableAgents
  const backendAgents = chatStore.availableAgents

  if (backendAgents && backendAgents.length > 0) {
    agents.value = backendAgents.map(formatAgent)
    return
  }

  // 如果store没有，主动请求后端
  try {
    const remoteAgents = await chatApi.getEnabledAgents()

    if (remoteAgents && remoteAgents.length > 0) {
      agents.value = remoteAgents.map(formatAgent)
    } else {
      agents.value = AI_AGENTS
    }
  } catch (e) {
    // 请求失败时使用本地配置作为备用
    agents.value = AI_AGENTS
  }
}

// 监听availableAgents变化，更新本地agents列表
watch(
  () => chatStore.availableAgents,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      agents.value = newVal.map(formatAgent)
    } else {
      // 如果store中的数据为空，也要有备用方案
      agents.value = AI_AGENTS
    }
  },
  { immediate: true },
)

// 监听currentAgentId变化，确保UI及时更新
watch(
  () => chatStore.currentAgentId,
  (newAgentId, oldAgentId) => {
    // 强制触发UI更新
    nextTick(() => {
      // 强制重新计算currentAgent
      const agent = currentAgent.value
    })
  },
  { immediate: true },
)

// 监听store中currentAgent变化，确保UI同步
watch(
  () => chatStore.currentAgent,
  (newAgent, oldAgent) => { },
  { immediate: true, deep: true },
)

// 扩展功能数据 - 静态数据
const extensions = [
  {
    id: 'pdf-parser',
    name: 'PDF解析',
    description: '智能解析PDF文档',
    icon: 'i-carbon-document-pdf',
  },
  {
    id: 'web-search',
    name: '网络搜索',
    description: '实时获取最新信息',
    icon: 'i-carbon-search',
  },
  {
    id: 'translate',
    name: '智能翻译',
    description: '多语言翻译服务',
    icon: 'i-carbon-translate',
  },
]

// 页面状态 - 使用性能优化的响应式数据
const inputText = ref('')
const showSidebar = ref(false)
const showAgentSelector = ref(false)
const showExtensions = ref(false)
const showHistory = ref(false)
const scrollTop = ref(0)
const scrollIntoView = ref('')
const attachments = ref<Array<{ type: 'image' | 'file'; url: string; name: string }>>([])
// const isRecording = ref(false)
const activeExtensions = ref<string[]>([]) // 移除默认激活的语音输入扩展
// const recordStartTime = ref(0)
// const recordTimer = ref<any>(null)

// 历史记录搜索相关状态
const searchKeyword = ref('')
const showSearchInput = ref(false)
const historyLoading = ref(false)

// 网络状态
const isOnline = ref(true)

// 懒加载状态
const showWelcome = ref(false)
const showQuickActions = ref(false)

// 计算属性
const canSend = computed(() => {
  return (inputText.value.trim() || attachments.value.length > 0) && !isLoading.value
})

// 加载指示器显示逻辑 - 只在没有流式消息时显示
const showLoadingIndicator = computed(() => {
  // 如果不在加载状态，不显示指示器
  if (!isLoading.value) return false

  // 检查是否有正在流式输出的消息
  const session = currentSession.value
  if (!session || !session.messages.length) return true

  // 查找最后一条AI消息是否正在流式输出或处于错误状态
  const lastMessage = session.messages[session.messages.length - 1]
  const hasStreamingMessage =
    lastMessage &&
    lastMessage.role === 'assistant' &&
    (lastMessage.isStreaming || lastMessage.isError)

  // 如果有流式消息正在输出或已出错，则不显示加载指示器
  return !hasStreamingMessage
})

// 虚拟滚动配置
const MESSAGE_HEIGHT = 120 // 每条消息的估计高度（rpx）
const VISIBLE_COUNT = 30 // 增加可见区域显示的消息数量，提升用户体验
const BUFFER_COUNT = 10 // 增加缓冲区消息数量，减少滚动时的白屏

/**
 * @description 获取消息内容，避免直接绑定响应式数据到rich-text组件
 * @param message 消息对象
 * @returns 处理后的内容
 */
const getMessageContent = (message: any): string => {
  if (!message) return ''

  // 用户消息直接返回原始内容
  if (message.role === 'user') {
    return message.content || ''
  }

  // 处理助手消息，使用缓存
  const content = message.content || ''

  // 如果内容为空，返回空字符串
  if (!content) return ''

  // 如果有缓存的渲染结果，直接使用
  if (message.renderedContent) {
    return message.renderedContent
  }

  // 否则处理并缓存结果（不直接修改原消息对象）
  if (content && message.role === 'assistant') {
    try {
      // 使用markdownCache避免重复渲染
      if (!markdownCache.has(content)) {
        const rendered = markdownToHtml(content)
        markdownCache.set(content, rendered)
      }

      // 返回缓存的结果
      return markdownCache.get(content) || content
    } catch (error) {
      console.error('Markdown渲染失败:', error)
      return content
    }
  }

  return content
}

/**
 * @description 根据消息状态返回对应的类名
 * @param message 消息对象
 * @returns 类名
 */
const getClassStreamingOrError = (message: any) => {
  if (message.isStreaming) {
    return 'streaming'
  }
  if (message.isError) {
    return 'error'
  }
  return ''
}

/**
 * @description 计算可见消息列表（虚拟滚动优化 + markdown渲染缓存）
 */
const visibleMessages = computed(() => {
  const session = currentSession.value
  if (!session || !session.messages.length) return []

  const messages = session.messages
  const totalMessages = messages.length

  // 如果消息数量较少，直接返回全部
  if (totalMessages <= VISIBLE_COUNT + BUFFER_COUNT) {
    return messages
  }

  // 计算当前滚动位置对应的消息索引
  const scrollPosition = scrollTop.value || 0
  const estimatedIndex = Math.floor(scrollPosition / MESSAGE_HEIGHT)

  // 计算显示范围，确保索引不越界
  const startIndex = Math.max(0, estimatedIndex - BUFFER_COUNT)
  const endIndex = Math.min(totalMessages, estimatedIndex + VISIBLE_COUNT + BUFFER_COUNT)

  // 返回切片后的消息列表
  return messages.slice(startIndex, endIndex)
})

// 强制文字输入模式标记，默认为true表示默认使用文字输入
// const forceTextMode = ref(true)

// 判断是否显示语音按钮（需要主动切换到语音模式才显示）
// const showVoiceButton = computed(() => {
//   // 如果强制文字模式，则不显示语音按钮
//   if (forceTextMode.value) return false
//   // 只有在非强制文字模式下且没有文字输入时才显示语音按钮
//   return !inputText.value.trim() && attachments.value.length === 0
// })

// 语音按钮文字
// const voiceButtonText = computed(() => {
//   if (isRecording.value) {
//     return '松开发送'
//   }
//   return '按住说话'
// })

// 按日期分组的历史会话
const groupedSessions = computed(() => {
  const sessions = chatStore.sessionList || []
  const groups: Record<string, any[]> = {}

  sessions.forEach((session) => {
    const timestamp = getSessionTimestamp(session)
    const date = formatDate(timestamp)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(session)
  })

  // 按日期排序，最新的在前
  const sortedGroups: Record<string, any[]> = {}
  Object.keys(groups)
    .sort((a, b) => {
      const dateA = getDateValue(a)
      const dateB = getDateValue(b)
      return dateB - dateA
    })
    .forEach((date) => {
      // 每组内按更新时间排序，最新的在前
      sortedGroups[date] = groups[date].sort((a, b) => {
        const timeA = getSessionTimestamp(a)
        const timeB = getSessionTimestamp(b)
        return timeB - timeA
      })
    })

  return sortedGroups
})

// 过滤后的分组会话（用于搜索）
const filteredGroupedSessions = computed(() => {
  if (!searchKeyword.value.trim()) {
    return groupedSessions.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  const filteredGroups: Record<string, any[]> = {}

  Object.entries(groupedSessions.value).forEach(([date, sessions]) => {
    const filteredSessions = sessions.filter((session) => {
      // 搜索会话标题
      if (session.title.toLowerCase().includes(keyword)) {
        return true
      }

      // 搜索会话预览内容
      const preview = getSessionPreview(session).toLowerCase()
      if (preview.includes(keyword)) {
        return true
      }

      // 搜索消息内容
      return session.messages.some((message: any) =>
        message.content.toLowerCase().includes(keyword),
      )
    })

    if (filteredSessions.length > 0) {
      filteredGroups[date] = filteredSessions
    }
  })

  return filteredGroups
})

// 历史会话数量
const historySessionCount = computed(() => {
  if (searchKeyword.value.trim()) {
    return Object.values(filteredGroupedSessions.value).reduce(
      (total, sessions) => total + sessions.length,
      0,
    )
  }
  return chatStore.sessionList?.length || 0
})

// 计算输入区域底部安全距离
const inputAreaBottomSafeDistance = computed(() => {
  // 确保最小安全距离为 60rpx，增加底部缓冲空间
  // 在有底部安全区域的设备上，额外增加 30rpx 的视觉缓冲
  const minSafeDistance = 60
  const deviceSafeArea = safeAreaInsets.value.bottom || 0
  const additionalBuffer = deviceSafeArea > 0 ? 30 : 20

  return Math.max(deviceSafeArea + additionalBuffer, minSafeDistance)
})

/**
 * @description 缓存markdown渲染结果，避免无限递归更新
 */
const markdownCache = new Map<string, string>()

/**
 * @description 防抖渲染队列，优化流式消息性能
 */
let renderDebounceTimer: number | null = null
const renderQueue = new Set<string>()

/**
 * @description 处理消息内容渲染，使用缓存优化性能
 * @param content 消息内容
 * @returns 处理后的HTML内容
 */
const renderMessageContent = (content: string): string => {
  if (!content) return ''

  // 检查缓存
  if (markdownCache.has(content)) {
    return markdownCache.get(content)!
  }

  try {
    // 使用轻量级markdown处理，转换为HTML
    const rendered = markdownToHtml(content)

    // 缓存结果，但限制缓存大小避免内存泄漏
    if (markdownCache.size > 100) {
      // 清除最旧的缓存项
      const firstKey = markdownCache.keys().next().value
      markdownCache.delete(firstKey)
    }
    markdownCache.set(content, rendered)

    return rendered
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return content // 降级返回原始文本
  }
}

/**
 * @description 防抖渲染函数，用于流式消息优化
 * @param content 消息内容
 * @param callback 渲染完成回调
 */
const debouncedRender = (content: string, callback: (result: string) => void) => {
  renderQueue.add(content)

  if (renderDebounceTimer) {
    clearTimeout(renderDebounceTimer)
  }

  renderDebounceTimer = setTimeout(() => {
    const toRender = Array.from(renderQueue)
    renderQueue.clear()

    toRender.forEach((contentToRender) => {
      const result = renderMessageContent(contentToRender)
      callback(result)
    })

    renderDebounceTimer = null
  }, 100) // 100ms防抖延迟
}

/**
 * @description 初始化AI智能体数据
 */
const initAgents = async () => {
  // 确保store中的智能体数据已加载
  if (chatStore.availableAgents.length === 0) {
    await chatStore.initializeAgents()
  }

  // 加载本地智能体列表
  await loadAgents()

  // 确保导航栏显示正确的智能体名称
  // 这里强制更新一次，确保页面显示与store中的数据同步
  nextTick(() => {
    const storeAgent = chatStore.currentAgent
    console.log('初始化后的当前智能体:', storeAgent)
  })
}

/**
 * @description 页面加载初始化
 */
onLoad(async () => {
  // 首先获取系统信息
  getSystemInfo()

  // 初始化网络状态监听
  initNetworkStatus()

  // 初始化AI助手列表
  await initAgents()

  // 监听屏幕变化
  // #ifdef APP-PLUS || H5
  uni.onWindowResize && uni.onWindowResize(handleResize)
  // #endif

  // 分阶段加载UI组件，提升首屏性能
  nextTick(() => {
    // 立即显示欢迎区域
    showWelcome.value = true

    // 标记首屏渲染完成
    performanceMonitor.markFirstPaint()

    // 延迟显示快速操作区域
    setTimeout(() => {
      showQuickActions.value = true

      // 标记页面加载完成
      performanceMonitor.markPageLoaded()
    }, 100)
  })
})

/**
 * @description 获取系统信息并设置安全区域
 */
const getSystemInfo = () => {
  try {
    const info = uni.getSystemInfoSync()
    systemInfo.value = info
    statusBarHeight.value = info.statusBarHeight || 0

    // 设置安全区域
    if (info.safeAreaInsets) {
      safeAreaInsets.value = info.safeAreaInsets
    } else if (info.safeArea) {
      safeAreaInsets.value = {
        top: info.safeArea.top || info.statusBarHeight || 0,
        bottom: info.safeArea.bottom ? info.screenHeight - info.safeArea.bottom : 0,
      }
    } else {
      safeAreaInsets.value = {
        top: info.statusBarHeight || 0,
        bottom: 0,
      }
    }

    // 设置导航栏高度 = 状态栏高度 + 导航栏内容高度
    navBarHeight.value = (info.statusBarHeight || 0) + 44
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 设置默认值
    navBarHeight.value = 76
    safeAreaInsets.value = { top: 20, bottom: 0 }
  }
}

/**
 * @description 初始化网络状态监听
 */
const initNetworkStatus = () => {
  // 获取初始网络状态
  uni.getNetworkType({
    success: (res) => {
      isOnline.value = res.networkType !== 'none'
    },
    fail: () => {
      isOnline.value = true // 默认认为在线
    },
  })

  // 监听网络状态变化
  uni.onNetworkStatusChange((res) => {
    const wasOnline = isOnline.value
    isOnline.value = res.isConnected

    if (!wasOnline && res.isConnected) {
      uni.showToast({
        title: '网络已连接',
        icon: 'success',
        duration: 1500,
      })
    } else if (wasOnline && !res.isConnected) {
      uni.showToast({
        title: '网络连接断开',
        icon: 'none',
        duration: 2000,
      })
    }
  })
}

/**
 * @description 监听屏幕方向变化
 */
const handleResize = () => {
  // 重新获取系统信息以适配新的屏幕尺寸
  getSystemInfo()
}

/**
 * @description 页面卸载清理
 */
onUnload(() => {
  // 清理定时器
  // if (recordTimer.value) {
  //   clearTimeout(recordTimer.value)
  //   recordTimer.value = null
  // }

  if (scrollDebounceTimer) {
    clearTimeout(scrollDebounceTimer)
    scrollDebounceTimer = null
  }

  if (renderDebounceTimer) {
    clearTimeout(renderDebounceTimer)
    renderDebounceTimer = null
  }

  if (searchDebounceTimer) {
    clearTimeout(searchDebounceTimer)
    searchDebounceTimer = null
  }

  // 清理markdown缓存和渲染队列，避免内存泄漏
  markdownCache.clear()
  renderQueue.clear()

  // 清理附件URL对象，避免内存泄漏
  attachments.value.forEach((attachment) => {
    if (attachment.url && attachment.url.startsWith('blob:')) {
      URL.revokeObjectURL(attachment.url)
    }
  })

  // 清空附件列表
  attachments.value = []

  // 页面卸载时清空所有聊天数据
  chatStore.clearAllData()
  console.log('页面卸载，聊天数据已清空')
})

// 监听当前会话变化，自动滚动到底部
watch(
  () => {
    const session = currentSession.value
    return session ? session.messages.length : 0
  },
  () => {
    nextTick(() => {
      scrollToBottom()
    })
  },
)

// 添加弹窗互斥管理函数
const closeAllModals = () => {
  showSidebar.value = false
  showHistory.value = false
  showExtensions.value = false
  showAgentSelector.value = false
}

// 关闭侧边栏
const closeSidebar = () => {
  showSidebar.value = false
}

// 关闭历史记录
const closeHistory = () => {
  showHistory.value = false
}

// 关闭扩展功能
const closeExtensions = () => {
  showExtensions.value = false
}

/**
 * @description 返回上一页
 */
const handleBack = () => {
  uni.navigateBack({
    delta: 1,
  })
}

// 处理AI选择器点击（由于选择器界面被注释，这里直接显示侧边栏）
const handleAgentSelectorClick = () => {
  closeAllModals()
  showSidebar.value = true

  // 强制刷新当前智能体信息，确保显示正确
  nextTick(() => {
    const storeAgent = chatStore.currentAgent
    const pageAgent = currentAgent.value
    console.log('选择器点击时的智能体状态:', {
      storeAgent,
      pageAgent,
      currentAgentId: chatStore.currentAgentId,
    })
  })
}

/**
 * @description 更多选项菜单
 */
const handleMore = () => {
  closeAllModals() // 先关闭所有弹窗
  uni.showActionSheet({
    itemList: ['AI助手', '清空对话', '历史记录', '扩展功能', '设置'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          showSidebar.value = true
          break
        case 1:
          clearCurrentChat()
          break
        case 2:
          showHistoryDialog()
          break
        case 3:
          showExtensions.value = true
          break
        case 4:
          showSettings()
          break
      }
    },
  })
}

// 切换AI助手
const switchAgent = async (agentId: string) => {
  try {
    console.log('页面 switchAgent 开始:', agentId)
    console.log('切换前的状态:', {
      currentAgentId: chatStore.currentAgentId,
      currentAgent: chatStore.currentAgent,
    })

    // 切换助手
    await chatStore.switchAgent(agentId)

    console.log('切换后的状态:', {
      currentAgentId: chatStore.currentAgentId,
      currentAgent: chatStore.currentAgent,
    })

    // 创建新会话
    await chatStore.createSession()

    // 关闭所有弹窗
    closeAllModals()

    // 等待下一个tick确保所有响应式更新完成
    await nextTick()

    // 再次等待确保UI完全更新
    setTimeout(() => {
      const currentAgentFromStore = chatStore.currentAgent
      console.log('最终的当前智能体:', currentAgentFromStore)

      const agentName = currentAgentFromStore ? currentAgentFromStore.name : '新助手'
      uni.showToast({
        title: `已切换到${agentName}`,
        icon: 'none',
        duration: 1500,
      })
    }, 100)
  } catch (error) {
    console.error('切换助手失败:', error)
    uni.showToast({
      title: '切换失败，请重试',
      icon: 'none',
    })
  }
}

/**
 * @description 切换扩展功能
 * @param extId 扩展功能ID
 */
const toggleExtension = (extId: string) => {
  const currentExtensions = activeExtensions.value
  const index = currentExtensions.indexOf(extId)

  // 创建新数组以触发shallowRef更新
  if (index > -1) {
    activeExtensions.value = currentExtensions.filter((id) => id !== extId)
  } else {
    activeExtensions.value = [...currentExtensions, extId]
  }

  const ext = extensions.find((e) => e.id === extId)
  if (ext) {
    uni.showToast({
      title: `${ext.name}已${index > -1 ? '禁用' : '启用'}`,
      icon: 'none',
    })
  }
}

// 添加附件
const handleAttachment = () => {
  closeAllModals() // 先关闭所有弹窗
  uni.showActionSheet({
    itemList: ['拍照', '从相册选择', '选择文件'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          chooseImage('camera')
          break
        case 1:
          chooseImage('album')
          break
        case 2:
          chooseFile()
          break
      }
    },
  })
}

/**
 * @description 选择图片
 * @param sourceType 图片来源类型
 */
const chooseImage = (sourceType: 'camera' | 'album') => {
  uni.chooseImage({
    count: 9 - attachments.value.length,
    sourceType: [sourceType],
    success: async (res) => {
      const tempPaths = Array.isArray(res.tempFilePaths) ? res.tempFilePaths : [res.tempFilePaths]

      // 显示上传进度
      uni.showLoading({
        title: '上传中...',
        mask: true,
      })

      try {
        const uploadPromises = tempPaths.map(async (tempPath) => {
          try {
            // chat-api.ts 上传文件
            const uploadResult = await chatApi.uploadFile(tempPath, 'image')
            return {
              type: 'image' as const,
              url: uploadResult.url,
              name: uploadResult.name,
              size: uploadResult.size,
              metadata: uploadResult.metadata,
            }
          } catch (error) {
            console.error('图片上传失败:', error)
            uni.showToast({
              title: '图片上传失败',
              icon: 'none',
            })
            return null
          }
        })

        const uploadedAttachments = await Promise.all(uploadPromises)
        const validAttachments = uploadedAttachments.filter((att) => att !== null)

        // 创建新数组以触发shallowRef更新
        attachments.value = [...attachments.value, ...validAttachments]

        if (validAttachments.length > 0) {
          uni.showToast({
            title: `已上传${validAttachments.length}张图片`,
            icon: 'success',
          })
        }
      } catch (error) {
        console.error('批量上传失败:', error)
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none',
        })
      } finally {
        uni.hideLoading()
      }
    },
  })
}

/**
 * @description 选择文件
 */
const chooseFile = () => {
  // #ifdef H5
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf,.doc,.docx,.txt'
  input.onchange = async (e: any) => {
    const file = e.target.files[0]
    if (file) {
      uni.showLoading({
        title: '上传中...',
        mask: true,
      })

      try {
        // 为H5环境下的File对象创建一个本地临时URL，以便API可以处理
        const localUrl = URL.createObjectURL(file)

        // 使用和chooseImage相同的上传逻辑，传递localUrl和文件类型
        const uploadResult = await chatApi.uploadFile(localUrl, 'file')
        const newAttachment = {
          type: 'file' as const,
          url: uploadResult.url,
          name: uploadResult.name || file.name,
          size: uploadResult.size || file.size,
          metadata: uploadResult.metadata,
        }

        // 创建新数组以触发shallowRef更新
        attachments.value = [...attachments.value, newAttachment]

        uni.showToast({
          title: '文件上传成功',
          icon: 'success',
        })

        // 释放创建的对象URL，避免内存泄漏
        URL.revokeObjectURL(localUrl)
      } catch (error) {
        console.error('文件上传失败:', error)
        uni.showToast({
          title: '文件上传失败',
          icon: 'none',
        })
      } finally {
        uni.hideLoading()
      }
    }
  }
  input.click()
  // #endif

  // #ifndef H5
  uni.showToast({
    title: '文件选择功能暂不支持',
    icon: 'none',
  })
  // #endif
}

/**
 * @description 移除附件
 * @param index 附件索引
 */
const removeAttachment = (index: number) => {
  // 创建新数组以触发shallowRef更新
  attachments.value = attachments.value.filter((_, i) => i !== index)
}

// 预览附件
const previewAttachment = (att: any) => {
  if (att.type === 'image') {
    uni.previewImage({
      urls: [att.url],
    })
  }
}

// 开始录音（长按开始）
// const startVoiceRecord = (e: TouchEvent) => {
//   // 阻止默认行为和事件冒泡，防止页面滚动
//   e.preventDefault()
//   e.stopPropagation()

//   if (!activeExtensions.value.includes('voice-input')) {
//     uni.showToast({
//       title: '请先启用语音输入扩展',
//       icon: 'none',
//     })
//     return
//   }

//   if (isLoading.value) {
//     return
//   }

//   // 开始录音
//   isRecording.value = true
//   recordStartTime.value = Date.now()

//   // 震动反馈
//   // #ifdef APP-PLUS || MP-WEIXIN
//   uni.vibrateShort({
//     type: 'light',
//   })
//   // #endif

//   uni.showToast({
//     title: '正在录音...',
//     icon: 'none',
//     duration: 1000,
//   })

//   // 设置录音超时（最长60秒）
//   recordTimer.value = setTimeout(() => {
//     if (isRecording.value) {
//       stopVoiceRecord()
//       uni.showToast({
//         title: '录音时间过长，已自动发送',
//         icon: 'none',
//       })
//     }
//   }, 60000)
// }

// 停止录音（松开结束）
// const stopVoiceRecord = async (e?: TouchEvent) => {
//   // 阻止默认行为，防止页面滚动
//   if (e) {
//     e.preventDefault()
//     e.stopPropagation()
//   }

//   if (!isRecording.value) return

//   const recordDuration = Date.now() - recordStartTime.value

//   // 清除定时器
//   if (recordTimer.value) {
//     clearTimeout(recordTimer.value)
//     recordTimer.value = null
//   }

//   // 录音时间太短
//   if (recordDuration < 1000) {
//     isRecording.value = false
//     uni.showToast({
//       title: '录音时间太短',
//       icon: 'none',
//     })
//     return
//   }

//   try {
//     // 停止录音动画
//     isRecording.value = false

//     uni.showLoading({
//       title: '识别中...',
//     })

//     // 在真实环境中，这里应该获取录音文件路径
//     // 当前由于录音功能复杂性，先使用模拟方式
//     // const audioFilePath = await getRecordedAudioPath()

//     // TODO: 实际录音功能实现
//     // const speechResult = await chatApi.speechToText(audioFilePath)
//     // const recognizedText = speechResult.text

//     // 模拟语音识别过程（实际应替换为真实的语音转文字）
//     await new Promise((resolve) => setTimeout(resolve, 1500 + Math.random() * 1000))

//     // 模拟语音转文字结果
//     const mockTexts = [
//       '请帮我分析一下简历',
//       '我想了解面试技巧',
//       '如何提升技术能力',
//       '制定学习计划',
//       '开始模拟面试',
//       '我有编程问题想咨询',
//     ]

//     const recognizedText = mockTexts[Math.floor(Math.random() * mockTexts.length)]

//     uni.hideLoading()

//     // 直接发送识别结果
//     await sendVoiceMessage(recognizedText)
//   } catch (error) {
//     isRecording.value = false
//     uni.hideLoading()
//     uni.showToast({
//       title: '语音识别失败',
//       icon: 'none',
//     })
//   }
// }

// 阻止滑动事件，防止录音时页面滚动
// const preventScroll = (e: TouchEvent) => {
//   // 只在录音状态下阻止滚动
//   if (isRecording.value) {
//     e.preventDefault()
//     e.stopPropagation()
//   }
// }

// 取消录音（滑动取消）
// const cancelVoiceRecord = (e?: TouchEvent) => {
//   // 阻止默认行为，防止页面滚动
//   if (e) {
//     e.preventDefault()
//     e.stopPropagation()
//   }

//   if (!isRecording.value) return

//   isRecording.value = false

//   // 清除定时器
//   if (recordTimer.value) {
//     clearTimeout(recordTimer.value)
//     recordTimer.value = null
//   }

//   uni.showToast({
//     title: '已取消录音',
//     icon: 'none',
//   })
// }

/**
 * @description 真实的语音转文字处理（当录音功能完善后使用）
 * @param audioFilePath 音频文件路径
 * @returns 识别的文本
 */
// const processVoiceToText = async (audioFilePath: string): Promise<string> => {
//   try {
//     const speechResult = await chatApi.speechToText(audioFilePath)
//     return speechResult.text
//   } catch (error) {
//     console.error('语音转文字失败:', error)
//     throw new Error('语音识别失败')
//   }
// }

/**
 * @description 发送语音识别的消息（复用发送消息逻辑）
 * @param recognizedText 识别的文本
 */
// const sendVoiceMessage = async (recognizedText: string) => {
//   if (!recognizedText.trim()) return

//   // 复用现有的发送消息逻辑，避免重复代码
//   await sendMessage(recognizedText, [])
// }

/**
 * @description 统一的消息发送函数
 * @param content 消息内容
 * @param messageAttachments 附件列表
 */
const sendMessage = async (content: string, messageAttachments: any[]) => {
  // 防止重复发送
  if (isLoading.value) {
    console.log('正在发送消息，请稍候...')
    return
  }

  // 检查网络状态
  if (!isOnline.value) {
    uni.showToast({
      title: '网络连接断开，请检查网络',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    // 检查是否存在当前会话，如果不存在则创建
    if (!currentSession.value) {
      console.log('没有当前会话，创建新会话')
      await chatStore.createSession()
    }

    // 使用store的sendMessage方法，它会处理添加用户消息、调用API、添加AI回复等逻辑
    await chatStore.sendMessage(content, messageAttachments)
  } catch (error) {
    console.error('发送消息失败', error)
    // 确保在发生错误时停止加载状态
    chatStore.setLoading(false)

    // 根据错误类型给出不同的提示
    const errorMessage = error instanceof Error ? error.message : '发送失败'
    if (errorMessage.includes('网络') || errorMessage.includes('连接')) {
      uni.showToast({
        title: '网络连接异常，请重试',
        icon: 'none',
        duration: 2000,
      })
    }
  }
}

/**
 * @description 处理发送消息
 */
const handleSend = async () => {
  if (!canSend.value) return

  const content = inputText.value.trim()
  const messageAttachments = [...attachments.value]

  // 清空输入
  inputText.value = ''
  attachments.value = []

  // 使用统一的发送消息函数
  await sendMessage(content, messageAttachments)
}

/**
 * @description 取消当前请求
 */
const handleCancelRequest = () => {
  try {
    // 取消当前的流式响应
    chatStore.cancelCurrentStream()

    uni.showToast({
      title: '已取消发送',
      icon: 'none',
      duration: 1500,
    })
  } catch (error) {
    console.error('取消请求失败:', error)
  }
}

// 创建新对话 - 清空当前会话
const createNewChat = async () => {
  try {
    // 清空当前会话数据，等待用户发送消息时再创建新会话
    chatStore.clearAllData()
    closeAllModals() // 关闭所有弹窗
    uni.showToast({
      title: '已清空对话，开始新的聊天',
      icon: 'success',
    })
  } catch (error) {
    console.error('清空对话失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  }
}

/**
 * @description 格式化日期为中文格式
 * @param timestamp 时间戳
 * @returns 格式化后的日期字符串
 */
const formatDate = (timestamp: number): string => {
  const now = new Date()
  const date = new Date(timestamp)
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  if (targetDate.getTime() === today.getTime()) {
    return '今天'
  } else if (targetDate.getTime() === yesterday.getTime()) {
    return '昨天'
  } else if (now.getTime() - targetDate.getTime() < 7 * 24 * 60 * 60 * 1000) {
    const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    return days[date.getDay()]
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

/**
 * @description 安全获取会话的更新时间
 * @param session 会话对象
 * @returns 时间戳
 */
const getSessionTimestamp = (session: any): number => {
  return session.updatedAt || session.updatedTime || session.createdAt || Date.now()
}

/**
 * @description 获取日期的数值用于排序
 * @param dateStr 日期字符串
 * @returns 数值
 */
const getDateValue = (dateStr: string): number => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  switch (dateStr) {
    case '今天':
      return today.getTime()
    case '昨天':
      return yesterday.getTime()
    default:
      // 对于星期和月日格式，使用当前时间作为基准进行估算
      if (dateStr.startsWith('星期')) {
        const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
        const dayIndex = days.indexOf(dateStr)
        const currentDay = now.getDay()
        let daysAgo = currentDay - dayIndex
        if (daysAgo <= 0) daysAgo += 7
        return today.getTime() - daysAgo * 24 * 60 * 60 * 1000
      } else {
        // 月日格式，假设是今年的日期
        const match = dateStr.match(/(\d+)月(\d+)日/)
        if (match) {
          const month = parseInt(match[1]) - 1
          const day = parseInt(match[2])
          return new Date(now.getFullYear(), month, day).getTime()
        }
      }
      return 0
  }
}

/**
 * @description 格式化时间为HH:MM格式
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

/**
 * @description 获取会话预览文本
 * @param session 会话对象
 * @returns 预览文本
 */
const getSessionPreview = (session: any): string => {
  const userMessages = session.messages.filter((msg: any) => msg.role === 'user')
  if (userMessages.length === 0) {
    return '暂无对话内容'
  }

  const lastUserMessage = userMessages[userMessages.length - 1]
  const preview = lastUserMessage.content.replace(/\n/g, ' ').trim()
  return preview.length > 50 ? preview.substring(0, 50) + '...' : preview
}

/**
 * @description 获取会话使用的AI助手
 * @param session 会话对象
 * @returns AI助手对象
 */
const getSessionAgent = (session: any): any => {
  const agentId = session.agentId || 'general'

  // 优先从当前可用代理列表中查找
  const agent = agents.value.find((agent) => agent.id === agentId)
  if (agent) {
    return agent
  }

  // 备用：返回默认助手
  return agents.value[0] || AI_AGENTS[0]
}

/**
 * @description 搜索防抖定时器
 */
let searchDebounceTimer: number | null = null

/**
 * @description 切换搜索输入框显示状态
 */
const toggleSearchInput = () => {
  showSearchInput.value = !showSearchInput.value
  if (!showSearchInput.value) {
    searchKeyword.value = ''
  }
}

/**
 * @description 处理搜索输入（防抖）
 */
const handleSearchInput = () => {
  if (searchDebounceTimer) {
    clearTimeout(searchDebounceTimer)
  }

  searchDebounceTimer = setTimeout(() => {
    // 搜索逻辑已在计算属性中处理
    searchDebounceTimer = null
  }, 300)
}

/**
 * @description 清空搜索
 */
const clearSearch = () => {
  searchKeyword.value = ''
}

/**
 * @description 高亮搜索关键词
 * @param text 原始文本
 * @returns 高亮后的HTML
 */
const highlightSearchKeyword = (text: string): string => {
  if (!searchKeyword.value.trim() || !text) {
    return text
  }

  const keyword = searchKeyword.value.trim()
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="search-highlight">$1</span>')
}

/**
 * @description 显示历史记录弹窗
 */
const showHistoryDialog = async () => {
  closeAllModals() // 先关闭所有弹窗

  try {
    historyLoading.value = true
    showHistory.value = true

    // 加载最新的会话列表
    await chatStore.loadUserSessions()
  } catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: '加载历史记录失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    historyLoading.value = false
  }
}

/**
 * @description 加载历史会话
 * @param session 要加载的会话
 */
const loadHistorySession = async (session: any) => {
  try {
    // 切换到选中的会话
    await chatStore.switchSession(session.id)

    // 关闭历史记录弹窗
    closeHistory()

    uni.showToast({
      title: '已切换到历史会话',
      icon: 'success',
      duration: 1500,
    })
  } catch (error) {
    console.error('加载历史会话失败:', error)
    uni.showToast({
      title: '加载会话失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

/**
 * @description 删除历史会话
 * @param sessionId 会话ID
 */
const deleteHistorySession = async (sessionId: string) => {
  try {
    // 显示确认对话框
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个会话吗？删除后无法恢复。',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        },
      })
    })

    if (!result) return

    // 删除会话
    await chatStore.deleteSession(sessionId)

    uni.showToast({
      title: '会话已删除',
      icon: 'success',
      duration: 1500,
    })

    // 如果删除的是当前会话，需要处理
    if (sessionId === currentSessionId.value) {
      // 创建新会话或切换到其他会话
      const remainingSessions = chatStore.sessionList
      if (remainingSessions && remainingSessions.length > 0) {
        await chatStore.switchSession(remainingSessions[0].id)
      } else {
        // 没有其他会话，清空当前会话
        chatStore.clearAllData()
      }
    }
  } catch (error) {
    console.error('删除会话失败:', error)
    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

/**
 * @description 清空所有历史记录
 */
const clearAllHistory = async () => {
  try {
    // 显示确认对话框
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有历史记录吗？此操作无法恢复。',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        },
      })
    })

    if (!result) return

    // 获取所有会话ID
    const sessions = chatStore.sessionList || []

    // 批量删除所有会话
    for (const session of sessions) {
      await chatStore.deleteSession(session.id)
    }

    // 清空当前数据
    chatStore.clearAllData()

    // 关闭历史记录弹窗
    closeHistory()

    uni.showToast({
      title: '历史记录已清空',
      icon: 'success',
      duration: 1500,
    })
  } catch (error) {
    console.error('清空历史记录失败:', error)
    uni.showToast({
      title: '清空失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 显示设置
const showSettings = () => {
  uni.showToast({
    title: '设置功能开发中',
    icon: 'none',
  })
}

// 清空当前对话
const clearCurrentChat = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空当前对话吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 直接清空所有数据，等待用户发送新消息时再创建会话
          chatStore.clearAllData()
          uni.showToast({
            title: '对话已清空',
            icon: 'success',
          })
        } catch (error) {
          console.error('清空对话失败:', error)
          uni.showToast({
            title: '操作失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 滚动到底部
const scrollToBottom = () => {
  const session = currentSession.value
  const messages = session ? session.messages : []
  if (messages.length > 0) {
    scrollIntoView.value = `msg-${messages.length - 1}`
  }
}

// 滚动防抖定时器
let scrollDebounceTimer: number | null = null

/**
 * @description 处理滚动事件（防抖优化）
 * @param e 滚动事件
 */
const handleScroll = (e: any) => {
  // 立即更新滚动位置用于虚拟滚动计算
  scrollTop.value = e.detail.scrollTop

  // 防抖处理其他滚动相关逻辑
  if (scrollDebounceTimer) {
    clearTimeout(scrollDebounceTimer)
  }

  scrollDebounceTimer = setTimeout(() => {
    // 这里可以添加其他需要防抖的滚动处理逻辑
    // 例如：保存滚动位置到本地存储
    scrollDebounceTimer = null
  }, 100)
}

// 切换到文字输入模式
// const switchToTextInput = () => {
//   // 使用强制文字模式标记，确保切换稳定
//   forceTextMode.value = true
//   nextTick(() => {
//     // 清空输入框
//     inputText.value = ''
//   })
//   uni.showToast({
//     title: '已切换到文字模式',
//     icon: 'none',
//     duration: 1500,
//   })
// }

/**
 * @description 切换到语音输入模式
 * 当用户点击麦克风按钮时，会切换到语音输入模式
 * 清空输入框内容并显示语音输入界面
 */
// const switchToVoiceInput = () => {
//   // 清空强制文字模式标记，切换到语音模式
//   forceTextMode.value = false
//   // 清空输入框内容
//   inputText.value = ''
//   // 清空附件
//   attachments.value = []
//   // 显示提示
//   uni.showToast({
//     title: '已切换到语音模式，长按说话',
//     icon: 'none',
//     duration: 2000,
//   })
// }

// 监听输入文本变化，保持文字输入模式的稳定性
// watch(inputText, (newValue) => {
//   // 当用户在文字模式下输入内容时，保持文字模式不变
//   // 不再自动切换到语音模式，需要用户主动点击切换
// })

// 监听输入框失去焦点，保持当前输入模式
// const handleInputBlur = () => {
//   // 保持当前模式不变，不再自动切换到语音模式
//   // 用户需要主动点击切换按钮来改变输入模式
// }

/**
 * @description 切换think思考过程的显示/隐藏
 * @param messageId 消息ID
 */
const toggleThink = (messageId: string) => {
  // 使用chatStore的方法来更新消息
  chatStore.toggleMessageThink(messageId)
}

/**
 * @description 重试发送失败的消息
 * @param message 失败的消息对象
 */
const retryMessage = async (message: any) => {
  if (!message || message.role !== 'assistant' || !message.isError) {
    return
  }

  try {
    // 找到对应的用户消息
    const session = currentSession.value
    if (!session) return

    const messageIndex = session.messages.findIndex((m) => m.id === message.id)
    if (messageIndex === -1) return

    // 找到前一条用户消息
    let userMessage = null
    for (let i = messageIndex - 1; i >= 0; i--) {
      if (session.messages[i].role === 'user') {
        userMessage = session.messages[i]
        break
      }
    }

    if (!userMessage) {
      uni.showToast({
        title: '无法找到原始消息',
        icon: 'none',
      })
      return
    }

    // 重置错误消息状态
    chatStore.resetMessageError(message.id)

    // 重新发送消息
    await sendMessage(userMessage.content, userMessage.attachments || [])
  } catch (error) {
    console.error('重试消息失败:', error)
    uni.showToast({
      title: '重试失败，请稍后再试',
      icon: 'none',
    })
  }
}
</script>

<style lang="scss" scoped>
// ========================
// CSS 变量定义
// ========================
.ai-chat-container {
  // 主题色
  --primary-color: #00c9a7;
  --primary-dark: #018d71;
  --primary-light: #00c49a;
  --primary-bg: #e8f8f5;
  --primary-bg-light: #d4f4ed;
  --primary-bg-lighter: #f0fffe;

  // 危险色
  --danger-color: #ff6b6b;
  --danger-dark: #ee5a52;
  --danger-darker: #ff4444;

  // 中性色
  --text-primary: #333;
  --text-secondary: #666;
  --text-muted: #999;
  --text-light: #9ca3af;
  --bg-primary: #fff;
  --bg-secondary: #f8f8f8;
  --bg-tertiary: #f5f5f5;
  --bg-page: #f7f9fc;
  --border-color: #f0f0f0;
  --border-light: rgba(0, 0, 0, 0.06);

  // 间距
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 40rpx;

  // 圆角
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-full: 50%;

  // 阴影
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
  --shadow-primary: 0 8px 24px rgba(0, 201, 167, 0.15);
}

// ========================
// 基础布局
// ========================
.ai-chat-container {
  // 确保容器占满整个视口
  position: fixed; // 使用fixed定位防止页面滚动
  top: 0;
  right: 0; // 添加right:0确保宽度正确
  bottom: 0; // 添加bottom:0确保高度正确
  left: 0;
  display: flex;
  flex-direction: column;
  width: 100%; // 使用100%而不是100vw，避免水平滚动条问题
  min-height: 85vh; // 确保最小高度为视口高度
  max-height: 200vh; // 确保最大高度不超过视口
  overflow: hidden; // 禁止页面级别滚动
  // 禁止滚动相关属性
  overscroll-behavior: none;
  touch-action: manipulation; // 只允许基础触摸操作，禁止滚动
  // 禁止长按
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  // 禁止拉拽
  user-select: none;
  background: var(--bg-page);
  transform: translateZ(0);
  // 性能优化：启用GPU加速和布局优化
  will-change: transform;
  contain: layout style paint;
  -webkit-overflow-scrolling: auto;
}

// 主内容区容器
.content-container {
  // 确保内容区域不会超出视口
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 100%; // 设置明确高度
  min-height: 0; // 确保flex子项能够收缩
  max-height: 100vh; // 确保不超过视口高度
  margin-top: 40rpx;
  overflow: hidden; // 禁止内容区滚动
  // 禁止滚动
  overscroll-behavior: none;
  touch-action: manipulation;
}


// ========================
// 顶部导航栏
// ========================
.nav-bar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // 确保头部栏始终固定在顶部
  width: 100%;
  min-height: 100rpx;
  color: var(--bg-primary);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(0, 201, 167, 0.15);

  // 背景装饰
  &::before,
  &::after {
    position: absolute;
    content: '';
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    animation: floating 6s ease-in-out infinite;
  }

  &::before {
    top: 10%;
    right: 15%;
    width: 60px;
    height: 60px;
  }

  &::after {
    bottom: 20%;
    left: 10%;
    width: 80rpx;
    height: 80rpx;
    animation-duration: 5s;
    animation-direction: reverse;
  }

  .nav-left,
  .nav-right {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    margin: 10rpx;
    overflow: hidden;
    color: var(--bg-primary);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-full);
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(255, 255, 255, 0.2) inset;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    // 添加光泽效果
    &::before {
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      content: '';
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
      opacity: 0;
      transition: all 0.6s ease;
      transform: rotate(45deg);
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%);
      border-color: rgba(255, 255, 255, 0.4);
      box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(255, 255, 255, 0.3) inset;
      transform: translateY(-2px) scale(1.05);

      &::before {
        opacity: 1;
        animation: shimmer 0.6s ease-out;
      }
    }

    &:active {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.3) 100%);
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.2),
        0 1px 4px rgba(255, 255, 255, 0.4) inset;
      transform: translateY(0) scale(0.95);
    }

    // 图标动画
    .i-carbon-arrow-left,
    .i-carbon-overflow-menu-horizontal {
      transition: all 0.3s ease;
    }

    &:hover .i-carbon-arrow-left {
      transform: translateX(-2px);
    }

    &:hover .i-carbon-overflow-menu-horizontal {
      transform: rotate(90deg);
    }
  }

  .nav-center {
    position: absolute;
    left: 50%;
    z-index: 2;
    transform: translateX(-50%);
  }

  .ai-selector {
    position: relative;
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-xl);
    overflow: hidden;
    font-size: 32rpx;
    font-weight: 600;
    color: var(--bg-primary);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.12),
      0 3px 10px rgba(255, 255, 255, 0.2) inset;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    // 添加脉冲动画背景
    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      content: '';
      background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
      border-radius: 50%;
      transition: all 0.6s ease;
      transform: translate(-50%, -50%);
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%);
      border-color: rgba(255, 255, 255, 0.4);
      box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.15),
        0 5px 15px rgba(255, 255, 255, 0.3) inset;
      transform: translateY(-3px) scale(1.02);

      &::before {
        width: 100%;
        height: 100%;
        animation: pulse-glow 1.5s ease-out infinite;
      }

      .i-carbon-chevron-down {
        transform: rotate(180deg) scale(1.1);
      }
    }

    &:active {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.3) 100%);
      box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(255, 255, 255, 0.4) inset;
      transform: translateY(-1px) scale(0.98);
    }

    // 图标动画
    .i-carbon-chevron-down {
      margin-left: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // AI图标动画
    [class*="i-carbon-"] {
      transition: all 0.3s ease;

      &:not(.i-carbon-chevron-down) {
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }
    }

    &:hover [class*="i-carbon-"]:not(.i-carbon-chevron-down) {
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
      transform: scale(1.1) rotate(5deg);
    }
  }
}

// ========================
// 侧边栏样式
// ========================
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.3s;

  &.active {
    pointer-events: auto;
    touch-action: none;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.sidebar.active .sidebar-content {
  transform: translateX(0);
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  width: 85%;
  max-width: 640rpx;
  height: 100%;
  overflow: hidden;
  background-color: var(--bg-primary);
  box-shadow: 8rpx 0 48rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-100%);

  .sidebar-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-xl);
    color: var(--bg-primary);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-light) 100%);

    .sidebar-title {
      font-size: 40rpx;
      font-weight: 600;
    }
  }

  .agent-list {
    flex: 1;
    padding: var(--spacing-sm);
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    .agent-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      background-color: var(--bg-secondary);
      border-radius: var(--radius-lg);
      transition: all 0.3s;

      &.active {
        background: linear-gradient(135deg, var(--primary-bg) 0%, var(--primary-bg-light) 100%);
        box-shadow: var(--shadow-sm);
      }

      &:active {
        transform: scale(0.98);
      }

      .agent-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72rpx;
        height: 72rpx;
        margin-right: var(--spacing-md);
        border-radius: 20rpx;
      }

      .agent-info {
        flex: 1;
        overflow: hidden;

        .agent-name {
          display: block;
          overflow: hidden;
          font-size: 30rpx;
          font-weight: 500;
          color: var(--text-primary);
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .agent-desc {
          display: block;
          margin-top: var(--spacing-xs);
          font-size: 24rpx;
          color: var(--text-muted);
        }
      }
    }
  }

  .sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-md);
      font-size: 30rpx;
      color: var(--primary-dark);
      background-color: var(--primary-bg);
      border-radius: var(--radius-lg);
      transition: all 0.3s;

      &:active {
        background-color: var(--primary-bg-light);
        transform: scale(0.98);
      }
    }
  }
}

// ========================
// 主内容区
// ========================
.main-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx 40rpx;
  overflow: hidden; // 禁止主内容区滚动
  // 禁止滚动相关属性
  overscroll-behavior: none;
  touch-action: manipulation;
  background: var(--bg-page);
}

// 欢迎区域
.welcome-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  margin-bottom: 80rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 48rpx;
  box-shadow: var(--shadow-primary);

  &::before {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(45deg, transparent, rgba(0, 201, 167, 0.05), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s ease-in-out infinite;
  }

  .ai-avatar-large {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 40rpx;
    border-radius: var(--radius-full);
    box-shadow: 0 8px 24px rgba(0, 201, 167, 0.3);
    animation: float 3s ease-in-out infinite;

    &::before {
      position: absolute;
      top: -8rpx;
      right: -8rpx;
      bottom: -8rpx;
      left: -8rpx;
      z-index: -1;
      content: '';
      background: linear-gradient(45deg,
          var(--primary-color),
          var(--primary-light),
          var(--primary-color));
      border-radius: var(--radius-full);
      animation: rotate 4s linear infinite;
    }
  }

  .welcome-title {
    position: relative;
    z-index: 1;
    margin-bottom: var(--spacing-md);
    text-align: center;

    .gradient-text {
      font-size: 56rpx;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 201, 167, 0.1);
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .welcome-desc {
    position: relative;
    z-index: 1;
    font-size: 32rpx;
    line-height: 1.5;
    color: var(--text-secondary);
    text-align: center;
  }
}

// 加载占位符
.welcome-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300rpx;

  .loading-placeholder {
    width: 160rpx;
    height: 160rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-full);
    animation: shimmer 1.5s infinite;
  }
}

// 快速操作区
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  width: 100%;
  max-width: 1200rpx;
  // 性能优化：避免布局抖动
  contain: layout;

  .quick-action-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: var(--spacing-xl);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 2rpx solid rgba(0, 201, 167, 0.1);
    border-radius: var(--radius-xl);
    box-shadow: 0 4px 16px rgba(0, 201, 167, 0.08);
    transition: all 0.3s ease;

    &::before {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      content: '';
      background: linear-gradient(90deg, transparent, rgba(0, 201, 167, 0.05), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      border-color: var(--primary-color);
      box-shadow: 0 8px 24px rgba(0, 201, 167, 0.15);
      transform: translateY(-2px);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: scale(0.98) translateY(0);
    }

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 96rpx;
      height: 96rpx;
      margin-right: var(--spacing-lg);
      font-size: 48rpx;
      color: var(--primary-color);
      background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(0, 179, 154, 0.1) 100%);
      backdrop-filter: blur(10px);
      border-radius: var(--radius-lg);
    }

    .action-content {
      flex: 1;

      .action-title {
        display: block;
        margin-bottom: var(--spacing-xs);
        font-size: 32rpx;
        font-weight: 600;
        color: var(--text-primary);
      }

      .action-desc {
        display: block;
        font-size: 28rpx;
        line-height: 1.4;
        color: var(--text-secondary);
      }
    }
  }
}

// ========================
// 聊天内容区
// ========================
.chat-content {
  flex: 1;
  height: 100%;
  min-height: 0; // 确保能够收缩
  max-height: calc(100vh - 200rpx); // 预留顶部导航和底部输入区空间
  padding-bottom: 160rpx; // 增加底部padding确保内容不被底部栏遮挡
  overflow-x: hidden;
  overflow-y: auto;
  overscroll-behavior: contain;
  // 允许scroll-view内部滚动
  touch-action: pan-y pinch-zoom; // 允许垂直平移和缩放
  background-color: var(--bg-primary);
  will-change: scroll-position;
  // 滚动性能优化
  -webkit-overflow-scrolling: touch;
  contain: layout style;
}

.message-list {
  padding: 40rpx 32rpx;
}

.message-item {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: 48rpx;
  animation: fadeIn 0.3s ease-out;
  // 性能优化：减少重排和重绘
  contain: layout style;
  will-change: auto;

  &.message-user {
    flex-direction: row-reverse;
  }

  .message-avatar {
    flex-shrink: 0;

    .user-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      color: var(--bg-primary);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: var(--radius-full);
    }

    .ai-avatar-small {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      color: var(--bg-primary);
      border-radius: var(--radius-full);
    }
  }
}

.message-item .message-content {
  display: flex;
  max-width: 70%;
  padding: 14rpx 18rpx;
  border-radius: 40rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.message-item.message-user .message-content {
  color: var(--bg-primary);
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-light) 100%);
  border-bottom-right-radius: var(--radius-sm);
}

.message-item.message-assistant .message-content {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-bottom-left-radius: var(--radius-sm);
}

// 消息文本样式（包含Markdown样式）
.message-text {
  line-height: 1.6;
  word-break: break-word;

  // 流式消息样式
  &.streaming {
    position: relative;

    &::after {
      position: absolute;
      right: -8rpx;
      bottom: 0;
      width: 4rpx;
      height: 1.2em;
      content: '';
      background-color: var(--primary-dark);
      animation: cursor-blink 1s step-end infinite;
    }
  }

  &.error {
    color: var(--danger-color);
    opacity: 0.7;
  }

  // Markdown 样式定义
  :deep(.markdown-heading) {
    margin: 32rpx 0 24rpx 0;
    font-weight: 600;
    line-height: 1.4;
    color: #1f2937;

    &.markdown-h1 {
      padding-bottom: 16rpx;
      font-size: 40rpx;
      border-bottom: 2rpx solid #e5e7eb;
    }

    &.markdown-h2 {
      font-size: 36rpx;
      color: var(--primary-dark);
    }

    &.markdown-h3 {
      font-size: 32rpx;
      color: #374151;
    }

    &.markdown-h4,
    &.markdown-h5,
    &.markdown-h6 {
      font-size: 28rpx;
      color: #6b7280;
    }
  }

  :deep(.markdown-paragraph) {
    margin: 16rpx 0;
    line-height: 1.7;
    color: #374151;
  }

  :deep(.markdown-list) {
    padding-left: 40rpx;
    margin: 24rpx 0;

    &.markdown-ordered-list {
      list-style-type: decimal;
    }

    .markdown-list-item {
      margin: 8rpx 0;
      line-height: 1.6;
      color: #374151;

      &:before {
        content: '';
      }
    }
  }

  :deep(.markdown-blockquote) {
    padding: 24rpx 32rpx;
    margin: 32rpx 0;
    font-style: italic;
    color: #166534;
    background-color: #f0fdf4;
    border-left: 8rpx solid var(--primary-dark);

    p {
      margin: 0;
    }
  }

  :deep(.markdown-table) {
    width: 100%;
    margin: 32rpx 0;
    font-size: 28rpx;
    border-collapse: collapse;
    border: 2rpx solid #e5e7eb;

    th,
    td {
      padding: 16rpx 24rpx;
      text-align: left;
      border: 2rpx solid #e5e7eb;
    }

    th {
      font-weight: 600;
      color: #374151;
      background-color: #f9fafb;
    }

    td {
      color: #6b7280;
    }

    tr:nth-child(even) td {
      background-color: #f9fafb;
    }
  }

  :deep(.markdown-inline-code) {
    padding: 4rpx 12rpx;
    margin: 0 4rpx;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 26rpx;
    color: #ef4444;
    background-color: #f3f4f6;
    border: 2rpx solid #e5e7eb;
    border-radius: var(--radius-sm);
  }

  :deep(.hljs) {
    margin: 32rpx 0;
    overflow-x: auto;
    background-color: #1f2937 !important;
    border-radius: var(--radius-md);

    code {
      display: block;
      padding: 32rpx;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 26rpx;
      line-height: 1.5;
      color: #f9fafb;
    }

    .hljs-keyword,
    .hljs-selector-tag,
    .hljs-built_in {
      color: #8b5cf6;
    }

    .hljs-string,
    .hljs-attr {
      color: #10b981;
    }

    .hljs-number,
    .hljs-literal {
      color: #f59e0b;
    }

    .hljs-comment {
      font-style: italic;
      color: #9ca3af;
    }

    .hljs-function,
    .hljs-title {
      color: #3b82f6;
    }

    .hljs-variable,
    .hljs-name {
      color: #ef4444;
    }
  }

  :deep(.markdown-strong) {
    font-weight: 600;
    color: #1f2937;
  }

  :deep(.markdown-em) {
    font-style: italic;
    color: #6b7280;
  }

  :deep(.analysis-card) {
    padding: 30rpx;
    margin: 32rpx 0;
    background: #f8f9fa;
    border: 2rpx solid #e5e7eb;
    border-radius: 20rpx;

    .analysis-header {
      display: flex;
      gap: 20rpx;
      align-items: center;
      margin-bottom: 24rpx;
      font-weight: 600;
      color: #1f2937;
    }

    .analysis-content {
      font-size: 28rpx;
      line-height: 1.6;
      color: #4b5563;

      .progress-bar {
        height: 12rpx;
        margin: 16rpx 0;
        overflow: hidden;
        background: #e5e7eb;
        border-radius: 6rpx;

        .progress-fill {
          height: 100%;
          background: var(--primary-dark);
          border-radius: 6rpx;
          transition: width 0.3s ease;
        }
      }
    }
  }

  :deep(a) {
    color: var(--primary-dark);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(hr) {
    margin: 40rpx 0;
    border: none;
    border-top: 2rpx solid #e5e7eb;
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    margin: 16rpx 0;
    border-radius: var(--radius-md);
  }

  :deep(input[type='checkbox']) {
    margin-right: 16rpx;
    accent-color: var(--primary-dark);
  }

  :deep(kbd) {
    padding: 4rpx 12rpx;
    margin: 0 4rpx;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 24rpx;
    color: #374151;
    background-color: #f3f4f6;
    border: 2rpx solid #d1d5db;
    border-radius: var(--radius-sm);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

// 流式消息样式增强
.ai-message-container {
  position: relative;
  width: 100%;

  .streaming-indicator {
    display: flex;
    gap: 8rpx;
    align-items: center;
    margin-top: 16rpx;
    font-size: 24rpx;
    color: var(--text-muted);

    .cursor-blink {
      width: 6rpx;
      height: 32rpx;
      background-color: var(--primary-dark);
      border-radius: 2rpx;
      animation: cursor-blink 1s step-end infinite;
    }
  }

  .error-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12rpx 20rpx;
    margin-top: 16rpx;
    background-color: #fef2f2;
    border: 2rpx solid #fecaca;
    border-radius: var(--radius-md);

    .error-content {
      display: flex;
      flex: 1;
      gap: 8rpx;
      align-items: center;

      .error-text {
        font-size: 24rpx;
        color: var(--danger-color);
      }
    }

    .error-actions {
      display: flex;
      align-items: center;
      margin-left: 16rpx;

      .retry-btn {
        display: flex;
        gap: 4rpx;
        align-items: center;
        padding: 8rpx 12rpx;
        font-size: 22rpx;
        color: var(--primary-color);
        background-color: rgba(0, 201, 167, 0.1);
        border: 1rpx solid var(--primary-color);
        border-radius: 16rpx;
        transition: all 0.2s ease;

        &:active {
          background-color: rgba(0, 201, 167, 0.2);
          transform: scale(0.95);
        }

        text {
          font-size: 22rpx;
          color: var(--primary-color);
        }
      }
    }
  }
}

// 光标闪烁动画
@keyframes cursor-blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

// Think思考过程样式
.think-container {
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f8ff 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;

  .think-header {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    user-select: none;
    background: rgba(59, 130, 246, 0.05);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    transition: background-color 0.3s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.08);
    }

    .think-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32rpx;
      height: 32rpx;
      color: #3b82f6;
      background: rgba(59, 130, 246, 0.1);
      border-radius: var(--radius-sm);
    }

    .think-label {
      flex: 1;
      font-size: 28rpx;
      font-weight: 500;
      color: #3b82f6;
    }

    .think-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32rpx;
      height: 32rpx;
      color: #6b7280;
      transition: all 0.3s ease;

      &:hover {
        color: #3b82f6;
        transform: scale(1.1);
      }
    }
  }

  .think-content {
    padding: var(--spacing-lg);
    animation: slideDown 0.3s ease-out;

    .think-text {
      font-size: 26rpx;
      line-height: 1.6;
      color: #4b5563;
      word-break: break-word;
      white-space: pre-wrap;
    }
  }
}

// Think展开动画
@keyframes slideDown {
  0% {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
  }

  100% {
    max-height: 500rpx;
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    opacity: 1;
  }
}

// 消息附件
.message-attachments {
  margin-top: var(--spacing-md);

  .attachment-item {
    margin-top: var(--spacing-sm);

    .attachment-image {
      max-width: 400rpx;
      max-height: 400rpx;
      border-radius: var(--radius-lg);
    }

    .attachment-file {
      display: flex;
      gap: var(--spacing-sm);
      align-items: center;
      padding: 20rpx 28rpx;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 20rpx;

      .file-name {
        font-size: 26rpx;
      }
    }
  }
}

// 输入中指示器
.typing-indicator {
  display: flex;
  gap: 12rpx;
  padding: 16rpx 0;

  .typing-dot {
    width: 20rpx;
    height: 20rpx;
    background-color: var(--primary-dark);
    border-radius: var(--radius-full);
    animation: typing 1.4s infinite;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

.message-spacer {
  height: 200rpx; // 进一步增加空隙高度，确保最后一条消息有充足的显示空间
}

// ========================
// 扩展功能面板
// ========================

.panel-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: 60vh;
  margin-top: auto;
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  box-shadow: 0 -8rpx 48rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(100%);

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 2rpx solid var(--border-color);

    .panel-title {
      font-size: 36rpx;
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .extensions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    overflow-y: auto;

    .extension-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32rpx 16rpx;
      text-align: center;
      background-color: var(--bg-secondary);
      border-radius: var(--radius-lg);
      transition: all 0.3s;

      &.active {
        background: linear-gradient(135deg, var(--primary-bg) 0%, var(--primary-bg-light) 100%);
        border: 4rpx solid var(--primary-dark);
      }

      &:active {
        transform: scale(0.95);
      }

      .ext-icon {
        margin-bottom: var(--spacing-sm);
        font-size: 48rpx;
        color: var(--primary-dark);
      }

      .ext-name {
        margin-bottom: var(--spacing-xs);
        font-size: 24rpx;
        font-weight: 600;
        color: var(--text-primary);
      }

      .ext-desc {
        font-size: 20rpx;
        line-height: 1.2;
        color: var(--text-secondary);
      }
    }
  }
}

.extensions-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.3s;

  &.active {
    pointer-events: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.extensions-panel.active .panel-content {
  transform: translateY(0);
}

// ========================
// 底部输入区
// ========================
// 底部输入区
.input-area {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  // 确保在所有设备上都有足够的内边距
  padding-top: 16rpx;
  padding-right: 16rpx;
  padding-left: 16rpx;
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: blur(20rpx);
  backdrop-filter: blur(20rpx);
  border-top: 1px solid #e8eaed;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);

  // 基础样式重置
  * {
    box-sizing: border-box;
  }

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    background: linear-gradient(90deg, transparent 0%, #018d71 20%, #018d71 80%, transparent 100%);
    opacity: 0.3;
  }
}

// 附件预览
.attachment-preview {
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f2f5;

  .attachment-list {
    display: flex;
    gap: 10px;
    padding-bottom: 4px;
    overflow-x: auto;

    &::-webkit-scrollbar {
      height: 2px;
    }

    &::-webkit-scrollbar-track {
      background: #f0f2f5;
      border-radius: 1px;
    }

    &::-webkit-scrollbar-thumb {
      background: #018d71;
      border-radius: 1px;
    }
  }

  .preview-item {
    position: relative;
    flex-shrink: 0;
  }

  .preview-image {
    width: 64px;
    height: 64px;
    border: 2px solid #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(1, 141, 113, 0.15);
  }

  .preview-file {
    display: flex;
    gap: 6px;
    align-items: center;
    padding: 10px 14px;
    background: linear-gradient(135deg, #f0fffe 0%, #ffffff 100%);
    border: 1px solid #e0f8f7;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(1, 141, 113, 0.08);

    .file-name {
      max-width: 100px;
      overflow: hidden;
      font-size: 12px;
      font-weight: 500;
      color: #018d71;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .remove-btn {
    position: absolute;
    top: -6px;
    right: -6px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: #fff;
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    border: 2px solid #fff;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
    transition: all 0.3s ease;

    &:active {
      box-shadow: 0 1px 4px rgba(245, 101, 101, 0.4);
      transform: scale(0.85);
    }
  }
}

.input-container {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 60rpx;
  padding: 12px 0px 16px;
  background: rgba(255, 255, 255, 0.95);

  .left-actions,
  .right-actions {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  .left-actions {
    order: 1;
  }

  .right-actions {
    order: 3;
  }

  .action-btn {
    position: relative;
    box-sizing: border-box;
    // 基础布局
    display: flex;
    flex-grow: 0;

    // 防止变形
    flex-shrink: 0;
    align-items: center;
    justify-content: center;

    // 固定尺寸 - 确保与输入框高度一致
    width: 44px !important;
    min-width: 44px !important;
    max-width: 44px !important;
    height: 44px !important;
    min-height: 44px !important;
    max-height: 44px !important;
    overflow: hidden;

    // 样式
    color: #666;
    cursor: pointer;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #e0e6ed;
    border-radius: 50%;
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(255, 255, 255, 0.8) inset;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    // 添加光泽效果
    &::before {
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      content: '';
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.6) 50%, transparent 70%);
      opacity: 0;
      transition: all 0.6s ease;
      transform: rotate(45deg);
    }

    // 添加脉冲效果
    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      content: '';
      background: radial-gradient(circle, rgba(1, 141, 113, 0.2) 0%, transparent 70%);
      border-radius: 50%;
      transition: all 0.4s ease;
      transform: translate(-50%, -50%);
    }

    &:hover {
      color: #018d71;
      background: linear-gradient(135deg, #f0f8f7 0%, #d4edda 100%);
      border-color: #018d71;
      box-shadow:
        0 6px 20px rgba(1, 141, 113, 0.25),
        0 2px 8px rgba(255, 255, 255, 0.9) inset;
      transform: translateY(-2px) scale(1.05);

      &::before {
        opacity: 1;
        animation: shimmer 0.8s ease-out;
      }

      &::after {
        width: 100%;
        height: 100%;
        animation: pulse-ripple 1s ease-out infinite;
      }

      // 图标动画
      [class*="i-carbon-"] {
        filter: drop-shadow(0 2px 4px rgba(1, 141, 113, 0.3));
        transform: scale(1.1) rotate(5deg);
      }
    }

    &:active {
      background: linear-gradient(135deg, #e6fffa 0%, #b3f5e6 100%);
      box-shadow:
        0 2px 8px rgba(1, 141, 113, 0.3),
        0 1px 4px rgba(255, 255, 255, 0.9) inset;
      transform: scale(0.95) translateY(0);

      [class*="i-carbon-"] {
        transform: scale(0.9);
      }
    }

    &.active {
      color: #fff;
      background: linear-gradient(135deg, #018d71 0%, #00c49a 100%);
      border-color: #018d71;
      box-shadow: 0 4px 12px rgba(1, 141, 113, 0.3);

      &:hover {
        background: linear-gradient(135deg, #016a5a 0%, #00b089 100%);
        box-shadow: 0 6px 16px rgba(1, 141, 113, 0.4);
      }

      &:active {
        box-shadow: 0 2px 8px rgba(1, 141, 113, 0.4);
        transform: scale(0.9);
      }
    }

    &.cancel {
      color: #fff;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      border-color: #ff6b6b;
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);

      &:hover {
        background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
        box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
      }

      &:active {
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
        transform: scale(0.9);
      }
    }

    &.recording {
      color: #fff;
      background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
      border-color: #f56565;
      box-shadow: 0 4px 16px rgba(245, 101, 101, 0.4);
      animation: pulse 1.5s infinite;
    }

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
      background: #f5f5f5;
      border-color: #e0e0e0;

      &:hover {
        color: #ccc;
        background: #f5f5f5;
        border-color: #e0e0e0;
        box-shadow: none;
        transform: none;
      }
    }
  }

  .input-field {
    box-sizing: border-box;
    flex: 1;
    align-self: center;
    order: 2;

    // 确保在小屏幕上也有足够的触摸区域
    min-width: 0;

    // 固定高度与按钮保持一致
    height: 44px !important;
    min-height: 44px !important;
    max-height: 120px;

    // 内边距 - 确保文字垂直居中
    padding: 10px 16px !important;
    overflow-x: hidden;
    overflow-y: auto;

    // 字体样式
    font-size: 16px;
    line-height: 1.4;
    color: #333;
    word-break: break-all;
    word-wrap: break-word;
    resize: none;

    // 外观
    background: #fff;
    border: 1px solid #e0e6ed;
    border-radius: 22px;
    outline: none;

    // 动画
    transition: all 0.3s ease;

    &::placeholder {
      color: #9ca3af;
    }

    &:focus {
      background: #fff;
      border-color: #018d71;
      box-shadow: 0 0 0 3px rgba(1, 141, 113, 0.1);
    }

    &:hover:not(:focus) {
      border-color: #b0d7c4;
      box-shadow: 0 2px 8px rgba(1, 141, 113, 0.08);
    }

    &:disabled {
      color: #999;
      cursor: not-allowed;
      background: #f5f5f5;
    }
  }
}

// 语音输入区域
.voice-input-area {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  padding: 0 16px;
  touch-action: none;
  cursor: pointer;
  user-select: none;
  background: var(--bg-primary);

  // 兼容H5端
  /* #ifdef H5 */
  background: #f8f9fa;
  border: 2px solid rgba(1, 141, 113, 0.1);
  border: 1px solid #e0e6ed;
  border-radius: 20px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 4px rgba(0, 0, 0, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s ease;
  /* #endif */

  &:hover:not(.recording) {
    background: var(--primary-bg-lighter);

    // H5端适配
    /* #ifdef H5 */
    background: #f0f8f7;
    border-color: rgba(1, 141, 113, 0.2);
    border-color: #018d71;
    box-shadow:
      0 6px 16px rgba(1, 141, 113, 0.08),
      0 3px 6px rgba(0, 0, 0, 0.04);
    box-shadow: 0 2px 8px rgba(1, 141, 113, 0.08);
    transform: translateY(-1px);
    /* #endif */
  }

  &:active:not(.recording) {
    box-shadow:
      0 4px 12px rgba(1, 141, 113, 0.12),
      0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(0) scale(0.98);
  }

  &.recording {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
    border-color: var(--danger-color);
    box-shadow:
      0 8px 24px rgba(255, 107, 107, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
    animation: recording-pulse 1.5s infinite;

    &::after {
      position: absolute;
      top: -4px;
      right: -4px;
      width: 12px;
      height: 12px;
      content: '';
      background: var(--danger-darker);
      border: 2px solid var(--bg-primary);
      border-radius: var(--radius-full);
      box-shadow: 0 2px 8px rgba(255, 68, 68, 0.4);
      animation: pulse 1s infinite;
    }
  }

  .voice-input-btn {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 8px 0;

    .voice-icon {
      font-size: 22px;
      color: #6b7280;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.active {
        color: #ffffff;
        filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3));
        animation: voice-bounce 0.8s ease-in-out infinite alternate;
      }
    }

    .voice-text {
      font-size: 16px;
      font-weight: 500;
      color: #6b7280;
      letter-spacing: 0.5px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      .recording & {
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// 重复的动画已删除，统一定义在上方

// ========================
// 历史记录弹窗
// ========================
.history-dialog {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.3s;

  &.active {
    pointer-events: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.history-dialog.active .history-content {
  opacity: 1;
  transform: scale(1);
}

.history-content {
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  margin: auto;
  background-color: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  transition:
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s;
  transform: scale(0.9);

  .history-header {
    display: flex;
    align-items: center;
    padding: 20px;
    color: var(--bg-primary);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-light) 100%);
    border-radius: var(--radius-md) var(--radius-md) 0 0;

    .history-title {
      font-size: 18px;
      font-weight: 600;
    }

    .history-actions {
      display: flex;
      gap: 8px;

      .header-btn {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        overflow: hidden;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-full);
        box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.1),
          0 1px 4px rgba(255, 255, 255, 0.3) inset;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

        // 添加光泽效果
        &::before {
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          content: '';
          background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.4) 50%, transparent 70%);
          opacity: 0;
          transition: all 0.6s ease;
          transform: rotate(45deg);
        }

        &:hover {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.15),
            0 2px 8px rgba(255, 255, 255, 0.4) inset;
          transform: translateY(-1px) scale(1.05);

          &::before {
            opacity: 1;
            animation: shimmer 0.6s ease-out;
          }

          // 图标动画
          [class*="i-carbon-"] {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            transform: scale(1.1);
          }
        }

        &:active {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
          box-shadow:
            0 1px 4px rgba(0, 0, 0, 0.2),
            0 1px 2px rgba(255, 255, 255, 0.5) inset;
          transform: scale(0.95);

          [class*="i-carbon-"] {
            transform: scale(0.9);
          }
        }

        // 特殊按钮样式
        &:first-child:hover [class*="i-carbon-search"] {
          transform: scale(1.2) rotate(15deg);
        }

        &:nth-child(2):hover [class*="i-carbon-trash"] {
          color: #ff6b6b;
          transform: scale(1.1) rotate(-5deg);
        }

        &:last-child:hover [class*="i-carbon-close"] {
          transform: scale(1.1) rotate(90deg);
        }
      }
    }
  }

  // 搜索容器
  .search-container {
    padding: 16px 20px;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);

    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: var(--bg-secondary);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-sm);
      transition: border-color 0.3s;

      &:focus-within {
        border-color: var(--primary-color);
      }

      .search-icon {
        margin-right: 8px;
        color: var(--text-muted);
      }

      .search-input {
        flex: 1;
        font-size: 14px;
        color: var(--text-primary);
        background: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: var(--text-muted);
        }
      }

      .clear-search {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        color: var(--text-muted);
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        transition: all 0.3s;

        &:hover {
          color: var(--text-primary);
          background-color: rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .history-body {
    flex: 1;
    overflow: hidden;

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      text-align: center;

      .loading-spinner {
        width: 32px;
        height: 32px;
        margin-bottom: 16px;
        border: 3px solid var(--border-color);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        font-size: 14px;
        color: var(--text-muted);
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      text-align: center;

      .empty-icon {
        margin-bottom: 20px;
        color: #ccc;
      }

      .empty-text {
        margin-bottom: 8px;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-secondary);
      }

      .empty-desc {
        font-size: 14px;
        color: var(--text-muted);
      }
    }

    .history-list {
      height: 100%;
      padding: 8px 0;
      overflow-y: auto;

      .date-group {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 8px;
        }

        .date-header {
          padding: 8px 20px;
          font-size: 12px;
          font-weight: 600;
          color: var(--text-secondary);
          background-color: #f8f9fa;
          border-bottom: 1px solid #e5e7eb;
        }

        .session-group {
          background-color: var(--bg-primary);
        }
      }
    }
  }
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s;

  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: var(--primary-bg);
    transform: scale(0.98);
  }

  &.active {
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--primary-bg-light) 100%);
    border-left: 4px solid var(--primary-dark);
  }

  &:last-child {
    border-bottom: none;
  }

  .session-main {
    flex: 1;
    overflow: hidden;

    .session-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 8px;

      .session-title {
        flex: 1;
        margin-right: 12px;
        overflow: hidden;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .session-time {
        flex-shrink: 0;
        font-size: 12px;
        color: var(--text-muted);
      }
    }

    .session-preview {
      margin-bottom: 8px;

      .preview-text {
        display: -webkit-box;
        overflow: hidden;
        font-size: 14px;
        line-height: 1.4;
        color: var(--text-secondary);
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .session-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .message-count,
      .agent-info {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--text-muted);
      }

      .agent-dot {
        width: 8px;
        height: 8px;
        margin-right: 6px;
        border-radius: var(--radius-full);
      }
    }
  }

  .session-actions {
    display: flex;
    align-items: center;
    margin-left: 12px;

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: var(--text-muted);
      border-radius: var(--radius-full);
      transition: all 0.3s;

      &:hover {
        color: var(--danger-darker);
        background-color: #fff5f5;
      }

      &:active {
        transform: scale(0.9);
      }
    }
  }
}

// ========================
// 工具类
// ========================
.flex-1 {
  flex: 1;
}

// ========================
// 工具类
// ========================
.flex-1 {
  flex: 1;
}

// ========================
// 响应式设计
// ========================
@media (max-width: 768px) {
  .quick-actions {
    grid-template-columns: 1fr;
  }

  .agents-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .extensions-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }

  .message-text {
    :deep(.hljs) {
      font-size: 12px;

      code {
        padding: 12px;
      }
    }

    :deep(.markdown-table) {
      display: block;
      overflow-x: auto;
      font-size: 12px;
      white-space: nowrap;

      th,
      td {
        padding: 6px 8px;
      }
    }
  }

  // 移动端固定定位优化
  .nav-bar {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .input-area {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

// ========================
// 设备兼容性优化
// ========================
// iOS Safari 兼容性
@supports (-webkit-touch-callout: none) {

  .nav-bar,
  .input-area {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

// 针对小屏幕设备的额外优化
@media (max-height: 600px) {
  .message-spacer {
    height: 140rpx;
  }

  .chat-content {
    padding-bottom: 160rpx;
  }

  .input-area {
    min-height: 70rpx;
    padding-top: 12rpx;
  }

  .input-container {
    gap: 8px;
    min-height: 50rpx;
    padding: 8px 0px 12px;
  }

  .input-field {
    height: 40px !important;
    min-height: 40px !important;
    padding: 8px 14px !important;
    font-size: 15px;
    line-height: 1.4;
  }

  .action-btn {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    height: 40px !important;
    min-height: 40px !important;
    max-height: 40px !important;
  }
}

// 针对超小屏幕设备的优化
@media (max-height: 500px) {
  .input-area {
    min-height: 60rpx;
    padding-top: 8rpx;
  }

  .input-container {
    gap: 6px;
    padding: 6px 0px 10px;
  }

  .input-field {
    height: 36px !important;
    min-height: 36px !important;
    padding: 6px 12px !important;
    font-size: 14px;
    line-height: 1.4;
  }

  .action-btn {
    width: 36px !important;
    min-width: 36px !important;
    max-width: 36px !important;
    height: 36px !important;
    min-height: 36px !important;
    max-height: 36px !important;
  }
}

// ========================
// 动画定义
// ========================
@keyframes floating {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(45deg);
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateX(100%) rotate(45deg);
  }
}

// 脉冲发光动画
@keyframes pulse-glow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }

  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }

  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

// 脉冲涟漪动画
@keyframes pulse-ripple {
  0% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.5);
  }

  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.2);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 101, 101, 0.7);
    transform: scale(1);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(245, 101, 101, 0);
    transform: scale(1.05);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(245, 101, 101, 0);
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes typing {

  0%,
  60%,
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }

  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes recording-pulse {
  0% {
    box-shadow:
      0 8px 24px rgba(255, 107, 107, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
  }

  50% {
    box-shadow:
      0 12px 32px rgba(255, 107, 107, 0.6),
      0 6px 12px rgba(0, 0, 0, 0.15);
    transform: scale(1.04);
  }

  100% {
    box-shadow:
      0 8px 24px rgba(255, 107, 107, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
  }
}

@keyframes voice-bounce {
  0% {
    transform: scale(1) rotate(0deg);
  }

  50% {
    transform: scale(1.2) rotate(5deg);
  }

  100% {
    transform: scale(1.1) rotate(-5deg);
  }
}

@keyframes input-focus {
  0% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-2px) scale(1.01);
  }

  100% {
    transform: translateY(-1px) scale(1);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes button-float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-2px);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes cursor-blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

@keyframes slideDown {
  0% {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
  }

  100% {
    max-height: 500rpx;
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    opacity: 1;
  }
}
/* Markdown 内容样式 */
.markdown-content {
  /* 标题样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 24rpx;
    margin-bottom: 16rpx;
    font-weight: 600;
    line-height: 1.25;
  }

  h1 {
    font-size: 32rpx;
  }

  h2 {
    font-size: 28rpx;
  }

  h3 {
    font-size: 26rpx;
  }

  h4 {
    font-size: 24rpx;
  }

  h5 {
    font-size: 22rpx;
  }

  h6 {
    font-size: 20rpx;
  }
  /* 段落样式 */
  p {
    margin-bottom: 16rpx;
    line-height: 1.6;
  }
  /* 强调和粗体 */
  strong,
  b {
    font-weight: 600;
  }

  em,
  i {
    font-style: italic;
  }
  /* 代码样式 */
  code {
    padding: 2rpx 6rpx;
    font-family: 'SF Mono', Monaco, 'Inconsolata', 'Roboto Mono', 'Source Code Pro', Menlo, Consolas,
      'DejaVu Sans Mono', monospace;
    font-size: 85%;
    background-color: rgba(175, 184, 193, 0.2);
    border-radius: 6rpx;
  }

  pre {
    padding: 16rpx;
    margin: 16rpx 0;
    overflow-x: auto;
    background-color: #f6f8fa;
    border-radius: 12rpx;

    code {
      padding: 0;
      font-size: 26rpx;
      color: #24292e;
      background-color: transparent;
      border-radius: 0;
    }
  }
  /* 引用样式 */
  blockquote {
    padding: 12rpx 16rpx;
    padding-left: 16rpx;
    margin: 16rpx 0;
    color: #6a737d;
    background-color: rgba(0, 201, 167, 0.05);
    border-left: 4rpx solid #00c9a7;
    border-radius: 8rpx;
  }
  /* 列表样式 */
  ul,
  ol {
    padding-left: 32rpx;
    margin: 16rpx 0;
  }

  li {
    margin-bottom: 8rpx;
    line-height: 1.6;
  }

  ul li {
    list-style-type: disc;
  }

  ol li {
    list-style-type: decimal;
  }
  /* 链接样式 */
  a {
    color: #00c9a7;
    text-decoration: underline;
  }
  /* 表格样式 */
  table {
    width: 100%;
    margin: 16rpx 0;
    border-collapse: collapse;
  }

  th,
  td {
    padding: 12rpx 16rpx;
    text-align: left;
    border: 1rpx solid #d0d7de;
  }

  th {
    font-weight: 600;
    background-color: #f6f8fa;
  }
  /* 分割线 */
  hr {
    height: 2rpx;
    margin: 24rpx 0;
    background-color: #d0d7de;
    border: none;
  }
  /* 代码高亮样式 */
  .hljs {
    color: #24292e;
    background: #f6f8fa;
    border-radius: 8rpx;
  }

  .hljs-comment,
  .hljs-quote {
    font-style: italic;
    color: #6a737d;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-subst {
    color: #d73a49;
  }

  .hljs-number,
  .hljs-literal,
  .hljs-variable,
  .hljs-template-variable,
  .hljs-tag .hljs-attr {
    color: #005cc5;
  }

  .hljs-string,
  .hljs-doctag {
    color: #032f62;
  }

  .hljs-title,
  .hljs-section,
  .hljs-selector-id {
    font-weight: 600;
    color: #6f42c1;
  }

  .hljs-subst {
    font-weight: normal;
  }

  .hljs-type,
  .hljs-class .hljs-title,
  .hljs-tag,
  .hljs-regexp,
  .hljs-symbol,
  .hljs-bullet,
  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-meta {
    color: #e36209;
  }

  .hljs-deletion {
    background: #ffeef0;
  }

  .hljs-addition {
    background: #f0fff4;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: bold;
  }
}

// ========================
// 搜索高亮样式
// ========================
.search-highlight {
  padding: 2px 4px;
  font-weight: 500;
  color: #856404;
  background-color: #fff3cd;
  border-radius: 3px;
}

// ========================
// 动画定义
// ========================
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
