<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

// 隐私与安全页
// 用于设置数据隐私和安全相关内容

// 隐私设置项
const privacySettings = ref([
  {
    id: 'data-collection',
    title: '数据收集',
    desc: '允许系统收集使用数据以改进服务',
    checked: true,
  },
  {
    id: 'interview-record',
    title: '面试记录',
    desc: '允许保存面试视频和语音记录',
    checked: true,
  },
  {
    id: 'recommendation',
    title: '个性化推荐',
    desc: '根据您的学习情况提供个性化推荐',
    checked: true,
  },
  {
    id: 'third-party',
    title: '第三方分享',
    desc: '与第三方服务共享您的学习数据',
    checked: false,
  },
])

// 安全设置项
const securitySettings = ref([
  {
    id: 'password',
    title: '修改密码',
    desc: '定期更改密码以保护账号安全',
    type: 'link',
    url: '/pages/auth/reset-password',
  },
  {
    id: 'two-factor',
    title: '两步验证',
    desc: '启用两步验证提高账号安全性',
    type: 'switch',
    checked: false,
  },
  {
    id: 'login-record',
    title: '登录记录',
    desc: '查看您的账号登录历史记录',
    type: 'link',
    url: '/pages/user/login-record',
  },
])

// 处理隐私设置变更
const onPrivacyChange = (idx: number, e: any) => {
  privacySettings.value[idx].checked = e.detail.value
  // 保存到本地存储
  saveSettings()
}

// 处理安全设置变更
const onSecurityChange = (idx: number, e: any) => {
  if (securitySettings.value[idx].type === 'switch') {
    securitySettings.value[idx].checked = e.detail.value
    // 保存到本地存储
    saveSettings()
  }
}

// 跳转链接
const goToPage = (url: string) => {
  if (url) {
    uni.navigateTo({ url })
  }
}

// 保存设置
const saveSettings = () => {
  uni.setStorageSync('privacySettings', JSON.stringify(privacySettings.value))
  uni.setStorageSync('securitySettings', JSON.stringify(securitySettings.value))
}

// 清除所有数据
const clearAllData = () => {
  uni.showModal({
    title: '清除数据',
    content: '确定要清除所有个人数据吗？此操作不可恢复。',
    confirmColor: '#ff4d4f',
    success: (res) => {
      if (res.confirm) {
        // 实际项目中这里应该调用API清除用户数据
        uni.showLoading({ title: '正在清除...' })

        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '数据已清除',
            icon: 'success',
          })
        }, 1500)
      }
    },
  })
}

// 初始化从本地存储加载设置
const initSettings = () => {
  const storedPrivacy = uni.getStorageSync('privacySettings')
  const storedSecurity = uni.getStorageSync('securitySettings')

  if (storedPrivacy) {
    privacySettings.value = JSON.parse(storedPrivacy)
  }

  if (storedSecurity) {
    securitySettings.value = JSON.parse(storedSecurity)
  }
}

// 打开隐私政策
const openPrivacyPolicy = (): void => {
  uni.navigateTo({
    url: '/pages/user/privacy-policy',
  })
}
// 页面加载时初始化设置
initSettings()
</script>
<template>
  <view class="privacy-page">
    <HeadBar title="隐私与安全" :show-back="true" :show-right-button="false" />
    <view class="privacy-content">
      <!-- 隐私设置 -->
      <view class="settings-section">
        <view class="settings-card">
          <view class="setting-item" v-for="(item, idx) in privacySettings" :key="item.id">
            <view class="setting-info">
              <text class="setting-title">{{ item.title }}</text>
              <text class="setting-desc">{{ item.desc }}</text>
            </view>
            <switch
              :checked="item.checked"
              color="#00B39A"
              @change="(e) => onPrivacyChange(idx, e)"
            />
          </view>
        </view>
      </view>

      <!-- 安全设置 -->
      <view class="settings-section">
        <view class="section-title">安全设置</view>
        <view class="settings-card">
          <view class="setting-item" v-for="(item, idx) in securitySettings" :key="item.id">
            <view class="setting-info">
              <text class="setting-title">{{ item.title }}</text>
              <text class="setting-desc">{{ item.desc }}</text>
            </view>

            <template v-if="item.type === 'switch'">
              <switch
                :checked="item.checked"
                color="#00B39A"
                @change="(e) => onSecurityChange(idx, e)"
              />
            </template>

            <template v-else-if="item.type === 'link'">
              <text
                class="i-fa-solid-chevron-right"
                style="font-size: 22rpx; color: #ccc"
                @click="goToPage(item.url)"
              ></text>
            </template>
          </view>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="settings-section">
        <view class="section-title">数据管理</view>
        <view class="settings-card">
          <view class="setting-item data-export">
            <view class="setting-info">
              <text class="setting-title">导出个人数据</text>
              <text class="setting-desc">导出您的所有学习记录和个人信息</text>
            </view>
            <view class="action-btn export-btn">
              <text class="i-fa-solid-download" style="margin-right: 8rpx; font-size: 22rpx"></text>
              <text>导出</text>
            </view>
          </view>

          <view class="setting-item data-clear" @click="clearAllData">
            <view class="setting-info">
              <text class="setting-title danger-text">清除所有数据</text>
              <text class="setting-desc">删除您的所有个人数据（不可恢复）</text>
            </view>
            <view class="action-btn clear-btn">
              <text
                class="i-fa-solid-trash-alt"
                style="margin-right: 8rpx; font-size: 22rpx"
              ></text>
              <text>清除</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 说明文本 -->
      <view class="privacy-info">
        <text>我们重视您的隐私保护。欲了解更多信息，请查看我们的</text>
        <text class="link-text" @click="openPrivacyPolicy">《隐私政策》</text>
      </view>
    </view>
  </view>
</template>
<style scoped lang="scss">
.privacy-page {
  min-height: 100vh;
  background: #f5f5f5;
}
.privacy-content {
  padding: 40rpx;
}
.page-title {
  margin-bottom: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
}
.settings-section {
  margin-bottom: 40rpx;
}
.section-title {
  position: relative;
  padding-left: 16rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #222;

  &::before {
    position: absolute;
    top: 6rpx;
    left: 0;
    width: 8rpx;
    height: 28rpx;
    content: '';
    background: #00b39a;
    border-radius: 4rpx;
  }
}
.settings-card {
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}
.setting-info {
  flex: 1;
}
.setting-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #222;
}
.setting-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
}
.danger-text {
  color: #ff4d4f;
}
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 30rpx;
}
.export-btn {
  color: #00b39a;
  background: #e6f7f5;
}
.clear-btn {
  color: #ff4d4f;
  background: #fff2f0;
}
.privacy-info {
  margin-top: 48rpx;
  font-size: 22rpx;
  line-height: 1.5;
  color: #999;
  text-align: center;
}
.link-text {
  color: #00b39a;
  text-decoration: underline;
}
</style>
