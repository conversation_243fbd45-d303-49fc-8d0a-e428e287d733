<script setup lang="ts">
/**
 * @description 通用加载组件
 * @component Loading
 */

// 定义组件的 props 类型
interface LoadingProps {
  /** 是否显示加载状态 */
  visible?: boolean
  /** 加载文本 */
  text?: string
  /** 加载器大小 */
  size?: 'small' | 'medium' | 'large'
  /** 加载器颜色 */
  color?: string
  /** 是否显示文本 */
  showText?: boolean
  /** 加载器类型 */
  type?: 'spinner' | 'dots' | 'pulse'
  /** 容器内边距 */
  padding?: string
  /** 是否使用遮罩背景 */
  overlay?: boolean
  /** 遮罩背景色 */
  overlayColor?: string
}

// 定义默认值
const props = withDefaults(defineProps<LoadingProps>(), {
  visible: true,
  text: '加载中...',
  size: 'medium',
  color: '#00c9a7',
  showText: true,
  type: 'spinner',
  padding: '100rpx 0',
  overlay: false,
  overlayColor: 'rgba(255, 255, 255, 0.8)',
})

/**
 * @description 获取加载器尺寸样式
 * @returns 尺寸样式对象
 */
const getSizeStyle = () => {
  const sizeMap = {
    small: { width: '40rpx', height: '40rpx', fontSize: '24rpx' },
    medium: { width: '60rpx', height: '60rpx', fontSize: '28rpx' },
    large: { width: '80rpx', height: '80rpx', fontSize: '32rpx' },
  }
  return sizeMap[props.size]
}

/**
 * @description 获取加载器颜色样式
 * @returns 颜色样式字符串
 */
const getColorStyle = () => {
  return {
    borderTopColor: props.color,
    backgroundColor: props.color,
  }
}

/**
 * @description 获取容器样式
 * @returns 容器样式对象
 */
const getContainerStyle = () => {
  const baseStyle = {
    padding: props.padding,
  }

  if (props.overlay) {
    return {
      ...baseStyle,
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      backgroundColor: props.overlayColor,
      zIndex: '9999',
    }
  }

  return baseStyle
}
</script>

<template>
  <view v-if="visible" class="loading-container" :style="getContainerStyle()">
    <!-- 旋转加载器 -->
    <view v-if="type === 'spinner'" class="loading-content">
      <view class="loading-spinner" :style="{ ...getSizeStyle(), ...getColorStyle() }"></view>
      <text v-if="showText" class="loading-text" :style="{ fontSize: getSizeStyle().fontSize }">
        {{ text }}
      </text>
    </view>

    <!-- 点状加载器 -->
    <view v-else-if="type === 'dots'" class="loading-content">
      <view class="loading-dots">
        <view
          v-for="i in 3"
          :key="i"
          class="dot"
          :style="{
            ...getSizeStyle(),
            backgroundColor: color,
            animationDelay: `${(i - 1) * 0.2}s`,
          }"
        ></view>
      </view>
      <text v-if="showText" class="loading-text" :style="{ fontSize: getSizeStyle().fontSize }">
        {{ text }}
      </text>
    </view>

    <!-- 脉冲加载器 -->
    <view v-else-if="type === 'pulse'" class="loading-content">
      <view class="loading-pulse" :style="{ ...getSizeStyle(), backgroundColor: color }"></view>
      <text v-if="showText" class="loading-text" :style="{ fontSize: getSizeStyle().fontSize }">
        {{ text }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 旋转加载器样式 */
.loading-spinner {
  border: 4rpx solid #f3f3f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 点状加载器样式 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.dot {
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 脉冲加载器样式 */
.loading-pulse {
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 加载文本样式 */
.loading-text {
  color: #7f8c8d;
  text-align: center;
  line-height: 1.5;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .loading-container {
    padding: 60rpx 0;
  }

  .loading-text {
    font-size: 24rpx !important;
  }
}
</style>
