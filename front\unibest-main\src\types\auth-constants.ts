/**
 * 认证模块常量定义
 */

import type { MajorOption, GradeOption, ClientConfig } from './auth'

// 默认客户端配置
export const DEFAULT_CLIENT_CONFIG: ClientConfig = {
  clientId: 'app',
  grantType: 'phone',
}

// API接口路径常量
export const AUTH_API_ENDPOINTS = {
  // 验证码相关
  SEND_SMS_CODE: '/app/auth/send-sms-code',
  SEND_EMAIL_CODE: '/app/auth/send-email-code',

  // 登录相关
  LOGIN_BY_PHONE: '/app/auth/login/phone',
  LOGIN_BY_PASSWORD: '/app/auth/login/password',
  THIRD_PARTY_LOGIN: '/app/auth/third-party-login',

  // 注册相关
  REGISTER: '/app/auth/register',
  CHECK_PHONE_EXISTS: '/app/auth/check-phone',
  CHECK_EMAIL_EXISTS: '/app/auth/check-email',

  // 密码相关
  RESET_PASSWORD: '/app/auth/reset-password',
  CHANGE_PASSWORD: '/app/auth/change-password',

  // Token相关
  REFRESH_TOKEN: '/app/auth/refresh-token',
  LOGOUT: '/app/auth/logout',

  // 用户信息相关
  CURRENT_USER: '/app/auth/current-user',
  ONLINE_STATUS: '/app/auth/online-status',
} as const

// 专业选项列表
export const MAJOR_OPTIONS: MajorOption[] = [
  { value: '', text: '请选择专业' },
  { value: '计算机科学与技术', text: '计算机科学与技术' },
  { value: '软件工程', text: '软件工程' },
  { value: '人工智能', text: '人工智能' },
  { value: '数据科学与大数据技术', text: '数据科学与大数据技术' },
  { value: '物联网工程', text: '物联网工程' },
  { value: '信息系统', text: '信息系统' },
  { value: '网络工程', text: '网络工程' },
  { value: '信息安全', text: '信息安全' },
  { value: '其他', text: '其他' },
]

// 年级选项列表
export const GRADE_OPTIONS: GradeOption[] = ['大一', '大二', '大三', '大四']

// 验证规则常量
export const VALIDATION_PATTERNS = {
  // 手机号正则
  PHONE: /^1[3-9]\d{9}$/,
  // 邮箱正则
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  // 验证码正则（6位数字）
  SMS_CODE: /^\d{6}$/,
  // 密码强度正则（至少8位，包含字母和数字）
  PASSWORD_STRONG: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/,
  // 学号正则（根据实际需求调整）
  STUDENT_ID: /^\d{8,12}$/,
} as const

// 验证消息常量
export const VALIDATION_MESSAGES = {
  PHONE: {
    REQUIRED: '请输入手机号码',
    INVALID: '请输入正确的手机号码格式',
  },
  EMAIL: {
    REQUIRED: '请输入邮箱地址',
    INVALID: '请输入正确的邮箱格式',
    EXISTS: '该邮箱已被注册',
  },
  CODE: {
    REQUIRED: '请输入验证码',
    INVALID: '请输入6位数字验证码',
  },
  PASSWORD: {
    REQUIRED: '请输入密码',
    TOO_SHORT: '密码长度不能少于6位',
    TOO_LONG: '密码长度不能超过20位',
    WEAK: '密码至少包含字母和数字，长度8位以上',
    MISMATCH: '两次输入的密码不一致',
  },
  REAL_NAME: {
    REQUIRED: '请输入真实姓名',
    INVALID: '请输入正确的姓名格式',
  },
  STUDENT_ID: {
    REQUIRED: '请输入学号',
    INVALID: '请输入正确的学号格式',
  },
  MAJOR: {
    REQUIRED: '请选择专业',
  },
  GRADE: {
    REQUIRED: '请选择年级',
  },
  AGREEMENT: {
    REQUIRED: '请先同意用户协议和隐私政策',
  },
} as const

// 通知消息常量
export const NOTIFICATION_MESSAGES = {
  SUCCESS: {
    LOGIN: '登录成功',
    REGISTER: '注册成功',
    SEND_SMS_CODE: '验证码已发送到您的手机',
    SEND_EMAIL_CODE: '验证码已发送到您的邮箱',
    RESET_PASSWORD: '密码重置成功',
    CHANGE_PASSWORD: '密码修改成功',
    LOGOUT: '已安全退出',
  },
  ERROR: {
    LOGIN_FAILED: '登录失败，请检查输入信息',
    REGISTER_FAILED: '注册失败，请稍后重试',
    SEND_CODE_FAILED: '验证码发送失败，请稍后重试',
    RESET_PASSWORD_FAILED: '密码重置失败，请稍后重试',
    NETWORK_ERROR: '网络连接失败，请检查网络',
    SERVER_ERROR: '服务器错误，请稍后重试',
    TOKEN_EXPIRED: '登录已过期，请重新登录',
  },
  INFO: {
    DEV_CODE: '开发模式：验证码为 123456',
    TOKEN_EXPIRE_SOON: 'Token即将过期，请重新登录',
  },
} as const

// 存储键名常量
export const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  TOKEN: 'token',
  IS_LOGGED_IN: 'isLoggedIn',
  LOGIN_TIME: 'loginTime',
} as const

// 页面路由常量
export const ROUTE_PATHS = {
  LOGIN: '/pages/auth/login',
  REGISTER: '/pages/auth/register',
  FORGET_PASSWORD: '/pages/auth/forgetpassword',
  HOME: '/pages/index/index',
  PROFILE: '/pages/profile/index',
} as const

// 时间相关常量
export const TIME_CONSTANTS = {
  // 验证码倒计时秒数
  CODE_COUNTDOWN: 60,
  // 通知显示时长（毫秒）
  NOTIFICATION_DURATION: 3000,
  // 错误消息自动清除时长（毫秒）
  ERROR_CLEAR_DURATION: 3000,
  // Token过期时间（小时）
  TOKEN_EXPIRE_HOURS: 2,
} as const

// 密码强度配置
export const PASSWORD_STRENGTH_CONFIG = {
  WEAK: {
    text: '弱',
    class: 'strength-weak',
    color: '#ff4d4f',
  },
  MEDIUM: {
    text: '中',
    class: 'strength-medium',
    color: '#faad14',
  },
  STRONG: {
    text: '强',
    class: 'strength-strong',
    color: '#52c41a',
  },
} as const

// 默认头像路径
export const DEFAULT_AVATAR = '/static/images/default-avatar.png'

// 开发环境默认值
export const DEV_DEFAULTS = {
  PHONE: '13333333333',
  SMS_CODE: '123456',
  PASSWORD: '123456',
  EMAIL: '<EMAIL>',
} as const
