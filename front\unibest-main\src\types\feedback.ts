/**
 * 意见反馈模块类型定义
 */

// 基础响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 反馈类型枚举
export enum FeedbackType {
  FEATURE_SUGGESTION = '功能建议',
  CONTENT_ISSUE = '内容问题',
  USAGE_PROBLEM = '使用问题',
  OTHER = '其他',
}

// 反馈状态枚举
export enum FeedbackStatus {
  PENDING = 'PENDING', // 待处理
  PROCESSING = 'PROCESSING', // 处理中
  RESOLVED = 'RESOLVED', // 已解决
  CLOSED = 'CLOSED', // 已关闭
}

// 反馈提交参数
export interface SubmitFeedbackParams {
  /** 反馈类型 */
  type: string
  /** 反馈内容 */
  content: string
  /** 联系方式（可选） */
  contactInfo?: string
  /** 设备信息 */
  deviceInfo?: string
  /** 应用版本 */
  appVersion?: string
  /** 平台信息 */
  platform?: string
}

// 反馈详情
export interface FeedbackDetail {
  /** 反馈ID */
  id: string
  /** 反馈类型 */
  type: string
  /** 反馈内容 */
  content: string
  /** 联系方式 */
  contactInfo?: string
  /** 设备信息 */
  deviceInfo?: string
  /** 应用版本 */
  appVersion?: string
  /** 平台信息 */
  platform?: string
  /** 状态 */
  status: FeedbackStatus
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime?: string
  /** 处理回复 */
  reply?: string
  /** 处理人 */
  handler?: string
}

// 反馈列表查询参数
export interface FeedbackListParams {
  /** 页码 */
  pageNum?: number
  /** 页大小 */
  pageSize?: number
  /** 反馈类型 */
  type?: string
  /** 状态 */
  status?: FeedbackStatus
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

// 反馈列表响应
export interface FeedbackListResponse {
  /** 总记录数 */
  total: number
  /** 当前页数据 */
  rows: FeedbackDetail[]
}

// 反馈统计数据
export interface FeedbackStats {
  /** 总反馈数 */
  totalCount: number
  /** 待处理数 */
  pendingCount: number
  /** 处理中数 */
  processingCount: number
  /** 已解决数 */
  resolvedCount: number
  /** 各类型统计 */
  typeStats: {
    [key: string]: number
  }
}
