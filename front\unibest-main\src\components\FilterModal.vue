<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'

// 定义筛选项类型
interface FilterOption {
  key: string
  label: string
  value?: any
}

// 定义筛选组类型
interface FilterSection {
  type: 'select' | 'toggle' | 'multi-select'
  key: string
  title: string
  options?: FilterOption[]
  toggleText?: string
  defaultValue?: any
}

// 定义组件属性
interface Props {
  visible: boolean
  title?: string
  filterSections: FilterSection[]
  modelValue: Record<string, any>
}

// 定义事件
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'confirm', value: Record<string, any>): void
  (e: 'reset'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '筛选条件',
})

const emit = defineEmits<Emits>()

// 内部筛选值状态
const internalFilters = ref<Record<string, any>>({ ...props.modelValue })

/**
 * @description 更新筛选值
 * @param key 筛选项键名
 * @param value 筛选值
 */
const updateFilter = (key: string, value: any): void => {
  internalFilters.value[key] = value
}

/**
 * @description 切换布尔值筛选
 * @param key 筛选项键名
 */
const toggleFilter = (key: string): void => {
  internalFilters.value[key] = !internalFilters.value[key]
}

/**
 * @description 关闭弹框
 */
const closeModal = (): void => {
  emit('update:visible', false)
  emit('close')
}

/**
 * @description 确认筛选
 */
const confirmFilter = (): void => {
  emit('update:modelValue', { ...internalFilters.value })
  emit('confirm', { ...internalFilters.value })
  closeModal()
}

/**
 * @description 重置筛选
 */
const resetFilter = (): void => {
  const resetValues: Record<string, any> = {}

  props.filterSections.forEach((section: FilterSection) => {
    if (section.defaultValue !== undefined) {
      resetValues[section.key] = section.defaultValue
    } else {
      switch (section.type) {
        case 'select':
          resetValues[section.key] = section.options?.[0]?.key || ''
          break
        case 'toggle':
          resetValues[section.key] = false
          break
        case 'multi-select':
          resetValues[section.key] = []
          break
      }
    }
  })

  internalFilters.value = resetValues
  emit('update:modelValue', resetValues)
  emit('reset')
}

/**
 * @description 处理多选
 * @param key 筛选项键名
 * @param optionKey 选项键名
 */
const toggleMultiSelect = (key: string, optionKey: string): void => {
  if (!internalFilters.value[key]) {
    internalFilters.value[key] = []
  }

  const index = internalFilters.value[key].indexOf(optionKey)
  if (index > -1) {
    internalFilters.value[key].splice(index, 1)
  } else {
    internalFilters.value[key].push(optionKey)
  }
}

/**
 * @description 检查多选项是否被选中
 * @param key 筛选项键名
 * @param optionKey 选项键名
 */
const isMultiSelectActive = (key: string, optionKey: string): boolean => {
  return internalFilters.value[key]?.includes(optionKey) || false
}

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue: Record<string, any>) => {
    internalFilters.value = { ...newValue }
  },
  { deep: true },
)

// 监听弹框显示状态
watch(
  () => props.visible,
  (newVisible: boolean) => {
    if (newVisible) {
      internalFilters.value = { ...props.modelValue }
    }
  },
)
</script>

<template>
  <view v-if="visible" class="filter-modal-overlay" @click="closeModal">
    <view class="filter-modal" @click.stop>
      <view class="modal-header">
        <text class="modal-title">{{ title }}</text>
        <button class="modal-close" @click="closeModal">
          <view class="i-mdi-close close-icon"></view>
        </button>
      </view>

      <view class="modal-content">
        <view v-for="section in filterSections" :key="section.key" class="filter-section">
          <text class="section-title">{{ section.title }}</text>

          <!-- 单选筛选 -->
          <view v-if="section.type === 'select'" class="filter-options">
            <button
              v-for="option in section.options"
              :key="option.key"
              class="filter-option"
              :class="internalFilters[section.key] === option.key ? 'active' : ''"
              @click="updateFilter(section.key, option.key)"
            >
              {{ option.label }}
            </button>
          </view>

          <!-- 开关筛选 -->
          <button
            v-else-if="section.type === 'toggle'"
            class="filter-toggle"
            @click="toggleFilter(section.key)"
          >
            <view
              :class="
                internalFilters[section.key]
                  ? 'i-mdi-checkbox-marked'
                  : 'i-mdi-checkbox-blank-outline'
              "
              class="checkbox-icon"
            ></view>
            <text class="toggle-text">{{ section.toggleText || '启用此选项' }}</text>
          </button>

          <!-- 多选筛选 -->
          <view v-else-if="section.type === 'multi-select'" class="filter-options">
            <button
              v-for="option in section.options"
              :key="option.key"
              class="filter-option"
              :class="isMultiSelectActive(section.key, option.key) ? 'active' : ''"
              @click="toggleMultiSelect(section.key, option.key)"
            >
              {{ option.label }}
            </button>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="reset-btn" @click="resetFilter">
          <text class="btn-text">重置</text>
        </button>
        <button class="confirm-btn" @click="confirmFilter">
          <text class="btn-text">确定</text>
        </button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.filter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;

  .filter-modal {
    width: 100%;
    max-width: 750rpx;
    background: white;
    border-radius: 32rpx 32rpx 0 0;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #e2e8f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1e293b;
      }

      .modal-close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        background: #f1f5f9;
        border-radius: 50%;
        border: none;
        transition: all 0.3s ease;

        &:hover {
          background: #e2e8f0;
        }

        .close-icon {
          font-size: 24rpx;
          color: #64748b;
        }
      }
    }

    .modal-content {
      padding: 32rpx;
      max-height: 60vh;
      overflow-y: auto;

      .filter-section {
        margin-bottom: 40rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          display: block;
          margin-bottom: 20rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
        }

        .filter-options {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .filter-option {
            padding: 12rpx 24rpx;
            background: #f1f5f9;
            border-radius: 20rpx;
            font-size: 24rpx;
            color: #475569;
            border: 2rpx solid transparent;
            transition: all 0.3s ease;

            &.active {
              background: #00c9a7;
              color: white;
              border-color: #00c9a7;
            }

            &:hover:not(.active) {
              border-color: #00c9a7;
              color: #00c9a7;
            }
          }
        }

        .filter-toggle {
          display: flex;
          align-items: center;
          gap: 16rpx;
          padding: 16rpx 24rpx;
          background: #f8fafc;
          border-radius: 16rpx;
          font-size: 26rpx;
          color: #475569;
          border: none;
          transition: all 0.3s ease;

          &:hover {
            background: #f1f5f9;
          }

          .checkbox-icon {
            font-size: 28rpx;
            color: #94a3b8;
            transition: color 0.3s ease;

            &.i-mdi-checkbox-marked {
              color: #00c9a7;
            }
          }

          .toggle-text {
            font-size: 26rpx;
            font-weight: 500;
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 20rpx;
      padding: 24rpx 32rpx 32rpx;
      border-top: 1rpx solid #e2e8f0;

      .reset-btn {
        flex: 1;
        padding: 20rpx;
        background: #f8fafc;
        border: 2rpx solid #e2e8f0;
        border-radius: 16rpx;
        font-size: 26rpx;
        color: #64748b;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          border-color: #cbd5e1;
        }

        .btn-text {
          font-weight: 500;
        }
      }

      .confirm-btn {
        flex: 2;
        padding: 20rpx;
        background: #00c9a7;
        border: 2rpx solid #00c9a7;
        border-radius: 16rpx;
        font-size: 26rpx;
        color: white;
        transition: all 0.3s ease;

        &:hover {
          background: #00b894;
          border-color: #00b894;
        }

        .btn-text {
          font-weight: 500;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
