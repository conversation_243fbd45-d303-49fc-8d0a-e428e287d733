<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed, defineProps, onUnmounted } from 'vue'
// #ifdef H5
import * as echarts from 'echarts'
// #endif

/**
 * @description 日历热力图组件
 * @prop year 显示的年份
 * @prop data 热力图数据数组，格式为 [['2023-01-01', 100], ['2023-01-02', 200]]
 * @prop width 图表宽度
 * @prop height 图表高度
 * @prop maxValue 数据最大值，用于颜色映射
 * <AUTHOR>
 */
interface HeatmapDataItem {
  date: string
  value: number
}

interface Props {
  year: number
  data?: Array<[string, number]>
  width?: number | string
  height?: number | string
  maxValue?: number
}

// @ts-ignore
const props = withDefaults(defineProps<Props>(), {
  year: new Date().getFullYear(),
  data: () => [],
  width: 800,
  height: 400,
  maxValue: 1000,
})

// 生成唯一的组件ID
const componentId = ref(`calendar-heatmap-${Date.now()}-${Math.floor(Math.random() * 1000)}`)
const chartDomId = ref(`heatmap-chart-${componentId.value}`)
const canvasId = ref(`heatmap-canvas-${componentId.value}`)

// 图表实例
let heatmapChart: any = null
let chartInitialized = ref(false)

// 计算实际的宽高（统一使用px单位）
const actualWidth = computed(() => {
  const w = props.width || 800
  if (typeof w === 'number') {
    return uni.upx2px(w)
  }
  const value = parseInt(w.toString())
  if (w.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || 800
})

const actualHeight = computed(() => {
  const h = props.height || 400
  if (typeof h === 'number') {
    return uni.upx2px(h)
  }
  const value = parseInt(h.toString())
  if (h.toString().includes('rpx')) {
    return uni.upx2px(value)
  }
  return value || 400
})

// 统计数据计算
const statsData = computed(() => {
  const heatmapData = getVirtualData(props.year)
  const totalValue = heatmapData.reduce((sum, [, value]) => sum + value, 0)
  const avgValue = totalValue / heatmapData.length
  const maxDataValue = Math.max(...heatmapData.map(([, value]) => value))

  return {
    totalRecords: heatmapData.length,
    totalValue,
    avgValue: Math.round(avgValue * 100) / 100,
    maxDataValue,
    activeDays: heatmapData.filter(([, value]) => value > 0).length,
  }
})

/**
 * @description 生成虚拟数据
 * @param year 年份
 */
function getVirtualData(year: number): Array<[string, number]> {
  // 如果props有数据，优先使用props数据
  if (props.data && props.data.length > 0) {
    return props.data
  }

  // 生成示例数据
  const date = new Date(year, 0, 1).getTime()
  const end = new Date(year + 1, 0, 1).getTime()
  const dayTime = 3600 * 24 * 1000
  const data: Array<[string, number]> = []

  for (let time = date; time < end; time += dayTime) {
    const dateStr = new Date(time).toISOString().split('T')[0]
    const value = Math.floor(Math.random() * props.maxValue)
    data.push([dateStr, value])
  }
  return data
}

/**
 * @description 初始化H5端的echarts图表
 */
function initEchartsCalendar() {
  // #ifdef H5
  const chartDom = document.getElementById(chartDomId.value)
  if (!chartDom || chartInitialized.value) return

  chartInitialized.value = true
  heatmapChart = echarts.init(chartDom)

  const heatmapData = getVirtualData(props.year)

  const option = {
    title: {
      text: `${props.year}年 活动热力图`,
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 16,
      },
    },
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        return `${params.data[0]}<br/>活动量: ${params.data[1]}`
      },
    },
    visualMap: {
      min: 0,
      max: props.maxValue,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: 20,
      inRange: {
        color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127'],
      },
      text: ['高', '低'],
      textStyle: {
        color: '#666',
      },
    },
    calendar: {
      top: 60,
      left: 30,
      right: 30,
      cellSize: ['auto', 20],
      range: props.year.toString(),
      itemStyle: {
        borderWidth: 0.5,
        borderColor: '#fff',
      },
      yearLabel: {
        show: false,
      },
      monthLabel: {
        nameMap: 'cn',
        fontSize: 12,
        color: '#666',
      },
      dayLabel: {
        firstDay: 1,
        nameMap: ['日', '一', '二', '三', '四', '五', '六'],
        fontSize: 12,
        color: '#666',
      },
    },
    series: [
      {
        type: 'heatmap',
        coordinateSystem: 'calendar',
        data: heatmapData,
        label: {
          show: false,
        },
      },
    ],
  }

  heatmapChart.setOption(option)

  // 响应式调整
  const resizeHandler = () => {
    if (heatmapChart) {
      heatmapChart.resize()
    }
  }
  window.addEventListener('resize', resizeHandler)

  // 组件销毁时清理
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler)
    if (heatmapChart) {
      heatmapChart.dispose()
      heatmapChart = null
    }
  })
  // #endif
}

/**
 * @description 获取当前组件实例（小程序端使用）
 */
function getCurrentInstance() {
  // #ifdef MP-WEIXIN
  return getCurrentPages()[getCurrentPages().length - 1]
  // #endif
}

/**
 * @description 绘制小程序端的Canvas日历热力图
 */
function initCanvasCalendar() {
  // #ifdef MP-WEIXIN
  const context = uni.createCanvasContext(canvasId.value, getCurrentInstance())
  if (!context) {
    console.error('无法创建Canvas上下文')
    return
  }

  const heatmapData = getVirtualData(props.year)

  // Canvas绘制参数
  const cellSize = 15
  const padding = 30
  const weekLabels = ['日', '一', '二', '三', '四', '五', '六']
  const monthLabels = [
    '1月',
    '2月',
    '3月',
    '4月',
    '5月',
    '6月',
    '7月',
    '8月',
    '9月',
    '10月',
    '11月',
    '12月',
  ]

  // 清空画布
  context.clearRect(0, 0, actualWidth.value, actualHeight.value)

  // 设置背景
  context.fillStyle = '#ffffff'
  context.fillRect(0, 0, actualWidth.value, actualHeight.value)

  // 绘制标题
  context.fillStyle = '#333333'
  context.font = '16px Arial'
  context.setTextAlign('center')
  context.fillText(`${props.year}年 活动热力图`, actualWidth.value / 2, 25)

  // 绘制周标签
  context.font = '12px Arial'
  context.fillStyle = '#666666'
  context.setTextAlign('right')
  weekLabels.forEach((label, index) => {
    const y = padding + 40 + index * (cellSize + 1)
    context.fillText(label, padding - 5, y + cellSize / 2 + 3)
  })

  // 绘制月份标签和热力图
  const startDate = new Date(props.year, 0, 1)
  let currentDate = new Date(startDate)
  let weekIndex = 0

  // 创建数据映射
  const dataMap = new Map()
  heatmapData.forEach(([date, value]) => {
    dataMap.set(date, value)
  })

  // 绘制月份标签
  for (let month = 0; month < 12; month++) {
    const monthStart = new Date(props.year, month, 1)
    const monthWeek = Math.floor(
      (monthStart.getTime() - startDate.getTime()) / (7 * 24 * 60 * 60 * 1000),
    )
    const x = padding + 20 + monthWeek * (cellSize + 1)
    context.fillStyle = '#666666'
    context.setTextAlign('left')
    context.fillText(monthLabels[month], x, padding + 25)
  }

  // 绘制热力图格子
  while (currentDate.getFullYear() === props.year) {
    const weekDay = currentDate.getDay()
    const dateStr = currentDate.toISOString().split('T')[0]
    const value = dataMap.get(dateStr) || 0

    // 计算颜色
    const intensity = Math.min(value / props.maxValue, 1)
    const colors = ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
    const colorIndex = Math.floor(intensity * (colors.length - 1))
    context.fillStyle = colors[colorIndex]

    // 计算位置
    const x = padding + 20 + weekIndex * (cellSize + 1)
    const y = padding + 40 + weekDay * (cellSize + 1)

    // 绘制格子
    context.fillRect(x, y, cellSize, cellSize)

    // 绘制边框
    context.strokeStyle = '#ffffff'
    context.lineWidth = 1
    context.strokeRect(x, y, cellSize, cellSize)

    // 下一天
    currentDate.setDate(currentDate.getDate() + 1)
    if (weekDay === 6) {
      weekIndex++
    }
  }

  // 绘制图例
  const legendY = actualHeight.value - 40
  const legendX = actualWidth.value - 150
  context.fillStyle = '#666666'
  context.font = '10px Arial'
  context.setTextAlign('left')
  context.fillText('少', legendX - 20, legendY + 10)

  const legendColors = ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
  legendColors.forEach((color, index) => {
    context.fillStyle = color
    context.fillRect(legendX + index * 12, legendY, 10, 10)
  })

  context.fillStyle = '#666666'
  context.fillText('多', legendX + legendColors.length * 12 + 10, legendY + 10)

  // 执行绘制
  context.draw()
  // #endif
}

/**
 * @description 更新图表数据
 */
function updateChart() {
  // #ifdef H5
  if (heatmapChart) {
    const heatmapData = getVirtualData(props.year)
    heatmapChart.setOption({
      calendar: {
        range: props.year.toString(),
      },
      series: [
        {
          data: heatmapData,
        },
      ],
    })
  }
  // #endif

  // #ifdef MP-WEIXIN
  initCanvasCalendar()
  // #endif
}

/**
 * @description 获取指定日期的数据值
 * @param date 日期字符串 YYYY-MM-DD
 */
function getDateValue(date: string): number {
  const heatmapData = getVirtualData(props.year)
  const dateData = heatmapData.find(([d]) => d === date)
  return dateData ? dateData[1] : 0
}

/**
 * @description 获取指定月份的统计数据
 * @param month 月份 (1-12)
 */
function getMonthStats(month: number) {
  const heatmapData = getVirtualData(props.year)
  const monthData = heatmapData.filter(([date]) => {
    const dateObj = new Date(date)
    return dateObj.getMonth() + 1 === month
  })

  const totalValue = monthData.reduce((sum, [, value]) => sum + value, 0)
  const avgValue = totalValue / monthData.length
  const maxValue = Math.max(...monthData.map(([, value]) => value))
  const activeDays = monthData.filter(([, value]) => value > 0).length

  return {
    totalDays: monthData.length,
    activeDays,
    totalValue,
    avgValue: Math.round(avgValue * 100) / 100,
    maxValue,
  }
}

// 监听props变化
watch(
  () => [props.year, props.data],
  () => {
    updateChart()
  },
  { deep: true },
)

// 页面挂载时初始化
onMounted(() => {
  nextTick(() => {
    // #ifdef H5
    setTimeout(() => {
      initEchartsCalendar()
    }, 300)
    // #endif

    // #ifdef MP-WEIXIN
    setTimeout(() => {
      initCanvasCalendar()
    }, 300)
    // #endif
  })
})

// 组件销毁时清理资源
onUnmounted(() => {
  // #ifdef H5
  if (heatmapChart) {
    heatmapChart.dispose()
    heatmapChart = null
  }
  // #endif
  chartInitialized.value = false
})

// 暴露给父组件的方法
defineExpose({
  updateChart,
  getDateValue,
  getMonthStats,
  statsData,
})
</script>

<template>
  <view class="calendar-heatmap-container">
    <!-- H5端使用echarts -->
    <!-- #ifdef H5 -->
    <view
      :id="chartDomId"
      class="heatmap-chart"
      :style="{ width: actualWidth + 'px', height: actualHeight + 'px' }"
    ></view>
    <!-- #endif -->

    <!-- 小程序端使用Canvas -->
    <!-- #ifdef MP-WEIXIN -->
    <canvas
      :canvas-id="canvasId"
      class="heatmap-canvas"
      :style="{ width: actualWidth + 'px', height: actualHeight + 'px' }"
    ></canvas>
    <!-- #endif -->

    <!-- 统计信息 -->
    <view class="heatmap-stats">
      <view class="stats-item">
        <text class="stats-label">年份</text>
        <text class="stats-value">{{ year }}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">总记录</text>
        <text class="stats-value">{{ statsData.totalRecords }}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">活跃天数</text>
        <text class="stats-value">{{ statsData.activeDays }}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">总活动量</text>
        <text class="stats-value">{{ statsData.totalValue }}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">平均值</text>
        <text class="stats-value">{{ statsData.avgValue }}</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.calendar-heatmap-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.heatmap-chart {
  width: 100%;
  min-height: 400px;
}

.heatmap-canvas {
  width: 100%;
  min-height: 400px;
}

.heatmap-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  flex-wrap: wrap;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 120rpx;
  margin: 8rpx 0;
}

.stats-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.stats-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #00c9a7;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .heatmap-stats {
    flex-direction: column;
    align-items: center;
  }

  .stats-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding: 0 20rpx;
  }

  .stats-label {
    margin-bottom: 0;
    margin-right: 20rpx;
  }
}
</style>
