/**
 * 题目相关API服务
 */
import { httpGet, httpPost } from '@/utils/http'
import type { ApiResponse } from '@/types/learning'
import type {
  QuestionDetail,
  Comment,
  CommentCreate,
  CommentQueryParams,
  CommentListResponse,
  QuestionBookmarkParams,
  QuestionPracticeRecord,
  QuestionStats,
} from '@/types/question'

/**
 * 题目API服务类
 */
export const questionApi = {
  /**
   * 获取题目详情
   * @param questionId 题目ID
   * @returns 题目详情响应
   */
  getDetail(questionId: string): Promise<ApiResponse<QuestionDetail>> {
    return httpGet<QuestionDetail>(`/app/learning/questions/${questionId}`)
  },

  /**
   * 切换题目收藏状态
   * @param params 收藏参数
   * @returns 收藏状态响应
   */
  toggleBookmark(params: QuestionBookmarkParams): Promise<
    ApiResponse<{
      isBookmarked: boolean
      message: string
    }>
  > {
    return httpPost<{
      isBookmarked: boolean
      message: string
    }>(`/app/learning/questions/${params.questionId}/bookmark`, {
      isBookmarked: params.isBookmarked,
    })
  },

  /**
   * 获取题目评论列表
   * @param params 查询参数
   * @returns 评论列表响应
   */
  getComments(params: CommentQueryParams): Promise<ApiResponse<CommentListResponse>> {
    return httpGet<CommentListResponse>(`/app/learning/questions/${params.questionId}/comments`, {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      orderBy: params.orderBy || 'createTime',
      orderDirection: params.orderDirection || 'desc',
    })
  },

  /**
   * 创建评论
   * @param params 评论创建参数
   * @returns 创建结果响应
   */
  createComment(params: CommentCreate): Promise<ApiResponse<Comment>> {
    return httpPost<Comment>(`/app/learning/questions/${params.questionId}/comments`, {
      content: params.content,
      parentId: params.parentId,
    })
  },

  /**
   * 删除评论
   * @param commentId 评论ID
   * @returns 删除结果响应
   */
  deleteComment(commentId: string): Promise<ApiResponse<{ message: string }>> {
    return httpPost<{ message: string }>(`/app/learning/comments/${commentId}/delete`)
  },

  /**
   * 点赞评论
   * @param commentId 评论ID
   * @returns 点赞结果响应
   */
  likeComment(commentId: string): Promise<
    ApiResponse<{
      isLiked: boolean
      likeCount: number
      message?: string
    }>
  > {
    return httpPost<{
      isLiked: boolean
      likeCount: number
      message?: string
    }>(`/app/learning/comments/${commentId}/like`)
  },

  /**
   * 提交题目练习记录
   * @param record 练习记录
   * @returns 提交结果响应
   */
  submitPracticeRecord(record: Omit<QuestionPracticeRecord, 'id' | 'createTime'>): Promise<
    ApiResponse<{
      isCorrect: boolean
      message: string
      analysis?: string
    }>
  > {
    return httpPost<{
      isCorrect: boolean
      message: string
      analysis?: string
    }>(`/app/learning/questions/${record.questionId}/practice`, {
      userAnswer: record.userAnswer,
      timeSpent: record.timeSpent,
    })
  },

  /**
   * 获取题目统计信息
   * @param questionId 题目ID
   * @returns 统计信息响应
   */
  getStats(questionId: string): Promise<ApiResponse<QuestionStats>> {
    return httpGet<QuestionStats>(`/app/learning/questions/${questionId}/stats`)
  },

  /**
   * 获取相关题目推荐
   * @param questionId 题目ID
   * @param limit 推荐数量限制
   * @returns 相关题目列表响应
   */
  getRelatedQuestions(questionId: string, limit = 5): Promise<ApiResponse<QuestionDetail[]>> {
    return httpGet<QuestionDetail[]>(`/app/learning/questions/${questionId}/related`, {
      limit,
    })
  },

  /**
   * 举报题目或评论
   * @param params 举报参数
   * @returns 举报结果响应
   */
  report(params: {
    targetId: string
    targetType: 'question' | 'comment'
    reason: string
    description?: string
  }): Promise<ApiResponse<{ message: string }>> {
    return httpPost<{ message: string }>('/app/learning/report', params)
  },
}
