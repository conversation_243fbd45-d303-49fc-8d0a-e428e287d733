// 聊天API服务
export interface ChatRequest {
  message: string
  sessionId?: string
  attachments?: Array<{
    type: 'image' | 'file'
    url: string
    name: string
  }>
}

export interface ChatResponse {
  success: boolean
  data: {
    message: string
    messageId: string
    sessionId: string
  }
  error?: string
}

// 模拟API响应延迟
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// 生成AI回复内容
const generateAIResponse = (message: string, agentType: string = 'general'): string => {
  // 根据AI助手类型生成不同的回复
  if (message.includes('简历')) {
    return generateResumeAnalysis()
  } else if (message.includes('面试')) {
    return generateInterviewTips()
  } else if (message.includes('技能')) {
    return generateSkillAssessment()
  } else if (message.includes('学习')) {
    return generateLearningPlan()
  }

  // 通用回复
  const responses = [
    '我理解你的问题。让我为你详细解答...',
    '这是一个很好的问题！根据我的分析...',
    '感谢你的提问。基于你的情况，我建议...',
    '让我帮你分析一下这个问题...',
    '根据我的经验，这里有几个关键点需要注意...',
  ]

  return responses[Math.floor(Math.random() * responses.length)]
}

// 生成简历分析结果
const generateResumeAnalysis = (): string => {
  return `
    <div class="analysis-card">
      <div class="analysis-header">
        <i class="i-carbon-chart-pie text-blue-500"></i>
        <span>简历分析报告</span>
      </div>
      <div class="analysis-content">
        <div class="mb-4">
          <strong>整体评分：</strong> 85/100
          <div class="progress-bar">
            <div class="progress-fill" style="width: 85%;"></div>
          </div>
        </div>
        
        <div class="mb-4">
          <strong>优势分析：</strong>
          <ul class="list-disc list-inside mt-2 text-gray-600">
            <li>工作经历描述详细，量化成果突出</li>
            <li>技能列表与目标职位高度匹配</li>
            <li>教育背景优秀，有相关证书加分</li>
          </ul>
        </div>
        
        <div class="mb-4">
          <strong>改进建议：</strong>
          <ul class="list-disc list-inside mt-2 text-gray-600">
            <li>增加项目经验的技术细节描述</li>
            <li>优化个人简介，突出核心竞争力</li>
            <li>调整版式设计，提高视觉吸引力</li>
          </ul>
        </div>
        
        <div class="flex flex-wrap gap-2 mt-4">
          <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">ATS友好度: 92%</span>
          <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">关键词匹配: 18/20</span>
          <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">建议修改: 5处</span>
        </div>
      </div>
    </div>
  `
}

// 生成面试技巧
const generateInterviewTips = (): string => {
  return `基于你的情况，我为你准备了以下面试建议：

**📋 常见问题准备：**
1. 自我介绍 - 控制在2-3分钟，突出与职位相关的经历
2. 为什么选择我们公司 - 展示你对公司的了解和热情  
3. 你的优势和劣势 - 诚实但要积极，劣势要说明改进措施

**💡 面试技巧：**
• 使用STAR法则回答行为问题（情境-任务-行动-结果）
• 准备5-8个反问面试官的问题
• 注意肢体语言，保持眼神交流
• 提前15分钟到达，熟悉环境

**🎯 注意事项：**
• 准备具体的项目案例和数据支撑
• 了解行业趋势和公司最新动态
• 练习模拟面试，提升表达流畅度

需要我帮你进行模拟面试练习吗？`
}

// 生成技能评估
const generateSkillAssessment = (): string => {
  return `
    <div class="analysis-card">
      <div class="analysis-header">
        <i class="i-carbon-certificate-check text-red-500"></i>
        <span>技能评估结果</span>
      </div>
      <div class="analysis-content">
        <div class="mb-4">
          <strong>前端开发技能水平：</strong>
          <div class="mt-3 space-y-3">
            <div>
              <div class="flex justify-between mb-1">
                <span>JavaScript</span>
                <span>85%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 85%;"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>Vue.js/React</span>
                <span>78%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 78%;"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>CSS/HTML</span>
                <span>90%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 90%;"></div>
              </div>
            </div>
            
            <div>
              <div class="flex justify-between mb-1">
                <span>TypeScript</span>
                <span>65%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 65%;"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <strong>📈 推荐学习路径：</strong>
          <ol class="list-decimal list-inside mt-2 text-gray-600">
            <li>深入学习TypeScript高级特性和类型系统</li>
            <li>掌握Vue3/React性能优化和最佳实践</li>
            <li>学习前端工程化和现代构建工具</li>
            <li>了解微前端架构和组件库开发</li>
          </ol>
        </div>
      </div>
    </div>
  `
}

// 生成学习计划
const generateLearningPlan = (): string => {
  return `
    <div class="analysis-card">
      <div class="analysis-header">
        <i class="i-carbon-education text-green-500"></i>
        <span>个性化学习计划</span>
      </div>
      <div class="analysis-content">
        <div class="mb-4">
          <strong>📅 第1-2周：基础巩固</strong>
          <ul class="list-disc list-inside mt-2 text-gray-600">
            <li>JavaScript核心概念复习（每天2小时）</li>
            <li>完成LeetCode算法题30道</li>
            <li>阅读《JavaScript高级程序设计》相关章节</li>
          </ul>
        </div>
        
        <div class="mb-4">
          <strong>📅 第3-4周：框架深入</strong>
          <ul class="list-disc list-inside mt-2 text-gray-600">
            <li>Vue3 Composition API深入学习</li>
            <li>完成一个完整的项目实战</li>
            <li>学习Pinia状态管理和路由应用</li>
          </ul>
        </div>
        
        <div class="mb-4">
          <strong>📚 学习资源推荐：</strong>
          <div class="flex flex-wrap gap-2 mt-2">
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">📖 Vue官方文档</span>
            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">🎥 YouTube教程</span>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">💻 GitHub项目</span>
          </div>
        </div>
        
        <div>
          <strong>⏰ 学习安排：</strong>
          <div class="mt-2 text-gray-600">
            <p>• 每日学习时间：3-4小时</p>
            <p>• 预计完成时间：4周</p>
            <p>• 学习进度追踪：每周总结复盘</p>
          </div>
        </div>
      </div>
    </div>
  `
}

// 聊天API
export const chatApi = {
  // 发送消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    // 模拟网络延迟
    await delay(1000 + Math.random() * 1000)

    try {
      const aiResponse = generateAIResponse(request.message)

      return {
        success: true,
        data: {
          message: aiResponse,
          messageId: `msg_${Date.now()}`,
          sessionId: request.sessionId || `session_${Date.now()}`,
        },
      }
    } catch (error) {
      return {
        success: false,
        data: {
          message: '',
          messageId: '',
          sessionId: '',
        },
        error: '消息发送失败，请重试',
      }
    }
  },

  // 上传文件
  async uploadFile(file: File): Promise<{ success: boolean; url?: string; error?: string }> {
    await delay(500 + Math.random() * 1000)

    try {
      // 模拟文件上传
      const url = URL.createObjectURL(file)
      return {
        success: true,
        url,
      }
    } catch (error) {
      return {
        success: false,
        error: '文件上传失败',
      }
    }
  },

  // 语音转文字
  async speechToText(
    audioBlob: Blob,
  ): Promise<{ success: boolean; text?: string; error?: string }> {
    await delay(1500 + Math.random() * 500)

    try {
      // 模拟语音识别
      const mockTexts = [
        '请帮我分析一下简历',
        '我想了解面试技巧',
        '如何提升技术能力',
        '制定学习计划',
      ]

      return {
        success: true,
        text: mockTexts[Math.floor(Math.random() * mockTexts.length)],
      }
    } catch (error) {
      return {
        success: false,
        error: '语音识别失败',
      }
    }
  },
}
