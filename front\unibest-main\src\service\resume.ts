/**
 * 简历管理相关API接口
 */
import { httpGet, httpPut } from '@/utils/http'
import type { ResumeDetail, ResumeUploadResult, ResumeQueryParams, ResumePreviewContent } from '@/types/resume'
import { RESUME_API_ENDPOINTS, FILE_CONSTANTS, RESUME_MESSAGES } from '@/types/resume-constants'

/**
 * 获取当前用户的所有简历列表
 * @returns 简历列表响应
 */
export function getMyResumeList(): Promise<IResData<ResumeDetail[]>> {
  return httpGet<ResumeDetail[]>(RESUME_API_ENDPOINTS.MY_RESUME_LIST)
}

/**
 * 分页查询简历列表
 * @param params 查询参数
 * @returns 分页结果响应
 */
export function getResumeList(params: {
  params: ResumeQueryParams
}): Promise<IResData<{ total: number; rows: ResumeDetail[] }>> {
  return httpGet<{ total: number; rows: ResumeDetail[] }>(
    RESUME_API_ENDPOINTS.RESUME_LIST,
    params.params,
  )
}

/**
 * 根据ID获取简历详情
 * @param params 简历ID参数
 * @returns 简历详情响应
 */
export function getResumeDetail(params: {
  params: { resumeId: string }
}): Promise<IResData<ResumeDetail>> {
  return httpGet<ResumeDetail>(`${RESUME_API_ENDPOINTS.RESUME_DETAIL}/${params.params.resumeId}`)
}

/**
 * 上传简历文件
 * @param params 上传参数
 * @returns 上传结果响应
 */
export function uploadResume(params: {
  filePath: string
  fileName?: string
  fileType?: string
  formData?: Record<string, any>
}): Promise<IResData<ResumeUploadResult>> {
  return new Promise((resolve, reject) => {
    const uploadParams: UniApp.UploadFileOption = {
      url: RESUME_API_ENDPOINTS.UPLOAD_RESUME,
      filePath: params.filePath,
      name: 'file',
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        reject(error)
      },
    }

    // 如果提供了文件名，添加到formData中
    if (params.fileName || params.fileType || params.formData) {
      uploadParams.formData = {
        ...(params.formData || {}),
        ...(params.fileName && { fileName: params.fileName }),
        ...(params.fileType && { fileType: params.fileType }),
      }
    }

    uni.uploadFile(uploadParams)
  })
}

/**
 * 重命名简历
 * @param params 重命名参数
 * @returns 操作结果响应
 */
export function renameResume(params: {
  params: { resumeId: number; resumeName: string }
}): Promise<IResData<void>> {
  console.log('重命名简历', params.params.resumeId, params.params.resumeName)
  return httpPut<void>(`${RESUME_API_ENDPOINTS.RESUME_DETAIL}/${params.params.resumeId}/rename`, {
    resumeName: params.params.resumeName,
  })
}

/**
 * 设置默认简历
 * @param params 简历ID参数
 * @returns 操作结果响应
 */
export function setDefaultResume(params: {
  params: { resumeId: number }
}): Promise<IResData<void>> {
  return httpPut<void>(`${RESUME_API_ENDPOINTS.RESUME_DETAIL}/${params.params.resumeId}/default`)
}

/**
 * 取消默认简历
 * @param params 简历ID参数
 * @returns 操作结果响应
 */
export function cancelDefaultResume(params: {
  params: { resumeId: number }
}): Promise<IResData<void>> {
  return httpPut<void>(
    `${RESUME_API_ENDPOINTS.RESUME_DETAIL}/${params.params.resumeId}/cancel-default`,
  )
}

/**
 * 获取默认简历
 * @returns 默认简历信息响应
 */
export function getDefaultResume(): Promise<IResData<ResumeDetail>> {
  return httpGet<ResumeDetail>(RESUME_API_ENDPOINTS.GET_DEFAULT_RESUME)
}

/**
 * 删除简历
 * @param params 简历ID数组参数
 * @returns 操作结果响应
 */
export function deleteResume(params: { params: { resumeIds: number[] } }): Promise<IResData<void>> {
  return httpPut<void>(`${RESUME_API_ENDPOINTS.DELETE_RESUME}/${params.params.resumeIds.join(',')}`)
}

/**
 * 下载简历文件
 * @param params 简历ID参数
 * @returns 下载地址
 */
export function getDownloadUrl(params: { params: { resumeId: number } }): string {
  return `${RESUME_API_ENDPOINTS.DOWNLOAD_RESUME}/${params.params.resumeId}`
}

/**
 * 预览简历文件
 * @param params 预览参数
 * @returns 预览操作结果
 */
export function previewResume(params: {
  params: { resumeId: number; fileUrl: string }
}): Promise<void> {
  return new Promise((resolve, reject) => {
    // H5端使用window.open预览
    // #ifdef H5
    window.open(params.params.fileUrl, '_blank')
    resolve()
    // #endif

    // 小程序端使用uni.downloadFile + uni.openDocument
    // #ifndef H5
    uni.showLoading({
      title: RESUME_MESSAGES.SUCCESS.PREVIEW,
    })

    uni.downloadFile({
      url: params.params.fileUrl,
      success: (res) => {
        uni.hideLoading()
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            success: () => {
              resolve()
            },
            fail: (err) => {
              console.error('预览文件失败：', err)
              uni.showToast({
                title: RESUME_MESSAGES.ERROR.PREVIEW_FAILED,
                icon: 'none',
              })
              reject(err)
            },
          })
        } else {
          uni.showToast({
            title: RESUME_MESSAGES.ERROR.DOWNLOAD_FAILED,
            icon: 'none',
          })
          reject(new Error('下载失败'))
        }
      },
      fail: (err) => {
        uni.hideLoading()
        console.error('下载文件失败：', err)
        uni.showToast({
          title: RESUME_MESSAGES.ERROR.DOWNLOAD_FAILED,
          icon: 'none',
        })
        reject(err)
      },
    })
    // #endif
  })
}

/**
 * 格式化文件大小
 * @param params 文件大小参数
 * @returns 格式化后的大小字符串
 */
export function formatFileSize(params: { params: { size: number } }): string {
  const size = params.params.size
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
  }
}

/**
 * 验证文件类型
 * @param params 文件名参数
 * @returns 是否为合法的简历文件类型
 */
export function validateResumeFile(params: { params: { fileName: string } }): boolean {
  const extension = params.params.fileName
    .substring(params.params.fileName.lastIndexOf('.'))
    .toLowerCase()
  const supportedExtensions = ['.pdf', '.doc', '.docx']
  return supportedExtensions.includes(extension)
}

/**
 * 验证文件大小
 * @param params 文件大小参数
 * @returns 是否超过大小限制
 */
export function validateFileSize(params: { params: { fileSize: number } }): boolean {
  return params.params.fileSize <= FILE_CONSTANTS.MAX_FILE_SIZE
}

/**
 * 获取文件图标类名
 * @param params 文件名参数
 * @returns 图标类名
 */
export function getFileIcon(params: { params: { fileName: string } }): string {
  const extension = params.params.fileName
    .substring(params.params.fileName.lastIndexOf('.'))
    .toLowerCase()

  // 检查是否为支持的文件类型
  if (extension === '.pdf') {
    return 'i-mdi-file-pdf-box'
  } else if (extension === '.doc' || extension === '.docx') {
    return 'i-mdi-file-word-box'
  }

  return 'i-mdi-file-document'
}

/**
 * 获取简历结构化预览内容
 * @param params 简历ID参数
 * @returns 简历结构化内容响应
 */
export function getResumePreviewContent(params: {
  params: { resumeId: string }
}): Promise<IResData<ResumePreviewContent>> {
  return httpGet<ResumePreviewContent>(
    `${RESUME_API_ENDPOINTS.PREVIEW_RESUME}/${params.params.resumeId}/structured`,
  )
}

/**
 * 获取简历文件预览内容（原始文件内容）
 * @param params 简历ID参数
 * @returns 文件预览内容响应
 */
export function getResumeFileContent(params: {
  params: { resumeId: string }
}): Promise<IResData<{ content: string; type: string }>> {
  return httpGet<{ content: string; type: string }>(
    `${RESUME_API_ENDPOINTS.PREVIEW_RESUME}/${params.params.resumeId}/file`,
  )
}

/**
 * 获取简历预览图片
 * @param params 简历ID参数
 * @returns 简历预览图片URL响应
 */
export function getResumePreviewImage(params: {
  params: { resumeId: string }
}): Promise<IResData<{ imageUrl: string; thumbnailUrl?: string }>> {
  return httpGet<{ imageUrl: string; thumbnailUrl?: string }>(
    `${RESUME_API_ENDPOINTS.PREVIEW_RESUME}/${params.params.resumeId}/image`,
  )
}

/**
 * 模拟获取简历预览图片（开发阶段使用）
 * @param params 简历ID参数
 * @returns 模拟的简历预览图片数据
 */
export function getMockResumePreviewImage(params: {
  params: { resumeId: string }
}): Promise<IResData<{ imageUrl: string; thumbnailUrl?: string }>> {
  return new Promise((resolve) => {
    // 模拟API延迟
    setTimeout(() => {
      resolve({
        code: 200,
        message: '获取成功',
        data: {
          imageUrl: 'https://via.placeholder.com/800x1200/f0f0f0/666666?text=简历预览图',
          thumbnailUrl: 'https://via.placeholder.com/400x600/f0f0f0/666666?text=简历缩略图',
        },
      } as IResData<{ imageUrl: string; thumbnailUrl?: string }>)
    }, 800)
  })
}

/**
 * 模拟获取简历预览内容（开发阶段使用）
 * @param params 简历ID参数
 * @returns 模拟的简历结构化内容
 */
export function getMockResumePreviewContent(params: {
  params: { resumeId: string }
}): Promise<IResData<ResumePreviewContent>> {
  return new Promise((resolve) => {
    // 模拟API延迟
    setTimeout(() => {
      const mockData: ResumePreviewContent = {
        basicInfo: {
          resumeId: parseInt(params.params.resumeId),
          resumeName: '张三的简历',
          createTime: '2024-01-15 10:30:00',
          updateTime: '2024-01-20 16:45:00',
          isDefault: true,
        },
        personalInfo: {
          name: '张三',
          gender: '男',
          age: 28,
          phone: '13800138000',
          email: '<EMAIL>',
          address: '北京市朝阳区',
          summary: '具有5年前端开发经验，熟练掌握Vue、React等主流框架，有丰富的项目开发和团队协作经验。',
          jobIntention: {
            position: '高级前端工程师',
            industry: '互联网/软件',
            city: '北京',
            salary: '20K-30K',
            jobType: '全职',
          },
        },
        educationList: [
          {
            id: 1,
            school: '北京理工大学',
            major: '计算机科学与技术',
            degree: '本科',
            startTime: '2016-09',
            endTime: '2020-06',
            gpa: '3.8',
            mainCourses: ['数据结构', '算法设计', '软件工程', '数据库原理'],
            description: '主修计算机相关课程，成绩优异，多次获得奖学金。',
          },
        ],
        workExperienceList: [
          {
            id: 1,
            company: '腾讯科技有限公司',
            position: '高级前端工程师',
            department: '技术部',
            startTime: '2022-03',
            endTime: '2024-01',
            description: '负责公司核心产品的前端开发工作，参与架构设计和技术选型。',
            achievements: [
              '主导完成了用户管理系统的重构，提升了50%的性能',
              '建立了前端代码规范和自动化测试流程',
              '指导新人开发，提升团队整体技术水平',
            ],
          },
          {
            id: 2,
            company: '字节跳动有限公司',
            position: '前端工程师',
            department: '产品研发部',
            startTime: '2020-07',
            endTime: '2022-02',
            description: '参与多个产品线的前端开发，负责移动端和PC端的开发工作。',
            achievements: [
              '独立完成了移动端H5项目的开发',
              '优化了页面加载速度，提升了30%的用户体验',
              '参与了微前端架构的设计和实现',
            ],
          },
        ],
        skillList: [
          { id: 1, name: 'Vue.js', type: '前端框架', level: 5 },
          { id: 2, name: 'React', type: '前端框架', level: 4 },
          { id: 3, name: 'TypeScript', type: '编程语言', level: 4 },
          { id: 4, name: 'Node.js', type: '后端技术', level: 3 },
          { id: 5, name: 'Webpack', type: '构建工具', level: 4 },
          { id: 6, name: 'Git', type: '版本控制', level: 5 },
        ],
        projectList: [
          {
            id: 1,
            name: '企业管理系统',
            role: '前端负责人',
            startTime: '2023-01',
            endTime: '2023-06',
            description: '基于Vue3和Element Plus开发的企业内部管理系统',
            technologies: ['Vue3', 'TypeScript', 'Element Plus', 'Pinia'],
            achievements: ['提升了管理效率40%', '获得用户好评率95%'],
          },
        ],
        awardList: [
          {
            id: 1,
            name: '优秀员工奖',
            issuer: '腾讯科技有限公司',
            time: '2023-12',
            level: '公司级',
            description: '因工作表现突出获得年度优秀员工奖',
          },
        ],
        certificateList: [
          {
            id: 1,
            name: '软件设计师',
            issuer: '工业和信息化部',
            time: '2021-05',
            number: 'SD202105001234',
            description: '中级软件设计师资格证书',
          },
        ],
        languageList: [
          { id: 1, name: '英语', level: 'CET-6', certificate: '英语六级证书' },
        ],
        hobbies: ['阅读', '摄影', '旅行', '编程'],
        selfEvaluation: '我是一个积极向上、责任心强的人，具有良好的沟通能力和团队协作精神。在工作中注重细节，追求完美，能够在压力下保持高效的工作状态。热爱学习新技术，关注行业发展趋势。',
      }

      resolve({
        code: 200,
        message: '获取成功',
        data: mockData,
      } as IResData<ResumePreviewContent>)
    }, 1000)
  })
}
