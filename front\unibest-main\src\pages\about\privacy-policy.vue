<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

// 隐私政策页面
// 详细说明数据收集、使用和保护政策

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 政策更新日期
const lastUpdated = ref('2024-01-15')

// 隐私政策内容
const privacySections = ref([
  {
    title: '1. 信息收集',
    content: `我们可能收集以下类型的信息：
• 基本信息：姓名、性别、年级、专业等
• 账户信息：用户名、密码、邮箱、手机号等
• 使用数据：面试记录、学习进度、偏好设置等
• 设备信息：设备型号、操作系统、网络信息等
• 多媒体内容：面试录音录像、上传的简历等`,
  },
  {
    title: '2. 信息使用',
    content: `我们使用收集的信息用于：
• 提供和改进面试评测服务
• 生成个性化的学习建议
• 分析用户行为以优化用户体验
• 发送服务通知和重要更新
• 提供客户支持和技术服务`,
  },
  {
    title: '3. 信息存储与安全',
    content: `我们采取以下措施保护您的信息：
• 使用加密技术保护数据传输
• 定期备份和安全存储数据
• 限制员工对用户数据的访问权限
• 定期进行安全审计和漏洞检测
• 遵循行业最佳实践和安全标准`,
  },
  {
    title: '4. 信息共享',
    content: `我们承诺：
• 不会将您的个人信息出售给第三方
• 仅在法律要求或您明确同意的情况下共享信息
• 与可信的服务提供商共享必要信息以提供服务
• 所有第三方合作伙伴都需遵守严格的隐私保护要求`,
  },
  {
    title: '5. Cookie和类似技术',
    content: `我们使用Cookie和类似技术：
• 记住您的登录状态和偏好设置
• 分析网站使用情况和性能
• 提供个性化的内容和服务
• 您可以通过浏览器设置控制Cookie的使用`,
  },
  {
    title: '6. 您的权利',
    content: `您对个人信息享有以下权利：
• 访问权：查看我们收集的关于您的信息
• 更正权：修改不准确的个人信息
• 删除权：要求删除您的个人信息
• 限制处理权：限制我们处理您的信息
• 数据可携带权：获取您的数据副本`,
  },
  {
    title: '7. 儿童隐私保护',
    content: `我们特别注重保护儿童的隐私：
• 不会故意收集13岁以下儿童的个人信息
• 如发现误收集儿童信息，将立即删除
• 鼓励家长监督和指导孩子的网络活动`,
  },
  {
    title: '8. 国际数据传输',
    content: `如需跨境传输数据，我们将：
• 确保目标国家具有适当的数据保护水平
• 采用标准合同条款或其他法律机制
• 获得您的明确同意`,
  },
  {
    title: '9. 政策更新',
    content: `我们可能会更新本隐私政策：
• 重大变更将提前通知用户
• 更新后的政策将在平台上公布
• 继续使用服务即表示接受更新后的政策`,
  },
  {
    title: '10. 联系我们',
    content: `如您对隐私政策有任何疑问或需要行使权利，请联系我们：
• 邮箱：<EMAIL>
• 电话：************
• 我们将在30天内回复您的请求`,
  },
])

// 数据类型说明
const dataTypes = ref([
  {
    type: '必需数据',
    description: '提供基本服务所必需的数据',
    examples: '用户名、密码、基本资料',
    retention: '账户存续期间',
  },
  {
    type: '功能数据',
    description: '增强服务功能的数据',
    examples: '面试记录、学习偏好',
    retention: '2年或用户删除',
  },
  {
    type: '分析数据',
    description: '用于服务优化的匿名数据',
    examples: '使用统计、性能数据',
    retention: '1年',
  },
])
</script>

<template>
  <view class="privacy-page">
    <HeadBar title="隐私政策" :show-back="true" :show-right-button="false" />

    <view class="content-area" style="margin-top: 80rpx">
      <!-- 政策标题 -->
      <view class="privacy-header">
        <text class="privacy-title">AI面试助手隐私政策</text>
        <text class="last-updated">最后更新：{{ lastUpdated }}</text>
        <text class="intro-text">
          我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。
        </text>
      </view>

      <!-- 数据类型说明 -->
      <view class="data-types-section">
        <text class="section-header">数据类型说明</text>
        <view class="data-types-grid">
          <view class="data-type-item" v-for="(dataType, index) in dataTypes" :key="index">
            <text class="data-type-title">{{ dataType.type }}</text>
            <text class="data-type-desc">{{ dataType.description }}</text>
            <text class="data-type-examples">示例：{{ dataType.examples }}</text>
            <text class="data-type-retention">保存期限：{{ dataType.retention }}</text>
          </view>
        </view>
      </view>

      <!-- 政策内容 -->
      <view class="privacy-content">
        <view class="section" v-for="(section, index) in privacySections" :key="index">
          <text class="section-title">{{ section.title }}</text>
          <text class="section-content">{{ section.content }}</text>
        </view>
      </view>

      <!-- 重要提示 -->
      <view class="important-notice">
        <view class="notice-icon">
          <text
            class="i-fa-solid-exclamation-triangle"
            style="font-size: 32rpx; color: #f59e42"
          ></text>
        </view>
        <view class="notice-content">
          <text class="notice-title">重要提示</text>
          <text class="notice-text">
            如果您不同意本隐私政策的任何内容，请不要使用我们的服务。我们建议您定期查看本政策，以了解我们如何保护您的信息。
          </text>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="contact-footer">
        <text class="contact-title">隐私问题咨询</text>
        <text class="contact-email">隐私邮箱：<EMAIL></text>
        <text class="contact-phone">服务热线：************</text>
        <text class="response-time">我们将在30天内回复您的隐私相关询问</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.privacy-page {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background: #f7f9fc;
}

// 头部导航
.nav-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 24rpx;
  color: #333;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.2s;

  &:active {
    background: #e0e0e0;
    transform: scale(0.9);
  }
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}

.placeholder {
  width: 60rpx;
}

// 内容区域
.content-area {
  padding: 40rpx 32rpx;
}

// 政策标题
.privacy-header {
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.privacy-title {
  display: block;
  margin-bottom: 16rpx;
  font-size: 38rpx;
  font-weight: bold;
  color: #222;
}

.last-updated {
  display: block;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #00c9a7;
}

.intro-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

// 数据类型说明
.data-types-section {
  margin-bottom: 32rpx;
}

.section-header {
  position: relative;
  display: block;
  padding-left: 8rpx;
  margin-bottom: 24rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #222;

  &::after {
    position: absolute;
    bottom: -8rpx;
    left: 0;
    width: 64rpx;
    height: 6rpx;
    content: '';
    background: #00c9a7;
    border-radius: 4rpx;
  }
}

.data-types-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}

.data-type-item {
  padding: 24rpx;
  background: #fff;
  border-left: 6rpx solid #00c9a7;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.data-type-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}

.data-type-desc {
  display: block;
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: #555;
}

.data-type-examples {
  display: block;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #777;
}

.data-type-retention {
  display: block;
  font-size: 24rpx;
  color: #00c9a7;
}

// 政策内容
.privacy-content {
  padding: 32rpx;
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.section {
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: block;
  padding-bottom: 12rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-content {
  display: block;
  font-size: 28rpx;
  line-height: 1.8;
  color: #555;
  white-space: pre-line;
}

// 重要提示
.important-notice {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin-bottom: 32rpx;
  background: linear-gradient(135deg, #fff8e1, #fff3c4);
  border: 2rpx solid #ffd54f;
  border-radius: 24rpx;
}

.notice-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.notice-content {
  flex: 1;
}

.notice-title {
  display: block;
  margin-bottom: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}

.notice-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #555;
}

// 联系信息
.contact-footer {
  padding: 32rpx;
  text-align: center;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
  border-radius: 24rpx;
}

.contact-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}

.contact-email {
  display: block;
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: #00c9a7;
}

.contact-phone {
  display: block;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #00c9a7;
}

.response-time {
  display: block;
  font-size: 24rpx;
  color: #666;
}
</style>
