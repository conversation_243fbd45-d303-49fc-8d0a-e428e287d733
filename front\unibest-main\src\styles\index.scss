// ==================== 智能面试系统 - 主题样式入口 ====================

// 导入基础样式
@import './variables';
@import './mixins';
@import './base';
@import './layout';

// ==================== 组件样式 ====================
// 这里可以导入全局组件样式，如果需要的话
// @import './components/button.scss';
// @import './components/card.scss';

// ==================== 页面特定样式 ====================
// 首页样式增强
.page-index {
  // 基于index.vue的样式优化
  .welcome-section {
    @include fade-in;

    .welcome-text {
      @include text-gradient($gradient-primary);
    }

    .primary-interview-btn {
      @include btn-primary;
      @include gpu-accelerate;

      // 添加闪光效果
      position: relative;
      overflow: hidden;

      &::before {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        content: '';
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:active::before {
        left: 100%;
      }
    }
  }

  .stats-card {
    @include data-card;
    @include slide-in-up;

    .stat-item {
      transition: transform $transition-base;

      &:hover {
        transform: translateY(-4rpx);
      }
    }
  }

  .section-switcher {
    .switcher-item {
      transition: all $transition-base;

      &.active {
        color: $primary-color;
        transform: translateY(-2rpx);
      }
    }
  }
}

// 面试房间页面样式增强
.page-interview-room {
  .interview-room {
    // 保持现有的专业蓝色主题
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);

    .header {
      @include glass-morphism(0.95);
      background: rgba(30, 60, 114, 0.95);
      border-bottom: 1rpx solid rgba(59, 130, 246, 0.3);
    }

    .question-card {
      @include card-glass;

      &::before {
        background: linear-gradient(90deg, #3b82f6 0%, #10b981 100%);
      }
    }

    .footer-controls {
      @include glass-morphism(0.95);
      background: rgba(30, 60, 114, 0.95);
      border-top: 1rpx solid rgba(59, 130, 246, 0.3);
    }
  }
}

// 用户反馈页面样式增强
.page-feedback {
  .feedback-list-page {
    background: $bg-gradient;

    .stats-card {
      @include data-card;
      color: $text-inverse;
      background: $gradient-primary;
    }

    .feedback-item {
      @include card-hover;

      .status-badge {
        &.status-pending {
          @include status-warning;
        }

        &.status-processing {
          @include status-info;
        }

        &.status-resolved {
          @include status-success;
        }

        &.status-closed {
          color: #6b7280;
          background: rgba(107, 114, 128, 0.1);
          border: 1rpx solid rgba(107, 114, 128, 0.3);
        }
      }
    }
  }
}

// ==================== 通用页面布局样式 ====================
.page-layout {
  min-height: 100vh;
  background: $bg-gradient;

  // 导航栏区域
  .nav-section {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: $z-index-fixed;

    // 安全区域适配
    padding-top: env(safe-area-inset-top);
    background: $gradient-primary;
    box-shadow: 0 4rpx 20rpx rgba($primary-color, 0.2);
  }

  // 主内容区域
  .main-section {
    min-height: 100vh;
    padding-top: calc(#{$navbar-height} + env(safe-area-inset-top));
    padding-bottom: calc(#{$tabbar-height} + env(safe-area-inset-bottom));

    .content-container {
      padding: $spacing-xl;

      @include mobile {
        padding: $spacing-base;
      }
    }
  }

  // 底部导航区域
  .bottom-section {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: $z-index-fixed;

    // 安全区域适配
    padding-bottom: env(safe-area-inset-bottom);
    background: $bg-primary;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  }
}

// ==================== 数据可视化样式 ====================
.chart-container {
  @include card-base;
  padding: $card-padding;

  .chart-title {
    margin-bottom: $spacing-lg;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    text-align: center;
  }

  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-base;
    justify-content: center;
    margin-top: $spacing-lg;

    .legend-item {
      display: flex;
      gap: $spacing-xs;
      align-items: center;

      .legend-color {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
      }

      .legend-text {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

// ==================== 表单样式增强 ====================
.form-container {
  .form-group {
    margin-bottom: $spacing-xl;

    .form-label {
      display: block;
      margin-bottom: $spacing-sm;
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
      color: $text-primary;
    }

    .form-help {
      margin-top: $spacing-xs;
      font-size: $font-size-sm;
      color: $text-tertiary;
    }

    .form-error {
      margin-top: $spacing-xs;
      font-size: $font-size-sm;
      color: $error-color;
    }
  }

  .form-actions {
    @include flex-center;
    gap: $spacing-base;
    margin-top: $spacing-2xl;

    @include mobile {
      flex-direction: column;

      .ui-button {
        width: 100%;
      }
    }
  }
}

// ==================== 列表样式增强 ====================
.list-container {
  .list-item {
    @include card-base;
    padding: $spacing-lg;
    margin-bottom: $spacing-base;
    transition: all $transition-base;

    &:hover {
      box-shadow: $shadow-md;
      transform: translateY(-2rpx);
    }

    &:active {
      box-shadow: $shadow-sm;
      transform: translateY(1rpx);
    }

    .item-header {
      @include flex-between;
      margin-bottom: $spacing-sm;

      .item-title {
        font-size: $font-size-base;
        font-weight: $font-weight-semibold;
        color: $text-primary;
      }

      .item-meta {
        font-size: $font-size-sm;
        color: $text-tertiary;
      }
    }

    .item-content {
      margin-bottom: $spacing-sm;
      font-size: $font-size-sm;
      line-height: $line-height-relaxed;
      color: $text-secondary;
    }

    .item-footer {
      @include flex-between;

      .item-tags {
        display: flex;
        gap: $spacing-xs;

        .tag {
          padding: $spacing-xs $spacing-sm;
          font-size: $font-size-xs;
          color: $primary-color;
          background: rgba($primary-color, 0.1);
          border-radius: $radius-sm;
        }
      }

      .item-actions {
        display: flex;
        gap: $spacing-sm;

        .action-btn {
          padding: $spacing-xs;
          font-size: $font-size-base;
          color: $text-tertiary;
          transition: color $transition-fast;

          &:hover {
            color: $primary-color;
          }
        }
      }
    }
  }
}

// ==================== 模态框样式增强 ====================
.modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-modal;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  @include flex-center;

  .modal-content {
    @include card-base;
    width: 90%;
    max-width: 600rpx;
    max-height: 80vh;
    overflow-y: auto;
    @include smooth-scroll;

    .modal-header {
      @include flex-between;
      padding: $spacing-xl;
      border-bottom: 1rpx solid $border-light;

      .modal-title {
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        color: $text-primary;
      }

      .modal-close {
        font-size: $font-size-xl;
        color: $text-tertiary;
        cursor: pointer;
        transition: color $transition-fast;

        &:hover {
          color: $text-secondary;
        }
      }
    }

    .modal-body {
      padding: $spacing-xl;
    }

    .modal-footer {
      @include flex-center;
      gap: $spacing-base;
      padding: $spacing-xl;
      background: $bg-secondary;
      border-top: 1rpx solid $border-light;
    }
  }
}

// ==================== 响应式增强 ====================
@include mobile {
  .page-layout {
    .main-section {
      .content-container {
        padding: $spacing-base;
      }
    }
  }

  .modal-overlay {
    .modal-content {
      width: 95%;
      margin: $spacing-base;
    }
  }
}

@include tablet {
  .page-layout {
    .main-section {
      .content-container {
        max-width: 750rpx;
        margin: 0 auto;
      }
    }
  }
}

@include desktop {
  .page-layout {
    .main-section {
      .content-container {
        max-width: 1200rpx;
        margin: 0 auto;
      }
    }
  }
}

// ==================== 打印样式 ====================
@media print {
  .nav-section,
  .bottom-section,
  .ui-button,
  .modal-overlay {
    display: none !important;
  }

  .page-layout {
    .main-section {
      padding: 0;
    }
  }

  .ui-card {
    border: 1rpx solid $border-medium;
    box-shadow: none;
  }
}

// ==================== 高对比度模式 ====================
@media (prefers-contrast: high) {
  .ui-card {
    border: 2rpx solid $border-dark;
  }

  .ui-button {
    border: 2rpx solid currentColor;
  }

  .status-badge {
    border-width: 2rpx;
  }
}

// ==================== 减少动画模式 ====================
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}
