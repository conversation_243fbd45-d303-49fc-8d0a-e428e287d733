// ==================== 智能面试系统 - 通用混入 ====================

// ==================== 布局混入 ====================
// Flexbox 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox 垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// Flexbox 水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// Flexbox 两端对齐
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 绝对居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// ==================== 文本混入 ====================
// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
  }
}

// 文本渐变
@mixin text-gradient($gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// ==================== 按钮混入 ====================
// 基础按钮样式
@mixin btn-base {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: $font-weight-semibold;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: $radius-xl;
  transition: all $transition-base;

  &:active {
    transform: translateY(2rpx);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
  }
}

// 主要按钮
@mixin btn-primary {
  @include btn-base;
  color: $text-inverse;
  background: $gradient-primary;
  box-shadow: $shadow-primary;

  &:active {
    box-shadow: $shadow-primary-lg;
  }
}

// 次要按钮
@mixin btn-secondary {
  @include btn-base;
  color: $primary-color;
  background: $bg-primary;
  border: 2rpx solid $primary-color;

  &:active {
    background: rgba($primary-color, 0.05);
  }
}

// 危险按钮
@mixin btn-danger {
  @include btn-base;
  color: $text-inverse;
  background: linear-gradient(135deg, $error-color 0%, #dc2626 100%);
  box-shadow: 0 8rpx 24rpx rgba($error-color, 0.3);
}

// ==================== 卡片混入 ====================
// 基础卡片
@mixin card-base {
  overflow: hidden;
  background: $bg-primary;
  border-radius: $card-radius;
  box-shadow: $card-shadow;
}

// 玻璃形态卡片
@mixin card-glass {
  background: $glass-bg;
  backdrop-filter: $glass-backdrop;
  border: 1rpx solid $glass-border;
  border-radius: $card-radius;
  box-shadow: $shadow-md;
}

// 悬浮卡片
@mixin card-hover {
  @include card-base;
  transition: all $transition-base;

  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-4rpx);
  }
}

// ==================== 输入框混入 ====================
// 基础输入框
@mixin input-base {
  width: 100%;
  padding: $spacing-lg;
  font-size: $font-size-base;
  color: $text-primary;
  background: $bg-primary;
  border: 2rpx solid $border-light;
  border-radius: $radius-md;
  transition: all $transition-base;

  &:focus {
    border-color: $primary-color;
    outline: none;
    box-shadow: 0 0 0 6rpx rgba($primary-color, 0.1);
  }

  &::placeholder {
    color: $text-tertiary;
  }

  &:disabled {
    color: $text-disabled;
    cursor: not-allowed;
    background: $bg-secondary;
  }
}

// ==================== 动画混入 ====================
// 淡入动画
@mixin fade-in($duration: $transition-base) {
  animation: fadeIn $duration ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 滑入动画
@mixin slide-in-up($duration: $transition-base) {
  animation: slideInUp $duration $ease-out-cubic;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 缩放动画
@mixin scale-in($duration: $transition-base) {
  animation: scaleIn $duration $ease-out-cubic;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 脉冲动画
@mixin pulse($color: $primary-color) {
  animation: pulse 2s infinite;

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba($color, 0.7);
    }
    70% {
      box-shadow: 0 0 0 20rpx rgba($color, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba($color, 0);
    }
  }
}

// ==================== 响应式混入 ====================
// 媒体查询
@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

// ==================== 状态混入 ====================
// 加载状态
@mixin loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;

  @keyframes loading {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}

// 成功状态
@mixin status-success {
  color: $success-color;
  background: $success-bg;
  border: 1rpx solid $success-border;
}

// 警告状态
@mixin status-warning {
  color: $warning-color;
  background: $warning-bg;
  border: 1rpx solid $warning-border;
}

// 错误状态
@mixin status-error {
  color: $error-color;
  background: $error-bg;
  border: 1rpx solid $error-border;
}

// 信息状态
@mixin status-info {
  color: $info-color;
  background: $info-bg;
  border: 1rpx solid $info-border;
}

// ==================== 特殊效果混入 ====================
// 毛玻璃效果
@mixin glass-morphism($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(20px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

// 新拟态效果
@mixin neumorphism($color: #f0f0f0) {
  background: $color;
  box-shadow:
    8rpx 8rpx 16rpx rgba(0, 0, 0, 0.1),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
}

// 渐变边框
@mixin gradient-border($gradient, $width: 2rpx) {
  position: relative;
  background: $bg-primary;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: $width;
    content: '';
    background: $gradient;
    border-radius: inherit;
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}

// ==================== 性能优化混入 ====================
// GPU加速
@mixin gpu-accelerate {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

// 滚动优化
@mixin smooth-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

// ==================== 智能面试系统专用混入 ====================
// 能力评估颜色
@mixin ability-color($score) {
  @if $score >= 90 {
    color: $ability-excellent;
  } @else if $score >= 80 {
    color: $ability-good;
  } @else if $score >= 70 {
    color: $ability-average;
  } @else if $score >= 60 {
    color: $ability-poor;
  } @else {
    color: $ability-critical;
  }
}

// 面试状态样式
@mixin interview-status($status) {
  @if $status == 'active' {
    color: $interview-active;
    background: rgba($interview-active, 0.1);
  } @else if $status == 'pending' {
    color: $interview-pending;
    background: rgba($interview-pending, 0.1);
  } @else if $status == 'completed' {
    color: $interview-completed;
    background: rgba($interview-completed, 0.1);
  } @else if $status == 'failed' {
    color: $interview-failed;
    background: rgba($interview-failed, 0.1);
  }
}

// 数据卡片样式
@mixin data-card {
  @include card-base;
  position: relative;
  padding: $card-padding;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 4rpx;
    content: '';
    background: $gradient-primary;
    border-radius: $card-radius $card-radius 0 0;
  }
}
