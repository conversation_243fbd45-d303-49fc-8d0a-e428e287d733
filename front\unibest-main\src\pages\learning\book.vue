<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import Loading from '@/components/Loading.vue'
// @ts-ignore
import CategoryFilter from '@/components/CategoryFilter.vue'
// @ts-ignore
import SearchBox from '@/components/SearchBox.vue'
// 引入类型定义
import type { Ref } from 'vue'
// 引入API和类型定义
import { getBookList, getReadingStats } from '../../service/learning'
import type { Book, BookListParams, ReadingStats, CategoryOption } from '../../types/learning'
import { BOOK_CATEGORIES, BOOK_CATEGORY_LABELS } from '../../types/learning-constants'

// 页面加载状态
const isPageLoaded: Ref<boolean> = ref(false)
const isLoading: Ref<boolean> = ref(false)
const isInitialLoading: Ref<boolean> = ref(true)
const isRefreshing: Ref<boolean> = ref(false)
// 添加上拉加载更多状态
const isLoadingMore: Ref<boolean> = ref(false)
const hasMoreData: Ref<boolean> = ref(true)
const totalPages: Ref<number> = ref(0)

// 加载类型状态
const loadingType: Ref<'normal' | 'category' | 'search'> = ref('normal')

// 分类选项
const categoryOptions: Ref<CategoryOption[]> = ref([
  {
    key: BOOK_CATEGORIES.ALL,
    name: BOOK_CATEGORY_LABELS[BOOK_CATEGORIES.ALL],
    icon: 'i-mdi-view-grid',
  },
  {
    key: BOOK_CATEGORIES.TECHNICAL,
    name: BOOK_CATEGORY_LABELS[BOOK_CATEGORIES.TECHNICAL],
    icon: 'i-mdi-code-braces',
  },
  {
    key: BOOK_CATEGORIES.BEHAVIORAL,
    name: BOOK_CATEGORY_LABELS[BOOK_CATEGORIES.BEHAVIORAL],
    icon: 'i-mdi-account-voice',
  },
  {
    key: BOOK_CATEGORIES.ALGORITHM,
    name: BOOK_CATEGORY_LABELS[BOOK_CATEGORIES.ALGORITHM],
    icon: 'i-mdi-function-variant',
  },
  {
    key: BOOK_CATEGORIES.RESUME,
    name: BOOK_CATEGORY_LABELS[BOOK_CATEGORIES.RESUME],
    icon: 'i-mdi-file-document-edit',
  },
  {
    key: BOOK_CATEGORIES.SALARY,
    name: BOOK_CATEGORY_LABELS[BOOK_CATEGORIES.SALARY],
    icon: 'i-mdi-currency-usd',
  },
])

// 当前选中的分类
const selectedCategory: Ref<string> = ref(BOOK_CATEGORIES.ALL)

// 搜索关键词
const searchQuery: Ref<string> = ref('')

// 专业选择器显示状态
const showCategorySelector: Ref<boolean> = ref(false)

// 面试宝典数据
const bookList: Ref<Book[]> = ref([])

// 阅读统计
const readingStats: Ref<ReadingStats | null> = ref(null)

// 查询参数
const queryParams: Ref<BookListParams> = ref({
  page: 1,
  pageSize: 20,
  category: '',
  keyword: '',
  orderBy: 'updateTime',
  orderDirection: 'desc',
})

// 筛选后的书籍列表（改为直接返回bookList，因为筛选在后端完成）
const filteredBooks = computed(() => {
  // 由于使用后端分页+筛选，直接返回已加载的书籍列表
  return bookList.value
})

/**
 * @description 获取加载文本
 * @returns 返回当前加载类型对应的提示文本
 */
const getLoadingText = computed(() => {
  const textMap = {
    normal: '加载中...',
    category: '正在筛选书籍...',
    search: '正在搜索书籍...',
  }
  return textMap[loadingType.value]
})

/**
 * @description 获取难度对应的样式类名
 * @param difficulty 难度等级（入门/进阶/高级）
 * @returns 返回对应的样式类名字符串
 */
const getDifficultyStyle = (difficulty: string): string => {
  const styleMap: Record<string, string> = {
    入门: 'bg-green-100 text-green-700',
    进阶: 'bg-yellow-100 text-yellow-700',
    高级: 'bg-red-100 text-red-700',
  }
  return styleMap[difficulty] || 'bg-gray-100 text-gray-700'
}

/**
 * @description 处理分类改变事件
 * @param category 分类的 key 值
 */
const handleCategoryChange = (category: string): void => {
  selectedCategory.value = category
  // 更新查询参数并重新加载数据
  queryParams.value.category = category === BOOK_CATEGORIES.ALL ? '' : category
  queryParams.value.page = 1 // 重置分页
  hasMoreData.value = true // 重置加载状态
  loadingType.value = 'category' // 设置加载类型为分类筛选
  loadBookData()
}

/**
 * @description 处理分类重置事件
 */
const handleCategoryReset = (): void => {
  selectedCategory.value = BOOK_CATEGORIES.ALL
  queryParams.value.category = ''
  queryParams.value.page = 1 // 重置分页
  hasMoreData.value = true // 重置加载状态
  loadingType.value = 'category' // 设置加载类型为分类筛选
  loadBookData()
}

/**
 * @description 处理搜索事件
 * @param searchValue 搜索关键词
 */
const handleSearch = (searchValue: string): void => {
  queryParams.value.keyword = searchValue
  queryParams.value.page = 1 // 重置到第一页
  hasMoreData.value = true // 重置加载状态
  loadingType.value = 'search' // 设置加载类型为搜索
  loadBookData()
}

/**
 * @description 清除搜索内容
 */
const clearSearch = (): void => {
  // SearchBox组件会自动清除输入框内容
  // 我们只需要重置查询参数并重新加载数据
  queryParams.value.keyword = ''
  queryParams.value.page = 1
  hasMoreData.value = true // 重置加载状态
  loadingType.value = 'normal' // 设置加载类型为普通加载
  loadBookData()
}

/**
 * @description 开始阅读书籍
 * @param book 书籍对象
 */
const startReading = (book: Book): void => {
  if (!book.isFree && book.price > 0) {
    uni.showModal({
      title: '付费内容',
      content: `《${book.title}》是付费内容，价格：¥${book.price}，是否购买？`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到支付页面
          uni.navigateTo({
            url: `/pages/pay/pay?id=${book.id}&type=book`,
          })
        }
      },
    })
    return
  }

  uni.navigateTo({
    url: `/pages/learning/book-reader?id=${book.id}`,
  })
}

/**
 * @description 获取列表项的动画延迟时间
 * @param index 列表项的索引
 * @returns 返回动画延迟时间字符串
 */
const getAnimationDelay = (index: number): string => {
  // 判断是否为新加载的书籍（索引大于等于之前的书籍数量）
  const isNewItem = index >= previousBookCount.value

  if (isNewItem) {
    // 新书籍：使用相对索引和较短延迟
    const relativeIndex = index - previousBookCount.value
    return `${Math.min(relativeIndex * 0.05, 0.5)}s` // 最大0.5秒延迟
  } else {
    // 现有书籍：不需要动画延迟
    return '0s'
  }
}

/**
 * @description 初始化页面动画效果
 */
const initPage = async (): Promise<void> => {
  await nextTick()
  setTimeout(() => {
    isPageLoaded.value = true
  }, 100)
}

/**
 * @description 初始化页面数据
 */
const initializePageData = async (): Promise<void> => {
  try {
    // 并行加载数据
    await Promise.all([loadBookData(), loadReadingStats()])
  } catch (error) {
    console.error('页面数据初始化失败:', error)
    uni.showToast({
      title: '页面加载失败，请重试',
      icon: 'error',
      duration: 2000,
    })
  } finally {
    isInitialLoading.value = false
  }
}

// 添加一个状态来跟踪上一次的书籍数量
const previousBookCount: Ref<number> = ref(0)

/**
 * @description 加载书籍数据
 * @param isRefresh 是否为刷新操作
 * @param isLoadMore 是否为加载更多操作
 */
const loadBookData = async (isRefresh = false, isLoadMore = false): Promise<void> => {
  try {
    if (isRefresh) {
      isRefreshing.value = true
      // 重置分页
      queryParams.value.page = 1
      hasMoreData.value = true
    } else if (isLoadMore) {
      isLoadingMore.value = true
      // 加载下一页
      queryParams.value.page += 1
    } else if (!isInitialLoading.value) {
      isLoading.value = true
      // 重置分页
      queryParams.value.page = 1
      hasMoreData.value = true
    }

    const response = await getBookList(queryParams.value)
    console.log(response)

    if (response.code === 200 && response.data) {
      const newBooks = response.data.records || []
      totalPages.value = response.data.pages || 0

      if (isRefresh || (!isLoadMore && !isInitialLoading.value)) {
        // 刷新或普通加载：替换数据
        previousBookCount.value = 0 // 重置计数
        bookList.value = newBooks
      } else if (isLoadMore) {
        // 加载更多：追加数据
        previousBookCount.value = bookList.value.length // 记录之前的数量
        // 使用 push 方法直接添加，避免大数组展开的性能问题
        bookList.value.push(...newBooks)
      } else {
        // 初始加载
        previousBookCount.value = 0 // 重置计数
        bookList.value = newBooks
      }

      // 检查是否还有更多数据
      hasMoreData.value = queryParams.value.page < totalPages.value

      if (isRefresh) {
        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 500,
        })
      } else if (isLoadMore && newBooks.length === 0) {
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none',
          duration: 500,
        })
      }
    } else {
      throw new Error(response.message || '获取书籍列表失败')
    }
  } catch (error) {
    console.error('加载书籍数据失败:', error)

    // 如果是加载更多失败，回退页码
    if (isLoadMore) {
      queryParams.value.page = Math.max(1, queryParams.value.page - 1)
    }

    // 如果是网络错误，保持现有数据
    if (bookList.value.length === 0) {
      uni.showToast({
        title: '加载失败，请检查网络连接',
        icon: 'error',
        duration: 1000,
      })
    } else {
      uni.showToast({
        title: isLoadMore ? '加载更多失败，请重试' : '刷新失败，请重试',
        icon: 'error',
        duration: 1000,
      })
    }
  } finally {
    isRefreshing.value = false
    isLoading.value = false
    isLoadingMore.value = false
  }
}

/**
 * @description 加载阅读统计数据
 */
const loadReadingStats = async (): Promise<void> => {
  try {
    const response = await getReadingStats()

    if (response.code === 200 && response.data) {
      readingStats.value = response.data
    }
  } catch (error) {
    console.error('加载阅读统计失败:', error)
    // 阅读统计不是关键数据，失败时不显示错误提示
  }
}

/**
 * @description 处理下拉刷新
 */
const handleRefresh = async (): Promise<void> => {
  console.log('handleRefresh')
  await Promise.all([loadBookData(true, false), loadReadingStats()])
}

/**
 * @description 处理上拉加载更多
 */
const handleLoadMore = async (): Promise<void> => {
  if (isLoadingMore.value || !hasMoreData.value) {
    return
  }

  console.log('handleLoadMore - 开始加载')
  const startTime = performance.now()

  try {
    await loadBookData(false, true)
    const endTime = performance.now()
    console.log(`handleLoadMore - 加载完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)
  } catch (error) {
    console.error('handleLoadMore - 加载失败:', error)
  }
}

// 防抖版本的 handleLoadMore
let loadMoreDebounceTimer: number | null = null

/**
 * @description 处理滚动到底部事件（带防抖）
 */
const handleScrollToLower = (): void => {
  // 清除之前的计时器
  if (loadMoreDebounceTimer) {
    clearTimeout(loadMoreDebounceTimer)
  }

  // 设置新的计时器，200ms 防抖
  loadMoreDebounceTimer = setTimeout(() => {
    handleLoadMore()
    loadMoreDebounceTimer = null
  }, 200)
}

/**
 * @description 组件销毁时清理定时器
 */
const cleanup = (): void => {
  // 清理加载更多的防抖计时器
  if (loadMoreDebounceTimer) {
    clearTimeout(loadMoreDebounceTimer)
    loadMoreDebounceTimer = null
  }
}

// 页面加载时执行初始化
onMounted(async () => {
  initPage()
  await initializePageData()
})

// 页面卸载时清理资源
onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <view class="book-container">
    <HeadBar title="面试宝典" :fixed="true" />

    <!-- 主要内容区域 -->
    <scroll-view
      class="main-content"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
      @scrolltolower="handleScrollToLower"
    >
      <view v-if="!isInitialLoading" class="content-wrapper fade-in">
        <!-- 头部欢迎区域 -->
        <view class="welcome-section">
          <view class="welcome-content">
            <text class="welcome-title">面试宝典</text>
            <text class="welcome-subtitle">精选面试书籍，助你成功通过面试</text>
          </view>
          <view class="welcome-decoration">
            <view class="i-mdi-book-open-page-variant decoration-icon"></view>
          </view>
        </view>

        <!-- 阅读统计 -->
        <!-- <view class="stats-section">
          <view class="stats-grid">
            <view class="stat-item">
              <view class="stat-icon-wrapper">
                <view class="i-mdi-book-check stat-icon"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ readingStats.completedBooks }}</text>
                <text class="stat-label">已读完</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon-wrapper">
                <view class="i-mdi-file-document-multiple stat-icon"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ readingStats.totalPages }}</text>
                <text class="stat-label">总页数</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon-wrapper">
                <view class="i-mdi-fire stat-icon"></view>
              </view>
              <view class="stat-info">
                <text class="stat-value">{{ readingStats.readingStreak }}</text>
                <text class="stat-label">连续天数</text>
              </view>
            </view>
          </view>
        </view> -->

        <!-- 搜索框 -->
        <view class="search-section">
          <SearchBox
            v-model="searchQuery"
            placeholder="搜索书名、作者或内容..."
            search-button-text="搜索"
            :loading="isLoading"
            @search="handleSearch"
            @clear="clearSearch"
          />
        </view>

        <!-- 分类筛选 -->
        <CategoryFilter
          class="category-filter"
          title="分类筛选"
          :category-options="categoryOptions"
          :selected-category="selectedCategory"
          max-width="100%"
          :show-icon="true"
          @change="handleCategoryChange"
          @reset="handleCategoryReset"
        />

        <!-- 书籍列表 -->
        <view class="books-section">
          <view class="section-header">
            <view class="section-title-wrapper">
              <text class="section-title">面试书籍推荐</text>
              <text class="section-subtitle">精心挑选的面试必读书籍</text>
            </view>
          </view>
          <Loading
            v-if="isLoading"
            :visible="isLoading"
            :text="getLoadingText"
            size="medium"
            type="dots"
          />
          <view v-else>
            <!-- 书籍网格 -->
            <view v-if="filteredBooks.length > 0" class="books-grid">
              <view
                v-for="(book, index) in filteredBooks"
                :key="book.id"
                class="book-card"
                :style="{ animationDelay: getAnimationDelay(index) }"
                @click="startReading(book)"
              >
                <!-- 书籍封面 -->
                <view class="book-cover-wrapper">
                  <image class="book-cover" :src="book.cover" mode="aspectFill" />
                  <!-- 免费/ 付费标签 -->
                  <view v-if="book.isFree" class="free-badge">免费</view>
                  <view v-else class="paid-badge">付费</view>
                </view>

                <!-- 书籍信息 -->
                <view class="book-info">
                  <text class="book-title">{{ book.title }}</text>
                  <text class="book-author">{{ book.author }}</text>

                  <view class="book-meta">
                    <view class="difficulty-tag" :class="getDifficultyStyle(book.difficulty)">
                      <text class="difficulty-text">{{ book.difficulty }}</text>
                    </view>
                    <view class="book-stats">
                      <view class="i-mdi-eye stat-icon"></view>
                      <text class="stat-text">{{ (book.readCount / 1000).toFixed(1) }}k</text>
                    </view>
                  </view>

                  <view class="book-footer">
                    <text class="book-price" :class="book.isFree ? 'price-free' : 'price-paid'">
                      {{ book.isFree ? '免费' : `¥${book.price}` }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-state">
              <view class="i-mdi-book-open-blank-variant empty-icon"></view>
              <text class="empty-title">暂无相关书籍</text>
              <text class="empty-desc">尝试调整筛选条件或搜索其他内容</text>
            </view>

            <!-- 加载更多状态 -->
            <view v-if="filteredBooks.length > 0" class="load-more-section">
              <!-- 正在加载更多 -->
              <view v-if="isLoadingMore" class="loading-more">
                <view class="loading-icon">
                  <view class="i-mdi-loading loading-spinner"></view>
                </view>
                <text class="loading-text">正在加载更多书籍...</text>
                <text class="loading-progress">
                  {{ totalPages > 0 ? Math.round((queryParams.page / totalPages) * 100) : 0 }}%
                </text>
              </view>

              <!-- 没有更多数据 -->
              <view v-else-if="!hasMoreData && bookList.length > 0" class="no-more-data">
                <text class="no-more-text">已显示全部书籍</text>
                <text class="total-count">共 {{ bookList.length }} 本书</text>
              </view>

              <!-- 可以加载更多 -->
              <view v-else-if="hasMoreData" class="pull-up-tip">
                <view class="tip-row">
                  <text class="tip-text">上拉加载更多</text>
                  <text class="tip-icon i-mdi-arrow-up"></text>
                </view>
                <text class="loaded-count">已加载 {{ bookList.length }} 本书</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载状态 -->
    <LoadingCard :visible="isInitialLoading" :text="'正在加载页面...'" />
  </view>
</template>

<style scoped lang="scss">
.book-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

// 主要内容区域
.main-content {
  flex: 1;
  background: transparent;
  padding-top: 44px;
}

.content-wrapper {
  padding: 0 20rpx 160rpx;

  &.fade-in {
    animation: fadeIn 0.6s ease-out;
  }
}

// 欢迎区域
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  margin: 20rpx 0 32rpx;
  background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.2);

  .welcome-content {
    flex: 1;

    .welcome-title {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: white;
    }

    .welcome-subtitle {
      font-size: 24rpx;
      line-height: 1.4;
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .welcome-decoration {
    .decoration-icon {
      font-size: 64rpx;
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 阅读统计
.stats-section {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .stats-grid {
    display: flex;
    justify-content: space-around;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .stat-icon-wrapper {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
        border-radius: 16rpx;

        .stat-icon {
          font-size: 32rpx;
          color: #00c9a7;
        }
      }

      .stat-info {
        display: flex;
        flex-direction: column;

        .stat-value {
          font-size: 28rpx;
          font-weight: 700;
          color: #1e293b;
        }

        .stat-label {
          font-size: 22rpx;
          color: #64748b;
        }
      }
    }
  }
}

// 搜索框
.search-section {
  margin-bottom: 32rpx;
}

// 分类筛选
.category-filter {
  margin-bottom: 28rpx;
}

// 书籍区域
.books-section {
  .section-header {
    margin-bottom: 32rpx;

    .section-title-wrapper {
      .section-title {
        display: block;
        margin-bottom: 8rpx;
        font-size: 32rpx;
        font-weight: 700;
        color: #1e293b;
      }

      .section-subtitle {
        font-size: 24rpx;
        line-height: 1.4;
        color: #64748b;
      }
    }
  }

  .books-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;

    .book-card {
      width: calc(50% - 12rpx);
      background: white;
      border-radius: 20rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      opacity: 0;
      transition: all 0.3s ease;
      transform: translateY(30rpx);
      animation: slideInUp 0.6s ease-out forwards;

      &:active {
        transform: scale(0.98);
      }

      .book-cover-wrapper {
        position: relative;
        width: 100%;
        height: 320rpx;
        overflow: hidden;
        background: #f8fafc;

        .book-cover {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .progress-badge {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          padding: 8rpx 16rpx;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          font-size: 22rpx;
          font-weight: 500;
          border-radius: 20rpx;
        }

        .free-badge {
          position: absolute;
          top: 16rpx;
          left: 16rpx;
          padding: 8rpx 16rpx;
          background: #16a34a;
          color: white;
          font-size: 22rpx;
          font-weight: 500;
          border-radius: 20rpx;
        }

        .paid-badge {
          position: absolute;
          top: 16rpx;
          left: 16rpx;
          padding: 8rpx 16rpx;
          background: #f59e0b;
          color: white;
          font-size: 22rpx;
          font-weight: 500;
          border-radius: 20rpx;
        }
      }

      .book-info {
        padding: 20rpx;

        .book-title {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: #1e293b;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .book-author {
          display: block;
          margin-bottom: 16rpx;
          font-size: 24rpx;
          color: #64748b;
        }

        .book-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;

          .difficulty-tag {
            padding: 6rpx 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            border-radius: 12rpx;
          }

          .book-stats {
            display: flex;
            align-items: center;
            gap: 6rpx;

            .stat-icon {
              font-size: 20rpx;
              color: #64748b;
            }

            .stat-text {
              font-size: 20rpx;
              color: #64748b;
            }
          }
        }

        .book-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .book-price {
            font-size: 28rpx;
            font-weight: 600;

            &.price-free {
              color: #16a34a;
            }

            &.price-paid {
              color: #00c9a7;
            }
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      margin-bottom: 32rpx;
      font-size: 120rpx;
      color: #cbd5e1;
    }

    .empty-title {
      margin-bottom: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #475569;
    }

    .empty-desc {
      font-size: 24rpx;
      line-height: 1.5;
      color: #64748b;
      text-align: center;
    }
  }
}

// 加载更多状态指示器样式
.load-more-section {
  padding: 32rpx 0;
  text-align: center;
  animation: slideInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 24rpx;
  background: rgba(0, 201, 167, 0.08);
  border-radius: 12rpx;
  margin: 0 20rpx;

  .loading-icon {
    .loading-spinner {
      font-size: 24rpx;
      color: #00c9a7;
      animation: spin 1s linear infinite;
    }
  }

  .loading-text {
    font-size: 24rpx;
    color: #00c9a7;
    font-weight: 500;
  }

  .loading-progress {
    font-size: 20rpx;
    color: #00c9a7;
    font-weight: 600;
    background: rgba(0, 201, 167, 0.1);
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
  }
}

.no-more-data {
  padding: 24rpx;
  text-align: center;
  color: #999;

  .no-more-text {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    display: block;
  }

  .total-count {
    font-size: 20rpx;
    color: #ccc;
  }
}

.pull-up-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  color: #999;
  animation: breathe 2s ease-in-out infinite;

  .tip-row {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  .tip-text {
    font-size: 22rpx;
    color: #999;
  }

  .tip-icon {
    font-size: 20rpx;
    color: #999;
    animation: bounceVertical 2s ease-in-out infinite;
  }

  .loaded-count {
    font-size: 20rpx;
    color: #ccc;
    margin-top: 4rpx;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 呼吸动画
@keyframes breathe {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

// 垂直弹跳动画
@keyframes bounceVertical {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  60% {
    transform: translateY(-4rpx);
  }
}
</style>
