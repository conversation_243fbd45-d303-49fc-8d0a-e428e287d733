function e(e,r,n){return r=u(r),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return d(e)}(e,t()?Reflect.construct(r,n||[],u(e).constructor):r.apply(e,n))}function t(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean,[],(function(){})))}catch(e){}return(t=function(){return!!e})()}function r(){r=function(){return t};var e,t={},n=Object.prototype,i=n.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof g?t:g,a=Object.create(i.prototype),s=new W(n||[]);return o(a,"_invoke",{value:S(e,r,s)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var v="suspendedStart",h="suspendedYield",p="executing",y="completed",m={};function g(){}function w(){}function b(){}var _={};l(_,s,(function(){return this}));var x=Object.getPrototypeOf,k=x&&x(x(T([])));k&&k!==n&&i.call(k,s)&&(_=k);var j=b.prototype=g.prototype=Object.create(_);function O(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(n,o,a,s){var c=d(e[n],e,o);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==typeof l&&i.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(l).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}var n;o(this,"_invoke",{value:function(e,i){function o(){return new t((function(t,n){r(e,i,t,n)}))}return n=n?n.then(o,o):o()}})}function S(t,r,n){var i=v;return function(o,a){if(i===p)throw new Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===v)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=p;var u=d(t,r,n);if("normal"===u.type){if(i=n.done?y:h,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=y,n.method="throw",n.arg=u.arg)}}}function E(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=d(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function W(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(typeof t+" is not iterable")}return w.prototype=b,o(j,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=l(b,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,u,"GeneratorFunction")),e.prototype=Object.create(j),e},t.awrap=function(e){return{__await:e}},O(M.prototype),l(M.prototype,c,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new M(f(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(j),l(j,u,"Generator"),l(j,s,(function(){return this})),l(j,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,W.prototype={constructor:W,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),u=i.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function n(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}function s(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function f(e){var r="function"==typeof Map?new Map:void 0;return f=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,n)}function n(){return function(e,r,n){if(t())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,r);var o=new(e.bind.apply(e,i));return n&&l(o,n.prototype),o}(e,arguments,u(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),l(n,e)},f(e)}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(){return v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=u(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(arguments.length<3?e:r):i.value}},v.apply(this,arguments)}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||y(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){if(e){if("string"==typeof e)return m(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}function w(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{c(n.next(e))}catch(e){o(e)}}function s(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}c((n=n.apply(e,t||[])).next())}))}function b(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function _(e,t,r,n,i){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r}var x,k,j,O,M,S,E,A;"function"==typeof SuppressedError&&SuppressedError,function(e){e[e.start=0]="start",e[e.intermediate=1]="intermediate",e[e.end=2]="end"}(x||(x={})),function(e){e[e.disconnected=0]="disconnected",e[e.connecting=2]="connecting",e[e.connected=1]="connected"}(k||(k={})),function(e){e[e.start=0]="start",e[e.processing=1]="processing",e[e.stop=2]="stop"}(j||(j={})),function(e){e[e.start=0]="start",e[e.stop=2]="stop"}(O||(O={})),function(e){e[e.append=0]="append",e[e.break=1]="break"}(M||(M={})),function(e){e[e.offline=0]="offline",e[e.realtime=1]="realtime"}(S||(S={})),function(e){e.live="live",e.genneral="genneral"}(E||(E={})),function(e){e.action="action"}(A||(A={}));var P=function(t){function r(t,n,i,a,s){var c;return o(this,r),(c=e(this,r,[t])).name=i,c.code=n,c.request_id=s||"",c.sid=a||"",c}return c(r,f(Error)),s(r)}();function W(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})).replace(/-/g,"")}var T=function(){var e={resolve:function(){},reject:function(){}};return{promise:new Promise((function(t,r){e.resolve=t,e.reject=r})),controller:e}};function I(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce((function(e,t){for(var r in t)if(t.hasOwnProperty(r)){var n=t[r],o=e[r];"object"===i(n)&&null!=n&&"object"===i(o)&&null!=o?Array.isArray(n)?e[r]=p(n):e[r]=I(o,n):e[r]=n}return e}),{})}const C="function"==typeof Buffer,L=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),R=(e=>{let t={};return L.forEach(((e,r)=>t[e]=r)),t})(),U=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,z=String.fromCharCode.bind(String),B=("function"==typeof Uint8Array.from&&Uint8Array.from.bind(Uint8Array),e=>e.replace(/=/g,"").replace(/[+\/]/g,(e=>"+"==e?"-":"_"))),F=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),D=e=>{let t,r,n,i,o="";const a=e.length%3;for(let a=0;a<e.length;){if((r=e.charCodeAt(a++))>255||(n=e.charCodeAt(a++))>255||(i=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=r<<16|n<<8|i,o+=L[t>>18&63]+L[t>>12&63]+L[t>>6&63]+L[63&t]}return a?o.slice(0,a-3)+"===".substring(a):o},N="function"==typeof btoa?e=>btoa(e):C?e=>Buffer.from(e,"binary").toString("base64"):D,q=C?e=>Buffer.from(e).toString("base64"):e=>{let t=[];for(let r=0,n=e.length;r<n;r+=4096)t.push(z.apply(null,e.subarray(r,r+4096)));return N(t.join(""))},V=(e,t=!1)=>t?B(q(e)):q(e),H=e=>{if(e=e.replace(/\s+/g,""),!U.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,r,n,i="";for(let o=0;o<e.length;)t=R[e.charAt(o++)]<<18|R[e.charAt(o++)]<<12|(r=R[e.charAt(o++)])<<6|(n=R[e.charAt(o++)]),i+=64===r?z(t>>16&255):64===n?z(t>>16&255,t>>8&255):z(t>>16&255,t>>8&255,255&t);return i},G=V;var K,$,Z,J={code:"600000",message:"必要参数缺失"},X={code:"600003",message:"连接异常"},Y={code:"600004",message:"无效的响应"},Q={code:"999999",message:"未知错误"},ee={EmptyStreamError:{code:"700000",message:"无效的流数据"},PlayNotAllowed:{code:"700006",message:"播放不允许"},MissingPlayerLibsError:{code:"700001",message:"缺失播放插件"},H264NotSupported:{code:"700002",message:"当前设备不支持 H.264"},Unknown:{code:"700005",message:"播放失败"}},te={code:"800000",message:"不支持的环境"},re={code:"800001",message:"未找到指定约束的设备"},ne={code:"800002",message:"设备访问权限异常/无法请求使用源设备"},ie={code:"800003",message:"暂时无法访问摄像头/麦克风，请确保当前没有其他应用请求访问设备，并重试"},oe={code:"800004",message:"无效的设备请求参数"},ae={code:"800005",message:"未知原因操作已终止"},se={code:"800006",message:"当前页面未处于激活状态"},ce={code:"800007",message:"页面未发生用户交互，请求被终止"};!function(e){e.InvalidParam="InvalidParam",e.InvalidResponse="InvalidResponse",e.ContextError="ContextError",e.NetworkError="NetworkError",e.ConnectError="ConnectError",e.InvalidConnect="InvalidConnect",e.MediaError="MediaError",e.UserMediaError="UserMediaError"}(K||(K={})),function(e){e[e.verbose=0]="verbose",e[e.debug=1]="debug",e[e.info=2]="info",e[e.warn=3]="warn",e[e.error=4]="error",e[e.none=5]="none"}($||($={}));var ue=function(){function e(){o(this,e),Z.set(this,$.warn)}return s(e,[{key:"setLogLevel",value:function(e){_(this,Z,e,"f")}},{key:"record",value:function(e){var t,r,n,i,o;if(e>=b(this,Z,"f")){for(var a=arguments.length,s=new Array(a>1?a-1:0),c=1;c<a;c++)s[c-1]=arguments[c];switch(e){case $.verbose:(t=console).log.apply(t,["[SDK] [VERBOSE] "].concat(s));break;case $.debug:(r=console).log.apply(r,["[SDK] [DEBUG] "].concat(s));break;case $.info:(n=console).log.apply(n,["[SDK] [INFO] "].concat(s));break;case $.warn:(i=console).warn.apply(i,["[SDK] [WARN] "].concat(s));break;case $.error:(o=console).error.apply(o,["[SDK] [ERROR] "].concat(s))}}}}]),e}();Z=new WeakMap;var le,fe,de,ve,he,pe,ye,me,ge,we,be,_e,xe,ke=new ue,je=function(){function e(t,r){var n=this;o(this,e),fe.set(this,void 0),de.set(this,le.CLOSED),ve.set(this,"web"),he.set(this,void 0),pe.set(this,void 0),ye.set(this,void 0),me.set(this,void 0),"undefined"!=typeof wx&&wx.env&&_(this,ve,"miniprogram","f"),ke.record($.debug,"[ws]",b(this,ve,"f"),t),_(this,fe,void 0,"f"),_(this,de,le.CONNECTING,"f"),"miniprogram"===b(this,ve,"f")?(_(this,fe,wx.connectSocket({url:encodeURI(t)}),"f"),b(this,fe,"f").onOpen((function(){var e,t;ke.record($.debug,"[ws]","channel open"),_(n,de,le.OPEN,"f");for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=b(n,he,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(i))})),b(this,fe,"f").onMessage((function(){for(var e,t,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=b(n,pe,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(i))})),b(this,fe,"f").onClose((function(){var e,t;ke.record($.debug,"[ws]","channel closed"),_(n,de,le.CLOSED,"f");for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=b(n,me,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(i))})),b(this,fe,"f").onError((function(e){var t;ke.record($.error,"[ws]","channel error",e),null===(t=b(n,ye,"f"))||void 0===t||t.call(n,e)}))):(_(this,fe,new WebSocket(t),"f"),(null==r?void 0:r.binaryData)&&(ke.record($.debug,"[ws]","binaryType:ab"),b(this,fe,"f").binaryType="arraybuffer"),b(this,fe,"f").onopen=function(){var e,t;ke.record($.debug,"[ws]","channel open");for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=b(n,he,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(i))},b(this,fe,"f").onmessage=function(){for(var e,t,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=b(n,pe,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(i))},b(this,fe,"f").onclose=function(){var e,t;ke.record($.debug,"[ws]","channel closed");for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=b(n,me,"f"))||void 0===t||(e=t).call.apply(e,[n].concat(i))},b(this,fe,"f").onerror=function(e){var t;ke.record($.error,"[ws]","channel error",e),null===(t=b(n,ye,"f"))||void 0===t||t.call(n,e)})}return s(e,[{key:"readyState",get:function(){return"miniprogram"===b(this,ve,"f")?b(this,de,"f"):b(this,fe,"f").readyState}},{key:"onopen",set:function(e){_(this,he,e,"f")}},{key:"onmessage",set:function(e){_(this,pe,e,"f")}},{key:"onclose",set:function(e){_(this,me,e,"f")}},{key:"onerror",set:function(e){_(this,ye,e,"f")}},{key:"send",value:function(e){"miniprogram"===b(this,ve,"f")?b(this,fe,"f").send({data:e}):b(this,fe,"f").send(e)}},{key:"close",value:function(e){var t,r,n,i;_(this,de,le.CLOSING,"f"),ke.record($.debug,"[ws]","close channel",null!==(t=null==e?void 0:e.code)&&void 0!==t?t:"",null!==(r=null==e?void 0:e.reason)&&void 0!==r?r:""),"miniprogram"===b(this,ve,"f")?null===(n=b(this,fe,"f"))||void 0===n||n.close(e):null===(i=b(this,fe,"f"))||void 0===i||i.close(null==e?void 0:e.code,null==e?void 0:e.reason)}}]),e}();function Oe(e,t){var r,n=!1;return{abort:function(){var e;n=!0,clearTimeout(undefined),r&&(r.onerror=null,r.onopen=null,r.onmessage=null,null===(e=r.close)||void 0===e||e.call(r))},instablishPromise:new Promise((function(i,o){try{var a=0;(r=new je(e,t)).onopen=function(){r.onerror=null,r.onopen=null,a=setTimeout((function(){n?r.close():i(r)}),50)},r.onclose=function(e){clearTimeout(a),r.onerror=null,r.onopen=null,r.onclose=null,n||o(e)},r.onerror=function(e){clearTimeout(a),r.onerror=null,r.onopen=null,r.onclose=null,n||o(e)}}catch(e){o(e)}}))}}le=je,fe=new WeakMap,de=new WeakMap,ve=new WeakMap,he=new WeakMap,pe=new WeakMap,ye=new WeakMap,me=new WeakMap,je.CONNECTING=0,je.OPEN=1,je.CLOSING=2,je.CLOSED=3;var Me,Se,Ee,Ae,Pe,We,Te,Ie,Ce,Le,Re,Ue,ze,Be,Fe,De,Ne,qe,Ve=0,He=function(){function e(t){var r,n=this;o(this,e),ge.set(this,Ve),we.set(this,{}),be.set(this,[]),_e.set(this,(function(e,t,r){if("function"!=typeof t)throw TypeError("listener must be a function");-1===b(n,be,"f").indexOf(e)&&b(n,be,"f").push(e),b(n,we,"f")[e]=b(n,we,"f")[e]||[],b(n,we,"f")[e].push({once:r||!1,fn:t})})),xe.set(this,(function(e,t){var r=b(n,we,"f")[e],i=[];null==r||r.forEach((function(e,r){e.fn.apply(null,t),e.once&&i.unshift(r)})),null==i||i.forEach((function(e){r.splice(e,1)}))})),_(this,ge,null!==(r=null==t?void 0:t.emitDelay)&&void 0!==r?r:Ve,"f")}return s(e,[{key:"on",value:function(e,t){return b(this,_e,"f").call(this,e,t,!1),this}},{key:"once",value:function(e,t){return b(this,_e,"f").call(this,e,t,!0),this}},{key:"off",value:function(e,t){var r=b(this,be,"f").indexOf(e);if(e&&-1!==r)if(t){var n=[],i=b(this,we,"f")[e];null==i||i.forEach((function(e,r){e.fn===t&&n.unshift(r)})),null==n||n.forEach((function(e){i.splice(e,1)})),i.length||(b(this,be,"f").splice(r,1),delete b(this,we,"f")[e])}else delete b(this,we,"f")[e],b(this,be,"f").splice(r,1);return this}},{key:"removeAllListeners",value:function(){return _(this,we,{},"f"),_(this,be,[],"f"),this}},{key:"emit",value:function(e){for(var t=this,r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];b(this,ge,"f")?setTimeout((function(){b(t,xe,"f").call(t,e,n)}),b(this,ge,"f")):b(this,xe,"f").call(this,e,n)}},{key:"emitSync",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];b(this,xe,"f").call(this,e,r)}},{key:"destroy",value:function(){_(this,we,{},"f"),_(this,be,[],"f")}}]),e}();ge=new WeakMap,we=new WeakMap,be=new WeakMap,_e=new WeakMap,xe=new WeakMap,function(e){e.connected="connected",e.disconnected="disconnected",e.nlp="nlp",e.asr="asr",e.stream_start="stream_start",e.frame_start="frame_start",e.frame_stop="frame_stop",e.action_start="action_start",e.action_stop="action_stop",e.tts_duration="tts_duration",e.subtitle_info="subtitle_info",e.error="error"}(Me||(Me={})),function(e){e.play="play",e.waiting="waiting",e.playing="playing",e.stop="stop",e.playNotAllowed="not-allowed",e.error="error"}(Se||(Se={}));var Ge,Ke=function(t){function n(){var t;return o(this,n),t=e(this,n),Ee.set(d(t),void 0),Ae.set(d(t),"xrtc"),Pe.set(d(t),void 0),We.set(d(t),!1),Te.set(d(t),1),Ie.set(d(t),"center"),Ce.set(d(t),void 0),Le.set(d(t),void 0),Re.set(d(t),void 0),Ue.set(d(t),void 0),ze.set(d(t),{width:1080,height:1920}),Be.set(d(t),1),Fe.set(d(t),(function(){return w(d(t),void 0,void 0,r().mark((function e(){var t,n,i,o,a,s,c=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(b(this,Ee,"f")&&b(this,Ee,"f").destroy(),n=void 0,e.prev=2,"xrtc"!==b(this,Ae,"f")){e.next=11;break}return e.next=6,import("./xrtc-player-BDn4MFE-.js");case 6:i=e.sent,o=i.XRTCPlayer,_(this,Ee,new o,"f"),e.next=17;break;case 11:if("webrtc"!==b(this,Ae,"f")){e.next=17;break}return e.next=14,import("./webrtc-player-6-MVvVqH.js");case 14:a=e.sent,s=a.WebRTCPlayer,_(this,Ee,new s,"f");case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(2),n=new P(ee.MissingPlayerLibsError.message,ee.MissingPlayerLibsError.code,K.MediaError);case 22:if(!n){e.next=24;break}return e.abrupt("return",Promise.reject(n));case 24:null===(t=b(this,Ee,"f"))||void 0===t||t.on(Se.play,(function(){c.emit(Se.play)})).on(Se.waiting,(function(){c.emit(Se.waiting)})).on(Se.playing,(function(){c.emit(Se.playing)})).on(Se.playNotAllowed,(function(){c.emit(Se.playNotAllowed)})).on(Se.stop,(function(){c.emit(Se.stop)})).on(Se.error,(function(e){c.emit(Se.error,e)}));case 25:case"end":return e.stop()}}),e,this,[[2,19]])})))})),De.set(d(t),(function(){var e,r;if(!b(d(t),Re,"f")){var n=_(d(t),Re,document.createElement("div"),"f");n.setAttribute("id","xvideo"),n.style.position="relative",n.style.width="100%",n.style.height="100%",n.style.minWidth="100%",n.style.minHeight="100%",n.style.pointerEvents="none",null===(e=b(d(t),Le,"f"))||void 0===e||e.appendChild(n)}if(!b(d(t),Ue,"f")){var i=_(d(t),Ue,document.createElement("div"),"f");i.style.position="absolute",t.resize(),b(d(t),Re,"f").appendChild(i),window.addEventListener("resize",b(d(t),Ne,"f"))}if(b(d(t),Le,"f"))try{null===(r=b(d(t),Pe,"f"))||void 0===r||r.observe(b(d(t),Le,"f"))}catch(e){}})),Ne.set(d(t),(function(){var e,r,n=b(d(t),ze,"f"),i=n.width,o=n.height,a=(null===(e=b(d(t),Re,"f"))||void 0===e?void 0:e.offsetWidth)||0,s=(null===(r=b(d(t),Re,"f"))||void 0===r?void 0:r.offsetHeight)||0;if(b(d(t),Ue,"f")){var c=1;c=a/s>i/o?s/o:a/i,b(d(t),Ue,"f").style.left="50%";var u="-50%";"bottom"===b(d(t),Ie,"f")?(u="0",b(d(t),Ue,"f").style.bottom="0px",b(d(t),Ue,"f").style.transformOrigin="center bottom"):(b(d(t),Ue,"f").style.top="50%",b(d(t),Ue,"f").style.transformOrigin="center center");var l=b(d(t),Be,"f")*c;b(d(t),Ue,"f").style.transform="translate3d(-50%,".concat(u,",0) scale(").concat(l,", ").concat(c,")")}})),qe.set(d(t),(function(){var e;if(b(d(t),Le,"f"))try{null===(e=b(d(t),Pe,"f"))||void 0===e||e.unobserve(b(d(t),Le,"f"))}catch(e){}window.removeEventListener("resize",b(d(t),Ne,"f")),b(d(t),Ue,"f")&&(b(d(t),Ue,"f").remove(),_(d(t),Ue,void 0,"f")),b(d(t),Re,"f")&&(b(d(t),Re,"f").remove(),_(d(t),Re,void 0,"f"))})),void 0!==window.ResizeObserver&&_(d(t),Pe,new ResizeObserver((function(e){e.forEach((function(){var e;null===(e=b(d(t),Ne,"f"))||void 0===e||e.call(d(t))}))})),"f"),t}return c(n,He),s(n,[{key:"renderAlign",set:function(e){_(this,Ie,e,"f")}},{key:"playerType",set:function(e){_(this,Ae,e,"f")}},{key:"muted",get:function(){var e,t=b(this,We,"f");return b(this,Ee,"f")&&(t=null===(e=b(this,Ee,"f"))||void 0===e?void 0:e.muted),t},set:function(e){_(this,We,e,"f"),b(this,Ee,"f")&&(e?b(this,Ee,"f").muted=!0:(b(this,Ee,"f").muted=!1,b(this,Ee,"f").resume()))}},{key:"volume",get:function(){var e;return(null===(e=b(this,Ee,"f"))||void 0===e?void 0:e.volume)||b(this,Te,"f")},set:function(e){e>1&&(e=1),_(this,Te,e,"f"),b(this,Ee,"f")&&(b(this,Ee,"f").volume=e)}},{key:"stream",set:function(e){_(this,Ce,e,"f"),b(this,Ee,"f")&&(b(this,Ee,"f").stream=e)}},{key:"container",set:function(e){var t;if(b(this,Le,"f"))try{null===(t=b(this,Pe,"f"))||void 0===t||t.unobserve(b(this,Le,"f"))}catch(e){}_(this,Le,e,"f")}},{key:"videoSize",set:function(e){_(this,ze,e,"f")}},{key:"playStream",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return _(this,Ce,e,"f"),b(this,De,"f").call(this),t.next=5,b(this,Fe,"f").call(this);case 5:return b(this,Ee,"f")&&(b(this,Ee,"f")&&(b(this,Ee,"f").stream=e),b(this,Ee,"f").videoWrapper=b(this,Ue,"f")),t.prev=6,t.next=9,null===(n=b(this,Ee,"f"))||void 0===n?void 0:n.play();case 9:t.next=15;break;case 11:t.prev=11,t.t0=t.catch(6),this.stop();case 15:case"end":return t.stop()}}),t,this,[[6,11]])})))}},{key:"resume",value:function(){return w(this,void 0,void 0,r().mark((function e(){var t=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!b(this,Ee,"f")){e.next=2;break}return e.abrupt("return",b(this,Ee,"f").resume().then((function(){b(t,Ee,"f")&&(b(t,Ee,"f").muted=!1)})));case 2:return e.abrupt("return",Promise.reject("player not found"));case 3:case"end":return e.stop()}}),e,this)})))}},{key:"stop",value:function(){var e;b(this,qe,"f").call(this),null===(e=b(this,Ee,"f"))||void 0===e||e.stop()}},{key:"scaleX",get:function(){return b(this,Be,"f")||1},set:function(e){_(this,Be,e,"f"),b(this,Ne,"f").call(this)}},{key:"setSinkId",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,null===(n=b(this,Ee,"f"))||void 0===n?void 0:n.setSinkId(e);case 2:case"end":return t.stop()}}),t,this)})))}},{key:"getSinkId",value:function(){return w(this,void 0,void 0,r().mark((function e(){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(null===(t=b(this,Ee,"f"))||void 0===t?void 0:t.getSinkId())||"");case 1:case"end":return e.stop()}}),e,this)})))}},{key:"destroy",value:function(){var e;try{null===(e=b(this,Pe,"f"))||void 0===e||e.disconnect()}catch(e){}_(this,Pe,void 0,"f"),this.stop(),v(u(n.prototype),"destroy",this).call(this)}},{key:"resize",value:function(){var e;b(this,Ue,"f")&&(b(this,Ue,"f").style.width="".concat(b(this,ze,"f").width,"px"),b(this,Ue,"f").style.height="".concat(b(this,ze,"f").height,"px")),null===(e=b(this,Ee,"f"))||void 0===e||e.resize(),b(this,Ne,"f").call(this)}}],[{key:"getVersion",value:function(){return"3.1.1-1011"}},{key:"setLogLevel",value:function(e){ke.setLogLevel(e)}}]),n}();function $e(){var e={transF32ToRawData:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16e3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:16e3,i=e.transSamplingRate(t,r,n);return e.transF32ToS16(i).buffer},transSamplingRate:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:44100,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:16e3;if(t===r)return e;var n=Math.round(e.length*(r/t)),i=new Float32Array(n),o=(e.length-1)/(n-1);i[0]=e[0];for(var a=1;a<n-1;a++){var s=a*o,c=Number(Math.floor(s).toFixed()),u=Number(Math.ceil(s).toFixed()),l=s-c;i[a]=e[c]+(e[u]-e[c])*l}return i[n-1]=e[e.length-1],i},transF32ToS16:function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r]<0?32768*e[r]:32767*e[r];t.push(n)}return new Int16Array(t)}};self.onmessage=function(t){var r=t.data,n=r.audio,i=r.sampleRate,o=void 0===i?16e3:i,a=r.destSampleRate,s=void 0===a?16e3:a;try{var c=e.transF32ToRawData(n,o,s);self.postMessage({data:c})}catch(t){self.postMessage({error:{code:t.type,message:t.message}})}}}Ee=new WeakMap,Ae=new WeakMap,Pe=new WeakMap,We=new WeakMap,Te=new WeakMap,Ie=new WeakMap,Ce=new WeakMap,Le=new WeakMap,Re=new WeakMap,Ue=new WeakMap,ze=new WeakMap,Be=new WeakMap,Fe=new WeakMap,De=new WeakMap,Ne=new WeakMap,qe=new WeakMap,function(e){e.recoder_audio="recoder_audio",e.ended="ended",e.mute="mute",e.unmute="unmute",e.error="error",e.deviceAutoSwitched="device-auto-switched"}(Ge||(Ge={}));var Ze,Je,Xe,Ye,Qe,et,tt,rt,nt,it,ot,at,st,ct,ut,lt,ft,dt,vt,ht,pt,yt,mt,gt,wt,bt,_t,xt,kt,jt,Ot,Mt,St,Et=function(){function e(){o(this,e)}return s(e,null,[{key:"requestPermissions",value:function(t){return w(this,void 0,void 0,r().mark((function n(){var i,o,a;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:i="",r.t0=t,r.next="audioinput"===r.t0||"audiooutput"===r.t0?4:"videoinput"===r.t0?6:8;break;case 4:return i="microphone",r.abrupt("break",8);case 6:return i="camera",r.abrupt("break",8);case 8:if(!i){r.next=22;break}if(o="prompt",!navigator.permissions){r.next=17;break}return r.next=13,navigator.permissions.query({name:i});case 13:if(a=r.sent,"denied"!==(o=a.state)){r.next=17;break}return r.abrupt("return",Promise.reject(new P(ne.message,ne.code,K.UserMediaError)));case 17:if("prompt"!==o){r.next=22;break}return r.next=20,e.getUserMedia({video:"camera"===i,audio:"microphone"===i});case 20:r.sent.getTracks().forEach((function(e){e.stop()}));case 22:case"end":return r.stop()}}),n)})))}},{key:"getEnumerateDevices",value:function(t){return w(this,void 0,void 0,r().mark((function n(){var i;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){r.next=2;break}return r.abrupt("return",Promise.reject(new P(te.message,te.code,K.UserMediaError)));case 2:return r.next=4,e.requestPermissions(t);case 4:return r.next=6,navigator.mediaDevices.enumerateDevices().then((function(e){return e.filter((function(e){return e.kind===t&&e.deviceId}))})).catch((function(e){return Promise.reject(new P(e.message||e.name||se.message,se.code,K.UserMediaError))}));case 6:return i=r.sent,r.abrupt("return",i);case 8:case"end":return r.stop()}}),n)})))}},{key:"getUserMedia",value:function(e){return w(this,void 0,void 0,r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,navigator.mediaDevices.getUserMedia(e).catch((function(e){var t=new P(ae.message,ae.code,K.UserMediaError);switch(null==e?void 0:e.name){case"NotAllowedError":t=new P(ne.message,ne.code,K.UserMediaError);break;case"SecurityError":t=new P(te.message,te.code,K.UserMediaError);break;case"NotReadableError":t=new P(ie.message,ie.code,K.UserMediaError);break;case"NotFoundError":t=new P(re.message,re.code,K.UserMediaError);break;case"OverconstrainedError":t=new P(oe.message,oe.code,K.UserMediaError)}return Promise.reject(t)}));case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})))}}]),e}(),At=function(t){function n(t){var i,a;return o(this,n),a=e(this,n),Ze.add(d(a)),Je.set(d(a),!1),Xe.set(d(a),new window.AudioContext),Ye.set(d(a),void 0),Qe.set(d(a),void 0),et.set(d(a),[]),tt.set(d(a),{sampleRate:16e3,analyser:!1}),rt.set(d(a),void 0),nt.set(d(a),void 0),it.set(d(a),void 0),ot.set(d(a),void 0),at.set(d(a),void 0),st.set(d(a),void 0),ct.set(d(a),void 0),ut.set(d(a),0),lt.set(d(a),!1),ft.set(d(a),!1),dt.set(d(a),12e4),vt.set(d(a),void 0),ht.set(d(a),!1),pt.set(d(a),!1),yt.set(d(a),void 0),mt.set(d(a),void 0),gt.set(d(a),(function(){var e;if(!b(d(a),ct,"f"))try{var t=URL.createObjectURL(new Blob([(null===(e=$e.toLocaleString().match(/(?:\/\*[\s\S]*?\*\/|\/\/.*?\r?\n|[^{])+\{([\s\S]*)\}$/))||void 0===e?void 0:e[1])||""]));_(d(a),ct,new Worker(t),"f"),URL.revokeObjectURL(t),b(d(a),ct,"f").onmessage=function(e){var t,r;b(d(a),ut,"f")>0&&_(d(a),ut,(r=b(d(a),ut,"f"),--r),"f");var o=e.data.data;v((i=d(a),u(n.prototype)),"emitSync",i).call(i,Ge.recoder_audio,{s16buffer:o,frameStatus:b(d(a),lt,"f")?b(d(a),ht,"f")&&0===b(d(a),ut,"f")?x.end:x.intermediate:x.start,fullDuplex:null!==(t=b(d(a),pt,"f"))&&void 0!==t&&t,extend:Object.assign({sampleRate:a.sampleRate},I(b(d(a),yt,"f")||{},{}))}),b(d(a),lt,"f")||b(d(a),ht,"f")||_(d(a),lt,!0,"f")},b(d(a),ct,"f").onerror=function(e){b(d(a),wt,"f").call(d(a)),ke.record($.error,"[audioWorker]",e)}}catch(e){ke.record($.error,"[prepareAudioWorker]",e)}if(!b(d(a),ct,"f"))return Promise.reject(new P(ae.message,ae.code,K.UserMediaError))})),wt.set(d(a),(function(){var e,t;_(d(a),ut,0,"f"),null===(t=null===(e=b(d(a),ct,"f"))||void 0===e?void 0:e.terminate)||void 0===t||t.call(e),_(d(a),ct,void 0,"f")})),bt.set(d(a),(function(){b(d(a),st,"f")||(_(d(a),st,b(d(a),Xe,"f").createScriptProcessor(4096,1,1),"f"),b(d(a),st,"f").onaudioprocess=function(e){var t,r=e.inputBuffer.getChannelData(0).slice(0);null===(t=b(d(a),ct,"f"))||void 0===t||t.postMessage({audio:r,sampleRate:b(d(a),Xe,"f").sampleRate,destSampleRate:b(d(a),tt,"f").sampleRate||16e3}),_(d(a),ut,b(d(a),ut,"f")+1,"f")}),b(d(a),Ye,"f")||(_(d(a),Ye,b(d(a),Xe,"f").createAnalyser(),"f"),b(d(a),Ye,"f").fftSize=2048,_(d(a),Qe,new Uint8Array(b(d(a),Ye,"f").frequencyBinCount),"f"))})),_t.set(d(a),(function(){a.emitSync(Ge.mute)})),xt.set(d(a),(function(){a.emitSync(Ge.unmute)})),kt.set(d(a),(function(){b(d(a),jt,"f").call(d(a)),a.emitSync(Ge.ended),b(d(a),pt,"f")?(a.stopRecord(),a.startRecord(0,b(d(a),mt,"f"),b(d(a),yt,"f")).then((function(){a.emit(Ge.deviceAutoSwitched)})).catch((function(e){a.emitSync(Ge.error,e)}))):(a.stopRecord(),a.emitSync(Ge.error,new P(ne.message,ne.code,K.UserMediaError)))})),jt.set(d(a),(function(){var e,t,r;null===(e=b(d(a),it,"f"))||void 0===e||e.removeEventListener("mute",b(d(a),_t,"f")),null===(t=b(d(a),it,"f"))||void 0===t||t.removeEventListener("unmute",b(d(a),_t,"f")),null===(r=b(d(a),it,"f"))||void 0===r||r.removeEventListener("ended",b(d(a),_t,"f"))})),Ot.set(d(a),(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return w(d(a),[].concat(t),void 0,(function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;return r().mark((function o(){var a,s,c,u,l;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(s=t<=0,b(e,bt,"f").call(e),b(e,st,"f")){r.next=5;break}return ke.record($.warn,"none scriptProcessor"),r.abrupt("return");case 5:return _(e,ot,new Promise((function(t){_(e,at,{resolve:t},"f")})),"f"),r.next=8,Et.getUserMedia({audio:{noiseSuppression:!0,echoCancellation:!0,autoGainControl:!0},video:!1});case 8:c=r.sent,_(e,nt,c,"f"),(u=_(e,it,c.getAudioTracks()[0],"f")).addEventListener("mute",b(e,_t,"f")),u.addEventListener("unmute",b(e,xt,"f")),u.addEventListener("ended",b(e,kt,"f")),c.addEventListener("addtrack",(function(){ke.record($.verbose,"addtrack")})),c.addEventListener("removetrack",(function(){ke.record($.verbose,"removetrack")})),_(e,lt,!1,"f"),_(e,ht,!1,"f"),_(e,rt,b(e,Xe,"f").createMediaStreamSource(c),"f"),l=[],(null===(a=b(e,tt,"f"))||void 0===a?void 0:a.analyser)&&b(e,Ye,"f")&&l.push(b(e,Ye,"f")),b(e,st,"f")&&l.push(b(e,st,"f")),b(e,Ze,"m",Mt).call(e,l),_(e,et,l,"f"),_(e,ft,!0,"f"),_(e,pt,s,"f"),_(e,yt,I({nlp:!0},i||{}),"f"),_(e,mt,n,"f"),s||_(e,vt,setTimeout((function(){e.stopRecord()}),t||b(e,dt,"f")),"f");case 29:case"end":return r.stop()}}),o)}))()}))})),_(d(a),tt,Object.assign(Object.assign({},b(d(a),tt,"f")),t),"f"),a}return c(n,He),s(n,[{key:"recording",get:function(){return b(this,ft,"f")||!1}},{key:"byteTimeDomainData",get:function(){var e,t;if(b(this,ft,"f")){if(b(this,Ye,"f"))return b(this,Qe,"f")||_(this,Qe,new Uint8Array((null===(e=b(this,Ye,"f"))||void 0===e?void 0:e.frequencyBinCount)||0),"f"),null===(t=b(this,Ye,"f"))||void 0===t||t.getByteTimeDomainData(b(this,Qe,"f")),b(this,Qe,"f");ke.record($.error,"none analyser inited")}}},{key:"startRecord",value:function(e,t,n){return w(this,void 0,void 0,r().mark((function i(){var o,a,s=this;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!b(this,ft,"f")){r.next=3;break}return ke.record($.warn,"[recorder]","conflicted recorder start"),r.abrupt("return");case 3:if(window.isSecureContext){r.next=5;break}return r.abrupt("return",Promise.reject(new P(te.message,te.code,K.UserMediaError)));case 5:return r.next=7,new Promise((function(e,t){b(s,Xe,"f").resume().then(e).catch((function(e){ke.record($.error,"[resume]",e),t(new P(ae.message,ae.code,K.UserMediaError))})),setTimeout((function(){t(new P(ce.message,ce.code,K.UserMediaError))}),1500)}));case 7:return b(this,gt,"f").call(this),r.prev=8,r.next=11,b(this,Ot,"f").call(this,e,t,n);case 11:r.next=17;break;case 13:throw r.prev=13,r.t0=r.catch(8),_(this,ft,!1,"f"),r.t0;case 17:return r.prev=17,null===(a=null===(o=b(this,at,"f"))||void 0===o?void 0:o.resolve)||void 0===a||a.call(o),r.finish(17);case 20:case"end":return r.stop()}}),i,this,[[8,13,17,20]])})))}},{key:"stopRecord",value:function(){var e=this,t=Object.create(null,{emitSync:{get:function(){return v(u(n.prototype),"emitSync",e)}}});return w(this,arguments,void 0,(function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return r().mark((function i(){var o,a,s,c,u,l,f;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,b(e,ot,"f");case 2:if(b(e,ft,"f")){r.next=4;break}return r.abrupt("return");case 4:for(clearTimeout(b(e,vt,"f")),_(e,ft,!1,"f"),b(e,jt,"f").call(e),u=null===(o=b(e,nt,"f"))||void 0===o?void 0:o.getAudioTracks(),l=0,f=(null==u?void 0:u.length)||0;l<f;l++)null===(a=null==u?void 0:u[l])||void 0===a||a.stop();_(e,ht,!0,"f"),!0!==n&&0!==b(e,ut,"f")||(t.emitSync.call(e,Ge.recoder_audio,{s16buffer:new ArrayBuffer(2),frameStatus:x.end,fullDuplex:null!==(s=b(e,pt,"f"))&&void 0!==s&&s,extend:Object.assign({sampleRate:e.sampleRate},I(b(e,yt,"f")||{},{}))}),!0===n&&b(e,wt,"f").call(e));try{b(e,Ze,"m",St).call(e,b(e,et,"f"))}catch(e){ke.record($.warn,"[disconnect media]",e)}finally{_(e,et,[],"f")}null===(c=b(e,mt,"f"))||void 0===c||c.call(e),_(e,mt,void 0,"f");case 14:case"end":return r.stop()}}),i)}))()}))}},{key:"switchDevice",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n,i;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(b(this,st,"f")){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,Et.getUserMedia({audio:{deviceId:{exact:e},noiseSuppression:!0,echoCancellation:!0},video:!1});case 4:i=t.sent,b(this,Ze,"m",St).call(this,b(this,et,"f")),b(this,jt,"f").call(this),null===(n=b(this,nt,"f"))||void 0===n||n.getAudioTracks().forEach((function(e){return e.stop()})),_(this,nt,i,"f"),_(this,rt,b(this,Xe,"f").createMediaStreamSource(i),"f"),b(this,Ze,"m",Mt).call(this,b(this,et,"f"));case 11:case"end":return t.stop()}}),t,this)})))}},{key:"destroy",value:function(){_(this,Je,!0,"f"),this.stopRecord(),v(u(n.prototype),"destroy",this).call(this)}},{key:"isDestroyed",value:function(){return b(this,Je,"f")}},{key:"sampleRate",get:function(){return b(this,tt,"f").sampleRate||16e3}}],[{key:"getVersion",value:function(){return"3.1.1-1011"}},{key:"setLogLevel",value:function(e){ke.setLogLevel(e)}}]),n}();Je=new WeakMap,Xe=new WeakMap,Ye=new WeakMap,Qe=new WeakMap,et=new WeakMap,tt=new WeakMap,rt=new WeakMap,nt=new WeakMap,it=new WeakMap,ot=new WeakMap,at=new WeakMap,st=new WeakMap,ct=new WeakMap,ut=new WeakMap,lt=new WeakMap,ft=new WeakMap,dt=new WeakMap,vt=new WeakMap,ht=new WeakMap,pt=new WeakMap,yt=new WeakMap,mt=new WeakMap,gt=new WeakMap,wt=new WeakMap,bt=new WeakMap,_t=new WeakMap,xt=new WeakMap,kt=new WeakMap,jt=new WeakMap,Ot=new WeakMap,Ze=new WeakSet,Mt=function(e){var t,r;if(!e.length)return _(this,et,[],"f"),null===(t=b(this,rt,"f"))||void 0===t?void 0:t.connect(b(this,Xe,"f").destination);null===(r=b(this,rt,"f"))||void 0===r||r.connect(e[0]);for(var n=1;n<e.length;n++)e[n-1].connect(e[n]);e[e.length-1].connect(b(this,Xe,"f").destination)},St=function(e){var t,r;if(!(null==e?void 0:e.length))return null===(t=b(this,rt,"f"))||void 0===t?void 0:t.disconnect(b(this,Xe,"f").destination);null===(r=b(this,rt,"f"))||void 0===r||r.disconnect(e[0]);for(var n=1;n<e.length;n++)e[n-1].disconnect(e[n]);e[e.length-1].disconnect(b(this,Xe,"f").destination)};var Pt=["src","img","video","link","txt","action","cmd","options","h5_url"];function Wt(e){var t=!1;return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e&&"[object String]"===Object.prototype.toString.call(e)?e.replace(/\[={0,1}([a-zA-Z_-])+\d*:?-?\d*\]/g,""):e||""}(e).replace(/(\[打招呼\])|(\[鞠躬\])|(\[左手点赞\])|(\[右手点赞\])|(\[双手比心\])|(\[拜拜\])|(\[看上边摄像头\])|(\[放交通卡\])|(\[左边出口\])|(\[右边出口\])|(\[左上内容-单手\])|(\[左中内容-单手\])|(\[左下内容-单手\])|(\[右上内容-单手\])|(\[右中内容-单手\])|(\[右下内容-单手\])|(\[左上内容-双手\])|(\[左中内容-双手\])|(\[左下内容-双手\])|(\[右上内容-双手\])|(\[右中内容-双手\])|(\[右下内容-双手\])|(\[展开双手\])|(\[聆听点头\])|(\[轻微摇头\])|(\[双手放下\])/g,"").replace(/\[\[(\w+)=(((?!\]\]).)+)\]\]/g,(function(e,t){return-1===Pt.indexOf(t)?"":e})).replace(/(\[\[txt=[^\[\]]+\]\])|(\[\[cmd=[^\[\]]+\]\])|(\[\[action=[^\[\]]+\]\])|(\[\[txt=(((?!\]\]).)+)\]\])/g,"").replace(/\[\[link=([^\[\]]+)\]\]/g,(function(e,t){return'<a class="llm-content-link" target="_blank"  href="'.concat(encodeURI(t),'">').concat(t,"</a>")})).replace(/\[\[h5_url=([^\[\]]+)\]\]/g,(function(e,t){return'<div class="llm-content-iframe"><iframe class="content-iframe" src="'.concat(encodeURI(t),'" frameborder="no" border="0" marginwidth="0" marginheight="0" allowtransparency="yes"></iframe></div>')})).replace(/\[\[(src|img)=(((?!\]\]).)+)\]\]/g,(function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=r.split(";");r=n[0].replace("ceph.xfyousheng.com","ossbj.xfinfr.com");for(var i={},o=1;o<n.length;o++){var a=h(n[o].split("="),2),s=a[0],c=a[1];i[s]=c}return'<div class="llm-content-img"  style="width:'.concat(i.width||100,"%;\" ><img src='").concat(r,'\' onload="globalImgLoad(this)" onerror="globalImgError(this)"></div>')})).replace(/\[\[video=(((?!\]\]).)+)\]\]/g,(function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=r.split(";");if(1===n.length)return t?"":(t=!0,"<div class=\"llm-content-video\">\n                        <video \n                          onloadstart='imVideoWaiting(this)'\n                          ontimeupdate='imVideoPlaying(this)'\n                          onwaiting='imVideoWaiting(this)'\n                          onended='imVideoEnded(this)' \n                          onerror='imVideoError(this)' \n                          onplay='imVideoPlay(this)' \n                          onplaying='imVideoPlaying(this)' \n                          webkit-playsinline \n                          playsinline \n                          x5-playsinline \n                          autoplay=\"autoplay\"  \n                          preload=\"auto\" \n                          src='".concat(r,'\' \n                          loop=\'loop\'\n                          controls\n                          class="content-video">\n                        </video>\n                        <div class="loading-icon">\n                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60px" height="60px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n                            <path opacity="0.2" fill="#FF6700" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n                                s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n                                c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"></path>\n                            <path fill="#FF6700" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n                                C22.32,8.481,24.301,9.057,26.013,10.047z" transform="rotate(42.1171 20 20)">\n                                <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20" to="360 20 20" dur="0.5s" repeatCount="indefinite"></animateTransform>\n                            </path>\n                          </svg>\n                        </div>\n                      </div>'));if(2===n.length){if(t)return"";t=!0;var i=n[1]||"";return i=Number(i.split("=")[1]),r=n[0],1===i?"<div class=\"llm-content-video\">\n                          <video  \n                            onloadstart='imVideoWaiting(this)'\n                            ontimeupdate='imVideoPlaying(this)'\n                            onwaiting='imVideoWaiting(this)'\n                            onended='imVideoEnded(this)' \n                            onerror='imVideoError(this)' \n                            onplay='imVideoPlay(this)' \n                            onplaying='imVideoPlaying(this)' \n                            webkit-playsinline \n                            playsinline \n                            x5-playsinline \n                            autoplay=\"autoplay\"  \n                            preload=\"auto\" \n                            src='".concat(r,'\' \n                            muted \n                            loop=\'loop\'\n                            style="width:100%;" \n                            controls\n                            class="content-video">\n                          </video>\n                          <div class="loading-icon">\n                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60px" height="60px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n                              <path opacity="0.2" fill="#FF6700" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n                                  s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n                                  c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"></path>\n                              <path fill="#FF6700" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n                                  C22.32,8.481,24.301,9.057,26.013,10.047z" transform="rotate(42.1171 20 20)">\n                                  <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20" to="360 20 20" dur="0.5s" repeatCount="indefinite"></animateTransform>\n                              </path>\n                            </svg>\n                          </div>\n                        </div>'):"<div class=\"llm-content-video\">\n                          <video \n                            onloadstart='imVideoWaiting(this)'\n                            ontimeupdate='imVideoPlaying(this)'\n                            onwaiting='imVideoWaiting(this)'\n                            onended='imVideoEnded(this)' \n                            onerror='imVideoError(this)' \n                            onplay='imVideoPlay(this)' \n                            onplaying='imVideoPlaying(this)' \n                            webkit-playsinline \n                            playsinline \n                            x5-playsinline \n                            autoplay=\"autoplay\"  \n                            preload=\"auto\" \n                            src='".concat(r,'\' \n                            loop=\'loop\'\n                            controls\n                            class="content-video">\n                          </video>\n                          <div class="loading-icon">\n                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60px" height="60px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n                              <path opacity="0.2" fill="#FF6700" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n                                  s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n                                  c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"></path>\n                              <path fill="#FF6700" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n                                  C22.32,8.481,24.301,9.057,26.013,10.047z" transform="rotate(42.1171 20 20)">\n                                  <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20" to="360 20 20" dur="0.5s" repeatCount="indefinite"></animateTransform>\n                              </path>\n                            </svg>\n                          </div>\n                        </div>')}return e}))}var Tt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function It(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Ct(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var Lt={exports:{}};function Rt(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Ut,zt={exports:{}},Bt=Ct(Object.freeze({__proto__:null,default:{}}));function Ft(){return Ut||(Ut=1,zt.exports=(e=e||function(e,t){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==Tt&&Tt.crypto&&(r=Tt.crypto),!r)try{r=Bt}catch(e){}var n=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),o={},a=o.lib={},s=a.Base={extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=a.WordArray=s.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var a=r[o>>>2]>>>24-o%4*8&255;t[n+o>>>2]|=a<<24-(n+o)%4*8}else for(var s=0;s<i;s+=4)t[n+s>>>2]=r[s>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=s.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(n());return new c.init(t,e)}}),u=o.enc={},l=u.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new c.init(r,t/2)}},f=u.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new c.init(r,t)}},d=u.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},v=a.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,i=n.words,o=n.sigBytes,a=this.blockSize,s=o/(4*a),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,l=e.min(4*u,o);if(u){for(var f=0;f<u;f+=a)this._doProcessBlock(i,f);r=i.splice(0,u),n.sigBytes-=l}return new c.init(r,l)},clone:function(){var e=s.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=v.extend({cfg:s.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){v.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new h.HMAC.init(e,r).finalize(t)}}});var h=o.algo={};return o}(Math),e)),zt.exports;var e}var Dt,Nt={exports:{}};function qt(){return Dt?Nt.exports:(Dt=1,Nt.exports=(e=Ft(),function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,a=r.algo,s=[],c=[];!function(){function e(e){for(var r=t.sqrt(e),n=2;n<=r;n++)if(!(e%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)e(n)&&(i<8&&(s[i]=r(t.pow(n,.5))),c[i]=r(t.pow(n,1/3)),i++),n++}();var u=[],l=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],l=r[5],f=r[6],d=r[7],v=0;v<64;v++){if(v<16)u[v]=0|e[t+v];else{var h=u[v-15],p=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,y=u[v-2],m=(y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10;u[v]=p+u[v-7]+m+u[v-16]}var g=n&i^n&o^i&o,w=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),b=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+c[v]+u[v];d=f,f=l,l=s,s=a+b|0,a=o,o=i,i=n,n=b+(w+g)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+l|0,r[6]=r[6]+f|0,r[7]=r[7]+d|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=t.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=o._createHelper(l),r.HmacSHA256=o._createHmacHelper(l)}(Math),e.SHA256));var e}var Vt,Ht,Gt={exports:{}};Lt.exports=function(e){return e.HmacSHA256}(Ft(),qt(),Vt||(Vt=1,Gt.exports=(Ht=Ft(),void function(){var e=Ht,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,i=4*n;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),a=this._iKey=t.clone(),s=o.words,c=a.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;o.sigBytes=a.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})}())));var Kt=It(Lt.exports),$t={exports:{}};$t.exports=function(e){return function(){var t=e,r=t.lib.WordArray;function n(e,t,n){for(var i=[],o=0,a=0;a<t;a++)if(a%4){var s=n[e.charCodeAt(a-1)]<<a%4*2|n[e.charCodeAt(a)]>>>6-a%4*2;i[o>>>2]|=s<<24-o%4*8,o++}return r.create(i,o)}t.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],o=0;o<r;o+=3)for(var a=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;s<4&&o+.75*s<r;s++)i.push(n.charAt(a>>>6*(3-s)&63));var c=n.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(e){var t=e.length,r=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<r.length;o++)i[r.charCodeAt(o)]=o}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return n(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64}(Ft());var Zt=It($t.exports),Jt={exports:{}};Jt.exports=function(e){return e.enc.Utf8}(Ft());var Xt=It(Jt.exports);function Yt(e,t,r){var n=h(e.match(/^wss?:\/\/([^\/]+)(\/.*)/)||[],3);n[0];var i=n[1],o=n[2],a=(new Date).toUTCString(),s="host: ".concat(i,"\ndate: ").concat(a,"\n").concat("GET"," ").concat(o," HTTP/1.1"),c=Kt(s,r),u=Zt.stringify(c),l='api_key="'.concat(t,'", algorithm="').concat("hmac-sha256",'", headers="').concat("host date request-line",'", signature="').concat(u,'"'),f=Zt.stringify(Xt.parse(l));return"".concat(e,"?authorization=").concat(f,"&date=").concat(a,"&host=").concat(i)}var Qt=function(){this.__data__=[],this.size=0};var er=function(e,t){return e===t||e!=e&&t!=t},tr=er;var rr=function(e,t){for(var r=e.length;r--;)if(tr(e[r][0],t))return r;return-1},nr=rr,ir=Array.prototype.splice;var or=rr;var ar=rr;var sr=rr;var cr=Qt,ur=function(e){var t=this.__data__,r=nr(t,e);return!(r<0)&&(r==t.length-1?t.pop():ir.call(t,r,1),--this.size,!0)},lr=function(e){var t=this.__data__,r=or(t,e);return r<0?void 0:t[r][1]},fr=function(e){return ar(this.__data__,e)>-1},dr=function(e,t){var r=this.__data__,n=sr(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};function vr(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}vr.prototype.clear=cr,vr.prototype.delete=ur,vr.prototype.get=lr,vr.prototype.has=fr,vr.prototype.set=dr;var hr=vr,pr=hr;var yr=function(){this.__data__=new pr,this.size=0};var mr=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r};var gr=function(e){return this.__data__.get(e)};var wr=function(e){return this.__data__.has(e)},br="object"==typeof Tt&&Tt&&Tt.Object===Object&&Tt,_r=br,xr="object"==typeof self&&self&&self.Object===Object&&self,kr=_r||xr||Function("return this")(),jr=kr.Symbol,Or=jr,Mr=Object.prototype,Sr=Mr.hasOwnProperty,Er=Mr.toString,Ar=Or?Or.toStringTag:void 0;var Pr=function(e){var t=Sr.call(e,Ar),r=e[Ar];try{e[Ar]=void 0;var n=!0}catch(e){}var i=Er.call(e);return n&&(t?e[Ar]=r:delete e[Ar]),i},Wr=Object.prototype.toString;var Tr=Pr,Ir=function(e){return Wr.call(e)},Cr=jr?jr.toStringTag:void 0;var Lr=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Cr&&Cr in Object(e)?Tr(e):Ir(e)};var Rr=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},Ur=Lr,zr=Rr;var Br,Fr=function(e){if(!zr(e))return!1;var t=Ur(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Dr=kr["__core-js_shared__"],Nr=(Br=/[^.]+$/.exec(Dr&&Dr.keys&&Dr.keys.IE_PROTO||""))?"Symbol(src)_1."+Br:"";var qr=function(e){return!!Nr&&Nr in e},Vr=Function.prototype.toString;var Hr=function(e){if(null!=e){try{return Vr.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Gr=Fr,Kr=qr,$r=Rr,Zr=Hr,Jr=/^\[object .+?Constructor\]$/,Xr=Function.prototype,Yr=Object.prototype,Qr=Xr.toString,en=Yr.hasOwnProperty,tn=RegExp("^"+Qr.call(en).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var rn=function(e){return!(!$r(e)||Kr(e))&&(Gr(e)?tn:Jr).test(Zr(e))},nn=function(e,t){return null==e?void 0:e[t]};var on=function(e,t){var r=nn(e,t);return rn(r)?r:void 0},an=on(kr,"Map"),sn=on(Object,"create"),cn=sn;var un=function(){this.__data__=cn?cn(null):{},this.size=0};var ln=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},fn=sn,dn=Object.prototype.hasOwnProperty;var vn=function(e){var t=this.__data__;if(fn){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return dn.call(t,e)?t[e]:void 0},hn=sn,pn=Object.prototype.hasOwnProperty;var yn=sn;var mn=un,gn=ln,wn=vn,bn=function(e){var t=this.__data__;return hn?void 0!==t[e]:pn.call(t,e)},_n=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=yn&&void 0===t?"__lodash_hash_undefined__":t,this};function xn(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}xn.prototype.clear=mn,xn.prototype.delete=gn,xn.prototype.get=wn,xn.prototype.has=bn,xn.prototype.set=_n;var kn=xn,jn=hr,On=an;var Mn=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var Sn=function(e,t){var r=e.__data__;return Mn(t)?r["string"==typeof t?"string":"hash"]:r.map},En=Sn;var An=Sn;var Pn=Sn;var Wn=Sn;var Tn=function(){this.size=0,this.__data__={hash:new kn,map:new(On||jn),string:new kn}},In=function(e){var t=En(this,e).delete(e);return this.size-=t?1:0,t},Cn=function(e){return An(this,e).get(e)},Ln=function(e){return Pn(this,e).has(e)},Rn=function(e,t){var r=Wn(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};function Un(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Un.prototype.clear=Tn,Un.prototype.delete=In,Un.prototype.get=Cn,Un.prototype.has=Ln,Un.prototype.set=Rn;var zn=hr,Bn=an,Fn=Un;var Dn=hr,Nn=yr,qn=mr,Vn=gr,Hn=wr,Gn=function(e,t){var r=this.__data__;if(r instanceof zn){var n=r.__data__;if(!Bn||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Fn(n)}return r.set(e,t),this.size=r.size,this};function Kn(e){var t=this.__data__=new Dn(e);this.size=t.size}Kn.prototype.clear=Nn,Kn.prototype.delete=qn,Kn.prototype.get=Vn,Kn.prototype.has=Hn,Kn.prototype.set=Gn;var $n=Kn;var Zn=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e},Jn=on,Xn=function(){try{var e=Jn(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var Yn=function(e,t,r){"__proto__"==t&&Xn?Xn(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r},Qn=Yn,ei=er,ti=Object.prototype.hasOwnProperty;var ri=function(e,t,r){var n=e[t];ti.call(e,t)&&ei(n,r)&&(void 0!==r||t in e)||Qn(e,t,r)},ni=ri,ii=Yn;var oi=function(e,t,r,n){var i=!r;r||(r={});for(var o=-1,a=t.length;++o<a;){var s=t[o],c=n?n(r[s],e[s],s,r,e):void 0;void 0===c&&(c=e[s]),i?ii(r,s,c):ni(r,s,c)}return r};var ai=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n};var si=function(e){return null!=e&&"object"==typeof e},ci=Lr,ui=si;var li=function(e){return ui(e)&&"[object Arguments]"==ci(e)},fi=si,di=Object.prototype,vi=di.hasOwnProperty,hi=di.propertyIsEnumerable,pi=li(function(){return arguments}())?li:function(e){return fi(e)&&vi.call(e,"callee")&&!hi.call(e,"callee")},yi=Array.isArray,mi={exports:{}};var gi=function(){return!1};!function(e,t){var r=kr,n=gi,i=t&&!t.nodeType&&t,o=i&&e&&!e.nodeType&&e,a=o&&o.exports===i?r.Buffer:void 0,s=(a?a.isBuffer:void 0)||n;e.exports=s}(mi,mi.exports);var wi=mi.exports,bi=/^(?:0|[1-9]\d*)$/;var _i=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&bi.test(e))&&e>-1&&e%1==0&&e<t};var xi=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},ki=Lr,ji=xi,Oi=si,Mi={};Mi["[object Float32Array]"]=Mi["[object Float64Array]"]=Mi["[object Int8Array]"]=Mi["[object Int16Array]"]=Mi["[object Int32Array]"]=Mi["[object Uint8Array]"]=Mi["[object Uint8ClampedArray]"]=Mi["[object Uint16Array]"]=Mi["[object Uint32Array]"]=!0,Mi["[object Arguments]"]=Mi["[object Array]"]=Mi["[object ArrayBuffer]"]=Mi["[object Boolean]"]=Mi["[object DataView]"]=Mi["[object Date]"]=Mi["[object Error]"]=Mi["[object Function]"]=Mi["[object Map]"]=Mi["[object Number]"]=Mi["[object Object]"]=Mi["[object RegExp]"]=Mi["[object Set]"]=Mi["[object String]"]=Mi["[object WeakMap]"]=!1;var Si=function(e){return Oi(e)&&ji(e.length)&&!!Mi[ki(e)]};var Ei=function(e){return function(t){return e(t)}},Ai={exports:{}};!function(e,t){var r=br,n=t&&!t.nodeType&&t,i=n&&e&&!e.nodeType&&e,o=i&&i.exports===n&&r.process,a=function(){try{var e=i&&i.require&&i.require("util").types;return e||o&&o.binding&&o.binding("util")}catch(e){}}();e.exports=a}(Ai,Ai.exports);var Pi=Ai.exports,Wi=Si,Ti=Ei,Ii=Pi&&Pi.isTypedArray,Ci=Ii?Ti(Ii):Wi,Li=ai,Ri=pi,Ui=yi,zi=wi,Bi=_i,Fi=Ci,Di=Object.prototype.hasOwnProperty;var Ni=function(e,t){var r=Ui(e),n=!r&&Ri(e),i=!r&&!n&&zi(e),o=!r&&!n&&!i&&Fi(e),a=r||n||i||o,s=a?Li(e.length,String):[],c=s.length;for(var u in e)!t&&!Di.call(e,u)||a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Bi(u,c))||s.push(u);return s},qi=Object.prototype;var Vi=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||qi)};var Hi=function(e,t){return function(r){return e(t(r))}},Gi=Hi(Object.keys,Object),Ki=Vi,$i=Gi,Zi=Object.prototype.hasOwnProperty;var Ji=Fr,Xi=xi;var Yi=function(e){return null!=e&&Xi(e.length)&&!Ji(e)},Qi=Ni,eo=function(e){if(!Ki(e))return $i(e);var t=[];for(var r in Object(e))Zi.call(e,r)&&"constructor"!=r&&t.push(r);return t},to=Yi;var ro=function(e){return to(e)?Qi(e):eo(e)},no=oi,io=ro;var oo=function(e,t){return e&&no(t,io(t),e)};var ao=Rr,so=Vi,co=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t},uo=Object.prototype.hasOwnProperty;var lo=Ni,fo=function(e){if(!ao(e))return co(e);var t=so(e),r=[];for(var n in e)("constructor"!=n||!t&&uo.call(e,n))&&r.push(n);return r},vo=Yi;var ho=function(e){return vo(e)?lo(e,!0):fo(e)},po=oi,yo=ho;var mo=function(e,t){return e&&po(t,yo(t),e)},go={exports:{}};!function(e,t){var r=kr,n=t&&!t.nodeType&&t,i=n&&e&&!e.nodeType&&e,o=i&&i.exports===n?r.Buffer:void 0,a=o?o.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=a?a(r):new e.constructor(r);return e.copy(n),n}}(go,go.exports);var wo=go.exports;var bo=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t};var _o=function(){return[]},xo=function(e,t){for(var r=-1,n=null==e?0:e.length,i=0,o=[];++r<n;){var a=e[r];t(a,r,e)&&(o[i++]=a)}return o},ko=_o,jo=Object.prototype.propertyIsEnumerable,Oo=Object.getOwnPropertySymbols,Mo=Oo?function(e){return null==e?[]:(e=Object(e),xo(Oo(e),(function(t){return jo.call(e,t)})))}:ko,So=oi,Eo=Mo;var Ao=function(e,t){return So(e,Eo(e),t)};var Po=function(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e},Wo=Hi(Object.getPrototypeOf,Object),To=Po,Io=Wo,Co=Mo,Lo=_o,Ro=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)To(t,Co(e)),e=Io(e);return t}:Lo,Uo=oi,zo=Ro;var Bo=function(e,t){return Uo(e,zo(e),t)},Fo=Po,Do=yi;var No=function(e,t,r){var n=t(e);return Do(e)?n:Fo(n,r(e))},qo=No,Vo=Mo,Ho=ro;var Go=function(e){return qo(e,Ho,Vo)},Ko=No,$o=Ro,Zo=ho;var Jo=function(e){return Ko(e,Zo,$o)},Xo=on(kr,"DataView"),Yo=an,Qo=on(kr,"Promise"),ea=on(kr,"Set"),ta=on(kr,"WeakMap"),ra=Lr,na=Hr,ia="[object Map]",oa="[object Promise]",aa="[object Set]",sa="[object WeakMap]",ca="[object DataView]",ua=na(Xo),la=na(Yo),fa=na(Qo),da=na(ea),va=na(ta),ha=ra;(Xo&&ha(new Xo(new ArrayBuffer(1)))!=ca||Yo&&ha(new Yo)!=ia||Qo&&ha(Qo.resolve())!=oa||ea&&ha(new ea)!=aa||ta&&ha(new ta)!=sa)&&(ha=function(e){var t=ra(e),r="[object Object]"==t?e.constructor:void 0,n=r?na(r):"";if(n)switch(n){case ua:return ca;case la:return ia;case fa:return oa;case da:return aa;case va:return sa}return t});var pa=ha,ya=Object.prototype.hasOwnProperty;var ma=function(e){var t=e.length,r=new e.constructor(t);return t&&"string"==typeof e[0]&&ya.call(e,"index")&&(r.index=e.index,r.input=e.input),r},ga=kr.Uint8Array;var wa=function(e){var t=new e.constructor(e.byteLength);return new ga(t).set(new ga(e)),t},ba=wa;var _a=function(e,t){var r=t?ba(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)},xa=/\w*$/;var ka=function(e){var t=new e.constructor(e.source,xa.exec(e));return t.lastIndex=e.lastIndex,t},ja=jr?jr.prototype:void 0,Oa=ja?ja.valueOf:void 0;var Ma=wa;var Sa=wa,Ea=_a,Aa=ka,Pa=function(e){return Oa?Object(Oa.call(e)):{}},Wa=function(e,t){var r=t?Ma(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)};var Ta=function(e,t,r){var n=e.constructor;switch(t){case"[object ArrayBuffer]":return Sa(e);case"[object Boolean]":case"[object Date]":return new n(+e);case"[object DataView]":return Ea(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return Wa(e,r);case"[object Map]":case"[object Set]":return new n;case"[object Number]":case"[object String]":return new n(e);case"[object RegExp]":return Aa(e);case"[object Symbol]":return Pa(e)}},Ia=Rr,Ca=Object.create,La=function(){function e(){}return function(t){if(!Ia(t))return{};if(Ca)return Ca(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}(),Ra=Wo,Ua=Vi;var za=function(e){return"function"!=typeof e.constructor||Ua(e)?{}:La(Ra(e))},Ba=pa,Fa=si;var Da=function(e){return Fa(e)&&"[object Map]"==Ba(e)},Na=Ei,qa=Pi&&Pi.isMap,Va=qa?Na(qa):Da,Ha=pa,Ga=si;var Ka=function(e){return Ga(e)&&"[object Set]"==Ha(e)},$a=Ei,Za=Pi&&Pi.isSet,Ja=Za?$a(Za):Ka,Xa=$n,Ya=Zn,Qa=ri,es=oo,ts=mo,rs=wo,ns=bo,is=Ao,os=Bo,as=Go,ss=Jo,cs=pa,us=ma,ls=Ta,fs=za,ds=yi,vs=wi,hs=Va,ps=Rr,ys=Ja,ms=ro,gs=ho,ws="[object Arguments]",bs="[object Function]",_s="[object Object]",xs={};xs[ws]=xs["[object Array]"]=xs["[object ArrayBuffer]"]=xs["[object DataView]"]=xs["[object Boolean]"]=xs["[object Date]"]=xs["[object Float32Array]"]=xs["[object Float64Array]"]=xs["[object Int8Array]"]=xs["[object Int16Array]"]=xs["[object Int32Array]"]=xs["[object Map]"]=xs["[object Number]"]=xs[_s]=xs["[object RegExp]"]=xs["[object Set]"]=xs["[object String]"]=xs["[object Symbol]"]=xs["[object Uint8Array]"]=xs["[object Uint8ClampedArray]"]=xs["[object Uint16Array]"]=xs["[object Uint32Array]"]=!0,xs["[object Error]"]=xs[bs]=xs["[object WeakMap]"]=!1;var ks=function e(t,r,n,i,o,a){var s,c=1&r,u=2&r,l=4&r;if(n&&(s=o?n(t,i,o,a):n(t)),void 0!==s)return s;if(!ps(t))return t;var f=ds(t);if(f){if(s=us(t),!c)return ns(t,s)}else{var d=cs(t),v=d==bs||"[object GeneratorFunction]"==d;if(vs(t))return rs(t,c);if(d==_s||d==ws||v&&!o){if(s=u||v?{}:fs(t),!c)return u?os(t,ts(s,t)):is(t,es(s,t))}else{if(!xs[d])return o?t:{};s=ls(t,d,c)}}a||(a=new Xa);var h=a.get(t);if(h)return h;a.set(t,s),ys(t)?t.forEach((function(i){s.add(e(i,r,n,i,t,a))})):hs(t)&&t.forEach((function(i,o){s.set(o,e(i,r,n,o,t,a))}));var p=f?void 0:(l?u?ss:as:u?gs:ms)(t);return Ya(p||t,(function(i,o){p&&(i=t[o=i]),Qa(s,o,e(i,r,n,o,t,a))})),s},js=ks;var Os,Ms,Ss,Es,As,Ps,Ws,Ts,Is,Cs,Ls,Rs,Us,zs,Bs,Fs,Ds,Ns,qs,Vs,Hs,Gs,Ks,$s,Zs,Js,Xs,Ys,Qs,ec,tc,rc,nc,ic,oc,ac,sc,cc,uc,lc,fc,dc=It((function(e){return js(e,5)})),vc=function(t){function n(t){var i;return o(this,n),i=e(this,n),Os.add(d(i)),Ms.set(d(i),{useInlinePlayer:!0}),Ss.set(d(i),k.disconnected),Es.set(d(i),void 0),As.set(d(i),void 0),Ps.set(d(i),!1),Ws.set(d(i),{appId:"",apiKey:"",apiSecret:"",serverUrl:"wss://avatar.cn-huadong-1.xf-yun.com/v1/interact",sceneId:"",sceneVersion:""}),Ts.set(d(i),"avatar"),Is.set(d(i),!1),Cs.set(d(i),{avatar_dispatch:{interactive_mode:M.break,enable_action_status:1,content_analysis:0},avatar:{avatar_id:"",width:720,height:1280,audio_format:1},stream:{protocol:"xrtc",bitrate:1e6,fps:25,alpha:0},tts:{vcn:"",speed:50,pitch:50,volume:100},air:{air:0,add_nonsemantic:0}}),Ls.set(d(i),void 0),Rs.set(d(i),void 0),Us.set(d(i),void 0),zs.set(d(i),void 0),Bs.set(d(i),void 0),Fs.set(d(i),(function(){return w(d(i),void 0,void 0,r().mark((function e(){var t,n;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!b(this,zs,"f")){e.next=2;break}return e.abrupt("return",b(this,zs,"f"));case 2:return e.next=4,null===(t=b(this,Us,"f"))||void 0===t?void 0:t.websocketPromise;case 4:if(n=e.sent){e.next=7;break}return e.abrupt("return",Promise.reject(new Error(K.InvalidConnect)));case 7:return e.abrupt("return",n);case 8:case"end":return e.stop()}}),e,this)})))})),Ds.set(d(i),(function(e,t){var r,n,o=e;if("[object String]"!==Object.prototype.toString.call(e)){var a=e;(null==a?void 0:a.header)&&!a.header.request_id&&(a.header.request_id=W()),o=JSON.stringify(a)}t?ke.record($.debug,"[ws]","[msg send]:ignore record audio data, req_id:",(null===(r=null==e?void 0:e.header)||void 0===r?void 0:r.request_id)||""):ke.record($.debug,"[ws]","[msg send]",o),null===(n=b(d(i),zs,"f"))||void 0===n||n.send(o)})),Ns.set(d(i),void 0),qs.set(d(i),void 0),Vs.set(d(i),(function(e){_(d(i),Ns,e,"f")})),Hs.set(d(i),0),Gs.set(d(i),void 0),Ks.set(d(i),(function(){clearTimeout(b(d(i),Gs,"f"))})),$s.set(d(i),(function(){return w(d(i),void 0,void 0,r().mark((function e(){var t,n,i,o,a,s,c,u,l,f;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(this,Ns,void 0,"f"),s=(null===(t=b(this,Ws,"f"))||void 0===t?void 0:t.signedUrl)||Yt((null===(n=b(this,Ws,"f"))||void 0===n?void 0:n.serverUrl)||"",(null===(i=b(this,Ws,"f"))||void 0===i?void 0:i.apiKey)||"",(null===(o=b(this,Ws,"f"))||void 0===o?void 0:o.apiSecret)||""),_(this,Ss,k.connecting,"f"),c=Oe(s,{binaryData:null!==(a=b(this,Ms,"f").binaryData)&&void 0!==a&&a}),u=c.instablishPromise,l=c.abort,_(this,Us,{websocketPromise:u,abort:l},"f"),e.next=7,u;case 7:return f=e.sent,_(this,zs,f,"f"),e.abrupt("return",f);case 10:case"end":return e.stop()}}),e,this)})))})),Xs.set(d(i),(function(e){var t,r,n,i,o,a,s,c,u,l,f,d,v=null,h=null;try{v=JSON.parse(e)}catch(e){null==e||e.message}var p=!1;if(0!==(null===(t=null==v?void 0:v.header)||void 0===t?void 0:t.code))h={code:null===(r=null==v?void 0:v.header)||void 0===r?void 0:r.code,message:null===(n=null==v?void 0:v.header)||void 0===n?void 0:n.message,sid:(null===(i=null==v?void 0:v.header)||void 0===i?void 0:i.sid)||""},p=!0;else if(null===(o=null==v?void 0:v.payload)||void 0===o?void 0:o.nlp){var y=v.payload.nlp;0!==y.error_code&&(h={code:y.error_code,message:y.error_message,sid:(null===(a=null==v?void 0:v.header)||void 0===a?void 0:a.sid)||"",request_id:(null==y?void 0:y.request_id)||""})}else if(null===(s=null==v?void 0:v.payload)||void 0===s?void 0:s.asr){var m=v.payload.asr;0!==m.error_code&&(h={code:m.error_code,message:m.error_message,sid:(null===(c=null==v?void 0:v.header)||void 0===c?void 0:c.sid)||"",request_id:(null==m?void 0:m.request_id)||""})}else if(null===(u=null==v?void 0:v.payload)||void 0===u?void 0:u.tts){var g=v.payload.tts;0!==g.error_code&&(h={code:g.error_code,message:g.error_message,sid:(null===(l=null==v?void 0:v.header)||void 0===l?void 0:l.sid)||"",request_id:(null==g?void 0:g.request_id)||""})}else if(null===(f=null==v?void 0:v.payload)||void 0===f?void 0:f.avatar){var w=v.payload.avatar;0!==w.error_code&&(h={code:w.error_code||Q.code,message:w.error_message||Q.message,sid:(null===(d=null==v?void 0:v.header)||void 0===d?void 0:d.sid)||"",request_id:(null==w?void 0:w.request_id)||""})}return{data:v,error:h,is_socket_error:p}})),Ys.set(d(i),(function(){return w(d(i),void 0,void 0,r().mark((function e(){var t,n,i=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=b(this,Os,"m",Js).call(this),b(this,Ds,"f").call(this,t),!(n=b(this,zs,"f"))){e.next=5;break}return e.abrupt("return",new Promise((function(e,t){n.onmessage=function(r){var o,a,s,c,u,l,f,d,v,h,p,y,m,g,w=b(i,Xs,"f").call(i,r.data),_=w.data,x=w.error;n.onmessage=null;var k=void 0,j=void 0;if(x)j=new P(null!==(o=x.message)&&void 0!==o?o:Q.message,null!==(a=x.code)&&void 0!==a?a:Q.code,K.ConnectError);else{ke.record($.debug,"[stream_url]",null===(c=null===(s=null==_?void 0:_.payload)||void 0===s?void 0:s.avatar)||void 0===c?void 0:c.stream_url);var O=(null===(l=null===(u=null==_?void 0:_.payload)||void 0===u?void 0:u.avatar)||void 0===l?void 0:l.stream_url)||"",M=(null===(f=null==_?void 0:_.header)||void 0===f?void 0:f.sid)||"";O?k={stream_url:O,sid:M,session:(null===(d=null==_?void 0:_.header)||void 0===d?void 0:d.session)||"",appid:(null===(p=null===(h=null===(v=null==_?void 0:_.payload)||void 0===v?void 0:v.avatar)||void 0===h?void 0:h.stream_extend)||void 0===p?void 0:p.appid)||"",user_sign:(null===(g=null===(m=null===(y=null==_?void 0:_.payload)||void 0===y?void 0:y.avatar)||void 0===m?void 0:m.stream_extend)||void 0===g?void 0:g.user_sign)||""}:j=new P(Y.message,Y.code,K.InvalidResponse)}!j&&k?e(k):(n.onerror=null,t(j),b(i,Os,"m",fc).call(i,!0))}})));case 5:return e.abrupt("return",Promise.reject(new P(Y.message,Y.code,K.InvalidResponse)));case 6:case"end":return e.stop()}}),e,this)})))})),Qs.set(d(i),(function(){if(b(d(i),zs,"f")){b(d(i),zs,"f").onclose=function(){b(d(i),Os,"m",fc).call(d(i))};var e=null;b(d(i),zs,"f").onmessage=function(t){var r,n,o,a,s,c,u,l,f,v,h,p,y=b(d(i),Xs,"f").call(d(i),t.data),m=y.data,g=y.error,w=y.is_socket_error;if(ke.record($.verbose,"[msg handler]",null===(r=null==m?void 0:m.header)||void 0===r?void 0:r.sid),g)ke.record($.error,"[error]",g),w||"nlp"===b(d(i),Ts,"f")&&(null===(n=null==m?void 0:m.payload)||void 0===n?void 0:n.nlp)?(_(d(i),Is,!1,"f"),b(d(i),Vs,"f").call(d(i),g)):i.emit(Me.error,new P(null!==(o=g.message)&&void 0!==o?o:Q.message,null!==(a=g.code)&&void 0!==a?a:Q.code,K.ConnectError),m);else if(null===(s=null==m?void 0:m.payload)||void 0===s?void 0:s.nlp){var x=null===(c=null==m?void 0:m.payload)||void 0===c?void 0:c.nlp;(null==x?void 0:x.request_id)===(null==e?void 0:e.request_id)&&(x.streamNlp||x.stream_nlp)?x.content="".concat((null==e?void 0:e.content)||"").concat((null===(u=null==x?void 0:x.answer)||void 0===u?void 0:u.text)||""):x.content=(null===(l=null==x?void 0:x.answer)||void 0===l?void 0:l.text)||"";var k=Object.assign(Object.assign({},x),{displayContent:Wt(x.content)});"nlp"!==b(d(i),Ts,"f")||void 0!==(null==k?void 0:k.status)&&2!==(null==k?void 0:k.status)||_(d(i),Is,!0,"f"),e=k,i.emit(Me.nlp,dc(k),dc(m))}else if(null===(f=null==m?void 0:m.payload)||void 0===f?void 0:f.asr)i.emit(Me.asr,null===(v=null==m?void 0:m.payload)||void 0===v?void 0:v.asr);else if(null===(h=null==m?void 0:m.payload)||void 0===h?void 0:h.avatar){var M=null===(p=null==m?void 0:m.payload)||void 0===p?void 0:p.avatar;switch(M.event_type){case"stream_start":i.emit(Me.stream_start);break;case"driver_status":M.vmr_status===j.start?(i.emit(Me.frame_start,M),clearTimeout(b(d(i),Gs,"f"))):M.vmr_status===j.stop&&(i.emit(Me.frame_stop,M),b(d(i),Ks,"f").call(d(i)));break;case"action_status":M.action_status===O.start?i.emit(Me.action_start,M):M.action_status===O.stop&&i.emit(Me.action_stop,M);break;case"tts_duration":i.emit(Me.tts_duration,M);break;case"subtitle_info":var S=!1;clearTimeout(b(d(i),ic,"f")),b(d(i),tc,"f")!==M.request_id&&(S=!0,_(d(i),rc,0,"f"),_(d(i),tc,M.request_id,"f"),_(d(i),ec,[],"f")),b(d(i),ec,"f").push(M),S&&(cancelAnimationFrame(b(d(i),nc,"f")),_(d(i),nc,0,"f")),b(d(i),nc,"f")||b(d(i),Os,"m",sc).call(d(i))}}}}})),ec.set(d(i),[]),tc.set(d(i),""),rc.set(d(i),0),nc.set(d(i),0),ic.set(d(i),0),oc.set(d(i),100),cc.set(d(i),(function(){_(d(i),Hs,setInterval((function(){b(d(i),Fs,"f").call(d(i)).then((function(){var e;b(d(i),Ds,"f").call(d(i),{header:{request_id:W(),app_id:(null===(e=b(d(i),Ws,"f"))||void 0===e?void 0:e.appId)||"",ctrl:"ping"}})})).catch((function(e){ke.record($.error,"[heartbeat error]",e)}))}),4e3),"f")})),uc.set(d(i),(function(e){var t,r,n,o=b(d(i),Ns,"f");(t=i).emit.apply(t,[Me.disconnected].concat(p(e||"nlp"===b(d(i),Ts,"f")&&("nlp"!==b(d(i),Ts,"f")||b(d(i),Is,"f"))?[]:[o?new P(null==o?void 0:o.message,null==o?void 0:o.code,null==o?void 0:o.name,(null==o?void 0:o.sid)||""):new P(X.message,X.code,K.NetworkError,(null===(r=b(d(i),Rs,"f"))||void 0===r?void 0:r.sid)||"")]))),b(d(i),Ms,"f").useInlinePlayer&&(null===(n=b(d(i),Es,"f"))||void 0===n||n.stop())})),lc.set(d(i),(function(e,t){return w(d(i),void 0,void 0,r().mark((function n(){var i,o,a,s,c,u,l;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!b(this,Ms,"f").useInlinePlayer||!["xrtc","webrtc"].includes(null===(i=b(this,Cs,"f").stream)||void 0===i?void 0:i.protocol)){r.next=14;break}if(ke.record($.debug,"[player]:useInlinePlayer","inited"),b(this,Ms,"f").useInlinePlayer&&this.createPlayer(),b(this,Es,"f")){r.next=5;break}return r.abrupt("return",Promise.reject(new P(ee.MissingPlayerLibsError.message,ee.MissingPlayerLibsError.code,K.MediaError)));case 5:return e.stream_url.startsWith("xrtc")?(l=null===(o=e.stream_url)||void 0===o?void 0:o.match(/^xrtc(s?):\/\/([^/]*)\/([^/]+)/),u={sid:e.sid,server:"http".concat(l[1],"://").concat(l[2]),auth:null!==(s=null===(a=e.user_sign)||void 0===a?void 0:a.replace(/^Bearer /,""))&&void 0!==s?s:"",appid:e.appid,timeStr:"".concat(Date.now()),userId:"c"+l[3],roomId:l[3]},b(this,Es,"f").playerType="xrtc"):(b(this,Es,"f").playerType="webrtc",u={sid:e.sid,streamUrl:e.stream_url}),ke.record($.debug,"[player]: playerType",b(this,Es,"f").playerType),b(this,Es,"f").videoSize={width:b(this,Cs,"f").avatar.width,height:b(this,Cs,"f").avatar.height},b(this,Es,"f").container=t,ke.record($.debug,"[player]","preset streamSize:",b(this,Es,"f").videoSize),r.next=12,b(this,Es,"f").playStream(u);case 12:r.next=15;break;case 14:ke.record($.debug,"[player]: ingore; [inline]/[protocol]",b(this,Ms,"f").useInlinePlayer,["xrtc","webrtc"].includes(null===(c=b(this,Cs,"f").stream)||void 0===c?void 0:c.protocol));case 15:case"end":return r.stop()}}),n,this)})))})),_(d(i),Ms,Object.assign(Object.assign({},b(d(i),Ms,"f")),t),"f"),b(d(i),Ms,"f").useInlinePlayer&&i.createPlayer(),i}return c(n,He),s(n,[{key:"player",get:function(){return b(this,Es,"f")}},{key:"setApiInfo",value:function(e){return _(this,Ws,I(b(this,Ws,"f"),e),"f"),this}},{key:"setGlobalParams",value:function(e){return _(this,Cs,I(b(this,Cs,"f"),e),"f"),this}},{key:"start",value:function(e){return w(this,void 0,void 0,r().mark((function t(){var n,i,o,a,s,c,u;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(_(this,Ns,void 0,"f"),_(this,Ts,"avatar","f"),s=(a=e||{}).wrapper,c=a.preRes,b(this,Ws,"f")&&(null===(n=b(this,Cs,"f").avatar)||void 0===n?void 0:n.avatar_id)&&(null===(i=b(this,Cs,"f").tts)||void 0===i?void 0:i.vcn)&&b(this,Cs,"f").avatar.width&&b(this,Cs,"f").avatar.height){t.next=5;break}return t.abrupt("return",Promise.reject(new P(J.message,J.code,K.InvalidParam)));case 5:if(void 0!==s||!b(this,Ms,"f").useInlinePlayer||!["xrtc","webrtc"].includes(null===(o=b(this,Cs,"f").stream)||void 0===o?void 0:o.protocol)){t.next=7;break}return t.abrupt("return",Promise.reject(new P("播放节点未指定",J.code,K.InvalidParam)));case 7:return b(this,Os,"m",fc).call(this,!0),t.prev=8,_(this,Ls,c||void 0,"f"),t.next=12,b(this,$s,"f").call(this);case 12:t.next=19;break;case 14:return t.prev=14,t.t0=t.catch(8),ke.record($.error,"[ws]:connect failed",(null===t.t0||void 0===t.t0?void 0:t.t0.message)||""),_(this,Ss,k.disconnected,"f"),t.abrupt("return",Promise.reject(new P((null===t.t0||void 0===t.t0?void 0:t.t0.message)||X.message,(null===t.t0||void 0===t.t0?void 0:t.t0.code)||X.code,K.ConnectError)));case 19:return t.prev=19,t.next=22,b(this,Ys,"f").call(this);case 22:return u=t.sent,b(this,Qs,"f").call(this),_(this,Rs,dc(u),"f"),_(this,Ss,k.connected,"f"),this.emit(Me.connected,u),ke.record($.debug,"[interact]:success",u),b(this,cc,"f").call(this),b(this,Ks,"f").call(this),t.next=32,b(this,lc,"f").call(this,u,s);case 32:t.next=39;break;case 34:throw t.prev=34,t.t1=t.catch(19),b(this,Os,"m",fc).call(this,!0),_(this,Ss,k.disconnected,"f"),t.t1;case 39:case"end":return t.stop()}}),t,this,[[8,14],[19,34]])})))}},{key:"connectNlp",value:function(){return w(this,void 0,void 0,r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(_(this,Ts,"nlp","f"),_(this,Is,!1,"f"),b(this,Ws,"f")){e.next=4;break}return e.abrupt("return",Promise.reject(new P(J.message,J.code,K.InvalidParam)));case 4:return b(this,Os,"m",fc).call(this,!0),e.prev=5,e.next=8,b(this,$s,"f").call(this);case 8:e.next=15;break;case 10:return e.prev=10,e.t0=e.catch(5),ke.record($.error,"[ws]:connect failed",(null===e.t0||void 0===e.t0?void 0:e.t0.message)||""),_(this,Ss,k.disconnected,"f"),e.abrupt("return",Promise.reject(new P((null===e.t0||void 0===e.t0?void 0:e.t0.message)||X.message,(null===e.t0||void 0===e.t0?void 0:e.t0.code)||X.code,K.ConnectError)));case 15:b(this,Qs,"f").call(this),_(this,Ss,k.connected,"f"),this.emit(Me.connected),ke.record($.debug,"[interact]:success");case 19:case"end":return e.stop()}}),e,this,[[5,10]])})))}},{key:"interrupt",value:function(){return w(this,void 0,void 0,r().mark((function e(){var t=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b(this,Fs,"f").call(this).then((function(){var e;b(t,Ds,"f").call(t,{header:{app_id:(null===(e=b(t,Ws,"f"))||void 0===e?void 0:e.appId)||"",ctrl:"reset"}})}));case 2:case"end":return e.stop()}}),e,this)})))}},{key:"writeText",value:function(e,t){return w(this,void 0,void 0,r().mark((function n(){var i,o,a,s,c,u,l,f,d,v,h,p,y,m,g,w;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return l=(u=t||{}).request_id,f=u.session,d=u.uid,v=u.nlp,h=u.avatar_dispatch,p=u.air,y=u.parameter,m=u.tts,l=l||W(),g=null!=p?p:b(this,Cs,"f").air,w={},g&&(w={air:I(b(this,Cs,"f").air,p)}),r.prev=5,r.next=8,b(this,Fs,"f").call(this);case 8:r.next=14;break;case 10:return r.prev=10,r.t0=r.catch(5),ke.record($.error,"[writeText]",r.t0),r.abrupt("return",Promise.reject(new P(X.message,X.code,K.InvalidConnect)));case 14:return b(this,Ds,"f").call(this,{header:{app_id:(null===(i=b(this,Ws,"f"))||void 0===i?void 0:i.appId)||"",request_id:l,ctrl:v?"text_interact":"text_driver",session:f||"",uid:d||"",scene_id:(null===(o=b(this,Ws,"f"))||void 0===o?void 0:o.sceneId)||"",scene_version:(null===(a=b(this,Ws,"f"))||void 0===a?void 0:a.sceneVersion)||""},parameter:Object.assign(Object.assign({avatar_dispatch:Object.assign(Object.assign({},I(b(this,Cs,"f").avatar_dispatch,h)),{interactive_mode:null!==(s=null==h?void 0:h.interactive_mode)&&void 0!==s?s:null===(c=b(this,Cs,"f").avatar_dispatch)||void 0===c?void 0:c.interactive_mode}),tts:Object.assign({},I(b(this,Cs,"f").tts,m,{audio:{sample_rate:2===b(this,Os,"m",Zs).call(this)?24e3:16e3}}))},w),y),payload:{text:{content:e}}}),r.abrupt("return",l);case 16:case"end":return r.stop()}}),n,this,[[5,10]])})))}},{key:"writeJsonText",value:function(e,t,n){return w(this,void 0,void 0,r().mark((function i(){var o,a,s,c,u,l,f,d,v,h,p;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return c=t.request_id,u=t.nlp,l=t.avatar_dispatch,f=t.air,d=t.tts,v=t.parameter,c=c||W(),h=null!=f?f:b(this,Cs,"f").air,p={},h&&(p={air:I(b(this,Cs,"f").air,f)}),r.prev=5,r.next=8,b(this,Fs,"f").call(this);case 8:r.next=14;break;case 10:return r.prev=10,r.t0=r.catch(5),ke.record($.error,"[writeJsonText]",r.t0),r.abrupt("return",Promise.reject(new P(X.message,X.code,K.InvalidConnect)));case 14:return b(this,Ds,"f").call(this,{header:{app_id:(null===(o=b(this,Ws,"f"))||void 0===o?void 0:o.appId)||"",request_id:c,ctrl:u?"text_interact":"text_driver"},parameter:Object.assign(Object.assign({avatar_dispatch:Object.assign(Object.assign({},I(b(this,Cs,"f").avatar_dispatch,l)),{interactive_mode:null!==(a=null==l?void 0:l.interactive_mode)&&void 0!==a?a:null===(s=b(this,Cs,"f").avatar_dispatch)||void 0===s?void 0:s.interactive_mode}),tts:Object.assign({},I(b(this,Cs,"f").tts,d,{audio:{sample_rate:2===b(this,Os,"m",Zs).call(this)?24e3:16e3}}))},p),v),payload:{json_text:{text:e,cmd:n}}}),r.abrupt("return",c);case 16:case"end":return r.stop()}}),i,this,[[5,10]])})))}},{key:"writeAudio",value:function(e,t,n){return w(this,void 0,void 0,r().mark((function i(){var o,a,s,c,u,l,f,d,v,h,p,y,m,w,k,j,O,M,S,E,A,T,C;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,b(this,Fs,"f").call(this);case 3:r.next=9;break;case 5:return r.prev=5,r.t0=r.catch(0),ke.record($.error,"[writeAudio]",r.t0),r.abrupt("return",Promise.reject(new P(X.message,X.code,K.InvalidConnect)));case 9:return"",h=!1,(t===x.start||!b(this,Ps,"f")&&t===x.end)&&(h=!0,_(this,Bs,W(),"f"),ke.record($.info,"[writeAudio]","audio is first Frame, reset")),_(this,Bs,v=b(this,Bs,"f")||W(),"f"),t===x.end?_(this,Ps,!1,"f"):_(this,Ps,!0,"f"),y=(p=n||{}).nlp,m=p.full_duplex,w=p.avatar,k=p.vc,j=p.avatar_dispatch,O=p.air,M=p.audio,S=p.session,E=p.uid,A=g(p,["nlp","full_duplex","avatar","vc","avatar_dispatch","air","audio","session","uid"]),T=null!=O?O:b(this,Cs,"f").air,C={},T&&(C={air:I(b(this,Cs,"f").air,O)}),b(this,Ds,"f").call(this,{header:{app_id:(null===(o=b(this,Ws,"f"))||void 0===o?void 0:o.appId)||"",request_id:v,ctrl:y?"audio_interact":"audio_driver",session:S||"",uid:E||""},parameter:Object.assign(Object.assign(Object.assign({avatar_dispatch:Object.assign(Object.assign(Object.assign({},j),!y&&h?{interactive_mode:null!==(a=null==j?void 0:j.interactive_mode)&&void 0!==a?a:null===(s=b(this,Cs,"f").avatar_dispatch)||void 0===s?void 0:s.interactive_mode}:{}),{audio_mode:null!==(c=null==j?void 0:j.audio_mode)&&void 0!==c?c:0,content_analysis:(y?null!==(u=null==j?void 0:j.content_analysis)&&void 0!==u?u:null===(l=b(this,Cs,"f").avatar_dispatch)||void 0===l?void 0:l.content_analysis:0)||0})},y?{asr:{full_duplex:m?1:0}}:{}),(null==k?void 0:k.vc)?{vc:{vc:null!==(f=null==k?void 0:k.vc)&&void 0!==f?f:0,voice_name:(null==k?void 0:k.voice_name)||""}}:{}),C),payload:{audio:Object.assign(Object.assign(Object.assign({},M),{sample_rate:null!==(d=null==M?void 0:M.sample_rate)&&void 0!==d?d:y||2!==b(this,Os,"m",Zs).call(this)?16e3:24e3,status:t,audio:G(new Uint8Array(e)),frame_size:e.byteLength}),A),avatar:w||[]}},!0),r.abrupt("return",v);case 20:case"end":return r.stop()}}),i,this,[[0,5]])})))}},{key:"writeCmd",value:function(e,t){return w(this,void 0,void 0,r().mark((function n(){var i,o,a;return r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return o=W(),r.prev=1,r.next=4,b(this,Fs,"f").call(this);case 4:r.next=10;break;case 6:return r.prev=6,r.t0=r.catch(1),ke.record($.error,"[writeCmd]",r.t0),r.abrupt("return",Promise.reject(new P(X.message,X.code,K.InvalidConnect)));case 10:a=null,r.prev=11,r.t1=e,r.next="action"===r.t1?15:17;break;case 15:return a={cmd_text:{avatar:[{type:e,value:t}]}},r.abrupt("break",17);case 17:r.next=21;break;case 19:r.prev=19,r.t2=r.catch(11);case 21:return a&&b(this,Ds,"f").call(this,{header:{app_id:(null===(i=b(this,Ws,"f"))||void 0===i?void 0:i.appId)||"",request_id:o,ctrl:"cmd"},payload:a}),r.abrupt("return",o);case 23:case"end":return r.stop()}}),n,this,[[1,6],[11,19]])})))}},{key:"recorder",get:function(){return b(this,As,"f")}},{key:"destroyRecorder",value:function(){var e,t;null===(e=b(this,As,"f"))||void 0===e||e.stopRecord(),null===(t=b(this,As,"f"))||void 0===t||t.destroy(),_(this,As,void 0,"f")}},{key:"createRecorder",value:function(e){var t,r=this;if(b(this,As,"f")&&!(null===(t=b(this,As,"f"))||void 0===t?void 0:t.isDestroyed()))return b(this,As,"f");var n=-1;return _(this,As,new At(Object.assign({sampleRate:16e3},e)),"f").on(Ge.recoder_audio,(function(e){var t,i;if(b(r,Ss,"f")===k.connected){var o=e.frameStatus;-1===n&&e.frameStatus!==x.end&&(o=0,n=0),r.writeAudio(e.s16buffer,o,{nlp:null!==(i=null===(t=null==e?void 0:e.extend)||void 0===t?void 0:t.nlp)&&void 0!==i&&i,full_duplex:e.fullDuplex?1:0}).catch((function(e){ke.record($.error,"[writeAudio]",e)}))}else ke.record($.info,"[writeAudio]","channel disconnected, ignore audio data")})),b(this,As,"f")}},{key:"createPlayer",value:function(){return b(this,Es,"f")?b(this,Es,"f"):_(this,Es,new Ke,"f")}},{key:"stop",value:function(){b(this,Os,"m",fc).call(this,!0)}},{key:"destroy",value:function(){var e,t;null===(e=b(this,Es,"f"))||void 0===e||e.destroy(),_(this,Es,void 0,"f"),b(this,Os,"m",fc).call(this,!0),null===(t=b(this,As,"f"))||void 0===t||t.destroy(),_(this,As,void 0,"f"),v(u(n.prototype),"destroy",this).call(this)}}],[{key:"getVersion",value:function(){return"3.1.1-1011"}},{key:"setLogLevel",value:function(e){ke.setLogLevel(e)}}]),n}();Ms=new WeakMap,Ss=new WeakMap,Es=new WeakMap,As=new WeakMap,Ps=new WeakMap,Ws=new WeakMap,Ts=new WeakMap,Is=new WeakMap,Cs=new WeakMap,Ls=new WeakMap,Rs=new WeakMap,Us=new WeakMap,zs=new WeakMap,Bs=new WeakMap,Fs=new WeakMap,Ds=new WeakMap,Ns=new WeakMap,qs=new WeakMap,Vs=new WeakMap,Hs=new WeakMap,Gs=new WeakMap,Ks=new WeakMap,$s=new WeakMap,Xs=new WeakMap,Ys=new WeakMap,Qs=new WeakMap,ec=new WeakMap,tc=new WeakMap,rc=new WeakMap,nc=new WeakMap,ic=new WeakMap,oc=new WeakMap,cc=new WeakMap,uc=new WeakMap,lc=new WeakMap,Os=new WeakSet,Zs=function(){var e,t,r;return null!==(r=null===(t=null===(e=b(this,Cs,"f"))||void 0===e?void 0:e.avatar)||void 0===t?void 0:t.audio_format)&&void 0!==r?r:1},Js=function(){var e,t,r,n,i,o,a=null!==(e=b(this,Cs,"f").stream)&&void 0!==e?e:{},s=a.protocol,c=void 0===s?"xrtc":s,u=a.bitrate,l=void 0===u?1e6:u,f=g(a,["protocol","bitrate"]),d=b(this,Cs,"f").avatar,v=d.avatar_id,h=d.width,p=d.height,y=g(d,["avatar_id","width","height"]),m=b(this,Cs,"f").tts,w=m.vcn,_=m.speed,x=m.pitch,k=m.volume,j=g(m,["vcn","speed","pitch","volume"]),O=b(this,Cs,"f").subtitle||{},M=O.subtitle,S=O.font_color,E=g(O,["subtitle","font_color"]),A=b(this,Cs,"f").background,P=b(this,Cs,"f").air,W={};return P&&(W={air:P}),Object.assign({header:{app_id:(null===(t=b(this,Ws,"f"))||void 0===t?void 0:t.appId)||"",ctrl:"start",scene_id:(null===(r=b(this,Ws,"f"))||void 0===r?void 0:r.sceneId)||"",scene_version:(null===(n=b(this,Ws,"f"))||void 0===n?void 0:n.sceneVersion)||""},parameter:Object.assign(Object.assign({avatar_dispatch:{enable_action_status:null!==(o=null===(i=b(this,Cs,"f").avatar_dispatch)||void 0===i?void 0:i.enable_action_status)&&void 0!==o?o:0},avatar:Object.assign(Object.assign({stream:Object.assign(Object.assign({},f),{protocol:c,bitrate:Math.floor((l||1e6)/1024)}),avatar_id:v,width:h,height:p},y),{audio_format:b(this,Os,"m",Zs).call(this)}),tts:Object.assign({vcn:w,speed:null!=_?_:50,pitch:null!=x?x:50,volume:null!=k?k:100,audio:{sample_rate:2===b(this,Os,"m",Zs).call(this)?24e3:16e3}},j)},M?{subtitle:Object.assign({subtitle:M,font_color:null!=S?S:"#FFFFFF"},E)}:{}),W)},(null==A?void 0:A.data)||b(this,Ls,"f")?{payload:Object.assign({background:A},b(this,Ls,"f")?{preload_resources:b(this,Ls,"f")}:{})}:void 0)},ac=function(e,t){if(0===e.length||t<e[0].bg)return{target:null,index:-1};if(t>e[e.length-1].ed)return{target:null,index:-2};for(var r=0,n=e.length-1;r<=n;){var i=Math.floor((r+n)/2),o=e[i],a=o.bg,s=o.ed;if(t>=a&&t<=s)return{target:e[i],index:i};t<a?n=i-1:r=i+1}return{target:null,index:-1}},sc=function e(){var t=this;_(this,nc,requestAnimationFrame((function(r){b(t,rc,"f")||_(t,rc,r,"f");var n=null;if(b(t,ec,"f").length&&b(t,rc,"f")){var i=b(t,Os,"m",ac).call(t,b(t,ec,"f"),r-b(t,rc,"f")-b(t,oc,"f")),o=i.target,a=i.index;o?(n=o,t.emit(Me.subtitle_info,o),b(t,ec,"f").splice(0,a+1)):-2===a&&(b(t,ec,"f").length=0,cancelAnimationFrame(b(t,nc,"f")),_(t,nc,0,"f"))}b(t,ec,"f").length?b(t,Os,"m",e).call(t):(cancelAnimationFrame(b(t,nc,"f")),_(t,nc,0,"f"),_(t,ic,setTimeout((function(){t.emit(Me.subtitle_info)}),((null==n?void 0:n.ed)||0)-((null==n?void 0:n.bg)||0)+1e3),"f"))})),"f")},fc=function(){var e,t,r,n,i,o=arguments.length>0&&void 0!==arguments[0]&&arguments[0];_(this,Ps,!1,"f"),clearInterval(b(this,Hs,"f")),clearTimeout(b(this,Gs,"f")),o&&_(this,Ns,void 0,"f"),clearTimeout(b(this,qs,"f")),_(this,Ls,void 0,"f"),_(this,Ss,k.disconnected,"f");var a=b(this,zs,"f");null===(e=b(this,As,"f"))||void 0===e||e.stopRecord(),null===(t=b(this,Es,"f"))||void 0===t||t.stop(),(null==a?void 0:a.readyState)===je.OPEN?(a.onclose=null,a.onmessage=null,b(this,Ds,"f").call(this,{header:{request_id:W(),app_id:(null===(r=b(this,Ws,"f"))||void 0===r?void 0:r.appId)||"",ctrl:"stop"}}),a.close()):(null===(n=b(this,Us,"f"))||void 0===n||n.abort(),null===(i=null==a?void 0:a.close)||void 0===i||i.call(a)),b(this,zs,"f")&&(b(this,uc,"f").call(this,o),_(this,Us,void 0,"f"),_(this,zs,void 0,"f"),_(this,Rs,void 0,"f"))};export{vc as A,P as C,K as E,ke as L,Se as P,Ge as R,Me as S,Et as U,c as _,o as a,e as b,d as c,$ as d,b as e,_ as f,s as g,w as h,r as i,ee as j,T as k,v as l,u as m,He as n,i as o,Rt as p};
