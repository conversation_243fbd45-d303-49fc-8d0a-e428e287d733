<script setup lang="ts">
import { ref } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'

// 偏好设置页
// 用于设置面试难度和类型偏好

// 面试难度设置
const difficultyLevel = ref(2) // 默认中等难度
const difficultyLabels = ['简单', '中等', '困难']

// 面试类型设置
const interviewTypes = ref([
  { id: 'tech', name: '技术面试', checked: true },
  { id: 'hr', name: 'HR面试', checked: true },
  { id: 'comprehensive', name: '综合面试', checked: false },
  { id: 'group', name: '群面', checked: false },
])

// 面试行业偏好
const industryPreferences = ref([
  { id: 'internet', name: '互联网', checked: true },
  { id: 'finance', name: '金融', checked: false },
  { id: 'enterprise', name: '企业服务', checked: false },
  { id: 'education', name: '教育', checked: false },
  { id: 'medical', name: '医疗健康', checked: false },
  { id: 'iot', name: '物联网', checked: false },
])

// 语言偏好
const languagePreference = ref('中文')
const languageOptions = ['中文', '英文', '中英混合']

// 提醒设置
const reminderSettings = ref({
  enabled: true,
  frequency: '每周三次',
  time: '19:00',
})

// 频率选项
const frequencyOptions = ['每天', '每周三次', '每周一次', '每月']

// 时间选项
const timeOptions = ['08:00', '12:00', '15:00', '19:00', '21:00']

// 更新面试难度
const updateDifficulty = (level: number) => {
  difficultyLevel.value = level
  saveSettings()
}

// 更新面试类型选择
const updateInterviewType = (idx: number) => {
  interviewTypes.value[idx].checked = !interviewTypes.value[idx].checked
  saveSettings()
}

// 更新行业偏好
const updateIndustry = (idx: number) => {
  industryPreferences.value[idx].checked = !industryPreferences.value[idx].checked
  saveSettings()
}

// 更新语言偏好
const updateLanguage = (e: any) => {
  const index = Number(e.detail.value)
  languagePreference.value = languageOptions[index]
  saveSettings()
}

// 更新提醒开关
const updateReminderEnabled = (e: any) => {
  reminderSettings.value.enabled = e.detail.value
  saveSettings()
}

// 更新提醒频率
const updateFrequency = (e: any) => {
  const index = Number(e.detail.value)
  reminderSettings.value.frequency = frequencyOptions[index]
  saveSettings()
}

// 更新提醒时间
const updateTime = (e: any) => {
  const index = Number(e.detail.value)
  reminderSettings.value.time = timeOptions[index]
  saveSettings()
}

// 保存设置到本地存储
const saveSettings = () => {
  const settings = {
    difficultyLevel: difficultyLevel.value,
    interviewTypes: interviewTypes.value,
    industryPreferences: industryPreferences.value,
    languagePreference: languagePreference.value,
    reminderSettings: reminderSettings.value,
  }

  uni.setStorageSync('userPreferences', JSON.stringify(settings))
  uni.showToast({
    title: '设置已保存',
    icon: 'success',
    duration: 1000,
  })
}

// 恢复默认设置
const resetSettings = () => {
  uni.showModal({
    title: '恢复默认',
    content: '确定要恢复默认设置吗？',
    success: (res) => {
      if (res.confirm) {
        // 重置为默认值
        difficultyLevel.value = 2
        interviewTypes.value.forEach((item) => {
          item.checked = item.id === 'tech' || item.id === 'hr'
        })
        industryPreferences.value.forEach((item) => {
          item.checked = item.id === 'internet'
        })
        languagePreference.value = '中文'
        reminderSettings.value = {
          enabled: true,
          frequency: '每周三次',
          time: '19:00',
        }

        // 保存重置后的设置
        saveSettings()
      }
    },
  })
}

// 初始化加载已保存的设置
const loadSettings = () => {
  const savedSettings = uni.getStorageSync('userPreferences')
  if (savedSettings) {
    const settings = JSON.parse(savedSettings)

    // 更新各项设置
    difficultyLevel.value = settings.difficultyLevel

    if (settings.interviewTypes) {
      interviewTypes.value = settings.interviewTypes
    }

    if (settings.industryPreferences) {
      industryPreferences.value = settings.industryPreferences
    }

    if (settings.languagePreference) {
      languagePreference.value = settings.languagePreference
    }

    if (settings.reminderSettings) {
      reminderSettings.value = settings.reminderSettings
    }
  }
}

// 页面加载时初始化设置
loadSettings()
</script>
<template>
  <view class="settings-page">
    <HeadBar title="偏好设置" :show-back="true" :show-right-button="false" />
    <view class="settings-content">
    <!-- 面试难度设置 -->
    <view class="settings-section" style="margin-top: 80rpx">
      <view class="section-title">面试难度</view>
      <view class="difficulty-slider">
        <view
          class="difficulty-option"
          v-for="(label, level) in difficultyLabels"
          :key="level"
          :class="{ active: difficultyLevel === level }"
          @click="updateDifficulty(level)"
        >
          <text>{{ label }}</text>
        </view>
      </view>
    </view>

    <!-- 面试类型设置 -->
    <view class="settings-section">
      <view class="section-title">面试类型</view>
      <view class="settings-card">
        <view
          class="type-item"
          v-for="(type, idx) in interviewTypes"
          :key="type.id"
          :class="{ active: type.checked }"
          @click="updateInterviewType(idx)"
        >
          <text>{{ type.name }}</text>
          <text class="check-icon" v-if="type.checked">✓</text>
        </view>
      </view>
    </view>

    <!-- 行业偏好设置 -->
    <view class="settings-section">
      <view class="section-title">行业偏好</view>
      <view class="industry-grid">
        <view
          class="industry-item"
          v-for="(industry, idx) in industryPreferences"
          :key="industry.id"
          :class="{ active: industry.checked }"
          @click="updateIndustry(idx)"
        >
          <text>{{ industry.name }}</text>
        </view>
      </view>
    </view>

    <!-- 语言偏好 -->
    <view class="settings-section">
      <view class="section-title">语言偏好</view>
      <view class="settings-card">
        <view class="form-item">
          <text class="form-label">面试语言</text>
          <picker
            :range="languageOptions"
            :value="languageOptions.indexOf(languagePreference)"
            @change="updateLanguage"
          >
            <view class="picker-view">
              <text>{{ languagePreference }}</text>
              <text class="i-fa-solid-chevron-down" style="font-size: 22rpx; color: #999"></text>
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 提醒设置 -->
    <view class="settings-section">
      <view class="section-title">提醒设置</view>
      <view class="settings-card">
        <view class="form-item reminder-switch">
          <text class="form-label">学习提醒</text>
          <switch
            :checked="reminderSettings.enabled"
            color="#00B39A"
            @change="updateReminderEnabled"
          />
        </view>

        <template v-if="reminderSettings.enabled">
          <view class="form-item">
            <text class="form-label">提醒频率</text>
            <picker
              :range="frequencyOptions"
              :value="frequencyOptions.indexOf(reminderSettings.frequency)"
              @change="updateFrequency"
            >
              <view class="picker-view">
                <text>{{ reminderSettings.frequency }}</text>
                <text class="i-fa-solid-chevron-down" style="font-size: 22rpx; color: #999"></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">提醒时间</text>
            <picker
              :range="timeOptions"
              :value="timeOptions.indexOf(reminderSettings.time)"
              @change="updateTime"
            >
              <view class="picker-view">
                <text>{{ reminderSettings.time }}</text>
                <text class="i-fa-solid-chevron-down" style="font-size: 22rpx; color: #999"></text>
              </view>
            </picker>
          </view>
        </template>
      </view>
    </view>

    <!-- 重置按钮 -->
    <view class="reset-btn" @click="resetSettings">
      <text class="i-fa-solid-undo" style="margin-right: 8rpx; font-size: 20rpx"></text>
      <text>恢复默认设置</text>
  </view>
    </view>
  </view>
</template>
<style scoped lang="scss">
.settings-page {
  min-height: 100vh;
  background: #f5f5f5;
}
.settings-content {
  padding: 0 40rpx 0 40rpx;
}
.page-title {
  margin-bottom: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
}
.settings-section {
  margin-bottom: 40rpx;
}
.section-title {
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}
.difficulty-slider {
  display: flex;
  overflow: hidden;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.difficulty-option {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;

  &.active {
    font-weight: bold;
    color: #fff;
    background: #00b39a;
  }
}
.settings-card {
  padding: 16rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    font-weight: 500;
    color: #00b39a;
  }
}
.check-icon {
  font-weight: bold;
  color: #00b39a;
}
.industry-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.industry-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(33.33% - 16rpx);
  height: 80rpx;
  font-size: 26rpx;
  color: #666;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s;

  &.active {
    font-weight: 500;
    color: #fff;
    background: #00b39a;
  }
}
.form-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}
.reminder-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #333;
}
.picker-view {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #f5f5f5;
  border-radius: 12rpx;
}
.reset-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  margin-top: 48rpx;
  font-size: 28rpx;
  color: #666;
  background: #fff;
  border: 1rpx solid #ddd;
  border-radius: 44rpx;

  &:active {
    background: #f5f5f5;
  }
}
</style>
