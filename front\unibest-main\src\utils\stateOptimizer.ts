/**
 * State Optimizer
 * Provides efficient state management with batching, memoization, and selective updates
 */

import { performanceMonitor } from './performanceMonitor'

interface StateUpdate<T> {
  key: string
  value: T
  timestamp: number
  priority: 'high' | 'medium' | 'low'
}

interface BatchConfig {
  delay: number
  maxBatchSize: number
  priorityThreshold: 'high' | 'medium' | 'low'
}

interface MemoizedComputation<T> {
  result: T
  dependencies: any[]
  timestamp: number
  hitCount: number
}

class StateOptimizer {
  private pendingUpdates = new Map<string, StateUpdate<any>>()
  private batchTimer: number | null = null
  private memoCache = new Map<string, MemoizedComputation<any>>()
  private updateCallbacks = new Map<string, Set<Function>>()
  private readonly DEFAULT_BATCH_CONFIG: BatchConfig = {
    delay: 16, // ~60fps
    maxBatchSize: 50,
    priorityThreshold: 'medium',
  }

  /**
   * Batch state updates to prevent excessive re-renders
   */
  batchUpdate<T>(key: string, value: T, priority: 'high' | 'medium' | 'low' = 'medium'): void {
    performanceMonitor.incrementCounter('stateOptimizer.batch-update')

    const update: StateUpdate<T> = {
      key,
      value,
      timestamp: Date.now(),
      priority,
    }

    this.pendingUpdates.set(key, update)

    // High priority updates are processed immediately
    if (priority === 'high') {
      this.flushUpdates()
      return
    }

    // Schedule batch processing
    if (!this.batchTimer) {
      this.batchTimer = window.setTimeout(() => {
        this.flushUpdates()
      }, this.DEFAULT_BATCH_CONFIG.delay)
    }

    // Force flush if batch size limit reached
    if (this.pendingUpdates.size >= this.DEFAULT_BATCH_CONFIG.maxBatchSize) {
      this.flushUpdates()
    }
  }

  /**
   * Memoize expensive computations
   */
  memoize<T>(key: string, computeFn: () => T, dependencies: any[] = []): T {
    performanceMonitor.startTiming(`stateOptimizer.memoize.${key}`)

    const cached = this.memoCache.get(key)

    // Check if cached result is still valid
    if (cached && this.areDependenciesEqual(cached.dependencies, dependencies)) {
      cached.hitCount++
      performanceMonitor.endTiming(`stateOptimizer.memoize.${key}`)
      performanceMonitor.incrementCounter('stateOptimizer.memo-hit')
      return cached.result
    }

    // Compute new result
    const result = computeFn()

    this.memoCache.set(key, {
      result,
      dependencies: [...dependencies],
      timestamp: Date.now(),
      hitCount: cached ? cached.hitCount : 0,
    })

    performanceMonitor.endTiming(`stateOptimizer.memoize.${key}`)
    performanceMonitor.incrementCounter('stateOptimizer.memo-miss')

    return result
  }

  /**
   * Create a debounced function to prevent excessive calls
   */
  debounce<T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string,
  ): (...args: Parameters<T>) => void {
    let timeoutId: number | null = null
    const debounceKey = key || fn.name || 'anonymous'

    return (...args: Parameters<T>) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      timeoutId = window.setTimeout(() => {
        performanceMonitor.incrementCounter(`stateOptimizer.debounce.${debounceKey}`)
        fn(...args)
        timeoutId = null
      }, delay)
    }
  }

  /**
   * Create a throttled function to limit execution frequency
   */
  throttle<T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string,
  ): (...args: Parameters<T>) => void {
    let lastExecution = 0
    const throttleKey = key || fn.name || 'anonymous'

    return (...args: Parameters<T>) => {
      const now = Date.now()

      if (now - lastExecution >= delay) {
        performanceMonitor.incrementCounter(`stateOptimizer.throttle.${throttleKey}`)
        fn(...args)
        lastExecution = now
      }
    }
  }

  /**
   * Subscribe to state changes
   */
  subscribe(key: string, callback: Function): () => void {
    if (!this.updateCallbacks.has(key)) {
      this.updateCallbacks.set(key, new Set())
    }

    this.updateCallbacks.get(key)!.add(callback)

    // Return unsubscribe function
    return () => {
      const callbacks = this.updateCallbacks.get(key)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.updateCallbacks.delete(key)
        }
      }
    }
  }

  /**
   * Create a computed property that updates when dependencies change
   */
  computed<T>(
    key: string,
    computeFn: () => T,
    dependencies: string[],
  ): { value: T; subscribe: (callback: Function) => () => void } {
    let currentValue = this.memoize(
      key,
      computeFn,
      dependencies.map((dep) => this.getStateValue(dep)),
    )

    const subscribe = (callback: Function) => {
      const unsubscribers = dependencies.map((dep) =>
        this.subscribe(dep, () => {
          const newValue = this.memoize(
            key,
            computeFn,
            dependencies.map((d) => this.getStateValue(d)),
          )
          if (newValue !== currentValue) {
            currentValue = newValue
            callback(newValue)
          }
        }),
      )

      return () => {
        unsubscribers.forEach((unsub) => unsub())
      }
    }

    return {
      get value() {
        return currentValue
      },
      subscribe,
    }
  }

  /**
   * Optimize reactive object updates
   */
  optimizeReactive<T extends Record<string, any>>(
    obj: T,
    options: {
      deepWatch?: boolean
      batchUpdates?: boolean
      memoizeGetters?: boolean
    } = {},
  ): T {
    const { deepWatch = false, batchUpdates = true, memoizeGetters = false } = options

    return new Proxy(obj, {
      set: (target, property, value) => {
        const key = String(property)
        const oldValue = target[property]

        // Skip update if value hasn't changed
        if (oldValue === value) {
          return true
        }

        // Deep comparison for objects if enabled
        if (deepWatch && typeof value === 'object' && typeof oldValue === 'object') {
          if (this.deepEqual(oldValue, value)) {
            return true
          }
        }

        target[property] = value

        // Batch or immediate update
        if (batchUpdates) {
          this.batchUpdate(key, value, 'medium')
        } else {
          this.notifySubscribers(key, value)
        }

        return true
      },

      get: (target, property) => {
        const key = String(property)
        const value = target[property]

        // Memoize getter results if enabled
        if (memoizeGetters && typeof value === 'function') {
          return this.memoize(`getter.${key}`, () => value.call(target))
        }

        return value
      },
    })
  }

  /**
   * Clear memoization cache
   */
  clearMemoCache(pattern?: RegExp): number {
    let cleared = 0

    if (pattern) {
      for (const key of this.memoCache.keys()) {
        if (pattern.test(key)) {
          this.memoCache.delete(key)
          cleared++
        }
      }
    } else {
      cleared = this.memoCache.size
      this.memoCache.clear()
    }

    performanceMonitor.incrementCounter('stateOptimizer.cache-cleared')
    return cleared
  }

  /**
   * Get memoization statistics
   */
  getMemoStats(): {
    size: number
    hitRate: number
    entries: Array<{ key: string; hitCount: number }>
  } {
    const entries = Array.from(this.memoCache.entries()).map(([key, memo]) => ({
      key,
      hitCount: memo.hitCount,
    }))

    const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0)
    const totalCalls = totalHits + entries.length // Approximation
    const hitRate = totalCalls > 0 ? totalHits / totalCalls : 0

    return {
      size: this.memoCache.size,
      hitRate,
      entries: entries.sort((a, b) => b.hitCount - a.hitCount),
    }
  }

  /**
   * Force flush all pending updates
   */
  flushUpdates(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = null
    }

    if (this.pendingUpdates.size === 0) {
      return
    }

    performanceMonitor.startTiming('stateOptimizer.flushUpdates')

    // Sort updates by priority
    const updates = Array.from(this.pendingUpdates.values()).sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    // Process updates
    for (const update of updates) {
      this.notifySubscribers(update.key, update.value)
    }

    this.pendingUpdates.clear()

    performanceMonitor.endTiming('stateOptimizer.flushUpdates')
    performanceMonitor.incrementCounter('stateOptimizer.batch-flushed')
  }

  // Private methods

  private areDependenciesEqual(deps1: any[], deps2: any[]): boolean {
    if (deps1.length !== deps2.length) {
      return false
    }

    return deps1.every((dep, index) => {
      const other = deps2[index]

      if (typeof dep === 'object' && typeof other === 'object') {
        return this.deepEqual(dep, other)
      }

      return dep === other
    })
  }

  private deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) {
      return true
    }

    if (obj1 == null || obj2 == null) {
      return obj1 === obj2
    }

    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return false
    }

    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)

    if (keys1.length !== keys2.length) {
      return false
    }

    return keys1.every((key) => keys2.includes(key) && this.deepEqual(obj1[key], obj2[key]))
  }

  private notifySubscribers(key: string, value: any): void {
    const callbacks = this.updateCallbacks.get(key)
    if (callbacks) {
      callbacks.forEach((callback) => {
        try {
          callback(value)
        } catch (error) {
          console.error(`Error in state update callback for ${key}:`, error)
        }
      })
    }
  }

  private getStateValue(key: string): any {
    // This would integrate with your actual state management system
    // For now, return undefined as placeholder
    return undefined
  }
}

// Export singleton instance
export const stateOptimizer = new StateOptimizer()

// Export class for testing
export { StateOptimizer }
