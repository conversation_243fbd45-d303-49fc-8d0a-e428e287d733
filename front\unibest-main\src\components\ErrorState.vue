<template>
  <view class="error-state">
    <view class="error-content">
      <!-- 错误图标 -->
      <view class="error-icon">
        <text 
          :class="iconType" 
          :style="{ 
            fontSize: iconSize + 'rpx', 
            color: iconColor 
          }"
        ></text>
      </view>
      
      <!-- 错误信息 -->
      <view class="error-info">
        <text class="error-title">{{ title }}</text>
        <text class="error-description">{{ description }}</text>
      </view>
      
      <!-- 操作按钮 -->
      <view class="error-actions" v-if="showActions">
        <button 
          v-if="showRetry"
          class="retry-button" 
          @click="handleRetry"
          :loading="retrying"
        >
          {{ retrying ? '重试中...' : '重试' }}
        </button>
        
        <button 
          v-if="showSecondary"
          class="secondary-button" 
          @click="handleSecondary"
        >
          {{ secondaryText }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

/**
 * @description 错误状态组件 Props
 */
interface Props {
  /** 错误类型 */
  type?: 'network' | 'empty' | 'error' | 'timeout' | 'forbidden' | 'notfound'
  /** 自定义标题 */
  title?: string
  /** 自定义描述 */
  description?: string
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 是否显示重试按钮 */
  showRetry?: boolean
  /** 是否显示次要按钮 */
  showSecondary?: boolean
  /** 次要按钮文本 */
  secondaryText?: string
  /** 图标大小 */
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'error',
  showActions: true,
  showRetry: true,
  showSecondary: false,
  secondaryText: '返回首页',
  size: 'medium',
})

/**
 * @description 事件定义
 */
const emit = defineEmits<{
  retry: []
  secondary: []
}>()

const retrying = ref(false)

/**
 * @description 错误配置映射
 */
const errorConfigs = {
  network: {
    icon: 'mdi-wifi-off',
    title: '网络连接失败',
    description: '请检查网络连接后重试',
    color: '#FF6B6B',
  },
  empty: {
    icon: 'mdi-folder-open-outline',
    title: '暂无数据',
    description: '当前没有可显示的内容',
    color: '#95A5A6',
  },
  error: {
    icon: 'mdi-close-circle-outline',
    title: '出现错误',
    description: '服务暂时不可用，请稍后重试',
    color: '#FF6B6B',
  },
  timeout: {
    icon: 'mdi-clock-outline',
    title: '请求超时',
    description: '请求时间过长，请检查网络后重试',
    color: '#F39C12',
  },
  forbidden: {
    icon: 'mdi-lock-outline',
    title: '访问受限',
    description: '您没有权限访问此内容',
    color: '#E74C3C',
  },
  notfound: {
    icon: 'mdi-magnify',
    title: '页面不存在',
    description: '抱歉，您访问的页面不存在',
    color: '#34495E',
  },
}

/**
 * @description 当前错误配置
 */
const currentConfig = computed(() => errorConfigs[props.type])

/**
 * @description 显示标题
 */
const title = computed(() => props.title || currentConfig.value.title)

/**
 * @description 显示描述
 */
const description = computed(() => props.description || currentConfig.value.description)

/**
 * @description 图标类型
 */
const iconType = computed(() => currentConfig.value.icon)

/**
 * @description 图标颜色
 */
const iconColor = computed(() => currentConfig.value.color)

/**
 * @description 图标大小
 */
const iconSize = computed(() => {
  const sizeMap = {
    small: 60,
    medium: 80,
    large: 100,
  }
  return sizeMap[props.size]
})

/**
 * @description 处理重试
 */
const handleRetry = async () => {
  if (retrying.value) return
  
  retrying.value = true
  
  try {
    emit('retry')
    
    // 模拟重试延迟
    await new Promise(resolve => setTimeout(resolve, 500))
  } finally {
    retrying.value = false
  }
}

/**
 * @description 处理次要操作
 */
const handleSecondary = () => {
  emit('secondary')
}
</script>

<style scoped lang="scss">
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 40rpx;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 500rpx;
}

.error-icon {
  margin-bottom: 30rpx;
  opacity: 0.8;
  animation: fadeInDown 0.6s ease-out;
  
  text {
    // MDI图标支持
    &::before {
      font-family: 'Material Design Icons';
    }
  }
}

.error-info {
  margin-bottom: 40rpx;
  animation: fadeInUp 0.6s ease-out 0.2s both;
  
  .error-title {
    display: block;
    margin-bottom: 16rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    line-height: 1.4;
  }
  
  .error-description {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
  animation: fadeInUp 0.6s ease-out 0.4s both;
  
  .retry-button {
    padding: 24rpx 48rpx;
    background: linear-gradient(135deg, #00C9A7 0%, #4FD1C7 100%);
    color: #fff;
    border: none;
    border-radius: 50rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #00B399 0%, #47C7BD 100%);
    }
    
    &[loading] {
      opacity: 0.7;
    }
  }
  
  .secondary-button {
    padding: 20rpx 40rpx;
    background: transparent;
    color: #666;
    border: 2rpx solid #E0E0E0;
    border-radius: 50rpx;
    font-size: 26rpx;
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.98);
      border-color: #00C9A7;
      color: #00C9A7;
    }
  }
}

// 动画定义
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .error-state {
    min-height: 300rpx;
    padding: 30rpx;
  }
  
  .error-content {
    max-width: 400rpx;
  }
  
  .error-info {
    .error-title {
      font-size: 28rpx;
    }
    
    .error-description {
      font-size: 24rpx;
    }
  }
  
  .error-actions {
    .retry-button {
      padding: 20rpx 40rpx;
      font-size: 26rpx;
    }
    
    .secondary-button {
      padding: 18rpx 36rpx;
      font-size: 24rpx;
    }
  }
}
</style> 