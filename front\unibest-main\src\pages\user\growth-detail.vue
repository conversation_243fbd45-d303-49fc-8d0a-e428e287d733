<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import RadarChart from '@/components/RadarChart.vue'
// @ts-ignore
import TrendChart from '@/components/TrendChart.vue'
// @ts-ignore
import Share from '@/components/Share.vue'

/**
 * @description 成长曲线详细分析页面
 * <AUTHOR>
 */

// 页面加载状态
const isLoading = ref(true)

// 分享相关状态
const showShareModal = ref(false)

/**
 * @description 能力维度数据
 */
interface AbilityData {
  name: string
  current: number
  previous: number
  max: number
  color: string
  icon: string
}

/**
 * @description 历史数据点
 */
interface HistoryPoint {
  date: string
  专业知识: number
  逻辑思维: number
  语言表达: number
  心理素质: number
  团队协作: number
}

/**
 * @description 能力数据
 */
const abilities = ref<AbilityData[]>([
  {
    name: '专业知识',
    current: 82,
    previous: 75,
    max: 100,
    color: '#00c9a7',
    icon: 'i-fa-solid-graduation-cap',
  },
  {
    name: '逻辑思维',
    current: 80,
    previous: 73,
    max: 100,
    color: '#4fd1c7',
    icon: 'i-fa-solid-brain',
  },
  {
    name: '语言表达',
    current: 85,
    previous: 78,
    max: 100,
    color: '#f59e0b',
    icon: 'i-fa-solid-comments',
  },
  {
    name: '心理素质',
    current: 75,
    previous: 68,
    max: 100,
    color: '#ef4444',
    icon: 'i-fa-solid-heart',
  },
  {
    name: '团队协作',
    current: 85,
    previous: 80,
    max: 100,
    color: '#8b5cf6',
    icon: 'i-fa-solid-users',
  },
])

/**
 * @description 历史数据
 */
const historyData = ref<HistoryPoint[]>([
  { date: '2024-01-01', 专业知识: 45, 逻辑思维: 40, 语言表达: 50, 心理素质: 35, 团队协作: 55 },
  { date: '2024-01-15', 专业知识: 52, 逻辑思维: 48, 语言表达: 55, 心理素质: 42, 团队协作: 60 },
  { date: '2024-02-01', 专业知识: 60, 逻辑思维: 55, 语言表达: 62, 心理素质: 50, 团队协作: 65 },
  { date: '2024-02-15', 专业知识: 68, 逻辑思维: 65, 语言表达: 70, 心理素质: 58, 团队协作: 72 },
  { date: '2024-03-01', 专业知识: 75, 逻辑思维: 73, 语言表达: 78, 心理素质: 68, 团队协作: 80 },
  { date: '2024-03-15', 专业知识: 82, 逻辑思维: 80, 语言表达: 85, 心理素质: 75, 团队协作: 85 },
])

/**
 * @description 当前选择的时间范围
 */
const selectedTimeRange = ref('3months')
const timeRangeOptions = [
  { value: '1month', label: '近1个月' },
  { value: '3months', label: '近3个月' },
  { value: '6months', label: '近6个月' },
  { value: 'all', label: '全部' },
]

/**
 * @description 当前选择的能力维度
 */
const selectedAbilities = ref(['专业知识', '逻辑思维', '语言表达', '心理素质', '团队协作'])

/**
 * @description 计算平均分
 */
const averageScore = computed(() => {
  const total = abilities.value.reduce((sum, ability) => sum + ability.current, 0)
  return Math.round(total / abilities.value.length)
})

/**
 * @description 计算总体提升
 */
const overallImprovement = computed(() => {
  const currentTotal = abilities.value.reduce((sum, ability) => sum + ability.current, 0)
  const previousTotal = abilities.value.reduce((sum, ability) => sum + ability.previous, 0)
  return currentTotal - previousTotal
})

/**
 * @description 获取最强能力
 */
const strongestAbility = computed(() => {
  return abilities.value.reduce((max, ability) => (ability.current > max.current ? ability : max))
})

/**
 * @description 获取最需要提升的能力
 */
const weakestAbility = computed(() => {
  return abilities.value.reduce((min, ability) => (ability.current < min.current ? ability : min))
})

/**
 * @description 获取进步最大的能力
 */
const mostImprovedAbility = computed(() => {
  return abilities.value.reduce((max, ability) => {
    const improvement = ability.current - ability.previous
    const maxImprovement = max.current - max.previous
    return improvement > maxImprovement ? ability : max
  })
})

/**
 * @description 分享数据
 */
const shareData = computed(() => {
  const currentDate = new Date().toLocaleDateString('zh-CN')
  return {
    title: '我的面试能力成长报告',
    content: `综合评分${averageScore.value}分，${strongestAbility.value.name}表现最佳(${strongestAbility.value.current}分)，${mostImprovedAbility.value.name}进步最大(+${mostImprovedAbility.value.current - mostImprovedAbility.value.previous}分)。持续成长中！`,
    url: window?.location?.href || 'https://example.com/growth-report',
    imageUrl: '', // 可以设置为报告截图或默认图片
    extra: {
      reportDate: currentDate,
      averageScore: averageScore.value,
      totalImprovement: overallImprovement.value
    }
  }
})

/**
 * @description 学习推荐数据
 */
const learningRecommendations = ref([
  {
    type: 'weak',
    ability: '心理素质',
    title: '面试心理调节训练',
    desc: '提升面试时的心理素质和抗压能力',
    resources: ['冥想练习', '模拟面试', '心理建设课程'],
    icon: 'i-fa-solid-heart',
    color: '#ef4444',
  },
  {
    type: 'strong',
    ability: '语言表达',
    title: '高级表达技巧',
    desc: '进一步提升语言表达的感染力和说服力',
    resources: ['演讲技巧', '表达艺术', '沟通心理学'],
    icon: 'i-fa-solid-comments',
    color: '#f59e0b',
  },
  {
    type: 'general',
    ability: '综合能力',
    title: '全面技能提升',
    desc: '综合提升各项面试核心能力',
    resources: ['面试真题', '行业知识', '团队合作训练'],
    icon: 'i-fa-solid-chart-line',
    color: '#00c9a7',
  },
])

/**
 * @description 时间范围切换
 */
const handleTimeRangeChange = (range: string) => {
  selectedTimeRange.value = range
  // 这里可以根据时间范围过滤数据
}

/**
 * @description 能力维度切换
 */
const toggleAbility = (abilityName: string) => {
  const index = selectedAbilities.value.indexOf(abilityName)
  if (index > -1) {
    if (selectedAbilities.value.length > 1) {
      selectedAbilities.value.splice(index, 1)
    }
  } else {
    selectedAbilities.value.push(abilityName)
  }
}

/**
 * @description 跳转到学习资源
 */
const goToLearning = (type: string) => {
  uni.navigateTo({
    url: `/pages/learning/index?type=${type}`,
    fail: () => {
      uni.showToast({
        title: '功能开发中',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 开始练习
 */
const startPractice = () => {
  uni.navigateTo({
    url: '/pages/learning/practice',
    fail: () => {
      uni.showToast({
        title: '功能开发中',
        icon: 'none',
      })
    },
  })
}

/**
 * @description 分享报告
 */
const shareReport = () => {
  showShareModal.value = true
}

/**
 * @description 处理分享成功
 */
const handleShareSuccess = (platform: string, result?: any) => {
  console.log('分享成功:', platform, result)
  uni.showToast({
    title: '分享成功',
    icon: 'success',
    duration: 2000,
  })
}

/**
 * @description 处理分享失败
 */
const handleShareError = (platform: string, error: Error) => {
  console.error('分享失败:', platform, error)
  uni.showToast({
    title: '分享失败，请重试',
    icon: 'none',
    duration: 2000,
  })
}

/**
 * @description 处理分享取消
 */
const handleShareCancel = () => {
  console.log('用户取消分享')
}

// 页面加载
onShow(() => {
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
})
</script>

<template>
  <view class="growth-detail-page">
    <!-- 顶部导航栏 -->
    <HeadBar
      title="成长分析报告"
      :show-back="true"
      :show-right-button="true"
      right-icon="i-fa-solid-share-alt"
      right-button-bg="rgba(255, 255, 255, 0.25)"
      right-button-border="rgba(255, 255, 255, 0.25)"
      @rightClick="shareReport()"
      :fixed="true"
    />

    <!-- 加载状态 -->
    <LoadingCard :visible="isLoading" text="正在分析您的成长数据..." />

    <!-- 内容区域 -->
    <view v-show="!isLoading" class="content-area">
      <!-- 总体概览 -->
      <view class="section overview-section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-chart-bar section-icon"></text>
            总体表现
          </text>
          <text class="section-subtitle">您的综合能力分析</text>
        </view>

        <view class="overview-grid">
          <view class="overview-card main-score">
            <view class="score-wrapper">
              <text class="score-value">{{ averageScore }}</text>
              <text class="score-unit">分</text>
            </view>
            <text class="score-label">综合评分</text>
            <view class="score-trend positive" v-if="overallImprovement > 0">
              <text class="i-fa-solid-arrow-up trend-icon"></text>
              <text class="trend-text">+{{ overallImprovement }}分</text>
            </view>
          </view>

          <view class="overview-card">
            <view class="card-icon" :style="{ backgroundColor: strongestAbility.color }">
              <text :class="strongestAbility.icon" class="icon-text"></text>
            </view>
            <text class="card-title">最强能力</text>
            <text class="card-value">{{ strongestAbility.name }}</text>
            <text class="card-score">{{ strongestAbility.current }}分</text>
          </view>

          <view class="overview-card">
            <view class="card-icon" :style="{ backgroundColor: mostImprovedAbility.color }">
              <text :class="mostImprovedAbility.icon" class="icon-text"></text>
            </view>
            <text class="card-title">进步最大</text>
            <text class="card-value">{{ mostImprovedAbility.name }}</text>
            <text class="card-improvement">
              +{{ mostImprovedAbility.current - mostImprovedAbility.previous }}分
            </text>
          </view>
        </view>
      </view>

      <!-- 时间范围选择器 -->
      <view class="section filter-section">
        <view class="section-header compact">
          <text class="section-title">
            <text class="i-fa-solid-filter section-icon"></text>
            分析维度
          </text>
        </view>

        <view class="filter-container">
          <view class="filter-row">
            <text class="filter-label">时间范围</text>
            <view class="time-selector">
              <view
                class="time-option"
                :class="{ active: selectedTimeRange === option.value }"
                v-for="option in timeRangeOptions"
                :key="option.value"
                @click="handleTimeRangeChange(option.value)"
              >
                <text class="option-text">{{ option.label }}</text>
              </view>
            </view>
          </view>

          <view class="filter-row">
            <text class="filter-label">能力维度</text>
            <view class="ability-selector">
              <view
                class="ability-option"
                :class="{ active: selectedAbilities.includes(ability.name) }"
                v-for="ability in abilities"
                :key="ability.name"
                @click="toggleAbility(ability.name)"
              >
                <view class="option-icon" :style="{ backgroundColor: ability.color }">
                  <text :class="ability.icon" class="icon-text"></text>
                </view>
                <text class="option-text">{{ ability.name }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 能力雷达图区域 -->
      <view class="section radar-section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-crosshairs section-icon"></text>
            能力雷达图
          </text>
          <text class="section-subtitle">多维度能力对比分析</text>
        </view>

        <view class="chart-container">
          <!-- 雷达图组件 -->
          <RadarChart
            :ability-data="abilities"
            :show-comparison="false"
            width="600rpx"
            height="500rpx"
          />
        </view>
      </view>

      <!-- 趋势分析区域 -->
      <view class="section trend-section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-chart-line section-icon"></text>
            成长趋势
          </text>
          <text class="section-subtitle">能力发展历程追踪</text>
        </view>

        <view class="chart-container">
          <!-- 趋势图组件 -->
          <TrendChart
            :trend-data="historyData"
            :selected-abilities="selectedAbilities"
            width="700rpx"
            height="400rpx"
          />
        </view>
      </view>

      <!-- 详细能力分析 -->
      <view class="section abilities-section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-tasks section-icon"></text>
            能力详细分析
          </text>
          <text class="section-subtitle">各项能力的具体表现</text>
        </view>

        <view class="abilities-list">
          <view class="ability-item" v-for="ability in abilities" :key="ability.name">
            <view class="ability-header">
              <view class="ability-icon" :style="{ backgroundColor: ability.color }">
                <text :class="ability.icon" class="icon-text"></text>
              </view>
              <view class="ability-info">
                <text class="ability-name">{{ ability.name }}</text>
                <view class="ability-scores">
                  <text class="current-score">{{ ability.current }}分</text>
                  <view
                    class="score-change"
                    :class="{ positive: ability.current > ability.previous }"
                  >
                    <text
                      class="i-fa-solid-arrow-up change-icon"
                      v-if="ability.current > ability.previous"
                    ></text>
                    <text class="i-fa-solid-minus change-icon" v-else></text>
                    <text class="change-text">
                      {{ ability.current - ability.previous > 0 ? '+' : ''
                      }}{{ ability.current - ability.previous }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <view class="ability-progress">
              <view class="progress-track">
                <view
                  class="progress-fill"
                  :style="{
                    width: (ability.current / ability.max) * 100 + '%',
                    backgroundColor: ability.color,
                  }"
                ></view>
              </view>
              <text class="progress-text">
                {{ Math.round((ability.current / ability.max) * 100) }}%
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 学习推荐 -->
      <view class="section recommendations-section">
        <view class="section-header">
          <text class="section-title">
            <text class="i-fa-solid-lightbulb section-icon"></text>
            个性化推荐
          </text>
          <text class="section-subtitle">基于您的能力分析的学习建议</text>
        </view>

        <view class="recommendations-container">
          <view
            class="recommendation-card"
            v-for="rec in learningRecommendations"
            :key="rec.ability"
            @click="goToLearning(rec.type)"
          >
            <view class="rec-header">
              <view class="rec-icon" :style="{ backgroundColor: rec.color }">
                <text :class="rec.icon" class="icon-text"></text>
              </view>
              <view class="rec-badge" :class="rec.type">
                <text class="badge-text">
                  {{
                    rec.type === 'weak'
                      ? '重点提升'
                      : rec.type === 'strong'
                        ? '优势强化'
                        : '全面发展'
                  }}
                </text>
              </view>
            </view>

            <view class="rec-content">
              <text class="rec-title">{{ rec.title }}</text>
              <text class="rec-desc">{{ rec.desc }}</text>

              <view class="rec-resources">
                <text
                  class="resource-tag"
                  v-for="resource in rec.resources.slice(0, 2)"
                  :key="resource"
                >
                  {{ resource }}
                </text>
                <text class="more-resources" v-if="rec.resources.length > 2">
                  +{{ rec.resources.length - 2 }}
                </text>
              </view>
            </view>

            <view class="rec-action">
              <text class="action-text">开始学习</text>
              <text class="i-fa-solid-arrow-right action-icon"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 快速操作 -->
      <view class="section actions-section">
        <view class="section-header compact">
          <text class="section-title">
            <text class="i-fa-solid-bolt section-icon"></text>
            快速行动
          </text>
        </view>

        <view class="actions-grid">
          <view class="action-card primary" @click="startPractice">
            <view class="action-icon">
              <text class="i-fa-solid-play action-icon-text"></text>
            </view>
            <text class="action-title">开始练习</text>
          </view>

          <view class="action-card secondary" @click="goToLearning('resources')">
            <view class="action-icon">
              <text class="i-fa-solid-book action-icon-text"></text>
            </view>
            <text class="action-title">学习资源</text>
          </view>

          <view class="action-card tertiary" @click="goToLearning('mock')">
            <view class="action-icon">
              <text class="i-fa-solid-users action-icon-text"></text>
            </view>
            <text class="action-title">模拟面试</text>
          </view>
        </view>
      </view>

      <!-- 底部空白 -->
      <view class="bottom-spacing"></view>
    </view>

    <!-- 分享组件 -->
    <Share
      :visible="showShareModal"
      :share-data="shareData"
      :platforms="['wechat', 'moments', 'qq', 'weibo', 'link']"
      :show-cancel="true"
      :mask-closable="true"
      @update:visible="showShareModal = $event"
      @share-success="handleShareSuccess"
      @share-error="handleShareError"
      @cancel="handleShareCancel"
    />
  </view>
</template>

<style lang="scss" scoped>
.growth-detail-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  overflow-x: hidden;

  // 优化背景装饰
  &::before {
    content: '';
    position: fixed;
    top: -30%;
    left: -30%;
    width: 160%;
    height: 160%;
    background: radial-gradient(circle at 25% 25%, rgba(0, 201, 167, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(79, 209, 199, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
    animation: backgroundFloat 25s ease-in-out infinite;
  }
}

// 内容区域优化
.content-area {
  position: relative;
  z-index: 1;
  margin-top: 100rpx;
  padding: 16rpx 20rpx 0 20rpx;
}

// 通用section样式优化
.section {
  position: relative;
  margin: 16rpx 0 24rpx 0;
  padding: 36rpx 28rpx 28rpx 28rpx;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20rpx) saturate(160%);
  -webkit-backdrop-filter: blur(20rpx) saturate(160%);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 201, 167, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  // 简化装饰元素
  &::before {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 28rpx;
    width: 60rpx;
    height: 60rpx;
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.05), rgba(79, 209, 199, 0.08));
    border-radius: 50%;
    animation: gentleFloat 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
  }

  > * {
    position: relative;
    z-index: 2;
  }

  // 悬停效果
  &:hover {
    transform: translateY(-2rpx);
    box-shadow:
      0 12rpx 40rpx rgba(0, 201, 167, 0.12),
      0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  }
}

// Section 标题样式优化
.section-header {
  margin-bottom: 28rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 201, 167, 0.08);

  &.compact {
    margin-bottom: 20rpx;
    padding-bottom: 12rpx;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 30rpx;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: 0.5rpx;
  margin-bottom: 6rpx;
}

.section-icon {
  font-size: 26rpx;
  color: #00c9a7;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 201, 167, 0.15));
}

.section-subtitle {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.6;
  font-weight: 400;
}

// 总体概览样式优化
.overview-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 20rpx;
  margin-top: 12rpx;
}

.overview-card {
  padding: 28rpx 20rpx 24rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(0, 201, 167, 0.08);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.06);
  transition: all 0.25s ease;
  text-align: center;

  &.main-score {
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    color: #ffffff;
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);

    .score-label {
      color: rgba(255, 255, 255, 0.95);
    }
  }

  &:active {
    transform: scale(0.98);
  }
}

.score-wrapper {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 6rpx;
  margin-bottom: 10rpx;
}

.score-value {
  font-size: 44rpx;
  font-weight: 800;
  line-height: 1;
}

.score-unit {
  font-size: 22rpx;
  font-weight: 600;
  opacity: 0.9;
}

.score-label {
  font-size: 22rpx;
  color: #64748b;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.score-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;

  &.positive {
    color: rgba(255, 255, 255, 0.95);
  }
}

.trend-icon {
  font-size: 14rpx;
}

.trend-text {
  font-size: 18rpx;
  font-weight: 600;
}

.card-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 14rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.icon-text {
  font-size: 18rpx;
  color: #ffffff;
}

.card-title {
  font-size: 20rpx;
  color: #64748b;
  margin-bottom: 6rpx;
  display: block;
  font-weight: 500;
}

.card-value {
  font-size: 24rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4rpx;
  display: block;
}

.card-score,
.card-improvement {
  font-size: 18rpx;
  color: #00c9a7;
  font-weight: 600;
  display: block;
}

// 筛选器样式优化
.filter-section {
  padding: 28rpx !important;
}

.filter-container {
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.05);
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4rpx;
}

.time-selector {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.time-option {
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(0, 201, 167, 0.15);
  border-radius: 18rpx;
  transition: all 0.25s ease;
  backdrop-filter: blur(5rpx);

  &.active {
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    border-color: transparent;
    box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.25);

    .option-text {
      color: #ffffff;
    }
  }

  &:active {
    transform: scale(0.96);
  }
}

.option-text {
  font-size: 22rpx;
  color: #00c9a7;
  font-weight: 500;
}

.ability-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.ability-option {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 14rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(0, 201, 167, 0.15);
  border-radius: 18rpx;
  transition: all 0.25s ease;
  backdrop-filter: blur(5rpx);

  &.active {
    background: rgba(0, 201, 167, 0.12);
    border-color: rgba(0, 201, 167, 0.25);
    box-shadow: 0 2rpx 8rpx rgba(0, 201, 167, 0.15);
  }

  &:active {
    transform: scale(0.96);
  }
}

.option-icon {
  width: 22rpx;
  height: 22rpx;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-text {
    font-size: 11rpx;
  }
}

// 图表容器样式
.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background: rgba(248, 250, 252, 0.4);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.05);
}

// 能力列表样式优化
.abilities-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.ability-item {
  padding: 20rpx;
  background: rgba(248, 250, 252, 0.6);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(0, 201, 167, 0.06);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 201, 167, 0.04);
  transition: all 0.25s ease;

  &:hover {
    background: rgba(248, 250, 252, 0.8);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.08);
  }
}

.ability-header {
  display: flex;
  align-items: center;
  gap: 14rpx;
  margin-bottom: 14rpx;
}

.ability-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);

  .icon-text {
    font-size: 18rpx;
  }
}

.ability-info {
  flex: 1;
}

.ability-name {
  font-size: 26rpx;
  font-weight: 700;
  color: #1e293b;
  display: block;
  margin-bottom: 6rpx;
}

.ability-scores {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.current-score {
  font-size: 30rpx;
  font-weight: 800;
  color: #00c9a7;
}

.score-change {
  display: flex;
  align-items: center;
  gap: 3rpx;
  padding: 3rpx 8rpx;
  background: rgba(239, 68, 68, 0.08);
  border-radius: 10rpx;

  &.positive {
    background: rgba(0, 201, 167, 0.08);

    .change-icon,
    .change-text {
      color: #00c9a7;
    }
  }
}

.change-icon {
  font-size: 12rpx;
  color: #ef4444;
}

.change-text {
  font-size: 18rpx;
  font-weight: 600;
  color: #ef4444;
}

.ability-progress {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.progress-track {
  flex: 1;
  height: 6rpx;
  background: rgba(0, 201, 167, 0.08);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-text {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 600;
  min-width: 36rpx;
}

// 推荐样式优化
.recommendations-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recommendation-card {
  padding: 20rpx;
  background: rgba(248, 250, 252, 0.7);
  backdrop-filter: blur(12rpx);
  -webkit-backdrop-filter: blur(12rpx);
  border: 1rpx solid rgba(0, 201, 167, 0.08);
  border-radius: 18rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 201, 167, 0.06);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 28rpx rgba(0, 201, 167, 0.12);
  }
}

.rec-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14rpx;
}

.rec-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);

  .icon-text {
    font-size: 18rpx;
  }
}

.rec-badge {
  padding: 5rpx 10rpx;
  border-radius: 14rpx;
  font-size: 18rpx;
  font-weight: 600;

  &.weak {
    background: rgba(239, 68, 68, 0.08);
    color: #ef4444;
  }

  &.strong {
    background: rgba(0, 201, 167, 0.08);
    color: #00c9a7;
  }

  &.general {
    background: rgba(59, 130, 246, 0.08);
    color: #3b82f6;
  }
}

.rec-content {
  margin-bottom: 14rpx;
}

.rec-title {
  font-size: 26rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 6rpx;
  display: block;
}

.rec-desc {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 10rpx;
  display: block;
}

.rec-resources {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
}

.resource-tag {
  padding: 3rpx 8rpx;
  background: rgba(0, 201, 167, 0.08);
  border-radius: 10rpx;
  font-size: 18rpx;
  color: #00c9a7;
  font-weight: 500;
}

.more-resources {
  padding: 3rpx 8rpx;
  background: rgba(0, 201, 167, 0.04);
  border: 1rpx dashed rgba(0, 201, 167, 0.25);
  border-radius: 10rpx;
  font-size: 18rpx;
  color: #00c9a7;
  font-weight: 500;
}

.rec-action {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 10rpx;
  background: rgba(0, 201, 167, 0.04);
  border-radius: 14rpx;
  border: 1rpx solid rgba(0, 201, 167, 0.15);
}

.action-text {
  font-size: 22rpx;
  color: #00c9a7;
  font-weight: 600;
}

.action-icon {
  font-size: 16rpx;
  color: #00c9a7;
}

// 快速操作样式优化
.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx 14rpx;
  border-radius: 18rpx;
  transition: all 0.25s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

  &.primary {
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    color: #ffffff;
  }

  &.secondary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #ffffff;
  }

  &.tertiary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: #ffffff;
  }

  &:active {
    transform: scale(0.96);
  }
}

.action-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5rpx);
}

.action-icon-text {
  font-size: 18rpx;
}

.action-title {
  font-size: 22rpx;
  font-weight: 600;
}

// 底部间距
.bottom-spacing {
  height: 100rpx;
}

// 优化动画
@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-8rpx) rotate(2deg);
    opacity: 0.8;
  }
}

@keyframes backgroundFloat {
  0%,
  100% {
    transform: translateX(0) translateY(0);
  }
  33% {
    transform: translateX(-1%) translateY(-0.5%);
  }
  66% {
    transform: translateX(0.5%) translateY(-1%);
  }
}

// 响应式适配优化
@media screen and (max-width: 750rpx) {
  .content-area {
    padding: 0 16rpx;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 8rpx;
  }

  .filter-row {
    gap: 10rpx;
  }

  .time-selector,
  .ability-selector {
    gap: 6rpx;
  }

  .section {
    padding: 28rpx 20rpx 20rpx 20rpx;
    margin: 12rpx 0 20rpx 0;
  }
}

// 暗黑模式适配优化
@media (prefers-color-scheme: dark) {
  .growth-detail-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }

  .section {
    background: rgba(30, 41, 59, 0.6);
    backdrop-filter: blur(20rpx) saturate(150%);
    -webkit-backdrop-filter: blur(20rpx) saturate(150%);
    border: 1rpx solid rgba(148, 163, 184, 0.1);
  }

  .section-title {
    color: #f1f5f9;
  }

  .section-subtitle {
    color: #94a3b8;
  }

  .overview-card {
    background: rgba(30, 41, 59, 0.7);
    border: 1rpx solid rgba(148, 163, 184, 0.1);
  }

  .filter-container,
  .chart-container,
  .ability-item,
  .recommendation-card {
    background: rgba(30, 41, 59, 0.4);
    border: 1rpx solid rgba(148, 163, 184, 0.08);
  }
}
</style>
