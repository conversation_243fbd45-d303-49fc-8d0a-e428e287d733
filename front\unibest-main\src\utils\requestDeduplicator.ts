/**
 * Request Deduplicator
 * Prevents duplicate API calls by caching in-flight requests
 */

import { performanceMonitor } from './performanceMonitor'

interface PendingRequest<T> {
  promise: Promise<T>
  timestamp: number
  timeout: number
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest<any>>()
  private readonly DEFAULT_TIMEOUT = 30000 // 30 seconds

  /**
   * Deduplicate requests by key
   * If a request with the same key is already in flight, return the existing promise
   */
  async dedupe<T>(key: string, requestFn: () => Promise<T>, timeout?: number): Promise<T> {
    const now = Date.now()
    const requestTimeout = timeout || this.DEFAULT_TIMEOUT

    // Check if we have a pending request
    const existing = this.pendingRequests.get(key)
    if (existing) {
      // Check if the existing request hasn't timed out
      if (now - existing.timestamp < existing.timeout) {
        performanceMonitor.incrementCounter('requestDeduplicator.dedupe-hit')
        return existing.promise
      } else {
        // Remove expired request
        this.pendingRequests.delete(key)
      }
    }

    // Create new request
    performanceMonitor.incrementCounter('requestDeduplicator.new-request')
    const promise = this.executeRequest(key, requestFn, requestTimeout)

    // Store the pending request
    this.pendingRequests.set(key, {
      promise,
      timestamp: now,
      timeout: requestTimeout,
    })

    return promise
  }

  /**
   * Clear a specific pending request
   */
  clear(key: string): void {
    this.pendingRequests.delete(key)
  }

  /**
   * Clear all pending requests
   */
  clearAll(): void {
    this.pendingRequests.clear()
  }

  /**
   * Get the number of pending requests
   */
  getPendingCount(): number {
    return this.pendingRequests.size
  }

  /**
   * Check if a request is pending
   */
  isPending(key: string): boolean {
    const existing = this.pendingRequests.get(key)
    if (!existing) return false

    const now = Date.now()
    if (now - existing.timestamp >= existing.timeout) {
      this.pendingRequests.delete(key)
      return false
    }

    return true
  }

  /**
   * Get pending request keys
   */
  getPendingKeys(): string[] {
    const now = Date.now()
    const validKeys: string[] = []

    for (const [key, request] of this.pendingRequests) {
      if (now - request.timestamp < request.timeout) {
        validKeys.push(key)
      } else {
        this.pendingRequests.delete(key)
      }
    }

    return validKeys
  }

  private async executeRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    timeout: number,
  ): Promise<T> {
    try {
      performanceMonitor.startTiming(`requestDeduplicator.${key}`)

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Request timeout: ${key}`))
        }, timeout)
      })

      // Race between the actual request and timeout
      const result = await Promise.race([requestFn(), timeoutPromise])

      performanceMonitor.endTiming(`requestDeduplicator.${key}`)
      performanceMonitor.incrementCounter('requestDeduplicator.success')

      return result
    } catch (error) {
      performanceMonitor.endTiming(`requestDeduplicator.${key}`)
      performanceMonitor.incrementCounter('requestDeduplicator.error')
      throw error
    } finally {
      // Always clean up the pending request
      this.pendingRequests.delete(key)
    }
  }
}

// Export singleton instance
export const requestDeduplicator = new RequestDeduplicator()

// Export class for testing
export { RequestDeduplicator }
