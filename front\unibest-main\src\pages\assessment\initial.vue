<script setup lang="ts">
import { ref, computed, onMounted, reactive, shallowRef } from 'vue'
import type {
  AssessmentQuestion,
  AssessmentResult,
  InitialAbilityAssessment,
  UserGrowthProfile,
  UserGrowthStage,
} from '@/types/onboarding'
import { getAssessmentQuestions, submitAssessmentResults } from '@/service/assessment'

// 响应式数据
const currentQuestion = ref(0)
const answers = shallowRef<{ [key: number]: AssessmentResult }>({})
const isProcessing = ref(false)
const isLoading = ref(true)
const loadingFailed = ref(false)

// 使用shallowRef提高性能
const questions = shallowRef<AssessmentQuestion[]>([])

// 默认的评估问题
const defaultQuestions: AssessmentQuestion[] = [
  {
    id: 'q1',
    type: 'single',
    category: 'professionalKnowledge',
    question: '当面试官问到你不熟悉的技术概念时，你通常会怎么回应？',
    options: [
      { id: 'a', text: '直接承认不了解，但表达学习意愿', score: 4 },
      { id: 'b', text: '尝试从相关知识推导解释', score: 5 },
      { id: 'c', text: '避开话题，转向熟悉的领域', score: 2 },
      { id: 'd', text: '模糊回应，希望蒙混过关', score: 1 },
    ],
  },
  {
    id: 'q2',
    type: 'scale',
    category: 'logicalThinking',
    question: '请评估你解决复杂逻辑问题的能力（如算法题、系统设计等）',
    minValue: 1,
    maxValue: 5,
  },
  {
    id: 'q3',
    type: 'single',
    category: 'languageExpression',
    question: '描述项目经验时，你认为最重要的是什么？',
    options: [
      { id: 'a', text: '详细说明技术实现细节', score: 3 },
      { id: 'b', text: '突出项目的业务价值和成果', score: 5 },
      { id: 'c', text: '强调个人在项目中的贡献', score: 4 },
      { id: 'd', text: '简要概述项目功能', score: 2 },
    ],
  },
  {
    id: 'q4',
    type: 'scale',
    category: 'stressResistance',
    question: '面对时间紧迫或高压面试环境，你的应对能力如何？',
    minValue: 1,
    maxValue: 5,
  },
  {
    id: 'q5',
    type: 'single',
    category: 'teamCollaboration',
    question: '如果你和团队成员在技术方案上有分歧，你会？',
    options: [
      { id: 'a', text: '坚持自己的观点，据理力争', score: 2 },
      { id: 'b', text: '主动寻求共识，考虑多方意见', score: 5 },
      { id: 'c', text: '服从多数决定', score: 3 },
      { id: 'd', text: '请求上级或专家裁决', score: 4 },
    ],
  },
  {
    id: 'q6',
    type: 'scale',
    category: 'innovation',
    question: '你在工作或学习中提出创新想法和解决方案的频率如何？',
    minValue: 1,
    maxValue: 5,
  },
  {
    id: 'q7',
    type: 'single',
    category: 'professionalKnowledge',
    question: '面试前你通常如何准备技术问题？',
    options: [
      { id: 'a', text: '系统复习基础知识和核心概念', score: 5 },
      { id: 'b', text: '针对岗位要求重点准备', score: 4 },
      { id: 'c', text: '简单浏览常见面试题', score: 2 },
      { id: 'd', text: '依靠平时积累，不特别准备', score: 3 },
    ],
  },
  {
    id: 'q8',
    type: 'single',
    category: 'languageExpression',
    question: '当面试官提出开放性问题时，你的回答策略是？',
    options: [
      { id: 'a', text: '用STAR法则结构化回答', score: 5 },
      { id: 'b', text: '举具体例子说明', score: 4 },
      { id: 'c', text: '简洁直接回答要点', score: 3 },
      { id: 'd', text: '随机应变，想到什么说什么', score: 2 },
    ],
  },
]

// 计算属性
const progressPercent = computed(() => {
  return Math.round((currentQuestion.value / questions.value.length) * 100)
})

const isCurrentAnswered = computed(() => {
  return answers.value[currentQuestion.value] !== undefined
})

// 初始化数据
onMounted(async () => {
  await loadQuestions()
})

/**
 * @description 加载评估问题
 */
async function loadQuestions() {
  isLoading.value = true
  loadingFailed.value = false
  
  try {
    const res = await getAssessmentQuestions()
    if (res && res.code === 200 && res.data && res.data.length > 0) {
      questions.value = res.data
      console.log('成功加载评估问题', questions.value.length)
    } else {
      // 如果没有数据或返回格式不符合预期，使用默认问题
      questions.value = defaultQuestions
      console.log('使用默认评估问题', questions.value.length)
    }
  } catch (error) {
    console.error('加载评估问题失败', error)
    loadingFailed.value = true
    // 使用默认问题
    questions.value = defaultQuestions
  } finally {
    // 添加300ms延迟，提升用户体验
    setTimeout(() => {
      isLoading.value = false
    }, 300)
  }
}

// 方法
function getCategoryIcon(category: string): string {
  const icons = {
    professionalKnowledge: 'i-mdi-book-open',
    logicalThinking: 'i-mdi-brain',
    languageExpression: 'i-mdi-message-text',
    stressResistance: 'i-mdi-shield-check',
    teamCollaboration: 'i-mdi-account-group',
    innovation: 'i-mdi-lightbulb',
  }
  return icons[category] || 'i-mdi-help-circle'
}

function getCategoryName(category: string): string {
  const names = {
    professionalKnowledge: '专业知识',
    logicalThinking: '逻辑思维',
    languageExpression: '语言表达',
    stressResistance: '抗压能力',
    teamCollaboration: '团队协作',
    innovation: '创新能力',
  }
  return names[category] || category
}

/**
 * @description 选择单选题选项
 * @param optionId 选项ID
 * @param score 分数
 */
function selectOption(optionId: string, score: number) {
  const question = questions.value[currentQuestion.value]
  answers.value = {
    ...answers.value,
    [currentQuestion.value]: {
      questionId: question.id,
      answer: optionId,
      score: score,
      category: question.category,
    }
  }
}

/**
 * @description 选择评分题分数
 * @param score 分数
 */
function selectScale(score: number) {
  const question = questions.value[currentQuestion.value]
  answers.value = {
    ...answers.value,
    [currentQuestion.value]: {
      questionId: question.id,
      answer: score,
      score: score,
      category: question.category,
    }
  }
}

function nextQuestion() {
  if (currentQuestion.value < questions.value.length - 1) {
    currentQuestion.value++
  } else {
    completeAssessment()
  }
}

function previousQuestion() {
  if (currentQuestion.value > 0) {
    currentQuestion.value--
  }
}

async function completeAssessment() {
  isProcessing.value = true
  currentQuestion.value = questions.value.length

  try {
    // 将答案转换为数组格式
    const answersArray = Object.values(answers.value)
    
    // 尝试调用API提交评估结果
    const res = await submitAssessmentResults(answersArray)
    
    if (res && res.code === 200 && res.data) {
      // 使用服务器返回的评估结果
      console.log('成功提交评估结果', res.data)
      saveAssessmentResults(res.data)
    } else {
      console.log('服务器响应无效，使用本地计算结果')
      // 如果API调用成功但无数据或格式不符合预期，使用本地计算
      const scores = calculateAbilityScores()
      const growthProfile = generateGrowthProfile(scores)
      saveAssessmentResults(scores, growthProfile)
    }
  } catch (error) {
    console.error('提交评估结果失败', error)
    // API调用失败，使用本地计算
    console.log('API调用失败，使用本地计算结果')
    const scores = calculateAbilityScores()
    const growthProfile = generateGrowthProfile(scores)
    saveAssessmentResults(scores, growthProfile)
  }

  // 延迟跳转到结果页面
  setTimeout(() => {
    uni.redirectTo({
      url: '/pages/assessment/result',
    })
  }, 2000)
}

/**
 * @description 保存评估结果到本地存储
 * @param scores 能力分数
 * @param profile 用户成长档案
 */
function saveAssessmentResults(scores: InitialAbilityAssessment, profile?: UserGrowthProfile) {
  // 保存评估结果
  uni.setStorageSync('hasCompletedInitialAssessment', true)
  uni.setStorageSync('initialAssessmentResult', JSON.stringify(scores))
  
  if (profile) {
    uni.setStorageSync('userGrowthProfile', JSON.stringify(profile))
  } else {
    // 如果没有提供profile，生成一个
    const growthProfile = generateGrowthProfile(scores)
    uni.setStorageSync('userGrowthProfile', JSON.stringify(growthProfile))
  }
}

function calculateAbilityScores(): InitialAbilityAssessment {
  const categoryScores = {
    professionalKnowledge: [],
    logicalThinking: [],
    languageExpression: [],
    stressResistance: [],
    teamCollaboration: [],
    innovation: [],
  }

  // 收集各分类的得分
  Object.values(answers.value).forEach((answer) => {
    if (categoryScores[answer.category]) {
      categoryScores[answer.category].push(answer.score)
    }
  })

  // 计算各维度平均分（转换为100分制）
  const result: InitialAbilityAssessment = {
    professionalKnowledge: Math.round(
      (categoryScores.professionalKnowledge.reduce((a, b) => a + b, 0) /
        categoryScores.professionalKnowledge.length) *
        20,
    ),
    logicalThinking: Math.round(
      (categoryScores.logicalThinking.reduce((a, b) => a + b, 0) /
        categoryScores.logicalThinking.length) *
        20,
    ),
    languageExpression: Math.round(
      (categoryScores.languageExpression.reduce((a, b) => a + b, 0) /
        categoryScores.languageExpression.length) *
        20,
    ),
    stressResistance: Math.round(
      (categoryScores.stressResistance.reduce((a, b) => a + b, 0) /
        categoryScores.stressResistance.length) *
        20,
    ),
    teamCollaboration: Math.round(
      (categoryScores.teamCollaboration.reduce((a, b) => a + b, 0) /
        categoryScores.teamCollaboration.length) *
        20,
    ),
    innovation: Math.round(
      (categoryScores.innovation.reduce((a, b) => a + b, 0) / categoryScores.innovation.length) *
        20,
    ),
    overallScore: 0,
  }

  // 计算总分
  result.overallScore = Math.round(
    (result.professionalKnowledge +
      result.logicalThinking +
      result.languageExpression +
      result.stressResistance +
      result.teamCollaboration +
      result.innovation) /
      6,
  )

  return result
}

function generateGrowthProfile(assessment: InitialAbilityAssessment): UserGrowthProfile {
  const now = new Date().toISOString()

  // 根据总分判断用户阶段
  let stage: UserGrowthStage = 'new_user'
  if (assessment.overallScore >= 85) {
    stage = 'expert'
  } else if (assessment.overallScore >= 75) {
    stage = 'advanced'
  } else if (assessment.overallScore >= 65) {
    stage = 'intermediate'
  } else if (assessment.overallScore >= 50) {
    stage = 'beginner'
  }

  return {
    userId: 'user_' + Date.now(),
    currentStage: stage,
    joinDate: now,
    lastActiveDate: now,
    totalInterviews: 0,
    initialAssessment: assessment,
    currentAssessment: assessment,
    improvementRate: 0,
    targetPosition: '前端工程师', // 默认岗位，后续可配置
    learningGoals: [],
    achievements: [],
    continuousLearningDays: 1,
    completedCourses: 0,
  }
}

/**
 * @description 获取单选题的选中状态
 * @param optionId 选项ID
 * @returns 选中状态
 */
function getOptionClass(optionId: string): string {
  const currentAnswer = answers.value[currentQuestion.value]
  return currentAnswer?.answer === optionId ? 'selected' : ''
}

/**
 * @description 获取评分题的选中状态
 * @param score 评分
 * @returns 选中状态
 */
function getScaleClass(score: number): string {
  const currentAnswer = answers.value[currentQuestion.value]
  return currentAnswer?.answer === score ? 'selected' : ''
}

/**
 * @description 重试加载问题
 */
function retryLoading() {
  loadQuestions()
}
</script>

<template>
  <view class="assessment-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">初始能力评估</text>
        <text class="header-subtitle">了解你的能力水平，定制专属学习计划</text>
      </view>
      <view class="progress-info">
        <text class="progress-text">{{ currentQuestion + 1 }} / {{ questions.length }}</text>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner">
        <view class="spinner"></view>
      </view>
      <text class="loading-text">加载评估问题中...</text>
    </view>

    <!-- 加载失败 -->
    <view v-else-if="loadingFailed" class="loading-failed">
      <text class="i-mdi-alert-circle error-icon"></text>
      <text class="error-text">加载问题失败</text>
      <text class="error-description">请检查网络连接后重试</text>
      <button class="retry-button" @click="retryLoading">重新加载</button>
    </view>

    <!-- 问题内容 -->
    <view v-else class="question-container">
      <view v-if="currentQuestion < questions.length" class="question-card">
        <view class="question-header">
          <view class="question-category">
            <text
              class="category-icon"
              :class="getCategoryIcon(questions[currentQuestion].category)"
            ></text>
            <text class="category-name">
              {{ getCategoryName(questions[currentQuestion].category) }}
            </text>
          </view>
          <view class="question-number">
            <text>第 {{ currentQuestion + 1 }} 题</text>
          </view>
        </view>

        <text class="question-text">{{ questions[currentQuestion].question }}</text>

        <!-- 单选题 -->
        <view v-if="questions[currentQuestion].type === 'single'" class="options-container">
          <view
            v-for="option in questions[currentQuestion].options"
            :key="option.id"
            class="option-item"
            :class="getOptionClass(option.id)"
            @click="selectOption(option.id, option.score)"
          >
            <view class="option-radio">
              <text
                v-if="getOptionClass(option.id) === 'selected'"
                class="i-mdi-check"
              ></text>
            </view>
            <text class="option-text">{{ option.text }}</text>
          </view>
        </view>

        <!-- 评分题 -->
        <view v-if="questions[currentQuestion].type === 'scale'" class="scale-container">
          <view class="scale-labels">
            <text class="scale-label">完全不会</text>
            <text class="scale-label">非常熟练</text>
          </view>
          <view class="scale-options">
            <view
              v-for="score in 5"
              :key="score"
              class="scale-option"
              :class="getScaleClass(score)"
              @click="selectScale(score)"
            >
              <text class="scale-number">{{ score }}</text>
            </view>
          </view>
          <text class="scale-description">请根据你的实际水平选择对应分数</text>
        </view>
      </view>

      <!-- 评估完成页面 -->
      <view v-else class="completion-container">
        <view class="completion-icon">
          <text class="i-mdi-check-circle"></text>
        </view>
        <text class="completion-title">评估完成！</text>
        <text class="completion-desc">正在为你分析评估结果，生成专属能力报告...</text>
        <view class="loading-animation">
          <view class="loading-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view v-if="!isLoading && !loadingFailed" class="footer-actions">
      <button
        v-if="currentQuestion > 0 && currentQuestion < questions.length"
        class="btn-secondary"
        @click="previousQuestion"
      >
        上一题
      </button>

      <button
        v-if="currentQuestion < questions.length && isCurrentAnswered"
        class="btn-primary"
        @click="nextQuestion"
      >
        {{ currentQuestion === questions.length - 1 ? '完成评估' : '下一题' }}
      </button>

      <button
        v-if="currentQuestion < questions.length && !isCurrentAnswered"
        class="btn-disabled"
        disabled
      >
        请先回答问题
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.assessment-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  will-change: transform;
  contain: layout style paint;
}

/* 顶部导航样式 */
.header {
  background: #fff;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.header-content {
  text-align: center;
  margin-bottom: 24rpx;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 8rpx;
}

.header-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
  min-width: 80rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00c9a7, #00b39a);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-spinner {
  margin-bottom: 20rpx;
}

.spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 201, 167, 0.2);
  border-top-color: #00c9a7;
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 加载失败 */
.loading-failed {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
  color: #ff5252;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.error-description {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-button {
  background: #00c9a7;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 问题容器 */
.question-container {
  flex: 1;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.question-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  animation: slideIn 0.4s ease-out;
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.question-category {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 201, 167, 0.1);
  border-radius: 20rpx;
}

.category-icon {
  font-size: 24rpx;
  color: #00c9a7;
}

.category-name {
  font-size: 24rpx;
  color: #00c9a7;
  font-weight: 600;
}

.question-number {
  padding: 8rpx 16rpx;
  background: #f5f5f5;
  border-radius: 20rpx;

  text {
    font-size: 22rpx;
    color: #666;
    font-weight: 600;
  }
}

.question-text {
  display: block;
  font-size: 32rpx;
  color: #222;
  line-height: 1.5;
  margin-bottom: 40rpx;
  font-weight: 500;
}

/* 选择题样式 */
.options-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8f9fc;
  border: 2rpx solid transparent;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  transform: translateZ(0);

  &.selected {
    background: rgba(0, 201, 167, 0.1);
    border-color: #00c9a7;
  }

  &:active {
    transform: scale(0.98) translateZ(0);
  }
}

.option-radio {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  transition: all 0.3s ease;

  .option-item.selected & {
    border-color: #00c9a7;
    background: #00c9a7;
    color: #fff;
  }
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

/* 评分题样式 */
.scale-container {
  text-align: center;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.scale-label {
  font-size: 24rpx;
  color: #666;
}

.scale-options {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 0 20rpx;
}

.scale-option {
  width: 80rpx;
  height: 80rpx;
  border: 3rpx solid #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  transform: translateZ(0);

  &.selected {
    border-color: #00c9a7;
    background: #00c9a7;
    transform: scale(1.1) translateZ(0);

    .scale-number {
      color: #fff;
      font-weight: bold;
    }
  }

  &:active {
    transform: scale(0.95) translateZ(0);
  }
}

.scale-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
}

.scale-description {
  font-size: 24rpx;
  color: #999;
}

/* 完成页面样式 */
.completion-container {
  text-align: center;
  padding: 80rpx 40rpx;
}

.completion-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 32rpx;
  background: linear-gradient(135deg, #00c9a7, #00b39a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  animation: checkAnimation 0.6s ease-out;
  transform: translateZ(0);
}

.completion-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 16rpx;
}

.completion-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.loading-animation {
  display: flex;
  justify-content: center;
}

.loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: #00c9a7;
  border-radius: 50%;
  animation: dotBounce 1.4s infinite ease-in-out both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
  &:nth-child(3) {
    animation-delay: 0s;
  }
}

/* 底部操作按钮 */
.footer-actions {
  padding: 24rpx 32rpx 40rpx;
  background: #fff;
  display: flex;
  gap: 16rpx;
  position: relative;
  z-index: 2;
}

.btn-primary {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #00c9a7, #00b39a);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  transform: translateZ(0);

  &:active {
    transform: scale(0.98) translateZ(0);
    box-shadow: 0 4rpx 16rpx rgba(0, 201, 167, 0.3);
  }
}

.btn-secondary {
  width: 160rpx;
  height: 88rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transform: translateZ(0);

  &:active {
    background: #e0e0e0;
  }
}

.btn-disabled {
  flex: 1;
  height: 88rpx;
  background: #f0f0f0;
  color: #999;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
}

/* 动画 */
@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(40rpx) translateZ(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes checkAnimation {
  0% {
    transform: scale(0) translateZ(0);
  }
  50% {
    transform: scale(1.1) translateZ(0);
  }
  100% {
    transform: scale(1) translateZ(0);
  }
}

@keyframes dotBounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
