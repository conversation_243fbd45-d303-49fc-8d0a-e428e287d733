// ==================== 智能面试系统 - UI组件库导出 ====================

// 基础组件
export { default as Button } from './Button.vue'
export { default as Card } from './Card.vue'
export { default as Input } from './Input.vue'
export { default as Loading } from './Loading.vue'
export { default as Toast } from './Toast.vue'

// 组件类型定义
export interface ButtonProps {
  type?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'text'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  round?: boolean
  leftIcon?: string
  rightIcon?: string
  customClass?: string
}

export interface CardProps {
  title?: string
  subtitle?: string
  shadow?: boolean
  clickable?: boolean
  showTopBar?: boolean
  type?: 'default' | 'glass' | 'data' | 'stats'
  customClass?: string
  padding?: 'none' | 'small' | 'medium' | 'large'
}

export interface InputProps {
  type?: 'text' | 'number' | 'password' | 'textarea' | 'search' | 'tel' | 'email'
  modelValue?: string | number
  placeholder?: string
  label?: string
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  maxlength?: number
  showCount?: boolean
  leftIcon?: string
  rightIcon?: string
  error?: string
  help?: string
  size?: 'small' | 'medium' | 'large'
  autoHeight?: boolean
  customClass?: string
}

export interface LoadingProps {
  visible?: boolean
  type?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'custom'
  text?: string
  overlay?: boolean
  overlayClosable?: boolean
  size?: 'small' | 'medium' | 'large'
  color?: string
  customIcon?: string
  fullscreen?: boolean
  customClass?: string
}

export interface ToastProps {
  visible?: boolean
  message?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  position?: 'top' | 'center' | 'bottom'
  duration?: number
  showIcon?: boolean
  closable?: boolean
  customClass?: string
}

// 工具函数
export const createToast = (options: Partial<ToastProps>) => {
  // 这里可以实现全局Toast的创建逻辑
  // 由于uni-app的限制，这里提供一个简化版本
  return {
    show: () => {
      uni.showToast({
        title: options.message || '',
        icon: options.type === 'success' ? 'success' : 'none',
        duration: options.duration || 3000,
      })
    },
  }
}

// 主题配置
export const themeConfig = {
  colors: {
    primary: '#00c9a7',
    primaryLight: '#00b294',
    primaryDark: '#00b39a',
    secondary: '#3b82f6',
    success: '#4CAF50',
    warning: '#FFC107',
    error: '#ff4d4f',
    info: '#2196F3',
  },
  spacing: {
    xs: '8rpx',
    sm: '12rpx',
    base: '16rpx',
    md: '20rpx',
    lg: '24rpx',
    xl: '32rpx',
    '2xl': '40rpx',
    '3xl': '48rpx',
    '4xl': '64rpx',
  },
  radius: {
    xs: '4rpx',
    sm: '8rpx',
    base: '12rpx',
    md: '16rpx',
    lg: '20rpx',
    xl: '24rpx',
    '2xl': '32rpx',
    full: '50%',
  },
  fontSize: {
    xs: '20rpx',
    sm: '24rpx',
    base: '28rpx',
    lg: '32rpx',
    xl: '36rpx',
    '2xl': '40rpx',
    '3xl': '48rpx',
  },
}

// 样式工具类生成器
export const generateUtilityClasses = () => {
  const utilities: Record<string, string> = {}

  // 间距工具类
  Object.entries(themeConfig.spacing).forEach(([key, value]) => {
    utilities[`m-${key}`] = `margin: ${value};`
    utilities[`mt-${key}`] = `margin-top: ${value};`
    utilities[`mb-${key}`] = `margin-bottom: ${value};`
    utilities[`ml-${key}`] = `margin-left: ${value};`
    utilities[`mr-${key}`] = `margin-right: ${value};`
    utilities[`p-${key}`] = `padding: ${value};`
    utilities[`pt-${key}`] = `padding-top: ${value};`
    utilities[`pb-${key}`] = `padding-bottom: ${value};`
    utilities[`pl-${key}`] = `padding-left: ${value};`
    utilities[`pr-${key}`] = `padding-right: ${value};`
  })

  // 颜色工具类
  Object.entries(themeConfig.colors).forEach(([key, value]) => {
    utilities[`text-${key}`] = `color: ${value};`
    utilities[`bg-${key}`] = `background-color: ${value};`
    utilities[`border-${key}`] = `border-color: ${value};`
  })

  // 圆角工具类
  Object.entries(themeConfig.radius).forEach(([key, value]) => {
    utilities[`rounded-${key}`] = `border-radius: ${value};`
  })

  // 字体大小工具类
  Object.entries(themeConfig.fontSize).forEach(([key, value]) => {
    utilities[`text-${key}`] = `font-size: ${value};`
  })

  return utilities
}
