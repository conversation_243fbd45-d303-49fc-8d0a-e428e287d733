<script setup lang="ts">
/**
 * 通用Modal弹框组件
 * 支持多种类型的弹框展示，可自定义内容和操作按钮
 * 兼容H5端和微信小程序端
 * <AUTHOR>
 */

import { ref, defineProps, defineEmits, watch, nextTick } from 'vue'

// 定义模态框类型
type ModalType = 'info' | 'success' | 'warning' | 'error' | 'confirm' | 'custom'

// 定义按钮配置
interface ModalButton {
  /** 按钮文本 */
  text: string
  /** 按钮类型 */
  type?: 'primary' | 'secondary' | 'danger'
  /** 点击回调 */
  onClick?: () => void | Promise<void>
  /** 是否加载中 */
  loading?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

// 使用defineProps的另一种方式来避免类型问题
const props = defineProps({
  /** 是否显示模态框 */
  visible: {
    type: Boolean,
    required: true,
  },
  /** 模态框标题 */
  title: {
    type: String,
    default: '',
  },
  /** 模态框内容 */
  content: {
    type: String,
    default: '',
  },
  /** 模态框类型 */
  type: {
    type: String as () => ModalType,
    default: 'info' as ModalType,
  },
  /** 是否显示关闭按钮 */
  showClose: {
    type: Boolean,
    default: true,
  },
  /** 是否点击遮罩层关闭 */
  maskClosable: {
    type: Boolean,
    default: true,
  },
  /** 自定义按钮配置 */
  buttons: {
    type: Array as () => ModalButton[],
    default: () => [],
  },
  /** 是否显示取消按钮 */
  showCancel: {
    type: Boolean,
    default: true,
  },
  /** 取消按钮文本 */
  cancelText: {
    type: String,
    default: '取消',
  },
  /** 是否显示确认按钮 */
  showConfirm: {
    type: Boolean,
    default: true,
  },
  /** 确认按钮文本 */
  confirmText: {
    type: String,
    default: '确定',
  },
  /** 确认按钮类型 */
  confirmType: {
    type: String as () => 'primary' | 'danger',
    default: 'primary' as 'primary' | 'danger',
  },
  /** 模态框宽度 */
  width: {
    type: String,
    default: '640rpx',
  },
  /** 自定义样式类名 */
  customClass: {
    type: String,
    default: '',
  },
  /** z-index */
  zIndex: {
    type: Number,
    default: 1000,
  },
})

// 定义事件
interface Emits {
  /** 更新显示状态 */
  (e: 'update:visible', value: boolean): void
  /** 确认事件 */
  (e: 'confirm'): void
  /** 取消事件 */
  (e: 'cancel'): void
  /** 关闭事件 */
  (e: 'close'): void
  /** 自定义按钮点击事件 */
  (e: 'button-click', button: ModalButton, index: number): void
}

const emit = defineEmits<Emits>()

// 内部状态
const isVisible = ref<boolean>(false)
const isAnimating = ref<boolean>(false)

/**
 * @description 获取模态框图标
 */
const getModalIcon = (): string => {
  const iconMap: Record<ModalType, string> = {
    info: 'i-fa-solid-info-circle',
    success: 'i-fa-solid-check-circle',
    warning: 'i-fa-solid-exclamation-triangle',
    error: 'i-fa-solid-times-circle',
    confirm: 'i-fa-solid-question-circle',
    custom: '',
  }
  return iconMap[props.type as ModalType] || ''
}

/**
 * @description 获取模态框主题色
 */
const getModalTheme = (): string => {
  const themeMap: Record<ModalType, string> = {
    info: '#3b82f6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    confirm: '#00c9a7',
    custom: '#00c9a7',
  }
  return themeMap[props.type as ModalType] || '#00c9a7'
}

/**
 * @description 关闭模态框
 */
const closeModal = async (): Promise<void> => {
  if (isAnimating.value) return

  isAnimating.value = true
  emit('update:visible', false)
  emit('close')

  // 等待动画完成
  await nextTick()
  setTimeout(() => {
    isVisible.value = false
    isAnimating.value = false
  }, 300)
}

/**
 * @description 处理遮罩层点击
 */
const handleMaskClick = (): void => {
  if (props.maskClosable && !isAnimating.value) {
    closeModal()
  }
}

/**
 * @description 处理确认
 */
const handleConfirm = (): void => {
  emit('confirm')
  if ((props.type as ModalType) !== 'custom') {
    closeModal()
  }
}

/**
 * @description 处理取消
 */
const handleCancel = (): void => {
  emit('cancel')
  closeModal()
}

/**
 * @description 处理自定义按钮点击
 * @param button 按钮配置
 * @param index 按钮索引
 */
const handleButtonClick = async (button: ModalButton, index: number): Promise<void> => {
  if (button.disabled || button.loading) return

  try {
    // 设置按钮加载状态
    if (button.onClick) {
      const result = button.onClick()
      if (result instanceof Promise) {
        button.loading = true
        await result
        button.loading = false
      }
    }

    emit('button-click', button, index)
  } catch (error) {
    button.loading = false
    console.error('按钮点击处理失败:', error)
  }
}

/**
 * @description 阻止事件冒泡
 */
const stopPropagation = (e: Event): void => {
  e.stopPropagation()
}

// 监听显示状态变化
watch(
  () => props.visible,
  (newVisible: boolean) => {
    if (newVisible && !isVisible.value) {
      isVisible.value = true
      isAnimating.value = true
      nextTick(() => {
        setTimeout(() => {
          isAnimating.value = false
        }, 300)
      })
    } else if (!newVisible && isVisible.value) {
      closeModal()
    }
  },
  { immediate: true },
)

/**
 * @description 获取模态框样式类名
 */
const getModalClass = (): string => {
  return [props.customClass, { 'toast-modal--animating': isAnimating.value }]
    .filter(Boolean)
    .join(' ')
}

/**
 * @description 获取模态框样式
 */
const getModalStyle = (): Record<string, string> => {
  return { width: props.width, '--theme-color': getModalTheme() } as Record<string, string>
}
</script>

<template>
  <view v-if="isVisible" class="toast-modal-overlay" :style="{ zIndex }" @click="handleMaskClick">
    <view
      class="toast-modal"
      :class="getModalClass()"
      :style="getModalStyle()"
      @click="stopPropagation"
    >
      <!-- 头部 -->
      <view v-if="title || showClose" class="modal-header">
        <view class="modal-header__content">
          <view v-if="getModalIcon()" class="modal-icon" :class="getModalIcon()"></view>
          <text v-if="title" class="modal-title">{{ title }}</text>
        </view>
        <button v-if="showClose" class="modal-close" @click="closeModal">
          <view class="i-fa-solid-times close-icon">X</view>
        </button>
      </view>

      <!-- 内容区域 -->
      <view class="modal-body">
        <!-- 默认内容 -->
        <view v-if="content" class="modal-content">
          <view
            v-if="!title && getModalIcon()"
            class="modal-content__icon"
            :class="getModalIcon()"
          ></view>
          <text class="modal-content__text">{{ content }}</text>
        </view>

        <!-- 自定义内容插槽 -->
        <slot v-else></slot>
      </view>

      <!-- 底部按钮 -->
      <view v-if="buttons?.length || showCancel || showConfirm" class="modal-footer">
        <!-- 自定义按钮组 -->
        <template v-if="buttons?.length">
          <button
            v-for="(button, index) in buttons"
            :key="index"
            class="modal-btn"
            :class="[
              `modal-btn--${button.type || 'secondary'}`,
              { 'modal-btn--loading': button.loading, 'modal-btn--disabled': button.disabled },
            ]"
            :disabled="button.disabled || button.loading"
            @click="handleButtonClick(button, index)"
          >
            <view v-if="button.loading" class="i-fa-solid-spinner loading-icon"></view>
            <text class="btn-text">{{ button.text }}</text>
          </button>
        </template>

        <!-- 默认按钮组 -->
        <template v-else>
          <button v-if="showCancel" class="modal-btn modal-btn--secondary" @click="handleCancel">
            <text class="btn-text">{{ cancelText || '取消' }}</text>
          </button>
          <button
            v-if="showConfirm"
            class="modal-btn"
            :class="`modal-btn--${confirmType}`"
            @click="handleConfirm"
          >
            <text class="btn-text">{{ confirmText || '确定' }}</text>
          </button>
        </template>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
/**
 * 通用Modal组件样式
 * 兼容H5端和微信小程序端
 */
.toast-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  animation: fadeIn 0.3s ease;
  backdrop-filter: blur(8rpx);

  // 添加渐变背景层
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: '';
    background: radial-gradient(circle at 50% 50%, rgba(0, 201, 167, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }

  .toast-modal {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow:
      0 20rpx 60rpx rgba(0, 201, 167, 0.15),
      0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    transform: scale(1);
    animation: slideUpScale 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    max-height: 80vh;
    max-width: 90vw;
    border: 1rpx solid rgba(0, 201, 167, 0.1);

    // 添加微妙的内发光效果
    &::after {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      content: '';
      background: linear-gradient(
        135deg,
        rgba(0, 201, 167, 0.03) 0%,
        rgba(79, 209, 199, 0.03) 50%,
        transparent 100%
      );
      pointer-events: none;
      border-radius: 24rpx;
    }

    &--animating {
      pointer-events: none;
    }

    .modal-header {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10rpx);

      &__content {
        display: flex;
        align-items: center;
        gap: 16rpx;
        flex: 1;

        .modal-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48rpx;
          height: 48rpx;
          font-size: 28rpx;
          color: #ffffff;
          background: linear-gradient(135deg, var(--theme-color, #00c9a7) 0%, #4fd1c7 100%);
          border-radius: 50%;
          box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);
        }

        .modal-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          line-height: 1.4;
        }
      }

      .modal-close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48rpx;
        height: 48rpx;
        background: #f8f9fa;
        border-radius: 50%;
        border: none;
        transition: all 0.3s ease;
        flex-shrink: 0;

        // #ifdef H5
        &:hover {
          background: #f1f5f9;
          transform: scale(1.1);
        }
        // #endif

        &:active {
          transform: scale(0.9);
        }

        .close-icon {
          font-size: 20rpx;
          color: #999999;
        }
      }
    }

    .modal-body {
      position: relative;
      z-index: 2;
      padding: 32rpx;
      max-height: 50vh;
      overflow-y: auto;

      .modal-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        &__icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 88rpx;
          height: 88rpx;
          font-size: 48rpx;
          color: #ffffff;
          background: linear-gradient(135deg, var(--theme-color, #00c9a7) 0%, #4fd1c7 100%);
          border-radius: 50%;
          margin-bottom: 24rpx;
          box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);
          animation: pulseGlow 2s ease-in-out infinite;
        }

        &__text {
          font-size: 28rpx;
          color: #666666;
          line-height: 1.6;
          margin-bottom: 8rpx;
        }
      }
    }

    .modal-footer {
      position: relative;
      z-index: 2;
      display: flex;
      gap: 16rpx;
      padding: 24rpx 32rpx 32rpx;
      border-top: 1rpx solid #f0f0f0;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10rpx);

      .modal-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        padding: 20rpx 24rpx;
        border-radius: 24rpx;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
        min-height: 76rpx;
        position: relative;
        overflow: hidden;

        // 按钮内发光效果
        &::before {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          content: '';
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          transition: all 0.3s ease;
        }

        &:active::before {
          width: 100%;
          height: 100%;
        }

        &--primary {
          background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
          color: #ffffff;
          box-shadow: 0 4rpx 12rpx rgba(0, 201, 167, 0.3);

          // #ifdef H5
          &:hover:not(.modal-btn--disabled) {
            transform: translateY(-2rpx);
            box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.4);
          }
          // #endif

          &:active:not(.modal-btn--disabled) {
            transform: scale(0.95);
          }
        }

        &--secondary {
          background: #f8f9fa;
          color: #666666;
          border: 1rpx solid #f0f0f0;

          // #ifdef H5
          &:hover:not(.modal-btn--disabled) {
            background: #f1f5f9;
            color: #333333;
            transform: translateY(-1rpx);
          }
          // #endif

          &:active:not(.modal-btn--disabled) {
            transform: scale(0.95);
          }
        }

        &--danger {
          background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
          color: #ffffff;
          box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);

          // #ifdef H5
          &:hover:not(.modal-btn--disabled) {
            transform: translateY(-2rpx);
            box-shadow: 0 6rpx 20rpx rgba(255, 71, 87, 0.4);
          }
          // #endif

          &:active:not(.modal-btn--disabled) {
            transform: scale(0.95);
          }
        }

        &--loading {
          pointer-events: none;
          opacity: 0.7;

          .loading-icon {
            animation: spin 1s linear infinite;
          }
        }

        &--disabled {
          opacity: 0.5;
          pointer-events: none;
        }

        .loading-icon {
          font-size: 24rpx;
        }

        .btn-text {
          position: relative;
          z-index: 1;
          font-size: 26rpx;
          font-weight: 500;
        }
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUpScale {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.25);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.4);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
// #ifdef H5
@media (max-width: 750px) {
  .toast-modal-overlay {
    padding: 20rpx;

    .toast-modal {
      max-width: 95vw;
      border-radius: 20rpx;

      .modal-header {
        padding: 24rpx 24rpx 20rpx;

        &__content .modal-title {
          font-size: 28rpx;
        }
      }

      .modal-body {
        padding: 24rpx;

        .modal-content__text {
          font-size: 26rpx;
        }
      }

      .modal-footer {
        padding: 20rpx 24rpx 24rpx;

        .modal-btn {
          padding: 18rpx 20rpx;
          font-size: 24rpx;
          min-height: 68rpx;
        }
      }
    }
  }
}

// #endif
</style>
