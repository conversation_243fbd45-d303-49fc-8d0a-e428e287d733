/**
 * 用户成长追踪服务
 * 提供学习行为记录、成长分析、成就系统、智能推荐等功能
 */

import type {
  UserGrowthProfile,
  UserGrowthStage,
  InitialAbilityAssessment,
  Achievement,
} from '@/types/onboarding'
import { UserGrowthStage as StageEnum } from '@/types/onboarding'

// 学习行为类型
export interface LearningBehavior {
  id: string
  userId: string
  type: 'interview' | 'study' | 'assessment' | 'practice' | 'achievement'
  category: string
  description: string
  timestamp: string
  data?: any
}

// 成长分析数据
export interface GrowthAnalysis {
  userId: string
  weeklyProgress: {
    week: string
    interviewCount: number
    studyHours: number
    improvementRate: number
    achievements: number
  }[]
  abilityTrends: {
    ability: string
    history: { date: string; score: number }[]
  }[]
  predictions: {
    nextLevel: UserGrowthStage
    estimatedDays: number
    keyMilestones: string[]
  }
}

// 成就配置
export interface AchievementConfig {
  id: string
  name: string
  description: string
  icon: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  condition: {
    type: string
    target: number
    period?: string
  }
  reward?: {
    points: number
    badges: string[]
  }
}

// 智能推荐
export interface SmartRecommendation {
  id: string
  type: 'learning' | 'practice' | 'interview' | 'course'
  title: string
  description: string
  reason: string
  priority: 'high' | 'medium' | 'low'
  estimatedTime: number // 分钟
  url?: string
  tags: string[]
}

class GrowthTrackingService {
  private readonly STORAGE_KEYS = {
    GROWTH_PROFILE: 'userGrowthProfile',
    LEARNING_BEHAVIORS: 'learningBehaviors',
    ACHIEVEMENTS: 'userAchievements',
    GROWTH_ANALYSIS: 'growthAnalysis',
  }

  private readonly ACHIEVEMENT_CONFIGS: AchievementConfig[] = [
    {
      id: 'first_interview',
      name: '初出茅庐',
      description: '完成你的第一次模拟面试',
      icon: 'i-mdi-school',
      rarity: 'common',
      condition: { type: 'interview_count', target: 1 },
    },
    {
      id: 'interview_master',
      name: '面试达人',
      description: '累计完成10次模拟面试',
      icon: 'i-mdi-star',
      rarity: 'rare',
      condition: { type: 'interview_count', target: 10 },
    },
    {
      id: 'continuous_learner',
      name: '坚持不懈',
      description: '连续学习7天',
      icon: 'i-mdi-calendar-check',
      rarity: 'rare',
      condition: { type: 'continuous_learning', target: 7 },
    },
    {
      id: 'rapid_improvement',
      name: '快速成长',
      description: '一周内能力提升超过20分',
      icon: 'i-mdi-trending-up',
      rarity: 'epic',
      condition: { type: 'ability_improvement', target: 20, period: 'week' },
    },
    {
      id: 'perfectionist',
      name: '完美主义者',
      description: '单次面试得分达到95分以上',
      icon: 'i-mdi-trophy',
      rarity: 'legendary',
      condition: { type: 'perfect_score', target: 95 },
    },
  ]

  /**
   * 记录学习行为
   */
  async recordLearningBehavior(
    behavior: Omit<LearningBehavior, 'id' | 'timestamp'>,
  ): Promise<void> {
    const behaviorRecord: LearningBehavior = {
      ...behavior,
      id: `behavior_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
    }

    // 获取现有行为记录
    const existingBehaviors = this.getLearningBehaviors()
    existingBehaviors.push(behaviorRecord)

    // 保存行为记录
    uni.setStorageSync(this.STORAGE_KEYS.LEARNING_BEHAVIORS, JSON.stringify(existingBehaviors))

    // 更新成长档案
    await this.updateGrowthProfile(behaviorRecord)

    // 检查成就解锁
    await this.checkAchievements(behaviorRecord)
  }

  /**
   * 获取学习行为记录
   */
  getLearningBehaviors(userId?: string): LearningBehavior[] {
    const stored = uni.getStorageSync(this.STORAGE_KEYS.LEARNING_BEHAVIORS)
    if (!stored) return []

    try {
      const behaviors: LearningBehavior[] = JSON.parse(stored)
      return userId ? behaviors.filter((b) => b.userId === userId) : behaviors
    } catch (error) {
      console.error('解析学习行为记录失败:', error)
      return []
    }
  }

  /**
   * 获取用户成长档案
   */
  getUserGrowthProfile(): UserGrowthProfile | null {
    const stored = uni.getStorageSync(this.STORAGE_KEYS.GROWTH_PROFILE)
    if (!stored) return null

    try {
      return JSON.parse(stored)
    } catch (error) {
      console.error('解析成长档案失败:', error)
      return null
    }
  }

  /**
   * 更新成长档案
   */
  private async updateGrowthProfile(behavior: LearningBehavior): Promise<void> {
    const profile = this.getUserGrowthProfile()
    if (!profile) return

    const now = new Date()
    profile.lastActiveDate = now.toISOString()

    // 更新统计数据
    if (behavior.type === 'interview') {
      profile.totalInterviews += 1
    }

    // 更新连续学习天数
    const lastActiveDate = new Date(profile.lastActiveDate)
    const daysDiff = Math.floor((now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysDiff === 0) {
      // 同一天，不变
    } else if (daysDiff === 1) {
      // 连续学习
      profile.continuousLearningDays += 1
    } else {
      // 中断了，重新开始
      profile.continuousLearningDays = 1
    }

    // 判断成长阶段是否需要升级
    const newStage = this.calculateUserStage(profile)
    if (newStage !== profile.currentStage) {
      profile.currentStage = newStage

      // 记录阶段升级行为
      await this.recordLearningBehavior({
        userId: profile.userId,
        type: 'achievement',
        category: 'stage_upgrade',
        description: `用户成长阶段升级到${newStage}`,
      })
    }

    // 保存更新后的档案
    uni.setStorageSync(this.STORAGE_KEYS.GROWTH_PROFILE, JSON.stringify(profile))
  }

  /**
   * 计算用户成长阶段
   */
  private calculateUserStage(profile: UserGrowthProfile): UserGrowthStage {
    const { totalInterviews, continuousLearningDays, currentAssessment } = profile
    const avgScore = currentAssessment.overallScore

    // 基于多个维度判断用户阶段
    if (avgScore >= 85 && totalInterviews >= 20 && continuousLearningDays >= 30) {
      return StageEnum.EXPERT
    } else if (avgScore >= 75 && totalInterviews >= 10 && continuousLearningDays >= 14) {
      return StageEnum.ADVANCED
    } else if (avgScore >= 65 && totalInterviews >= 5 && continuousLearningDays >= 7) {
      return StageEnum.INTERMEDIATE
    } else if (avgScore >= 50 && totalInterviews >= 1) {
      return StageEnum.BEGINNER
    } else {
      return StageEnum.NEW_USER
    }
  }

  /**
   * 检查成就解锁
   */
  private async checkAchievements(behavior: LearningBehavior): Promise<Achievement[]> {
    const profile = this.getUserGrowthProfile()
    if (!profile) return []

    const unlockedAchievements: Achievement[] = []
    const existingAchievements = profile.achievements.map((a) => a.id)

    for (const config of this.ACHIEVEMENT_CONFIGS) {
      // 跳过已解锁的成就
      if (existingAchievements.includes(config.id)) continue

      const isUnlocked = await this.checkAchievementCondition(config, profile, behavior)
      if (isUnlocked) {
        const achievement: Achievement = {
          id: config.id,
          name: config.name,
          description: config.description,
          icon: config.icon,
          rarity: config.rarity,
          unlockedAt: new Date().toISOString(),
        }

        unlockedAchievements.push(achievement)
        profile.achievements.push(achievement)

        // 记录成就解锁行为
        await this.recordLearningBehavior({
          userId: profile.userId,
          type: 'achievement',
          category: 'achievement_unlock',
          description: `解锁成就：${config.name}`,
          data: achievement,
        })
      }
    }

    if (unlockedAchievements.length > 0) {
      // 更新档案
      uni.setStorageSync(this.STORAGE_KEYS.GROWTH_PROFILE, JSON.stringify(profile))

      // 显示成就通知
      this.showAchievementNotification(unlockedAchievements)
    }

    return unlockedAchievements
  }

  /**
   * 检查成就条件
   */
  private async checkAchievementCondition(
    config: AchievementConfig,
    profile: UserGrowthProfile,
    currentBehavior: LearningBehavior,
  ): Promise<boolean> {
    const { condition } = config
    const behaviors = this.getLearningBehaviors(profile.userId)

    switch (condition.type) {
      case 'interview_count':
        return profile.totalInterviews >= condition.target

      case 'continuous_learning':
        return profile.continuousLearningDays >= condition.target

      case 'ability_improvement':
        if (condition.period === 'week') {
          const weekAgo = new Date()
          weekAgo.setDate(weekAgo.getDate() - 7)

          const recentBehaviors = behaviors.filter(
            (b) => new Date(b.timestamp) >= weekAgo && b.type === 'assessment',
          )

          if (recentBehaviors.length >= 2) {
            const scores = recentBehaviors.map((b) => b.data?.overallScore || 0)
            const improvement = Math.max(...scores) - Math.min(...scores)
            return improvement >= condition.target
          }
        }
        return false

      case 'perfect_score':
        return (
          currentBehavior.type === 'interview' && currentBehavior.data?.score >= condition.target
        )

      default:
        return false
    }
  }

  /**
   * 显示成就通知
   */
  private showAchievementNotification(achievements: Achievement[]): void {
    // 这里可以触发成就通知组件
    // 在实际应用中，可以通过事件总线或状态管理来通知UI显示成就
    achievements.forEach((achievement) => {
      console.log(`🎉 成就解锁: ${achievement.name} - ${achievement.description}`)
    })
  }

  /**
   * 生成成长分析
   */
  async generateGrowthAnalysis(userId: string): Promise<GrowthAnalysis> {
    const profile = this.getUserGrowthProfile()
    const behaviors = this.getLearningBehaviors(userId)

    if (!profile) {
      throw new Error('用户成长档案不存在')
    }

    // 生成周进度数据
    const weeklyProgress = this.generateWeeklyProgress(behaviors)

    // 生成能力趋势数据
    const abilityTrends = this.generateAbilityTrends(behaviors)

    // 生成预测数据
    const predictions = this.generatePredictions(profile, behaviors)

    const analysis: GrowthAnalysis = {
      userId,
      weeklyProgress,
      abilityTrends,
      predictions,
    }

    // 缓存分析结果
    uni.setStorageSync(this.STORAGE_KEYS.GROWTH_ANALYSIS, JSON.stringify(analysis))

    return analysis
  }

  /**
   * 生成周进度数据
   */
  private generateWeeklyProgress(behaviors: LearningBehavior[]): GrowthAnalysis['weeklyProgress'] {
    const weeks = []
    const now = new Date()

    for (let i = 3; i >= 0; i--) {
      const weekStart = new Date(now)
      weekStart.setDate(now.getDate() - (i * 7 + now.getDay()))
      weekStart.setHours(0, 0, 0, 0)

      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 6)
      weekEnd.setHours(23, 59, 59, 999)

      const weekBehaviors = behaviors.filter((b) => {
        const behaviorDate = new Date(b.timestamp)
        return behaviorDate >= weekStart && behaviorDate <= weekEnd
      })

      const interviewCount = weekBehaviors.filter((b) => b.type === 'interview').length
      const studyHours = weekBehaviors.filter((b) => b.type === 'study').length * 0.5 // 假设每次学习0.5小时
      const achievements = weekBehaviors.filter((b) => b.type === 'achievement').length

      weeks.push({
        week: `${weekStart.getMonth() + 1}/${weekStart.getDate()}`,
        interviewCount,
        studyHours,
        improvementRate: Math.random() * 10 + 5, // 模拟数据，实际应根据能力评估计算
        achievements,
      })
    }

    return weeks
  }

  /**
   * 生成能力趋势数据
   */
  private generateAbilityTrends(behaviors: LearningBehavior[]): GrowthAnalysis['abilityTrends'] {
    const abilities = [
      'professionalKnowledge',
      'logicalThinking',
      'languageExpression',
      'stressResistance',
      'teamCollaboration',
      'innovation',
    ]

    return abilities.map((ability) => ({
      ability,
      history: this.generateAbilityHistory(behaviors, ability),
    }))
  }

  /**
   * 生成单个能力的历史数据
   */
  private generateAbilityHistory(
    behaviors: LearningBehavior[],
    ability: string,
  ): { date: string; score: number }[] {
    const assessmentBehaviors = behaviors.filter(
      (b) => b.type === 'assessment' && b.data?.[ability] !== undefined,
    )

    return assessmentBehaviors
      .map((b) => ({
        date: b.timestamp,
        score: b.data[ability],
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }

  /**
   * 生成预测数据
   */
  private generatePredictions(
    profile: UserGrowthProfile,
    behaviors: LearningBehavior[],
  ): GrowthAnalysis['predictions'] {
    const currentStage = profile.currentStage
    const stageOrder: UserGrowthStage[] = [
      StageEnum.NEW_USER,
      StageEnum.BEGINNER,
      StageEnum.INTERMEDIATE,
      StageEnum.ADVANCED,
      StageEnum.EXPERT,
    ]
    const currentIndex = stageOrder.indexOf(currentStage)

    let nextLevel: UserGrowthStage = currentStage
    let estimatedDays = 0
    const keyMilestones: string[] = []

    if (currentIndex < stageOrder.length - 1) {
      nextLevel = stageOrder[currentIndex + 1]

      // 根据当前进度估算升级所需天数
      const recentProgress = behaviors.filter((b) => {
        const behaviorDate = new Date(b.timestamp)
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        return behaviorDate >= weekAgo
      }).length

      estimatedDays = Math.max(7, 30 - recentProgress * 2)

      // 生成关键里程碑
      switch (nextLevel) {
        case 'beginner':
          keyMilestones.push('完成5次模拟面试', '连续学习7天', '能力评估达到60分')
          break
        case 'intermediate':
          keyMilestones.push('完成10次模拟面试', '连续学习14天', '能力评估达到70分')
          break
        case 'advanced':
          keyMilestones.push('完成20次模拟面试', '连续学习21天', '能力评估达到80分')
          break
        case 'expert':
          keyMilestones.push('完成50次模拟面试', '连续学习30天', '能力评估达到90分')
          break
      }
    }

    return {
      nextLevel,
      estimatedDays,
      keyMilestones,
    }
  }

  /**
   * 生成智能推荐
   */
  async generateSmartRecommendations(userId: string): Promise<SmartRecommendation[]> {
    const profile = this.getUserGrowthProfile()
    const behaviors = this.getLearningBehaviors(userId)
    const recommendations: SmartRecommendation[] = []

    if (!profile) return recommendations

    // 基于能力短板的推荐
    const weakestAbility = this.findWeakestAbility(profile.currentAssessment)
    if (weakestAbility) {
      recommendations.push(this.generateAbilityRecommendation(weakestAbility))
    }

    // 基于学习习惯的推荐
    const habitRecommendation = this.generateHabitRecommendation(behaviors)
    if (habitRecommendation) {
      recommendations.push(habitRecommendation)
    }

    // 基于成长阶段的推荐
    const stageRecommendation = this.generateStageRecommendation(profile.currentStage)
    if (stageRecommendation) {
      recommendations.push(stageRecommendation)
    }

    // 新增：基于时间模式的推荐
    const timeBasedRecommendation = this.generateTimeBasedRecommendation(behaviors)
    if (timeBasedRecommendation) {
      recommendations.push(timeBasedRecommendation)
    }

    // 新增：基于历史表现的推荐
    const performanceRecommendation = this.generatePerformanceBasedRecommendation(
      profile,
      behaviors,
    )
    if (performanceRecommendation) {
      recommendations.push(performanceRecommendation)
    }

    // 新增：基于目标岗位的推荐
    const careerRecommendation = this.generateCareerBasedRecommendation(profile)
    if (careerRecommendation) {
      recommendations.push(careerRecommendation)
    }

    // 新增：基于学习偏好的推荐
    const preferenceRecommendation = this.generatePreferenceBasedRecommendation(behaviors)
    if (preferenceRecommendation) {
      recommendations.push(preferenceRecommendation)
    }

    // 按优先级排序并限制数量
    return recommendations
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })
      .slice(0, 5)
  }

  /**
   * 找出最薄弱的能力
   */
  private findWeakestAbility(assessment: InitialAbilityAssessment): string | null {
    const abilities = {
      professionalKnowledge: assessment.professionalKnowledge,
      logicalThinking: assessment.logicalThinking,
      languageExpression: assessment.languageExpression,
      stressResistance: assessment.stressResistance,
      teamCollaboration: assessment.teamCollaboration,
      innovation: assessment.innovation,
    }

    let weakestKey: string | null = null
    let lowestScore = 100

    Object.entries(abilities).forEach(([key, score]) => {
      if (score < lowestScore) {
        lowestScore = score
        weakestKey = key
      }
    })

    return weakestKey
  }

  /**
   * 生成能力改进推荐
   */
  private generateAbilityRecommendation(ability: string): SmartRecommendation {
    const abilityConfigs = {
      professionalKnowledge: {
        title: '专业知识强化训练',
        description: '针对性提升专业技能水平',
        url: '/pages/learning/professional',
      },
      logicalThinking: {
        title: '逻辑思维专项练习',
        description: '通过算法题提升逻辑分析能力',
        url: '/pages/practice/algorithm',
      },
      languageExpression: {
        title: '表达技巧训练营',
        description: '学习STAR法则，提升面试表达能力',
        url: '/pages/learning/expression',
      },
      stressResistance: {
        title: '抗压能力提升课程',
        description: '掌握压力管理技巧，提升面试表现',
        url: '/pages/learning/stress-management',
      },
      teamCollaboration: {
        title: '团队协作技能课程',
        description: '提升团队协作和沟通能力',
        url: '/pages/learning/teamwork',
      },
      innovation: {
        title: '创新思维培养',
        description: '培养创新意识和创造性思维',
        url: '/pages/learning/innovation',
      },
    }

    const config = abilityConfigs[ability] || abilityConfigs.professionalKnowledge

    return {
      id: `ability_${ability}_${Date.now()}`,
      type: 'learning',
      title: config.title,
      description: config.description,
      reason: `基于你的${ability}能力评估结果推荐`,
      priority: 'high',
      estimatedTime: 30,
      url: config.url,
      tags: [ability, 'skill_improvement'],
    }
  }

  /**
   * 生成学习习惯推荐
   */
  private generateHabitRecommendation(behaviors: LearningBehavior[]): SmartRecommendation | null {
    const recentBehaviors = behaviors.filter((b) => {
      const behaviorDate = new Date(b.timestamp)
      const threeDaysAgo = new Date()
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
      return behaviorDate >= threeDaysAgo
    })

    if (recentBehaviors.length < 2) {
      return {
        id: `habit_consistency_${Date.now()}`,
        type: 'practice',
        title: '保持学习节奏',
        description: '建议每天至少进行一次学习活动',
        reason: '最近学习频率较低，建议保持学习连续性',
        priority: 'medium',
        estimatedTime: 15,
        url: '/pages/practice/daily',
        tags: ['habit', 'consistency'],
      }
    }

    return null
  }

  /**
   * 生成阶段推荐
   */
  private generateStageRecommendation(stage: UserGrowthStage): SmartRecommendation | null {
    const stageConfigs = {
      new_user: {
        title: '新手入门指南',
        description: '快速了解平台功能，开始你的成长之旅',
        url: '/pages/guide/newbie',
      },
      beginner: {
        title: '基础能力巩固',
        description: '夯实基础，为进阶学习做准备',
        url: '/pages/learning/foundation',
      },
      intermediate: {
        title: '进阶技能提升',
        description: '学习更高级的面试技巧和专业知识',
        url: '/pages/learning/advanced',
      },
      advanced: {
        title: '高级挑战项目',
        description: '挑战复杂场景，提升综合能力',
        url: '/pages/challenge/expert',
      },
      expert: {
        title: '分享与指导',
        description: '帮助其他学习者，进一步巩固专业能力',
        url: '/pages/mentor/guide',
      },
    }

    const config = stageConfigs[stage]
    if (!config) return null

    return {
      id: `stage_${stage}_${Date.now()}`,
      type: 'course',
      title: config.title,
      description: config.description,
      reason: `基于你当前的${stage}阶段推荐`,
      priority: 'medium',
      estimatedTime: 45,
      url: config.url,
      tags: [stage, 'stage_based'],
    }
  }

  /**
   * 生成基于时间模式的推荐
   */
  private generateTimeBasedRecommendation(
    behaviors: LearningBehavior[],
  ): SmartRecommendation | null {
    const now = new Date()
    const recentBehaviors = behaviors.filter((b) => {
      const behaviorDate = new Date(b.timestamp)
      const daysDiff = (now.getTime() - behaviorDate.getTime()) / (1000 * 60 * 60 * 24)
      return daysDiff <= 7
    })

    if (recentBehaviors.length === 0) {
      return {
        id: 'time_based_inactive',
        type: 'practice',
        title: '重新开始学习之旅',
        description: '距离上次学习已经有一段时间了，来个简单的热身练习吧',
        reason: '长时间未学习',
        priority: 'high',
        estimatedTime: 15,
        url: '/pages/learning/quick-practice',
        tags: ['重新开始', '热身练习'],
      }
    }

    // 分析学习时间偏好
    const hourCounts = new Array(24).fill(0)
    recentBehaviors.forEach((b) => {
      const hour = new Date(b.timestamp).getHours()
      hourCounts[hour]++
    })

    const currentHour = now.getHours()
    const peakHour = hourCounts.indexOf(Math.max(...hourCounts))

    if (Math.abs(currentHour - peakHour) <= 1) {
      return {
        id: 'time_based_peak',
        type: 'practice',
        title: '黄金学习时间',
        description: '现在是你的最佳学习时间段，来个高强度练习吧！',
        reason: `${peakHour}点是你的高效学习时间`,
        priority: 'high',
        estimatedTime: 30,
        url: '/pages/learning/intensive-practice',
        tags: ['黄金时间', '高效学习'],
      }
    }

    return null
  }

  /**
   * 生成基于历史表现的推荐
   */
  private generatePerformanceBasedRecommendation(
    profile: UserGrowthProfile,
    behaviors: LearningBehavior[],
  ): SmartRecommendation | null {
    const interviewBehaviors = behaviors.filter((b) => b.type === 'interview')

    if (interviewBehaviors.length === 0) {
      return {
        id: 'performance_first_interview',
        type: 'interview',
        title: '开始你的第一次模拟面试',
        description: '通过实际面试体验了解自己的表现水平',
        reason: '尚未进行过模拟面试',
        priority: 'high',
        estimatedTime: 45,
        url: '/pages/interview/select',
        tags: ['首次面试', '自我了解'],
      }
    }

    // 分析最近表现趋势
    const recentInterviews = interviewBehaviors
      .filter((b) => {
        const daysDiff = (Date.now() - new Date(b.timestamp).getTime()) / (1000 * 60 * 60 * 24)
        return daysDiff <= 14
      })
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    if (recentInterviews.length >= 2) {
      const scores = recentInterviews.map((b) => b.data?.score || 0)
      const isImproving = scores[0] > scores[1]

      if (isImproving) {
        return {
          id: 'performance_improving',
          type: 'interview',
          title: '乘胜追击！',
          description: '你的面试表现正在提升，趁热打铁继续练习',
          reason: '面试成绩持续改善',
          priority: 'medium',
          estimatedTime: 40,
          url: '/pages/interview/advanced',
          tags: ['持续提升', '高级练习'],
        }
      } else {
        return {
          id: 'performance_declining',
          type: 'learning',
          title: '基础强化训练',
          description: '最近表现有所波动，建议回顾基础知识',
          reason: '面试成绩有所下降',
          priority: 'high',
          estimatedTime: 25,
          url: '/pages/learning/foundation-review',
          tags: ['基础巩固', '查缺补漏'],
        }
      }
    }

    return null
  }

  /**
   * 生成基于目标岗位的推荐
   */
  private generateCareerBasedRecommendation(
    profile: UserGrowthProfile,
  ): SmartRecommendation | null {
    const { targetPosition } = profile

    const careerMap = {
      前端工程师: {
        skills: ['Vue.js', 'React', 'JavaScript', 'CSS', 'HTML'],
        focus: '前端技术栈',
        url: '/pages/learning/frontend-path',
      },
      后端工程师: {
        skills: ['Java', 'Spring', 'MySQL', 'Redis', 'Docker'],
        focus: '后端技术栈',
        url: '/pages/learning/backend-path',
      },
      全栈工程师: {
        skills: ['全栈开发', 'API设计', '数据库设计', '系统架构'],
        focus: '全栈技能',
        url: '/pages/learning/fullstack-path',
      },
      算法工程师: {
        skills: ['机器学习', '深度学习', 'Python', '数学基础'],
        focus: '算法能力',
        url: '/pages/learning/algorithm-path',
      },
    }

    const career = careerMap[targetPosition] || careerMap['前端工程师']

    return {
      id: 'career_based',
      type: 'course',
      title: `${targetPosition}专项提升`,
      description: `针对${targetPosition}岗位的核心技能进行专项训练`,
      reason: `基于你的目标岗位：${targetPosition}`,
      priority: 'medium',
      estimatedTime: 60,
      url: career.url,
      tags: [targetPosition, career.focus, '专项训练'],
    }
  }

  /**
   * 生成基于学习偏好的推荐
   */
  private generatePreferenceBasedRecommendation(
    behaviors: LearningBehavior[],
  ): SmartRecommendation | null {
    // 分析学习类型偏好
    const typeCounts = behaviors.reduce(
      (acc, b) => {
        acc[b.type] = (acc[b.type] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

    const preferredType = Object.entries(typeCounts).sort(([, a], [, b]) => b - a)[0]?.[0]

    if (!preferredType) return null

    const preferenceMap = {
      interview: {
        title: '面试实战训练',
        description: '你偏爱实战练习，来挑战更难的面试场景吧',
        url: '/pages/interview/challenge',
        tags: ['实战训练', '挑战模式'],
      },
      study: {
        title: '深度学习课程',
        description: '你喜欢系统学习，这里有新的深度课程等你探索',
        url: '/pages/learning/advanced-course',
        tags: ['系统学习', '深度课程'],
      },
      practice: {
        title: '技能专项练习',
        description: '专项练习是你的强项，来试试新的练习模式',
        url: '/pages/practice/new-mode',
        tags: ['专项练习', '新模式'],
      },
    }

    const preference = preferenceMap[preferredType]
    if (!preference) return null

    return {
      id: 'preference_based',
      type: preferredType as any,
      title: preference.title,
      description: preference.description,
      reason: `基于你的学习偏好：${preferredType}`,
      priority: 'medium',
      estimatedTime: 35,
      url: preference.url,
      tags: preference.tags,
    }
  }
}

// 导出单例实例
export const growthTrackingService = new GrowthTrackingService()

// 导出工具函数
export { GrowthTrackingService }
