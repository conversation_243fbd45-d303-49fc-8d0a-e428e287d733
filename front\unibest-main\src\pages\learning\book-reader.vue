<script setup lang="ts">
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingCard from '@/components/LoadingCard.vue'
// @ts-ignore
import Notification from '@/components/Notification.vue'
// 引入类型定义
import type { Ref } from 'vue'
// 引入Markdown渲染库
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
// 引入API和类型定义
import {
  getBookDetail,
  getBookChapters,
  getChapterDetail,
  getReadingRecord,
  updateReadingProgress,
} from '../../service/learning'
import type {
  Book,
  BookChapter,
  BookDetail,
  ReadingRecord,
  ReadingSettings,
  ChapterListResponse,
} from '../../types/learning'
import { READING_THEMES, READING_THEME_LABELS, STORAGE_KEYS } from '../../types/learning-constants'

// 页面参数
const bookId: Ref<number> = ref(0)

// 页面状态
const isLoading: Ref<boolean> = ref(true)
const isContentLoading: Ref<boolean> = ref(false)

// 通知状态
const notificationVisible: Ref<boolean> = ref(false)
const notificationMessage: Ref<string> = ref('')
const notificationType: Ref<'success' | 'error' | 'info' | 'warning'> = ref('info')

// 书籍信息
const bookInfo: Ref<BookDetail | null> = ref(null)
const chapters: Ref<BookChapter[]> = ref([])
const currentChapter: Ref<BookChapter | null> = ref(null)
const currentChapterIndex: Ref<number> = ref(0)

// 界面控制
const showChapterList: Ref<boolean> = ref(false)
const showSettings: Ref<boolean> = ref(false)
const showMenu: Ref<boolean> = ref(false)
const isMenuVisible: Ref<boolean> = ref(true)

// 阅读设置
const readingSettings: Ref<ReadingSettings> = ref({
  fontSize: 16,
  fontFamily: 'PingFang SC',
  backgroundColor: '#ffffff',
  textColor: '#333333',
  lineHeight: 1.8,
  theme: 'light',
})

// 阅读进度
const readingProgress: Ref<number> = ref(0)
const scrollTop: Ref<number> = ref(0)

// 阅读记录
const readingRecord: Ref<ReadingRecord | null> = ref(null)

/**
 * @description 显示通知
 * @param message 通知内容
 * @param type 通知类型（info/success/error/warning）
 * @param duration 显示时长（毫秒）
 */
const showNotification = (
  message: string,
  type: 'success' | 'error' | 'info' | 'warning' = 'info',
  duration = 3000,
): void => {
  notificationMessage.value = message
  notificationType.value = type
  notificationVisible.value = true
}

// Markdown渲染器初始化
const md = new MarkdownIt({
  html: true, // 允许HTML标签
  linkify: true, // 自动识别链接
  typographer: true, // 启用一些语言中性的替换和引号美化
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return (
          '<pre class="hljs"><code>' +
          hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
          '</code></pre>'
        )
      } catch (__) {}
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  },
})

/**
 * @description 渲染章节内容的Markdown
 * @returns 渲染后的HTML字符串
 */
const renderedChapterContent = computed(() => {
  return currentChapter.value?.content ? md.render(currentChapter.value.content) : ''
})

// 预设主题
const themes = [
  {
    name: READING_THEME_LABELS[READING_THEMES.LIGHT],
    key: READING_THEMES.LIGHT,
    backgroundColor: '#ffffff',
    textColor: '#333333',
    icon: 'i-mdi-white-balance-sunny',
  },
  {
    name: READING_THEME_LABELS[READING_THEMES.DARK],
    key: READING_THEMES.DARK,
    backgroundColor: '#1a1a1a',
    textColor: '#e5e5e5',
    icon: 'i-mdi-moon-waning-crescent',
  },
  {
    name: READING_THEME_LABELS[READING_THEMES.SEPIA],
    key: READING_THEMES.SEPIA,
    backgroundColor: '#f7f3e9',
    textColor: '#5c4b37',
    icon: 'i-mdi-eye',
  },
]

// 字体大小选项
const fontSizeOptions = [
  { label: '小', value: 14 },
  { label: '标准', value: 16 },
  { label: '中', value: 18 },
  { label: '大', value: 20 },
  { label: '特大', value: 22 },
]

/**
 * @description 获取书籍信息
 * @param id 书籍ID
 */
const fetchBookInfo = async (id: number): Promise<BookDetail> => {
  const response = await getBookDetail(id)
  if (response.code === 200 && response.data) {
    return response.data
  }
  throw new Error(response.message || '获取书籍信息失败')
}

/**
 * @description 获取章节列表
 * @param bookId 书籍ID
 */
const fetchChapters = async (bookId: number): Promise<BookChapter[]> => {
  const response = await getBookChapters(bookId)
  if (response.code === 200 && response.data) {
    return response.data || []
  }
  throw new Error(response.message || '获取章节列表失败')
}

/**
 * @description 获取章节详情
 * @param chapterId 章节ID
 */
const fetchChapterDetail = async (chapterId: number): Promise<BookChapter> => {
  const response = await getChapterDetail(chapterId)
  console.log(response)
  if (response.code === 200 && response.data) {
    return response.data
  }
  throw new Error(response.message || '获取章节详情失败')
}

/**
 * @description 获取阅读记录
 * @param bookId 书籍ID
 */
const fetchReadingRecord = async (bookId: number): Promise<ReadingRecord | null> => {
  try {
    const response = await getReadingRecord(bookId)
    if (response.code === 200 && response.data) {
      return response.data
    }
    return null
  } catch (error) {
    console.warn('获取阅读记录失败:', error)
    return null
  }
}

/**
 * @description 初始化页面数据
 */
const initializeData = async (): Promise<void> => {
  try {
    isLoading.value = true

    // 并行获取书籍信息、章节列表和阅读记录
    const [book, chapterList, record] = await Promise.all([
      fetchBookInfo(bookId.value),
      fetchChapters(bookId.value),
      fetchReadingRecord(bookId.value),
    ])

    bookInfo.value = book
    chapters.value = chapterList
    readingRecord.value = record
    console.log(chapterList)
    // 设置当前章节
    if (chapterList.length > 0) {
      let targetChapterIndex = 0

      // 如果有阅读记录，恢复到上次阅读的章节
      if (record && record.chapterId) {
        const recordChapterIndex = chapterList.findIndex(
          (chapter) => chapter.id === record.chapterId,
        )
        if (recordChapterIndex >= 0) {
          targetChapterIndex = recordChapterIndex
        }
      }
      console.log(targetChapterIndex)

      await switchChapter(chapterList[targetChapterIndex], targetChapterIndex)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    showNotification(error instanceof Error ? error.message : '加载失败，请重试', 'error')
  } finally {
    isLoading.value = false
  }
}

/**
 * @description 切换章节
 * @param chapter 章节对象
 * @param index 章节索引
 */
const switchChapter = async (chapter: BookChapter, index: number): Promise<void> => {
  if (!chapter.isUnlocked) {
    showNotification('该章节暂未解锁', 'warning')
    return
  }

  if (chapter.id === currentChapter.value?.id) {
    showChapterList.value = false
    return
  }

  isContentLoading.value = true

  try {
    // 获取章节详细内容
    const chapterDetail = await fetchChapterDetail(chapter.id)

    currentChapter.value = chapterDetail
    currentChapterIndex.value = index
    scrollTop.value = 0
    showChapterList.value = false

    // 恢复阅读位置
    if (
      readingRecord.value &&
      readingRecord.value.chapterId === chapter.id &&
      readingRecord.value.bookmarkPosition
    ) {
      setTimeout(() => {
        scrollTop.value = readingRecord.value!.bookmarkPosition!
      }, 500)
    }
  } catch (error) {
    console.error('切换章节失败:', error)
    showNotification('切换失败，请重试', 'error')
  } finally {
    isContentLoading.value = false
  }
}

/**
 * @description 上一章
 */
const prevChapter = (): void => {
  if (currentChapterIndex.value > 0) {
    const prevIndex = currentChapterIndex.value - 1
    const prevChap = chapters.value[prevIndex]
    switchChapter(prevChap, prevIndex)
  } else {
    showNotification('已经是第一章了', 'info')
  }
}

/**
 * @description 下一章
 */
const nextChapter = (): void => {
  if (currentChapterIndex.value < chapters.value.length - 1) {
    const nextIndex = currentChapterIndex.value + 1
    const nextChap = chapters.value[nextIndex]
    switchChapter(nextChap, nextIndex)
  } else {
    showNotification('已经是最后一章了', 'info')
  }
}

/**
 * @description 应用主题设置
 * @param theme 主题配置
 */
const applyTheme = (theme: any): void => {
  readingSettings.value.theme = theme.key
  readingSettings.value.backgroundColor = theme.backgroundColor
  readingSettings.value.textColor = theme.textColor

  // 保存设置到本地存储
  saveReadingSettings()
}

/**
 * @description 调整字体大小
 * @param fontSize 字体大小
 */
const adjustFontSize = (fontSize: number): void => {
  readingSettings.value.fontSize = fontSize
  saveReadingSettings()
}

/**
 * @description 切换菜单显示状态
 */
const toggleMenu = (): void => {
  isMenuVisible.value = !isMenuVisible.value
}

/**
 * @description 处理滚动事件
 * @param event 滚动事件
 */
const handleScroll = (event: any): void => {
  scrollTop.value = event.detail.scrollTop

  // 计算阅读进度
  const scrollHeight = event.detail.scrollHeight
  const clientHeight = event.detail.scrollHeight - event.detail.deltaY
  if (scrollHeight > 0) {
    readingProgress.value = Math.min((scrollTop.value / (scrollHeight - clientHeight)) * 100, 100)
  }

  // 自动保存阅读进度（防抖）
  debounceSaveProgress()
}

// 防抖保存进度
let saveProgressTimer: number | null = null
const debounceSaveProgress = (): void => {
  if (saveProgressTimer) {
    clearTimeout(saveProgressTimer)
  }

  saveProgressTimer = setTimeout(() => {
    if (currentChapter.value && bookInfo.value) {
      saveReadingProgress()
    }
  }, 2000) // 2秒后保存
}

/**
 * @description 保存阅读进度
 */
const saveReadingProgress = async (): Promise<void> => {
  if (!currentChapter.value || !bookInfo.value) return

  try {
    const params = {
      chapterId: currentChapter.value.id,
      progress: readingProgress.value,
      readTime: Math.floor(Date.now() / 1000), // 当前时间戳
      bookmarkPosition: scrollTop.value,
    }

    await updateReadingProgress(bookInfo.value.id, params)
  } catch (error) {
    console.error('保存阅读进度失败:', error)
  }
}

/**
 * @description 返回上一页
 */
const goBack = (): void => {
  // 保存当前阅读进度
  if (currentChapter.value && bookInfo.value) {
    saveReadingProgress()
  }
  uni.navigateBack()
}

/**
 * @description 从本地存储加载阅读设置
 */
const loadReadingSettings = (): void => {
  try {
    const savedSettings = uni.getStorageSync(STORAGE_KEYS.READING_SETTINGS)
    if (savedSettings) {
      readingSettings.value = { ...readingSettings.value, ...savedSettings }
    }
  } catch (error) {
    console.error('加载阅读设置失败:', error)
  }
}

/**
 * @description 保存阅读设置到本地存储
 */
const saveReadingSettings = (): void => {
  try {
    uni.setStorageSync(STORAGE_KEYS.READING_SETTINGS, readingSettings.value)
  } catch (error) {
    console.error('保存阅读设置失败:', error)
  }
}

/**
 * @description 获取阅读器容器样式
 */
const getReaderContainerStyle = (): string => {
  return `background-color: ${readingSettings.value.backgroundColor};`
}

/**
 * @description 获取导航栏样式
 */
const getNavbarClass = (): string => {
  return isMenuVisible.value ? '' : 'nav-hidden'
}

/**
 * @description 获取菜单样式
 */
const getMenuClass = (): string => {
  return isMenuVisible.value ? '' : 'menu-hidden'
}

/**
 * @description 清理资源
 */
const cleanup = (): void => {
  if (saveProgressTimer) {
    clearTimeout(saveProgressTimer)
    saveProgressTimer = null
  }
}

// 页面加载时获取参数
onLoad((options: any) => {
  if (options?.id) {
    bookId.value = parseInt(options.id)
  }
})

// 页面显示时初始化
onShow(() => {
  loadReadingSettings()
})

// 组件挂载时初始化数据
onMounted(async () => {
  await initializeData()
})

// 页面卸载时保存进度并清理资源
onUnmounted(() => {
  if (currentChapter.value && bookInfo.value) {
    saveReadingProgress()
  }
  cleanup()
})
</script>

<template>
  <view class="reader-container" :style="getReaderContainerStyle()">
    <!-- 头部导航 -->
    <HeadBar
      :title="bookInfo?.title || '加载中...'"
      :show-back="true"
      :custom-back="true"
      :opacity="isMenuVisible ? 1 : 0"
      :class="getNavbarClass()"
      @back="goBack"
    />

    <!-- 主要内容区域 -->
    <view class="reader-content" v-if="!isLoading">
      <!-- 阅读内容 -->
      <scroll-view
        class="content-scroll"
        scroll-y
        :scroll-top="scrollTop"
        @scroll="handleScroll"
        @tap="toggleMenu"
      >
        <view
          class="content-wrapper"
          :style="{
            fontSize: readingSettings.fontSize + 'px',
            lineHeight: readingSettings.lineHeight,
            color: readingSettings.textColor,
            fontFamily: readingSettings.fontFamily,
          }"
        >
          <view v-if="currentChapter" class="chapter-content">
            <!-- 章节标题 -->
            <view class="chapter-title">
              {{ currentChapter.title }}
            </view>

            <!-- 章节信息 -->
            <view class="chapter-info">
              <text class="word-count">约{{ currentChapter.wordCount }}字</text>
              <text class="read-time">预计阅读{{ currentChapter.readTime }}分钟</text>
            </view>

            <!-- 章节正文 -->
            <view class="chapter-text">
              <rich-text
                v-if="renderedChapterContent"
                class="content-text markdown-content"
                :nodes="renderedChapterContent"
              ></rich-text>
              <text v-else class="no-content-text">暂无章节内容</text>
            </view>
          </view>

          <!-- 章节导航 -->
          <view class="chapter-navigation">
            <button
              v-if="currentChapterIndex > 0"
              class="nav-btn prev-btn"
              @click.stop="prevChapter"
            >
              <view class="i-mdi-chevron-left"></view>
              <text>上一章</text>
            </button>

            <button
              class="nav-btn next-btn"
              :disabled="currentChapterIndex === chapters.length - 1"
              @click.stop="nextChapter"
            >
              <text>下一章</text>
              <view class="i-mdi-chevron-right"></view>
            </button>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部菜单栏 -->
    <view class="bottom-menu" :class="getMenuClass()" v-if="!isLoading">
      <!-- 阅读进度 -->
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: readingProgress + '%' }"></view>
      </view>

      <!-- 菜单按钮 -->
      <view class="menu-buttons">
        <button class="menu-btn" @click="showChapterList = true">
          <view class="i-mdi-format-list-bulleted menu-icon"></view>
          <text class="menu-text">目录</text>
        </button>

        <button class="menu-btn" @click="showSettings = true">
          <view class="i-mdi-cog menu-icon"></view>
          <text class="menu-text">设置</text>
        </button>
      </view>
    </view>

    <!-- 章节列表弹窗 -->
    <view v-if="showChapterList" class="modal-overlay" @tap="showChapterList = false">
      <view class="chapter-list-modal" @tap.stop="">
        <view class="modal-header">
          <text class="modal-title">目录</text>
          <button class="close-btn" @click="showChapterList = false">
            <view class="i-mdi-close"></view>
          </button>
        </view>

        <scroll-view class="chapter-list" scroll-y>
          <view
            v-for="(chapter, index) in chapters"
            :key="chapter.id"
            class="chapter-item"
            :class="{
              active: chapter.id === currentChapter?.id,
              locked: !chapter.isUnlocked,
            }"
            @click="switchChapter(chapter, index)"
          >
            <view class="chapter-info">
              <text class="chapter-title">{{ chapter.title }}</text>
              <text class="chapter-meta">
                {{ chapter.wordCount }}字 · {{ chapter.readTime }}分钟
              </text>
            </view>

            <view class="chapter-status">
              <view v-if="!chapter.isUnlocked" class="i-mdi-lock lock-icon"></view>
              <view v-else-if="chapter.isCompleted" class="i-mdi-check-circle complete-icon"></view>
              <view
                v-else-if="chapter.id === currentChapter?.id"
                class="i-mdi-play current-icon"
              ></view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 设置弹窗 -->
    <view v-if="showSettings" class="modal-overlay" @tap="showSettings = false">
      <view class="settings-modal" @tap.stop="">
        <view class="modal-header">
          <text class="modal-title">阅读设置</text>
        </view>

        <view class="settings-content">
          <!-- 主题设置 -->
          <view class="setting-section">
            <text class="setting-title">阅读主题</text>
            <view class="theme-options">
              <button
                v-for="theme in themes"
                :key="theme.key"
                class="theme-btn"
                :class="{ active: readingSettings.theme === theme.key }"
                @click="applyTheme(theme)"
              >
                <view :class="theme.icon" class="theme-icon"></view>
                <text class="theme-name">{{ theme.name }}</text>
              </button>
            </view>
          </view>

          <!-- 字体大小 -->
          <view class="setting-section">
            <text class="setting-title">字体大小</text>
            <view class="font-size-options">
              <button
                v-for="option in fontSizeOptions"
                :key="option.value"
                class="font-size-btn"
                :class="{ active: readingSettings.fontSize === option.value }"
                @click="adjustFontSize(option.value)"
              >
                <text class="font-size-text" :style="{ fontSize: option.value + 'px' }">
                  {{ option.label }}
                </text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 通知弹窗 -->
    <Notification
      :visible="notificationVisible"
      :message="notificationMessage"
      :type="notificationType"
      @close="notificationVisible = false"
    />

    <!-- 加载状态 -->
    <LoadingCard
      :visible="isLoading || isContentLoading"
      :text="isLoading ? '正在加载书籍...' : '正在切换章节...'"
    />
  </view>
</template>

<style scoped lang="scss">
.reader-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  transition: background-color 0.3s ease;
}

// 导航栏隐藏动画
.nav-hidden {
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

// 主要内容区域
.reader-content {
  flex: 1;
  overflow: hidden;
}

.content-scroll {
  height: 100%;
  width: 100%;
}

.content-wrapper {
  padding: 40rpx 32rpx 200rpx;
  min-height: calc(100vh - 180rpx);
  transition: all 0.3s ease;
}

// 章节内容
.chapter-content {
  .chapter-title {
    display: block;
    margin-bottom: 24rpx;
    font-size: 36rpx;
    font-weight: 700;
    line-height: 1.4;
  }

  .chapter-info {
    display: flex;
    align-items: center;
    gap: 24rpx;
    margin-bottom: 40rpx;
    padding-bottom: 24rpx;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);

    .word-count,
    .read-time {
      font-size: 24rpx;
      opacity: 0.6;
    }
  }

  .chapter-text {
    .content-text {
      white-space: pre-wrap;
      word-wrap: break-word;
      line-height: inherit;
    }

    .no-content-text {
      display: block;
      font-size: 28rpx;
      line-height: 1.6;
      color: #94a3b8;
      text-align: center;
      margin: 60rpx 0;
    }
  }
}

// 章节导航
.chapter-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 80rpx;
  padding-top: 40rpx;
  border-top: 2rpx solid rgba(0, 0, 0, 0.1);

  .nav-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 20rpx 32rpx;
    background: #00c9a7;
    color: white;
    border: none;
    border-radius: 12rpx;
    font-size: 26rpx;
    font-weight: 500;
    transition: all 0.3s ease;

    &:disabled {
      background: #e2e8f0;
      color: #94a3b8;
      cursor: not-allowed;
    }

    &:not(:disabled):active {
      transform: scale(0.98);
      background: #00b393;
    }
  }
}

// 底部菜单
.bottom-menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: transform 0.3s ease;
  z-index: 100;

  &.menu-hidden {
    transform: translateY(100%);
  }

  .progress-bar {
    height: 6rpx;
    background: #f1f5f9;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #00c9a7 0%, #4fd1c7 100%);
      transition: width 0.3s ease;
    }
  }

  .menu-buttons {
    display: flex;
    padding: 20rpx;

    .menu-btn {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
      padding: 16rpx;
      background: transparent;
      border: none;

      .menu-icon {
        font-size: 32rpx;
        color: #64748b;
      }

      .menu-text {
        font-size: 22rpx;
        color: #64748b;
      }

      &:active {
        background: #f8fafc;
      }
    }
  }
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;

  .modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1e293b;
  }
}

// 章节列表弹窗
.chapter-list-modal {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;

  .chapter-list {
    max-height: 60vh;

    .chapter-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 32rpx;
      border-bottom: 2rpx solid #f8fafc;
      transition: background-color 0.2s ease;

      &.active {
        background: linear-gradient(90deg, rgba(0, 201, 167, 0.1) 0%, rgba(79, 209, 199, 0.1) 100%);

        .chapter-title {
          color: #00c9a7;
          font-weight: 600;
        }
      }

      &.locked {
        opacity: 0.5;

        .chapter-title {
          color: #94a3b8;
        }
      }

      &:not(.locked):active {
        background: #f8fafc;
      }

      .chapter-info {
        flex: 1;

        .chapter-title {
          display: block;
          margin-bottom: 8rpx;
          font-size: 28rpx;
          color: #1e293b;
        }

        .chapter-meta {
          font-size: 22rpx;
          color: #64748b;
        }
      }

      .chapter-status {
        .lock-icon {
          font-size: 24rpx;
          color: #94a3b8;
        }

        .complete-icon {
          font-size: 24rpx;
          color: #16a34a;
        }

        .current-icon {
          font-size: 24rpx;
          color: #00c9a7;
        }
      }
    }
  }
}

// 设置弹窗
.settings-modal {
  width: 90%;
  max-width: 800rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;

  .settings-content {
    padding: 32rpx;
    max-height: 40vh;
    overflow-y: auto;
  }

  .setting-section {
    margin-bottom: 28rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .setting-title {
      display: block;
      margin-bottom: 24rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }

  // 主题选项
  .theme-options {
    display: flex;
    gap: 20rpx;

    .theme-btn {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;
      padding: 24rpx 16rpx;
      background: #f8fafc;
      border: 2rpx solid transparent;
      border-radius: 16rpx;
      transition: all 0.2s ease;

      &.active {
        border-color: #00c9a7;
        background: rgba(0, 201, 167, 0.1);

        .theme-name {
          color: #00c9a7;
          font-weight: 600;
        }
      }

      .theme-icon {
        font-size: 32rpx;
        color: #64748b;
      }

      .theme-name {
        font-size: 24rpx;
        color: #64748b;
      }
    }
  }

  // 字体大小选项
  .font-size-options {
    display: flex;
    gap: 16rpx;

    .font-size-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8fafc;
      border: 2rpx solid transparent;
      border-radius: 12rpx;
      transition: all 0.2s ease;

      &.active {
        border-color: #00c9a7;
        background: rgba(0, 201, 167, 0.1);

        .font-size-text {
          color: #00c9a7;
          font-weight: 600;
        }
      }

      .font-size-text {
        color: #64748b;
        transition: all 0.2s ease;
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
